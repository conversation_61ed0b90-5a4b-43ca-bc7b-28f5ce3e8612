ABSTRACT
This document describes how iPiD customers
can integrate with iPiD Validate API to perform
bank account validations.
Confidentiality : Low
Revision Status : V 1.8
Revision : April 2025

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

1. Introduction ........................................................................................................ 4
1.1. URLs ...................................................................................................................... 4
1.2. Rate Limiting.......................................................................................................... 4
1.3. IP Whitelisting ........................................................................................................ 4
Outbound .............................................................................................................................................. 4
Inbound ................................................................................................................................................ 4

1.4. API Key .................................................................................................................. 4
1.5. Customer Id ........................................................................................................... 4

2. Public API ........................................................................................................... 5
2.1. Retrieve public key ................................................................................................. 7
2.1.1. Request ...................................................................................................................................... 7
2.1.2. Response ................................................................................................................................... 7

2.2 Validation ............................................................................................................... 8
2.2.1. Request ...................................................................................................................................... 8
2.2.2. Response ................................................................................................................................. 10

3. Encryption Guide ............................................................................................... 12
3.1. OpenPGP ............................................................................................................. 12
3.2. RSA ..................................................................................................................... 12

4. Data Model ........................................................................................................ 14
4.1. Creditor ............................................................................................................... 14
4.1. Identification ............................................................................................................................... 14

4.2. Creditor_account .................................................................................................. 14
4.3. Creditor_agent ..................................................................................................... 15

5. Appendix ........................................................................................................... 16
5.1. Response code and message ............................................................................... 16
5.2. Identity Type ........................................................................................................ 16
5.3. Scoring Guide ...................................................................................................... 17
5.4. Corridor Details .................................................................................................... 18
5.5. Corridor Request and Response Specifications ..................................................... 20

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Version
1.0

Date

Author
Ivan Chong,
Samantha Bautista
Samantha Bautista

1.1

November 23, 2023

1.2

January 25, 2024

Ivan Chong,
Samantha Bautista

1.3

May 7, 2024

Samantha Bautista

1.4

November 25, 2024

Samantha Bautista

1.5

December 20, 2024

Samantha Bautista

1.6

January 17, 2025

Samantha Bautista

1.7

March 11, 2025

Wang Xueqing
(Quinn)

1.8

April 1, 2025

Samantha Bautista

Summary
Release of version 1.0
Updated with information
about BR and MX
corridors
Included details on
public key hint, and node
selection
Updated with information
about CN node-02, UG,
AR, UY, BE, FR, IT, NL, PL
Updated with information
on US-node-01
enrichment, PE, MY
Live with PE, UY, MY;
added details on TR
Live with TR, ZA; added
details on CL, CO, EC
Adjustment of name
match scoring threshold
in section 5.3. Scoring
Guide
Updates in line with
enhancements to
response payload and
added details on SA

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

1. Introduction
The purpose of this document is to provide developers an official guideline on how to
consume iPiD Validation API.

1.1. URLs
Environment
Production
Sandbox

URL
https://api.ipid.live
https://sandbox.ipid.works

1.2. Rate Limiting
Our rate limit is 1000 RPS (request per second). Please note that not all corridors can
support such a high RPS. To provide a better integration experience, we will perform
internal rate limiting based on each corridor. If we are internally rate limiting a
particular corridor, you may notice higher than normal latency. In the worst-case
scenario, we may return an HTTP 429 status code even if you have not reached 1000
RPS, as we might be experiencing high traffic for that particular corridor. We also
monitor for anomalies and will throttle or block any suspicious usage patterns.

1.3. IP Whitelisting
Outbound
If there are any restriction for outbound traffic: please refer to latest list of IP to
whitelist
from
here
https://www.microsoft.com/enus/download/details.aspx?id=56519
Inbound
User will also need to provide IP of the system from which you make the request to be
whitelisted. If it is not whitelisted, iPiD will reject the incoming request.

1.4. API Key
An API key is required for most of the APIs. This can be acquired from an iPiD
representative. API key should be sent on every request, via header x-api-key.

1.5. Customer Id
Every request should be accompanied by a valid customer id, via header x-customerid. This can be acquired from an iPiD representative.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

2. Public API
The most common way to integrate with iPiD is through public API via HTTPS with
TLS 1.2 and above. iPiD API can be consumed directly through the internet, which is
secured by HTTPS with TLS 1.2, and our API gateway. The Diagram below shows a
simplified version of this integration option.
For further customization in integration, please reach out to the iPiD technical team.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

The order of API consumption is crucial, so please refer to flow chart below for
usage guide:

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

2.1. Retrieve public key
GET validation/api/v1/public-key
This API is used to retrieve public key that is needed to encrypt payload.

2.1.1. Request
Headers
Name
Name
x-api-key
x-customer-id

Mandatory
Mandatory
Y
Y

Parameter
Name
bic

Mandatory Type
C
string

Type
Type
string
string

Description
Description
API key provided by iPiD
Customer id provided by iPiD

Description
BIC code, either 8 or 11 digits. Will
prioritize bic if both bic and
country_code supplied.
country_code
C
string
Country code, based on ISO 3166,
Alpha-2. Will prioritize bic if both bic
and country_code supplied.
*conditional, either bic OR country_code is required.

2.1.2. Response
Body
Name
response_code
response_message
data.node_public_key

Mandatory
Y
Y
Y

Type
string
string
string

data.node_public_key_rsa Y

string

data.node_id

string

Y

Description
Refer to section 5.1 for full list
Refer to section 5.1 for full list
Public key of destination iPiD
node in Open PGP format
Public key of destination iPiD
node in RSA format
Id of destination iPiD node

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

2.2 Validation
POST validation/api/v1/bank-account/validate
This API is used to perform bank account validation

2.2.1. Request
Headers
Name
x-request-id

Mandatory Type
N
string

x-api-key
Y
string
x-customer-id
Y
string
Body
Name
Mandatory Type
encrypted_payload Y
string

node_id
public_key

Y
N

string
string

clearing_system_id N

string

bic

N

string

public_key_hint

N

object

Description
If supplied, iPiD will tag this field for
entire lifecycle of request, which will
facilitate debugging process
API key provided by iPiD
Customer id provided by iPiD
Description
Encrypted payload with public key
from response of 2.1.
Please refer to section 3 for
encryption guide.
nodeId from response of 2.1
PGP public key. If supplied, response
will be encrypted with this public key.
This value will be used to further
enhance our internal routing logic
without peeking into encrypted
payload.
This value will be used to further
enhance our internal routing logic
without peeking into encrypted
payload.
This field is used to facilitate key
rotation. If supplied, it will help us to
identify which public key was used to
encrypt request payload. If value do
not exists we will attempt to decrypt
the payload with both new and old
public key which might have
performance impact.
Refer to “public_key_hint” data model
in section 4.4

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Encrypted Payload
Name
creditor

Mandatory Type
Y
object

creditor_account

Y

object

creditor_agent

Y

object

Description
refer to “Creditor” data model in
section 4.1
refer to “Creditor_Account” data model
in section 4.2
refer to “Creditor_Agent” data model in
section 4.3

Request Samples
{
"creditor": {
"name": "FIRA DIYANKA"
},
"creditor_account": {
"account_id": "**********"
},
"creditor_agent": {
"bic": "CENAIDJA"
}
}
{
"creditor": {
"name": "Beijing Apple and Orange Company",
"identification": [
{
"value": "91111111ABCXYZ123",
"type": "registration_id"
}
]
},
"creditor_account": {
"account_id": "************"
},
"creditor_agent": {
"bic": "ABOCCNBJ"
}
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

2.2.2. Response
Headers
Name
Content-Type
Body
Name
response_code
response_message
data

Mandatory Type
Y
string

Mandatory
Y
Y
Y

Description
application/json

Type
string
string
string

Description
Refer to section 5.1 for full list
Refer to section 5.1 for full list
Consist of match_score,
match_score_description, and
encrypted payload (using client’s
public key)
Encrypted fields include: creditor,
creditor_account and creditor_agent,
typically including creditor’s name
(and name translation when
available) and account’s currency
and type when available.
Response payload is only populated
with data that deviates from the
original Request payload values. Else,
Response payload object fields will
not be populated.
Please refer to section 3 for
decryption guide and section 4 for
the data model.

Data
Name
match_score

Type
string

match_score_description string
logic_score

string

logic_test

string

Description
Scoring based on Creditor.Name or
Creditor.Given_name+Creditor.Surname
when validation is in 2000 status
Interpretation of match_score, with
additional supporting information
(depending on corridor); refer to section 5.3
Score value between 0-100 indicating
likelihood of account<>routing_code
validation; currently applicable only to usnode-01 and us-node-02; refer to section
5.3
Interpretation of logic score (pass, fail);
refer to section 5.3

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

reason_code

string

creditor
creditor_account

object
object

creditor_agent

object

public_key_hint

object

Further reason code for certain 2100 or
2103 responses when it is explicitly
identified that account structure is invalid
or bank is out of coverage
refer to “Creditor” data model in section 4.1
refer to “Creditor_Account” data model in
section 4.2
refer to “Creditor_Agent” data model in
section 4.3
This field is used to facilitate key rotation. If
supplied, it will help us to identify which
public key was used to encrypt request
payload.
Refer to “public_key_hint” data mdel in
section 4.4

Sample Response
{
"response_code": 2000,
"response_message": "ValidationSucceeded",
"data": {
"encrypted_payload": "-----BEGIN PGP MESSAGE----\n\nwV4Dvr0WcQhbYpkSAQdA81yhyMLzTyYSqpVUluRW9DFgwefUxwEsAZgRI4XP\nqF8wBvfKcMGcONoVpgZv
uUdfbXarz+OjphR0r9eJBHrP52D7ENtQDk2vdkAb\nGFb8RI2n0poBF2i+8Ti71Y57tT4txgr10lApAd32G39AOn5ea/4
aT3/vvCgw\n8wn9DaFnWormfq9XF1QzDwwwL4czRvciUHaW5VCdU034busZzZ1ePpcsuVpP\nxvswnSLzL8UxFb
gmlLPz3Or3URXWbINZvWrjomznaRlDraEl8xc0iJ4DYtm5\nxPwCjKNrVe15sNPpvK+I3vhe3WfzHZWvN5SD\n=Gq9
Y\n-----END PGP MESSAGE-----\n",
"match_score": 1,
"match_score_description": "Strong Match",
"public_key_hint": {
"start": "xjMEZ",
"end": "=Wloc"
}
}
}

Decrypted Payload
{
"creditor": {
"name": "北京苹果和橙子公司",
"name_translation": "Beijing Apple and Orange Company"
}
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

3. Encryption Guide
Encrypted payload is generated from a JSON payload. Following is an example of a
valid JSON payload based on instruct API in section 3.1:
{
"creditor": {
"name": "FIRA DIYANKA"
},
"creditor_account": {
"account_id": "**********"
},
"creditor_agent": {
"bic": "CENAIDJA"
}
}

3.1. OpenPGP
Encryption can be done with OpenPGP protocol. You may refer to this guide for
Javascript implementation: https://github.com/openpgpjs/openpgpjs

3.2. RSA
Encryption can also be done via RSA. We use following cipher type:
RSA/None/PKCS1Padding
To generate an RSA key, we recommend a minimum of 2048 bits. We support both
SPKI and PKCS#1 formats and expect the key in PEM encoding.
To encrypt with RSA, it is recommended to strip the payload of all white spaces
because there is a maximum data size.
For example, following is payload with whitespace:
{
"creditor": {
"name": "FIRA DIYANKA"
},
"creditor_account": {
"account_id": "**********"
},
"creditor_agent": {
"bic": "CENAIDJA"
}
}

And following is payload with whitespace removed:
{"creditor":{"name":"FIRA
DIYANKA",},"creditor_account":{"account_id":"**********",},"creditor_agent":{"bic":"CENAIDJA"}}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Sample code in type script:
const crypto = require("crypto");
const encrypt = async (payload: unknown, publicKeyArmored: string): Promise<string | undefined> =>
{
const stringifyPayload = JSON.stringify(payload);
const encryptedPayload = crypto.publicEncrypt({
key: publicKeyArmored,
padding: crypto.constants.RSA_PKCS1_PADDING,
}, Buffer.from(stringifyPayload)).toString('base64');
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

4. Data Model
4.1. Creditor
Name
name

Mandatory
Conditional

Type
string

given_name

Conditional

string

surname

Conditional

string

name_translation

Response
Only

string

msisdn

N

string

email
identification

N
N

string
object

Description
Name that will be used for name matching
purpose; must have either name or
given_name supplied
Given name that will be used for name
matching purpose; must have either name
or given_name
Surname that will be used for name
matching purpose; optionally used with
given name for certain corridors (US)
In case of name returned in foreign
characters from beneficiary institution, a
translated name value in latin characters
will also be returned
Phone number, should start with + and
follows by country code
refer to “Identification” object schema

4.1. Identification
Name
identification_value
identification_type

Mandatory
Y
Y

Type
string
string

Description
Value of identity
Type of identity, example: registration_id,
tax_id. Refer to section 6.2 for list of
expected types

Mandatory Type
Conditional string

Description
Must have either IBAN or account Id. Will
prioritize IBAN if both are supplied

4.2. Creditor_account
Name
iban

account_id

Conditional string

currency

N

string

account_type

N

string

account_holder_type N

string

For corridors where IBAN-only validation
is provided, this becomes the only
mandatory field in the request payload
Must have either IBAN or account Id. Will
prioritize IBAN if both supplied
Returns as part of response when
available; currency code, based on ISO
4217. Eg: SGD
Returns as part of response when
available; specifies nature or use of
account Eg: NRE account (for India);
foreign currency account
Returns as part of response when
available; specifies nature of account
holder (personal, non-personal)

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

4.3. Creditor_agent
Name
bic
clearing_system_id
bank_name

Mandatory
Conditional
Conditional
N

Type
string
string
string

bank_address

N

string

payment_scheme_eligibility N

string

Description
BIC code, either 8 or 11 digits
National clearing code
Returns as part of response
when available; name of
institution
Returns as part of response
when available; address of
institution
Returns as part of response
when available; ability of
account to receive funds
through payment schemes
such as SEPA credit transfer,
CORE1 DD, and more
(available for IBAN logic
validation only)

4.4 public_key_hint
Name
start

Manda
tory
Y

Type

Description

string

The first 5 characters of public key after removing start
marker line.
Eg:
-----BEGIN PGP PUBLIC KEY BLOCK----\n\nxpMEY3WsDxMFK4EEACMEIwQAQh1cXFVWUFnDX1
…
U4hteiMNEppb\nbpVr16rHBHeXTMM7zxs+c1xePNJW0raKT+GoZKmSDQ=
=\n=tZj7\n-----END PGP PUBLIC KEY BLOCK-----\n"

end

Y

string

The value should be “xpMEY” after removing marker line and
also new line.
The last 5 characters of public key after removing end marker
line.
Eg:
-----BEGIN PGP PUBLIC KEY BLOCK----\n\nxpMEY3WsDxMFK4EEACMEIwQAQh1cXFVWUFnDX1
…
U4hteiMNEppb\nbpVr16rHBHeXTMM7zxs+c1xePNJW0raKT+GoZKmSDQ=
=\n=tZj7\n-----END PGP PUBLIC KEY BLOCK-----\n"

The value should be “=tZj7” after removing marker line and
also new line.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

5. Appendix
5.1. Response code and message
Other than relying on HTTP status code, we will be using response_code &
response_message within response body to further explain different type response.
HTTP Status Code – 200
Code

Message

2000

Successful Validation

2001

Format Validation Successful

2002

Account Active Confirmed

2100

Creditor Account not found

2101

Creditor Account in flagged
status

2102

Creditor Agent Invalid

2103
2104
2105
2106

Creditor Account details
could not be validated
Creditor Name Match could
not be completed
Format Validation
Unsuccessful
Invalid registration ID

Description
Successfully found creditor_agent and creditor_account
to conduct validation and generate match_score
Logic-based check passed (account structure), most
relevant to IBAN validation
Confirmation of account existence and activeness
(account is in a status to receive funds successfully)
creditor_account.account_id not found from data
provider's response
Creditor Account is closed, dormant or blocked
Invalid BIC code or clearing system ID sent, or unable to
lookup data provider
Data provider could not confirm is account is within
creditor agent
Data provider could not complete name match given valid
account details
Logic-based check failed (account structure), most
relevant to IBAN validation
For validations that require an identification number
component, the id number supplied is missing or invalid

HTTP Status Code - 400
Code
4001
4002

Message
Not Authorised
Decryption Error

4003

Decrypted Request Error

4004
4005

Invalid Corridor
Insufficient Inputs
Rate limit exceeded for this
country

4006

Description
API key invalid
The encrypted payload was unable to be decrypted
The decrypted payload was unable to be parsed and/or
was invalid
Country not supported by Validate
Input fields do not meet corridor requirements
Transaction limit for a corridor has been reached; please
try again later

HTTP Status Code - 500
Code
5000
5001
5002

Message
General exception
Internal server error. Please
contact iPiD Support.
Could not communicate with
financial institution. Please retry.

Description
Unknown error
Either iPiD or data provider service is currently down.
Timeout or traffic issue on data provider side;
resolved with subsequent retry.

5.2. Identity Type
REGISTRATION_ID
TAX_ID
NRIC
PASSPORT
DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

5.3. Scoring Guide
Name Match Score
Match_score and match_score_description refers to the name match assessment
between the sender recipient and the actual recipient identified by the validation
result, serving as an account ownership check. Match_score_description does not
apply to a fixed range of match_score values and takes into consideration the
differences between data providers.
Match Score
Description
Strong Match

Indicative
Match Score
> 0.95

Partial Match

0.50 to 0.95

Weak Match

< 0.50

Description
Creditor Account found and is a highly confident
match.
Creditor Account found and is a partial match.
Additional information can be supplied depending
on the data corridor, such as the identification of
mismatched name part.
Creditor Account found but name is likely not a
match.

Logic Score
Logic_test and logic_score assess the consistency of account structure with the
historically expected account structures for the institution. These fields currently
only apply to the US corridor.
Logic Test
Pass

Indicative
Logic Score
45 - 100

Fail

< 45

Description
Structurally, the account number is consistent with
its account institution (currently only applicable for
us-node-01 and us-node-02)
Structurally, the account number is inconsistent with
its account institution, indicating a potential invalid
or non-existent account (currently only applicable
for us-node-01 and us-node-02)

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

5.4. Corridor Details
Corridor

Integration Type

Coverage

Remarks

Response Time

US

Analytics-based
+ Logic-based

All ABA-registered institution;
incomplete within-institution coverage
due to contributed database
limitations
All ABA-registered institution

Responses based on data consortium, leading to
higher chance of inconclusive results; restricts
access to account holder name

2-5 seconds

Return a logic score on the likelihood of whether
an account and routing code pair exists; does not
perform any name/ownership validation
Real-time microdeposit, returns certainty of
account existence, but no ownership/ name check

1-2 seconds

Validates only domestic currency (INR accounts)

3-7 seconds

Logic-based
Direct-to-FI

All RTP® Network Participating
Financial Institutions (~483 banks /
financial institutions)
All IMPS live members (~855 banks /
financial institutions)
142 major banks / financial
institutions
43 major banks / financial institutions
(NAPAS)
40 major banks / financial institutions
23 major banks / financial institutions

India

Direct-to-FI

Indonesia

Direct-to-FI

Vietnam

Direct-to-FI

Nigeria
Nepal

Direct-to-FI
Direct-to-FI

Pakistan
China

UK

Direct-to-FI
Direct-to-FI
(Individuals)
Direct-to-FI
(Institutions)
Direct-to-FI

32 major banks / financial institutions
2,000+ institutions within UnionPay
network
141 major banks / financial
institutions
~393 mandated institutions compliant
to CoP scheme

South Korea
Bangladesh

Direct-to-FI
Direct-to-FI

Uganda
Brazil

Direct-to-FI
Direct-to-FI

48 major banks / financial institutions
3 banks (Mutual Trust Bank, Dhaka
Bank, Eastern), more in progress
22 major banks / financial institutions
148 major banks / financial
institutions

5-10 seconds

2-3 seconds
Validates only domestic currency (VND accounts)
Restricts access to account holder name to only
those with match_score > 0.8
Validates only domestic currency (PKR accounts)
Restricts access to account holder name
Account holder name returned in certain match
scenarios (exact match with USCC)
Validates only domestic currency (GBP accounts);
restricts access to account holder name to only
partial matches
Validates only domestic currency (KRW accounts)
Restricts access to account holder name
Validates only domestic currency (BRL accounts)

2-3 seconds
2-5 seconds
2-3 seconds
2-3 seconds
2-3 seconds
8-12 seconds
2-5 seconds
2-3 seconds
2-3 seconds
2-5 seconds
5-8 seconds

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor
Mexico

Integration Type
Direct-to-FI

Coverage
129 major banks / financial
institutions

Remarks
Validates only domestic currency (MXN accounts)

Argentina
Belgium
Italy

Direct-to-FI
Hybrid (AB /DFI)
Hybrid (AB /DFI)

Validates only domestic currency (ARL accounts)
Validates business accounts only
Validates business accounts only

Uruguay
Peru
Malaysia
Turkey

Direct-to-FI
Direct-to-FI
Direct-to-FI
Direct-to-FI

63 major banks / financial institutions
All financial institutions; incomplete
within-institution coverage due to
contributed database limitations
17 major banks / financial institutions
4 major banks
37 major banks / financial institutions
50+ major banks/financial institutions

South Africa

Direct-to-FI

11 major banks / financial institutions

France, coming
soon
Netherlands,
coming soon
Poland, coming
soon
Chile, coming soon
Colombia, coming
soon
Ecuador, coming
soon
Saudi Arabia, coming
soon
-many- (IBAN
Validation)

Hybrid (AB /DFI)

All financial institutions; incomplete
within-institution coverage due to
contributed database limitations

Hybrid (AB /DFI)
Hybrid (AB /DFI)
Direct-to-FI
Direct-to-FI
Direct-to-FI
Direct-to-FI

Logic-based

Name returned is masked
Validates only domestic currency (MYR accounts)
Validates only domestic currency (TRY accounts);
name returned is masked
Returns certainty of account existence only;
ownership confirmation subject to availability of
information
Validates business accounts only

Response Time
(requires repeat
request after 5
minutes)
2-5 seconds
5-10 seconds
5-10 seconds
5-8 seconds
5-8 seconds
2-3 seconds
2-5 seconds
5-8 seconds
tba

Validates business accounts only

tba

Validates business accounts only

tba

20 major banks/financial institutions
1 bank (Bancolombia), more in
progress
3 banks (Guayaquil Bank, Pacific Bank,
Produbanco), more in progress
14 major banks/financial institutions

tba
tba

83 IBAN countries: AL, AD, AT, AZ, BH, BE,
BA, BR, BG, BI, CR, HR, CY, CZ, DK, DJ, DO,
EG, SV, EE, FO, FI, FR, GE, DE, GB, GI, GR,
GL, GT, HU, IS, IQ, IE, IL, IT, JO, KZ, XK, KW,
LV, LB, LY, LI, LT, LU, MK, MT, MR, MU, MD,
MC, ME, NL, NO, PK, PS, PL, PT, QA, BY,
RO, RU, LC, SM, ST, SA, RS, SC, SK, SI, SO,
ES, SD, SE, CH, TL, TN, TR, UA, AE, VA, VG

1-2 seconds

tba
tba

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

5.5. Corridor Request and Response Specifications
Corridor

US (analyticsbased) - node 1

Request Parameter Recommendations (preferred inputs with the conditional
fields)
Creditor
Creditor
Creditor
Creditor Agent
Identification
Account
given_name and
n/a
beneficiary
US ABA code as
surname
bank
clearing system ID
account ID

US (logicbased) – node
2
US (Direct-toFI) – node 3

n/a

n/a

n/a

n/a

India

name

n/a

Indonesia

name

n/a

Vietnam

name

n/a

Nigeria

name

n/a

Nepal

name

n/a

Pakistan

name

n/a

China
(individual) –
node 1

name (in Chinese
characters)

n/a

China
(business) –
node 2

name

UK

name

Uniform credit code
(18 char
alphanumeric code)
as registration_id
n/a

Response Specifications
Match_Score

Match_Score_Description

Logic_Score

Data

Discrete values
representative of
strength of match (1, 0.5,
0, etc.)
n/a

Strong, Partial (with
additional detail), Weak

Score value
indicating likelihood
of validity(0…100)

n/a

US ABA code as
clearing system ID

n/a

n/a

n/a

n/a

IFSC code as
clearing system ID

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Supplies full name string

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Supplies full name string

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Supplies full name string in
case of match_score > 0.8
only
Supplies full name string,
currency

n/a

Discrete values
representative of
strength of match (1, 0)

Strong, Weak

n/a

n/a

BIC Code

Continuous variable,
indicating strength of
match (0…1)

Strong, Partial, Weak

n/a

Supplies full name string (in
chinese simplified) and
name translation in UCCmatch scenarios
Supplies full name string in
case of partial match only,
currency

beneficiary
bank
account ID
beneficiary
bank
account ID
beneficiary
bank
account ID
beneficiary
bank
account ID
beneficiary
bank
account ID
NUBAN as
account ID

US ABA code as
clearing system ID

beneficiary
bank
account ID
beneficiary
bank
account ID
or IBAN
beneficiary
bank
account ID
(in 62* PAN
format)
beneficiary
bank
account ID

BIC Code

BIC Code
BIC Code
BIC Code

BIC Code

n/a

n/a

beneficiary
Sort code as
Discrete values
Strong, Partial, Weak
n/a
bank
clearing system ID
representative of
account ID
strength of match (1, 0.5,
or IBAN
0)
South Korea
name
n/a
beneficiary
BIC Code or local
Continuous variable,
Strong, Partial, Weak
n/a
Supplies full name string (in
bank
bank code as
indicating strength of
hangul), name translation,
account ID
clearing system ID
match (0…1)
currency
DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor

Bangladesh

Brazil

Mexico

Request Parameter Recommendations (preferred inputs with the conditional
fields)
Creditor
Creditor
Creditor
Creditor Agent
Identification
Account
name
n/a
beneficiary
BIC Code
bank
(MTBLBDDH,
account ID
DHBLBDDH,
EBLDBDDH)
name
CPF in
IBAN
n/a
000.000.000-00 OR
CNPJ in
00.000.000/000000 format as
registration_id
name
n/a
CLABE as
n/a
account ID

Uganda

name

n/a

beneficiary
bank
account ID
CBU as
account ID

BIC Code

Argentina

name

n/a

Belgium

name

VAT, Company No.
as reg_id

IBAN

n/a

Italy

name

VAT as reg_id

IBAN

n/a

Uruguay

name

n/a

beneficiary
bank
account ID

BIC Code

Peru

name

n/a

CCI as
account ID

n/a

Malaysia

name

n/a

BIC Code

Turkey

name

n/a

beneficiary
bank
account ID
IBAN

South Africa

name

n/a

beneficiary
bank
account ID

BIC Code or branch
code as clearing
system id

-many- (IBAN
Validation)

n/a

n/a

IBAN

n/a

n/a

n/a

Response Specifications
Match_Score

Match_Score_Description

Logic_Score

Data

Discrete values
representative of
strength of match (1, 0)

Strong, \Weak

n/a

n/a

Continuous variable,
indicating strength of
match (0…1)

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Discrete values
representative of
strength of match (1, 0.5,
0)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Discrete values
representative of
strength of match (1, 0.5,
0)
Discrete value (1)
confirmation of matched
ownership when
available
n/a

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Supplies full name string

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Supplies full name string in
registration ID match
scenarios
Supplies full name string in
registration ID match
scenarios
Supplies partially masked
name string, currency

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Supplies full name string,
currency

Strong, Partial, Weak

n/a

Supplies partially masked
name string

Strong match (when
ownership confirmation is
available)

n/a

n/a

n/a

n/a

Supplies bank name, bank
code, BIC, and bank
address

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor

France, coming
soon

Request Parameter Recommendations (preferred inputs with the conditional
fields)
Creditor
Creditor
Creditor
Creditor
Identification
Account
name
VAT, SIREN as
IBAN
n/a
reg_id

Netherlands,
coming soon

ame

VAT as reg_id

IBAN

n/a

Poland,
coming soon

ame

VAT, NIP, REGON
as reg_id

IBAN

n/a

Chile, coming
soon

name

RUT as reg_id

Colombia,
coming soon

name

n/a

Ecuador,
coming soon

Name

n/a

Saudi Arabia,
coming soon

Name

For indivials:
National ID OR
IQAMA
For businesses:
Unified National
Number AND
commercial
registration code

beneficiary
bank
account ID
beneficiary
bank
account ID
beneficiary
bank
account ID
Saudi
Arabia,
coming
soon

BIC Code or local
bank code as
clearing system id
BIC Code or local
bank code as
clearing system id
BIC Code or local
bank code as
clearing system id
n/a

Response Specifications
Match_Score

Match_Score_Description

Logic_Score

Data

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Strong, Partial, Weak

n/a

Supplies full name string in
registration ID match
scenarios
Supplies full name string in
registration ID match
scenarios
Supplies full name string in
registration ID match
scenarios
Supplies full name string

Strong, Partial, Weak

n/a

Supplies full name string

Strong, Partial, Weak

n/a

Supplies full name string

Strong, Partial, Weak

n/a

Supplies full name string

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

5.6. Node Selection
IPiD’s Validate solution is built with optionality and flexibility to local capabilities in mind. Thus, for
certain corridors, there will be multiple nodes available to power validation of various methods.
After passing the designated country code and receiving the public key. Users are advised to pass
the node-id of their preferred method as part of the validation request. Each corridor’s public key
applies to all nodes within that jurisdiction. Listed below are all nodes that serve different methods
of validation for its corridor:
Corridor /
country code
US

CN

IBAN (pass this
in country_code
parameter
during public
key step)

Node ID

Method

Description

us-node-01
us-node-02

Analyticsbased
Logic-based

us-node-03

Direct-to-FI

cn-node-01

Direct-to-FI
(individual)

cn-node-02

Direct-to-FI
(business)

iban-node-01

Logic-based

Lower coverage; ability to validate
ownership/name check
Returns likelihood account and routing
number combination is a valid pair;
definitive in identifying invalid accounts
Real-time microdeposit, returns certainty
of account existence, but no ownership/
name check
Full validation for Chinese bank accounts
for individuals (in the 62* PAN format);
requires account number only; returns
indication of name match only
Full validatnion for Chinese bank accounts
for institutions; requires uniform credit
code, name, bank account number and
BIC; returns full name and translation in
UCC-match scenario
Default node to run logic-based validation
(including those for full validation
countries like GB, BR, PK, BE, FR, IT, NL, PL,
etc.)

Status
LIVE
LIVE
LIVE
LIVE

LIVE

LIVE

To call using one’s preferred method of validation, simply replace the default node-id with the
node-id of one’s preferred method (see in red below).
curl --location 'https://sandbox.ipid.works/validation/api/v1/bank-account/validate' \
--header 'x-api-key: █

█ █ ██ █ █ ██ █ ██ ███ █ '\

--header 'x-customer-id: user-uat' \
--header 'Content-Type: application/json' \
--data '{
"encrypted_payload" : "-----BEGIN PGP MESSAGE----\n\nwcACA1OZJMBCv8a/EgQjBADRPycVQkPBSIxio36TblR/Z4UQCu0kXWzNZE9W\nVg2/sTpdqwDuTgCpVWFtKc9bN1tzipXXeP1
kvRmh5LjK7aTsDQBIlnNwXbfn\ntPUPB/bAZKjOHwoRkFFGTX7lShmdDMiZaBN0HWSGcAJF0lHFPqQrMba2cuWE\nXbcU9+yb6XX
DXgUY+zAHeWEuIdLwWrcyj/oT2m2tVRELHRp1Ma10GgS2ZDu2\nI2QAGGMkZkSKWc2JAkPZmIHSkwH9Rpth1nmC3fyPzePCMXJ
N68r6UIqn8mL9\nyVJm/UImXscgYWxUcDnYKA0/JvwSrev0wF6A3XNDtZ6tw94VfwkztgPEGQqD\nTF0pHQeu8Ok5B0GtOrPtzQIoP
Bdjnxz5eF1rPWHIdhZCmtSTMyPEyvxa7k7U\nhacxfhIUDMGg3Z1Ui1dAJYqhq7iR1Y+JJiS7zPBBcA==\n=K6QR\n-----END PGP
MESSAGE-----\n",
"node_id" : "{{identify node for preferred mode of validation}}"
}'

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an
@ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is strictly prohibited. If you have
received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd.
assumes no liability for any loss or damage arising from the unauthorized use or distribution of this document.

ABOUT iPiD
iPiD is a fast-growing, venture-backed fintech start-up that was founded in late 2021 by a
global team who have held senior roles at major payments and technology companies,
including SWIFT and Thomson Reuters. In addition to our HQ in Singapore, our global team
has representatives in India, Belgium, Malaysia, Netherlands, UAE, Spain, and Vietnam. iPiD’s
vision is to make cross-border payments easy, secure, and seamless. We achieve this by
partnering with financial services providers (banks, payment systems, payment fintechs,
wallets…) to deliver an addressing data platform that helps the payment industry to provide a
more efficient and user-friendly payment journey.
iPiD is built for all – we do not replace banks, payment fintechs, wallets or remittance
companies; nor do we replace existing payment rails. Our Advisory Board includes senior
figures from across the industry: Christian Sarafidis, Microsoft Chief Business Development
Officer WWFSI; Kosta Peric, Deputy Director, Financial Services for the Poor, the Bill & Melinda
Gates Foundation; and Nick Lewins, former banking Chief Technology Officer and now an
advisor in data and AI, cloud technology and digital transformation.
www.ipid.tech

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

