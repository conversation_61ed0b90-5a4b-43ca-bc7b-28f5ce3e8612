<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Command;

use Evp\Bundle\GrsBundle\Exception\GrsKeyManagerException;
use Evp\Bundle\GrsBundle\Service\KeyManager\GrsKeyManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class GenerateKeysCommand extends Command
{
    private GrsKeyManager $grsKeyManager;

    public function __construct(GrsKeyManager $grsKeyManager)
    {
        parent::__construct();

        $this->grsKeyManager = $grsKeyManager;
    }

    protected function configure()
    {
        $this
            ->setName('evp:grs:generate-keys')
            ->setDescription('Allows to generate keys for RS integration')
            ->addArgument(
                'passphrase',
                InputArgument::REQUIRED,
                'Passphrase to protect private key',
            )
            ->addArgument(
                'keysOutputPath',
                InputArgument::REQUIRED,
                'Output folder for generated keys'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $passphrase = $input->getArgument('passphrase');
        $keysOutputPath = $input->getArgument('keysOutputPath');

        if (!file_exists($keysOutputPath)) {
            mkdir($keysOutputPath, 0777, true);
        }

        try {
            $pemKeyPair = $this->grsKeyManager->generateRsaKeyPair($passphrase);
        } catch (GrsKeyManagerException $exception) {
            $output->writeln($exception->getMessage() . PHP_EOL);

            return 1;
        }

        file_put_contents(
            sprintf(
                '%s/private_key.pem',
                $keysOutputPath
            ),
            $pemKeyPair->getPrivateKey()
        );
        file_put_contents(
            sprintf(
                '%s/public_key.pem',
                $keysOutputPath
            ),
            $pemKeyPair->getPublicKey()
        );

        try {
            $publicKeyXml = $this->grsKeyManager->convertPublicRsaKey(
                $pemKeyPair->getPublicKey()
            );
        } catch (GrsKeyManagerException $exception) {
            $output->writeln($exception->getMessage() . PHP_EOL);

            return 1;
        }

        file_put_contents(
            sprintf(
                '%s/public_key.xml',
                $keysOutputPath
            ),
            $publicKeyXml
        );

        $output->writeln('Keys generated!' . PHP_EOL);
        return 0;
    }
}
