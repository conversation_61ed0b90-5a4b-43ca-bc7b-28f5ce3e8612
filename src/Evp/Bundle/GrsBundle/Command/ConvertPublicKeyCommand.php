<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Command;

use Evp\Bundle\GrsBundle\Exception\GrsKeyManagerException;
use Evp\Bundle\GrsBundle\Service\KeyManager\GrsKeyManager;
use Psr\Clock\ClockInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ConvertPublicKeyCommand extends Command
{
    private GrsKeyManager $grsKeyManager;
    private ClockInterface $clock;

    public function __construct(
        GrsKeyManager $grsKeyManager,
        ClockInterface $clock
    ) {
        parent::__construct();

        $this->grsKeyManager = $grsKeyManager;
        $this->clock = $clock;
    }

    protected function configure()
    {
        $this
            ->setName('evp:grs:convert-public-key')
            ->setDescription('Allows to convert public key from PEM to XML (from XML to PEM)')
            ->addArgument(
                'publicKeyPath',
                InputArgument::REQUIRED,
                'Public key path'
            )
            ->addArgument(
                'outputKeyPath',
                InputArgument::REQUIRED,
                'Output folder for the key'
            )
            ->addOption(
                'pemToXml',
                null,
                InputOption::VALUE_OPTIONAL,
                'Convert from PEM to XML',
                false
            )
            ->addOption(
                'xmlToPem',
                null,
                InputOption::VALUE_OPTIONAL,
                'Convert from XML to PEM',
                false
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $publicKeyPath = $input->getArgument('publicKeyPath');
        $outputKeyPath = $input->getArgument('outputKeyPath');
        $pemToXml = $input->getOption('pemToXml') === null;
        $xmlToPem = $input->getOption('xmlToPem') === null;

        if (
            ($pemToXml && $xmlToPem)
            || (!$pemToXml && !$xmlToPem)
        ) {
            $output->writeln('Choose one option!' . PHP_EOL);

            return 1;
        }

        if (!file_exists($publicKeyPath)) {
            $output->writeln('File with public key not exists!');

            return 1;
        }

        if (!file_exists($outputKeyPath)) {
            mkdir($outputKeyPath, 0777, true);
        }

        try {
            $publicKey = $this->grsKeyManager->convertPublicRsaKey(
                file_get_contents($publicKeyPath),
                $pemToXml
            );
        } catch (GrsKeyManagerException $exception) {
            $output->writeln($exception->getMessage() . PHP_EOL);

            return 1;
        }

        file_put_contents(
            sprintf(
                '%s/%s_public_key.%s',
                $outputKeyPath,
                $this->clock->now()->format('Y_m_d_H_m_s'),
                $pemToXml ? 'xml' : 'pem'
            ),
            $publicKey
        );

        $output->writeln('Keys converted!' . PHP_EOL);
        return 0;
    }
}
