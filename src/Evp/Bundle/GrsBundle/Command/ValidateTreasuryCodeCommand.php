<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Command;

use Evp\Bundle\GrsBundle\Service\Validator\TreasuryCodeValidator;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Paysera\Component\ConsoleProgressBarHelper\Exception\ConsoleProgressBarHelperException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ValidateTreasuryCodeCommand extends Command
{
    private TreasuryCodeValidator $treasuryCodeValidator;
    private ConsoleProgressBarHelper $progressBarHelper;

    public function __construct(
        TreasuryCodeValidator $treasuryCodeValidator,
        ConsoleProgressBarHelper $progressBarHelper
    ) {
        parent::__construct();

        $this->treasuryCodeValidator = $treasuryCodeValidator;
        $this->progressBarHelper = $progressBarHelper;
    }

    protected function configure()
    {
        $this
            ->setName('evp:grs:validate-treasury-code')
            ->setDescription('Allows to validate treasury code')
            ->addArgument(
                'codes',
                InputArgument::REQUIRED | InputArgument::IS_ARRAY,
                'Treasury codes separated by space. (Example: console command code1 code2)'
            )
        ;
    }

    /**
     * @throws ConsoleProgressBarHelperException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $codes = $input->getArgument('codes');

        $progressBar = $this->progressBarHelper->createProgressBar($output, count($codes));
        $progressBar->setRedrawFrequency(1);

        $table = new Table($output);
        $table->setHeaders(['Treasury code', 'Result']);

        foreach ($codes as $code) {
            $progressBar->advance();
            $valid = $this->treasuryCodeValidator->isValid($code);

            $formattedResult = $this->getFormattedResult($valid);
            $table->addRow([$code, $formattedResult]);
        }

        $output->writeln('');
        $table->render();

        $output->writeln("\nCommand finished.");
        return 0;
    }

    private function getFormattedResult(bool $valid): string
    {
        return $valid ? '<info>valid</info>' : '<error>invalid</error>';
    }
}
