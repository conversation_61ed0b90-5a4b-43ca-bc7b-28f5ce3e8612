<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Tests\Listener;

use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Event\AccountEvent;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\Event\ClientEvent;
use Evp\Bundle\ClientBundle\Entity\Event\ClientPartnerAssignmentChangedEvent;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Entity\PartnerClient;
use Evp\Bundle\ClientBundle\Repository\PartnerClientRepository;
use Evp\Bundle\GrsBundle\Listener\ClientListener;
use Evp\Bundle\GrsBundle\Service\ClientReportingHelper;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\QuestionnaireBundle\Entity\Questionnaire;
use Evp\Bundle\QuestionnaireBundle\Event\QuestionnaireEvent;
use Evp\Bundle\QuestionnaireBundle\QuestionnaireEvents;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Tests\BaseTestCase;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Debug\BufferingLogger;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class ClientListenerTest extends BaseTestCase
{
    private ClientListener $clientListener;
    private EventDispatcherInterface $eventDispatcher;
    private FixturesHelper $fixturesHelper;
    private MockObject $partnerClientRepository;
    private MockObject $publisher;
    private MockObject $deferredPublisher;
    private MockObject $logger;

    /** @var ClientNatural|MockObject */
    private $clientNatural;

    protected function setUp(): void
    {
        $this->clientNatural = $this->createMock(ClientNatural::class);
        $this->partnerClientRepository = $this->createMock(PartnerClientRepository::class);
        $this->logger = $this->createMock(BufferingLogger::class);
        $this->getContainer()->set('evp_client.repository.partner_client', $this->partnerClientRepository);
        $this->publisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set('evp_grs.publisher.grs_remote_job_publisher', $this->publisher);
        $this->deferredPublisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set('evp_grs.publisher.grs_deferred_remote_job_publisher', $this->deferredPublisher);
        $this->clientListener = new ClientListener(
            $this->getContainer()->get('evp_grs.service.client_reporting_helper'),
            $this->logger
        );
        $this->eventDispatcher = $this->getContainer()->get('event_dispatcher');
        $this->fixturesHelper = new FixturesHelper();
    }

    /**
     * @param array $clientNaturalOptions
     * @param array $publisherOptions
     * @dataProvider dataProviderForTestOnClientPartnerChanged
     */
    public function testOnClientPartnerChanged(
        array $clientNaturalOptions,
        array $publisherOptions
    ): void {
        $this->configureClientNaturalMock($clientNaturalOptions);
        $this->configurePartnerClientRepositoryMock($this->clientNatural);
        $this->configurePublisherMock($publisherOptions);
        $event = new ClientPartnerAssignmentChangedEvent($this->clientNatural);
        $this->clientListener->onClientPartnerChanged($event);
    }

    public function dataProviderForTestOnClientPartnerChanged(): array
    {
        return [
            'Case 1: client natural - identified' => [
                ['identificationLevel' => IdentificationLevels::IDENTIFIED],
                [
                    'jobKey' => ClientReportingHelper::OPENED_JOB_KEY,
                    'expects' => self::once(),
                ],
            ],
            'Case 2: client natural - unidentified' => [
                ['identificationLevel' => IdentificationLevels::UNIDENTIFIED],
                [
                    'jobKey' => ClientReportingHelper::OPENED_JOB_KEY,
                    'expects' => self::never(),
                ],
            ],
        ];
    }

    public function testOnClientBecameVerified(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $this->configurePublisherMock(['client' => $clientNatural, 'jobKey' => ClientReportingHelper::OPENED_JOB_KEY]);
        $event = new ClientEvent($clientNatural);
        $this->clientListener->onClientBecameVerified($event);
    }

    public function testOnClientBecameUnverified(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $this->configurePublisherMock(['client' => $clientNatural, 'jobKey' => ClientReportingHelper::CLOSED_JOB_KEY]);
        $event = new ClientEvent($clientNatural);
        $this->clientListener->onClientBecameUnverified($event);
    }

    public function testOnAccountOpened(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $this->configurePublisherMock(['client' => $clientNatural, 'jobKey' => ClientReportingHelper::OPENED_JOB_KEY]);
        $account = self::createMock(Account::class);
        $account->expects(self::any())
            ->method('getClient')
            ->willReturn($clientNatural)
        ;
        $event = new AccountEvent($account);
        $this->clientListener->onAccountOpened($event);
    }

    public function testOnAccountOpenedPartnerException(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $account = self::createMock(Account::class);
        $account->expects(self::any())
            ->method('getClient')
            ->willReturn($clientNatural)
        ;
        $this->partnerClientRepository->expects(self::once())
            ->method('findOneLatestByClient')
            ->willReturn(null)
        ;
        $this->configurePublisherMock(['expects' => self::never()]);

        $event = new AccountEvent($account);
        $this->clientListener->onAccountOpened($event);
    }

    public function testOnAccountClosed(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $this->configurePublisherMock(['client' => $clientNatural, 'jobKey' => ClientReportingHelper::CLOSED_JOB_KEY]);
        $account = $this->createMock(Account::class);
        $account->expects(self::any())
            ->method('getClient')
            ->willReturn($clientNatural)
        ;
        $event = new AccountEvent($account);
        $this->clientListener->onAccountClosed($event);
    }

    public function testOnQuestionnaireAcceptance(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $this->removeOtherQuestionnaireListeners();

        $this->publisher
            ->expects($this->never())
            ->method('publishJob')
        ;
        $this->deferredPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(ClientReportingHelper::OPENED_JOB_KEY, ['clientId' => $clientNatural->getId()])
        ;

        $this->eventDispatcher->dispatch(
            QuestionnaireEvents::QUESTIONNAIRE_QUESTIONNAIRE_BECAME_ACCEPTED,
            new QuestionnaireEvent(
                $this->fixturesHelper->createNaturalQuestionnaire(Questionnaire::STATUS_ACCEPTED, $clientNatural)
            )
        );
    }

    public function testOnQuestionnaireAcceptanceExceptionsHandling(): void
    {
        $clientNatural = $this->configureClientNaturalMock();
        $this->configurePartnerClientRepositoryMock($clientNatural);
        $exception = new Exception();
        $this->deferredPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->willThrowException($exception)
        ;
        $this->removeOtherQuestionnaireListeners();

        $this->eventDispatcher->dispatch(
            QuestionnaireEvents::QUESTIONNAIRE_QUESTIONNAIRE_BECAME_ACCEPTED,
            new QuestionnaireEvent(
                $this->fixturesHelper->createNaturalQuestionnaire(Questionnaire::STATUS_ACCEPTED, $clientNatural)
            )
        );

        $this->expectErrorLog(ClientListener::EMAILED_LOG_PREFIX . 'exception in ClientListener');
    }

    private function configureClientNaturalMock(array $options = []): ClientNatural
    {
        $identificationLevel = $options['identificationLevel'] ?? IdentificationLevels::IDENTIFIED;
        $this->clientNatural->expects(self::any())
            ->method('getLevel')
            ->willReturn($identificationLevel)
        ;
        $this->clientNatural->expects(self::any())
            ->method('getId')
            ->willReturn(1)
        ;

        return $this->clientNatural;
    }

    private function configurePartnerClientRepositoryMock(ClientNatural $clientNatural): void
    {
        $partnerClient = new PartnerClient(LicensedPartner::PAYSERA_GEORGIA, $clientNatural);
        $this->partnerClientRepository->expects(self::once())
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;
    }

    private function configurePublisherMock(array $options): void
    {
        $client = $options['client'] ?? $this->clientNatural;
        $jobKey = $options['jobKey'] ?? ClientReportingHelper::OPENED_JOB_KEY;
        $expects = $options['expects'] ?? self::once();
        $data = ['clientId' => $client->getId()];
        $this->publisher->expects($expects)
            ->method('publishJob')
            ->with($jobKey, $data)
        ;
    }

    private function removeOtherQuestionnaireListeners(): void
    {
        $listeners = $this->eventDispatcher->getListeners(QuestionnaireEvents::QUESTIONNAIRE_QUESTIONNAIRE_BECAME_ACCEPTED);
        foreach ($listeners as $listener) {
            if (!$listener[0] instanceof ClientListener) {
                $this->eventDispatcher->removeListener(
                    QuestionnaireEvents::QUESTIONNAIRE_QUESTIONNAIRE_BECAME_ACCEPTED,
                    $listener
                );
            }
        }
    }
}
