<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Tests\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\TreasuryCode;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\TreasuryCodeExists;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\TreasuryCodeFilter;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\TreasuryCodesResult;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\GeorgiaRevenueServiceApiClient;
use Evp\Bundle\GrsBundle\Service\TreasuryCodeManager;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Tests\BaseTestCase;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LogLevel;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Throwable;

class TreasuryCodeAdminControllerTest extends BaseTestCase
{
    /** @var RemoteJobPublisherInterface|MockObject */
    private $publisher;
    private MockObject $apiClient;
    private EntityManager $entityManager;

    /**
     * @throws Throwable
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->getMockEntityManager();

        $this->publisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set('evp_grs.publisher.grs_high_load_remote_job_publisher', $this->publisher);
        $this->apiClient = $this->createMock(GeorgiaRevenueServiceApiClient::class);
        $this->getContainer()->set('evp_grs.client.georgia_revenue_service_api_client', $this->apiClient);
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request(Request::METHOD_GET, '/admin/evp/grs/treasury-code/list');

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->assertNotFalse(
            strpos($this->client->getResponse()->headers->get('Location'), '/login')
        );
    }

    public function testInsufficientPrivileges(): void
    {
        $this->logIn(['SOME_USER']);
        $this->client->request(Request::METHOD_GET, '/admin/evp/grs/treasury-code/list');

        $this->assertTrue($this->client->getResponse()->isForbidden());
    }

    public function testShowStateTreasuryAnnexCodeAction(): void
    {
        $treasuryCode = new TreasuryCode([
            'id' => 1,
            'code' => '123456789',
            'description' => 'გარკვეული აღწერა',
            'beneficiary' => 'ზოგიერთი ბენეფიციარი',
        ]);

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCode')
            ->with($treasuryCode->getCode())
            ->willReturn($treasuryCode)
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/treasury-code/{$treasuryCode->getCode()}/show"
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());

        $content = $this->client->getResponse()->getContent();

        $this->assertStringContainsString($treasuryCode->getCode(), $content);
        $this->assertStringContainsString($treasuryCode->getDescription(), $content);
        $this->assertStringContainsString($treasuryCode->getBeneficiary(), $content);

        $this->expectNoErrorsLogged();
    }

    public function testShowStateTreasuryAnnexCodeActionFailed(): void
    {
        $code = '123456789';

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCode')
            ->with($code)
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/treasury-code/$code/show"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while getting treasury code'
        );
    }

    public function testListAction(): void
    {
        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodes')
            ->with(new TreasuryCodeFilter(['offset' => 0, 'limit' => 64]))
            ->willReturn(new TreasuryCodesResult([
                'items' => [
                    new TreasuryCode([
                        'id' => 1,
                        'code' => '123456789',
                        'description' => 'გარკვეული აღწერა',
                        'beneficiary' => 'ზოგიერთი ბენეფიციარი',
                    ]),
                    new TreasuryCode([
                        'id' => 2,
                        'code' => '123123123',
                        'description' => 'გარკვეული აღწერა',
                        'beneficiary' => 'ზოგიერთი ბენეფიციარი',
                    ]),
                ],
                '_metadata' => [
                    'total' => 2,
                    'limit' => 64,
                    'cursors' => [
                        'after' => '2',
                        'before' => '1',
                    ],
                    'offset' => 0,
                    'has_next' => false,
                    'has_previous' => false

                ]
            ]))
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            '/admin/evp/grs/treasury-code/list'
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->expectNoErrorsLogged();
    }

    public function testListActionFailed(): void
    {
        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodes')
            ->with(new TreasuryCodeFilter(['offset' => 0, 'limit' => 64]))
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            '/admin/evp/grs/treasury-code/list'
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while getting treasury codes'
        );
    }

    public function testCreateAction(): void
    {
        $data = [
            'code' => '123456789',
            'description' => 'გარკვეული აღწერა',
            'beneficiary' => 'ზოგიერთი ბენეფიციარი',
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodeExists')
            ->with($data['code'])
            ->willReturn(new TreasuryCodeExists(['exists' => false]))
        ;
        $this->apiClient->expects($this->once())
            ->method('createTreasuryCodeDebt')
            ->with(new TreasuryCode($data))
            ->willReturn(new TreasuryCode(array_merge($data, ['id' => 1])))
        ;

        $this->sendCreateForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    public function testCreateActionFailed(): void
    {
        $data = [
            'code' => '123456789',
            'description' => 'გარკვეული აღწერა',
            'beneficiary' => 'ზოგიერთი ბენეფიციარი',
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodeExists')
            ->with($data['code'])
            ->willReturn(new TreasuryCodeExists(['exists' => false]))
        ;
        $this->apiClient->expects($this->once())
            ->method('createTreasuryCodeDebt')
            ->with(new TreasuryCode($data))
            ->willThrowException(new Exception())
        ;

        $this->sendCreateForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while creating treasury code'
        );
    }

    public function testCreateActionIfCodeAlreadyExists(): void
    {
        $data = [
            'code' => '123456789',
            'description' => 'გარკვეული აღწერა',
            'beneficiary' => 'ზოგიერთი ბენეფიციარი',
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodeExists')
            ->with($data['code'])
            ->willReturn(new TreasuryCodeExists(['exists' => true]))
        ;
        $this->apiClient->expects($this->never())
            ->method('createTreasuryCodeDebt')
        ;

        $this->sendCreateForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    /** @dataProvider invalidDataProvider */
    public function testCreateActionFailedValidation(array $data, string $expectedErrorMessage): void
    {
        $this->apiClient->expects($this->never())
            ->method('getDebtTreasuryCodeExists')
        ;
        $this->apiClient->expects($this->never())
            ->method('createTreasuryCodeDebt')
        ;

        $this->sendCreateForm($data);

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->assertStringContainsString($expectedErrorMessage, $this->client->getResponse()->getContent());
    }

    public function testCreateActionFailedChecksIfCodeExists(): void
    {
        $data = [
            'code' => '123456789',
            'description' => 'გარკვეული აღწერა',
            'beneficiary' => 'ზოგიერთი ბენეფიციარი',
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtTreasuryCodeExists')
            ->with($data['code'])
            ->willThrowException(new Exception())
        ;

        $this->apiClient->expects($this->once())
            ->method('createTreasuryCodeDebt')
            ->with(new TreasuryCode($data))
            ->willReturn(new TreasuryCode(array_merge($data, ['id' => 1])))
        ;

        $this->sendCreateForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while checking treasury code'
        );
    }

    public function testDeleteAction(): void
    {
        $code = '123456789';

        $this->apiClient->expects($this->once())
            ->method('deleteDebtTreasuryCode')
            ->with($code)
            ->willReturn(null)
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_DELETE',
        ]);

        $this->client->request(
            Request::METHOD_DELETE,
            "/admin/evp/grs/treasury-code/$code/delete"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    public function testDeleteActionFailed(): void
    {
        $code = '123456789';

        $this->apiClient->expects($this->once())
            ->method('deleteDebtTreasuryCode')
            ->with($code)
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_DELETE',
        ]);

        $this->client->request(
            Request::METHOD_DELETE,
            "/admin/evp/grs/treasury-code/$code/delete"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while deleting treasury code'
        );
    }

    public function testImportAction(): void
    {
        $filePath = __DIR__ . '/../Resource/TreasuryCode/treasury_codes.txt';
        $expectedTreasuryCodes = array_filter(array_map('trim', file($filePath)));
        $expectedArguments = [];

        $expectedArguments[] = [TreasuryCodeManager::TREASURY_CODES_IMPORT_JOB_KEY, [
            TreasuryCodeManager::DATA_ACTION => TreasuryCodeManager::ACTION_DEACTIVATE_OLD_CODES,
        ]];

        foreach ($expectedTreasuryCodes as $code) {
            $expectedArguments[] = [TreasuryCodeManager::TREASURY_CODES_IMPORT_JOB_KEY, [
                TreasuryCodeManager::DATA_ACTION => TreasuryCodeManager::ACTION_IMPORT_CODE,
                TreasuryCodeManager::DATA_TREASURY_CODE => $code,
            ]];
        }

        $expectedArguments[] = [TreasuryCodeManager::TREASURY_CODES_IMPORT_JOB_KEY, [
            TreasuryCodeManager::DATA_ACTION => TreasuryCodeManager::ACTION_REMOVE_DEPRECATED_CODES,
            TreasuryCodeManager::DATA_ACTUAL_CODES_QUANTITY => count($expectedTreasuryCodes),
        ]];

        $this->publisher->expects($this->exactly(count($expectedArguments)))
            ->method('publishJob')
            ->withConsecutive(...$expectedArguments)
        ;

        $this->sendImportForm($filePath);

        $this->assertTrue($this->client->getResponse()->isRedirect());
    }

    public function testImportActionFailed(): void
    {
        $filePath = __DIR__ . '/../Resource/TreasuryCode/invalid_treasury_codes.txt';

        $this->publisher->expects($this->never())->method('publishJob');

        $this->sendImportForm($filePath);

        $this->assertTrue($this->client->getResponse()->isRedirect());
    }

    public function invalidDataProvider(): array
    {
        return [
            'Case 1: Code too short' => [
                'data' => [
                    'code' => '123',
                ],
                'expectedMessage' => 'This value should have exactly 9 characters.',
            ],
            'Case 2: Code too long' => [
                'data' => [
                    'code' => '12345678912',
                ],
                'expectedMessage' => 'This value should have exactly 9 characters.',
            ],
            'Case 3: Code is not numeric' => [
                'data' => [
                    'code' => 'test',
                    'beneficiary' => 'ზოგიერთი ბენეფიციარი',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => 'Please, enter a numeric value.',
            ],
            'Case 4: Code was not provided' => [
                'data' => [
                    'code' => '',
                    'beneficiary' => 'ზოგიერთი ბენეფიციარი',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => ' Please, enter a valid code.',
            ],
            'Case 5: Too long beneficiary provided' => [
                'data' => [
                    'code' => '123123123',
                    'beneficiary' => 'ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი',
                    'description' => 'ტესტი',
                ],
                'expectedMessage' => 'This value is too long. It should have 255 characters or less.',
            ],
            'Case 6: Too long description provided' => [
                'data' => [
                    'code' => '123123123',
                    'beneficiary' => 'ტესტი',
                    'description' => 'ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი',
                ],
                'expectedMessage' => 'This value is too long. It should have 400 characters or less.',
            ],
        ];
    }

    private function sendCreateForm(array $data): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_CREATE',
        ]);

        $crawler = $this->client->request(
            Request::METHOD_GET,
            '/admin/evp/grs/treasury-code/create'
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());

        $buttonCrawlerNode = $crawler->selectButton('Add');
        $form = $buttonCrawlerNode->form();

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches) && array_key_exists($matches[1], $data)) {
                $form->setValues([$key => $data[$matches[1]]]);
            }
        }

        $this->client->submit($form);
    }

    private function sendImportForm(string $filePath): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_LIST',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_VIEW',
            'ROLE_EVP_GRS_ADMIN_TREASURY_CODE_CREATE',
        ]);

        $crawler = $this->client->request(
            Request::METHOD_GET,
            '/admin/evp/grs/treasury-code/import'
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());

        $buttonCrawlerNode = $crawler->selectButton('Import');
        $form = $buttonCrawlerNode->form();

        $form->setValues([
            'treasury_code_import[upload]' => new UploadedFile($filePath, 'treasury_codes.txt'),
        ]);

        $this->client->submit($form);
    }

    private function logIn(array $roles): void
    {
        $session = $this->client->getContainer()->get('session');

        $user = new User();
        $user
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $token = new UsernamePasswordToken($user, null, 'main', $roles);

        $session->set('_security_main', serialize($token));
        $session->save();

        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    private function expectErrorsLoggedMessage(string $message): void
    {
        $logger = $this->getContainer()->get('logger');

        $logs = array_filter(array_map(function (array $logEntry) {
            return in_array($logEntry[0], [
                LogLevel::EMERGENCY,
                LogLevel::ALERT,
                LogLevel::CRITICAL,
                LogLevel::ERROR,
            ], true) ? $logEntry[1] : null;
        }, $logger->cleanLogs()));

        $this->assertContains($message, $logs);
    }
}
