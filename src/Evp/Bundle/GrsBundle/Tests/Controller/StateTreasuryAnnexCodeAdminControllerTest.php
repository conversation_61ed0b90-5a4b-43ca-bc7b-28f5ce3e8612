<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Tests\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCode;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCodeExists;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCodeFilter;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCodesResult;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\GeorgiaRevenueServiceApiClient;
use Evp\Tests\BaseTestCase;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LogLevel;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class StateTreasuryAnnexCodeAdminControllerTest extends BaseTestCase
{
    private EntityManager $entityManager;
    private MockObject $apiClient;

    protected function setUp(): void
    {
        $this->entityManager = $this->getMockEntityManager();
        $this->apiClient = $this->createMock(GeorgiaRevenueServiceApiClient::class);
        $this->getContainer()->set('evp_grs.client.georgia_revenue_service_api_client', $this->apiClient);
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request(Request::METHOD_GET, '/admin/evp/grs/state-treasury-annex-code/list');

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->assertNotFalse(
            strpos($this->client->getResponse()->headers->get('Location'), '/login')
        );
    }

    public function testInsufficientPrivileges(): void
    {
        $this->logIn(['SOME_USER']);
        $this->client->request(Request::METHOD_GET, '/admin/evp/grs/state-treasury-annex-code/list');

        $this->assertTrue($this->client->getResponse()->isForbidden());
    }

    public function testShowStateTreasuryAnnexCodeAction(): void
    {
        $stateTreasuryAnnexCode = new StateTreasuryAnnexCode([
            'id' => 1,
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ]);

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCode')
            ->with($stateTreasuryAnnexCode->getId())
            ->willReturn($stateTreasuryAnnexCode)
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/state-treasury-annex-code/{$stateTreasuryAnnexCode->getId()}/show"
        );

        $content = $this->client->getResponse()->getContent();

        $this->assertTrue($this->client->getResponse()->isSuccessful());

        $this->assertStringContainsString($stateTreasuryAnnexCode->getCode(), $content);
        $this->assertStringContainsString($stateTreasuryAnnexCode->getDescription(), $content);
        $this->assertStringContainsString($stateTreasuryAnnexCode->getType(), $content);

        $this->expectNoErrorsLogged();
    }

    public function testShowStateTreasuryAnnexCodeActionFailed(): void
    {
        $id = 1;

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCode')
            ->with($id)
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/state-treasury-annex-code/$id/show"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while getting state treasury annex code'
        );
    }

    public function testListAction(): void
    {
        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodes')
            ->with(new StateTreasuryAnnexCodeFilter(['offset' => 0, 'limit' => 64]))
            ->willReturn(new StateTreasuryAnnexCodesResult([
                'items' => [
                    new StateTreasuryAnnexCode([
                        'id' => 1,
                        'code' => '1',
                        'type' => 'annex-1',
                        'description' => 'გარკვეული აღწერა',
                    ]),
                    new StateTreasuryAnnexCode([
                        'id' => 2,
                        'code' => '2',
                        'type' => 'annex-2',
                        'description' => 'გარკვეული აღწერა',
                    ]),
                ],
                '_metadata' => [
                    'total' => 2,
                    'limit' => 64,
                    'cursors' => [
                        'after' => '2',
                        'before' => '1',
                    ],
                    'offset' => 0,
                    'has_next' => false,
                    'has_previous' => false

                ]
            ]))
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/state-treasury-annex-code/list"
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->expectNoErrorsLogged();
    }

    public function testListActionFailed(): void
    {
        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodes')
            ->with(new StateTreasuryAnnexCodeFilter(['offset' => 0, 'limit' => 64]))
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
        ]);

        $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/state-treasury-annex-code/list"
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while getting state treasury annex codes'
        );
    }

    public function testCreateAction(): void
    {
        $data = [
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodeExists')
            ->with($data['code'], $data['type'])
            ->willReturn(new StateTreasuryAnnexCodeExists(['exists' => false]))
        ;
        $this->apiClient->expects($this->once())
            ->method('createStateTreasuryAnnexCodeDebt')
            ->with(new StateTreasuryAnnexCode($data))
            ->willReturn(new StateTreasuryAnnexCode(array_merge($data, ['id' => 1])))
        ;

        $this->sendForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    public function testCreateActionFailed(): void
    {
        $data = [
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodeExists')
            ->with($data['code'], $data['type'])
            ->willReturn(new StateTreasuryAnnexCodeExists(['exists' => false]))
        ;
        $this->apiClient->expects($this->once())
            ->method('createStateTreasuryAnnexCodeDebt')
            ->with(new StateTreasuryAnnexCode($data))
            ->willThrowException(new Exception())
        ;

        $this->sendForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while creating state treasury annex code'
        );
    }

    public function testCreateActionIfCodeAlreadyExists(): void
    {
        $data = [
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodeExists')
            ->with($data['code'], $data['type'])
            ->willReturn(new StateTreasuryAnnexCodeExists(['exists' => true]))
        ;
        $this->apiClient->expects($this->never())
            ->method('createStateTreasuryAnnexCodeDebt')
        ;

        $this->sendForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    /** @dataProvider invalidDataProvider */
    public function testCreateActionFailedValidation(array $data, string $expectedErrorMessage): void
    {
        $this->apiClient->expects($this->never())
            ->method('getDebtStateTreasuryAnnexCodeExists')
        ;
        $this->apiClient->expects($this->never())
            ->method('createStateTreasuryAnnexCodeDebt')
        ;

        $this->sendForm($data);

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->assertStringContainsString($expectedErrorMessage, $this->client->getResponse()->getContent());
    }

    public function testCreateActionFailedChecksIfCodeExists(): void
    {
        $data = [
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ];

        $this->apiClient->expects($this->once())
            ->method('getDebtStateTreasuryAnnexCodeExists')
            ->with($data['code'])
            ->willThrowException(new Exception())
        ;
        $this->apiClient->expects($this->once())
            ->method('createStateTreasuryAnnexCodeDebt')
            ->with(new StateTreasuryAnnexCode($data))
            ->willReturn(new StateTreasuryAnnexCode(array_merge($data, ['id' => 1])))
        ;

        $this->sendForm($data);

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while checking state treasury annex code'
        );
    }

    public function testEditAction(): void
    {
        $data = [
            'code' => '1234',
            'type' => 'annex-2',
            'description' => 'ზოგიერთი განახლებული აღწერა'
        ];
        $oldStateTreasuryAnnexCode = new StateTreasuryAnnexCode([
            'id' => 1,
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ]);
        $updatedStateTreasuryAnnexCode = new StateTreasuryAnnexCode(array_merge($data, ['id' => 1]));

        $this->apiClient->expects($this->atLeastOnce())
            ->method('getDebtStateTreasuryAnnexCode')
            ->with($oldStateTreasuryAnnexCode->getId())
            ->willReturn($oldStateTreasuryAnnexCode)
        ;
        $this->apiClient->expects($this->once())
            ->method('updateDebtStateTreasuryAnnexCode')
            ->with($updatedStateTreasuryAnnexCode->getId(), $updatedStateTreasuryAnnexCode)
            ->willReturn($updatedStateTreasuryAnnexCode)
        ;

        $this->sendForm($data, $oldStateTreasuryAnnexCode->getId());

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    public function testEditActionFailed(): void
    {
        $data = [
            'code' => '1234',
            'type' => 'annex-3',
            'description' => 'ზოგიერთი განახლებული აღწერა'
        ];
        $oldStateTreasuryAnnexCode = new StateTreasuryAnnexCode([
            'id' => 1,
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ]);
        $updatedStateTreasuryAnnexCode = new StateTreasuryAnnexCode(array_merge($data, ['id' => 1]));

        $this->apiClient->expects($this->atLeastOnce())
            ->method('getDebtStateTreasuryAnnexCode')
            ->with($oldStateTreasuryAnnexCode->getId())
            ->willReturn($oldStateTreasuryAnnexCode)
        ;
        $this->apiClient->expects($this->once())
            ->method('updateDebtStateTreasuryAnnexCode')
            ->with($updatedStateTreasuryAnnexCode->getId(), $updatedStateTreasuryAnnexCode)
            ->willThrowException(new Exception())
        ;

        $this->sendForm($data, $oldStateTreasuryAnnexCode->getId());

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while editing state treasury annex code'
        );
    }

    /** @dataProvider invalidDataProvider */
    public function testEditActionFailedValidation(array $data, string $expectedErrorMessage): void
    {
        $oldStateTreasuryAnnexCode = new StateTreasuryAnnexCode([
            'id' => 1,
            'code' => '1',
            'type' => 'annex-1',
            'description' => 'გარკვეული აღწერა'
        ]);

        $updatedStateTreasuryAnnexCode = new StateTreasuryAnnexCode(array_merge($data, ['id' => 1]));

        $this->apiClient->expects($this->atLeastOnce())
            ->method('getDebtStateTreasuryAnnexCode')
            ->with($oldStateTreasuryAnnexCode->getId())
            ->willReturn($oldStateTreasuryAnnexCode)
        ;
        $this->apiClient->expects($this->never())
            ->method('updateDebtStateTreasuryAnnexCode')
            ->with($updatedStateTreasuryAnnexCode->getId(), $updatedStateTreasuryAnnexCode)
        ;

        $this->sendForm($data, $oldStateTreasuryAnnexCode->getId());

        $this->assertTrue($this->client->getResponse()->isSuccessful());
        $this->assertStringContainsString($expectedErrorMessage, $this->client->getResponse()->getContent());
    }

    public function testDeleteAction(): void
    {
        $id = 123;

        $this->apiClient->expects($this->once())
            ->method('deleteDebtStateTreasuryAnnexCode')
            ->with($id)
            ->willReturn(null)
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_DELETE',
        ]);

        $this->client->request(
            Request::METHOD_DELETE,
            "/admin/evp/grs/state-treasury-annex-code/$id/delete"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectNoErrorsLogged();
    }

    public function testDeleteActionFailed(): void
    {
        $id = 123;

        $this->apiClient->expects($this->once())
            ->method('deleteDebtStateTreasuryAnnexCode')
            ->with($id)
            ->willThrowException(new Exception())
        ;

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_DELETE',
        ]);

        $this->client->request(
            Request::METHOD_DELETE,
            "/admin/evp/grs/state-treasury-annex-code/$id/delete"
        );

        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->expectErrorsLoggedMessage(
            'GeorgianRevenueService integration exceptional case: Error while deleting state treasury annex code'
        );
    }

    public function invalidDataProvider(): array
    {
        return [
            'Case 1: Only code provided' => [
                'data' => [
                    'code' => '3',
                    'description' => '',
                ],
                'expectedMessage' => 'Please, provide a description.',
            ],
            'Case 2: Code is not numeric' => [
                'data' => [
                    'code' => 'test',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => 'Please, enter a numeric value.',
            ],
            'Case 3: Code not provided' => [
                'data' => [
                    'code' => '',
                    'type' => 'annex-1',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => 'Please, enter a valid code.',
            ],
            'Case 4: Too long description for annex-2 provided' => [
                'data' => [
                    'code' => '1234',
                    'type' => 'annex-2',
                    'description' => 'ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი',
                ],
                'expectedMessage' => 'For provided type, description should not have more than 255 characters.',
            ],
            'Case 5: Too long description for annex-3 provided' => [
                'data' => [
                    'code' => '1234',
                    'type' => 'annex-3',
                    'description' => 'ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი ტესტი',
                ],
                'expectedMessage' => 'For provided type, description should not have more than 400 characters.',
            ],
            'Case 6: Invalid code length for annex-1 provided' => [
                'data' => [
                    'code' => '123123123',
                    'type' => 'annex-1',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => 'For provided type, code should be exactly 1 character.',
            ],
            'Case 7: Invalid code length for annex-3 provided' => [
                'data' => [
                    'code' => '1',
                    'type' => 'annex-3',
                    'description' => 'სხვა არაკლასიფიცირებული შემოსავალი პროცენტებიდან',
                ],
                'expectedMessage' => 'For provided type, code should be exactly 4 characters.',
            ],
        ];
    }

    private function sendForm(array $data, int $id = null): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_LIST',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_VIEW',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_CREATE',
            'ROLE_EVP_GRS_ADMIN_STATE_TREASURY_ANNEX_CODE_ADMIN_EDIT',
        ]);

        $id ? $action = "/$id/edit" : $action = "/create";

        $crawler = $this->client->request(
            Request::METHOD_GET,
            "/admin/evp/grs/state-treasury-annex-code" . $action
        );

        $this->assertTrue($this->client->getResponse()->isSuccessful());

        $buttonCrawlerNode = $crawler->selectButton('Submit');
        $form = $buttonCrawlerNode->form();

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $data)) {
                    $form->setValues([$key => $data[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
    }

    private function logIn(array $roles): void
    {
        $session = $this->client->getContainer()->get('session');

        $user = new User();
        $user
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $token = new UsernamePasswordToken($user, null, 'main', $roles);

        $session->set('_security_main', serialize($token));
        $session->save();

        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    private function expectErrorsLoggedMessage(string $message): void
    {
        $logger = $this->getContainer()->get('logger');

        $logs = array_filter(array_map(function (array $logEntry) {
            return in_array($logEntry[0], [
                LogLevel::EMERGENCY,
                LogLevel::ALERT,
                LogLevel::CRITICAL,
                LogLevel::ERROR,
            ], true) ? $logEntry[1] : null;
        }, $logger->cleanLogs()));

        $this->assertContains($message, $logs);
    }
}
