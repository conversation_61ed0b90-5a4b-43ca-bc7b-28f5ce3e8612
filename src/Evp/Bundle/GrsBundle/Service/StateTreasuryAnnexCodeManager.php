<?php

declare(strict_types=1);

namespace Evp\Bundle\GrsBundle\Service;

use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCode;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCodeFilter;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\Entity\StateTreasuryAnnexCodesResult;
use Evp\Bundle\GeorgiaRevenueServiceApiClient\GeorgiaRevenueServiceApiClient;
use Exception;
use Psr\Log\LoggerInterface;

class StateTreasuryAnnexCodeManager implements StateTreasuryCodeInterface
{
    private GeorgiaRevenueServiceApiClient $client;
    private LoggerInterface $logger;

    public function __construct(GeorgiaRevenueServiceApiClient $client, LoggerInterface $logger)
    {
        $this->client = $client;
        $this->logger = $logger;
    }

    public function getStateTreasuryAnnexCode(string $id): ?StateTreasuryAnnexCode
    {
        try {
            $stateTreasuryAnnexCode = $this->client->getDebtStateTreasuryAnnexCode($id);
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while getting state treasury annex code',
                [$id, $exception]
            );
        }

        return $stateTreasuryAnnexCode ?? null;
    }

    public function getItemsByFilter(array $filters): ?StateTreasuryAnnexCodesResult
    {
        $stateTreasuryAnnexCodeFilter = new StateTreasuryAnnexCodeFilter();

        foreach($filters as $filter => $value) {
            $setter = 'set' . ucfirst($filter);

            if (method_exists($stateTreasuryAnnexCodeFilter, $setter)) {
                $stateTreasuryAnnexCodeFilter->$setter($value);
            }
        }

        try {
            $result = $this->client->getDebtStateTreasuryAnnexCodes($stateTreasuryAnnexCodeFilter);
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while getting state treasury annex codes',
                [$exception]
            );
        }

        return $result ?? null;
    }

    public function createStateTreasuryAnnexCode(array $data): ?StateTreasuryAnnexCode
    {
        try {
            $stateTreasuryAnnexCode = $this->client->createStateTreasuryAnnexCodeDebt(
                new StateTreasuryAnnexCode($data)
            );
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while creating state treasury annex code',
                [$data, $exception]
            );
        }

        return $stateTreasuryAnnexCode ?? null;
    }

    public function editStateTreasuryAnnexCode(StateTreasuryAnnexCode $stateTreasuryAnnexCode): ?StateTreasuryAnnexCode
    {
        try {
            $id = $stateTreasuryAnnexCode->getId();
            if ($id === null) {
                $this->logger->error(
                    sprintf('%sState treasury annex code id is required', self::ERROR_LOG_PREFIX),
                    [$stateTreasuryAnnexCode]
                );
                return null;
            }

            $updatedStateTreasuryAnnexCode = $this->client->updateDebtStateTreasuryAnnexCode(
                (string) $id,
                $stateTreasuryAnnexCode
            );
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while editing state treasury annex code',
                [$stateTreasuryAnnexCode, $exception]
            );

            return null;
        }

        return $updatedStateTreasuryAnnexCode;
    }

    public function deleteStateTreasuryAnnexCode(string $id): bool
    {
        try {
            $this->client->deleteDebtStateTreasuryAnnexCode($id);
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while deleting state treasury annex code',
                [$id, $exception]
            );

            return false;
        }

        return true;
    }

    public function isStateTreasuryAnnexCodeExists(string $code, string $type): bool
    {
        try {
            return $this->client->getDebtStateTreasuryAnnexCodeExists($code, $type)->isExists();
        } catch (Exception $exception) {
            $this->logger->error(
                self::ERROR_LOG_PREFIX . 'Error while checking state treasury annex code',
                [[$code, $type], $exception]
            );
        }

        return false;
    }
}
