<?php
declare(strict_types=1);

namespace Evp\Bundle\OperatorWidgetBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TransferStatusType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('transferId', 'hidden')
            ->add('status', 'choice', [
                'choices' => [
                    'evp.transferStatus.status.successful' => 'successful',
                    'evp.transferStatus.status.failed' => 'failed',
                ],
                'error_bubbling' => true,
                'label' => 'form.choice.label.status',
                'attr' => ['autocomplete' => 'off'],
            ])
            ->add('failureStatus', TransferFailureStatusType::class);
    }

    /**
     * Get default options
     *
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => 'Evp\Bundle\TransferProviderBundle\Entity\TransferStatus',
        ]);
    }

    public function getName(): string
    {
        return 'evp_transfer_status';
    }
}
