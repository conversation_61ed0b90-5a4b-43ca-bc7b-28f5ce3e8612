<?php

namespace Evp\Bundle\OperatorWidgetBundle\Controller;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Sonata\AdminBundle\Controller\CRUDController as BaseController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

/**
 * Transfer controller
 */
class TransferController extends BaseController
{
    protected string $refreshURLRoute = 'admin_evp_banktransfer_transfer_ajax';

    /**
     * Get list of supported banks
     *
     * @return array
     */
    protected function getBanks()
    {
        $banksToIgnore = $this->getParameter('evp_operator_widget.banks_to_ignore');

        $banks = array_unique(array_merge(
            $this->get('evp_bank_transfer.transfer_configuration')->getBankKeys(),
            $this->getBanksWithReadyTransfers()
        ));

        foreach ($banksToIgnore as $bankToIgnore) {
            $index = array_search($bankToIgnore, $banks);
            if ($index !== false) {
                unset($banks[$index]);
            }
        }

        return $banks;
    }

    /**
     * @return array
     */
    protected function getBanksWithReadyTransfers()
    {
        $banks = [];
        $foundBanks = $this
            ->getDoctrine()
            ->getManager()
            ->getRepository('EvpBankTransferBundle:TransferOut')
            ->findBanksWithExecutableTransfers()
        ;

        $bgBudgetBanks = $this
            ->getDoctrine()
            ->getManager()
            ->getRepository('PayseraBgBudgetPaymentBundle:BgBudgetPaymentTransfer')
            ->findBanksWithExecutableTransfers()
        ;

        $foundBanks = array_merge($foundBanks, $bgBudgetBanks);

        foreach ($foundBanks as $foundBank) {
            $banks[] = $foundBank['bank'];
        }

        return $banks;
    }

    /**
     * @return \Evp\Bundle\OperatorWidgetBundle\Service\BankArranger
     */
    private function getBankArranger()
    {
        return $this->get('evp_operator_widget.bank_arranger');
    }

    /**
     * (non-PHPdoc)
     *
     * @see BaseController::listAction()
     *
     * @param Request $request
     *
     * @return HttpResponse
     */
    public function listAction(Request $request = null)
    {
        $selectedBank = $request->get('bank');

        $list = '';
        $transfersFound = false;
        list($banks, $groupedTransfers) = $this->getBanksAndGroupedTransfers();
        foreach ($banks as $bank) {
            if ($selectedBank !== null) {
                if (is_array($selectedBank) && !in_array($bank, $selectedBank)) {
                    continue;
                } elseif (is_scalar($selectedBank) && $selectedBank !== $bank) {
                    continue;
                }
            }

            $transfers = $this->getTransfersByBank($bank, $groupedTransfers);
            if (!empty($transfers)) {
                $transfersFound = true;
                $content = $this->forward(
                    'EvpOperatorWidgetBundle:Widget:show',
                    [
                        'bank' => $bank,
                        'transfers' => $transfers,
                    ]
                )->getContent();
            } else {
                continue;
            }

            $list .= $this->renderView(
                'EvpOperatorWidgetBundle::container.html.twig', [
                    'id' => $bank,
                    'content' => $content,
                ]
            );
        }

        if (!$transfersFound) {
            $list .= $this->renderView(
                'EvpOperatorWidgetBundle::container.html.twig', [
                    'id' => 'no-transfers',
                    'content' => $this->renderView('EvpOperatorWidgetBundle::empty_list.html.twig'),
                ]
            );
        }

        return $this->renderWithExtraParams('EvpOperatorWidgetBundle::layout.html.twig', [
            'body' => $list,
            'selectedBanks' => (array) $selectedBank,
            'refreshUrlRoute' => $this->refreshURLRoute
        ]);
    }

    /**
     * @param TransferOut[] $transfers
     * @return TransferOut[]
     */
    private function groupTransfersByBank(array $transfers)
    {
        $groupedTransfers = [];
        foreach ($transfers as $transfer) {
            $groupedTransfers[$transfer->getBank()][] = $transfer;
        }

        return $groupedTransfers;
    }

    /**
     * Handle ajax requests
     *
     * @param Request $request
     *
     * @return HttpResponse
     */
    public function ajaxAction(Request $request)
    {
        $response = [
            'list' => [],
        ];

        $settings = $this->getDoctrine()->getManager()->getRepository('EvpOperatorWidgetBundle:OperatorSettings')
            ->findOneByUser($this->getUser());

        if ($settings) {
            $response['settings'] = [
                'hiddenBanks' => $settings->getHiddenBanks(),
            ];

            sort($response['settings']['hiddenBanks']);
        }

        list($banks, $groupedTransfers) = $this->getBanksAndGroupedTransfers();
        foreach ($banks as $bank) {
            $transfers = $this->getTransfersByBank($bank, $groupedTransfers);
            if (!empty($transfers)) {
                $response['list'][$bank] = $this->forward(
                    'EvpOperatorWidgetBundle:Widget:show',
                    [
                        'bank' => $bank,
                        'transfers' => $transfers,
                        'calledByAjax' => true,
                    ]
                )->getContent();
            }
        }

        if (count($response['list']) === 0) {
            $response['list'] = [
                $this->renderView(
                'EvpOperatorWidgetBundle::container.html.twig', [
                    'id' => 'no-transfers',
                    'content' => $this->renderView('EvpOperatorWidgetBundle::empty_list.html.twig'),
                ])
            ];
        }

        return new HttpResponse(json_encode($response));
    }

    /**
     * @return array
     */
    private function getBanksAndGroupedTransfers()
    {
        $banks = $this->getBanks();
        $transfers = $this->get('evp_bank_transfer.repository.transfer_out')->findExecutableTransfersByBank($banks);
        $groupedTransfers = $this->groupTransfersByBank($transfers);
        $banks = $this->getBankArranger()->arrange($banks, $transfers);
        return [$banks, $groupedTransfers];
    }

    /**
     * @param $bank
     * @param array $groupedTransfers
     * @return array
     */
    private function getTransfersByBank($bank, array $groupedTransfers)
    {
        if (array_key_exists($bank, $groupedTransfers)) {
            return $groupedTransfers[$bank];
        } else {
            return [];
        }
    }
}
