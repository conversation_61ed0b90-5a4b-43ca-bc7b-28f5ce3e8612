<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Repository\RemoteOperationRequestRepository;
use Paysera\Bundle\PartnerBundle\Worker\RecreateRemoteOperationRequestWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class RecreateRemoteOperationRequestCommand extends Command
{
    private RemoteOperationRequestRepository $remoteOperationRequestRepository;
    private RemoteJobPublisherInterface $remoteJobPublisher;

    public function __construct(
        RemoteOperationRequestRepository $remoteOperationRequestRepository,
        RemoteJobPublisherInterface $remoteJobPublisher
    ) {
        parent::__construct();

        $this->remoteOperationRequestRepository = $remoteOperationRequestRepository;
        $this->remoteJobPublisher = $remoteJobPublisher;
    }

    public function configure(): void
    {
        $this
            ->setDescription(
                'Publish multiple failed bank_operation remote_operation_requests ' .
                'which were failed to create in accounting-api.'
            )
            ->addOption(
                'limit',
                'l',
                InputOption::VALUE_OPTIONAL,
                'Limit of operations count which failed to create in accounting-api published for reprocessing at once',
                200
            )
        ;
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $operationIds = $this->remoteOperationRequestRepository->findFailedRemoteOperationRequestOperations(
            (int)$input->getOption('limit')
        );

        foreach ($operationIds as $operationId) {
            $this->remoteJobPublisher->publishJob(
                RecreateRemoteOperationRequestWorker::JOB_KEY,
                [
                    RecreateRemoteOperationRequestWorker::KEY_ID => (int)$operationId,
                ]
            );
        }

        return 0;
    }
}
