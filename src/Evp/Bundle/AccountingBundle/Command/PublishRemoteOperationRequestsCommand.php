<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Repository\RemoteOperationRequestRepository;
use Evp\Bundle\AccountingBundle\Service\RemoteOperationRequestPublishOrderOptimizer;
use Paysera\Bundle\PartnerBundle\Worker\ProcessRemoteOperationRequestWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PublishRemoteOperationRequestsCommand extends Command
{
    private RemoteOperationRequestRepository $remoteOperationRequestRepository;
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private string $status;
    private int $limit;
    private RemoteOperationRequestPublishOrderOptimizer $remoteOperationRequestOrderOptimizer;

    public function __construct(
        RemoteOperationRequestRepository $remoteOperationRequestRepository,
        RemoteJobPublisherInterface $remoteJobPublisher,
        string $status,
        int $limit,
        RemoteOperationRequestPublishOrderOptimizer $remoteOperationRequestOrderOptimizer
    ) {
        parent::__construct();
        $this->remoteOperationRequestRepository = $remoteOperationRequestRepository;
        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->status = $status;
        $this->limit = $limit;
        $this->remoteOperationRequestOrderOptimizer = $remoteOperationRequestOrderOptimizer;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publishing remote operation requests jobs to rabbitmq.')
            ->addOption('limit', 'l', InputOption::VALUE_OPTIONAL, 'Custom limit')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $limit = (int)($input->getOption('limit') ?? $this->limit);

        $remoteOperationRequestsData = $this->remoteOperationRequestRepository->findByStatusAsArray(
            $this->status,
            $limit
        );
        if (count($remoteOperationRequestsData) === 0) {
            return;
        }

        $remoteOperationRequestsData = $this->remoteOperationRequestOrderOptimizer->optimize($remoteOperationRequestsData);
        foreach ($remoteOperationRequestsData as $remoteOperationRequest) {
            $this->remoteJobPublisher->publishJob(
                ProcessRemoteOperationRequestWorker::JOB_KEY,
                ['id' => $remoteOperationRequest['id']]
            );
        }
    }
}
