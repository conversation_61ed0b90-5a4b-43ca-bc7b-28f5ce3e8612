<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Exception;
use Evp\Bundle\AccountingBundle\Entity\FailedOperation;
use Psr\Log\LoggerInterface;
use Doctrine\ORM\EntityManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Evp\Bundle\AccountingBundle\Repository\FailedOperationRepository;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Worker\PartnerAccountingOperationReprocessingWorker;

class FailedOperationsPublisherCommand extends Command
{
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private FailedOperationRepository $failedOperationRepository;
    private EntityManager $entityManager;
    private LoggerInterface $logger;

    public function __construct(
        RemoteJobPublisherInterface $remoteJobPublisher,
        FailedOperationRepository $failedOperationRepository,
        EntityManager $entityManager,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->failedOperationRepository = $failedOperationRepository;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publish failed operations to reprocess again')
            ->addArgument(
                'reprocessingRequestId',
                InputArgument::REQUIRED,
                'ID of OperationReprocessingRequest'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $reprocessingRequestId = (int)$input->getArgument('reprocessingRequestId');
        $id = 0;

        do {
            $failedOperations = $this->failedOperationRepository->findPendingWithLimit($reprocessingRequestId, $id, 100);

            foreach ($failedOperations as $failedOperation) {
                $id = $failedOperation->getId();

                try {
                    $failedOperation->setStatus(FailedOperation::STATUS_PUBLISHED);
                    $this->entityManager->flush();
                } catch (Exception $exception) {
                    $output->writeln('Failed to publish: ' . $id);
                    $this->logger->error('Failed to publish failed operation', ['exception' => $exception]);

                    continue;
                }

                $this->remoteJobPublisher->publishJob(
                    PartnerAccountingOperationReprocessingWorker::JOB_KEY,
                    [
                        PartnerAccountingOperationReprocessingWorker::KEY_ID => $id,
                        PartnerAccountingOperationReprocessingWorker::KEY_REQUEST_ID => $reprocessingRequestId,
                    ]
                );
            }

            $this->entityManager->clear();
        } while (count($failedOperations) > 0);

        $output->writeln('Jobs were published!');

        return 0;
    }
}
