<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Paysera\Bundle\PartnerBundle\Repository\RemoteOperationRequestRepository;
use Paysera\Bundle\PartnerBundle\Worker\ProcessRemoteOperationRequestWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PublishSpecificRemoteOperationRequestCommand extends Command
{
    private RemoteOperationRequestRepository $remoteOperationRequestRepository;
    private RemoteJobPublisherInterface $remoteJobPublisher;

    public function __construct(
        RemoteOperationRequestRepository $remoteOperationRequestRepository,
        RemoteJobPublisherInterface $remoteJobPublisher
    ) {
        parent::__construct();
        $this->remoteOperationRequestRepository = $remoteOperationRequestRepository;
        $this->remoteJobPublisher = $remoteJobPublisher;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publish single remote operation request job by id to rabbitmq.')
            ->addArgument('id', InputArgument::REQUIRED, 'RemoteOperationRequest ID')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $id = $input->getArgument('id');

        /** @var RemoteOperationRequest|null $remoteOperationRequest */
        $remoteOperationRequest = $this->remoteOperationRequestRepository->find($id);
        if ($remoteOperationRequest === null) {
            $output->writeln(sprintf('Remote operation request with "%s" id was not found', $id));

            return 2;
        }

        if ($remoteOperationRequest->isFinalStatus()) {
            $output->writeln('Remote operation request in final status and can not be published');

            return 1;
        }

        $this->remoteJobPublisher->publishJob(
            ProcessRemoteOperationRequestWorker::JOB_KEY,
            ['id' => $remoteOperationRequest->getId()]
        );

        return 0;
    }
}
