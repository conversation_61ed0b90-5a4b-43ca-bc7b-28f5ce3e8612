<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Evp\Bundle\BankTransferBundle\Repository\OperationRepository;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Entity\OperationReprocessingRequest;
use Paysera\Bundle\PartnerBundle\Worker\PartnerAccountingOperationReprocessingWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PublishBatchPartnerAccountingOperationReprocessingCommand extends Command
{
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private OperationRepository $operationRepository;
    private EntityManager $entityManager;
    private EntityRepository $userRepository;
    private int $minStartFromId;

    public function __construct(
        RemoteJobPublisherInterface $remoteJobPublisher,
        OperationRepository $operationRepository,
        EntityManager $entityManager,
        int $minStartFromId
    ) {
        parent::__construct();

        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->operationRepository = $operationRepository;
        $this->entityManager = $entityManager;
        $this->userRepository = $this->entityManager->getRepository(User::class);
        $this->minStartFromId = $minStartFromId;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publish batch of partner accounting operations to reprocess by IDs')
            ->addArgument(
                'fromId',
                InputArgument::REQUIRED,
                'First operation id from gateway.bank_operation table'
            )
            ->addArgument(
                'toId',
                InputArgument::REQUIRED,
                'Last operation id from gateway.bank_operation table'
            )
            ->addArgument(
                'username',
                InputArgument::REQUIRED,
                'evpbank username for logging purposes'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $fromId = (int)$input->getArgument('fromId');
        $toId = (int)$input->getArgument('toId');
        if ($toId < 0 || $fromId < 0 || $fromId > $toId) {
            $output->writeln('Wrong input arguments');

            return 1;
        }
        if ($this->minStartFromId > $fromId) {
            $output->writeln(sprintf("'fromId' can not be less than '%s'", $this->minStartFromId));

            return 1;
        }

        $user = $this->userRepository->findOneBy(['username' => $input->getArgument('username')]);
        if ($user === null) {
            $output->writeln('User was not found');

            return 1;
        }

        $reprocessingRequest = (new OperationReprocessingRequest())
            ->setReprocessType(OperationReprocessingRequest::REPROCESS_TYPE_IDS_BATCH)
            ->setStartFromId($fromId)
            ->setLastId($toId)
            ->setTriggeredBy($user)
        ;

        $this->entityManager->persist($reprocessingRequest);

        $this->entityManager->flush();

        $iterator = $this->operationRepository->iterateByIds(
            $fromId,
            $toId
        );
        foreach ($iterator as $data) {
            $this->remoteJobPublisher->publishJob(
                PartnerAccountingOperationReprocessingWorker::JOB_KEY,
                [
                    PartnerAccountingOperationReprocessingWorker::KEY_ID => current($data)['id'],
                    PartnerAccountingOperationReprocessingWorker::KEY_REQUEST_ID => $reprocessingRequest->getId(),
                ]
            );
        }
        $output->writeln('Jobs were published!');

        return 0;
    }
}
