<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Worker\PartnerAccountingOperationReprocessingWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class PublishPartnerAccountingOperationReprocessingCommand extends Command
{
    private RemoteJobPublisherInterface $remoteJobPublisher;

    public function __construct(RemoteJobPublisherInterface $remoteJobPublisher)
    {
        parent::__construct();

        $this->remoteJobPublisher = $remoteJobPublisher;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publish single partner accounting operation to reprocess by ID')
            ->addArgument(
                'id',
                InputArgument::REQUIRED,
                'Specific operation id from gateway.bank_operation table'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->remoteJobPublisher->publishJob(
            PartnerAccountingOperationReprocessingWorker::JOB_KEY,
            [PartnerAccountingOperationReprocessingWorker::KEY_ID => (int)$input->getArgument('id')]
        );

        return 0;
    }
}
