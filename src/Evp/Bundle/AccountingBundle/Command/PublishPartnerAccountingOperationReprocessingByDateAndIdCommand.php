<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Command;

use DateTime;
use Evp\Bundle\BankTransferBundle\Repository\OperationRepository;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Bundle\PartnerBundle\Worker\PartnerAccountingOperationReprocessingWorker;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PublishPartnerAccountingOperationReprocessingByDateAndIdCommand extends Command
{
    private const OPTION_ID_FROM = 'idFrom';
    private const OPTION_ID_TO = 'idTo';
    private const OPTION_DATE_FROM = 'dateFrom';
    private const OPTION_DATE_TO = 'dateTo';
    private const DATE_FORMAT = 'Y-m-d H:i:s';

    private RemoteJobPublisherInterface $remoteJobPublisher;
    private OperationRepository $operationRepository;
    private int $minOperationIdAllowed;

    public function __construct(
        RemoteJobPublisherInterface $remoteJobPublisher,
        OperationRepository $operationRepository,
        int $minOperationIdAllowed
    ) {
        parent::__construct();

        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->operationRepository = $operationRepository;
        $this->minOperationIdAllowed = $minOperationIdAllowed;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Publish single partner accounting operation to reprocess by ID')
            ->addOption(
                self::OPTION_ID_FROM,
                null,
                InputOption::VALUE_OPTIONAL,
                'First operation id from gateway.bank_operation table'
            )
            ->addOption(
                self::OPTION_ID_TO,
                null,
                InputOption::VALUE_OPTIONAL,
                'Last operation id from gateway.bank_operation table'
            )
            ->addOption(
                self::OPTION_DATE_FROM,
                null,
                InputOption::VALUE_OPTIONAL,
                'Starting created_at from gateway.bank_operation table'
            )
            ->addOption(
                self::OPTION_DATE_TO,
                null,
                InputOption::VALUE_OPTIONAL,
                'Ending created_at from gateway.bank_operation table'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $dateFrom = DateTime::createFromFormat(self::DATE_FORMAT, $input->getOption(self::OPTION_DATE_FROM));
        $dateTo = DateTime::createFromFormat(self::DATE_FORMAT, $input->getOption(self::OPTION_DATE_TO));
        if ($dateFrom === false || $dateTo === false) {
            $output->writeln(sprintf('Invalid date format. Required format: %s', self::DATE_FORMAT));

            return 1;
        }
        $idFrom = (int) $input->getOption(self::OPTION_ID_FROM);
        if ($idFrom < $this->minOperationIdAllowed) {
            $output->writeln(sprintf('Invalid idFrom format. Minimum allowed value: %s', $this->minOperationIdAllowed));

            return 1;
        }

        $idTo = $input->getOption(self::OPTION_ID_TO);
        if ($idTo !== null) {
            $idTo = (int) $idTo;
            if ($idTo < $idFrom) {
                $output->writeln(sprintf('Invalid idTo format. It must be more than idFrom: %s', $idFrom));
    
                return 1;
            }
        }


        $operationIterator = $this->operationRepository->iterateByDates(
            $dateFrom,
            $dateTo,
            $idFrom,
            $idTo
        );
        $operationIdsCount = 0;
        foreach ($operationIterator as $operation) {
            $this->remoteJobPublisher->publishJob(
                PartnerAccountingOperationReprocessingWorker::JOB_KEY,
                [PartnerAccountingOperationReprocessingWorker::KEY_ID => current($operation)['id']]
            );
            $operationIdsCount++;
        }

        $output->writeln(sprintf('Success. %s operations published.', $operationIdsCount));

        return 0;
    }
}
