<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Evp\Bundle\AccountingBundle\Entity\FailedOperation;

class FailedOperationRepository extends EntityRepository
{
    /**
     * @param int $reprocessingRequestId
     * @param int $id
     * @param int $limit
     * @return FailedOperation[]
     */
    public function findPendingWithLimit(int $reprocessingRequestId, int $id, int $limit): array
    {
        return $this->createQueryBuilder('fo')
            ->andWhere('fo.reprocessingRequestId = :reprocessingRequestId')
            ->andWhere('fo.status = :status')
            ->andWhere('fo.id > :id')
            ->setParameter('reprocessingRequestId', $reprocessingRequestId)
            ->setParameter('status', FailedOperation::STATUS_PENDING)
            ->setParameter('id', $id)
            ->setMaxResults($limit)
            ->orderBy('fo.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }
}
