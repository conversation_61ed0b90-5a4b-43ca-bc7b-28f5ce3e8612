<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_accounting.service.operation_processor.transfer_out"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\TransferOutOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.bank_resolver"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.transfer_in"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\TransferInOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.transfer_in_commission"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\TransferInCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.transfer_out_commission"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\TransferOutCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutCommissionOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.revoke_transfer_operation"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\RevokeTransferOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferInOperation"/>
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutOperation"/>
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutCommissionOperation"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.partial_return_transfer_operation"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\PartialReturnTransferOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\PartialReturnTransferInOperation"/>
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\PartialReturnTransferOutOperation"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.currency_convert"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\CurrencyConvertOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankAccountBundle\Entity\Operation\CurrencyConvertOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler" />
            <argument type="service" id="evp_currency.currency_type_helper" />
            <argument type="service" id="evp_bank_account.account_owner_resolver" />
            <argument type="service" id="evp_bundle_accounting.service.currency_convert_between_fiat_operation_handler" />
            <argument type="service" id="evp_accounting.service.currency_convert_metal_to_fiat_operation_handler" />
            <argument type="service" id="evp_accounting.service.currency_convert_fiat_to_metal_operation_handler" />
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_contis_accounting.accounting_processor"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="collection">
                <argument>paysera_al</argument>
                <argument>paysera_xk</argument>
            </argument>
        </service>

        <service id="evp_accounting.service.operation_processor.charge_operation_processor"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\ChargeOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankChargeBundle\Entity\ChargeOperation" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in"/>
            <argument>%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_accounting.service.operation_processor.refund_in"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\RefundInOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankRefundBundle\Entity\RefundInOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument>%evp_accounting.operation_processor.paysera_macro_accounts%</argument>
        </service>

        <service id="evp_accounting.service.operation_processor.refund_in_commission"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\RefundInCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankRefundBundle\Entity\RefundInCommissionOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.service.operation_processor.refund_out"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\RefundOutOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.bank_resolver"/>
        </service>

        <service id="evp_accounting.service.operation_processor.refund_out_commission"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\RefundOutCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\BankRefundBundle\Entity\RefundOutCommissionOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_accounting.operation_processor.skip"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\SkipOperationProcessor">
            <argument type="service" id="evp_accounting.operation_processor.accounting_client"/>
        </service>
    </services>
</container>
