<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_accounting.repository.accounting_operation"
                 class="Evp\Bundle\AccountingBundle\Repository\AccountingOperationRepository" lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpAccountingBundle:AccountingOperation</argument>
        </service>

        <service id="evp_accounting.repository.failed_operation"
                 class="Evp\Bundle\AccountingBundle\Repository\FailedOperationRepository" lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpAccountingBundle:FailedOperation</argument>
        </service>
    </services>
</container>
