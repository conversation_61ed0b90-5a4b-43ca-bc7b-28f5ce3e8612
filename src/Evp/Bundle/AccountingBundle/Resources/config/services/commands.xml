<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service class="Evp\Bundle\AccountingBundle\Command\EnqueueOperationsFromListCommand"
                 id="evp_accounting.command.enqueue_operations_from_list_command">
            <tag name="console.command"/>

            <argument id="evp_rabbit_mq_extension.remote_job_publisher" type="service"/>
        </service>
        <service class="Evp\Bundle\AccountingBundle\Command\AccountingReprocessCommand"
                 id="evp_accounting.command.accounting_reprocess_command">
            <tag name="console.command"/>

            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_bank_account.repository.operation" type="service"/>
            <argument id="evp_accounting.service.accounting_reprocessor" type="service"/>
            <argument id="paysera.csv_reader" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_bank_account.command.reprocess_operations.transfer"
                 class="Evp\Bundle\AccountingBundle\Command\ReprocessTransferAccountingOperationsCommand">
            <tag name="console.command" command="evp_bank_account:reprocess_operations:transfer"/>
            <tag name="monolog.logger" channel="paysera_accounting"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_account.repository.operation"/>
            <argument id="evp_accounting.service.accounting_reprocessor" type="service"/>
            <argument id="evp_accounting.service.maintenance.operation_for_reprocess_validator" type="service"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.command.reprocess_operations.transfer.by_date"
                 class="Evp\Bundle\AccountingBundle\Command\ReprocessTransferAccountingOperationsByDateCommand">
            <tag name="console.command" command="evp_bank_account:reprocess_operations:transfer:by_date"/>
            <tag name="monolog.logger" channel="paysera_accounting"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_accounting.service.accounting_reprocessor"/>
            <argument type="service" id="evp_accounting.service.maintenance.operation_for_reprocess_validator"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in_operation"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_out_operation"/>
        </service>

        <service id="evp_bank_account.command.accounting_reprocess_internal_metal_operations_command"
                 class="Evp\Bundle\AccountingBundle\Command\AccountingReprocessInternalMetalOperationsCommand">
            <tag name="console.command" command="evp:accounting:reprocess:internal:precious:metal"/>
            <tag name="monolog.logger" channel="paysera_accounting"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in_operation"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_out_operation"/>
            <argument type="service" id="evp_bank_account.repository.currency_account"/>
            <argument id="evp_accounting.service.accounting_reprocessor" type="service"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.command.publish_bank_operations"
                 class="Evp\Bundle\AccountingBundle\Command\PublishBankOperationsCommand">
            <tag name="console.command" command="evp:accounting:publish-bank-operations"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_accounting.service.operation_publisher"/>
            <argument type="service" id="evp_bank_account.repository.operation"/>
        </service>

        <service id="evp_accounting.command.recreate_remote_operation_request"
                 class="Evp\Bundle\AccountingBundle\Command\RecreateRemoteOperationRequestCommand">
            <tag name="console.command" command="evp:accounting:recreate-remote-operation-request"/>

            <argument type="service" id="paysera_partner.repository.remote_operation_request"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
        </service>

        <service id="evp_accounting.command.publish_specific_remote_operation_request"
                 class="Evp\Bundle\AccountingBundle\Command\PublishSpecificRemoteOperationRequestCommand">
            <tag name="console.command" command="evp:accounting:publish-specific-remote-operation-request"/>

            <argument type="service" id="paysera_partner.repository.remote_operation_request"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
        </service>

        <service id="evp_accounting.command.publish_new_remote_operation_requests"
                 class="Evp\Bundle\AccountingBundle\Command\PublishRemoteOperationRequestsCommand">
            <tag name="console.command" command="evp:accounting:publish-new-remote-operation-requests"/>

            <argument type="service" id="paysera_partner.repository.remote_operation_request"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="constant">Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest::STATUS_NEW</argument>
            <argument>500</argument>
            <argument type="service" id="evp_accounting.service.remote_operation_publish_order_optimizer"/>
        </service>

        <service id="evp_accounting.command.publish_failed_remote_operation_requests"
                 class="Evp\Bundle\AccountingBundle\Command\PublishRemoteOperationRequestsCommand">
            <tag name="console.command" command="evp:accounting:publish-failed-remote-operation-requests"/>

            <argument type="service" id="paysera_partner.repository.remote_operation_request"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="constant">Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest::STATUS_FAILED</argument>
            <argument>5000</argument>
            <argument type="service" id="evp_accounting.service.remote_operation_publish_order_optimizer"/>
        </service>

        <service id="evp_accounting.command.failed_operations_publisher"
                 class="Evp\Bundle\AccountingBundle\Command\FailedOperationsPublisherCommand">
            <tag name="console.command" command="evp:accounting:publish-failed-operations" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="service" id="evp_accounting.repository.failed_operation"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_accounting.command.publish_partner_accounting_operation_reprocessing_by_id_and_date"
                 class="Evp\Bundle\AccountingBundle\Command\PublishPartnerAccountingOperationReprocessingByDateAndIdCommand">
            <tag name="console.command" command="evp:accounting:publish-partner-operation-reprocessing-by-id-and-date" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="service" id="evp_bank_account.repository.operation"/>
            <argument>**********</argument>
        </service>

        <service id="evp_accounting.command.publish_partner_accounting_operation_reprocessing"
                 class="Evp\Bundle\AccountingBundle\Command\PublishPartnerAccountingOperationReprocessingCommand">
            <tag name="console.command" command="evp:accounting:publish-partner-operation-reprocessing" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
        </service>

        <service id="evp_accounting.command.publish_batch_partner_accounting_operations_reprocessing"
                 class="Evp\Bundle\AccountingBundle\Command\PublishBatchPartnerAccountingOperationReprocessingCommand">
            <tag name="console.command" command="evp:accounting:publish-batch-partner-operations-reprocessing" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="service" id="evp_bank_account.repository.operation"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument>**********</argument>
        </service>
    </services>
</container>
