<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Evp\Bundle\AccountingBundle\Entity\FailedOperation" table="accounting_failed_operations"
            repository-class="Evp\Bundle\AccountingBundle\Repository\FailedOperationRepository">
        <indexes>
            <index columns="reprocessing_request_id"></index>
        </indexes>

        <id name="id" type="integer">
            <generator strategy="IDENTITY"/>
        </id>

        <field name="createdAt" type="datetime_immutable" column="created_at"/>
        <field name="reprocessingRequestId" type="integer" column="reprocessing_request_id"/>
        <field name="operationId" type="integer" column="operation_id"/>
        <field name="error" type="string"/>
        <field name="status" type="string" length="16"/>
    </entity>
</doctrine-mapping>
