<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/workers.xml"/>
        <import resource="services/repositories.xml"/>
        <import resource="services/clients.xml"/>
        <import resource="services/commands.xml"/>
        <import resource="services/denormalizers.xml"/>
        <import resource="services/operation_processors.xml"/>
    </imports>

    <parameters>
        <parameter key="evp_accounting.accounting_soap_client.class">Evp\Bundle\AccountingBundle\Service\AccountingSoapClient</parameter>
        <parameter key="evp_accounting.accounting_soap_client.username"/>
        <parameter key="evp_accounting.accounting_soap_client.password"/>
        <parameter key="evp_accounting.accounting_soap_client.user_agent"><![CDATA[Mozilla/5.0 (compatible; SOAP; +http://gateway.mokejimai.lt/)]]></parameter>
        <parameter key="evp_accounting.operation_processor.owner_covenantee_id">2</parameter>
        <parameter key="evp_accounting.account_balance_resolver.class">Evp\Bundle\AccountingBundle\Service\AccountBalanceResolver</parameter>
        <parameter key="evp_accounting.accounting_department_email"><EMAIL></parameter>

        <parameter key="evp_accounting.accounting_group_key_resolver.custom_transfer_number_group_keys" type="collection">
            <parameter key="Bonus fill:">BonusPayout:</parameter>
            <parameter key="Refund:">Refund:</parameter>
            <parameter key="SMSPayout:">SMSPayout:</parameter>
        </parameter>
    </parameters>

    <services>
        <service id="evp_accounting.accounting_soap_client.class_map" parent="besimple.soap.classmap" public="false">
            <call method="set">
                <argument type="collection">
                    <argument key="Evp_Api_Accounting_Account">Evp_Api_Accounting_Account</argument>
                    <argument key="Evp_Api_Accounting_Address">Evp_Api_Accounting_Address</argument>
                    <argument key="Evp_Api_Accounting_Bill">Evp_Api_Accounting_Bill</argument>
                    <argument key="Evp_Api_Accounting_BillItem">Evp_Api_Accounting_BillItem</argument>
                    <argument key="Evp_Api_Accounting_Company">Evp_Api_Accounting_Company</argument>
                    <argument key="Evp_Api_Accounting_Covenantee">Evp_Api_Accounting_Covenantee</argument>
                    <argument key="Evp_Api_Accounting_Money">Evp_Api_Accounting_Money</argument>
                    <argument key="Evp_Api_Accounting_MoneyContainer">Evp_Api_Accounting_MoneyContainer</argument>
                    <argument key="Evp_Api_Accounting_Operation">Evp_Api_Accounting_Operation</argument>
                    <argument key="Evp_Api_Accounting_OperationItem">Evp_Api_Accounting_OperationItem</argument>
                    <argument key="Evp_Api_Accounting_OperationTemplate">Evp_Api_Accounting_OperationTemplate</argument>
                    <argument key="Evp_Api_Accounting_OperationTemplateItem">Evp_Api_Accounting_OperationTemplateItem</argument>
                    <argument key="Evp_Api_Accounting_Payment">Evp_Api_Accounting_Payment</argument>
                </argument>
            </call>
        </service>

        <service id="evp_accounting.accounting_soap_client.client.builder" parent="besimple.soap.client.builder">
            <argument index="0">%evp_accounting.accounting_soap_client.wsdl%</argument>
            <argument index="1" type="collection">
                <argument key="debug">%kernel.debug%</argument>
                <argument key="user_agent">%evp_accounting.accounting_soap_client.user_agent%</argument>
            </argument>
            <argument index="2" type="service" id="evp_accounting.accounting_soap_client.class_map" />
            <call method="withBasicAuthentication">
                <argument>%evp_accounting.accounting_soap_client.username%</argument>
                <argument>%evp_accounting.accounting_soap_client.password%</argument>
            </call>
        </service>

        <service id="evp_accounting.accounting_soap_client.client"
                 class="%besimple.soap.client.builder.class%">
            <factory service="evp_accounting.accounting_soap_client.client.builder" method="build"/>
        </service>

        <service id="evp_accounting.accounting_soap_client" class="%evp_accounting.accounting_soap_client.class%">
            <argument type="service" id="evp_accounting.accounting_soap_client.client" />
        </service>

        <service id="evp_accounting.accounting_operation_builder" class="Evp\Bundle\AccountingBundle\Service\AccountingOperationBuilder" >
            <argument type="service" id="logger" />
        </service>


        <service id="evp_accounting.operation_processor.accounting_client"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor\AccountingClient" >
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_accounting.accounting_soap_client" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider" />
            <argument type="string">44403</argument>
            <argument>W2P_TRANSFER_FUNDS_ON_WAY_IN</argument>
            <argument>W2P_TRANSFER_FUNDS_ON_WAY_OUT</argument>
            <argument>TRANSFER_TO_TRANSIT_ACCOUNT</argument>
            <argument>TRANSFER_FROM_TRANSIT_ACCOUNT</argument>
            <argument>FUNDS_ON_WAY</argument>
        </service>

        <service id="evp_accounting.operation_processor.base" abstract="true"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor">
            <tag name="monolog.logger" channel="evp_accounting.operation_processor" />
            <call method="setAccountingClient">
                <argument type="service" id="evp_accounting.operation_processor.accounting_client"/>
            </call>
            <call method="setBuilder">
                <argument type="service" id="evp_accounting.accounting_operation_builder" />
            </call>
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
            <call method="setEntityManager">
                <argument type="service" id="doctrine.orm.default_entity_manager"/>
            </call>
        </service>

        <service id="evp_accounting.operation_processor.currency_convert"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor\CurrencyConvertOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankAccountBundle\Entity\Operation\CurrencyConvertOperation"/>

            <argument type="service" id="evp_accounting.accounting_group_key_resolver" />
            <argument type="service" id="evp_currency.currency_type_helper" />
            <argument type="service" id="evp_bundle_accounting.service.currency_convert_fiat_template_provider" />
            <argument type="service" id="evp_accounting.service.operation_processor.currency_convert"/>
        </service>

        <service id="evp_accounting.operation_processor_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor\OperationProcessorResolver">
        </service>

        <service id="evp_accounting.partner_operation_processor_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor\OperationProcessorResolver">
        </service>

        <service id="evp_accounting.accounting_processor"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingProcessor">
            <tag name="monolog.logger" channel="evp_accounting.accounting_processor" />

            <argument type="service" id="evp_accounting.operation_processor_resolver" />
            <argument type="service" id="evp_accounting.service.operation_status_manager" />
            <argument type="service" id="evp_accounting.operation_processing_filter" />
            <argument type="service" id="evp_accounting.operation_processor.skip" />
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_accounting.partner_accounting_processor"
                 class="Evp\Bundle\AccountingBundle\Service\PartnerAccountingProcessor">
            <tag name="monolog.logger" channel="evp_accounting.partner_accounting_processor" />

            <argument type="service" id="evp_accounting.partner_operation_processor_resolver" />
            <argument type="service" id="evp_accounting.operation_processing_verifier" />
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_accounting.account_balance_resolver"
                 class="%evp_accounting.account_balance_resolver.class%">
            <tag name="monolog.logger" channel="evp_accounting.account_balance_resolver" />

            <argument type="service" id="evp_rest_statistics_api_client"/>
            <argument type="service" id="evp_bank.key_bank_resolver" />
            <argument type="service" id="evp_operator_widget.zend_cache.file.5m" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_accounting.accounting_group_key_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingGroupKeyResolver">
            <argument type="service" id="evp_vmi_payments.repository.vmi_payment"/>
            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument>%evp_accounting.accounting_group_key_resolver.custom_transfer_number_group_keys%</argument>
        </service>

        <service id="evp_accounting.accounting_group_key_manager"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingGroupKeyManager">
            <argument type="service" id="evp_accounting.accounting_soap_client"/>
            <argument type="service" id="evp_transfer_tax.repository.vpo_tax_provider_info"/>
            <argument type="service" id="evp_accounting.repository.accounting_operation"/>
        </service>

        <service id="evp_accounting.service.accounting_reprocessor"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingReprocessor">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_accounting.repository.accounting_operation" type="service"/>
            <argument id="evp_accounting.client.accounting_client" type="service"/>
            <argument id="evp_accounting.accounting_processor" type="service"/>
            <argument id="paysera_partner.accounting_operation_remover" type="service"/>
            <argument id="paysera_intermediate_statement.service.statement_handler" type="service"/>
        </service>

        <service id="evp_accounting.service.partner_accounting_reprocessor"
                 class="Evp\Bundle\AccountingBundle\Service\PartnerAccountingReprocessor">
            <argument id="evp_accounting.partner_accounting_processor" type="service"/>
            <argument id="paysera_partner.accounting_operation_remover" type="service"/>
        </service>

        <service id="evp_accounting.accounting_api.client_factory"
                 class="Paysera\AccountingClient\ClientFactory">
            <argument type="collection">
                <argument type="string" key="base_url">%accounting_api_base_url%</argument>
                <argument type="collection" key="mac">
                    <argument key="mac_id">%env(ACCOUNTING_API_USERNAME)%</argument>
                    <argument key="mac_secret">%env(ACCOUNTING_API_PASSWORD)%</argument>
                </argument>
            </argument>
        </service>

        <service id="evp_accounting.accounting_api.accounting_api.client" class="Paysera\AccountingClient\AccountingClient">
            <factory service="evp_accounting.accounting_api.client_factory" method="getAccountingClient"/>
        </service>

        <service id="evp_accounting.accounting_api.operation_entity_comparator"
                 class="Paysera\AccountingClient\OperationEntityComparator">
        </service>

        <service id="evp_accounting.service.operation_reference_checker"
                 class="Evp\Bundle\AccountingBundle\Service\OperationReferenceChecker">
            <argument type="service" id="evp_accounting.accounting_api.operation_entity_comparator"/>
            <argument type="service" id="evp_accounting.accounting_api.accounting_api.client"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="paysera_accounting_maintenance.client_factory"
                 class="Paysera\Client\AccountingMaintenanceClient\ClientFactory">
            <factory class="Paysera\Client\AccountingMaintenanceClient\ClientFactory" method="create" />
            <argument type="collection">
                <argument key="base_url">%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/accounting-maintenance/rest/v1/</argument>
                <argument type="collection" key="mac">
                    <argument key="mac_id">%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%</argument>
                    <argument key="mac_secret">%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%</argument>
                </argument>
            </argument>
        </service>

        <service id="paysera_accounting_maintenance.client"
                 class="Paysera\Client\AccountingMaintenanceClient\Client">
            <factory service="paysera_accounting_maintenance.client_factory" method="getClient"/>
        </service>

        <service id="evp_accounting.service.record_provider"
                 class="Evp\Bundle\AccountingBundle\Service\RecordProvider">
            <argument id="paysera_accounting_maintenance.client" type="service"/>
            <argument id="evp_accounting.denormalizer.record_result" type="service"/>
        </service>

        <service id="evp_accounting.service.maintenance.operation_for_reprocess_validator"
                 class="Evp\Bundle\AccountingBundle\Service\Maintenance\OperationForReprocessValidator">
            <argument>%evp_accounting.operation_processor.paysera_macro_accounts%</argument>
        </service>

        <service id="evp_accounting.service.currency_convert_fiat_to_metal_operation_handler"
                 class="Evp\Bundle\AccountingBundle\Service\CurrencyConvertFiatToMetalOperationHandler">
            <argument type="service" id="evp_accounting.service.gain_loss_operation_data_resolver" />
            <argument type="service" id="evp_bundle_client.service.partner_client_manager" />
            <argument type="service" id="evp_accounting.service.currency_convert_precious_metals_template_provider" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bundle_accounting.service.currency_convert_between_fiat_operation_handler"
                 class="Evp\Bundle\AccountingBundle\Service\CurrencyConvertBetweenFiatOperationHandler">
            <argument type="service" id="evp_accounting.service.gain_loss_operation_data_resolver" />
            <argument type="service" id="evp_bundle_client.service.partner_client_manager" />
            <argument type="service" id="evp_bundle_accounting.service.currency_convert_fiat_template_provider" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bundle_accounting.service.currency_convert_fiat_template_provider"
                 class="Evp\Bundle\AccountingBundle\Service\Template\CurrencyConvertFiatTemplateProvider">
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">W2P_CURRENCY_CONVERT_BUY</argument>
            <argument type="string">W2P_CURRENCY_CONVERT_SELL</argument>
            <argument type="string">W2P_CURRENCY_CONVERT_GAIN</argument>
            <argument type="string">W2P_CURRENCY_CONVERT_LOSS</argument>
        </service>

        <service id="evp_bundle_accounting.service.transfer_precious_metals_template_provider"
                 class="Evp\Bundle\AccountingBundle\Service\Template\TransferPreciousMetalsTemplateProvider">
            <argument type="string">PRECIOUS_METALS_TRANSFER_OUT</argument>
            <argument type="string">PRECIOUS_METALS_TRANSFER_IN</argument>
        </service>

        <service id="evp_bundle_accounting.service.transfer_internal_template_key_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\TransferInternalTemplateKeyResolver">
            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
            <argument>%evp_accounting.operation_processor.evp_main_account%</argument>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
            <argument>%evp_accounting.operation_processor.evp_salary_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_sms%</argument>
            <argument>%evp_accounting.operation_processor.paysera_demo_account%</argument>
            <argument>%evp_accounting.operation_processor.ignored_owner_accounts%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_without_invoice_account%</argument>
            <argument>%evp_accounting.operation_processor.n_technologijos_account%</argument>
            <argument type="string">~^Payment for delivery order ~</argument>
            <argument type="string">W2P_TRANSFER_CASH_IN_TRANSFER_OURS</argument>
            <argument type="string">W2P_TRANSFER_CASH_OUT_TRANSFER_OURS</argument>
            <argument type="string">BETWEEN_ACCOUNTS_OUT</argument>
            <argument type="string">GATEWAY_TERMINAL_RECEIVED</argument>
            <argument type="string">W2P_TRANSFER_OUT_EVP</argument>
            <argument type="string">W2P_TRANSFER_OUT_EVP_SMS</argument>
            <argument type="string">W2P_CHECKOUT_TO_OUR_ACCOUNT</argument>
            <argument type="string">BETWEEN_ACCOUNTS_IN</argument>
            <argument type="string">W2P_TRANSFER_IN_EVP</argument>
            <argument type="string">W2P_TRANSFER_IN_EVP_SMS</argument>
            <argument type="string">W2P_TRANSFER_IN_EVP_DELIVERY</argument>
            <argument type="string">W2P_TRANSFER_PARTNER_COMMISSION</argument>
            <argument type="string">PARTNER_TRANSFER_COMMISSION_NO_INVOICE</argument>
            <argument type="string">W2P_TRANSFER_CASHBACK_EVP</argument>
            <argument type="string">W2P_TRANSFER_CASHBACK_EVP_OUR</argument>
            <argument type="string">W2P_TRANSFER_CREDIT_EVP</argument>
        </service>

        <service id="evp_bundle_accounting.service.transfer_received_template_provider"
                 class="Evp\Bundle\AccountingBundle\Service\Template\ReceivedTemplateResolver">
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="collection">
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_ee%</argument>
                    <argument key="template">W2P_RECEIVED_TRANSIT_INBANK_EE</argument>
                </argument>
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_lv%</argument>
                    <argument key="template">W2P_RECEIVED_TRANSIT_INBANK_LV</argument>
                </argument>
            </argument>
            <argument>%evp_accounting.operation_processor.inbank_leasing_template_account_map%</argument>
            <argument type="string">W2P_RECEIVED</argument>
            <argument type="string">W2P_RECEIVED_OUR</argument>
            <argument type="string">GATEWAY_RECEIVED_MICRO</argument>
            <argument type="string">EMA_ADDPAYMENT_%%bank%%_%%currency%%</argument>
        </service>

        <service id="evp_bundle_accounting.service.external_out_bank_template_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\ExternalOutBankTemplateResolver">
            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_precious_metals_template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">W2P_TRANSFER_BANK_OUT</argument>
            <argument type="string">W2P_TRANSFER_BANK_OUT_OUR</argument>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bundle_accounting.service.vmi_template_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\VmiTemplateResolver">
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="string">%evp_accounting.operation_processor.vmi_bank_key%</argument>
            <argument type="string">W2P_TRANSFER_VMI_%%currency%%</argument>
            <argument type="string">W2P_TRANSFER_TO_VMI_%%currency%%</argument>
        </service>

        <service id="evp_bundle_accounting.service.cash_out_precious_metals_operation_handler"
                 class="Evp\Bundle\AccountingBundle\Service\CashOutPreciousMetalsOperationHandler">
            <argument type="service" id="evp_accounting.service.cash_out_precious_metals_template_provider" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_accounting.service.cash_out_precious_metals_template_provider"
                 class="Evp\Bundle\AccountingBundle\Service\Template\CashOutPreciousMetalsTemplateProvider">
            <argument type="string">PRECIOUS_METALS_ASSETS_GIVE</argument>
            <argument type="string">PRECIOUS_METALS_ASSETS_INTERNAL_GIVE_%%currency%%</argument>
            <argument type="string">PRECIOUS_METALS_RETURN_TO_ASSETS_%%currency%%</argument>
            <argument type="string">PRECIOUS_METALS_GIVE_GAIN</argument>
        </service>

        <service id="evp_accounting.service.atm_template_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\AtmTemplateResolver">
            <argument>%evp_accounting.operation_processor.atm.accounts%</argument>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_IN</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_OUT</argument>
            <argument type="string">TRANSFER_ATM%%key%%_COLLECTION_CASH_IN</argument>
            <argument type="string">TRANSFER_ATM%%key%%_COLLECTION_CASH_OUT</argument>
            <argument type="string">W2P_TRANSFER_CASH_IN_TRANSFER</argument>
            <argument type="string">W2P_TRANSFER_CASH_OUT_TRANSFER</argument>
            <argument type="string">TRANSFER_ATM%%key%%_GOT_CASH_%%currency%%</argument>
            <argument type="string">TRANSFER_ATM%%key%%_GAVE_CASH_%%currency%%</argument>
        </service>

        <service id="evp_accounting.service.atm_partner_template_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\AtmPartnerTemplateResolver">
            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument>%evp_accounting.operation_processor.atm.accounts%</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_IN</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_OUT</argument>
            <argument type="string">TRANSFER_ATM%%key%%_COLLECTION_CASH_IN</argument>
            <argument type="string">TRANSFER_ATM%%key%%_COLLECTION_CASH_OUT</argument>
            <argument type="string">W2P_TRANSFER_CASH_IN_TRANSFER</argument>
            <argument type="string">W2P_TRANSFER_CASH_OUT_TRANSFER</argument>
            <argument type="string">TRANSFER_ATM%%key%%_GOT_CASH_%%currency%%</argument>
            <argument type="string">TRANSFER_ATM%%key%%_GAVE_CASH_%%currency%%</argument>
        </service>

        <service id="evp_accounting.service.currency_convert_metal_to_fiat_operation_handler"
                 class="Evp\Bundle\AccountingBundle\Service\CurrencyConvertMetalToFiatOperationHandler">
            <argument type="service" id="evp_accounting.service.gain_loss_operation_data_resolver" />
            <argument type="service" id="evp_bundle_client.service.partner_client_manager" />
            <argument type="service" id="evp_accounting.service.currency_convert_precious_metals_template_provider" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_accounting.service.currency_convert_precious_metals_template_provider"
                 class="Evp\Bundle\AccountingBundle\Service\Template\CurrencyConvertPreciousMetalsTemplateProvider">
            <argument type="string">PRECIOUS_METALS_BUY_FROM_CURRENCY</argument>
            <argument type="string">PRECIOUS_METALS_BUY_TO_METALS</argument>
            <argument type="string">PRECIOUS_METALS_ASSETS_SELL_%%currency%%</argument>
            <argument type="string">PRECIOUS_METALS_SELL_FROM_METALS</argument>
            <argument type="string">PRECIOUS_METALS_SELL_TO_CURRENCY</argument>
            <argument type="string">PRECIOUS_METALS_ASSETS_BUY_%%currency%%</argument>
            <argument type="string">PRECIOUS_METALS_GAIN</argument>
            <argument type="string">PRECIOUS_METALS_LOSS</argument>
        </service>

        <service id="evp_accounting.service.contis_fill_account_template_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\Template\ContisFillAccountTemplateResolver">
            <argument type="string">CONTIS_TOP_UP_TO_INTERMEDIARY</argument>
            <argument type="string">CONTIS_TOP_UP_TO_INTERMEDIARY_2</argument>
            <argument type="collection">
                <argument>lt_lb_sepa_inst</argument>
            </argument>
        </service>

        <service id="evp_accounting.service.gain_loss_operation_data_resolver"
                 class="Evp\Bundle\AccountingBundle\Service\GainLossOperationDataResolver">
            <argument type="service" id="evp_currency.currency_converter.official_by_partner.cached" />
            <argument type="service" id="evp_bundle_client.service.partner_client_manager" />
        </service>

        <service id="evp_accounting.service.operation_status_manager"
                 class="Evp\Bundle\AccountingBundle\Service\OperationStatusManager">
            <tag name="monolog.logger" channel="evp_accounting"/>

            <argument type="service" id="logger"/>
        </service>

        <service id="evp_accounting.service.operation_publisher"
                 class="Evp\Bundle\AccountingBundle\Service\OperationPublisher">
            <tag name="monolog.logger" channel="evp_accounting"/>

            <argument type="service" id="evp_accounting.service.operation_status_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_accounting.operation_processing_filter"
                 class="Evp\Bundle\AccountingBundle\Service\ClearingAccountOperationProcessingFilter">
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
        </service>

        <service id="evp_accounting.operation_processing_verifier"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\OperationProcessingVerifier">
            <argument type="service" id="paysera.client.checkout_client.checkout_client"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transfer_from_operation"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_date_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_provider"/>
            <argument type="collection">
                <!-- Test GE Operations Out -->
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1274958786</argument>
                <argument>1274963848</argument>
                <argument>1275399751</argument>
                <argument>1275432379</argument>
                <argument>1275455171</argument>
                <argument>1277877818</argument>
                <argument>1277877894</argument>
                <argument>1277877909</argument>
                <argument>1278460150</argument>
                <argument>1278460152</argument>
                <argument>1278460550</argument>
                <argument>1278487546</argument>
                <argument>1278487539</argument>
                <argument>1278480039</argument>
                <argument>1278487554</argument>
                <argument>1278493577</argument>
                <argument>1278493576</argument>
                <argument>1284594191</argument>
                <argument>1284744481</argument>
                <argument>1284744511</argument>
                <argument>1284744551</argument>
                <argument>1284744584</argument>
                <argument>1284744592</argument>
                <argument>1284744680</argument>
                <argument>1285868693</argument>
                <argument>1285872844</argument>
                <argument>1285902995</argument>
                <argument>1285878447</argument>
                <argument>1285878448</argument>
                <argument>1286587480</argument>
                <argument>1286587500</argument>
                <argument>1286587573</argument>
                <argument>1274825966</argument>
                <argument>1274826834</argument>
                <argument>1278358019</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1261704228</argument>
                <argument>1261719222</argument>
                <argument>1261718281</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1271149396</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1271571394</argument>
                <argument>1271574238</argument>
                <argument>1271581840</argument>
                <argument>1271577257</argument>
                <argument>1271588102</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1274958786</argument>
                <argument>1274963848</argument>
                <argument>1275399751</argument>
                <argument>1275432379</argument>
                <argument>1275455171</argument>
                <argument>1277877785</argument>
                <argument>1277877818</argument>
                <argument>1277877781</argument>
                <argument>1277877851</argument>
                <argument>1277877873</argument>
                <argument>1277877894</argument>
                <argument>1277877909</argument>
                <argument>1278019619</argument>
                <argument>1278033837</argument>
                <argument>1278079915</argument>
                <argument>1278079956</argument>
                <argument>1278125518</argument>
                <argument>1278460152</argument>
                <argument>1278460550</argument>
                <argument>1278487546</argument>
                <argument>1278487539</argument>
                <argument>1278480039</argument>
                <argument>1278487554</argument>
                <argument>1278493577</argument>
                <argument>1278493576</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1261718280</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>1278460150</argument>
                <argument>1250489240</argument>
                <argument>1255985402</argument>
                <argument>1261809485</argument>
                <argument>1265067279</argument>
                <argument>1268383720</argument>
                <argument>1269773021</argument>
                <argument>1278916400</argument>
                <argument>1280739937</argument>
                <argument>1303949797</argument>
                <argument>1307474473</argument>
                <argument>1307474959</argument>
                <argument>1310780015</argument>
                <argument>1359252391</argument>
                <argument>1359253062</argument>
                <argument>1359253710</argument>
                <argument>1359254289</argument>
                <argument>1359254713</argument>
                <argument>1360503491</argument>
                <argument>1363599402</argument>
                <argument>1363600117</argument>
                <argument>1364057501</argument>
                <argument>1364922767</argument>
                <argument>1364926250</argument>
                <argument>1364926601</argument>
                <argument>1364939376</argument>
                <argument>1364940026</argument>
                <argument>1364940765</argument>
                <argument>1364941804</argument>
                <argument>1364942849</argument>
                <argument>1364943649</argument>
                <argument>1367201825</argument>
                <argument>1370043732</argument>
                <argument>1370043768</argument>
                <argument>1370060908</argument>
                <argument>1370061784</argument>
                <argument>1370062052</argument>
                <argument>1370476386</argument>
                <argument>1370476401</argument>
                <argument>1370635616</argument>
                <argument>1370899003</argument>
                <argument>1370899014</argument>
                <argument>1371206254</argument>
                <argument>1371206271</argument>
                <argument>1371503416</argument>
                <argument>1371700091</argument>
                <argument>1371946737</argument>
                <argument>1371946751</argument>
                <argument>1372803273</argument>
                <argument>1372803287</argument>
                <argument>1372803431</argument>
                <argument>1372803442</argument>
                <argument>1372827303</argument>
                <argument>1373229927</argument>
                <argument>1373229944</argument>
                <argument>1373230090</argument>
                <argument>1373230120</argument>
                <argument>1373230182</argument>
                <argument>1373230192</argument>
                <argument>1373230206</argument>
                <argument>1373428548</argument>
                <argument>1373696029</argument>
                <argument>1374045249</argument>
                <argument>1374364482</argument>
                <argument>1374416097</argument>
                <argument>1374416155</argument>
                <argument>1374418501</argument>
                <argument>1374422767</argument>
                <argument>1374867729</argument>
                <argument>1374869698</argument>
                <argument>1375313137</argument>
                <argument>1375764325</argument>
                <argument>1375986092</argument>
                <argument>1297613042</argument>
                <argument>1281129640</argument>
                <!-- Test GE Operations In -->
                <argument>1271310407</argument>
                <argument>1271268722</argument>
                <argument>1271557193</argument>
                <argument>1271562222</argument>
                <argument>1271569156</argument>
                <argument>1271572843</argument>
                <argument>1271720783</argument>
                <argument>1271747445</argument>
                <argument>1271747418</argument>
                <argument>1271755461</argument>
                <argument>1274958797</argument>
                <argument>1274958792</argument>
                <argument>1274958791</argument>
                <argument>1274958822</argument>
                <argument>1278130934</argument>
                <argument>1278498981</argument>
                <argument>1278499035</argument>
                <argument>1278504331</argument>
                <argument>1278504314</argument>
                <argument>1284594409</argument>
                <argument>1284594333</argument>
                <argument>1284594362</argument>
                <argument>1284594403</argument>
                <argument>1284594412</argument>
                <argument>1306248751</argument>
                <argument>1285903038</argument>
                <argument>1285903065</argument>
                <argument>1285903064</argument>
                <argument>1285905081</argument>
                <argument>1286589510</argument>
                <argument>1286589511</argument>
                <argument>1286589512</argument>
                <argument>1286589539</argument>
                <argument>1274825967</argument>
                <argument>1274826835</argument>
                <argument>1278358020</argument>
                <argument>1250489241</argument>
                <argument>1255584402</argument>
                <argument>1255985403</argument>
                <argument>1261809486</argument>
                <argument>1262992372</argument>
                <argument>1262992731</argument>
                <argument>1265067280</argument>
                <!-- Test GE Operations Refund Out -->
                <argument>1262880556</argument>
                <argument>1262881429</argument>
                <argument>1262882971</argument>
                <argument>1271550552</argument>
                <argument>1274168603</argument>
                <argument>1274189375</argument>
                <argument>1274189542</argument>
                <argument>1274189543</argument>
                <argument>1274189780</argument>
                <argument>1278082823</argument>
                <argument>1278083564</argument>
                <argument>1278083565</argument>
                <argument>1278084370</argument>
                <argument>1278084371</argument>
                <argument>1278085425</argument>
                <argument>1278086100</argument>
                <argument>1278086103</argument>
                <argument>1278386827</argument>
                <argument>1295619756</argument>
                <argument>1295676670</argument>
                <argument>1295676998</argument>
                <argument>1295677000</argument>
                <argument>1295677411</argument>
                <argument>1295677412</argument>
                <argument>1295677413</argument>
                <argument>1295677735</argument>
                <argument>1295678055</argument>
                <argument>1295678056</argument>
                <argument>1295678057</argument>
                <argument>1295678058</argument>
                <argument>1295678059</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <!-- Test GE Operations Partner Change -->
                <argument>**********</argument>
                <argument>**********</argument>
                <argument>**********</argument>
                <!-- Test GE Operations Charge -->
                <argument>**********</argument>
            </argument>
            <argument>%evp_accounting.operation_processor.paysera_macro_accounts%</argument>
            <argument type="collection">
                <argument>%evp_contis.contis_beneficiary_iban_gb%</argument>
                <argument>%evp_contis.contis_beneficiary_iban_lt%</argument>
            </argument>
            <argument type="service">
                <service class="DateTimeImmutable">
                    <argument type="string">2021-12-31 00:00:00</argument>
                </service>
            </argument>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_metals"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferMetalsRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="1"/>

            <argument type="service" id="evp_currency.currency_type_helper"/>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_owner_sender_beneficiary"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOwnerSenderBeneficiaryRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="2"/>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_in_owner_sender"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferInOwnerSenderRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="3"/>

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
            <argument>%evp_accounting.operation_processor.evp_salary_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_sms%</argument>
            <argument>%evp_accounting.operation_processor.ignored_owner_accounts%</argument>
            <argument>%evp_accounting.operation_processor.evp_main_account%</argument>
            <argument>%evp_accounting.operation_processor.n_technologijos_account%</argument>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_in_owner_beneficiary"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferInOwnerBeneficiaryRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="4"/>

            <argument type="service" id="logger"/>
            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument>%evp_accounting.operation_processor.paysera_demo_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_charge_account%</argument>
            <argument>%evp_accounting.operation_processor.ignored_owner_accounts%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_without_invoice_account%</argument>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_out_owner_beneficiary"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOutOwnerBeneficiaryRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="5"/>

            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument>%evp_accounting.operation_processor.paysera_demo_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_charge_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_sms%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_without_invoice_account%</argument>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_out_owner_sender"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOutOwnerSenderRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="6"/>

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="paysera_partner.partner_account_provider"/>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
            <argument>%evp_accounting.operation_processor.evp_salary_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_sms%</argument>
            <argument>%evp_accounting.operation_processor.ignored_owner_accounts%</argument>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_account%</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_without_invoice_account%</argument>
        </service>

        <service id="evp_accounting.service.verifier_rule.internal_transfer_owner_sender_out_beneficiary"
                 class="Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOwnerSenderBeneficiaryInbankOutRule">
            <tag name="evp_bank_transfer.operation_processing_verifier_rule" priority="7"/>
            <argument type="collection">
                <argument type="string">%evp_accounting.operation_processor.partner_account.mokilizingas_ee%</argument>
                <argument type="string">%evp_accounting.operation_processor.partner_account.mokilizingas_lv%</argument>
            </argument>
            <argument type="string">%evp_accounting.operation_processor.partner_charge_account%</argument>
        </service>

        <service id="evp_accounting.service.remote_operation_request_handler"
                 class="Evp\Bundle\AccountingBundle\Service\RemoteOperationRequestHandler">
            <argument type="service" id="paysera_partner.service.operation_create_transformer"/>
            <argument type="service" id="paysera_partner.service.operation_entity_comparator"/>
            <argument type="service" id="evp_accounting.accounting_api.accounting_api.client"/>
            <argument type="service" id="paysera_partner.accounting_operation_manager.client"/>
            <argument type="service" id="logger"/>
            <argument>2</argument>
        </service>

        <service id="evp_accounting.service.remote_operation_publish_order_optimizer"
                 class="Evp\Bundle\AccountingBundle\Service\RemoteOperationRequestPublishOrderOptimizer">
        </service>

        <service id="evp_accounting.service.accounting_exception_handler"
                 class="Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler">
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
