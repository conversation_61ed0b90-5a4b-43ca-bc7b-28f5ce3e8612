<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests;

use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Component\Money\Money;
use Evp\Tests\BaseTestCase;
use Paysera\AccountingClient\AccountingClient;
use Paysera\AccountingClient\Entity\Operation;
use Paysera\AccountingClient\Entity\OperationFull;
use Paysera\AccountingOperationManagerClient\Entity\OperationCollection;
use Paysera\AccountingOperationManagerClient\AccountingOperationManagerClient;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Paysera\Bundle\PartnerBundle\Service\OperationCreateTransformer;
use Paysera\Bundle\PartnerBundle\Service\OperationEntityComparator;
use Evp\Bundle\AccountingBundle\Service\RemoteOperationRequestHandler;
use Paysera\Component\RestClientCommon\Exception\RequestException;
use PHPUnit\Framework\MockObject\MockObject;
use Paysera\AccountingOperationManagerClient\Entity\Operation as AccountingOperation;
use DateTimeImmutable;
use Psr\Log\LoggerInterface;

class RemoteOperationRequestHandlerTest extends BaseTestCase
{
    private RemoteOperationRequestHandler $requestHandler;

    /**
     * @var AccountingClient|MockObject
     */
    private $accountingClient;

    /**
     * @var AccountingOperationManagerClient|MockObject
     */
    private $accountingOperationManagerClient;

    protected function setUp(): void
    {
        $this->accountingClient = $this->createMock(AccountingClient::class);
        $this->accountingOperationManagerClient = $this->createMock(AccountingOperationManagerClient::class);
        $this->requestHandler = new RemoteOperationRequestHandler(
            new OperationCreateTransformer(
                $this->getContainer()->get('paysera_partner.service.reference_generator')
            ),
            new OperationEntityComparator(),
            $this->accountingClient,
            $this->accountingOperationManagerClient,
            $this->createMock(LoggerInterface::class),
            2
        );
    }

    /**
     * @dataProvider remoteOperationRequestDataProvider
     *
     * @param RemoteOperationRequest $operationRequest
     * @param OperationCollection $operationCollection
     */
    public function testOperationCreation(
        RemoteOperationRequest $operationRequest,
        OperationCollection $operationCollection
    ): void {
        $this->accountingOperationManagerClient
            ->expects($this->once())
            ->method('create')
            ->willReturn($operationCollection)
        ;

        $this->assertSame(RemoteOperationRequest::STATUS_NEW, $operationRequest->getStatus());

        $this->requestHandler->createRemoteOperation($operationRequest);

        $this->assertSame('randomIdHash', $operationRequest->getAccountingId());
    }

    public function testOperationCreationHandlesExistingReferenceException(): void
    {
        $requestExceptionMock = $this->createMock(RequestException::class);
        $requestExceptionMock
            ->method('getErrorDescription')
            ->willReturn('Reference already exists')
        ;

        $request = $this->createRemoteOperationRequest();

        $operationByReference = new Operation(['id' => 'OPAA-1234']);
        $this->accountingClient
            ->expects($this->once())
            ->method('getOperationByReference')
            ->with('test_partner_code', 'evpbank:remote_operation_request:1')
            ->willReturn($operationByReference)
        ;

        $operationFull = new OperationFull([
            'id' => 'OPAA-1234',
            'amount_money' => [
                'amount' => $request->getAmountMoney()->getAmount(),
                'currency' => $request->getAmountMoney()->getCurrency()
            ],
            'details' => $request->getDetails(),
            'template' => ['name' => $request->getTemplateName()],
            'group' => ['type' => $request->getType(), 'reference' => $request->getReference()],
            'covenantee_id' => $request->getCovenanteeId(),
            'date' => $request->getOperationDate()->getTimestamp(),
        ]);
        $this->accountingClient
            ->expects($this->once())
            ->method('getOperationFull')
            ->with('test_partner_code', 'OPAA-1234')
            ->willReturn($operationFull)
        ;

        $this->accountingOperationManagerClient
            ->expects($this->once())
            ->method('create')
            ->willThrowException($requestExceptionMock)
        ;

        $this->requestHandler->createRemoteOperation($request);

        $this->assertEquals('OPAA-1234', $request->getAccountingId());
    }

    public function testOperationCreationPassesExistingReferenceException(): void
    {
        $requestExceptionMock = $this->createMock(RequestException::class);
        $requestExceptionMock
            ->method('getErrorDescription')
            ->willReturn('Reference already exists')
        ;

        $request = $this->createRemoteOperationRequest();

        $operationByReference = new Operation(['id' => 'OPAA-1234']);
        $this->accountingClient
            ->expects($this->once())
            ->method('getOperationByReference')
            ->with('test_partner_code', 'evpbank:remote_operation_request:1')
            ->willReturn($operationByReference)
        ;

        $operationFull = new OperationFull([
            'id' => 'OPAA-1234',
            'amount_money' => [
                'amount' => $request->getAmountMoney()->getAmount(),
                'currency' => $request->getAmountMoney()->getCurrency()
            ],
            'details' => 'Some other details to make operation different',
            'template' => ['name' => $request->getTemplateName()],
            'group' => ['type' => $request->getType(), 'reference' => $request->getReference()],
            'covenantee_id' => $request->getCovenanteeId(),
            'date' => $request->getOperationDate()->getTimestamp(),
        ]);
        $this->accountingClient
            ->expects($this->once())
            ->method('getOperationFull')
            ->with('test_partner_code', 'OPAA-1234')
            ->willReturn($operationFull)
        ;

        $this->accountingOperationManagerClient
            ->expects($this->once())
            ->method('create')
            ->willThrowException($requestExceptionMock)
        ;

        $this->expectExceptionObject($requestExceptionMock);

        $this->requestHandler->createRemoteOperation($request);
    }

    public function testOperationCreationRetryMechanism(): void
    {
        $requestExceptionMock = $this->createMock(RequestException::class);
        $requestExceptionMock
            ->method('getErrorDescription')
            ->willReturn('Failed to acquire lock, wait time expired')
        ;

        $this->accountingOperationManagerClient
            ->expects($this->exactly(2))
            ->method('create')
            ->willThrowException($requestExceptionMock)
        ;

        $this->expectExceptionObject($requestExceptionMock);

        $this->requestHandler->createRemoteOperation($this->createRemoteOperationRequest());
    }

    public function remoteOperationRequestDataProvider(): array
    {
        return [
            [
                $this->createRemoteOperationRequest(),
                (new OperationCollection())->setItems([$this->createAccountingOperation()])
            ],
        ];
    }

    private function createRemoteOperationRequest(): RemoteOperationRequest
    {
        $transferOutOperation = new TransferOutOperation();

        $request = (new RemoteOperationRequest())
            ->setOperation($transferOutOperation)
            ->setOperationDate(DateTimeImmutable::createFromMutable($transferOutOperation->getCreatedAt()))
            ->setDetails('testDetails')
            ->setReference('testReference')
            ->setTemplateName('testTemplateName')
            ->setCovenanteeId(111)
            ->setType('testType')
            ->setPartnerCode('test_partner_code')
            ->setAmountMoney(new Money(10, 'EUR'))
        ;

        $this->forcePropertyChange($request, 'id', 1);

        return $request;
    }

    private function createAccountingOperation(): AccountingOperation
    {
        return (new AccountingOperation())
            ->setId('randomIdHash')
            ->setDetails('testDetails')
            ->setTemplateId('testTemplateId')
            ->setCovenanteeId(111)
            ->setAmountMoney(new Money(10, 'EUR'))
            ->setCreatedAt(new DateTimeImmutable())
        ;
    }
}
