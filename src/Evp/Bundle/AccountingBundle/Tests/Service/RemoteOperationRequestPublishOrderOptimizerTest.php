<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service;

use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Evp\Bundle\AccountingBundle\Service\RemoteOperationRequestPublishOrderOptimizer;
use PHPUnit\Framework\TestCase;

class RemoteOperationRequestPublishOrderOptimizerTest extends TestCase
{
    public function testOptimizeOrdersByTypeAndReference(): void
    {
        $optimizer = new RemoteOperationRequestPublishOrderOptimizer();

        $remoteOperationRequests = [
            ['type' => 't', 'reference' => '1'],
            ['type' => 't', 'reference' => '1'],
            ['type' => 't', 'reference' => '2'],
            ['type' => 't', 'reference' => '2'],
            ['type' => 'p', 'reference' => '1'],
            ['type' => 'p', 'reference' => '3'],
            ['type' => 'p', 'reference' => '3'],
            ['type' => 't', 'reference' => '1'],
            ['type' => 't', 'reference' => '1'],
        ];

        $optimizedRequests = $optimizer->optimize($remoteOperationRequests);

        $this->assertCount(count($remoteOperationRequests), $optimizedRequests);
        $this->assertSame($remoteOperationRequests[0], $optimizedRequests[0]);
        $this->assertSame($remoteOperationRequests[2], $optimizedRequests[1]);
        $this->assertSame($remoteOperationRequests[4], $optimizedRequests[2]);
        $this->assertSame($remoteOperationRequests[5], $optimizedRequests[3]);
        $this->assertSame($remoteOperationRequests[1], $optimizedRequests[4]);
        $this->assertSame($remoteOperationRequests[3], $optimizedRequests[5]);
        $this->assertSame($remoteOperationRequests[6], $optimizedRequests[6]);
        $this->assertSame($remoteOperationRequests[7], $optimizedRequests[7]);
        $this->assertSame($remoteOperationRequests[8], $optimizedRequests[8]);
    }
}
