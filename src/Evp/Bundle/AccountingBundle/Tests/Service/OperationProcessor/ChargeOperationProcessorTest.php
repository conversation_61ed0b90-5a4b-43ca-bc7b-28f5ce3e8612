<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor;

use DateTime;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\ChargeOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankChargeBundle\Entity\Charge;
use Evp\Bundle\BankChargeBundle\Entity\ChargeOperation;
use Evp\Bundle\BankTransferBundle\Repository\TransferInRepository;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Component\Money\Money;
use Evp\Tests\BaseTestCase;
use Paysera\Bundle\PartnerBundle\Entity\Partner;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;
use Paysera\Bundle\PartnerBundle\Service\PartnerCovenanteeIdProvider;
use PHPUnit\Framework\MockObject\MockObject;

class ChargeOperationProcessorTest extends BaseTestCase
{
    private const CONTIS_PARTNER = 'paysera_lt';
    private const CLIENT_COVENANTEE_ID = 1;
    private const PAYSERA_AL_COVENANTEE_ID = ********;

    /**
     * @var EntityManager|MockObject
     */
    private $entityManager;

    /**
     * @var PartnerCovenanteeIdProvider|MockObject
     */
    private $partnerCovenanteeIdProvider;

    /**
     * @var PartnerClientManager|MockObject
     */
    private $partnerClientManager;

    /**
     * @var AccountTypeAtDateProvider|MockObject
     */
    private $accountTypeAtDateProvider;

    protected ChargeOperationProcessor $chargeOperationProcessor;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->partnerCovenanteeIdProvider = $this->createMock(PartnerCovenanteeIdProvider::class);
        $this->partnerClientManager = $this->createMock(PartnerClientManager::class);
        $this->accountTypeAtDateProvider = $this->createMock(AccountTypeAtDateProvider::class);

        $this->chargeOperationProcessor = new ChargeOperationProcessor(
            $this->getContainer()->get('evp_bank_transfer.service.template_provider'),
            $this->partnerClientManager,
            $this->createMock(AccountingExceptionHandler::class),
            $this->accountTypeAtDateProvider,
            $this->partnerCovenanteeIdProvider,
            $this->createMock(TransferInRepository::class),
            self::CONTIS_PARTNER
        );
        $this->chargeOperationProcessor->setEntityManager($this->entityManager);
    }

    /**
     * @dataProvider handleLtDataProvider
     */
    public function testCreateOperationsWithLtClient(
        ChargeOperation $operation,
        string $accountType,
        array $expectedData
    ): void {
        $this->partnerClientManager
            ->expects($this->once())
            ->method('getPartnerCodeByAccount')
            ->with($operation->getAccount(), $operation->getCreatedAt())
            ->willReturn(Partner::PAYSERA_LITHUANIA)
        ;

        $this->partnerCovenanteeIdProvider
            ->expects($this->never())
            ->method('getCovenanteeIdByPartnerCode')
        ;

        $this->accountTypeAtDateProvider
            ->expects($this->once())
            ->method('getAccountTypeAtDate')
            ->with($operation->getAccount(), $operation->getCreatedAt())
            ->willReturn($accountType)
        ;

        $this->assertOperationRequestPersisted($expectedData);

        $this->chargeOperationProcessor->process($operation);
    }

    public function handleLtDataProvider(): array
    {
        $amount = new Money(10, 'EUR');
        $operation = $this->createOperation($amount);
        $cardAccountOperationRequests = [
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(Partner::PAYSERA_LITHUANIA)
                ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_COMMISS_CARD'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(Partner::PAYSERA_LITHUANIA)
                ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_FUNDS_ON_WAY_OUT')
        ];

        return [
            'local account' => [
                $operation,
                'local',
                [
                    (new RemoteOperationRequest())
                        ->setOperation($operation)
                        ->setType(RemoteOperationRequest::TYPE_CHARGE)
                        ->setReference('1')
                        ->setAmountMoney($amount)
                        ->setPartnerCode(Partner::PAYSERA_LITHUANIA)
                        ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                        ->setDetails('details')
                        ->setTemplateName('W2P_TRANSFER_COMMISS_CARD')
                ]
            ],
            'card account - type contis' => [
                $operation,
                Account::TYPE_CONTIS,
                $cardAccountOperationRequests,
            ],
            'card account - type card' => [
                $operation,
                Account::TYPE_CARD_V2,
                $cardAccountOperationRequests,
            ],
        ];
    }

    /**
     * @dataProvider handleAlDataProvider
     */
    public function testCreateOperationsWithAlClient(
        ChargeOperation $operation,
        string $accountType,
        array $expectedData
    ): void {
        $this->partnerClientManager
            ->expects($this->once())
            ->method('getPartnerCodeByAccount')
            ->with($operation->getAccount(), $operation->getCreatedAt())
            ->willReturn(Partner::PAYSERA_ALBANIA)
        ;

        $this->partnerCovenanteeIdProvider
            ->expects($this->any())
            ->method('getCovenanteeIdByPartnerCode')
            ->willReturn((string)self::PAYSERA_AL_COVENANTEE_ID)
        ;

        $this->accountTypeAtDateProvider
            ->expects($this->once())
            ->method('getAccountTypeAtDate')
            ->with($operation->getAccount(), $operation->getCreatedAt())
            ->willReturn($accountType)
        ;

       $this->assertOperationRequestPersisted($expectedData);

        $this->chargeOperationProcessor->process($operation);
    }

    public function handleAlDataProvider(): array
    {
        $amount = new Money(10, 'EUR');
        $operation = $this->createOperation($amount);

        $cardAccountOperationRequests = [
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_COMMISS_CARD'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(self::CONTIS_PARTNER)
                ->setCovenanteeId(self::PAYSERA_AL_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_FUNDS_ON_WAY_OUT'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(self::CONTIS_PARTNER)
                ->setCovenanteeId(self::PAYSERA_AL_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_OUT'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(self::CONTIS_PARTNER)
                ->setCovenanteeId(self::PAYSERA_AL_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_TRANSFER_IN'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('RECEIVED_PAYSERA_LT_EUR'),
            (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setType(RemoteOperationRequest::TYPE_CHARGE)
                ->setReference('1')
                ->setAmountMoney($amount)
                ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                ->setDetails('details')
                ->setTemplateName('W2P_RECEIVED')
        ];

        return [
            'local account' => [
                $operation,
                'local',
                [
                    (new RemoteOperationRequest())
                        ->setOperation($operation)
                        ->setType(RemoteOperationRequest::TYPE_CHARGE)
                        ->setReference('1')
                        ->setAmountMoney($amount)
                        ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                        ->setCovenanteeId(self::CLIENT_COVENANTEE_ID)
                        ->setDetails('details')
                        ->setTemplateName('W2P_TRANSFER_COMMISS_CARD'),
                ]
            ],
            'card account - type contis' => [
                $operation,
                Account::TYPE_CONTIS,
                $cardAccountOperationRequests,
            ],
            'card account - type card' => [
                $operation,
                Account::TYPE_CONTIS,
                $cardAccountOperationRequests,
            ],
        ];
    }

    private function createOperation(Money $amount): ChargeOperation
    {
        $client = (new ClientNatural())->setCovenanteeId(self::CLIENT_COVENANTEE_ID);
        $account = (new Account())->setClient($client);
        $date = new DateTime();
        $charge = $this->createMock(Charge::class);
        $charge->method('getAccount')->willReturn($account);
        $charge->method('getDetails')->willReturn('details');
        $charge->method('getPurpose')->willReturn(Charge::PURPOSE_CARD_MONTHLY_FEE);
        $charge->method('getId')->willReturn(1);

        return (new ChargeOperation($charge, $account, $amount))->setCreatedAt($date);
    }

    private function assertOperationRequestPersisted(array $expectedData): void
    {
        $i = 0;
        $this->entityManager
            ->expects($this->exactly(count($expectedData)))
            ->method('persist')
            ->with($this->callback(function($actualEntity) use ($expectedData, &$i) {
                $this->assertInstanceOf(RemoteOperationRequest::class, $actualEntity);
                $this->assertSame($actualEntity->getType(), $expectedData[$i]->getType());
                $this->assertSame($actualEntity->getReference(), $expectedData[$i]->getReference());
                $this->assertSame(
                    $actualEntity->getAmountMoney()->getAmount(),
                    $expectedData[$i]->getAmountMoney()->getAmount()
                );
                $this->assertSame(
                    $actualEntity->getAmountMoney()->getCurrency(),
                    $expectedData[$i]->getAmountMoney()->getCurrency()
                );

                $this->assertSame($actualEntity->getDetails(), $expectedData[$i]->getDetails());
                $this->assertSame($actualEntity->getCovenanteeId(), $expectedData[$i]->getCovenanteeId());
                $this->assertSame($actualEntity->getPartnerCode(), $expectedData[$i]->getPartnerCode());
                $this->assertSame($actualEntity->getTemplateName(), $expectedData[$i]->getTemplateName());
                $i++;

                return true;
            }))
        ;
    }
}
