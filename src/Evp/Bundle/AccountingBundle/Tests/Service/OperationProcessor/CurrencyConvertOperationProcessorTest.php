<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor;

use DateTime;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\CurrencyConvertOperationProcessor;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertFiatToMetalOperationHandler;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertMetalToFiatOperationHandler;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\CurrencyConvertOperation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisAccountingBundle\Service\AccountingProcessor as ContisAccountingProcessor;
use Evp\Bundle\CurrencyBundle\Service\CurrencyTypeHelper;
use Evp\Component\Money\Money;
use Evp\Tests\BaseTestCase;
use InvalidArgumentException;
use PHPUnit_Framework_MockObject_MockObject as MockObject;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertBetweenFiatOperationHandler;

class CurrencyConvertOperationProcessorTest extends BaseTestCase
{
    private CurrencyConvertOperationProcessor $currencyConvertOperationProcessor;

    /**
     * @var PartnerClientManager|MockObject
     */
    private $partnerClientManager;

    /**
     * @var CurrencyConvertBetweenFiatOperationHandler|MockObject
     */
    private $currencyConvertBetweenFiat;

    /**
     * @var CurrencyConvertMetalToFiatOperationHandler|MockObject
     */
    private $currencyConvertMetalToFiat;

    /**
     * @var CurrencyConvertFiatToMetalOperationHandler|MockObject
     */
    private $currencyConvertFiatToMetal;

    public function setUp(): void
    {
        $this->currencyConvertBetweenFiat = $this->createMock(CurrencyConvertBetweenFiatOperationHandler::class);
        $this->currencyConvertMetalToFiat = $this->createMock(CurrencyConvertMetalToFiatOperationHandler::class);
        $this->currencyConvertFiatToMetal = $this->createMock(CurrencyConvertFiatToMetalOperationHandler::class);
        $this->partnerClientManager = $this->createMock(PartnerClientManager::class);
        $currencyTypeHelper = $this->createMock(CurrencyTypeHelper::class);
        $currencyTypeHelper->expects($this->any())->method('isPreciousMetal')->willReturnCallback(
            function ($currency) {
                return $currency === 'XAU' || $currency === 'XAG';
            }
        );

        $this->currencyConvertOperationProcessor = new CurrencyConvertOperationProcessor(
            $this->getContainer()->get('evp_accounting.service.accounting_exception_handler'),
            $currencyTypeHelper,
            $this->createMock(AccountOwnerResolver::class),
            $this->currencyConvertBetweenFiat,
            $this->currencyConvertMetalToFiat,
            $this->currencyConvertFiatToMetal,
            $this->partnerClientManager,
            $this->createMock(ContisAccountingProcessor::class),
            $this->createMock(TemplateProvider::class),
            ['paysera_al', 'paysera_xk']
        );
    }

    public function testConversionBetweenFiat(): void
    {
        $account = $this->getAccount();
        $operation = (new CurrencyConvertOperation())
            ->setCreatedAt(new DateTime())
            ->setSrcAmountMoney(new Money(2, 'EUR'))
            ->setDstAmountMoney(new Money(3, 'USD'))
            ->setAccount($account)
        ;

        $this->currencyConvertBetweenFiat->expects($this->exactly(1))->method('handle');

        $this->currencyConvertOperationProcessor->process($operation);
    }

    public function testFiatToMetal(): void
    {
        $account = $this->getAccount();
        $operation = (new CurrencyConvertOperation())
            ->setCreatedAt(new DateTime())
            ->setSrcAmountMoney(new Money(1500, 'USD'))
            ->setDstAmountMoney(new Money(1, 'XAU'))
            ->setAccount($account)
        ;

        $this->currencyConvertFiatToMetal->expects($this->exactly(1))->method('handle');

        $this->currencyConvertOperationProcessor->process($operation);
    }

    public function testMetalToFiatPayseraLt(): void
    {
        $account = $this->getAccount();
        $operation = (new CurrencyConvertOperation())
            ->setCreatedAt(new DateTime())
            ->setSrcAmountMoney(new Money(1, 'XAU'))
            ->setDstAmountMoney(new Money(1500, 'USD'))
            ->setAccount($account)
        ;

        $this->partnerClientManager->expects($this->any())
            ->method('getPartnerCodeByAccount')
            ->with($operation->getAccount(),
                $operation->getCreatedAt()
            )
            ->willReturn('paysera_lt');

        $this->currencyConvertMetalToFiat->expects($this->exactly(1))->method('handle');

        $this->currencyConvertOperationProcessor->process($operation);
    }

    public function testFiatToMetalPayseraAl(): void
    {
        $account = $this->getAccount();
        $operation = (new CurrencyConvertOperation())
            ->setCreatedAt(new DateTime())
            ->setSrcAmountMoney(new Money(1500, 'USD'))
            ->setDstAmountMoney(new Money(1, 'XAU'))
            ->setAccount($account)
        ;

        $this->partnerClientManager->expects($this->any())
            ->method('getPartnerCodeByAccount')
            ->with($operation->getAccount(),
                $operation->getCreatedAt()
            )
            ->willReturn('paysera_al');

        $this->currencyConvertBetweenFiat->expects($this->exactly(1))->method('handle');
        $this->currencyConvertMetalToFiat->expects($this->exactly(0))->method('handle');

        $this->currencyConvertOperationProcessor->process($operation);
    }

    /**
     * @covers CurrencyConvertOperationProcessor::process
     */
    public function testInvalidOperation(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->currencyConvertOperationProcessor->process(new TransferOutOperation());
    }

    /**
     * @return ClientNatural|MockObject
     */
    private function getClientNaturalMock()
    {
        /** @var ClientNatural|MockObject $client */
        $client = $this->createMock(ClientNatural::class);
        $client->expects($this->any())
            ->method('getCovenanteeId')
            ->will($this->returnValue(1))
        ;

        return $client;
    }

    private function getAccount(): Account
    {
        return (new Account())
            ->setClient($this->getClientNaturalMock())
        ;
    }
}
