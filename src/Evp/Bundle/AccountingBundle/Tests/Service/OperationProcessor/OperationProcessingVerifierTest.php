<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor;

use DateTime;
use DateTimeImmutable;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferFromOperationProvider;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisBundle\Entity\ContisOperation;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\OperationProcessingVerifier;
use Paysera\Bundle\PartnerBundle\Service\PartnerCovenanteeIdProvider;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationDateService;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\OperationProcessingVerifierRule;
use Paysera\Client\CheckoutClient\CheckoutClient;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class OperationProcessingVerifierTest extends TestCase
{
    private OperationProcessingVerifier $operationProcessingVerifier;

    /**
     * @var AccountOwnerResolver|MockObject
     */
    private AccountOwnerResolver $accountOwnerResolver;

    /**
     * @var PartnerCovenanteeIdProvider|MockObject
     */
    private PartnerCovenanteeIdProvider $partnerCovenanteeIdProvider;

    /**
     * @var OperationDateService|MockObject
     */
    private OperationDateService $operationDateService;

    private const SKIP_ACCOUNT_NUMBER = 'SKIP_ACCOUNT';
    private const FILL_ACCOUNT_IBAN = 'FILL_ACCOUNT_IBAN';

    public function setUp(): void
    {
        $this->accountOwnerResolver = $this->createMock(AccountOwnerResolver::class);
        $this->partnerCovenanteeIdProvider = $this->createMock(PartnerCovenanteeIdProvider::class);
        $partnerClientManager = $this->createMock(PartnerClientManager::class);
        $checkoutClient = $this->createMock(CheckoutClient::class);
        $transferFromOperationProvider = new TransferFromOperationProvider();
        $this->operationDateService = $this->createMock(OperationDateService::class);
        $operationProvider = $this->createMock(OperationProvider::class);

        $this->operationProcessingVerifier = new OperationProcessingVerifier(
            $checkoutClient,
            $this->accountOwnerResolver,
            $this->partnerCovenanteeIdProvider,
            $partnerClientManager,
            $transferFromOperationProvider,
            $this->operationDateService,
            $operationProvider,
            ['1', '3', '5',],
            [self::SKIP_ACCOUNT_NUMBER],
            [self::FILL_ACCOUNT_IBAN],
            new DateTimeImmutable('2021-12-31')
        );
        $this->operationProcessingVerifier->addRule($this->createMock(OperationProcessingVerifierRule::class));
    }

    /**
     * @dataProvider operationsDataProvider
     */
    public function testShouldProcess(Operation $operation, DateTimeImmutable $operationDate, bool $expectedResult): void
    {
        $this->operationDateService
            ->method('getTransferOutOperationDatetime')
            ->willReturn($operationDate)
        ;

        $this->assertEquals($expectedResult, $this->operationProcessingVerifier->shouldProcess($operation));
    }

    public function operationsDataProvider(): iterable
    {
        $validOperationDate = new DateTimeImmutable('2021-12-31');
        $contisOperation = $this->createMock(ContisOperation::class);
        $contisOperation->method('getId')->willReturn(9);
        yield 'Contis Operation' => [
            'operation' => $contisOperation,
            'operationDate' => $validOperationDate,
            'expectedResult' => false,
        ];

        $operation1 = $this->createMock(Operation::class);
        $operation1->method('getId')->willReturn(1);
        yield 'Operation #1 included in list' => [
            'operation' => $operation1,
            'operationDate' => $validOperationDate,
            'expectedResult' => false,
        ];

        $operation2 = $this->createMock(Operation::class);
        $operation2->method('getId')->willReturn(2);
        yield 'Operation #2 not included in list' => [
            'operation' => $operation2,
            'operationDate' => $validOperationDate,
            'expectedResult' => true,
        ];

        $operation3 = new TransferInOperation();
        $transfer3 = $this->createMock(TransferIn::class);
        $transfer3->method('getDebitAccount')->willReturn((new Account())->setNumber(self::SKIP_ACCOUNT_NUMBER));
        $operation3->setTransfer($transfer3);
        yield 'In Operation #3 debit account from skip-account list' => [
            'operation' => $operation3,
            'operationDate' => $validOperationDate,
            'expectedResult' => false,
        ];

        $operation4 = new TransferOutOperation();
        $transfer4 = $this->createMock(TransferOut::class);
        $transfer4->method('getBeneficiary')->willReturn((new PartyIban())->setIban(self::FILL_ACCOUNT_IBAN));
        $transfer4->method('getCreditAccount')->willReturn((new Account())->setNumber(self::SKIP_ACCOUNT_NUMBER));
        $operation4->setTransfer($transfer4);
        yield 'Out Operation #4 credit account from skip-account list and beneficiary fill account' => [
            'operation' => $operation4,
            'operationDate' => $validOperationDate,
            'expectedResult' => true,
        ];

        $operation5 = new TransferOutOperation();
        $transfer5 = $this->createMock(TransferOut::class);
        $transfer5->method('getBeneficiary')->willReturn(new PartyIban());
        $transfer5->method('getCreditAccount')->willReturn((new Account())->setNumber(self::SKIP_ACCOUNT_NUMBER));
        $operation5->setTransfer($transfer5);
        yield 'Operation #5 credit account from skip-account list and beneficiary not fill account' => [
            'operation' => $operation5,
            'operationDate' => $validOperationDate,
            'expectedResult' => false,
        ];

        $notValidOperationDate = new DateTimeImmutable('2021-12-29');
        $transfer6 = $this->createMock(TransferOut::class);
        $operation6 = new TransferOutOperation();
        $operation6->setTransfer($transfer6);
        yield 'Operation #6 out operation date is less than new accounting start date' => [
            'operation' => $operation6,
            'operationDate' => $notValidOperationDate,
            'expectedResult' => false,
        ];
    }

    public function testShouldVerifyByRules(): void
    {
        $operation = new TransferOutOperation();
        $transfer = $this->createMock(TransferInternal::class);
        $account = (new Account())->setNumber(self::SKIP_ACCOUNT_NUMBER);
        $transfer->method('getCreditAccount')->willReturn($account);
        $transfer->method('getDebitAccount')->willReturn($account);
        $transfer->method('getCreatedAt')->willReturn(new DateTime());
        $transfer->method('getId')->willReturn(1);
        $operation->setTransfer($transfer);

        $this
            ->accountOwnerResolver
            ->expects($this->exactly(2))
            ->method('getRealAccountOwner')
            ->with($account)
        ;

        $this->operationProcessingVerifier->shouldProcess($operation);
    }
}
