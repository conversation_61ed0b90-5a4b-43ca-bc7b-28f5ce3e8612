<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\CurrencyBundle\Service\CurrencyTypeHelper;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferMetalsRule;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class InternalTransferMetalsRuleTest extends TestCase
{
    private InternalTransferMetalsRule $internalTransferMetalsRule;
    /**
     * @var CurrencyTypeHelper|MockObject
     */
    private CurrencyTypeHelper $currencyTypeHelper;

    public function setUp(): void
    {
        $this->currencyTypeHelper = $this->createMock(CurrencyTypeHelper::class);
        $this->internalTransferMetalsRule = new InternalTransferMetalsRule(
            $this->currencyTypeHelper
        );
    }

    public function testMatch(): void
    {
        $transfer = $this->createMock(TransferInternal::class);
        $transfer
            ->expects($this->any())
            ->method('getAmountCurrency')
            ->willReturn('XAU')
        ;
        $parameterStack = new OperationProcessingMatchParameterStack(
            $transfer,
            $this->createMock(RefundInOperation::class),
            true,
            true
        );

        $this
            ->currencyTypeHelper
            ->expects($this->once())
            ->method('isPreciousMetal')
            ->with($transfer->getAmountCurrency())
            ->willReturn(true)
        ;

        $this->assertTrue($this->internalTransferMetalsRule->match($parameterStack));
    }

    public function testDoNotMatch(): void
    {
        $transfer = $this->createMock(TransferInternal::class);
        $transfer
            ->expects($this->any())
            ->method('getAmountCurrency')
            ->willReturn('EUR')
        ;
        $parameterStack = new OperationProcessingMatchParameterStack(
            $transfer,
            $this->createMock(RefundInOperation::class),
            true,
            true
        );

        $this
            ->currencyTypeHelper
            ->expects($this->once())
            ->method('isPreciousMetal')
            ->with($transfer->getAmountCurrency())
            ->willReturn(false)
        ;

        $this->assertFalse($this->internalTransferMetalsRule->match($parameterStack));
    }

    public function testVerify(): void
    {
        $params = $this->createMock(OperationProcessingVerifierParameterStack::class);

        $this->assertTrue($this->internalTransferMetalsRule->verify($params));
    }
}
