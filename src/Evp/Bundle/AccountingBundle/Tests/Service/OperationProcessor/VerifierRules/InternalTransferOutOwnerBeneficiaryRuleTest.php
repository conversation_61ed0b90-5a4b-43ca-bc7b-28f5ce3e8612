<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOutOwnerBeneficiaryRule;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class InternalTransferOutOwnerBeneficiaryRuleTest extends TestCase
{
    private InternalTransferOutOwnerBeneficiaryRule $internalTransferOutOwnerBeneficiaryRule;
    /**
     * @var PartnerAccountProvider|MockObject
     */
    private PartnerAccountProvider $partnerAccountProvider;
    private const ACCOUNT_NUMBER_FOR_CASH = 'ACCOUNT_NUMBER_FOR_CASH';
    private const ACCOUNT_NUMBER_PAYSERA_DEMO = 'ACCOUNT_NUMBER_PAYSERA_DEMO';
    private const ACCOUNT_NUMBER_PARTNER_CHARGE = 'ACCOUNT_NUMBER_PARTNER_CHARGE';
    private const ACCOUNT_NUMBER_SMS = 'ACCOUNT_NUMBER_SMS';
    private const ACCOUNT_NUMBER_CASHBACK = 'ACCOUNT_NUMBER_CASHBACK';
    private const ACCOUNT_NUMBER_PARTNER_COMMISSION = 'ACCOUNT_NUMBER_PARTNER_COMMISSION';
    private const TERMINAL_ACCOUNT = 'TERMINAL_ACCOUNT';

    public function setUp(): void
    {
        $this->partnerAccountProvider = $this->createMock(PartnerAccountProvider::class);
        $this->internalTransferOutOwnerBeneficiaryRule = new InternalTransferOutOwnerBeneficiaryRule(
            $this->partnerAccountProvider,
            self::ACCOUNT_NUMBER_FOR_CASH,
            self::ACCOUNT_NUMBER_PAYSERA_DEMO,
            self::ACCOUNT_NUMBER_PARTNER_CHARGE,
            self::ACCOUNT_NUMBER_SMS,
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION,
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION,
            [self::TERMINAL_ACCOUNT]
        );
    }

    /**
     * @dataProvider parameterDataProvider
     */
    public function testMatch(OperationProcessingMatchParameterStack $parameters, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferOutOwnerBeneficiaryRule->match($parameters));
    }

    public function parameterDataProvider(): iterable
    {
        yield 'Should match transfer out operation and owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                false,
                true
            ),
            'expectedResult' => true,
        ];

        yield 'Should not match because of operation type' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                false,
                true
            ),
            'expectedResult' => false,
        ];

        yield 'Should not match because of owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                false,
                false
            ),
            'expectedResult' => false,
        ];
    }

    /**
     * @dataProvider transferDataProvider
     */
    public function testVerify(OperationProcessingVerifierParameterStack $params, bool $expectedResult): void
    {
        $this->partnerAccountProvider->expects($this->any())
            ->method('getPartnerCashbackAccountNumber')
            ->willReturn(self::ACCOUNT_NUMBER_CASHBACK)
        ;
        $this->assertEquals($expectedResult, $this->internalTransferOutOwnerBeneficiaryRule->verify($params));
    }

    public function transferDataProvider(): iterable
    {
        yield 'Should not process for terminal account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::TERMINAL_ACCOUNT),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should process for cash account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_FOR_CASH),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should process for demo account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_PAYSERA_DEMO),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should not process for partner charge account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_PARTNER_CHARGE),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should not process for cashback account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_CASHBACK),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
        ];
    }
}
