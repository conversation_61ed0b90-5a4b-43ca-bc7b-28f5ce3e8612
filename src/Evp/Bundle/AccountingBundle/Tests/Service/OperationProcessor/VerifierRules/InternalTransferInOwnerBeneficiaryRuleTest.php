<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Exception\InvalidDebitAccountException;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferInOwnerBeneficiaryRule;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class InternalTransferInOwnerBeneficiaryRuleTest extends TestCase
{
    private InternalTransferInOwnerBeneficiaryRule $internalTransferInOwnerBeneficiaryRule;
    /**
     * @var PartnerAccountProvider|MockObject
     */
    private PartnerAccountProvider $partnerAccountProvider;
    private const ACCOUNT_NUMBER_FOR_CASH = 'ACCOUNT_NUMBER_FOR_CASH';
    private const ACCOUNT_NUMBER_PAYSERA_DEMO = 'ACCOUNT_NUMBER_PAYSERA_DEMO';
    private const ACCOUNT_NUMBER_PARTNER_CHARGE = 'ACCOUNT_NUMBER_PARTNER_CHARGE';
    private const IGNORED_OWNER_ACCOUNT = 'IGNORED_OWNER_ACCOUNT';
    private const ACCOUNT_NUMBER_PARTNER_COMMISSION = 'ACCOUNT_NUMBER_PARTNER_COMMISSION';
    private const TERMINAL_ACCOUNT = 'TERMINAL_ACCOUNT';
    private const PARTNER_CASHBACK_ACCOUNT = 'PARTNER_CASHBACK_ACCOUNT';

    public function setUp(): void
    {
        $this->partnerAccountProvider = $this->createMock(PartnerAccountProvider::class);
        $this->internalTransferInOwnerBeneficiaryRule = new InternalTransferInOwnerBeneficiaryRule(
            $this->createMock(LoggerInterface::class),
            $this->partnerAccountProvider,
            self::ACCOUNT_NUMBER_FOR_CASH,
            self::ACCOUNT_NUMBER_PAYSERA_DEMO,
            self::ACCOUNT_NUMBER_PARTNER_CHARGE,
            [self::IGNORED_OWNER_ACCOUNT],
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION,
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION,
            [self::TERMINAL_ACCOUNT]
        );
    }

    /**
     * @dataProvider parameterDataProvider
     */
    public function testMatch(OperationProcessingMatchParameterStack $params, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferInOwnerBeneficiaryRule->match($params));
    }

    public function parameterDataProvider(): iterable
    {
        yield 'Should match transfer in operation and owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(Transfer::class),
                $this->createMock(TransferInOperation::class),
                false,
                true
            ),
            'expectedResult' => true,
        ];

        yield 'Should not match because of owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(Transfer::class),
                $this->createMock(TransferInOperation::class),
                true,
                true
            ),
            'expectedResult' => false,
        ];

        yield 'Should not match because of operation type' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(Transfer::class),
                $this->createMock(TransferOutOperation::class),
                false,
                true
            ),
            'expectedResult' => false,
        ];
    }

    /**
     * @dataProvider dataProvider
     */
    public function testVerify(OperationProcessingVerifierParameterStack $param, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferInOwnerBeneficiaryRule->verify($param));
    }

    public function dataProvider(): iterable
    {
        yield 'Should process for cash account' => [
            'params' => new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_FOR_CASH),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should process for demo account' => [
            'params' => new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_PAYSERA_DEMO),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
        ];

        yield 'Should not process for partner charge account' => [
            'params' => new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::ACCOUNT_NUMBER_PARTNER_CHARGE),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
        ];

        yield 'Should not process for terminal account' => [
            'params' => new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::TERMINAL_ACCOUNT),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
        ];

        yield 'Should not process for ignore owner account' => [
            'params' => new OperationProcessingVerifierParameterStack(
                new Account(),
                (new Account())->setNumber(self::IGNORED_OWNER_ACCOUNT),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
        ];
    }

    public function testErrorOnInvalidAccount(): void
    {
        $parameterStack = new OperationProcessingVerifierParameterStack(
            new Account(),
            (new Account())->setNumber(self::PARTNER_CASHBACK_ACCOUNT),
            new DateTime(),
            'paysera_lt',
            'paysera_lt',
            1
        );

        $this->partnerAccountProvider
            ->method('getPartnerCashbackAccountNumber')
            ->willReturn(self::PARTNER_CASHBACK_ACCOUNT)
        ;
        $this->expectException(InvalidDebitAccountException::class);

        $this->internalTransferInOwnerBeneficiaryRule->verify($parameterStack);
    }
}
