<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferInOwnerSenderRule;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class InternalTransferInOwnerSenderRuleTest extends TestCase
{
    private InternalTransferInOwnerSenderRule $internalTransferInOwnerSenderRule;
    /**
     * @var AccountTypeAtDateProvider|MockObject
     */
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;
    private const ACCOUNT_NUMBER_FOR_CASH = 'ACCOUNT_NUMBER_FOR_CASH';
    private const ACCOUNT_NUMBER_SALARY = 'ACCOUNT_NUMBER_SALARY';
    private const ACCOUNT_NUMBER_SMS = 'ACCOUNT_NUMBER_SMS';
    private const IGNORED_OWNER_ACCOUNT = 'IGNORED_OWNER_ACCOUNT';
    private const TERMINAL_ACCOUNT = 'TERMINAL_ACCOUNT';

    public function setUp(): void
    {
        $this->accountTypeAtDateProvider = $this->createMock(AccountTypeAtDateProvider::class);
        $partnerAccountProvider = $this->createMock(PartnerAccountProvider::class);
        $this->internalTransferInOwnerSenderRule = new InternalTransferInOwnerSenderRule(
            $this->accountTypeAtDateProvider,
            $partnerAccountProvider,
            self::ACCOUNT_NUMBER_FOR_CASH,
            [self::TERMINAL_ACCOUNT],
            self::ACCOUNT_NUMBER_SALARY,
            self::ACCOUNT_NUMBER_SMS,
            [self::IGNORED_OWNER_ACCOUNT],
            'evpMainAccount',
            'nTechnologijosAccount'
        );
    }

    /**
     * @dataProvider parameterDataProvider
     */
    public function testMatch(OperationProcessingMatchParameterStack $parameters, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferInOwnerSenderRule->match($parameters));
    }

    public function parameterDataProvider(): iterable
    {
        yield 'Should match transfer in operation and owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                true,
                false
            ),
            'expectedResult' => true,
        ];

        yield 'Should not match because of owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                true,
                true
            ),
            'expectedResult' => false,
        ];

        yield 'Should not match because of operation type' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                false,
                true
            ),
            'expectedResult' => false,
        ];
    }

    /**
     * @dataProvider dataProvider
     */
    public function testVerify(
        OperationProcessingVerifierParameterStack $params,
        bool $expectedResult,
        string $accountType
    ): void {
        $this->accountTypeAtDateProvider->expects($this->any())
            ->method('getAccountTypeAtDate')
            ->willReturn($accountType)
        ;
        $this->assertEquals($expectedResult, $this->internalTransferInOwnerSenderRule->verify($params));
    }

    public function dataProvider(): iterable
    {
        yield 'Should process for cash account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_FOR_CASH),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
            'accountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should process for salary account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_SALARY),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
            'accountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for sms account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_SMS),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
            'accountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for terminal account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::TERMINAL_ACCOUNT),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
            'accountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for ignore owner account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::IGNORED_OWNER_ACCOUNT),
                (new Account())->setType(Account::TYPE_CONTIS),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'accountType' => Account::TYPE_CONTIS,
        ];
    }
}
