<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOutOwnerSenderRule;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class InternalTransferOutOwnerSenderRuleTest extends TestCase
{
    private InternalTransferOutOwnerSenderRule $internalTransferOutOwnerSenderRule;
    /**
     * @var AccountTypeAtDateProvider|MockObject
     */
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;
    /**
     * @var PartnerAccountProvider|MockObject
     */
    private PartnerAccountProvider $partnerAccountProvider;
    private const ACCOUNT_NUMBER_FOR_CASH = 'ACCOUNT_NUMBER_FOR_CASH';
    private const ACCOUNT_NUMBER_SALARY = 'ACCOUNT_NUMBER_SALARY';
    private const ACCOUNT_NUMBER_SMS = 'ACCOUNT_NUMBER_SMS';
    private const IGNORED_OWNER_ACCOUNT = 'IGNORED_OWNER_ACCOUNT';
    private const TERMINAL_ACCOUNT = 'TERMINAL_ACCOUNT';
    private const ACCOUNT_NUMBER_CASHBACK = 'ACCOUNT_NUMBER_CASHBACK';
    private const ACCOUNT_NUMBER_PARTNER_COMMISSION = 'ACCOUNT_NUMBER_PARTNER_COMMISSION';

    public function setUp(): void
    {
        $this->accountTypeAtDateProvider = $this->createMock(AccountTypeAtDateProvider::class);
        $this->partnerAccountProvider = $this->createMock(PartnerAccountProvider::class);
        $this->internalTransferOutOwnerSenderRule = new InternalTransferOutOwnerSenderRule(
            $this->accountTypeAtDateProvider,
            $this->partnerAccountProvider,
            [self::TERMINAL_ACCOUNT],
            self::ACCOUNT_NUMBER_SALARY,
            self::ACCOUNT_NUMBER_SMS,
            [self::IGNORED_OWNER_ACCOUNT],
            self::ACCOUNT_NUMBER_FOR_CASH,
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION,
            self::ACCOUNT_NUMBER_PARTNER_COMMISSION
        );
    }

    /**
     * @dataProvider parameterDataProvider
     */
    public function testMatch(OperationProcessingMatchParameterStack $parameters, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferOutOwnerSenderRule->match($parameters));
    }

    public function parameterDataProvider(): iterable
    {
        yield 'Should match transfer out operation and owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                true,
                false
            ),
            'expectedResult' => true,
        ];

        yield 'Should not match because of owner' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                true,
                true
            ),
            'expectedResult' => false,
        ];

        yield 'Should not match because of operation type' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                false,
                true
            ),
            'expectedResult' => false,
        ];
    }

    /**
     * @dataProvider dataProvider
     */
    public function testVerify(
        OperationProcessingVerifierParameterStack $params,
        bool $expectedResult,
        string $creditAccountType,
        string $debitAccountType
    ): void {
        $this->accountTypeAtDateProvider->expects($this->any())
            ->method('getAccountTypeAtDate')
            ->willReturnOnConsecutiveCalls($creditAccountType, $debitAccountType)
        ;
        $this->partnerAccountProvider->expects($this->any())
            ->method('getPartnerCashbackAccountNumber')
            ->willReturn(self::ACCOUNT_NUMBER_CASHBACK)
        ;
        $this->assertEquals($expectedResult, $this->internalTransferOutOwnerSenderRule->verify($params));
    }

    public function dataProvider(): iterable
    {
        yield 'Should not process for sms account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_SMS),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should process for cash account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_FOR_CASH),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => true,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for terminal account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::TERMINAL_ACCOUNT),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for cashback' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_CASHBACK),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for salary account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::ACCOUNT_NUMBER_SALARY),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_LOCAL,
        ];

        yield 'Should not process for ignore owner account' => [
            'params' =>  new OperationProcessingVerifierParameterStack(
                (new Account())->setNumber(self::IGNORED_OWNER_ACCOUNT),
                new Account(),
                new DateTime(),
                'paysera_lt',
                'paysera_lt',
                1
            ),
            'expectedResult' => false,
            'creditAccountType' => Account::TYPE_LOCAL,
            'debitAccountType' => Account::TYPE_CONTIS,
        ];
    }
}
