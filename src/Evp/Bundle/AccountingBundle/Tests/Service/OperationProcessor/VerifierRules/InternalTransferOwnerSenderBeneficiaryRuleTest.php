<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\InternalTransferOwnerSenderBeneficiaryRule;
use PHPUnit\Framework\TestCase;

class InternalTransferOwnerSenderBeneficiaryRuleTest extends TestCase
{
    private InternalTransferOwnerSenderBeneficiaryRule $internalTransferOwnerSenderBeneficiaryRule;

    public function setUp(): void
    {
        $this->internalTransferOwnerSenderBeneficiaryRule = new InternalTransferOwnerSenderBeneficiaryRule();
    }

    /**
     * @dataProvider parameterDataProvider
     */
    public function testMatch(OperationProcessingMatchParameterStack $parameters, bool $expectedResult): void
    {
        $this->assertEquals($expectedResult, $this->internalTransferOwnerSenderBeneficiaryRule->match($parameters));
    }

    public function parameterDataProvider(): iterable
    {
        yield 'Should match when both true' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferOutOperation::class),
                true,
                true
            ),
            'expectedResult' => false,
        ];

        yield 'Should match when both false' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                false,
                false
            ),
            'expectedResult' => true,
        ];

        yield 'Should not match one is false' => [
            'parameters' => new OperationProcessingMatchParameterStack(
                $this->createMock(TransferInternal::class),
                $this->createMock(TransferInOperation::class),
                false,
                true
            ),
            'expectedResult' => false,
        ];
    }

    public function testVerify(): void
    {
        $params = $this->createMock(OperationProcessingVerifierParameterStack::class);

        $this->assertTrue($this->internalTransferOwnerSenderBeneficiaryRule->verify($params));
    }
}
