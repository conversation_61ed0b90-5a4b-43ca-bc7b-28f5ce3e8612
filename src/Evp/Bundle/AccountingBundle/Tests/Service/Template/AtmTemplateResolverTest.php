<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\Template;

use Evp\Bundle\AccountingBundle\Service\Template\AtmTemplateResolver;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Component\Money\Money;
use Evp\Tests\BaseTestCase;

class AtmTemplateResolverTest extends BaseTestCase
{
    private AtmTemplateResolver $resolver;

    protected function setUp(): void
    {
        $this->resolver = $this->getContainer()
            ->get('evp_accounting.service.atm_template_resolver');
    }

    private function createTransfer(
        string $purpose,
        string $creditAccountNumber,
        string $debitAccountNumber,
        string $payerAccountNumber,
        string $beneficiaryAccountNumber,
        ?Money $amountMoney = null
    ): TransferInternal {
        $transfer = new TransferInternal();
        $transfer->setPurpose($purpose);

        $creditAccount = new Account();
        $creditAccount->setNumber($creditAccountNumber);
        $transfer->setCreditAccount($creditAccount);

        $debitAccount = new Account();
        $debitAccount->setNumber($debitAccountNumber);
        $transfer->setDebitAccount($debitAccount);

        $payer = new PartyAccount($payerAccountNumber);
        $transfer->setPayer($payer);

        $beneficiary = new PartyAccount($beneficiaryAccountNumber);
        $transfer->setBeneficiary($beneficiary);

        if ($amountMoney !== null) {
            $transfer->setAmountMoney($amountMoney);
        }

        return $transfer;
    }

    public function testIsAtmTransferReturnsFalseForNonCashPurpose(): void
    {
        $transfer = $this->createTransfer(
            'non_cash_purpose',
            'beneficiary_account_number',
            'payer_account_number',
            'payer_account_number',
            'beneficiary_account_number'
        );

        $this->assertFalse($this->resolver->isAtmTransfer($transfer));
    }

    public function testIsAtmTransferReturnsTrueForTransferFromMainToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP4910001170426',
            'EVP2610017891909',
            'EVP4910001170426',
            'EVP2610017891909'
        );

        $this->assertTrue($this->resolver->isAtmTransfer($transfer));
    }

    public function testIsAtmTransferReturnsTrueForTransferFromAtmToMain(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'EVP2610017891909',
            'EVP4910001170426',
            'EVP4910001170426',
            'EVP2610017891909'
        );

        $this->assertTrue($this->resolver->isAtmTransfer($transfer));
    }

    public function testIsAtmTransferReturnsTrueForCashOutFromAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'credit',
            'EVP1810018331173',
            'credit',
            'EVP1810018331173',
        );

        $this->assertTrue($this->resolver->isAtmTransfer($transfer));
    }

    public function testIsAtmTransferReturnsTrueForCashInToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP2610017891909',
            'debit_account_number',
            'EVP2610017891909',
            'debit_account_number',
            new Money(100, 'EUR')
        );

        $this->assertTrue($this->resolver->isAtmTransfer($transfer));
    }

    public function testResolveInTemplateForTransferFromMainToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP4910001170426',
            'EVP2610017891909',
            'EVP4910001170426',
            'EVP2610017891909'
        );

        $this->assertEquals(
            'TRANSFER_CASHDESK_COLLECTION_CASH_IN',
            $this->resolver->resolveInTemplate($transfer)
        );
    }

    public function testResolveInTemplateForTransferFromAtmToMain(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'EVP2610017891909',
            'EVP4910001170426',
            'EVP4910001170426',
            'EVP2610017891909'
        );

        $this->assertEquals(
            'TRANSFER_CASHDESK_COLLECTION_CASH_OUT',
            $this->resolver->resolveInTemplate($transfer)
        );
    }

    public function testResolveInTemplateForCashInToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP2610017891909',
            'debit_account_number',
            'EVP2610017891909',
            'debit_account_number',
            new Money(100, 'EUR')
        );

        $this->assertEquals('TRANSFER_ATM_GOT_CASH_EUR', $this->resolver->resolveInTemplate($transfer));
    }

    public function testResolveInTemplateForCashOutFromAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'credit_account_number',
            'EVP1810018331173',
            'credit_account_number',
            'EVP1810018331173',
        );

        $this->assertEquals('W2P_TRANSFER_CASH_OUT_TRANSFER', $this->resolver->resolveInTemplate($transfer));
    }

    public function testResolveInTemplateReturnsNullForNonAtmTransfer(): void
    {
        $transfer = $this->createTransfer(
            'non_cash_purpose',
            'beneficiary_account_number',
            'payer_account_number',
            'payer_account_number',
            'beneficiary_account_number'
        );

        $this->assertNull($this->resolver->resolveInTemplate($transfer));
    }

    public function testResolveOutTemplateForTransferFromMainToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP4910001170426',
            'EVP2610017891909',
            'EVP4910001170426',
            'EVP2610017891909',
        );

        $this->assertEquals('TRANSFER_ATM_COLLECTION_CASH_OUT', $this->resolver->resolveOutTemplate($transfer));
    }

    public function testResolveOutTemplateForTransferFromAtmToMain(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'EVP1810018331173',
            'EVP4910001170426',
            'EVP4910001170426',
            'EVP1810018331173'
        );

        $this->assertEquals('TRANSFER_ATM_2_COLLECTION_CASH_IN', $this->resolver->resolveOutTemplate($transfer));
    }

    public function testResolveOutTemplateForCashInToAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_IN,
            'EVP6610018331176',
            'debit_account_number',
            'EVP6610018331176',
            'debit_account_number'
        );

        $this->assertEquals('W2P_TRANSFER_CASH_IN_TRANSFER', $this->resolver->resolveOutTemplate($transfer));
    }

    public function testResolveOutTemplateForCashOutFromAtm(): void
    {
        $transfer = $this->createTransfer(
            Transfer::PURPOSE_CASH_OUT,
            'credit_account_number',
            'EVP2610017891909',
            'credit_account_number',
            'EVP2610017891909',
            new Money(100, 'EUR')
        );

        $this->assertEquals('TRANSFER_ATM_GAVE_CASH_EUR', $this->resolver->resolveOutTemplate($transfer));
    }

    public function testResolveOutTemplateReturnsNullForNonAtmTransfer(): void
    {
        $transfer = $this->createTransfer(
            'non_cash_purpose',
            'beneficiary_account_number',
            'payer_account_number',
            'payer_account_number',
            'beneficiary_account_number'
        );

        $this->assertNull($this->resolver->resolveOutTemplate($transfer));
    }
}
