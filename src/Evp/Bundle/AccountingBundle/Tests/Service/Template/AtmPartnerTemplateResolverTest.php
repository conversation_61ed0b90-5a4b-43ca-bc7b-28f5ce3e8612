<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Tests\Service\Template;

use Evp\Bundle\AccountingBundle\DataTransferObject\AtmTemplateParameterStack;
use Evp\Bundle\AccountingBundle\Service\Template\AtmPartnerTemplateResolver;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Tests\BaseTestCase;

class AtmPartnerTemplateResolverTest extends BaseTestCase
{
    private AtmPartnerTemplateResolver $resolver;

    protected function setUp(): void
    {
        $this->resolver = $this->getContainer()->get('evp_accounting.service.atm_partner_template_resolver');
    }

    public function testIsAtmTransferReturnsFalseForNonCashPurpose(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'debit_account_number',
            'EUR',
            'non_cash_purpose'
        );

        $this->assertFalse($this->resolver->isAtmTransfer($parameterStack));
    }

    public function testIsAtmTransferReturnsTrueForTransferFromMainToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP4910001170426',
            'EVP2610017891909',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertTrue($this->resolver->isAtmTransfer($parameterStack));
    }

    public function testIsAtmTransferReturnsTrueForTransferFromAtmToMain(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP2610017891909',
            'EVP4910001170426',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertTrue($this->resolver->isAtmTransfer($parameterStack));
    }

    public function testIsAtmTransferReturnsTrueForCashOutFromAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'EVP1810018331173',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertTrue($this->resolver->isAtmTransfer($parameterStack));
    }

    public function testIsAtmTransferReturnsTrueForCashInToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP6610018331176',
            'debit_account_number',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertTrue($this->resolver->isAtmTransfer($parameterStack));
    }

    public function testResolveInTemplateForTransferFromMainToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP4910001170426',
            'EVP2610017891909',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertEquals(
            'TRANSFER_CASHDESK_COLLECTION_CASH_IN',
            $this->resolver->resolveInTemplate($parameterStack)
        );
    }

    public function testResolveInTemplateForTransferFromAtmToMain(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP2610017891909',
            'EVP4910001170426',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertEquals(
            'TRANSFER_CASHDESK_COLLECTION_CASH_OUT',
            $this->resolver->resolveInTemplate($parameterStack)
        );
    }

    public function testResolveInTemplateForCashInToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP2610017891909',
            'debit_account_number',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertEquals('TRANSFER_ATM_GOT_CASH_EUR', $this->resolver->resolveInTemplate($parameterStack));
    }

    public function testResolveInTemplateForCashOutFromAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'EVP1810018331173',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertEquals('W2P_TRANSFER_CASH_OUT_TRANSFER', $this->resolver->resolveInTemplate($parameterStack));
    }

    public function testResolveInTemplateReturnsNullForNonAtmTransfer(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'debit_account_number',
            'EUR',
            'non_cash_purpose'
        );

        $this->assertNull($this->resolver->resolveInTemplate($parameterStack));
    }

    public function testResolveOutTemplateForTransferFromMainToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP4910001170426',
            'EVP2610017891909',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertEquals('TRANSFER_ATM_COLLECTION_CASH_OUT', $this->resolver->resolveOutTemplate($parameterStack));
    }

    public function testResolveOutTemplateForTransferFromAtmToMain(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP1810018331173',
            'EVP4910001170426',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertEquals('TRANSFER_ATM_2_COLLECTION_CASH_IN', $this->resolver->resolveOutTemplate($parameterStack));
    }

    public function testResolveOutTemplateForCashInToAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'EVP6610018331176',
            'debit_account_number',
            'EUR',
            Transfer::PURPOSE_CASH_IN
        );

        $this->assertEquals('W2P_TRANSFER_CASH_IN_TRANSFER', $this->resolver->resolveOutTemplate($parameterStack));
    }

    public function testResolveOutTemplateForCashOutFromAtm(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'EVP2610017891909',
            'EUR',
            Transfer::PURPOSE_CASH_OUT
        );

        $this->assertEquals('TRANSFER_ATM_GAVE_CASH_EUR', $this->resolver->resolveOutTemplate($parameterStack));
    }

    public function testResolveOutTemplateReturnsNullForNonAtmTransfer(): void
    {
        $parameterStack = new AtmTemplateParameterStack(
            'paysera_lt',
            'paysera_lt',
            'credit_account_number',
            'debit_account_number',
            'EUR',
            'non_cash_purpose'
        );

        $this->assertNull($this->resolver->resolveOutTemplate($parameterStack));
    }
}
