<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\OperationProcessorResolver;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\OperationProcessingVerifier;
use Psr\Log\LoggerInterface;

class PartnerAccountingProcessor
{
    private OperationProcessorResolver $operationProcessorResolver;
    private OperationProcessingVerifier $operationProcessingVerifier;
    private LoggerInterface $logger;

    public function __construct(
        OperationProcessorResolver $resolver,
        OperationProcessingVerifier $operationProcessingVerifier,
        LoggerInterface $logger
    ) {
        $this->operationProcessorResolver = $resolver;
        $this->operationProcessingVerifier = $operationProcessingVerifier;
        $this->logger = $logger;
    }

    public function processOperation(Operation $operation): void
    {
        if (!$this->operationProcessingVerifier->shouldProcess($operation)) {
            $this->logger->debug('Skipping partner operation creation', [
                'operation_id' => $operation->getId(),
            ]);
            return;
        }

        $this->operationProcessorResolver->getProcessor($operation)->process($operation);
    }
}
