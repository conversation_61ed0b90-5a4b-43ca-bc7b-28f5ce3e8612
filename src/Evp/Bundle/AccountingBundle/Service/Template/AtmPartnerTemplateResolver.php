<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\Template;

use Evp\Bundle\AccountingBundle\DataTransferObject\AtmTemplateParameterStack;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use RuntimeException;

class AtmPartnerTemplateResolver
{
    private PartnerAccountProvider $partnerAccountProvider;
    private array $atmAccountNumbers;
    private string $cashDeskCollectionCashIn;
    private string $cashDeskCollectionCashOut;
    private string $atmCollectionIn;
    private string $atmCollectionOut;
    private string $w2pTransferCashIn;
    private string $w2pTransferCashOut;
    private string $atmGotCash;
    private string $atmGaveCash;

    public function __construct(
        PartnerAccountProvider $partnerAccountProvider,
        array $atmAccountNumbers,
        string $cashDeskCollectionCashIn,
        string $cashDeskCollectionCashOut,
        string $atmCollectionIn,
        string $atmCollectionOut,
        string $w2pTransferCashIn,
        string $w2pTransferCashOut,
        string $atmGotCash,
        string $atmGaveCash
    ) {
        $this->partnerAccountProvider = $partnerAccountProvider;
        $this->atmAccountNumbers = $atmAccountNumbers;
        $this->cashDeskCollectionCashIn = $cashDeskCollectionCashIn;
        $this->cashDeskCollectionCashOut = $cashDeskCollectionCashOut;
        $this->atmCollectionIn = $atmCollectionIn;
        $this->atmCollectionOut = $atmCollectionOut;
        $this->w2pTransferCashIn = $w2pTransferCashIn;
        $this->w2pTransferCashOut = $w2pTransferCashOut;
        $this->atmGotCash = $atmGotCash;
        $this->atmGaveCash = $atmGaveCash;
    }

    public function isAtmTransfer(AtmTemplateParameterStack $parameterStack): bool
    {
        if (!in_array($parameterStack->getPurpose(), [Transfer::PURPOSE_CASH_OUT, Transfer::PURPOSE_CASH_IN], true)) {
            return false;
        }

        return (
            $this->isTransferFromMainToAtm(
                $parameterStack->getBeneficiaryPartnerCode(),
                $parameterStack->getCreditAccountNumber(),
                $parameterStack->getDebitAccountNumber()
            )
            || $this->isTransferFromAtmToMain(
                $parameterStack->getPayerPartnerCode(),
                $parameterStack->getCreditAccountNumber(),
                $parameterStack->getDebitAccountNumber()
            )
            || $this->isCashOutFromAtm($parameterStack->getDebitAccountNumber(), $parameterStack->getPurpose())
            || $this->isCashInToAtm($parameterStack->getCreditAccountNumber(), $parameterStack->getPurpose())
        );
    }

    public function resolveInTemplate(AtmTemplateParameterStack $parameterStack): ?string
    {
        if ($this->isTransferFromMainToAtm(
            $parameterStack->getBeneficiaryPartnerCode(),
            $parameterStack->getCreditAccountNumber(),
            $parameterStack->getDebitAccountNumber())
        ) {
            return $this->cashDeskCollectionCashIn;
        }

        if ($this->isTransferFromAtmToMain(
            $parameterStack->getPayerPartnerCode(),
            $parameterStack->getCreditAccountNumber(),
            $parameterStack->getDebitAccountNumber()
        )) {
            return $this->cashDeskCollectionCashOut;
        }

        if ($this->isCashInToAtm($parameterStack->getCreditAccountNumber(), $parameterStack->getPurpose())) {
            return strtoupper(strtr($this->atmGotCash, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($parameterStack->getCreditAccountNumber()),
                '%currency%' => $parameterStack->getCurrency(),
            ]));
        }

        if ($this->isCashOutFromAtm($parameterStack->getDebitAccountNumber(), $parameterStack->getPurpose())) {
            return $this->w2pTransferCashOut;
        }

        return null;
    }

    public function resolveOutTemplate(AtmTemplateParameterStack $parameterStack): ?string
    {
        if ($this->isTransferFromMainToAtm(
            $parameterStack->getBeneficiaryPartnerCode(),
            $parameterStack->getCreditAccountNumber(),
            $parameterStack->getDebitAccountNumber()
        )) {
            return strtoupper(strtr($this->atmCollectionOut, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($parameterStack->getDebitAccountNumber()),
            ]));
        }

        if ($this->isTransferFromAtmToMain(
            $parameterStack->getPayerPartnerCode(),
            $parameterStack->getCreditAccountNumber(),
            $parameterStack->getDebitAccountNumber()
        )) {
            return strtoupper(strtr($this->atmCollectionIn, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($parameterStack->getCreditAccountNumber()),
            ]));
        }

        if ($this->isCashInToAtm($parameterStack->getCreditAccountNumber(), $parameterStack->getPurpose())) {
            return $this->w2pTransferCashIn;
        }

        if ($this->isCashOutFromAtm($parameterStack->getDebitAccountNumber(), $parameterStack->getPurpose())) {
            return strtoupper(strtr($this->atmGaveCash, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($parameterStack->getDebitAccountNumber()),
                '%currency%' => $parameterStack->getCurrency(),
            ]));
        }

        return null;
    }

    private function isTransferFromMainToAtm(
        string $beneficiaryPartnerCode,
        string $creditAccountNumber,
        string $debitAccountNumber
    ): bool {
        $cashAccountNumber = $this->partnerAccountProvider->getPartnerCashAccountNumber($beneficiaryPartnerCode);

        return (
            $creditAccountNumber === $cashAccountNumber
            && in_array($debitAccountNumber, $this->atmAccountNumbers, true)
        );
    }

    private function isTransferFromAtmToMain(
        string $payerPartnerCode,
        string $creditAccountNumber,
        string $debitAccountNumber
    ): bool {
        $cashAccountNumber = $this->partnerAccountProvider->getPartnerCashAccountNumber($payerPartnerCode);

        return (
            $debitAccountNumber === $cashAccountNumber
            && in_array($creditAccountNumber, $this->atmAccountNumbers, true)
        );
    }

    private function isCashInToAtm(string $creditAccountNumber, string $purpose): bool
    {
        return (
            in_array($creditAccountNumber, $this->atmAccountNumbers, true)
            && $purpose === Transfer::PURPOSE_CASH_IN
        );
    }

    private function isCashOutFromAtm(string $debitAccountNumber, string $purpose): bool
    {
        return (
            in_array($debitAccountNumber, $this->atmAccountNumbers, true)
            && $purpose === Transfer::PURPOSE_CASH_OUT
        );
    }

    private function getAtmTemplateKeyReplaceString(string $accountNumber): string
    {
        foreach ($this->atmAccountNumbers as $key => $number) {
            if ($number === $accountNumber && $key === 0) {
                return '';
            }
            if ($number === $accountNumber) {
                return '_' . $key;
            }
        }

        throw new RuntimeException(sprintf('Account number "%s" not found in atm account numbers', $accountNumber));
    }
}
