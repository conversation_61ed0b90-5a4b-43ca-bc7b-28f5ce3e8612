<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\Template;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use RuntimeException;

class AtmTemplateResolver
{
    private array $atmAccountNumbers;
    private string $cashAccountNumber;
    private string $cashDeskCollectionCashIn;
    private string $cashDeskCollectionCashOut;
    private string $atmCollectionIn;
    private string $atmCollectionOut;
    private string $w2pTransferCashIn;
    private string $w2pTransferCashOut;
    private string $atmGotCash;
    private string $atmGaveCash;

    public function __construct(
        array $atmAccountNumbers,
        string $cashAccountNumber,
        string $cashDeskCollectionCashIn,
        string $cashDeskCollectionCashOut,
        string $atmCollectionIn,
        string $atmCollectionOut,
        string $w2pTransferCashIn,
        string $w2pTransferCashOut,
        string $atmGotCash,
        string $atmGaveCash
    ) {
        $this->atmAccountNumbers = $atmAccountNumbers;
        $this->cashAccountNumber = $cashAccountNumber;
        $this->cashDeskCollectionCashIn = $cashDeskCollectionCashIn;
        $this->cashDeskCollectionCashOut = $cashDeskCollectionCashOut;
        $this->atmCollectionIn = $atmCollectionIn;
        $this->atmCollectionOut = $atmCollectionOut;
        $this->w2pTransferCashIn = $w2pTransferCashIn;
        $this->w2pTransferCashOut = $w2pTransferCashOut;
        $this->atmGotCash = $atmGotCash;
        $this->atmGaveCash = $atmGaveCash;
    }

    public function isAtmTransfer(TransferInternal $transfer): bool
    {
        if (!in_array($transfer->getPurpose(), [Transfer::PURPOSE_CASH_OUT, Transfer::PURPOSE_CASH_IN], true)) {
            return false;
        }

        if (
            $this->isTransferFromMainToAtm($transfer)
            || $this->isTransferFromAtmToMain($transfer)
            || $this->isCashOutFromAtm($transfer)
            || $this->isCashInToAtm($transfer)
        ) {
            return true;
        }

        return false;
    }

    public function resolveInTemplate(TransferInternal $transfer): ?string
    {
        if ($this->isTransferFromMainToAtm($transfer)) {
            return $this->cashDeskCollectionCashIn;
        }

        if ($this->isTransferFromAtmToMain($transfer)) {
            return $this->cashDeskCollectionCashOut;
        }

        if ($this->isCashInToAtm($transfer)) {
            return strtoupper(strtr($this->atmGotCash, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($transfer->getCreditAccount()->getNumber()),
                '%currency%' => $transfer->getAmountMoney()->getCurrency(),
            ]));
        }

        if ($this->isCashOutFromAtm($transfer)) {
            return $this->w2pTransferCashOut;
        }

        return null;
    }

    public function resolveOutTemplate(TransferInternal $transfer): ?string
    {
        if ($this->isTransferFromMainToAtm($transfer)) {
            return strtoupper(strtr($this->atmCollectionOut, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($transfer->getDebitAccount()->getNumber()),
            ]));
        }

        if ($this->isTransferFromAtmToMain($transfer)) {
            return strtoupper(strtr($this->atmCollectionIn, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($transfer->getCreditAccount()->getNumber()),
            ]));
        }

        if ($this->isCashInToAtm($transfer)) {
            return $this->w2pTransferCashIn;
        }

        if ($this->isCashOutFromAtm($transfer)) {
            return strtoupper(strtr($this->atmGaveCash, [
                '%key%' => $this->getAtmTemplateKeyReplaceString($transfer->getDebitAccount()->getNumber()),
                '%currency%' => $transfer->getAmountMoney()->getCurrency(),
            ]));
        }

        return null;
    }

    private function isTransferFromMainToAtm(TransferInternal $transfer): bool
    {
        return (
            $transfer->getCreditAccount()->getNumber() === $this->cashAccountNumber
            && in_array($transfer->getDebitAccount()->getNumber(), $this->atmAccountNumbers, true)
        );
    }

    private function isTransferFromAtmToMain(TransferInternal $transfer): bool
    {
        return (
            $transfer->getDebitAccount()->getNumber() === $this->cashAccountNumber
            && in_array($transfer->getCreditAccount()->getNumber(), $this->atmAccountNumbers, true)
        );
    }

    private function isCashInToAtm(TransferInternal $transfer): bool
    {
        return (
            in_array($transfer->getPayer()->getNumber(), $this->atmAccountNumbers, true)
            && $transfer->getPurpose() === Transfer::PURPOSE_CASH_IN
        );
    }

    private function isCashOutFromAtm(TransferInternal $transfer): bool
    {
        return (
            in_array($transfer->getBeneficiary()->getNumber(), $this->atmAccountNumbers, true)
            && $transfer->getPurpose() === Transfer::PURPOSE_CASH_OUT
        );
    }

    private function getAtmTemplateKeyReplaceString(string $accountNumber): string
    {
        foreach ($this->atmAccountNumbers as $key => $number) {
            if ($number === $accountNumber && $key === 0) {
                return '';
            }
            if ($number === $accountNumber) {
                return '_' . $key;
            }
        }

        throw new RuntimeException(sprintf('Account number "%s" not found in atm account numbers', $accountNumber));
    }
}
