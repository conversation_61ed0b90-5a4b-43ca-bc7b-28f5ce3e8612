<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service;

use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;

class RemoteOperationRequestPublishOrderOptimizer
{
    /**
     * Order remoteOperationRequests to have one from every type_reference group, then next from every group
     * this way we avoid as much as possible to publish two consecutive with the same reference and type.
     *
     * @param array $remoteOperationRequests
     * @return array
     */
    public function optimize(array $remoteOperationRequests): array
    {
        $counter = 0;
        $length = count($remoteOperationRequests);
        $remoteOperationRequestsGroups = [];

        foreach ($remoteOperationRequests as $remoteOperationRequest) {
            $groupKey = sprintf(
                '%s_%s',
                $remoteOperationRequest['type'],
                $remoteOperationRequest['reference'],
            );
            $remoteOperationRequestsGroups[$groupKey][] = $remoteOperationRequest;
        }

        $orderedRemoteOperationRequests = [];
        foreach($remoteOperationRequestsGroups as $remoteOperationRequestsGroup) {
            $counter++;

            foreach($remoteOperationRequestsGroup as $index => $remoteOperationRequest) {
                $priority = $counter + $index * $length;
                $orderedRemoteOperationRequests[$priority] = $remoteOperationRequest;
            }
        }

        ksort($orderedRemoteOperationRequests);

        return array_values($orderedRemoteOperationRequests);
    }
}
