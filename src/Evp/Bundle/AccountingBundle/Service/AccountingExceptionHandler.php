<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service;

use Paysera\Bundle\PartnerBundle\Exception\PartnerAccountingRequestException;
use Paysera\Component\RestClientCommon\Exception\RequestException;
use Throwable;
use Psr\Log\LoggerInterface;

class AccountingExceptionHandler
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @throws PartnerAccountingRequestException|Throwable
     */
    public function handle(Throwable $exception, ?int $operationId = null): void
    {
        if ($exception instanceof RequestException) {
            $this->logger->error(
                'Received a request exception during accounting-api request',
                [
                    'error' => $exception->getError(),
                    'error_description' => $exception->getErrorDescription(),
                    'error_properties' => $exception->getErrorProperties(),
                    'errors' => $exception->getErrors(),
                    'operation_id' => $operationId ?? '',
                ]
            );

            throw new PartnerAccountingRequestException(
                'Failed to complete accounting-api/accounting-operation-manager request',
                $exception->getCode(),
                $exception
            );
        }

        $this->logger->error(
            'Received an exception during accounting-api request',
            [
                'exception_message' => $exception->getMessage(),
                'exception_class' => get_class($exception),
                'operation_id' => $operationId ?? '',
            ]
        );

        throw $exception;
    }
}
