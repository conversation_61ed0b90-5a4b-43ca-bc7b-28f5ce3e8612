<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use DateTimeImmutable;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankChargeBundle\Entity\ChargeOperation;
use Evp\Bundle\BankTransferBundle\Repository\TransferInRepository;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Exception;
use InvalidArgumentException;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;
use Paysera\Bundle\PartnerBundle\Service\PartnerCovenanteeIdProvider;

class ChargeOperationProcessor extends BaseOperationProcessor
{
    private TemplateProvider $templateProvider;
    private PartnerClientManager $partnerClientManager;
    private AccountingExceptionHandler $accountingExceptionHandler;
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;
    private PartnerCovenanteeIdProvider $partnerCovenanteeIdProvider;
    private TransferInRepository $transferInRepository;
    private string $contisPartnerCode;

    public function __construct(
        TemplateProvider $templateProvider,
        PartnerClientManager $partnerClientManager,
        AccountingExceptionHandler $accountingExceptionHandler,
        AccountTypeAtDateProvider $accountTypeAtDateProvider,
        PartnerCovenanteeIdProvider $partnerCovenanteeIdProvider,
        TransferInRepository $transferInRepository,
        string $contisPartnerCode
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->templateProvider = $templateProvider;
        $this->partnerClientManager = $partnerClientManager;
        $this->accountTypeAtDateProvider = $accountTypeAtDateProvider;
        $this->partnerCovenanteeIdProvider = $partnerCovenanteeIdProvider;
        $this->transferInRepository = $transferInRepository;
        $this->contisPartnerCode = $contisPartnerCode;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof ChargeOperation)) {
            throw new InvalidArgumentException(sprintf(
                'Unsupported operation class (operation_class=%s) expected ChargeOperation',
                get_class($operation)
            ));
        }

        $charge = $operation->getCharge();
        $client = $charge->getAccount()->getClient();
        $amount = $operation->getChargedAmountMoney();
        $clientPartnerCode = $this->partnerClientManager->getPartnerCodeByAccount(
            $operation->getAccount(),
            $operation->getCreatedAt()
        );

        if ($amount->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }
        $relationType = RemoteOperationRequest::TYPE_CHARGE;
        $relationReference = (string)$charge->getId();

        $transfer = $this->transferInRepository->findTransferByChargeId($charge->getId());
        if ($transfer !== null) {
            $relationType = RemoteOperationRequest::TYPE_TRANSFER;
            $relationReference = (string)$transfer->getId();
        }

        try {
            $remoteOperationRequest = (new RemoteOperationRequest())
                ->setOperation($operation)
                ->setAmountMoney($amount)
                ->setOperationDate(DateTimeImmutable::createFromMutable($operation->getCreatedAt()))
                ->setPartnerCode($clientPartnerCode)
                ->setDetails($charge->getDetails())
                ->setCovenanteeId($client->getCovenanteeId())
                ->setType($relationType)
                ->setTemplateName($this->templateProvider->resolveByCharge($charge, $client))
                ->setReference($relationReference)
            ;

            $this->entityManager->persist($remoteOperationRequest);

            $accountTypeAtDate = $this->accountTypeAtDateProvider->getAccountTypeAtDate(
                $operation->getAccount(),
                $operation->getCreatedAt()
            );
            if (in_array($accountTypeAtDate, [Account::TYPE_CONTIS, Account::TYPE_CARD_V2], true)) {
                $covenanteeId = $this->getCovenanteeIdForWayOut($clientPartnerCode, $client);

                $fundsOnWayOperationRequest = (clone $remoteOperationRequest)
                    ->setTemplateName($this->templateProvider->getTemplateForContisFundsOnWayOut())
                    ->setCovenanteeId($covenanteeId)
                    ->setPartnerCode($this->contisPartnerCode)
                ;
                $this->entityManager->persist($fundsOnWayOperationRequest);

                if ($clientPartnerCode !== $this->contisPartnerCode) {
                    $bankOutOperationRequest = (clone $fundsOnWayOperationRequest)
                        ->setTemplateName($this->templateProvider->getInternalOut())
                    ;
                    $this->entityManager->persist($bankOutOperationRequest);

                    $bankInOperationRequest = (clone $fundsOnWayOperationRequest)
                        ->setTemplateName($this->templateProvider->getInternalIn())
                    ;
                    $this->entityManager->persist($bankInOperationRequest);

                    $throughPartnerOperationRequest = (clone $remoteOperationRequest)
                        ->setTemplateName($this->templateProvider->getReceivedTransferInTroughPartner(
                            $this->contisPartnerCode,
                            $amount->getCurrency()
                        ))
                    ;
                    $this->entityManager->persist($throughPartnerOperationRequest);

                    $w2pReceivedOperationRequest = (clone $remoteOperationRequest)
                        ->setTemplateName($this->templateProvider->getWebToPayReceived())
                    ;
                    $this->entityManager->persist($w2pReceivedOperationRequest);
                }
            }
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function getCovenanteeIdForWayOut(string $clientPartnerCode, Client $client): int
    {
        if ($clientPartnerCode === $this->contisPartnerCode) {
            return $client->getCovenanteeId();
        } else {
            return (int)$this->partnerCovenanteeIdProvider->getCovenanteeIdByPartnerCode($clientPartnerCode);
        }
    }
}
