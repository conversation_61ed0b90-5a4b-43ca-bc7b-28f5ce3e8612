<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\AccountingBankResolver;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class TransferOutOperationProcessor extends BaseOperationProcessor
{
    private AccountingBankResolver $accountingBankResolver;
    private ExternalPartnerOperationHandler $externalPartnerOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        AccountingBankResolver $accountingBankResolver,
        ExternalPartnerOperationHandler $externalPartnerOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->accountingBankResolver = $accountingBankResolver;
        $this->externalPartnerOperationHandler = $externalPartnerOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * @param Operation $operation
     *
     * @throws InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!$operation instanceof TransferOutOperation) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    TransferOutOperation::class
                )
            );
        }

        $transfer = $operation->getTransfer();

        if ($transfer->getAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
        } elseif ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } elseif ($transfer instanceof TransferOut) {
            $this->processTransferOut($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }
    }

    private function processTransferInternal(TransferOutOperation $operation, TransferInternal $transfer): void
    {
        try {
            $this->internalPartnerOperationHandler->handleOut(
                $operation,
                $transfer,
                $transfer->getAmountMoney()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processTransferOut(TransferOutOperation $operation, TransferOut $transfer): void
    {
        try {
            $this->externalPartnerOperationHandler->handleOut(
                $transfer,
                $transfer->getCreditAccount(),
                $operation,
                $this->accountingBankResolver->resolveBank($transfer, $operation),
                $operation->getTransfer()->getAmountMoney()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
