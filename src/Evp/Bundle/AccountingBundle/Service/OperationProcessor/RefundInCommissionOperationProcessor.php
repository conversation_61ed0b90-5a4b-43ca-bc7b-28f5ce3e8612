<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\BankRefundBundle\Entity\RefundInCommissionOperation;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class RefundInCommissionOperationProcessor extends BaseOperationProcessor
{
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    )
    {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundInCommissionOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    RefundInCommissionOperation::class
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if (!($transfer instanceof TransferInInterface)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        if ($operation->getRefundedAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        try {
            $this->internalPartnerOperationHandler->handleInCommission(
                $operation,
                $operation->getAccount(),
                $operation->getRefundedAmountMoney()->negate(),
                $transfer->getPurpose()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
