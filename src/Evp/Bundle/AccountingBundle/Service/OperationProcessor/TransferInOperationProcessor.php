<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class TransferInOperationProcessor extends BaseOperationProcessor
{
    private ExternalPartnerOperationHandler $externalPartnerOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        ExternalPartnerOperationHandler $externalPartnerOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->externalPartnerOperationHandler = $externalPartnerOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * @param Operation $operation
     *
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof TransferInOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    TransferInOperation::class
                )
            );
        }

        $transfer = $operation->getTransfer();

        if ($transfer->getAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
        } elseif ($transfer instanceof TransferIn) {
            $this->processTransferIn($operation, $transfer);
        } elseif ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }
    }

    private function processTransferIn(TransferInOperation $operation, TransferIn $transfer): void
    {
        try {
            $this->externalPartnerOperationHandler->handleIn(
                $operation->getTransfer(),
                $transfer->getDebitAccount(),
                $operation,
                $transfer->getAmountMoney()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processTransferInternal(TransferInOperation $operation, TransferInternal $transfer): void
    {
        try {
            $this->internalPartnerOperationHandler->handleIn($operation, $transfer, $transfer->getAmountMoney());
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
