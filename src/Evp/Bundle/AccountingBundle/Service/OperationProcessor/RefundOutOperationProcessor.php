<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\AccountingBankResolver;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class RefundOutOperationProcessor extends BaseOperationProcessor
{
    private ExternalPartnerOperationHandler $externalPartnerOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;
    private AccountingBankResolver $accountingBankResolver;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        ExternalPartnerOperationHandler $externalPartnerOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler,
        AccountingBankResolver $accountingBankResolver
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
        $this->externalPartnerOperationHandler = $externalPartnerOperationHandler;
        $this->accountingBankResolver = $accountingBankResolver;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundOutOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected RefundOutOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if ($operation->getRefundedAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        if ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } elseif ($transfer instanceof TransferOut) {
            $this->processTransferOut($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }
    }

    private function processTransferInternal(RefundOutOperation $operation, TransferInternal $transfer): void
    {
        try {
            $this->internalPartnerOperationHandler->handleOut(
                $operation,
                $transfer,
                $operation->getRefundedAmountMoney()->negate()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processTransferOut(RefundOutOperation $operation, TransferOut $transfer): void
    {
         try {
             $this->externalPartnerOperationHandler->handleOut(
                 $transfer,
                 $operation->getAccount(),
                 $operation,
                 $this->accountingBankResolver->resolveBank($transfer, $operation),
                 $operation->getRefundedAmountMoney()->negate()
             );
         } catch (Exception $exception) {
             $this->accountingExceptionHandler->handle($exception);
         }
    }
}
