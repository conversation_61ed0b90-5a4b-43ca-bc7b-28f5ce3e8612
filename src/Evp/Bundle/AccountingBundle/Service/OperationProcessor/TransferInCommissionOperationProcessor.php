<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class TransferInCommissionOperationProcessor extends BaseOperationProcessor
{
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof TransferInCommissionOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    TransferInCommissionOperation::class
                )
            );
        }

        $transfer = $operation->getTransfer();
        if (!($transfer instanceof TransferInInterface)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $amount = $transfer->getDebitCommissionMoney();
        if ($transfer->getDebitCommissionConvertedMoney() && !$transfer->getDebitCommissionConvertedMoney()->isZero()) {
            $amount = $transfer->getDebitCommissionConvertedMoney();
        }

        if ($amount->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        if ($operation->getAccount() !== null) {
            try {
                $this->internalPartnerOperationHandler->handleInCommission(
                    $operation,
                    $operation->getAccount(),
                    $amount,
                    $transfer->getPurpose()
                );
            } catch (Exception $exception) {
                $this->accountingExceptionHandler->handle($exception);
            }
        } else {
            $this->logger->error('TransferInCommissionOperation with account NULL. Not processing.', [
                'operation_id' => $operation->getId(),
            ]);
        }
    }
}
