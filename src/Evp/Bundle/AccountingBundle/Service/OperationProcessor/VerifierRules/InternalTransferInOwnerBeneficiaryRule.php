<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Exception\InvalidDebitAccountException;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;
use Psr\Log\LoggerInterface;

class InternalTransferInOwnerBeneficiaryRule implements OperationProcessingVerifierRule
{
    private LoggerInterface $logger;
    private PartnerAccountProvider $partnerAccountProvider;
    private string $accountNumberForCash;
    private string $accountNumberForPayseraDemo;
    private string $accountNumberForPartnerCharge;
    private array $ignoredOwnerAccounts;
    private string $accountNumberForPartnerCommission;
    private string $accountNumberForPartnerCommissionWithoutInvoice;
    private array $terminalAccounts;

    public function __construct(
        LoggerInterface $logger,
        PartnerAccountProvider $partnerAccountProvider,
        string $accountNumberForCash,
        string $accountNumberForPayseraDemo,
        string $accountNumberForPartnerCharge,
        array $ignoredOwnerAccounts,
        string $accountNumberForPartnerCommission,
        string $accountNumberForPartnerCommissionWithoutInvoice,
        array $terminalAccounts
    ) {
        $this->logger = $logger;
        $this->partnerAccountProvider = $partnerAccountProvider;
        $this->accountNumberForCash = $accountNumberForCash;
        $this->accountNumberForPayseraDemo = $accountNumberForPayseraDemo;
        $this->accountNumberForPartnerCharge = $accountNumberForPartnerCharge;
        $this->ignoredOwnerAccounts = $ignoredOwnerAccounts;
        $this->accountNumberForPartnerCommission = $accountNumberForPartnerCommission;
        $this->accountNumberForPartnerCommissionWithoutInvoice = $accountNumberForPartnerCommissionWithoutInvoice;
        $this->terminalAccounts = $terminalAccounts;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferInOperation
            && !$parameterStack->getOperation() instanceof RefundInOperation
        ) {
            return false;
        }

        return !$parameterStack->isOwnerSender() && $parameterStack->isOwnerBeneficiary();
    }

    /**
     * @throws InvalidDebitAccountException
     */
    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        if (
            in_array($parameterStack->getDebitAccount()->getNumber(), [
                $this->accountNumberForCash,
                $this->accountNumberForPayseraDemo,
            ], true)
        ) {
            return true;
        }
        if ($parameterStack->getDebitAccount()->getNumber() === $this->accountNumberForPartnerCharge) {
            return false;
        }
        if (
            in_array($parameterStack->getDebitAccount()->getNumber(),
                $this->terminalAccounts,
                true)
        ) {
            return false;
        }
        $accountNumberForCashback = $this->partnerAccountProvider
            ->getPartnerCashbackAccountNumber($parameterStack->getBeneficiaryPartnerCode())
        ;
        if (
            in_array($parameterStack->getDebitAccount()->getNumber(), [
                $accountNumberForCashback,
                $this->accountNumberForPartnerCommission,
                $this->accountNumberForPartnerCommissionWithoutInvoice,
            ], true)
        ) {
            $this->logger->error('Invalid debit account for transfer', [
                'transfer_id' => $parameterStack->getTransferId()
            ]);
            throw new InvalidDebitAccountException();
        }
        if (
            in_array(
                $parameterStack->getDebitAccount()->getNumber(),
                $this->ignoredOwnerAccounts,
                true)
        ) {
            return false;
        }

        return true;
    }
}
