<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;

class InternalTransferOutOwnerBeneficiaryRule implements OperationProcessingVerifierRule
{
    private PartnerAccountProvider $partnerAccountProvider;
    private string $accountNumberForCash;
    private string $accountNumberForPayseraDemo;
    private string $accountNumberForPartnerCharge;
    private string $accountNumberForSms;
    private string $accountNumberForPartnerCommission;
    private string $accountNumberForPartnerCommissionWithoutInvoice;
    private array $terminalAccounts;

    public function __construct(
        PartnerAccountProvider $partnerAccountProvider,
        string $accountNumberForCash,
        string $accountNumberForPayseraDemo,
        string $accountNumberForPartnerCharge,
        string $accountNumberForSms,
        string $accountNumberForPartnerCommission,
        string $accountNumberForPartnerCommissionWithoutInvoice,
        array $terminalAccounts
    ) {
        $this->partnerAccountProvider = $partnerAccountProvider;
        $this->accountNumberForCash = $accountNumberForCash;
        $this->accountNumberForPayseraDemo = $accountNumberForPayseraDemo;
        $this->accountNumberForPartnerCharge = $accountNumberForPartnerCharge;
        $this->accountNumberForSms = $accountNumberForSms;
        $this->accountNumberForPartnerCommission = $accountNumberForPartnerCommission;
        $this->accountNumberForPartnerCommissionWithoutInvoice = $accountNumberForPartnerCommissionWithoutInvoice;
        $this->terminalAccounts = $terminalAccounts;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferOutOperation
            && !$parameterStack->getOperation() instanceof RefundOutOperation
        ) {
            return false;
        }

        return !$parameterStack->isOwnerSender() && $parameterStack->isOwnerBeneficiary();
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        if (in_array($parameterStack->getDebitAccount()->getNumber(), $this->terminalAccounts, true)) {
            return true;
        }
        if (
            in_array($parameterStack->getDebitAccount()->getNumber(), [
                $this->accountNumberForCash,
                $this->accountNumberForPayseraDemo,
                $this->accountNumberForPartnerCharge,
                $this->accountNumberForSms
            ], true)
        ) {
            return true;
        }
        $accountNumberForCashback = $this->partnerAccountProvider
            ->getPartnerCashbackAccountNumber($parameterStack->getBeneficiaryPartnerCode())
        ;
        if (
            in_array($parameterStack->getDebitAccount()->getNumber(), [
                $accountNumberForCashback,
                $this->accountNumberForPartnerCommission,
                $this->accountNumberForPartnerCommissionWithoutInvoice,
            ], true)
        ) {
            return false;
        }

        return true;
    }
}
