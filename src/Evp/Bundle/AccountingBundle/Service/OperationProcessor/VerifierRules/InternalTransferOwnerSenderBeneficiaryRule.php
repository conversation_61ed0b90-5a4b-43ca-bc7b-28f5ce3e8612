<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;

class InternalTransferOwnerSenderBeneficiaryRule implements OperationProcessingVerifierRule
{
    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferInOperation
            && !$parameterStack->getOperation() instanceof RefundInOperation
        ) {
            return false;
        }

        return ($parameterStack->isOwnerSender() && $parameterStack->isOwnerBeneficiary())
            || (!$parameterStack->isOwnerSender() && !$parameterStack->isOwnerBeneficiary())
        ;
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        return true;
    }
}
