<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;

interface OperationProcessingVerifierRule
{
    public function match(OperationProcessingMatchParameterStack $parameterStack): bool;
    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool;
}
