<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\CurrencyBundle\Service\CurrencyTypeHelper;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;

class InternalTransferMetalsRule implements OperationProcessingVerifierRule
{
    private CurrencyTypeHelper $currencyTypeHelper;

    public function __construct(CurrencyTypeHelper $currencyTypeHelper)
    {
        $this->currencyTypeHelper = $currencyTypeHelper;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferInOperation
            && !$parameterStack->getOperation() instanceof RefundInOperation
            && !$parameterStack->getOperation() instanceof TransferOutOperation
            && !$parameterStack->getOperation() instanceof RefundOutOperation
        ) {
            return false;
        }

        return $this->currencyTypeHelper->isPreciousMetal(
            $parameterStack->getTransfer()->getAmountCurrency()
        );
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        return true;
    }
}
