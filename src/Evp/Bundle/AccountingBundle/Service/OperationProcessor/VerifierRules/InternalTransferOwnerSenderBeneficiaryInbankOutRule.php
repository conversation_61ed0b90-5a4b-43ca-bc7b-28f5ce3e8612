<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;

class InternalTransferOwnerSenderBeneficiaryInbankOutRule implements OperationProcessingVerifierRule
{
    private array $inbankLeasingTemplateAccounts;
    private string $accountNumberForPartnerCharge;

    public function __construct(array $inbankLeasingTemplateAccounts, string $accountNumberForPartnerCharge)
    {
        $this->inbankLeasingTemplateAccounts = $inbankLeasingTemplateAccounts;
        $this->accountNumberForPartnerCharge = $accountNumberForPartnerCharge;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferOutOperation
            && !$parameterStack->getOperation() instanceof RefundOutOperation
        ) {
            return false;
        }

        return ($parameterStack->isOwnerSender() && $parameterStack->isOwnerBeneficiary())
            || (!$parameterStack->isOwnerSender() && !$parameterStack->isOwnerBeneficiary())
        ;
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        if (
            $parameterStack->getDebitAccount()->getNumber() === $this->accountNumberForPartnerCharge
            && in_array($parameterStack->getCreditAccount()->getNumber(), $this->inbankLeasingTemplateAccounts)
        ) {
            return false;
        }

        return true;
    }
}
