<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;

class InternalTransferInOwnerSenderRule implements OperationProcessingVerifierRule
{
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;
    private PartnerAccountProvider $partnerAccountProvider;
    private string $accountNumberForCash;
    private array $terminalAccounts;
    private string $evpSalaryAccount;
    private string $accountNumberForSms;
    private array $ignoredOwnerAccounts;
    private string $evpMainAccount;
    private string $nTechnologijosAccount;

    public function __construct(
        AccountTypeAtDateProvider $accountTypeAtDateProvider,
        PartnerAccountProvider $partnerAccountProvider,
        string $accountNumberForCash,
        array $terminalAccounts,
        string $evpSalaryAccount,
        string $accountNumberForSms,
        array $ignoredOwnerAccounts,
        string $evpMainAccount,
        string $nTechnologijosAccount
    ) {
        $this->accountTypeAtDateProvider = $accountTypeAtDateProvider;
        $this->partnerAccountProvider = $partnerAccountProvider;
        $this->accountNumberForCash = $accountNumberForCash;
        $this->terminalAccounts = $terminalAccounts;
        $this->evpSalaryAccount = $evpSalaryAccount;
        $this->accountNumberForSms = $accountNumberForSms;
        $this->ignoredOwnerAccounts = $ignoredOwnerAccounts;
        $this->evpMainAccount = $evpMainAccount;
        $this->nTechnologijosAccount = $nTechnologijosAccount;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferInOperation
            && !$parameterStack->getOperation() instanceof RefundInOperation
        ) {
            return false;
        }

        return $parameterStack->isOwnerSender() && !$parameterStack->isOwnerBeneficiary();
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        if (
            in_array($parameterStack->getCreditAccount()->getNumber(), [
                $this->accountNumberForCash,
                $this->evpSalaryAccount,
                $this->accountNumberForSms
            ], true)
        ) {
            return true;
        }
        $accountNumberForCashback = $this->partnerAccountProvider
            ->getPartnerCashbackAccountNumber($parameterStack->getPayerPartnerCode())
        ;
        if ($parameterStack->getCreditAccount()->getNumber() === $accountNumberForCashback) {
            return true;
        }
        if (in_array($parameterStack->getCreditAccount()->getNumber(), $this->terminalAccounts, true)) {
            return true;
        }
        if ($this->shouldProcess(
            $parameterStack->getCreditAccount(),
            $parameterStack->getDebitAccount(),
            $parameterStack->getDate()
        )) {
            return true;
        }
        if (
            $parameterStack->getCreditAccount()->getNumber() === $this->evpMainAccount
            && $parameterStack->getDebitAccount()->getNumber() === $this->nTechnologijosAccount
        ) {
            return true;
        }
        if (in_array($parameterStack->getCreditAccount()->getNumber(), $this->ignoredOwnerAccounts, true)) {
            return false;
        }

        return true;
    }

    private function shouldProcess(Account $creditAccount, Account $debitAccount, DateTime $operationDate): bool
    {
        $creditAccountType = $this->accountTypeAtDateProvider->getAccountTypeAtDate($creditAccount, $operationDate);
        $debitAccountType = $this->accountTypeAtDateProvider->getAccountTypeAtDate($debitAccount, $operationDate);

        return !(($creditAccountType === $debitAccountType || $creditAccountType === Account::TYPE_CONTIS));
    }
}
