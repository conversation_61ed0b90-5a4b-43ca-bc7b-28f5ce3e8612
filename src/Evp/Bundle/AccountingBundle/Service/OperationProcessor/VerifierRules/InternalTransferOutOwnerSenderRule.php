<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerAccountProvider;

class InternalTransferOutOwnerSenderRule implements OperationProcessingVerifierRule
{
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;
    private PartnerAccountProvider $partnerAccountProvider;
    private array $terminalAccounts;
    private string $evpSalaryAccount;
    private string $accountNumberForSms;
    private array $ignoredOwnerAccounts;
    private string $accountNumberForCash;
    private string $accountNumberForPartnerCommission;
    private string $accountNumberForPartnerCommissionWithoutInvoice;

    public function __construct(
        AccountTypeAtDateProvider $accountTypeAtDateProvider,
        PartnerAccountProvider $partnerAccountProvider,
        array $terminalAccounts,
        string $evpSalaryAccount,
        string $accountNumberForSms,
        array $ignoredOwnerAccounts,
        string $accountNumberForCash,
        string $accountNumberForPartnerCommission,
        string $accountNumberForPartnerCommissionWithoutInvoice
    ) {
        $this->accountTypeAtDateProvider = $accountTypeAtDateProvider;
        $this->partnerAccountProvider = $partnerAccountProvider;
        $this->terminalAccounts = $terminalAccounts;
        $this->evpSalaryAccount = $evpSalaryAccount;
        $this->accountNumberForSms = $accountNumberForSms;
        $this->ignoredOwnerAccounts = $ignoredOwnerAccounts;
        $this->accountNumberForCash = $accountNumberForCash;
        $this->accountNumberForPartnerCommission = $accountNumberForPartnerCommission;
        $this->accountNumberForPartnerCommissionWithoutInvoice = $accountNumberForPartnerCommissionWithoutInvoice;
    }

    public function match(OperationProcessingMatchParameterStack $parameterStack): bool
    {
        if (
            !$parameterStack->getOperation() instanceof TransferOutOperation
            && !$parameterStack->getOperation() instanceof RefundOutOperation
        ) {
            return false;
        }

        return $parameterStack->isOwnerSender() && !$parameterStack->isOwnerBeneficiary();
    }

    public function verify(OperationProcessingVerifierParameterStack $parameterStack): bool
    {
        if ($parameterStack->getCreditAccount()->getNumber() === $this->accountNumberForSms) {
            return false;
        }
        if ($parameterStack->getCreditAccount()->getNumber() === $this->accountNumberForCash) {
            return true;
        }
        if (in_array($parameterStack->getCreditAccount()->getNumber(), $this->terminalAccounts, true)) {
            return false;
        }
        $accountNumberForCashback = $this->partnerAccountProvider
            ->getPartnerCashbackAccountNumber($parameterStack->getPayerPartnerCode())
        ;
        if ($parameterStack->getCreditAccount()->getNumber() === $accountNumberForCashback) {
            return false;
        }
        if (in_array($parameterStack->getCreditAccount()->getNumber(), [
            $this->accountNumberForPartnerCommission,
            $this->accountNumberForPartnerCommissionWithoutInvoice
        ])) {
            return true;
        }
        if ($parameterStack->getCreditAccount()->getNumber() === $this->evpSalaryAccount) {
            return false;
        }
        if ($this->shouldProcess(
            $parameterStack->getCreditAccount(),
            $parameterStack->getDebitAccount(),
            $parameterStack->getDate())
        ) {
            return true;
        }
        if (in_array($parameterStack->getCreditAccount()->getNumber(), $this->ignoredOwnerAccounts, true)) {
            return false;
        }

        return true;
    }

    private function shouldProcess(Account $creditAccount, Account $debitAccount, DateTime $operationDate): bool
    {
        $creditAccountType = $this->accountTypeAtDateProvider->getAccountTypeAtDate($creditAccount, $operationDate);
        $debitAccountType = $this->accountTypeAtDateProvider->getAccountTypeAtDate($debitAccount, $operationDate);

        return ($creditAccountType === $debitAccountType || $creditAccountType === Account::TYPE_CONTIS);
    }
}
