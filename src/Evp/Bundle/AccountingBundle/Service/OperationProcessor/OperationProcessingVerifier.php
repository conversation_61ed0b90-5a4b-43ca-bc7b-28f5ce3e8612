<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use DateTimeImmutable;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationDateService;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationProvider;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferFromOperationProvider;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisBundle\Entity\ContisOperation;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingMatchParameterStack;
use Evp\Bundle\AccountingBundle\DTO\OperationProcessingVerifierParameterStack;
use Paysera\Bundle\PartnerBundle\Service\PartnerCovenanteeIdProvider;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\VerifierRules\OperationProcessingVerifierRule;
use Paysera\Client\CheckoutClient\CheckoutClient;
use Paysera\Client\CheckoutClient\Entity\PayoutFilter;

class OperationProcessingVerifier
{
    private CheckoutClient $checkoutClient;
    private AccountOwnerResolver $accountOwnerResolver;
    private PartnerCovenanteeIdProvider $partnerCovenanteeIdProvider;
    private PartnerClientManager $partnerClientManager;
    private TransferFromOperationProvider $transferFromOperationProvider;
    private OperationDateService $operationDateService;
    private OperationProvider $operationProvider;
    /** @var OperationProcessingVerifierRule[] */
    private array $verifierRules;
    private array $operationsToSkip;
    private array $skipAccountList;
    private array $contisFillAccountIbans;
    private DateTimeImmutable $newAccountingStartDate;

    public function __construct(
        CheckoutClient $checkoutClient,
        AccountOwnerResolver $accountOwnerResolver,
        PartnerCovenanteeIdProvider $partnerCovenanteeIdProvider,
        PartnerClientManager $partnerClientManager,
        TransferFromOperationProvider $transferFromOperationProvider,
        OperationDateService $operationDateService,
        OperationProvider $operationProvider,
        array $operationsToSkip,
        array $skipAccountList,
        array $contisFillAccountIbans,
        DateTimeImmutable $newAccountingStartDate
    ) {
        $this->checkoutClient = $checkoutClient;
        $this->accountOwnerResolver = $accountOwnerResolver;
        $this->partnerCovenanteeIdProvider = $partnerCovenanteeIdProvider;
        $this->partnerClientManager = $partnerClientManager;
        $this->transferFromOperationProvider = $transferFromOperationProvider;
        $this->operationDateService = $operationDateService;
        $this->operationProvider = $operationProvider;
        $this->operationsToSkip = array_map('intval', $operationsToSkip);
        $this->skipAccountList = $skipAccountList;
        $this->contisFillAccountIbans = $contisFillAccountIbans;
        $this->newAccountingStartDate = $newAccountingStartDate;
    }

    public function shouldProcess(Operation $operation): bool
    {
        if ($operation instanceof ContisOperation) {
            return false;
        }

        if (in_array($operation->getId(), $this->operationsToSkip, true)) {
            return false;
        }

        $transfer = $this->transferFromOperationProvider->getTransferFromOperation($operation);
        if ($transfer !== null) {
            return $this->verifyTransferOperation($transfer, $operation);
        }

        return true;
    }

    private function verifyTransferOperation(Transfer $transfer, Operation $operation): bool
    {
        if (!$this->verifyTransferOperationDate($transfer, $operation)) {
            return false;
        }

        if ($this->verifyCheckoutTransfer($transfer)) {
            return false;
        }

        if ($transfer instanceof TransferInternal) {
            return $this->verifyInternalTransferOperation($transfer, $operation);
        }

        if (!$transfer instanceof TransferIn && !$transfer instanceof TransferOut) {
            return true;
        }

        if ($transfer instanceof TransferIn) {
            $debitAccount = $transfer->getDebitAccount();
            if (in_array($debitAccount->getNumber(), $this->skipAccountList, true)) {
                return false;
            }
        } else {
            $isTransferToContisFillAccount = $transfer->getBeneficiary() instanceof PartyIban
                && in_array($transfer->getBeneficiary()->getIban(), $this->contisFillAccountIbans, true);

            if (
                !($operation instanceof TransferOutOperation && $isTransferToContisFillAccount)
                && in_array($transfer->getCreditAccount()->getNumber(), $this->skipAccountList, true)) {
                return false;
            }
        }

        return true;
    }

    private function verifyInternalTransferOperation(TransferInternal $transfer, Operation $operation): bool
    {
        $payerClient = $this->accountOwnerResolver->getRealAccountOwner($transfer->getCreditAccount());
        $payerPartnerCode = $this->partnerClientManager->getPartnerCodeByAccount(
            $transfer->getCreditAccount(),
            $operation->getCreatedAt()
        );

        $beneficiaryClient = $this->accountOwnerResolver->getRealAccountOwner($transfer->getDebitAccount());
        $beneficiaryPartnerCode = $this->partnerClientManager->getPartnerCodeByAccount(
            $transfer->getDebitAccount(),
            $operation->getCreatedAt()
        );

        $isOwnerSender = (string)$payerClient->getCovenanteeId()
            === $this->partnerCovenanteeIdProvider->getCovenanteeIdByPartnerCode($payerPartnerCode)
        ;

        $isOwnerBeneficiary = (string)$beneficiaryClient->getCovenanteeId()
            === $this->partnerCovenanteeIdProvider->getCovenanteeIdByPartnerCode($beneficiaryPartnerCode)
        ;

        $parameterStackMatch = new OperationProcessingMatchParameterStack(
            $transfer,
            $operation,
            $isOwnerSender,
            $isOwnerBeneficiary
        );

        $parameterStackVerifier = new OperationProcessingVerifierParameterStack(
            $transfer->getCreditAccount(),
            $transfer->getDebitAccount(),
            $transfer->getCreatedAt(),
            $payerPartnerCode,
            $beneficiaryPartnerCode,
            $transfer->getId(),
        );

        foreach ($this->verifierRules as $rule) {
            if ($rule->match($parameterStackMatch)) {
                return $rule->verify($parameterStackVerifier);
            }
        }

        return true;
    }

    private function verifyTransferOperationDate(Transfer $transfer, Operation $operation): bool
    {
        if ($transfer instanceof TransferOut) {
            $operationDate = $this->operationDateService->getTransferOutOperationDatetime(
                $transfer,
                $operation,
            );
        } elseif ($transfer instanceof TransferIn) {
            $transactionInDto = $this->operationProvider->getTransactionIn($transfer);
            $operationDate = $this->operationDateService->getTransferInOperationDatetime(
                $transfer,
                $operation,
                $transactionInDto
            );
        } else {
            $operationDate = $operation->getCreatedAt();
        }

        return $operationDate >= $this->newAccountingStartDate;
    }

    private function verifyCheckoutTransfer(Transfer $transfer): bool
    {
        $transferDetails = $transfer->getDetails();
        if (method_exists($transfer, 'getBank') || empty($transferDetails)) {
            return false;
        }

        $matches = $requestIds = [];
        $words = preg_split('/[^\w]/', $transferDetails);
        foreach ($words as $word) {
            if (preg_match('/^R\w{1}?\s*(\d{8,9})(?!\d)/', $word, $matches)) {
                $requestIds[] = $matches[0];
            }
        }

        if (count($requestIds) > 0) {
            foreach ($requestIds as $requestId) {
                $payoutFilter = new PayoutFilter();
                $payoutFilter->setData(['payment_request_numbers' => [$requestId]]);
                $payoutCollection = $this->checkoutClient->getPayouts($payoutFilter);
                if (count($payoutCollection->getItems()) > 0) {
                    return true;
                }
            }
        }

        return false;
    }

    public function addRule(OperationProcessingVerifierRule $rule): void
    {
        $this->verifierRules[] = $rule;
    }
}
