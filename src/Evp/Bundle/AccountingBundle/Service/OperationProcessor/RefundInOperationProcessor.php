<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class RefundInOperationProcessor extends BaseOperationProcessor
{
    private ExternalPartnerOperationHandler $externalPartnerOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;
    private array $skipAccountList;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        ExternalPartnerOperationHandler $externalPartnerOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler,
        array $skipAccountList
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->externalPartnerOperationHandler = $externalPartnerOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
        $this->skipAccountList = $skipAccountList;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundInOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    RefundInOperation::class
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if ($operation->getRefundedAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        if ($transfer instanceof TransferIn) {
            $this->processTransferIn($operation, $transfer);
        } elseif ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }
    }

    private function processTransferIn(RefundInOperation $operation, TransferIn $transfer): void
    {
        if (
            $transfer->getStatus() !== Transfer::STATUS_DONE
            || in_array($transfer->getDebitAccount()->getNumber(), $this->skipAccountList, true)
        ) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        try {
            $this->externalPartnerOperationHandler->handleIn(
                $operation->getRefund()->getTransfer(),
                $operation->getAccount(),
                $operation,
                $operation->getRefundedAmountMoney()->negate()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processTransferInternal(RefundInOperation $operation, TransferInternal $transfer): void
    {
        try {
            $this->internalPartnerOperationHandler->handleIn(
                $operation,
                $transfer,
                $operation->getRefundedAmountMoney()->negate()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
