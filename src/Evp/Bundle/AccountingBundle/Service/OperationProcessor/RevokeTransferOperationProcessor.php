<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutCommissionOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferInOperation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\BaseTransferOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class RevokeTransferOperationProcessor extends BaseOperationProcessor
{
    protected InternalPartnerOperationHandler $internalPartnerOperationHandler;
    protected AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        InternalPartnerOperationHandler $internalPartnerOperationHandler,
        AccountingExceptionHandler $accountingExceptionHandler
    ) {
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
        $this->accountingExceptionHandler = $accountingExceptionHandler;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!$operation instanceof BaseTransferOperation) {
            throw new InvalidArgumentException(
                sprintf('Unsupported operation class (operation_class=%s)', get_class($operation))
            );
        }

        $transfer = $operation->getTransfer();
        if (!($transfer instanceof TransferOutInterface)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        if ($transfer->getAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        $this->processTransfer($operation, $transfer);
    }

    protected function processTransfer(Operation $operation, TransferOutInterface $transfer): void
    {
        try {
            if ($operation instanceof RevokeTransferInOperation) {
                $this->internalPartnerOperationHandler->handleIn(
                    $operation,
                    $transfer,
                    $transfer->getAmountMoney()->negate()
                );
            } elseif ($operation instanceof RevokeTransferOutOperation) {
                $this->internalPartnerOperationHandler->handleOut(
                    $operation,
                    $transfer,
                    $transfer->getAmountMoney()->negate(),
                );
            } elseif ($operation instanceof RevokeTransferOutCommissionOperation) {
                $this->internalPartnerOperationHandler->handleOutCommission(
                    $operation,
                    $transfer->getCreditAccount(),
                    $transfer->getCreditCommissionMoney()->negate()
                );
            } else {
                throw new InvalidArgumentException(
                    sprintf('Unsupported operation class (operation_class=%s)', get_class($operation))
                );
            }
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
