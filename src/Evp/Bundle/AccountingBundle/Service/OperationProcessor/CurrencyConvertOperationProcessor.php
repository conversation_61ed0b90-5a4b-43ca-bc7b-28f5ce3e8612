<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertFiatToMetalOperationHandler;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertMetalToFiatOperationHandler;
use Evp\Bundle\AccountingBundle\Service\CurrencyConvertBetweenFiatOperationHandler;
use Evp\Bundle\BankAccountBundle\Entity\Operation\CurrencyConvertOperation;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisAccountingBundle\Service\AccountingProcessor as ContisAccountingProcessor;
use Evp\Bundle\CurrencyBundle\Service\CurrencyTypeHelper;
use Exception;
use InvalidArgumentException;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class CurrencyConvertOperationProcessor extends BaseOperationProcessor
{
    private CurrencyTypeHelper $currencyTypeHelper;
    private AccountOwnerResolver $accountOwnerResolver;
    private AccountingExceptionHandler $accountingExceptionHandler;
    private CurrencyConvertBetweenFiatOperationHandler $currencyConvertBetweenFiatOperationHandler;
    private CurrencyConvertMetalToFiatOperationHandler $currencyConvertMetalToFiatOperationHandler;
    private CurrencyConvertFiatToMetalOperationHandler $currencyConvertFiatToMetalOperationHandler;
    private PartnerClientManager $partnerClientManager;
    private ContisAccountingProcessor $contisAccountingProcessor;
    private TemplateProvider $templateProvider;
    private array $defaultConversionLogicPartnersList;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        CurrencyTypeHelper $currencyTypeHelper,
        AccountOwnerResolver $accountOwnerResolver,
        CurrencyConvertBetweenFiatOperationHandler $currencyConvertBetweenFiatOperationHandler,
        CurrencyConvertMetalToFiatOperationHandler $currencyConvertMetalToFiatOperationHandler,
        CurrencyConvertFiatToMetalOperationHandler $currencyConvertFiatToMetalOperationHandler,
        PartnerClientManager $partnerClientManager,
        ContisAccountingProcessor $contisAccountingProcessor,
        TemplateProvider $templateProvider,
        array $defaultConversionLogicPartnersList
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->currencyTypeHelper = $currencyTypeHelper;
        $this->currencyConvertBetweenFiatOperationHandler = $currencyConvertBetweenFiatOperationHandler;
        $this->accountOwnerResolver = $accountOwnerResolver;
        $this->currencyConvertMetalToFiatOperationHandler = $currencyConvertMetalToFiatOperationHandler;
        $this->currencyConvertFiatToMetalOperationHandler = $currencyConvertFiatToMetalOperationHandler;
        $this->partnerClientManager = $partnerClientManager;
        $this->contisAccountingProcessor = $contisAccountingProcessor;
        $this->templateProvider = $templateProvider;
        $this->defaultConversionLogicPartnersList = $defaultConversionLogicPartnersList;
    }

    /**
     * @param Operation $operation
     * @return void
     */
    public function process(Operation $operation)
    {
        if (!$operation instanceof CurrencyConvertOperation) {
            throw new InvalidArgumentException('Wrong operation type given to processor');
        }

        if ($operation->getSrcAmountMoney()->isZero() || $operation->getDstAmountMoney()->isZero()) {
            $this->savePartnerOperationAsSkipped($operation);
            return;
        }

        $metalSold = $this->currencyTypeHelper->isPreciousMetal($operation->getSrcAmountCurrency());
        $metalBought = $this->currencyTypeHelper->isPreciousMetal($operation->getDstAmountCurrency());
        $client = $this->accountOwnerResolver->getRealAccountOwner($operation->getAccount());

        $this->contisAccountingProcessor->createContisFundsOnWayOperationIfNeeded(
            $operation->getAccount(),
            $operation,
            CurrencyConvertOperation::CONTIS_TITLE,
            $this->templateProvider->getTemplateForContisFundsOnWayIn(),
            $operation->getCreatedAt(),
            $operation->getDstAmountMoney(),
            (string)$operation->getId(),
            null,
            RemoteOperationRequest::TYPE_OPERATION
        );

        $this->contisAccountingProcessor->createContisFundsOnWayOperationIfNeeded(
            $operation->getAccount(),
            $operation,
            CurrencyConvertOperation::CONTIS_TITLE,
            $this->templateProvider->getTemplateForContisFundsOnWayOut(),
            $operation->getCreatedAt(),
            $operation->getSrcAmountMoney(),
            (string)$operation->getId(),
            null,
            RemoteOperationRequest::TYPE_OPERATION
        );
        $shouldUseMetalsFlow = $this->checkIfPartnerShouldUseCurrencyConvertFlow($operation);
        if ($metalSold && $metalBought && $shouldUseMetalsFlow) {
            $this->logger->error('Not creating operations for conversion between metals. Not implemented');
        } elseif ($metalBought && $shouldUseMetalsFlow) {
            $this->processFiatToMetal($operation, $client);
        } elseif ($metalSold && $shouldUseMetalsFlow) {
            $this->processMetalToFiat($operation, $client);
        } else {
            $this->processBetweenFiat($operation, $client);
        }
    }

    private function processFiatToMetal(CurrencyConvertOperation $operation, Client $client): void
    {
        try {
            $this->currencyConvertFiatToMetalOperationHandler->handle(
                $operation,
                $client
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processMetalToFiat(CurrencyConvertOperation $operation, Client $client): void
    {
        try {
            $this->currencyConvertMetalToFiatOperationHandler->handle(
                $operation,
                $client
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function processBetweenFiat(CurrencyConvertOperation $operation, Client $client): void
    {
        try {
            $this->currencyConvertBetweenFiatOperationHandler->handle(
                $operation,
                $client
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    private function checkIfPartnerShouldUseCurrencyConvertFlow(Operation $operation): bool
    {
        $clientsPartnerCode = $this->partnerClientManager->getPartnerCodeByAccount(
            $operation->getAccount(),
            $operation->getCreatedAt(),
        );

        return !in_array($clientsPartnerCode, $this->defaultConversionLogicPartnersList, true);
    }
}
