<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Service;

use Paysera\AccountingClient\Entity\Operation;
use Paysera\AccountingClient\Entity\OperationFull;
use Paysera\AccountingOperationManagerClient\AccountingOperationManagerClient;
use Paysera\AccountingOperationManagerClient\Entity\OperationCreateCollection;
use Paysera\AccountingOperationManagerClient\Entity\OperationCreate;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Paysera\AccountingClient\AccountingClient;
use Paysera\Bundle\PartnerBundle\Service\OperationCreateTransformer;
use Paysera\Bundle\PartnerBundle\Service\OperationEntityComparator;
use Paysera\Component\RestClientCommon\Exception\RequestException;
use Paysera\AccountingOperationManagerClient\Entity\OperationCollection;
use Psr\Log\LoggerInterface;

class RemoteOperationRequestHandler
{
    private OperationCreateTransformer $operationCreateTransformer;
    private OperationEntityComparator $operationEntityComparator;
    private AccountingClient $accountingClient;
    private AccountingOperationManagerClient $accountingOperationManagerClient;
    private LoggerInterface $logger;
    private int $maxRetryCount;

    public function __construct(
        OperationCreateTransformer $operationCreateTransformer,
        OperationEntityComparator $operationEntityComparator,
        AccountingClient $accountingClient,
        AccountingOperationManagerClient $accountingOperationManagerClient,
        LoggerInterface $logger,
        int $maxRetryCount
    ) {
        $this->operationCreateTransformer = $operationCreateTransformer;
        $this->operationEntityComparator = $operationEntityComparator;
        $this->accountingClient = $accountingClient;
        $this->accountingOperationManagerClient = $accountingOperationManagerClient;
        $this->logger = $logger;
        $this->maxRetryCount = $maxRetryCount;
    }

    public function createRemoteOperation(RemoteOperationRequest $operationRequest): void
    {
        $operationCreate = $this->operationCreateTransformer
            ->transformFromRemoteOperationRequest($operationRequest)
        ;

        $partnerAccountingOperationCollection = $this->createOperation($operationRequest, $operationCreate);

        $operationRequest->setAccountingId($partnerAccountingOperationCollection->getItems()[0]->getId());
    }

    public function deleteRemoteOperation(RemoteOperationRequest $operationRequest): void
    {
        try {
            $this->accountingClient->deleteOperation(
                $operationRequest->getPartnerCode(),
                $operationRequest->getAccountingId()
            );
        } catch (RequestException $requestException) {
            if ($requestException->getError() !== 'not_found') {
                throw $requestException;
            }
        }

        $operationRequest->removeAccountingId();
    }

    private function createOperation(
        RemoteOperationRequest $operationRequest,
        OperationCreate $operationCreate,
        int $retryCount = 1
    ): OperationCollection {
        try {
            $operationCollection = $this->accountingOperationManagerClient->create(
                $operationRequest->getPartnerCode(),
                (new OperationCreateCollection())->setItems([$operationCreate])
            );
        } catch (RequestException $requestException) {
            $errorDescription = $requestException->getErrorDescription();
            if ($errorDescription === 'Reference already exists') {
                $operationByReference = $this->getOperationByReferenceAndCheck(
                    $operationRequest->getPartnerCode(),
                    $operationCreate
                );

                if ($operationByReference !== null) {
                    return (new OperationCollection())->setItems([
                        new \Paysera\AccountingOperationManagerClient\Entity\Operation($operationByReference->getData())
                    ]);
                }
            }

            if ($errorDescription !== 'Failed to acquire lock, wait time expired') {
                throw $requestException;
            }

            $this->logger->info(
                'RemoteOperationRequestHandler: Retry happened',
                ['remote_operation_request_id' => $operationRequest->getId()]
            );

            if ($retryCount >= $this->maxRetryCount) {
                throw $requestException;
            }

            return $this->createOperation($operationRequest, $operationCreate, ++$retryCount);
        }

        return $operationCollection;
    }

    private function getOperationByReference(string $partner, string $reference): ?Operation
    {
        try {
            return $this->accountingClient->getOperationByReference($partner, $reference);
        } catch (RequestException $requestException) {
            if ($requestException->getError() !== 'not_found') {
                throw $requestException;
            }
        }

        return null;
    }

    private function getOperationFull(string $partner, string $id): ?OperationFull
    {
        try {
            return $this->accountingClient->getOperationFull($partner, $id);
        } catch (RequestException $requestException) {
            if ($requestException->getError() !== 'not_found') {
                throw $requestException;
            }
        }

        return null;
    }

    private function getOperationByReferenceAndCheck(string $partner, OperationCreate $operationCreate): ?Operation
    {
        $operationByReference = $this->getOperationByReference($partner, $operationCreate->getReference());
        if ($operationByReference === null) {
            return null;
        }

        $operationFull = $this->getOperationFull($partner, $operationByReference->getId());
        if ($operationFull === null) {
            return null;
        }

        if (!$this->operationEntityComparator->areEqual($operationCreate, $operationFull)) {
            return null;
        }

        return $operationByReference;
    }
}
