<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\Entity;

use DateTimeImmutable;

class FailedOperation
{
    const STATUS_PENDING = 'pending';
    const STATUS_PUBLISHED = 'published';

    private ?int $id;
    private DateTimeImmutable $createdAt;
    private ?int $reprocessingRequestId;
    private ?int $operationId;
    private ?string $error;
    private string $status;

    public function __construct()
    {
        $this->id = null;
        $this->createdAt = new DateTimeImmutable();
        $this->reprocessingRequestId = null;
        $this->operationId = null;
        $this->error = null;
        $this->status = self::STATUS_PENDING;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getReprocessingRequestId(): ?int
    {
        return $this->reprocessingRequestId;
    }

    public function setReprocessingRequestId(int $reprocessingRequestId): self
    {
        $this->reprocessingRequestId = $reprocessingRequestId;

        return $this;
    }

    public function getOperationId(): ?int
    {
        return $this->operationId;
    }

    public function setOperationId(int $operationId): self
    {
        $this->operationId = $operationId;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }
}
