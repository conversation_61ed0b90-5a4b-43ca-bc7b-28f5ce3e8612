<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\DTO;

use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;

class OperationProcessingMatchParameterStack
{
    private Transfer $transfer;
    private Operation $operation;
    private bool $isOwnerSender;
    private bool $isOwnerBeneficiary;

    public function __construct(
        Transfer $transfer,
        Operation $operation,
        bool $isOwnerSender,
        bool $isOwnerBeneficiary
    ) {
        $this->transfer = $transfer;
        $this->operation = $operation;
        $this->isOwnerSender = $isOwnerSender;
        $this->isOwnerBeneficiary = $isOwnerBeneficiary;
    }

    public function getTransfer(): Transfer
    {
        return $this->transfer;
    }

    public function getOperation(): Operation
    {
        return $this->operation;
    }

    public function isOwnerSender(): bool
    {
        return $this->isOwnerSender;
    }

    public function isOwnerBeneficiary(): bool
    {
        return $this->isOwnerBeneficiary;
    }
}
