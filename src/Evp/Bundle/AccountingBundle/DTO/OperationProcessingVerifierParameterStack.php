<?php

declare(strict_types=1);

namespace Evp\Bundle\AccountingBundle\DTO;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;

class OperationProcessingVerifierParameterStack
{
    private Account $creditAccount;
    private Account $debitAccount;
    private DateTime $date;
    private string $payerPartnerCode;
    private string $beneficiaryPartnerCode;
    private int $transferId;

    public function __construct(
        Account $creditAccount,
        Account $debitAccount,
        DateTime $date,
        string $payerPartnerCode,
        string $beneficiaryPartnerCode,
        int $transferId
    ) {
        $this->creditAccount = $creditAccount;
        $this->debitAccount = $debitAccount;
        $this->date = $date;
        $this->payerPartnerCode = $payerPartnerCode;
        $this->beneficiaryPartnerCode = $beneficiaryPartnerCode;
        $this->transferId = $transferId;
    }

    public function getCreditAccount(): Account
    {
        return $this->creditAccount;
    }

    public function getDebitAccount(): Account
    {
        return $this->debitAccount;
    }

    public function getDate(): DateTime
    {
        return $this->date;
    }

    public function getPayerPartnerCode(): string
    {
        return $this->payerPartnerCode;
    }

    public function getBeneficiaryPartnerCode(): string
    {
        return $this->beneficiaryPartnerCode;
    }

    public function getTransferId(): int
    {
        return $this->transferId;
    }
}
