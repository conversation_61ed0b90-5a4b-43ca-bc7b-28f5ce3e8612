<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Controller;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Admin\ClientSanctionsScreeningAdmin;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResultItem;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreeningComment;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\ReviewActionWorkflow\ReviewActionTransitions;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\Workflows;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningItemsWereMatchedEvent;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningWhitelistProfilesCleanUpEvent;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningWhitelistProfilesEvent;
use Evp\Bundle\BlacklistBundle\Events;
use Evp\Bundle\BlacklistBundle\Form\ClientSanctionsScreeningReviewActionType;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository;
use Exception;
use LogicException;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Workflow\Registry;
use Symfony\Component\Workflow\Workflow;
use Symfony\Component\HttpFoundation\Request;

class ClientSanctionsScreeningAdminController extends CRUDController
{
    private Registry $workflowRegistry;
    private EventDispatcherInterface $eventDispatcher;
    private ?Workflow $workflow;
    private ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository;
    private EntityManagerInterface $entityManager;
    private bool $screeningIsCompleted = false;

    public function __construct(
        Registry $workflowRegistry,
        EventDispatcherInterface $eventDispatcher,
        ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository,
        EntityManagerInterface $entityManager
    ) {
        $this->workflowRegistry = $workflowRegistry;
        $this->eventDispatcher = $eventDispatcher;
        $this->workflow = null;
        $this->clientBlacklistCheckResultItemRepository = $clientBlacklistCheckResultItemRepository;
        $this->entityManager = $entityManager;
    }

    protected function preShow(Request $request, $object)
    {
        $defaultReferer = $this->container->get('router')->generate('admin_evp_blacklist_clientsanctionsscreening_list');
        $showPageReferer = $this->container->get('router')->generate('admin_evp_blacklist_clientsanctionsscreening_show', ['id' => $object->getId()]);
        $referer = $request->headers->get('referer');

        if ($referer !== null) {
            if (stripos($referer, $defaultReferer) !== false) {
                $this->setReferer($referer);
            } else if (stripos($referer, $showPageReferer) === false) {
                $this->setReferer($defaultReferer);
            }
        } else {
            $this->setReferer($defaultReferer);
        }
    }

    protected function preList(Request $request)
    {
        $this->setReferer($request->getRequestUri());
    }

    private function setReferer($referer): void
    {
        if ($this->admin instanceof ClientSanctionsScreeningAdmin) {
            $this->admin->setReferer($referer);
        }
    }

    protected function initWorkflow(ClientSanctionsScreening $object): void
    {
        $this->workflow = $this->workflowRegistry->get($object, Workflows::REVIEW_ACTION_WORKFLOW_NAME);
    }

    /** @throws NotFoundHttpException */
    public function startReviewAction(int $id): Response
    {
        $clientSanctionsScreening = $this->getClientSanctionsScreening($id);
        $this->initWorkflow($clientSanctionsScreening);

        if ($this->workflow->can($clientSanctionsScreening, ReviewActionTransitions::TO_ASSIGNED)) {
            $this->workflow->apply($clientSanctionsScreening, ReviewActionTransitions::TO_ASSIGNED);
        }

        $this->admin->update($clientSanctionsScreening);

        return $this->redirectToShow($clientSanctionsScreening->getId());
    }

    /**
     * @throws NotFoundHttpException
     * @throws LogicException
     */
    public function reviewAction(int $id): Response
    {
        if (!$this->admin instanceof ClientSanctionsScreeningAdmin) {
            throw new LogicException('Admin must be instanceof ClientSanctionsScreeningAdmin');
        }

        $user = $this->admin->getTokenStorage()->getToken()->getUser();
        $clientSanctionsScreening = $this->getClientSanctionsScreening($id);
        $this->initWorkflow($clientSanctionsScreening);

        $form = $this->createForm(
            ClientSanctionsScreeningReviewActionType::class,
            [],
            [
                'action' => $this->admin->generateObjectUrl('review', $clientSanctionsScreening),
                'available_transitions' => $this->workflow->getEnabledTransitions($clientSanctionsScreening),
                'blacklist_check_result_items' => $this->blacklistCheckResultItemsToReview($clientSanctionsScreening),
                'previous_action' => $clientSanctionsScreening->getReviewAction(),
            ]
        );
        $form->handleRequest($this->getRequest());

        if (!$form->isSubmitted() || !$form->isValid()) {
            $formView = $form->createView();
            return $this->renderWithExtraParams('@EvpBlacklist/Admin/ClientSanctionScreening/client_sanction_screening_review_actions.html.twig', [
                'form' => $formView,
                'object' => $clientSanctionsScreening,
                'user' => $user,
            ]);
        }

        if ($this->isSelectedProfilesNeedCheck($clientSanctionsScreening) && !$this->validateSelectedProfiles($form)) {
            return $this->redirectToShow($clientSanctionsScreening->getId());
        }

        if ($providedComment = $this->getComment($form)) {
            $comment = (new ClientSanctionsScreeningComment())
                ->setComment($providedComment)
                ->setSanctionsScreening($clientSanctionsScreening)
                ->setUserId($user->getId());

            $this->admin->getEntityManager()->persist($comment);
            $clientSanctionsScreening->addComment($comment);
        }

        $this->entityManager->getConnection()->beginTransaction();
        try {
            $this->applyTransition($clientSanctionsScreening, $form);
            $this->admin->update($clientSanctionsScreening);
            $this->entityManager->getConnection()->commit();
        } catch (Exception $exception) {
            $this->entityManager->getConnection()->rollBack();
            throw $exception;
        }

        if ($this->screeningIsCompleted) {
            $this->addFlash(
                'success',
                sprintf('Successfully completed the screening with id %s.', $clientSanctionsScreening->getId())
            );
        }

        return $this->redirectToShow($clientSanctionsScreening->getId());
    }

    /** @throws NotFoundHttpException */
    private function getClientSanctionsScreening(int $id): ClientSanctionsScreening
    {
        /** @var ClientSanctionsScreening $object */
        $object = $this->admin->getObject($id);

        if (!$object) {
            throw $this->createNotFoundException(sprintf('unable to find the object with id: %s', $id));
        }

        return $object;
    }

    private function getComment(FormInterface $form): ?string
    {
        $comments = array_merge(
            $form->get(ClientSanctionsScreeningReviewActionType::DEFAULT_COMMENTS_MATCH_NATURAL)->getData(),
            $form->get(ClientSanctionsScreeningReviewActionType::DEFAULT_COMMENTS_MATCH_LEGAL)->getData(),
            $form->get(ClientSanctionsScreeningReviewActionType::DEFAULT_COMMENTS_MISMATCH_NATURAL)->getData(),
            $form->get(ClientSanctionsScreeningReviewActionType::DEFAULT_COMMENTS_MISMATCH_LEGAL)->getData(),
        );

        if ($form->get(ClientSanctionsScreeningReviewActionType::COMMENT)->getData()) {
            $comments[] = $form->get(ClientSanctionsScreeningReviewActionType::COMMENT)->getData();
        }

        return !empty($comments) ? implode(', ', $comments) : null;
    }

    private function applyTransition(ClientSanctionsScreening $object, FormInterface $form): void
    {
        switch ($this->getClickedButtonName($form)) {
            case ClientSanctionsScreeningReviewActionType::FALSE_MATCH:
                $this->handleFalseMatch($object, $form);
                $this->workflow->apply($object, ReviewActionTransitions::TO_NOT_MATCHED);
                break;
            case ClientSanctionsScreeningReviewActionType::MATCH:
                $this->handleMatch($object, $form);
                $this->workflow->apply($object, ReviewActionTransitions::TO_MATCHED);
                break;
            case ClientSanctionsScreeningReviewActionType::PENDING:
                $this->workflow->apply($object, ReviewActionTransitions::TO_PENDING);
                break;
            case ClientSanctionsScreeningReviewActionType::ESCALATE:
                $this->workflow->apply($object, ReviewActionTransitions::TO_ESCALATED);
                break;
            case ClientSanctionsScreeningReviewActionType::COMPLETE:
                $this->workflow->apply($object, ReviewActionTransitions::TO_COMPLETED);
                break;
            default:
                throw new LogicException('Unsupported object transition');
        }
    }

    private function getClickedButtonName(FormInterface $form): string
    {
        return $form->getClickedButton()->getConfig()->getName();
    }

    private function redirectToShow(int $id): RedirectResponse
    {
        return $this->redirect($this->admin->generateUrl('show', ['id' => $id]));
    }

    private function isSelectedProfilesNeedCheck(ClientSanctionsScreening $object): bool
    {
        return in_array($object->getReviewAction(), [
            ClientSanctionsScreening::REVIEW_ACTION_INITIAL,
            ClientSanctionsScreening::REVIEW_ACTION_ASSIGNED,
            ClientSanctionsScreening::REVIEW_ACTION_NOT_MATCHED,
        ], true);
    }

    private function validateSelectedProfiles(FormInterface $form): bool
    {
        $selectedProfiles = $form->get(ClientSanctionsScreeningReviewActionType::MATCHED_PROFILES)->getData();
        $btnName = $this->getClickedButtonName($form);

        if (
            $btnName === ClientSanctionsScreeningReviewActionType::MATCH
            && count($selectedProfiles) !== 1
        ) {
            $this->addFlash('error', 'You need to select 1 profile that is the real match.');

            return false;
        }

        if (
            $btnName === ClientSanctionsScreeningReviewActionType::FALSE_MATCH
            && count($selectedProfiles) < 1
        ) {
            $this->addFlash('error', 'Select one or more profiles to whitelist.');

            return false;
        }

        return true;
    }

    private function whitelistProfiles(ClientSanctionsScreening $object, array $profilesToWhitelist, bool $screeningIsCompleted = false): void
    {
        if (count($profilesToWhitelist) < 1) {
            return;
        }

        $this->screeningIsCompleted = $screeningIsCompleted;

        $this->eventDispatcher->dispatch(
            Events::SANCTION_SCREENING_WHITELIST_PROFILES,
            new ClientSanctionsScreeningWhitelistProfilesEvent($object, $profilesToWhitelist)
        );
    }

    private function handleMatch(ClientSanctionsScreening $object, FormInterface $form): void
    {
        $selectedProfilesIds = $this->getSelectedProfiles($form);
        $availableItemsToReview = $this->blacklistCheckResultItemsToReview($object);
        $profilesIdsToReview = $availableItemsToReview->map(
            fn (ClientBlacklistCheckResultItem $item) => $item->getProfile()->getId()
        );
        $profilesIdsToWhitelist = array_diff($profilesIdsToReview->getValues(), $selectedProfilesIds);
        $this->whitelistProfiles($object, $profilesIdsToWhitelist);

        $matchedItems = $availableItemsToReview->filter(
            fn (ClientBlacklistCheckResultItem $item) => in_array($item->getProfile()->getId(), $selectedProfilesIds, true)
        );

        $this->eventDispatcher->dispatch(
            Events::SANCTION_SCREENING_ITEMS_WERE_MATCHED,
            new ClientSanctionsScreeningItemsWereMatchedEvent($matchedItems->toArray())
        );
    }

    private function handleFalseMatch(ClientSanctionsScreening $object, FormInterface $form): void
    {
        $profilesIdsToWhitelist = $this->getSelectedProfiles($form);
        $availableItemsToReview = $this->blacklistCheckResultItemsToReview($object)->toArray();

        $this->whitelistProfiles($object, $profilesIdsToWhitelist, count($profilesIdsToWhitelist) === count($availableItemsToReview));
    }

    private function blacklistCheckResultItemsToReview(ClientSanctionsScreening $sanctionsScreening): Collection
    {
        $items = $this->clientBlacklistCheckResultItemRepository->findNotReviewedByBlacklistCheckResultId(
            $sanctionsScreening->getClientBlacklistCheckResult()->getId()
        );

        return new ArrayCollection($items);
    }

    private function getSelectedProfiles(FormInterface $form): array
    {
        return array_values(
            $form->get(ClientSanctionsScreeningReviewActionType::MATCHED_PROFILES)->getData()
        );
    }
}
