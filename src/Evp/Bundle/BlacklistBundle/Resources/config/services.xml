<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/api.xml"/>
        <import resource="services/admins.xml"/>
        <import resource="services/repositories.xml"/>
        <import resource="services/message_processors.xml"/>
        <import resource="services/dow_jones_incremental_import_processors.xml"/>
        <import resource="services/listeners.xml"/>
        <import resource="services/consumers.xml"/>
        <import resource="services/workers.xml"/>
        <import resource="services/commands.xml"/>
        <import resource="services/controllers.xml"/>
        <import resource="services/voters.xml"/>
        <import resource="services/transformers.xml"/>
        <import resource="services/clients.xml"/>
        <import resource="services/twig_extensions.xml"/>
        <import resource="services/profile_filters.xml"/>
        <import resource="providers.xml" />
        <import resource="normalizers.xml"/>
        <import resource="common_words.xml" />
        <import resource="country_codes.xml"/>
        <import resource="parsers.xml"/>
        <import resource="blacklist_filters.xml"/>
        <import resource="sanction_list_check_result_providers.xml"/>
    </imports>

    <parameters>
        <parameter key="evp_blacklist.natural_person_check_frequency">P1M</parameter>
        <parameter key="evp_blacklist.legal_person_check_frequency">P1M</parameter>
        <parameter key="evp_blacklist.blacklist_mailer.to"><EMAIL></parameter>
        <parameter key="evp_blacklist.blacklist_mailer.from"><EMAIL></parameter>
        <parameter key="evp_blacklist.cache_dir">%temporary_files_directory%/paysera_blacklist</parameter>
        <parameter key="evp_blacklist.dow_jones_ai_filter_confidence_90">90</parameter>
        <parameter key="evp_blacklist.dow_jones_ai_filter_confidence_85">85</parameter>
        <parameter key="evp_blacklist.dow_jones_ai_filter_confidence_80">80</parameter>
        <parameter key="evp_blacklist.dow_jones_ai_filter_confidence_70">70</parameter>
        <parameter key="evp_blacklist.dj_periodical_check_log_folder">/tmp/dj_periodical_check</parameter>
        <parameter key="evp_blacklist.client_periodical_check_folder">/tmp/client_periodical_check</parameter>
        <parameter key="evp_blacklist.blacklist_data_export_folder">/tmp/blacklist_data_export</parameter>
        <!-- We use this folder as a buffer. For more details check this ticket: SERVERS-16246 -->
        <parameter key="evp_blacklist.dj_periodical_check_result_files_folder">/tmp/dj_periodical_check_result_files</parameter>
        <parameter key="evp_blacklist.blacklist_repository_proxy.max_retries">10</parameter>
        <parameter key="evp_blacklist.blacklist_repository_proxy.delay">500000</parameter>
        <parameter key="evp_blacklist.abbreviations_manager.abbreviations" type="collection">
            <parameter>SIA</parameter> <!-- LV -->
            <parameter>GmbH</parameter> <!-- DE -->
            <parameter>ООО</parameter> <!-- RU 8918 occurrences -->
            <parameter>АО</parameter> <!-- RU 2467 occurrences -->
            <parameter>ОАО</parameter> <!-- RU 2247 occurrences -->
            <parameter>LLC</parameter> <!-- EN 2020 occurrences -->
            <parameter>Ltd</parameter> <!-- EN 1357 occurrences -->
            <parameter>OOO</parameter> <!-- RU 1145 occurrences -->
            <parameter>ЗАО</parameter> <!-- RU 906 occurrences -->
            <parameter>JSC</parameter> <!-- EN 563 occurrences -->
            <parameter>ААТ</parameter> <!-- RU 534 occurrences -->
            <parameter>AO</parameter> <!-- EN 391 occurrences -->
            <parameter>ПАО</parameter> <!-- RU 325 occurrences -->
            <parameter>ТОВ</parameter> <!-- UA 281 occurrences -->
        </parameter>
    </parameters>

    <services>
        <service id="evp_blacklist.configuration_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\ConfigurationManager">

            <argument>%evp_blacklist.blacklist_current_configuration%</argument>

            <call method="addConfiguration">
                <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
                <argument type="collection">
                    <argument type="service" id="fos_elastica.client.default" key="client"/>
                    <argument type="service" id="evp_blacklist.service.sanctions_version_manager_v1" key="sanctions_version_manager"/>
                    <argument type="service" id="evp_blacklist.static_blacklist_importer_secondary" key="blacklist_importer"/>
                    <argument type="service" id="evp_blacklist.provider.dow_jones_full_v1" key="feed_data_provider"/>
                    <argument type="service" id="evp_blacklist.provider.dow_jones_incremental_v1" key="incremental_feed_data_provider"/>
                    <argument type="service" id="fos_elastica.finder.sanctions_production_v1.profile" key="profile_finder"/>
                </argument>
            </call>
        </service>
        <service id="evp_blacklist.self_test"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistSelfCheck">

            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="service" id="logger"/>
        </service>
        <!-- Blacklist provider manager -->
        <service id="evp_blacklist.provider_manager" class="Evp\Bundle\BlacklistBundle\Service\ProviderManager" />

        <service id="evp_blacklist.service.profile_query_builder"
                 class="Evp\Bundle\BlacklistBundle\Service\ProfileQueryBuilder">
            <argument type="service" id="evp_blacklist.service.abbreviations_manager"/>
            <argument type="service" id="evp_blacklist.helper.word_counter"/>
            <argument type="service" id="evp_util.transliterate_utilities_inbuilt"/>
            <argument type="service" id="evp_blacklist.profile_type_resolver"/>
            <argument>0.99</argument>
        </service>

        <service id="evp_blacklist.service.abbreviations_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\AbbreviationsManager">
            <argument>%evp_blacklist.abbreviations_manager.abbreviations%</argument>
        </service>

        <service id="evp_blacklist.service.profile_matcher"
                 class="Evp\Bundle\BlacklistBundle\Service\ProfileMatcher">
            <argument type="service" id="evp_blacklist.service.loose_string_matcher"/>
            <argument type="service" id="evp_blacklist.service.loose_string_match_criteria_resolver"/>
        </service>

        <service id="evp_blacklist.service.search_results_processor"
                 class="Evp\Bundle\BlacklistBundle\Service\SearchResultsProcessor">
            <argument type="service" id="evp_blacklist.service.profile_matcher"/>
            <argument type="service" id="evp_blacklist.repository.client_check_whitelist"/>
        </service>

        <service id="evp_blacklist.service.sanctions_version_manager_v1"
                 class="Evp\Bundle\BlacklistBundle\Service\SanctionsVersionManager">
            <argument type="service" id="fos_elastica.client.default"/>
            <argument type="service" id="event_dispatcher"/>
            <argument>%evp_blacklist.blacklist_alias_name_active%</argument>
            <argument>%evp_blacklist.blacklist_alias_name_importing%</argument>
            <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
            <argument type="service" id="logger"/>
            <argument>2</argument>
        </service>

        <service id="evp_blacklist.service.profile_importer"
                 class="Evp\Bundle\BlacklistBundle\Service\ProfileImporter">
            <argument type="service">
                <service class="Goodby\CSV\Import\Standard\Interpreter"/>
            </argument>
            <argument type="service">
                <service class="Goodby\CSV\Import\Standard\Lexer">
                    <argument type="service">
                        <service class="Goodby\CSV\Import\Standard\LexerConfig"/>
                    </argument>
                </service>
            </argument>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.profile_import_parser"/>
        </service>

        <service id="evp_blacklist.profile_import_parser" class="Evp\Bundle\BlacklistBundle\Service\Parser\ProfileImportParser">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.profile_identification_generator"/>
        </service>

        <service id="evp_blacklist.service.blacklist_import_monitoring_service"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistImportMonitoringService">
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%evp_blacklist.monitor_key%</argument>
        </service>

        <service id="evp_blacklist.profile_identification_generator" class="Evp\Bundle\BlacklistBundle\Service\ProfileIdentificationGenerator">
            <argument type="service" id="paysera_random_hash.url_safe_hash_generator" />
        </service>

        <service id="evp_blacklist.profile_manager" class="Evp\Bundle\BlacklistBundle\Service\ProfileManager">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
        </service>

        <service id="evp_blacklist.user_client" class="Evp\Component\UserRestClient\User\UserClient">
            <factory service="evp_user_client.user_rest_factory" method="userClient" />
        </service>

        <!-- Blacklist manager -->
        <service id="evp_blacklist.manager" class="Evp\Bundle\BlacklistBundle\Service\BlacklistManager">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_blacklist.service.profile_query_builder" />
            <argument type="service" id="evp_blacklist.repository.blacklist" />
            <argument type="service" id="evp_bank_transfer.transfer_routing_information_retriever" />
            <argument type="service" id="evp_bank.account_info_resolver"/>
            <argument type="service" id="evp_blacklist.service.search_results_processor"/>
            <argument type="service" id="evp_blacklist.repository.bank_code"/>
            <argument type="service" id="evp_blacklist.user_client"/>
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="evp_bank_transfer.repository.client_type_prediction"/>
            <argument type="string">%evp_blacklist.enabled%</argument>
            <argument type="service" id="logger" />
            <argument type="service" id="paysera_transfer_surveillance.transfer_payer_excluded_checker"/>
            <argument type="service" id="evp_blacklist.dow_jones_ai_manager"/>
            <argument type="service" id="evp_blacklist.search_request_manager"/>
        </service>

        <service id="evp_blacklist.filter_service" class="Evp\Bundle\BlacklistBundle\Service\BlacklistFilterService">
            <argument type="service" id="evp_blacklist.profile_filter_registry"/>
            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="service" id="paysera_feature_flag.service.stored_feature_flag"/>
        </service>

        <service id="evp_blacklist.update_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistUpdateManager">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_blacklist.provider_manager" />
        </service>

        <service id="evp_blacklist.sanctions_screening_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\SanctionsScreening\SanctionsScreeningManager">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_blacklist.repository.blacklist_client_sanctions_screening" />
            <argument type="service" id="evp_blacklist.service.change_tracker.blacklist_client_sanctions_screening" />
            <argument type="service" id="paysera_lock.lock_factory" />
        </service>

        <service id="evp_blacklist.dow_jones_ai_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\DowJonesAiManager">
            <argument type="service" id="evp_blacklist.client.dow_jones_ai_client" />
            <argument type="string">%evp_blacklist.dow_jones_ai_filter_confidence_80%</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.category_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryManager" />

        <service id="evp_blacklist.periodic_check.periodic_check_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\PeriodicCheckManager">
            <argument type="service" id="paysera.csv_reader" />
            <argument type="service" id="paysera.csv_writer" />
            <argument type="service" id="evp_blacklist.periodic_check.profile.profile_mapper" />
            <argument type="service" id="evp_blacklist.periodic_check.profile.manager" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile_data_check_log" />
            <argument type="service" id="evp_blacklist.periodical_check.blacklist_profile_data_check_log_folder"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.profile_mapper"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\ProfileMapper">
        </service>

        <service id="evp_blacklist.listener.whitelist_removal_listener" class="Evp\Bundle\BlacklistBundle\Listener\WhitelistRemovalListener">
            <tag name="kernel.event_listener" event="evp_client.name_changed" method="onClientUpdate"/>
            <tag name="kernel.event_listener" event="evp_client.natural_name_changed" method="onClientUpdate" />
            <tag name="kernel.event_listener" event="evp_client.became_verified" method="onClientUpdate"/>
            <tag name="kernel.event_listener" event="evp_client.became_unverified" method="onClientUpdate"/>
            <tag name="kernel.event_listener" event="evp_client.country_code_changed" method="onClientCountryCodeChanged" />
            <tag name="kernel.event_listener" event="evp_client.level_changed" method="onClientLevelChanged"/>

            <argument type="service" id="evp_bundle_blacklist.service.client_check_whitelist_manager"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.client_listener" class="Evp\Bundle\BlacklistBundle\Listener\ClientListener">
            <tag name="kernel.event_listener" event="evp_client.client_registered.persisted" method="onClientChange" />
            <tag name="kernel.event_listener" event="evp_client.name_changed" method="onClientChange" />
            <tag name="kernel.event_listener" event="evp_client.natural_name_changed" method="onClientChange" />
            <tag name="kernel.event_listener" event="evp_client.country_code_changed" method="onClientCountryCodeChanged" priority="1"/>
            <tag name="kernel.event_listener" event="evp_client.client_code_changed" method="onClientCodeChanged"/>

            <argument type="service" id="evp_blacklist.service.deferred_remote_job_publisher"/>
            <argument type="service" id="logger" />
            <argument type="service" id="evp_banking_history_integration.service.global_identification_manager"/>
        </service>

        <service id="evp_blacklist.remote_event_listener" class="Evp\Bundle\BlacklistBundle\Listener\RemoteEventListener">
            <tag name="monolog.logger" channel="evp_blacklist.remote_event_listener"/>
            <tag name="kernel.event_listener" event="remote.user_identity_document_document_changed" method="onIdentityDocumentChanged" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="evp_client.repository.client" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_banking_history_integration.service.global_identification_manager"/>
        </service>

        <service id="evp_blacklist.subscriber.client_sanctions_screening"
                 class="Evp\Bundle\BlacklistBundle\Listener\Workflow\ClientSanctionsScreeningListener">
            <argument type="service" id="security.token_storage"/>
            <argument type="service" id="workflow.registry"/>
            <argument type="service" id="evp_blacklist.service.change_tracker.blacklist_client_sanctions_screening"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result_item"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="evp_blacklist.listener.sanctions_screening_listener"
                 class="Evp\Bundle\BlacklistBundle\Listener\SanctionsScreeningListener">
            <tag name="kernel.event_listener"
                 event="evp_blacklist.client_blacklisted"
                 method="onClientBlacklisted" />
            <argument type="service" id="evp_blacklist.sanctions_screening_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher" />
        </service>

        <service id="evp_blacklist.listener.blacklist_incremental_import_listener"
                 class="Evp\Bundle\BlacklistBundle\Listener\BlacklistIncrementalImportListener">
            <tag name="kernel.event_listener"
                 event="evp_blacklist.incremental_profile_import"
                 method="onIncrementalImport"/>
            <argument type="service" id="evp_blacklist.service.blacklist_import_monitoring_service"/>
            <argument type="service" id="evp_rest_user_api_client" />
            <argument>%evp_blacklist.emails_to_notify%</argument>
        </service>

        <service id="evp_blacklist.listener.blacklist_import_listener"
                 class="Evp\Bundle\BlacklistBundle\Listener\BlacklistFullImportListener">
            <tag name="kernel.event_listener"
                 event="evp_blacklist.full_profile_import" method="onFullImport" />
            <argument type="service" id="evp_blacklist.service.blacklist_import_monitoring_service"/>
            <argument type="service" id="evp_rest_user_api_client" />
            <argument>%evp_blacklist.emails_to_notify%</argument>
        </service>

        <service id="evp_blacklist.service.loose_string_matcher"
                 class="Evp\Bundle\NameMatcherBundle\Service\LooseStringMatcher">
            <argument type="service" id="evp_util.transliterate_utilities_inbuilt"/>
            <argument type="service">
                <service class="Evp\Bundle\NameMatcherBundle\Entity\LooseStringMatchCriteria">
                    <call method="setWordMatchPercentage">
                        <argument>95</argument>
                    </call>
                    <call method="setMetaphoneMatchPercentage">
                        <argument>95</argument>
                    </call>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\NameMatcherBundle\Service\LooseStringMatchCalculator" />
            </argument>
            <argument>%evp_blacklist.common_words%</argument>
        </service>

        <service id="evp_blacklist.service.loose_string_match_criteria_resolver"
                 class="Evp\Bundle\BlacklistBundle\Service\LooseStringMatchCriteriaResolver">
            <call method="addRule">
                <argument type="service">
                    <service class="Evp\Bundle\BlacklistBundle\Service\LooseStringMatchCriteriaRule\PriorityGroupCriteriaRule">
                        <argument type="service">
                            <service class="Evp\Bundle\NameMatcherBundle\Entity\LooseStringMatchCriteria">
                                <call method="setWordMatchPercentage">
                                    <argument>83</argument>
                                </call>
                                <call method="setMetaphoneMatchPercentage">
                                    <argument>83</argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_blacklist.blacklist_sender" class="Evp\Bundle\BlacklistBundle\Service\BlacklistSender">
            <argument type="service" id="evp_mailer.mailer"/>
            <argument>%evp_blacklist.blacklist_mailer.to%</argument>
            <argument>%evp_blacklist.blacklist_mailer.from%</argument>
        </service>

        <service id="evp_blacklist.layout.blacklist_check" class="Evp\Bundle\MailerBundle\Entity\Layout">
            <argument type="constant">\Evp\Bundle\BlacklistBundle\Service\BlacklistSender::LAYOUT_BLACKLIST_CHECK</argument>
            <argument type="string">EvpBlacklistBundle:BlacklistCheck\Layout:users_list.plain.twig</argument>
            <argument type="string">EvpBlacklistBundle:BlacklistCheck\Layout:users_list.html.twig</argument>

            <tag name="evp_mailer.layout" />
        </service>

        <service id="evp_blacklist.letter.blacklist_check" class="Evp\Bundle\MailerBundle\Entity\Letter">
            <argument type="constant">\Evp\Bundle\BlacklistBundle\Service\BlacklistSender::LAYOUT_BLACKLIST_CHECK</argument>
            <argument type="string">EvpBlacklistBundle:BlacklistCheck\Letter:users_list.html.twig</argument>
            <argument type="string">EvpBlacklistBundle</argument>
            <argument type="constant">\Evp\Bundle\BlacklistBundle\Service\BlacklistSender::LAYOUT_BLACKLIST_CHECK</argument>

            <tag name="evp_mailer.letter" />
        </service>

        <service id="evp_blacklist.dowjones_downloader" class="Evp\Bundle\BlacklistBundle\Service\Downloader">
            <argument>%env(DOWJONES_HOST)%</argument>
            <argument>%env(DOWJONES_USERNAME)%</argument>
            <argument>%env(DOWJONES_PASSWORD)%</argument>
            <argument type="service">
                <service class="GuzzleHttp\Client" />
            </argument>
            <argument>%evp_blacklist.cache_dir%</argument>
        </service>

        <service id="evp_blacklist.dowjones_full_feed_reader"
                 class="Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader">
            <argument type="service">
                <service class="Evp\Bundle\BlacklistBundle\Service\Reader\DowJones\FullFeedProvider" />
            </argument>
            <argument type="service" id="evp_blacklist.dowjones_downloader" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.dowjones_incremental_daily_feed_reader"
                 class="Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader">
            <argument type="service">
                <service class="Evp\Bundle\BlacklistBundle\Service\Reader\DowJones\IncrementalDailyFeedProvider" />
            </argument>
            <argument type="service" id="evp_blacklist.dowjones_downloader" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.dowjones_country_code_resolver"
            class="Evp\Bundle\BlacklistBundle\Service\Resolver\CountryCodeResolver">
            <argument>%evp_blacklist.dowjones_countries_list%</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.category_importer"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.category_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_category"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_category_group"/>
        </service>

        <service id="evp_blacklist.profile_remover"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistProfileRemover">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.profile_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
            <argument type="service" id="fos_elastica.client.default"/>
            <argument>%evp_blacklist.cache_dir%</argument>
        </service>

        <service id="evp_blacklist.dowjones_xml_parser_resolver"
                 class="Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParserResolver">
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_person"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_entity"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_category1"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_category2"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_category3"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_person_association"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_entity_association"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_occupation"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_sanctions_reference"/>
            </call>
            <call method="addParser">
                <argument type="service" id="evp_blacklist.parser.dowjones_association_relationship"/>
            </call>
        </service>

        <service id="evp_blacklist.xml_parser_helper"
                 class="Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\XmlParserHelper"/>

        <service id="evp_blacklist.static_blacklist_importer"
                 class="Evp\Bundle\BlacklistBundle\Service\StaticBlacklistImporter">
            <argument type="service" id="evp_blacklist.repository.blacklist_profile" />
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_category"/>
            <argument type="service" id="evp_blacklist.profile_manager"/>
            <argument type="service" id="doctrine"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result_item"/>
            <argument type="service" id="logger" />
            <argument>Paysera</argument>
        </service>

        <service id="evp_blacklist.static_blacklist_importer_secondary"
                 class="Evp\Bundle\BlacklistBundle\Service\StaticBlacklistImporter">
            <argument type="service" id="evp_blacklist.repository.blacklist_profile" />
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_category"/>
            <argument type="service" id="evp_blacklist.profile_manager"/>
            <argument type="service" id="doctrine"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result_item"/>
            <argument type="service" id="logger" />
            <argument>Dow Jones</argument>
        </service>

        <service id="evp_blacklist.multi_index_listener_elasticsearch6"
                 class="Evp\Bundle\BlacklistBundle\Listener\MultiIndexListener">
            <tag name="doctrine.event_subscriber"/>

            <argument type="service" id="fos_elastica.object_persister.sanctions_secondary_v1.profile"/>
            <argument type="service" id="fos_elastica.indexable"/>
            <argument type="collection">
                <argument key="indexName">%evp_blacklist.blacklist_alias_name_importing%</argument>
                <argument key="typeName">profile</argument>
                <argument key="identifier">id</argument>
            </argument>
            <argument>false</argument>
        </service>

        <service id="evp_blacklist.search_provider.profile"
                 class="Evp\Bundle\BlacklistBundle\Service\ProfileSearchProvider">
            <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
        </service>

        <service id="evp_blacklist.helper.word_counter"
                 class="Evp\Bundle\BlacklistBundle\Service\Helper\WordCounter"/>

        <service id="evp_blacklist.symbols_hash_generator"
                 class="Paysera\Bundle\RandomHashBundle\Service\CustomSymbolsHashGenerator">
            <argument>25</argument>
            <argument type="string">ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789</argument>
        </service>

        <service id="evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckManager">
            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="service" id="paysera.csv_writer"/>
            <argument type="service" id="evp_blacklist.symbols_hash_generator"/>
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="string">%evp_blacklist.dj_periodical_check_log_folder%</argument>
        </service>

        <service id="evp_blacklist.service.dow_jones_import.full_import"
                 class="Evp\Bundle\BlacklistBundle\Service\DowJonesImport\DowJonesFullImport">
            <tag name="monolog.logger" channel="paysera.dowjones_full_import"/>

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_blacklist.category_importer" />
            <argument type="service" id="evp_blacklist.repository.blacklist_category" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile_relationship"/>
            <argument type="service" id="evp_blacklist.transformer.provider_category" />
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="logger" />
            <argument type="service" id="doctrine"/>
            <argument type="service" id="evp_blacklist.service.blacklist_repository_proxy"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="service" id="paysera.console_progress_bar_helper"/>
        </service>

        <service id="evp_blacklist.service.dow_jones_import.incremental_import"
                 class="Evp\Bundle\BlacklistBundle\Service\DowJonesImport\DowJonesIncrementalImport">
            <tag name="monolog.logger" channel="paysera.dowjones_incremental_import"/>

            <argument type="service" id="evp_blacklist.dowjones_incremental_daily_feed_reader"/>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_blacklist.category_importer" />
            <argument type="service" id="evp_blacklist.service.blacklist_repository_proxy" />
            <argument type="service" id="evp_blacklist.repository.blacklist_category" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile_relationship" />
            <argument type="service" id="evp_blacklist.transformer.provider_category" />
            <argument type="service" id="doctrine"/>
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.search_request_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\SearchRequestManager">
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.periodical_check.client_periodic_check_report_generator"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientPeriodicCheckReportGenerator">
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result_item"/>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="paysera.csv_writer"/>
            <argument type="string">%evp_blacklist.client_periodical_check_folder%</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.service_remover_resolver.blacklist_check_results_resolver"
                 class="Evp\Bundle\BlacklistBundle\Service\Remover\Resolver\BlacklistCheckResultsResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\ClientBundle\Entity\Client"/>

            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.text_character.add_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers\TextCharacterAddProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.text_character.delete_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers\TextCharacterDeleteProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.text_character.add_and_delete_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers\TextCharacterAddAndDeleteProfileHandler">
            <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.add_handler"/>
            <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.delete_handler"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.text_character.reversing_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers\TextCharacterReversingProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.handlers.date_add_or_subtract_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\DateAddOrSubtractHandler"/>

        <service id="evp_blacklist.periodic_check.profile.handlers.date_invalid_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\DateInvalidHandler"/>

        <service id="evp_blacklist.periodic_check.profile.word.delete_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\WordHandlers\WordDeleteProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.word.hyphen_joining_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\WordHandlers\WordHyphenJoiningProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.word.joining_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\WordHandlers\WordJoiningProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.word.swapping_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\WordHandlers\WordSwappingProfileHandler"/>

        <service id="evp_blacklist.periodic_check.profile.handlers.levenshtein_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\LevenshteinHandler">
            <argument type="service" id="evp_bundle_blacklist.service.levenshtein_utf8_helper"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.handlers.soundex_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\SoundexHandler"/>

        <service id="evp_blacklist.periodic_check.profile.handlers.metaphone_handler"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\MetaphoneHandler">
            <argument type="service" id="evp_bundle_blacklist.service.metaphone"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.manager"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\PeriodicCheckProfileManager">
            <argument type="service" id="evp_blacklist.periodic_check.profile.handlers_chain"/>
        </service>

        <service id="evp_blacklist.periodic_check.profile.handlers_chain"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\PeriodicCheckProfileHandlersChain">
            <argument type="service" id="logger"/>

            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.add_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.delete_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.add_and_delete_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.text_character.reversing_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.handlers.date_add_or_subtract_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.handlers.date_invalid_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.word.delete_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.word.hyphen_joining_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.word.joining_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.word.swapping_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.handlers.soundex_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.handlers.metaphone_handler"/>
            </call>
            <call method="addHandler">
                <argument type="service" id="evp_blacklist.periodic_check.profile.handlers.levenshtein_handler"/>
            </call>
        </service>

        <service id="evp_blacklist.periodical_check.periodical_blacklist_control_data_check_initializer"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckInitializer">
            <argument type="service" id="evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher"/>
            <argument type="service" id="evp_blacklist.periodical_check.blacklist_profile_ata_check_log_input_csv_file_validator"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.adapter_factory.client_periodic_check_result"/>
        </service>

        <service id="evp_blacklist.periodical_check.blacklist_profile_ata_check_log_input_csv_file_validator"
                 class="Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\BlacklistProfileDataCheckLogInputCsvFileValidator">
            <argument type="service">
                <service class="Goodby\CSV\Import\Standard\Interpreter"/>
            </argument>
            <argument type="service">
                <service class="Goodby\CSV\Import\Standard\Lexer">
                    <argument type="service">
                        <service class="Goodby\CSV\Import\Standard\LexerConfig"/>
                    </argument>
                </service>
            </argument>
        </service>

        <service id="evp_blacklist.periodical_check.blacklist_profile_data_check_log_folder"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistProfileDataCheckLogFolder">
            <argument type="service" id="filesystem"/>
            <argument type="string">%evp_blacklist.dj_periodical_check_result_files_folder%</argument>
        </service>

        <service id="evp_blacklist.periodical_check.blacklist_profile_tmp_export_folder"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistProfileTmpExportFolder">
            <argument type="service" id="filesystem"/>
            <argument type="string">%evp_blacklist.blacklist_data_export_folder%</argument>
        </service>

        <service id="evp_blacklist.service.blacklist_client_sanctions_screening_change_log_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientSanctionsScreeningChangeLogManager">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_blacklist.service.blacklist_client_check_whitelist_change_log_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistChangeLogManager">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="security.token_storage"/>
        </service>

        <service id="evp_blacklist.service.change_tracker.blacklist_client_sanctions_screening"
                 class="Evp\Bundle\BlacklistBundle\Service\ChangeTracker\ClientSanctionsScreeningChangeTracker">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="security.token_storage"/>
            <argument type="service" id="evp_blacklist.service.blacklist_client_sanctions_screening_change_log_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_client_sanctions_screening_change_log"/>
            <argument>sanctionsScreening</argument>
        </service>

        <service id="evp_bundle_blacklist.service.levenshtein_utf8_helper"
                 class="Evp\Bundle\BlacklistBundle\Service\LevenshteinUtf8Helper"/>

        <service id="evp_bundle_blacklist.service.metaphone"
                 class="Evp\Bundle\BlacklistBundle\Service\Metaphone\MetaphoneService">
            <call method="addFilter">
                <argument type="service">
                    <service class="Evp\Bundle\BlacklistBundle\Service\Metaphone\Filters\Normalize"/>
                </argument>
            </call>
            <call method="addFilter">
                <argument type="service">
                    <service class="Evp\Bundle\BlacklistBundle\Service\Metaphone\Filters\RemoveDoubleChars"/>
                </argument>
            </call>
            <call method="addFilter">
                <argument type="service">
                    <service class="Evp\Bundle\BlacklistBundle\Service\Metaphone\Filters\Replacements"/>
                </argument>
            </call>
        </service>

        <service id="evp_blacklist.client_check_whitelist_listener"
                 class="Evp\Bundle\BlacklistBundle\Listener\ClientCheckWhitelistListener">

            <tag name="kernel.event_listener"
                 event="evp_blacklist.client_sanctions_screening.whitelist_profiles"
                 method="onReviewActionWhitelisted"/>

            <argument type="service" id="evp_bundle_blacklist.service.client_check_whitelist_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_category_group"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_blacklist.service.client_blacklist_check_result_item_review_log_manager"/>
        </service>

        <service id="evp_bundle_blacklist.service.client_check_whitelist_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.client_check_whitelist"/>
            <argument type="service" id="evp_blacklist.service.blacklist_client_check_whitelist_change_log_manager"/>
        </service>

        <service id="evp_bundle_blacklist.service.blacklist_check_results_exporter"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCheckResultsExporter">
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_client_sanctions_screening"/>
            <argument type="service" id="router"/>
        </service>

        <service id="evp_bundle_blacklist.service.blacklist_check_results_dow_jones_exporter"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCheckResultsExport\BlacklistCheckResultsDowJonesExporter">
            <argument type="service" id="evp_blacklist.periodical_check.blacklist_profile_tmp_export_folder"/>
            <argument type="service" id="evp_client.user_information_provider"/>
            <argument type="service" id="evp_questionnaire_service.repositories.client_legal_questionnaire"/>
            <argument type="service" id="evp_bundle_blacklist.service.blacklist_matched_profile_details_mapper"/>
            <argument type="service" id="evp_bundle_blacklist.service.blacklist_check_results_dow_jones_exporter_table_constructor"/>
        </service>

        <service id="evp_bundle_blacklist.service.blacklist_check_results_dow_jones_exporter_table_constructor"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCheckResultsExport\BlacklistCheckResultsDowJonesTableConstructor">
        </service>

        <service id="evp_bundle_blacklist.service.blacklist_matched_profile_details_mapper"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCheckResultsExport\BlacklistMatchedProfileDetailsMapper">
        </service>

        <service id="evp_blacklist.service.blacklist_repository_proxy"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistRepositoryProxy">
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument>%evp_blacklist.blacklist_repository_proxy.max_retries%</argument>
            <argument>%evp_blacklist.blacklist_repository_proxy.delay%</argument>
        </service>

        <service id="evp_blacklist.profile_type_resolver"
                 class="Evp\Bundle\BlacklistBundle\Service\Resolver\ProfileTypeResolver"/>

        <service id="evp_bundle_blacklist.service.user_file_exporter"
                 class="Evp\Bundle\BlacklistBundle\Service\UserFileExporter">
            <argument type="service" id="evp_bundle_blacklist.service.legal_documents_client.client"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bundle_blacklist.service.legal_documents_client.client_factory"
                 class="Paysera\Bundle\LegalDocumentApiClient\ClientFactory">
            <factory class="Paysera\Bundle\LegalDocumentApiClient\ClientFactory" method="create" />
            <argument type="collection">
                <argument key="base_url">%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/legal-document/rest/v1/</argument>
                <argument type="collection" key="mac">
                    <argument key="mac_id">%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%</argument>
                    <argument key="mac_secret">%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%</argument>
                </argument>
            </argument>
        </service>

        <service id="evp_bundle_blacklist.service.legal_documents_client.client"
                 class="Paysera\Bundle\LegalDocumentApiClient\LegalDocumentApiClient">
            <factory service="evp_bundle_blacklist.service.legal_documents_client.client_factory"
                     method="getLegalDocumentApiClient"/>
        </service>

        <service id="evp_blacklist.service.user_query_builder"
                 class="Evp\Bundle\BlacklistBundle\Service\UserQueryBuilder">
        </service>

        <service id="evp_blacklist.service.client_blacklist_check_result_item_review_log_manager"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientBlacklistCheckResultItemReviewLogManager">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.service.client_blacklist_check_result_item_review_log_factory"/>
        </service>

        <service id="evp_blacklist.service.client_blacklist_check_result_item_review_log_factory"
                 class="Evp\Bundle\BlacklistBundle\Service\Factory\ClientBlacklistCheckResultItemReviewLogFactory">
            <argument type="service" id="paysera.component.user.user_id_fetcher"/>
            <argument type="service" id="evp.component.time.clock"/>
        </service>

        <service id="evp_blacklist.client_sanctions_screening_items_were_matched_event_listener"
                 class="Evp\Bundle\BlacklistBundle\Listener\ClientSanctionsScreeningItemsWereMatchedEventListener">
            <tag name="kernel.event_listener"
                 event="evp_blacklist.client_sanctions_screening.items_were_matched"
                 method="onClientSanctionsScreeningItemsWereMatchedEvent"/>
            <argument type="service" id="evp_blacklist.service.client_blacklist_check_result_item_review_log_manager"/>
        </service>

        <service id="evp_blacklist.adapter_factory.client_periodic_check_result"
                 class="Evp\Bundle\BlacklistBundle\Service\ClientPeriodicCheckResultAdapterFactory">
            <argument type="service" id="paysera_file_management.adapter_factory"/>
            <argument>dj_periodical_check_result_files</argument>
        </service>

        <service id="evp_blacklist.service.event_processor.event_deduplication_service"
                 class="Evp\Bundle\BlacklistBundle\Service\EventsProcessor\EventDeduplicationService">
        </service>

        <service id="evp_blacklist.service.deferred_remote_job_publisher"
                 class="Evp\Bundle\BlacklistBundle\Service\BlacklistCheckDeferredRemoteJobPublisher">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="evp_blacklist.service.event_processor.event_deduplication_service"/>
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
