<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity
            repository-class="Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistChangeLogRepository"
            name="Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelistChangeLog"
            table="blacklist_client_check_whitelists_change_log">

        <id name="id" type="integer">
            <generator/>
        </id>

        <indexes>
            <index columns="client_check_whitelist_id"/>
            <index columns="action"/>
            <index columns="user_id"/>
        </indexes>

        <field name="clientCheckWhitelist" column="client_check_whitelist_id" type="integer" nullable="true"/>
        <field name="date" type="datetime_immutable"/>
        <field name="action" length="10"/>
        <field name="field" length="25" nullable="true"/>
        <field name="value" length="50" nullable="true"/>
        <field name="userId" column="user_id" type="integer"/>
    </entity>
</doctrine-mapping>
