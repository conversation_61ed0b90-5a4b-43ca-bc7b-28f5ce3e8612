<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Evp\Bundle\BlacklistBundle\Entity\NamesComparisonResult"
            repository-class="Evp\Bundle\BlacklistBundle\Repository\NamesComparisonResultRepository"
            table="blacklist_names_comparison_results">

        <id name="id" type="bigint" column="id">
            <generator strategy="AUTO"/>
        </id>

        <indexes>
            <index columns="decision"/>
            <index columns="failure_reason"/>
        </indexes>

        <field name="leftName" column="left_name" type="string" nullable="false"/>
        <field name="rightName" column="right_name" type="string" nullable="false"/>
        <field name="modelAnswer" column="model_answer" type="text" nullable="true"/>
        <field name="decision" column="decision" type="string" nullable="false"/>
        <field name="failureReason" column="failure_reason" type="string" nullable="true"/>
        <field name="responseCode" column="response_code" type="integer" nullable="false"/>
        <field name="requestDuration" column="request_duration" type="integer" nullable="false"/>
        <field name="model" column="model" type="string" nullable="false"/>
        <field name="createdAt" type="datetime_immutable" column="created_at"/>

        <many-to-one target-entity="Evp\Bundle\BankTransferBundle\Entity\Transfer" field="transfer">
            <join-column name="transfer_id" nullable="true" on-delete="CASCADE"/>
        </many-to-one>
        <many-to-one target-entity="Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult" field="blacklistCheckResult">
            <join-column name="check_result_id" nullable="true" on-delete="CASCADE"/>
        </many-to-one>
    </entity>
</doctrine-mapping>
