<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_blacklist.admin.blacklist" class="Evp\Bundle\BlacklistBundle\Admin\BlacklistAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Sanction list" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\Blacklist</argument>
            <argument/>
        </service>

        <service id="evp_blacklist.admin.person" class="Evp\Bundle\BlacklistBundle\Admin\ProfileAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Profile" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\Profile</argument>
            <argument>Evp\Bundle\BlacklistBundle\Controller\ProfileAdminController</argument>
            <argument type="service" id="evp_blacklist.profile_identification_generator"/>
            <call method="setTemplate">
                <argument>show</argument>
                <argument>EvpBlacklistBundle:Admin\Profile:show.html.twig</argument>
            </call>
            <call method="setTemplate">
                <argument>batch</argument>
                <argument>EvpBlacklistBundle:Admin\Profile:list__batch.html.twig</argument>
            </call>
            <call method="setTemplate">
                <argument>list</argument>
                <argument>EvpBlacklistBundle:Admin\Profile:list.html.twig</argument>
            </call>
        </service>

        <service id="evp_blacklist.admin.client_check_whitelist"
                 class="Evp\Bundle\BlacklistBundle\Admin\ClientCheckWhitelistAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Client Check Whitelists" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist</argument>
            <argument/>

            <call method="setDependencies">
                <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
                <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result_item"/>
                <argument type="service" id="evp_blacklist.repository.blacklist_client_sanctions_screening"/>
                <argument type="service" id="event_dispatcher"/>
                <argument type="service" id="workflow.registry"/>
                <argument type="service" id="doctrine.orm.default_entity_manager"/>
                <argument type="service" id="evp_blacklist.service.blacklist_client_check_whitelist_change_log_manager"/>
            </call>
        </service>

        <service id="evp_blacklist.admin.blacklist_control_data_check_log" class="Evp\Bundle\BlacklistBundle\Admin\PeriodicalBlacklistControlDataCheckLogAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Blacklists check logs" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog</argument>
            <argument>Evp\Bundle\BlacklistBundle\Controller\PeriodicalBlacklistControlDataCheckLogAdminController</argument>
            <call method="setTemplate">
                <argument>show</argument>
                <argument>EvpBlacklistBundle:Admin/PeriodicalBlacklistControlDataCheckLog:show__title.html.twig</argument>
            </call>
        </service>

        <service id="evp_blacklist.admin.profile_check_results_log" class="Evp\Bundle\BlacklistBundle\Admin\ProfileCheckResultLogAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Profile check results log" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\ProfileCheckResult</argument>
            <argument/>
        </service>

        <service id="evp_blacklist.admin.category_group" class="Evp\Bundle\BlacklistBundle\Admin\CategoryGroupAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Profile Category Groups" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\CategoryGroup</argument>
            <argument/>
        </service>

        <service id="evp_blacklist.admin.category" class="Evp\Bundle\BlacklistBundle\Admin\CategoryAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Profile categories" group="Blacklists"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\Category</argument>
            <argument/>
        </service>

        <service id="evp_blacklist.admin.client_sanction_screening" class="Evp\Bundle\BlacklistBundle\Admin\ClientSanctionsScreeningAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Items to review" group="Periodic Screening"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening</argument>
            <argument>Evp\Bundle\BlacklistBundle\Controller\ClientSanctionsScreeningAdminController</argument>
            <call method="setTemplate">
                <argument>show</argument>
                <argument>EvpBlacklistBundle:Admin\ClientSanctionScreening:show.html.twig</argument>
            </call>
            <call method="setTokenStorage">
                <argument type="service" id="security.token_storage"/>
            </call>
            <call method="setEntityManager">
                <argument type="service" id="doctrine.orm.default_entity_manager"/>
            </call>
            <call method="setSession">
                <argument type="service" id="session"/>
            </call>
        </service>

        <service id="evp_blacklist.admin.extension.workflow"
                 class="Yokai\SonataWorkflow\Admin\Extension\WorkflowExtension">
            <argument type="service" id="workflow.registry"/>
            <argument type="collection">
                <argument type="collection" key="render_actions"></argument>
                <argument type="string" key="workflow_name">blacklist_client_sanctions_screening_review_action</argument>
                <argument type="string" key="dropdown_transitions_label">Review action transitions</argument>
            </argument>
        </service>

        <service id="evp_blacklist.admin.client_check_logs" class="Evp\Bundle\BlacklistBundle\Admin\ClientCheckLogsAdmin">
            <tag name="sonata.admin" manager_type="orm" label="Client periodic check log details" group="Periodic Screening"/>
            <argument/>
            <argument>Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult</argument>
            <argument>Evp\Bundle\BlacklistBundle\Controller\ClientCheckLogsAdminController</argument>
            <call method="setTemplate">
                <argument>show</argument>
                <argument>EvpBlacklistBundle:Admin\ClientCheckLogs:show.html.twig</argument>
            </call>
            <call method="setFormFactory">
                <argument type="service" id="form.factory"/>
            </call>
        </service>
    </services>
</container>
