<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="evp_blacklist.ai_legal_name_comparison_context">
            You will receive two legal names. Your task is to determine whether they refer to the same legal entity or person by carefully following specific matching and exclusion criteria. Please adhere to these instructions:

            1. **Alphabet Variations**:
            - The names may be written in any combination of Latin, Cyrillic, or other regionally used alphabets.
            - Account for standard transliteration differences between these alphabets (e.g., "ООО Ромашка" and "LLC Romashka" can be considered equivalent if phonetically identical).

            2. **Name Structure and Order**:
            - Legal names may include an organization's type suffix (e.g., "LLC", "Ltd.", "ООО") or descriptive terms (e.g., "Corp.", "Company").
            - Equivalency of suffixes should be determined by standard translations or abbreviations (e.g., "ООО" as "LLC").
            - The central part (main name component) of the names is the most critical for matching.
            - Variations in order or additional components (descriptors) may appear but should not cause mismatches unless the components directly conflict.
            - The **main name component must not be translated** between languages; it must appear as textually or phonetically identical based on its original form (e.g., "Romashka" as "Romashka," not translated to "Daisy").

            3. **Matching and Exclusion Criteria**:
            - **Main Name Component Match**:
            - The primary part of the name must match exactly or be a recognized variation after accounting for transliterations and abbreviations (e.g., "Romashka Ltd." and "Romashka" are acceptable matches).
            - Main name components include the core name without organizational suffixes or descriptors.
            - The **main name component** must match according to its **original form** and must not be translated into another language.

            - **Organizational Suffixes**:
            - Ignore suffix differences (e.g., "Ltd.", "LLC", or "ООО") unless they directly conflict semantically (e.g., "Inc." vs. "Ltd." suggests different corporate structures and would result in "No").
            - **Descriptors or Qualifiers**:
            - Ignore non-conflicting descriptive terms (e.g., "Trading", "Manufacturing", "Logistics") if the main name component matches. However, conflicting descriptors trigger a mismatch (e.g., "Romashka Trading" vs. "Romashka Logistics").
            - **No Conflicting Information**:
            - Do not match names if key components (main names or suffixes) are contradictory (e.g., "Tulip LLC" vs. "Romashka LLC").

            4. **Special Cases and Clarifications**:
            - **Mixed Alphabets**:
            - Names containing components from multiple languages (e.g., Latin and Cyrillic) can match if the key components are phonetically or textually equivalent (e.g., "Torgovaya Kompaniya Romashka" and "Romashka Trading Company").
            - **Abbreviations**:
            - Abbreviated forms of names (e.g., "ACME Corporation" vs. "ACME Corp.") should match unless they cause conflicts (e.g., "ACME Manufacturing" vs. "ACME Logistics").
            - **Mergers or Compound Names**:
            - For merged entities or joint operations (e.g., "Romashka-Trade LLC"), there must still be a clearly recognizable component matching (e.g., "Romashka").
            - **Conflicting Organizational Types**:
            - Conflicting suffixes suggest different entities (e.g., "Inc." vs. "Ltd.") and should result in "No," even if the central name component matches.

            5. **Response Format**:
            - Your response should be only "Yes" or "No" based strictly on the rules above.

            Examples:
            - **Yes Examples**:
            - "Romashka LLC" and "ООО Ромашка"
            - Main Name: "Romashka" vs. "Ромашка" → Phonetic Match
            - Suffix: "LLC" vs. "ООО" → Equivalent organization types
            - Result: Yes

            - "ACME Corporation" and "ACME Corp."
            - Main Name: "ACME" vs. "ACME" → Match
            - Suffix: "Corporation" vs. "Corp." → Equivalent
            - Result: Yes

            - "Romashka Trading Ltd." and "ООО Ромашка"
            - Main Name: "Romashka" vs. "Ромашка" → Phonetic Match
            - Descriptive Qualifier: "Trading" → Non-conflicting, ignored
            - Suffix: "Ltd." vs. "ООО" → Equivalent
            - Result: Yes

            - "Torgovaya Kompaniya Romashka" and "Romashka Trading Company"
            - Main Name: "Romashka" vs. "Romashka" → Match
            - Transliteration: Phonetic equivalence for descriptive terms ("Torgovaya Kompaniya" and "Trading Company")
            - Result: Yes

            - **No Examples**:
            - "Tulip LLC" and "Romashka LLC"
            - Main Name: "Tulip" vs. "Romashka" → No Match (Different main components)
            - Result: No

            - "ACME Manufacturing Inc." and "ACME Logistics Ltd."
            - Main Name: "ACME" vs. "ACME" → Match
            - Descriptor: "Manufacturing" vs. "Logistics" → Conflict
            - Suffix: "Inc." vs. "Ltd." → Conflict
            - Result: No

            - "ООО Тюльпан" and "Romashka"
            - Main Name: "Тюльпан" vs. "Romashka" → No Match (Different main components)
            - Result: No

            - "Romashka" and "Daisy"
            - Main Name: "Romashka" vs. "Daisy" → No Match (Main Name Translated)
            - Result: No
        </parameter>
    </parameters>

    <services>
        <service id="evp_blacklist.profile_filter.ai_service.natural_name_comparison.4o-mini"
                 class="Paysera\Component\AIService\AINameComparisonService">
            <argument type="service" id="paysera.chat_gpt_client.4o-mini"/>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%paysera_transfer_surveillance.ai_name_comparison_context%</argument>
            <argument>blacklist.ai.execution_time</argument>
            <argument>blacklist.ai.status_code</argument>
        </service>

        <service id="evp_blacklist.profile_filter.ai_service.legal_name_comparison.4o-mini"
                 class="Paysera\Component\AIService\AINameComparisonService">
            <argument type="service" id="paysera.chat_gpt_client.4o-mini"/>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%evp_blacklist.ai_legal_name_comparison_context%</argument>
            <argument>blacklist.ai.execution_time</argument>
            <argument>blacklist.ai.status_code</argument>
        </service>

        <service id="evp_blacklist.profile_filter.names_comparison_result_service"
                 class="Evp\Bundle\BlacklistBundle\Service\NamesComparisonResultService">
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service id="evp_blacklist.profile_filter_registry" class="Evp\Bundle\BlacklistBundle\Service\ProfileFilterRegistry"/>

        <service id="evp_blacklist.profile_filter.name" class="Evp\Bundle\BlacklistBundle\Service\BlacklistProfileFilter\NameMatchingFilter">
            <tag name="evp_blacklist.profile_filter"/>
            <argument type="service" id="evp_blacklist.profile_filter.ai_service.natural_name_comparison.4o-mini"/>
            <argument type="service" id="evp_blacklist.profile_filter.ai_service.legal_name_comparison.4o-mini"/>
            <argument type="service" id="evp_blacklist.profile_filter.names_comparison_result_service"/>
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
