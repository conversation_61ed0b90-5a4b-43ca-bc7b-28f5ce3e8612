<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <parameters>
        <parameter key="evp_blacklist.worker.elasticsearch_return_count_limit">250</parameter>
        <parameter key="evp_blacklist.worker.blacklist_check_ai_confidence_90">90</parameter>
        <parameter key="evp_blacklist.worker.blacklist_check.monitor_key">evpbank.blacklist_check</parameter>
        <parameter key="evp_blacklist.worker.blacklist_check.global_identification_id_wait_timeout">5</parameter>
    </parameters>

    <services>
        <service id="evp_blacklist.worker.blacklist_check"
                 class="Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_blacklist.filter_service" />
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_questionnaire_service.repositories.questionnaire"/>
            <argument type="service" id="evp_client.repository.partner_client"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher" />
            <argument>%evp_blacklist.worker.blacklist_check_ai_confidence_90%</argument>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%evp_blacklist.worker.blacklist_check.monitor_key%</argument>
            <argument type="service" id="evp_banking_history_integration.service.global_identification_manager"/>
            <argument>%evp_blacklist.worker.blacklist_check.global_identification_id_wait_timeout%</argument>

            <tag name="remote_job_worker" worker_key="gateway.check_blacklist_users"/>
            <tag name="remote_job_worker" worker_key="gateway.check_blacklist_user_generate_report"/>
        </service>

        <service id="evp_blacklist.maintenance_worker.blacklist_check"
                 parent="evp_blacklist.worker.blacklist_check">
            <tag name="remote_job_worker" worker_key="gateway.maintenance.blacklist"/>
        </service>

        <service id="evp_bundle_blacklist.worker.periodic_check_process_data_worker"
                 class="Evp\Bundle\BlacklistBundle\Worker\PeriodicCheckProcessDataWorker">
            <argument type="service" id="logger"/>
            <argument type="service" id="paysera.csv_reader"/>
            <argument type="service" id="paysera.csv_writer"/>
            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="service" id="evp_blacklist.periodic_check.periodic_check_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_profile_data_check_log"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="paysera.file_archiver_enhanced"/>
            <argument type="service" id="evp_blacklist.periodical_check.blacklist_profile_data_check_log_folder"/>
            <argument type="service" id="evp_blacklist.adapter_factory.client_periodic_check_result"/>

            <tag name="remote_job_worker" worker_key="gateway.blacklist_periodic_check_process_data"/>
        </service>

        <service id="evp_bundle_blacklist.jobs_consumer.periodic_check"
                 class="Evp\Bundle\BlacklistBundle\Worker\PeriodicCheckConsumedJobsWorker"
                 parent="evp_rabbit_mq_extension.jobs_consumer"/>

        <service id="evp_rabbit_mq_extension.periodic_check_jobs_worker" parent="evp_rabbit_mq_extension.consumed_jobs_worker">
            <argument index="0" type="service" id="evp_bundle_blacklist.jobs_consumer.periodic_check"/>
        </service>

        <service id="evp_bundle_blacklist.worker.blacklist_check_generate_report_file_worker"
                 class="Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckGenerateReportFileWorker">
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result"/>
            <argument type="service" id="evp_bundle_blacklist.service.blacklist_check_results_dow_jones_exporter"/>
            <argument type="service" id="evp_bundle_blacklist.service.user_file_exporter"/>

            <tag name="remote_job_worker" worker_key="gateway.check_blacklist_user_generate_report_file"/>
        </service>

        <service id="evp_blacklist.worker.blacklist_updated_profile_check_worker"
                 class="Evp\Bundle\BlacklistBundle\Worker\BlacklistUpdatedProfileCheckWorker">
            <argument type="service" id="evp_banking_history_integration.service.user_history_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
            <argument type="service" id="evp_blacklist.service.user_query_builder"/>
            <argument type="service" id="evp_blacklist.dow_jones_ai_manager"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="logger"/>
            <argument>true</argument>
            <argument>%evp_blacklist.worker.elasticsearch_return_count_limit%</argument>
            <argument type="service" id="evp_banking_history_integration.service.global_identification_manager"/>

            <tag name="remote_job_worker" worker_key="gateway.check_blacklist_updated_profile"/>
        </service>
    </services>
</container>
