<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <parameters>
        <parameter key="evp_blacklist.emails_to_notify" type="collection">
            <parameter><EMAIL></parameter>
        </parameter>
        <parameter key="evp_blacklist.monitor_key">evpbank.blacklist</parameter>
        <parameter key="evp_blacklist.command.retries.attempts_limit">5</parameter>
        <parameter key="evp_blacklist.command.retries.delay_between_attempts">1</parameter>
        <parameter key="evp_blacklist.ai_names_comparison_monitor_key">evpbank.blacklist.ai_names_comparison</parameter>
    </parameters>
    <services>

        <service id="evp_blacklist.command.blacklist_search"
                 class="Evp\Bundle\BlacklistBundle\Command\BlacklistSearchCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.repository.blacklist" />
            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="collection">
                <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
            </argument>
            <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
            <argument type="service" id="evp_blacklist.client.dow_jones_ai_client" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.blacklist_update"
                 class="Evp\Bundle\BlacklistBundle\Command\BlacklistUpdateCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.update_manager" />
        </service>

        <service id="evp_blacklist.command.self_test"
                 class="Evp\Bundle\BlacklistBundle\Command\BlacklistSelfTestCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.self_test"/>
            <argument type="collection">
                <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
            </argument>
        </service>

        <service id="evp_blacklist.command.update_client_blacklist"
                 class="Evp\Bundle\BlacklistBundle\Command\UpdateClientBlacklistCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
            <argument type="service" id="evp_client.client_activity_indicator" />
            <argument>%evp_blacklist.natural_person_check_frequency%</argument>
            <argument>%evp_blacklist.legal_person_check_frequency%</argument>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp.component.time.clock"/>
            <argument type="service" id="doctrine"/>
        </service>

        <service id="evp_blacklist.command.client_blacklist_check"
                 class="Evp\Bundle\BlacklistBundle\Command\ClientBlacklistCheckCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_client.repository.client" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.clients_blacklist_profile_check"
                 class="Evp\Bundle\BlacklistBundle\Command\ClientsBlacklistProfileCheckCommand">
            <tag name="console.command" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_client.repository.client" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
            <argument type="service" id="paysera.csv_reader" />
        </service>

        <service id="evp_blacklist.command.generate_periodic_clients_blacklist_profile_check_report"
                 class="Evp\Bundle\BlacklistBundle\Command\GeneratePeriodicClientsBlacklistProfileCheckReportCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result" />
            <argument type="service" id="evp_blacklist.periodical_check.client_periodic_check_report_generator" />
        </service>

        <service id="evp_blacklist.command.remove_profiles"
                 class="Evp\Bundle\BlacklistBundle\Command\RemoveProfilesCommand">
            <tag name="console.command" />

            <argument type="service" id="evp_blacklist.profile_remover" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile" />
            <argument type="service" id="evp_blacklist.repository.blacklist" />
        </service>

        <service id="evp_blacklist.command.manage_blacklist.create"
                 class="Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistCreateCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
        </service>

        <service id="evp_blacklist.command.manage_blacklist.delete"
                 class="Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistDeleteCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
        </service>

        <service id="evp_blacklist.command.manage_blacklist.flags"
                 class="Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistFlagsCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
        </service>

        <service id="evp_blacklist.command.manage_blacklist.licensed_partners"
                 class="Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistLicensedPartnersCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="evp_client.repository.licensed_partner"/>
        </service>

        <service id="evp_blacklist.command.manage_blacklist.update"
                 class="Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistUpdateCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
        </service>

        <service id="evp_blacklist.command.dow_jones_import.full_import"
                 class="Evp\Bundle\BlacklistBundle\Command\DowJonesImport\DowJonesFullImportCommand">
            <tag name="console.command"/>
            <tag name="monolog.logger" channel="paysera.dowjones_full_import"/>

            <argument type="service" id="evp_blacklist.service.dow_jones_import.full_import"/>
        </service>

        <service id="evp_blacklist.command.dow_jones_import.incremental_import"
                 class="Evp\Bundle\BlacklistBundle\Command\DowJonesImport\DowJonesIncrementalImportCommand">
            <tag name="console.command"/>
            <tag name="monolog.logger" channel="paysera.dowjones_incremental_import"/>

            <argument type="service" id="evp_blacklist.service.dow_jones_import.incremental_import"/>
        </service>

        <service id="evp_blacklist.command.static_blacklist_import"
                 class="Evp\Bundle\BlacklistBundle\Command\StaticBlacklistImportCommand">
            <tag name="console.command"/>
            <argument type="service" id="evp_blacklist.static_blacklist_importer" />
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.import_profiles_to_elasticsearch"
                 class="Evp\Bundle\BlacklistBundle\Command\ImportProfilesToElasticsearchCommand">
            <tag name="console.command"/>
            <argument type="service" id="evp_blacklist.search_provider.profile" />
            <argument type="service" id="fos_elastica.in_place_pager_persister" />
            <argument type="service" id="fos_elastica.index_manager" />
            <argument type="service" id="evp_blacklist.repository.blacklist" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="collection">
                <argument key="elasticsearch6">%evp_blacklist.blacklist_configuration_name_secondary_v1%</argument>
            </argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.generate_new_index_and_mark_as_importing"
                 class="Evp\Bundle\BlacklistBundle\Command\GenerateNewIndexAndMarkAsImportingCommand">
            <tag name="console.command"/>
            <argument type="collection">
                <argument key="elasticsearch6"
                          type="service" id="evp_blacklist.service.sanctions_version_manager_v1" />
            </argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.switch_active_index_and_remove_old_indexes"
                 class="Evp\Bundle\BlacklistBundle\Command\SwitchActiveIndexAndRemoveOldIndexesCommand">
            <tag name="console.command"/>
            <argument type="collection">
                <argument key="elasticsearch6"
                          type="service" id="evp_blacklist.service.sanctions_version_manager_v1" />
            </argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_blacklist.command.client_sanction_list_test_check"
                 class="Evp\Bundle\BlacklistBundle\Command\ClientSanctionListTestCheckCommand">
            <tag name="console.command"/>
            <argument type="service" id="paysera.csv_reader"/>
            <argument type="service" id="paysera.csv_writer"/>
            <argument type="service" id="evp_blacklist.manager"/>
            <argument>%evp_blacklist.blacklist_configuration_name_v1%</argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_blacklist.command.user_type_prediction_result_check"
                 class="Evp\Bundle\BlacklistBundle\Command\UserTypePredictionResultCheckCommand">
            <tag name="console.command"/>
            <argument type="service" id="evp_bank_transfer.client.user_type_ai_resolver"/>
        </service>

        <service id="evp_blacklist.command.periodical_blacklist_control_data_check"
                 class="Evp\Bundle\BlacklistBundle\Command\PeriodicalBlacklistControlDataCheckCommand">
            <tag name="console.command"/>
            <argument type="service" id="evp_blacklist.repository.blacklist" />
            <argument type="service" id="evp_blacklist.repository.blacklist_profile"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="doctrine"/>
            <argument type="service" id="paysera.console_progress_bar_helper"/>
            <argument type="service" id="evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager"/>
            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="logger"/>
            <argument>%evp_blacklist.command.retries.attempts_limit%</argument>
            <argument>%evp_blacklist.command.retries.delay_between_attempts%</argument>
        </service>

        <service id="evp_blacklist.command.monitor_periodical_blacklist_control_data_check"
                 class="Evp\Bundle\BlacklistBundle\Command\MonitorPeriodicalBlacklistControlDataCheckCommand">
            <tag name="console.command"/>

            <argument type="service" id="evp_blacklist.repository.blacklist_profile_data_check_log"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>P31D</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="DateInterval">
                    <argument>P1D</argument>
                </service>
            </argument>
            <argument>%evp_blacklist.monitor_key%</argument>
        </service>

        <service
                id="evp_blacklist.command.initialize_blacklist_profile_data_check_log"
                class="Evp\Bundle\BlacklistBundle\Command\InitializeBlacklistProfileDataCheckLogCommand">
            <tag name="console.command"/>

            <argument type="service" id="evp_blacklist.periodical_check.periodical_blacklist_control_data_check_initializer"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service
                id="evp_blacklist.command.send_reports"
                class="Evp\Bundle\BlacklistBundle\Command\BlacklistSendReportsCommand">
            <tag name="console.command"/>
            <argument type="service" id="evp_blacklist.repository.client_blacklist_check_result"/>
            <argument type="service" id="evp_blacklist.blacklist_sender"/>
        </service>

        <service
                id="evp_blacklist.command.maintenance.elastic_search.remove_index_command"
                class="Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch\RemoveIndexCommand">
            <tag name="console.command"/>

            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service
                id="evp_blacklist.command.maintenance.elastic_search.mark_index_as_importing_command"
                class="Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch\MarkIndexAsImportingCommand">
            <tag name="console.command"/>

            <argument type="service" id="evp_blacklist.configuration_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service
                id="evp_blacklist.command.maintenance.elastic_search.display_available_indexes_command"
                class="Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch\DisplayAvailableIndexesCommand">
            <tag name="console.command"/>

            <argument type="service" id="evp_blacklist.configuration_manager"/>
        </service>

        <service
                id="evp_blacklist.command.maintenance.whitelist.synchronise_whitelisted_profiles_command"
                class="Evp\Bundle\BlacklistBundle\Command\Maintenance\Whitelist\SynchronizeWhitelistedProfilesCommand">
            <tag name="console.command"/>

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="doctrine"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
            <argument type="service" id="paysera_transfer_surveillance.service.whitelisted_profile_manager"/>
            <argument type="service" id="evp_blacklist.service.blacklist_import_monitoring_service"/>
            <argument type="service" id="logger"/>
            <argument>10</argument>
            <argument>1</argument>
        </service>

        <service id="evp_blacklist.command.monitor_names_comparison_results"
                 class="Evp\Bundle\BlacklistBundle\Command\Monitoring\MonitorNamesComparisonResultsCommand">
            <tag name="console.command" command="paysera:blacklist:monitor-names-comparison-results"/>

            <argument type="service" id="evp_blacklist.repository.names_comparison_result_repository"/>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%evp_blacklist.ai_names_comparison_monitor_key%</argument>
        </service>

        <service id="evp_blacklist.command.monitor_names_comparison_answer_correctness"
                 class="Evp\Bundle\BlacklistBundle\Command\Monitoring\MonitorNamesComparisonAnswerCorrectnessCommand">
            <tag name="console.command" command="paysera:blacklist:monitor-names-comparison-answer-correctness"/>

            <argument type="service" id="evp_blacklist.profile_filter.ai_service.natural_name_comparison.4o-mini"/>
            <argument type="service" id="evp_blacklist.profile_filter.ai_service.legal_name_comparison.4o-mini"/>
            <argument type="service" id="paysera.console_progress_bar_helper"/>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument type="service" id="logger"/>
            <argument>%evp_blacklist.ai_names_comparison_monitor_key%</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="name1">Shakhboz Kamol Yuldashev</argument>
                    <argument key="name2">YULDASHEV SHAHBOZ</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">John Smith</argument>
                    <argument key="name2">Smith John</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">ნინო ჯაფარიძე</argument>
                    <argument key="name2">Nino Japaridze</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Maria Ivanova Petrova</argument>
                    <argument key="name2">Maria Petrova</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Eugenijus Vaičius</argument>
                    <argument key="name2">Eugenijaus Vaičiaus individuali veikla</argument>
                    <argument key="expected">false</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Oleksandr Prokopenko</argument>
                    <argument key="name2">Oleksandr and Svitlana Prokopenko</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Kvedaravičiūtė Vilija</argument>
                    <argument key="name2">Vilija Gintautė</argument>
                    <argument key="expected">false</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Георгий Иванович Смирнов</argument>
                    <argument key="name2">Georgiy Ivanovich Smirnov</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Anatoliy Volkov</argument>
                    <argument key="name2">Anatolii Volkov</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Alejandro García</argument>
                    <argument key="name2">Alex Garcia</argument>
                    <argument key="expected">false</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Dr. Emily Clark Ltd</argument>
                    <argument key="name2">Emily Clark</argument>
                    <argument key="expected">true</argument>
                </argument>
            </argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="name1">ABC Corporation Ltd.</argument>
                    <argument key="name2">ABC Corp. Ltd.</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">XYZ Holdings Incorporated</argument>
                    <argument key="name2">XYZ Holdings Inc</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Innovative Systems Pvt. Ltd.</argument>
                    <argument key="name2">Innovative Systems Private Limited</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">John &amp; Sons Enterprises</argument>
                    <argument key="name2">John and Sons Enterprises</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Elite Designs</argument>
                    <argument key="name2">Elite Design Studio</argument>
                    <argument key="expected">false</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Алексей Иванов ИП</argument>
                    <argument key="name2">Aleksey Ivanov Sole Proprietor</argument>
                    <argument key="expected">true</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Tokyo Solutions K.K.</argument>
                    <argument key="name2">Tokyo Solutions Corporation</argument>
                    <argument key="expected">false</argument>
                </argument>
                <argument type="collection">
                    <argument key="name1">Eco Green Ventures</argument>
                    <argument key="name2">EcoGreen Ventures Ltd.</argument>
                    <argument key="expected">true</argument>
                </argument>
            </argument>
        </service>
    </services>
</container>
