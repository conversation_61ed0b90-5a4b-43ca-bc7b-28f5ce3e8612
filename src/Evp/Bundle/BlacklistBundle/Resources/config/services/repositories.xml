<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_blacklist.repository.blacklist"
                 class="Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:Blacklist</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:Profile</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_category"
                 class="Evp\Bundle\BlacklistBundle\Repository\CategoryRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:Category</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_category_group"
                 class="Evp\Bundle\BlacklistBundle\Repository\CategoryGroupRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:CategoryGroup</argument>
        </service>

        <service id="evp_blacklist.repository.client_blacklist_check_result"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientBlacklistCheckResult</argument>
        </service>

        <service id="evp_blacklist.repository.client_blacklist_check_result_item"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientBlacklistCheckResultItem</argument>
        </service>

        <service id="evp_blacklist.repository.bank_code"
                 class="Evp\Bundle\BlacklistBundle\Repository\BankCodeRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:BankCode</argument>
        </service>

        <service id="evp_blacklist.repository.client_check_whitelist"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientCheckWhitelist</argument>
        </service>

        <service id="evp_blacklist.repository.client_check_whitelist_change_log"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistChangeLogRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientCheckWhitelistChangeLog</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_data_check_log"
                 class="Evp\Bundle\BlacklistBundle\Repository\BlacklistProfileDataCheckLogRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:BlacklistProfileDataCheckLog</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_client_sanctions_screening_comment"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningCommentRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientSanctionsScreeningComment</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_client_sanctions_screening_change_log"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningChangeLogRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientSanctionsScreeningChangeLog</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_client_sanctions_screening"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientSanctionsScreening</argument>
        </service>

        <service id="evp_blacklist.repository.profile_check_result_repository"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileCheckResultRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileCheckResult</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_role"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileRoleRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileRole</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_image"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileImageRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileImage</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_source"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileSourceRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileSource</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_sanctions_reference"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileSanctionsReferenceRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileSanctionsReference</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_note"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileNoteRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileNote</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_address"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileAddressRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileAddress</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_birth_places"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileBirthPlaceRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileBirthPlace</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_date_details"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileDateDetailRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileDateDetail</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_id_numbers"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileIdNumberRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileIdNumber</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_association"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileAssociationRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileAssociation</argument>
        </service>

        <service id="evp_blacklist.repository.blacklist_profile_relationship"
                 class="Evp\Bundle\BlacklistBundle\Repository\ProfileRelationshipRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ProfileRelationship</argument>
        </service>

        <service id="evp_blacklist.repository.client_blacklist_check_result_item_review_log_repository"
                 class="Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemReviewLogRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:ClientBlacklistCheckResultItemReviewLog</argument>
        </service>

        <service id="evp_blacklist.repository.names_comparison_result_repository"
                 class="Evp\Bundle\BlacklistBundle\Repository\NamesComparisonResultRepository"
                 lazy="true">
            <factory service="doctrine.orm.default_entity_manager" method="getRepository"/>
            <argument>EvpBlacklistBundle:NamesComparisonResult</argument>
        </service>
    </services>
</container>
