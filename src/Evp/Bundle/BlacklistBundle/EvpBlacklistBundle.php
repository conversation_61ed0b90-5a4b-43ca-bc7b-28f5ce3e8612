<?php

namespace Evp\Bundle\BlacklistBundle;

use Evp\Component\DependencyInjection\AddTaggedCompilerPass;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Evp\Bundle\BlacklistBundle\DependencyInjection\Compiler\BlacklistProviderCompilerPass;

class EvpBlacklistBundle extends Bundle
{
    /**
     * Builds the bundle.
     *
     * It is only ever called once when the cache is empty.
     *
     * This method can be overridden to register compilation passes,
     * other extensions, ...
     *
     * @param \Symfony\Component\DependencyInjection\ContainerBuilder $container
     */
    public function build(ContainerBuilder $container)
    {
        $container->addCompilerPass(new BlacklistProviderCompilerPass());

        $container->addCompilerPass(new AddTaggedCompilerPass(
            'evp_blacklist.message_processor.dow_jones_incremental_import',
            'evp_blacklist.dow_jones_incremental_import_processor',
            'addProcessor',
            ['key']
        ));

        $container->addCompilerPass(new AddTaggedCompilerPass(
            'evp_blacklist.profile_filter_registry',
            'evp_blacklist.profile_filter',
            'addFilter'
        ));
    }
}
