<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Controller;

use Evp\Bundle\BlacklistBundle\Controller\PublicBlacklistApiController;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistFilter;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\TestCaseBundle\Context\Functional\Database\PersistableWebTestCase;
use Paysera\Bundle\SecurityBundle\Service\AuthorizationChecker;
use Paysera\Component\Serializer\Entity\Result;
use PHPUnit\Framework\MockObject\MockObject;

class PublicBlacklistApiControllerTest extends PersistableWebTestCase
{
    private ?PublicBlacklistApiController $controller;
    private ?MockObject $blacklistRepository;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->blacklistRepository = $this->createMock(BlacklistRepository::class);
        $this->controller = new PublicBlacklistApiController(
            $this->createMock(AuthorizationChecker::class),
            $this->blacklistRepository
        );
    }

    public function testGetBlacklists(): void
    {
        $this->blacklistRepository
            ->expects(self::any())
            ->method('findByFilter')
            ->willReturn([
                new Blacklist('accuity', 'Accuity'),
                new Blacklist('dow_jones', 'Dow Jones'),
            ])
        ;
        /** @var Result $blacklists */
        $blacklists = $this->controller->getBlacklists(new BlacklistFilter());

        self::assertInstanceOf(Result::class, $blacklists);
        self::assertCount(2, $blacklists->getItems());
    }
}
