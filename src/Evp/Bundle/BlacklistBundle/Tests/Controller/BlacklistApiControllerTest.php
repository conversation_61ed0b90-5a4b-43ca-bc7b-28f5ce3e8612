<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Controller;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Tests\Fixtures\FixturesHelper;
use Evp\Bundle\MacAuthenticationBundle\Security\MacToken;
use Symfony\Bundle\FrameworkBundle\Routing\Router;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class BlacklistApiControllerTest extends PersistableWebTestCase
{
    private ?EntityManagerInterface $entityManager;
    private ?FixturesHelper $fixturesHelper;
    private ?TokenStorageInterface $tokenStorage;
    private ?Router $router;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->router = $this->getContainer()->get('router');
        $this->tokenStorage = $this->getContainer()->get('security.token_storage');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testGetUnauthorized(): void
    {
        $client = $this->fixturesHelper->createClient();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->entityManager->flush();

        $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);

        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                ['client_id' => $client->getCovenanteeId()]
            ),
            Request::METHOD_GET
        );

        $this->assertEquals(401, $response->getStatusCode());
    }

    public function testGetClientNotFound(): void
    {
        $this->mockAuth();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                ['client_id' => 1]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertEquals(400, $response->getStatusCode());
        $this->assertEquals('Client not found', $result['error_description']);
    }

    public function testGetSuccessCheckResultItems(): void
    {
        $this->mockAuth();

        $client = $this->fixturesHelper->createClient();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                ['client_id' => $client->getCovenanteeId()]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertNotEmpty($result['results']);
        $this->assertEquals(1, count($result['results']));

        foreach ($result['results'] as $result) {
            foreach ($result['result_items'] as $resultItem) {
                $this->assertEquals($profile->getId(), $resultItem['profile_id']);
                $this->assertEquals($profile->getFirstName(), $resultItem['profile_first_name']);
                $this->assertEquals($profile->getMiddleName(), $resultItem['profile_middle_name']);
                $this->assertEquals($profile->getLastName(), $resultItem['profile_last_name']);
                $this->assertEquals($profile->getCountry(), $resultItem['profile_country']);
                $this->assertEquals($profile->getTopCategoryGroup()->getName(), $resultItem['category_name']);
            }
        }
    }

    public function testGetSuccessWithoutAdditionalFilters(): void
    {
        $this->mockAuth();

        $client = $this->fixturesHelper->createClient();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->entityManager->flush();

        $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);

        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                ['client_id' => $client->getCovenanteeId()]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertNotEmpty($result['results']);
        $this->assertEquals(2, count($result['results']));

        foreach ($result['results'] as $result) {
            $this->assertEquals(1, count($result['result_items']));
        }
    }

    public function testGetSuccessFilteredByBlacklistId(): void
    {
        $this->mockAuth();

        $client = $this->fixturesHelper->createClient();
        $blacklist1 = $this->fixturesHelper->createBlacklist();
        $blacklist2 = $this->fixturesHelper->createBlacklist(__METHOD__, __METHOD__);
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->entityManager->flush();

        $blacklistCheckResult1 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist1, $profile);
        $blacklistCheckResult2 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist2, $profile);

        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                [
                    'client_id' => $client->getCovenanteeId(),
                    'blacklist_id' => $blacklist1->getId()
                ]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertNotEmpty($result['results']);
        $this->assertEquals(1, count($result['results']));
        $this->assertSame($result['results'][0]['id'], $blacklistCheckResult1->getId());
        $this->assertSame($result['results'][0]['blacklist']['id'], $blacklist1->getId());
    }

    public function testGetSuccessFilteredByCheckedAtFrom(): void
    {
        $this->mockAuth();

        $client = $this->fixturesHelper->createClient();
        $blacklist1 = $this->fixturesHelper->createBlacklist();
        $blacklist2 = $this->fixturesHelper->createBlacklist(__METHOD__, __METHOD__);
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->entityManager->flush();

        $blacklistCheckResult1 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist1, $profile);
        $blacklistCheckResult1->setCheckedAt(
            DateTime::createFromFormat('Y-m-d H:i:s', '2023-01-01 00:00:00')
        );

        $blacklistCheckResult2 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist2, $profile);
        $blacklistCheckResult2->setCheckedAt(
            DateTime::createFromFormat('Y-m-d H:i:s', '2024-01-01 00:00:00')
        );

        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                [
                    'client_id' => $client->getCovenanteeId(),
                    'checked_at_from' => DateTime::createFromFormat(
                        'Y-m-d H:i:s',
                        '2024-01-01 00:00:00'
                    )->getTimestamp()
                ]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertNotEmpty($result['results']);
        $this->assertEquals(1, count($result['results']));
        $this->assertSame($result['results'][0]['id'], $blacklistCheckResult2->getId());
    }

    public function testGetSuccessFilteredByCheckedAtTo(): void
    {
        $this->mockAuth();

        $client = $this->fixturesHelper->createClient();
        $blacklist1 = $this->fixturesHelper->createBlacklist();
        $blacklist2 = $this->fixturesHelper->createBlacklist(__METHOD__, __METHOD__);
        $profile = $this->fixturesHelper->createBlacklistProfile();

        $this->entityManager->flush();

        $blacklistCheckResult1 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist1, $profile);
        $blacklistCheckResult1->setCheckedAt(
            DateTime::createFromFormat('Y-m-d H:i:s', '2023-01-01 00:00:00')
        );

        $blacklistCheckResult2 = $this->fixturesHelper->createBlacklistCheckResult($client, $blacklist2, $profile);
        $blacklistCheckResult2->setCheckedAt(
            DateTime::createFromFormat('Y-m-d H:i:s', '2024-01-01 00:00:00')
        );

        $this->entityManager->flush();

        $response = $this->makeRequestWithData(
            [],
            $this->router->generate(
                'EvpBlacklistBundle_get_blacklist_check_results',
                [
                    'client_id' => $client->getCovenanteeId(),
                    'checked_at_to' => DateTime::createFromFormat(
                        'Y-m-d H:i:s',
                        '2023-01-01 00:00:00'
                    )->getTimestamp()
                ]
            ),
            Request::METHOD_GET
        );

        $result = json_decode($response->getContent(), true);

        $this->assertNotEmpty($result['results']);
        $this->assertEquals(1, count($result['results']));
        $this->assertSame($result['results'][0]['id'], $blacklistCheckResult1->getId());
    }

    private function mockAuth()
    {
        $token = new MacToken(['IS_AUTHENTICATED_ANONYMOUSLY']);
        $token->setAuthenticated(true);
        $this->tokenStorage->setToken($token);
    }
}
