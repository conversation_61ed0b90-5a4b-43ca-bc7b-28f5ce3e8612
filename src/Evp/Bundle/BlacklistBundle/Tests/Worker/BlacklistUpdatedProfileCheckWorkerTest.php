<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Worker;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankingHistoryClientBundle\Client\UserHistoryClient;
use Evp\Bundle\BankingHistoryIntegrationBundle\Service\UserHistoryManager;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistUpdatedProfileCheckWorker;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisher;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class BlacklistUpdatedProfileCheckWorkerTest extends PersistableWebTestCase
{
    private EntityManager $entityManager;
    private BlacklistUpdatedProfileCheckWorker $blacklistUpdatedProfileCheckWorker;
    private FixturesHelper $fixturesHelper;
    /** @var MockObject|UserHistoryClient */
    private $userHistoryClient;
    /** @var MockObject|RemoteJobPublisher */
    private $remoteJobPublisher;

    protected function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();

        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->userHistoryClient = $this->createMock(UserHistoryClient::class);
        $userHistoryManager = new UserHistoryManager(
            $this->userHistoryClient,
            $this->getContainer()->get('logger')
        );

        $this->remoteJobPublisher = $this->createMock(RemoteJobPublisher::class);

        $this->blacklistUpdatedProfileCheckWorker = new BlacklistUpdatedProfileCheckWorker(
            $userHistoryManager,
            $this->getContainer()->get('evp_blacklist.repository.blacklist_profile'),
            $this->getContainer()->get('evp_blacklist.service.user_query_builder'),
            $this->getContainer()->get('evp_blacklist.dow_jones_ai_manager'),
            $this->getContainer()->get('evp_client.repository.client'),
            $this->remoteJobPublisher,
            $this->getContainer()->get('logger'),
            false,
            500,
            $this->getContainer()->get('evp_banking_history_integration.service.global_identification_manager'),
        );
    }

    public function testWorker()
    {
        $blacklist = $this->fixturesHelper->createBlacklist();
        $this->fixturesHelper->createPersonProfile(
            $blacklist,
            'John',
            'Doe'
        );
        $this->fixturesHelper->createClientNatural(
            1,
            IdentificationLevels::IDENTIFIED,
            'John',
            'Doe'
        );
        $this->fixturesHelper->createClientLegal(2);

        $this->entityManager->flush();

        $this->userHistoryClient
            ->expects($this->once())
            ->method('searchUsers')
            ->willReturn($this->bankingHistoryResponseTemplate())
        ;

        $this->remoteJobPublisher
            ->expects($this->exactly(2))
            ->method('publishJob')
        ;

        $this->blacklistUpdatedProfileCheckWorker->work([
            'id' => 1
        ]);
    }

    private function bankingHistoryResponseTemplate(): array
    {
        return [
            'hits' => [
                'hits' => [
                    [
                        '_source' => [
                            'id' => 1,
                            'first_name' => 'John',
                            'last_name' => 'Doe',
                            'date_of_birth' => '2000-01-01',
                            'type' => 'natural',
                        ],
                    ],
                    [
                        '_source' => [
                            'id' => 1,
                            'first_name' => 'John',
                            'last_name' => 'Doe',
                            'date_of_birth' => '2000-01-01',
                            'type' => 'natural',
                        ],
                    ],
                    [
                        '_source' => [
                            'id' => 2,
                            'legal_name' => 'UAB Testas',
                            'type' => 'legal',
                            'real_beneficiaries' => [
                                [
                                    'name' => 'John Doe',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
