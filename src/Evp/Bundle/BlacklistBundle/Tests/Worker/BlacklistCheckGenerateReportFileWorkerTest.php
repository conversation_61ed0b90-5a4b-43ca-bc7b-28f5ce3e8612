<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Worker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Service\UserFileExporter;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckGenerateReportFileWorker;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class BlacklistCheckGenerateReportFileWorkerTest extends PersistableWebTestCase
{
    private MockObject $userFileExporter;
    private BlacklistCheckGenerateReportFileWorker $worker;
    private EntityManagerInterface $entityManager;
    private ?FixturesHelper $fixturesHelper;

    public function setUp(): void
    {
        $client = $this->createClientWithNewDatabase();
        $container = $client->getContainer();

        $this->userFileExporter = $this->createMock(UserFileExporter::class);
        $container->set('evp_bundle_blacklist.service.user_file_exporter', $this->userFileExporter);

        $this->entityManager = $container->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->worker = $container->get('evp_bundle_blacklist.worker.blacklist_check_generate_report_file_worker');
    }

    public function testWorker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklist = ($this->fixturesHelper->createBlacklist())->setPeriodicCheckReportNeeded(true);
        $this->entityManager->flush();

        $result = $this->fixturesHelper->createClientBlacklistCheckResult(false, $blacklist);
        $this->entityManager->flush();

        $this->userFileExporter->expects($this->once())->method('uploadDocumentForClient');

        $this->worker->work([
            'clientId' => $client->getId(),
            'clientBlacklistCheckResultId' => $result->getId()
        ]);

        $this->expectInfoLog('Generated a blacklist check report file for a client.');
    }

    public function testWorkerReportIsNotNeeded(): void
    {
        $client = $this->fixturesHelper->createClientNatural(2);
        $blacklist = ($this->fixturesHelper->createBlacklist('test', 'test'));
        $this->entityManager->flush();

        $result = $this->fixturesHelper->createClientBlacklistCheckResult(false, $blacklist);
        $this->entityManager->flush();

        $this->userFileExporter->expects($this->exactly(0))->method('uploadDocumentForClient');

        $this->worker->work([
            'clientId' => $client->getId(),
            'clientBlacklistCheckResultId' => $result->getId()
        ]);

        $this->expectInfoLog('Blacklist check report file generation is not needed for a blacklist.');
    }
}
