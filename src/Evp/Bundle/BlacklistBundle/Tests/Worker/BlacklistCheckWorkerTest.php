<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Worker;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Evp\Bundle\BankTransferBundle\Tests\Functional\TransferInEvent\DummyPublisher;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistFilterService;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckGenerateReportFileWorker;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Repository\PartnerClientRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\QuestionnaireBundle\Repository\QuestionnaireRepository;
use Evp\Bundle\RabbitMqExtensionBundle\Exception\InvalidDataException;
use Evp\Bundle\RabbitMqExtensionBundle\Service\DeferredRemoteJobPublisher;
use Exception;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class BlacklistCheckWorkerTest extends PersistableWebTestCase
{
    private FixturesHelper $fixturesHelper;
    private EntityManagerInterface $entityManager;

    /**
     * @var BlacklistFilterService|MockObject
     */
    private $blacklistFilterService;

    /**
     * @var QuestionnaireRepository|MockObject
     */
    private $questionnaireRepository;

    /**
     * @var PartnerClientRepository|MockObject
     */
    private $partnerClientRepository;

    private BlacklistCheckWorker $blacklistCheckWorker;

    private ClientBlacklistCheckResultRepository $blacklistCheckResultRepository;
    private ClientSanctionsScreeningRepository $clientSanctionsScreeningRepository;
    private ?DummyPublisher $publisher;
    private ?DeferredRemoteJobPublisher $deferredPublisher;
    private string $aiConfidence;
    private MonitoringClient $monitoringClient;
    private string $monitoringKey;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();

        $this->publisher = new DummyPublisher();
        $this->getContainer()->set('dummy_publisher', $this->publisher);

        $this->getContainer()->set('evp_rabbit_mq_extension.deferred_remote_job_publisher',new DeferredRemoteJobPublisher(
            $this->getContainer()->get('doctrine.orm.default_entity_manager'),
            $this->publisher
        ));

        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $clientRepository = $this->getContainer()->get('evp_client.repository.client');
        $logger = $this->getContainer()->get('logger');
        $this->blacklistFilterService = $this->createMock(BlacklistFilterService::class);
        $this->questionnaireRepository = $this->createMock(QuestionnaireRepository::class);
        $this->partnerClientRepository = $this->createMock(PartnerClientRepository::class);
        $this->deferredPublisher = $this->getContainer()->get('evp_rabbit_mq_extension.deferred_remote_job_publisher');
        $this->aiConfidence = '90';
        $this->monitoringClient = $this->getContainer()->get('paysera_monitoring.monitoring_client');
        $this->monitoringKey = $this->getContainer()->getParameter('evp_blacklist.worker.blacklist_check.monitor_key');

        $this->blacklistCheckWorker = new BlacklistCheckWorker(
            $this->entityManager,
            $clientRepository,
            $logger,
            $this->blacklistFilterService,
            $this->getContainer()->get('evp_blacklist.repository.blacklist'),
            $this->questionnaireRepository,
            $this->partnerClientRepository,
            $this->getContainer()->get('event_dispatcher'),
            $this->getContainer()->get('evp_rabbit_mq_extension.deferred_remote_job_publisher'),
            $this->aiConfidence,
            $this->monitoringClient,
            $this->monitoringKey,
            $this->getContainer()->get('evp_banking_history_integration.service.global_identification_manager'),
            $this->getContainer()->getParameter('evp_blacklist.worker.blacklist_check.global_identification_id_wait_timeout'),
        );

        $this->blacklistCheckResultRepository = $this->getContainer()
            ->get('evp_blacklist.repository.client_blacklist_check_result')
        ;
        $this->clientSanctionsScreeningRepository = $this->getContainer()
            ->get('evp_blacklist.repository.blacklist_client_sanctions_screening')
        ;
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testWithNaturalClient(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);

        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturn([])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()],
            'purpose' => BlacklistCheckWorker::PURPOSE_BLACKLIST_DATA_EXPORT,
        ]);

        $this->deferredPublisher->onCommit();

        $checkResults = $this->blacklistCheckResultRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $checkResults);
        $this->assertEquals($blacklists[0]->getId(), $checkResults[0]->getBlacklist()->getId());

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(0, $sanctions);

        $targetJobPublishedCount = 0;

        foreach ($this->publisher->getJobsPublished() as $job) {
            if ($job[0] === BlacklistCheckGenerateReportFileWorker::JOB_CHECK_BLACKLIST_USER_GENERATE_REPORT_FILE) {
                $targetJobPublishedCount++;
            }
        }

        $this->assertEquals(2, $targetJobPublishedCount);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     * @dataProvider blacklistedNaturalClientDataProvider
     */
    public function testWithBlacklistedNaturalClient(
        int $sanctionScreeningCount,
        int $sanctionScreeningExpectedCount,
        string $status = ClientSanctionsScreening::STATUS_NEW
    ): void
    {
        $client = $this->fixturesHelper->createClientNatural(1,IdentificationLevels::IDENTIFIED);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        if ($sanctionScreeningCount) {
            foreach ($blacklists as $blacklist) {
                $checkResult = $this->fixturesHelper
                    ->createClientBlacklistCheckResult()
                    ->setClient($client)
                    ->setBlacklist($blacklist);

                for ($i = 0; $i < $sanctionScreeningCount; $i++) {
                    $this->fixturesHelper->createClientSanctionsScreening(
                        $client,
                        $checkResult,
                        $status
                    );
                }
            }
        }

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy(['client' => $client->getId(), 'blacklisted' => true]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount($sanctionScreeningExpectedCount, $sanctions);
    }

    public function blacklistedNaturalClientDataProvider(): array
    {
        return [
            'sanction screening client exists' => [
                'existsForBlacklist' => 1,
                'expected' => 5,
            ],
            'sanction screening clients exist' => [
                'existsForBlacklist' => 2,
                'expected' => 10,
            ],
            'sanction screening client not exists' => [
                'existsForBlacklist' => 0,
                'expected' => 2,
            ],
            'completed sanction screening' => [
                'existsForBlacklist' => 1,
                'expected' => 7,
                ClientSanctionsScreening::STATUS_COMPLETED,
            ]
        ];
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testWithBlacklistedNaturalClientGenerateReport(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::IDENTIFIED);
        $this->entityManager->flush();

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        foreach ($blacklists as $blacklist) {
            $this->fixturesHelper->createClientSanctionsScreening(
                $client,
                $this->fixturesHelper->createClientBlacklistCheckResult()
                    ->setClient($client)
                    ->setBlacklist($blacklist),
                ClientSanctionsScreening::STATUS_COMPLETED
            );
        }

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()],
            'purpose' => BlacklistCheckWorker::PURPOSE_BLACKLIST_DATA_EXPORT,
        ]);

        $this->deferredPublisher->onCommit();

        $targetJobPublishedCount = 0;
        foreach ($this->publisher->getJobsPublished() as $job) {
            if ($job[0] === BlacklistCheckGenerateReportFileWorker::JOB_CHECK_BLACKLIST_USER_GENERATE_REPORT_FILE) {
                $targetJobPublishedCount++;
            }
        }

        $this->assertEquals(2, $targetJobPublishedCount);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testWithLegalClient(): void
    {
        $client = $this->fixturesHelper->createClientLegal();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $this->prepareBlacklists($licensedPartner);

        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturn([])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(0, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testWithBlacklistedLegalClient(): void
    {
        $client = $this->fixturesHelper->createClientLegal();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithCompanyProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testLegalClientWithRealBeneficiary(): void
    {
        $client = $this->fixturesHelper->createClientLegal(100);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $this->prepareBlacklists($licensedPartner);
        $legalQuestionnaire = $this->fixturesHelper->createLegalQuestionnaire();

        $this->questionnaireRepository
            ->expects($this->once())
            ->method('findLastByClient')
            ->willReturn($legalQuestionnaire)
        ;

        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturn([])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->blacklistFilterService
            ->method('getRealBeneficiaryCheckResults')
            ->willReturn([])
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy([
            'client' => $client->getId(),
            'blacklisted' => false,
        ]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(0, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testLegalClientWithBlacklistedRealBeneficiary(): void
    {
        $client = $this->fixturesHelper->createClientLegal(100);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $legalQuestionnaire = $this->fixturesHelper->createLegalQuestionnaire();
        $this->entityManager->flush();

        $this->questionnaireRepository
            ->expects($this->once())
            ->method('findLastByClient')
            ->willReturn($legalQuestionnaire)
        ;

        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturn([])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);

        $this->blacklistFilterService
            ->method('getRealBeneficiaryCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy([
            'client' => $client->getId(),
            'blacklisted' => true,
        ]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testDowJonesAiCheck()
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::IDENTIFIED);;
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy(
            [
                'client' => $client->getId(),
                'blacklisted' => true
            ]
        );
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testDowJonesAiCheckLowConfidence()
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::IDENTIFIED);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy([
            'client' => $client->getId(),
            'blacklisted' => true,
        ]);
        $this->assertCount(2, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testDowJonesAiCheckMultipleClients()
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::IDENTIFIED);
        $secondClient = $this->fixturesHelper->createClientNatural(
            2,
            IdentificationLevels::IDENTIFIED,
            'Testas2'
        );
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklists = $this->prepareBlacklists($licensedPartner);
        $this->entityManager->flush();

        $blacklistResults = $this->createResultsWithPersonProfile($blacklists);
        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturnCallback(fn (Blacklist $blacklist) => $blacklistResults[$blacklist->getId()])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId(), $secondClient->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy([
            'client' => [$client->getId(), $secondClient->getId()],
            'blacklisted' => true,
        ]);
        $this->assertCount(4, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(2, $sanctions);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $secondClient->getId()]);
        $this->assertCount(2, $sanctions);
    }

    /**
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     */
    public function testDowJonesAiCheckMultipleResults()
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::IDENTIFIED);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $partnerClient = $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $blacklist = $this->fixturesHelper->createBlacklist()
            ->setLicensedPartners(new ArrayCollection([$licensedPartner]))
            ->setPeriodicCheckNeeded(true)
        ;

        $profile = $this->fixturesHelper->createPersonProfile($blacklist);
        $blacklistResult = $this->fixturesHelper->createBlacklistResult($profile);

        $this->blacklistFilterService
            ->method('getClientCheckResults')
            ->willReturn([$blacklistResult])
        ;

        $this->partnerClientRepository
            ->method('findOneLatestByClient')
            ->willReturn($partnerClient)
        ;

        $this->entityManager->flush();

        $this->blacklistCheckWorker->work([
            'clients' => [$client->getId()]
        ]);

        $count = $this->blacklistCheckResultRepository->findBy([
            'client' => $client->getId(),
            'blacklisted' => true,
        ]);
        $this->assertCount(1, $count);

        $sanctions = $this->clientSanctionsScreeningRepository->findBy(['client' => $client->getId()]);
        $this->assertCount(1, $sanctions);
    }

    /**
     * @param Blacklist[] $blacklists
     * @return BlacklistResult[][]
     */
    private function createResultsWithPersonProfile(array $blacklists): array
    {
        $blacklistResults = [];

        foreach ($blacklists as $blacklist) {
            $profile = $this->fixturesHelper->createPersonProfile(
                $blacklist,
                'First',
                'Last',
                'lt',
                null,
                Profile::TYPE_PERSON,
                null,
                $this->fixturesHelper->createCategory($blacklist->getId())
            );

            $blacklistResults[$blacklist->getId()] = [
                $this->fixturesHelper->createBlacklistResult($profile),
            ];
        }

        return $blacklistResults;
    }

    /**
     * @param Blacklist[] $blacklists
     * @return BlacklistResult[][]
     */
    private function createResultsWithCompanyProfile(array $blacklists): array
    {
        $blacklistResults = [];

        foreach ($blacklists as $blacklist) {
            $profile = $this->fixturesHelper->createCompanyProfile(
                $blacklist,
                'Company-name',
                $this->fixturesHelper->createCategory($blacklist->getId())
            );

            $blacklistResults[$blacklist->getId()] = [
                $this->fixturesHelper->createBlacklistResult($profile),
            ];
        }

        return $blacklistResults;
    }

    /**
     * @return Blacklist[]
     */
    private function prepareBlacklists(LicensedPartner $licensedPartner): array
    {
        return [
            $this->fixturesHelper->createBlacklist('External', 'external')
                ->setExternal(true)
                ->setPeriodicCheckNeeded(true)
                ->setLicensedPartners(new ArrayCollection([$licensedPartner])),
            $this->fixturesHelper->createBlacklist('Related to partner', 'related-to-partner')
                ->setPeriodicCheckNeeded(true)
                ->setLicensedPartners(new ArrayCollection([$licensedPartner])),
            $this->fixturesHelper->createBlacklist('Related but deprecated', 'related-but-deprecated')
                ->setDeprecated(true)
                ->setPeriodicCheckNeeded(true)
                ->setLicensedPartners(new ArrayCollection([$licensedPartner])),
            $this->fixturesHelper->createBlacklist('Related but not need check', 'related-but-not-need-check')
                ->setPeriodicCheckNeeded(false)
                ->setLicensedPartners(new ArrayCollection([$licensedPartner])),
            $this->fixturesHelper->createBlacklist('Not related', 'not-related')
                ->setPeriodicCheckNeeded(true),
        ];
    }
}
