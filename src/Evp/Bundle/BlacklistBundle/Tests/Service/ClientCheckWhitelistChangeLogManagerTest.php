<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\UnitOfWork;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelistChangeLog;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistChangeLogManager;
use Generator;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Sonata\UserBundle\Entity\BaseUser;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;

class ClientCheckWhitelistChangeLogManagerTest extends TestCase
{

    private FixturesHelper $fixturesHelper;
    /**
     * @var MockObject|EntityManagerInterface
     */
    private $entityManagerMock;
    /**
     * @var MockObject|TokenStorage
     */
    private $tokenStorage;
    private ClientCheckWhitelistChangeLogManager $manager;

    protected function setUp(): void
    {
        $this->fixturesHelper = new FixturesHelper();
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $logger = $this->createMock(LoggerInterface::class);
        $this->tokenStorage = $this->createMock(TokenStorage::class);
        $this->manager = new ClientCheckWhitelistChangeLogManager(
            $this->entityManagerMock,
            $logger,
            $this->tokenStorage
        );
    }

    /**
     * @dataProvider dataProviderLog
     */
    public function testLog(string $action): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $categoryGroup = $this->fixturesHelper->createCategoryGroup();
        $profile = $this->fixturesHelper->createPersonProfile($blacklist)->setExternalId('123');
        $token = $this->createMock(TokenInterface::class);
        $baseUser = $this->createMock(BaseUser::class);
        $classMetadataMock = $this->createMock(ClassMetadata::class);

        $clientCheckWhitelist = $this->fixturesHelper->createClientCheckWhitelist(
            $client,
            $blacklist,
            $categoryGroup,
            $profile->getExternalId()
        );

        if ($action === ClientCheckWhitelistChangeLog::ACTION_MODIFY) {
            $unitOfWorkMock = $this->createMock(UnitOfWork::class);
            $this->entityManagerMock->expects($this->once())
                ->method('getUnitOfWork')
                ->willReturn($unitOfWorkMock)
            ;

            $blacklistNew = $this->fixturesHelper->createBlacklist('Paysera V1', Blacklist::KEY_PAYSERA_V1);

            $unitOfWorkMock->expects($this->once())
                ->method('getOriginalEntityData')
                ->willReturn(
                    [
                        'id' => 1,
                        'client' => $this->fixturesHelper->createClientNatural(2),
                        'blacklist' => $blacklistNew,
                        'categoryGroup' => $this->fixturesHelper->createCategoryGroup()->setKey(CategoryGroup::GROUP_KEY_SOR),
                        'profileExternalId' => $this->fixturesHelper->createPersonProfile($blacklistNew)->setExternalId('321'),
                    ]
                )
            ;
        }

        $this->entityManagerMock->expects($this->once())
            ->method('getClassMetadata')
            ->with(ClientCheckWhitelist::class)
            ->willReturn($classMetadataMock)
        ;

        $classMetadataMock->expects($this->once())
            ->method('getFieldNames')
            ->willReturn(
                [
                    'id',
                    'profileExternalId',
                ]
            )
        ;

        $classMetadataMock->expects($this->once())
            ->method('getAssociationNames')
            ->willReturn(
                [
                    'client',
                    'blacklist',
                    'categoryGroup',
                ]
            )
        ;

        $classMetadataMock->expects($this->exactly(4))
            ->method('getFieldValue')
            ->will(
                $this->returnValueMap(
                    [
                        [$clientCheckWhitelist, ClientCheckWhitelistChangeLog::FIELD_CLIENT, $clientCheckWhitelist->getClient()],
                        [$clientCheckWhitelist, ClientCheckWhitelistChangeLog::FIELD_BLACKLIST, $clientCheckWhitelist->getBlacklist()],
                        [$clientCheckWhitelist, ClientCheckWhitelistChangeLog::FIELD_CATEGORY_GROUP, $clientCheckWhitelist->getCategoryGroup()],
                        [$clientCheckWhitelist, ClientCheckWhitelistChangeLog::FIELD_PROFILE_EXTERNAL_ID, $clientCheckWhitelist->getProfileExternalId()],
                    ]
                )
            )
        ;

        $this->entityManagerMock->expects($this->exactly(4))
            ->method('persist')
            ->with(
                $this->callback(
                    function (object $entity) use ($action) {
                        if (!$entity instanceof ClientCheckWhitelistChangeLog) {
                            return false;
                        }

                        if ($action !== $entity->getAction()) {
                            return false;
                        }

                        return true;
                    }
                )
            )
        ;

        $baseUser->expects($this->once())
            ->method('getId')
            ->willReturn(1)
        ;

        $token->expects($this->once())
            ->method('getUser')
            ->willReturn($baseUser)
        ;

        $this->tokenStorage->expects($this->once())
            ->method('getToken')
            ->willReturn($token)
        ;

        $this->manager->log($clientCheckWhitelist, $action);
    }

    public function dataProviderLog(): Generator
    {
        yield 'create action' => [
            'action' => ClientCheckWhitelistChangeLog::ACTION_CREATE,
        ];
        yield 'modify action' => [
            'action' => ClientCheckWhitelistChangeLog::ACTION_MODIFY,
        ];
        yield 'delete action' => [
            'action' => ClientCheckWhitelistChangeLog::ACTION_DELETE,
        ];
    }
}
