<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use DMS\PHPUnitExtensions\ArraySubset\ArraySubsetAsserts;
use Evp\Bundle\BankTransferBundle\Entity\ClientTypePrediction;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAdditionalInformation;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\Helper\WordCounter;
use Evp\Bundle\BlacklistBundle\Service\ProfileQueryBuilder;
use Evp\Bundle\BlacklistBundle\Service\Resolver\ProfileTypeResolver;
use Evp\Tests\BaseTestCase;
use Exception;

class ProfileQueryBuilderTest extends BaseTestCase
{
    use ArraySubsetAsserts;

    /**
     * @dataProvider queryBuildTestDataProvider
     * @param Profile $profile
     * @param string $configurationName
     * @param array $expectedQuery
     * @throws Exception
     */
    public function testQueryBuild(
        Profile $profile,
        string $configurationName,
        array $expectedQuery,
        bool $useAssertEqualsCanonicalizing = false
    ): void {
        $transliterator = $this->getContainer()->get('evp_util.transliterate_utilities_inbuilt');
        $abbreviationsManager = $this->getContainer()->get('evp_blacklist.service.abbreviations_manager');
        $queryData = (
            new ProfileQueryBuilder(
                $abbreviationsManager,
                new WordCounter(),
                $transliterator,
                new ProfileTypeResolver(),
                '0.99'
            )
        )
            ->build($profile, $configurationName);

        if ($useAssertEqualsCanonicalizing) {
            $this->assertEqualsCanonicalizing($expectedQuery, $queryData);
        } else {
            self::assertArraySubset($expectedQuery, $queryData);
        }
    }

    public function queryBuildTestDataProvider(): array
    {
        return [
            'displayName consists of 1 word' => [
                (new Profile())
                    ->setFirstName('Dmitriy'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'Dmitriy',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'Dmitriy',
                                                    ]
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ]
            ],
            'displayName consists of 2 words, one is less than minimum, not filtered' => [
                (new Profile())
                    ->setFirstName('COMPANY SDF'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'COMPANY SDF',
                                                        'minimum_should_match' => '100%',
                                                        'boost' => 2,
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'COMPANY SDF',
                                                        'minimum_should_match' => '100%',
                                                        'boost' => 2,
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'COMPANY SDF',
                                                        'minimum_should_match' => '100%',
                                                        'fuzziness' => 'AUTO:4,10',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'COMPANY SDF',
                                                        'minimum_should_match' => '100%',
                                                        'fuzziness' => 'AUTO:4,10',
                                                    ]
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ]
            ],
            'displayName consists of 2 words, both words are greater than minimum, filtered second word' => [
                (new Profile())
                    ->setFirstName('COMPANY SIA'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'COMPANY SIA',
                                                        'minimum_should_match' => '100%',
                                                        'boost' => 2,
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'COMPANY SIA',
                                                        'minimum_should_match' => '100%',
                                                        'boost' => 2,
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'COMPANY SIA',
                                                        'minimum_should_match' => '100%',
                                                        'fuzziness' => 'AUTO:4,10',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'COMPANY SIA',
                                                        'minimum_should_match' => '100%',
                                                        'fuzziness' => 'AUTO:4,10',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'bool' => [
                                                    'should' => [
                                                        [
                                                            'match' => [
                                                                'displayName' => [
                                                                    'query' => 'COMPANY',
                                                                    'minimum_should_match' => '100%',
                                                                ]
                                                            ]
                                                        ],
                                                        [
                                                            'match' => [
                                                                'alternative_names.displayName' => [
                                                                    'query' => 'COMPANY',
                                                                    'minimum_should_match' => '100%',
                                                                ]
                                                            ]
                                                        ],
                                                    ],
                                                    'filter' => [
                                                        [
                                                            'term' => [
                                                                'type' => 'company',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ]
            ],
            'displayName consists of 3 words' => [
                (new Profile())
                    ->setFirstName('Dmitriy')
                    ->setMiddleName('Anatolevich')
                    ->setLastName('Pavlov'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                'bool' => [
                                    'should' => [
                                        [
                                            'match' => [
                                                'displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '75%',
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'alternative_names.displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '75%',
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '100%',
                                                    'boost' => 2,
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'alternative_names.displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '100%',
                                                    'boost' => 2,
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '75%',
                                                    'fuzziness' => 'AUTO:4,10',
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'alternative_names.displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '75%',
                                                    'fuzziness' => 'AUTO:4,10',
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '100%',
                                                    'boost' => 2,
                                                    'fuzziness' => 'AUTO:4,10',
                                                ]
                                            ]
                                        ],
                                        [
                                            'match' => [
                                                'alternative_names.displayName' => [
                                                    'query' => 'Dmitriy Anatolevich Pavlov',
                                                    'minimum_should_match' => '100%',
                                                    'boost' => 2,
                                                    'fuzziness' => 'AUTO:4,10',
                                                ]
                                            ]
                                        ],
                                    ],
                                ],
                                ],
                            ],
                            'should' => [
                                [
                                    'match' => [
                                        'mixed_name' => [
                                            'query' => 'Dmitriy Anatolevich Pavlov',
                                            'cutoff_frequency' => 0.00001,
                                        ]
                                    ]
                                ],
                            ]
                        ],
                    ],
                ]
            ],
            'displayName consists of 9 words' => [
                (new Profile())
                    ->setFirstName('Dmitriy')
                    ->setMiddleName('Anatolevich')
                    ->setLastName('Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'Dmitriy Anatolevich Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov',
                                                        'minimum_should_match' => '25%',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'Dmitriy Anatolevich Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov',
                                                        'minimum_should_match' => '25%',
                                                    ]
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'should' => [
                                [
                                    'match' => [
                                        'mixed_name' => [
                                            'query' => 'Dmitriy Anatolevich Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov Pavlov',
                                            'cutoff_frequency' => 0.00001,
                                        ]
                                    ]
                                ],
                            ]
                        ],
                    ],
                ]
            ],
            'birthday' => [
                (new Profile())
                    ->setFirstName('Dmitriy')
                    ->setMiddleName('Anatolevich')
                    ->setLastName('Pavlov')
                    ->setBirthday('2000-01-01'),
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'filter' => [
                                'bool' => [
                                    'must' => [
                                        [
                                            'bool' => [
                                                'should' => [
                                                    [
                                                        'term' => [
                                                            'birthday' => '2000-01-01'
                                                        ]
                                                    ],
                                                    [
                                                        'bool' => ['must_not' => ['exists' => ['field' => 'birthday']]],
                                                    ],
                                                    [
                                                        'term' => [
                                                            'birthday' => '2000',
                                                        ],
                                                    ],
                                                    [
                                                        'term' => [
                                                            'alternative_birthdays.date' => '2000',
                                                        ],
                                                    ],
                                                    [
                                                        'term' => [
                                                            'alternative_birthdays.date' => '2000-01-01'
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'profile type person' => [
                (new Profile())
                    ->setFirstName('Dmitriy')
                    ->setMiddleName('Anatolevich')
                    ->setLastName('Pavlov')
                    ->setClientTypePrediction(
                        (new ClientTypePrediction())
                        ->setClientName('Dmitriy Anatolevich Pavlov')
                        ->setClientType(PartyAdditionalInformation::TYPE_NATURAL)
                        ->setConfidence(1)
                    )
                ,
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                1 => [
                                    'match' => [
                                        'type' => ['query' => 'person']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'profile type company' => [
                (new Profile())
                    ->setName('Test Company')
                    ->setClientTypePrediction(
                        (new ClientTypePrediction())
                            ->setClientName('Test Company')
                            ->setClientType(PartyAdditionalInformation::TYPE_LEGAL)
                            ->setConfidence(1)
                    )
                ,
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                1 => [
                                    'match' => [
                                        'type' => ['query' => 'company']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'not included profile type company because confidence value too small' => [
                (new Profile())
                    ->setName('Company')
                    ->setClientTypePrediction(
                        (new ClientTypePrediction())
                            ->setClientName('Company')
                            ->setClientType(PartyAdditionalInformation::TYPE_LEGAL)
                            ->setConfidence(0.9)
                    )
                ,
                $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1'),
                [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'match' => [
                                                    'displayName' => [
                                                        'query' => 'Company',
                                                    ]
                                                ]
                                            ],
                                            [
                                                'match' => [
                                                    'alternative_names.displayName' => [
                                                        'query' => 'Company',
                                                    ]
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                true
            ],
        ];
    }
}
