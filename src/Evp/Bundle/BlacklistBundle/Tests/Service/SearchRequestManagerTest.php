<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Elastica\Client;
use Elastica\Exception\ClientException;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\SearchRequestManager;
use FOS\ElasticaBundle\Finder\TransformedFinder;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;

class SearchRequestManagerTest extends TestCase
{
    /** @var ConfigurationManager | MockObject */
    private $configurationManager;
    /** @var LoggerInterface | MockObject */
    private $logger;
    /** @var SearchRequestManager */
    private $searchRequestManager;
    /** @var TransformedFinder | MockObject */
    private $transformedFinder;
    /** @var Client | MockObject */
    private $client;

    protected function setUp(): void
    {
        $this->configurationManager = self::createMock(ConfigurationManager::class);
        $this->logger = self::createMock(LoggerInterface::class);
        $this->transformedFinder = self::createMock(TransformedFinder::class);
        $this->client = self::createMock(Client::class);
        $this->searchRequestManager = new SearchRequestManager(
            $this->configurationManager,
            $this->logger
        );
    }

    public function testSendSearchRequest()
    {
        $this->configurationManager->expects(self::once())
            ->method('getProfileFinder')
            ->willReturn($this->transformedFinder)
        ;
        $this->configurationManager->expects(self::never())
            ->method('getClient')
            ->willReturn($this->client)
        ;
        $this->client->expects(self::never())
            ->method('connect')
        ;
        $this->transformedFinder->expects(self::once())
            ->method('findHybrid')
            ->with([], SearchRequestManager::LIMIT, ['search_type' => 'dfs_query_then_fetch',])
            ->willReturn([])
        ;
        $this->logger->expects(self::once())
            ->method('debug')
            ->with('Searching by query', [[]])
        ;
        $this->logger->expects(self::never())
            ->method('info')
        ;
        $this->assertEquals([], $this->searchRequestManager->sendSearchRequest([]));
    }

    public function testSendSearchRequestWhenThrowException()
    {
        $this->expectException(ClientException::class);
        $this->configurationManager->expects(self::exactly(4))
            ->method('getProfileFinder')
            ->willReturn($this->transformedFinder)
        ;
        $this->configurationManager->expects(self::exactly(3))
            ->method('getClient')
            ->willReturn($this->client)
        ;
        $this->client->expects(self::exactly(3))
            ->method('connect')
        ;
        $this->transformedFinder->expects(self::exactly(4))
            ->method('findHybrid')
            ->with([], SearchRequestManager::LIMIT, ['search_type' => 'dfs_query_then_fetch',])
            ->willThrowException(new ClientException())
        ;
        $this->logger->expects(self::exactly(4))
            ->method('debug')
            ->with('Searching by query', [[]])
        ;
        $this->logger->expects(self::exactly(3))
            ->method('info')
            ->withConsecutive(
                ['In SearchRequestManager retrying search request on error. Retry count is 1'],
                ['In SearchRequestManager retrying search request on error. Retry count is 2'],
                ['In SearchRequestManager retrying search request on error. Retry count is 3'],
            )
        ;
        $this->searchRequestManager->sendSearchRequest([]);
    }

    public function testSendSearchRequestWhenThrowExceptionCatchAndRecover()
    {
        $this->configurationManager->expects(self::exactly(2))
            ->method('getProfileFinder')
            ->willReturn($this->transformedFinder)
        ;
        $this->configurationManager->expects(self::exactly(1))
            ->method('getClient')
            ->willReturn($this->client)
        ;
        $this->client->expects(self::exactly(1))
            ->method('connect')
        ;
        $this->transformedFinder->expects(self::exactly(2))
            ->method('findHybrid')
            ->with([], SearchRequestManager::LIMIT, ['search_type' => 'dfs_query_then_fetch',])
            ->willReturnOnConsecutiveCalls($this->throwException(new ClientException()), [])
        ;
        $this->logger->expects(self::exactly(2))
            ->method('debug')
            ->with('Searching by query', [[]])
        ;
        $this->logger->expects(self::exactly(1))
            ->method('info')
            ->with('In SearchRequestManager retrying search request on error. Retry count is 1')
        ;
        $this->assertEquals([], $this->searchRequestManager->sendSearchRequest([]));
    }
}
