<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistRepository;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistChangeLogManager;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class ClientCheckWhitelistManagerTest extends TestCase
{
    private FixturesHelper $fixturesHelper;
    /** @var MockObject|EntityManagerInterface */
    private EntityManagerInterface $entityManagerMock;
    /** @var MockObject|ClientCheckWhitelistRepository */
    private ClientCheckWhitelistRepository $clientCheckWhitelistRepositoryMock;
    /** @var MockObject|ClientCheckWhitelistChangeLogManager */
    private ClientCheckWhitelistChangeLogManager $clientCheckWhitelistChangeLogManager;
    private ClientCheckWhitelistManager $manager;

    public function setUp(): void
    {
        $this->fixturesHelper = new FixturesHelper();
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->clientCheckWhitelistRepositoryMock = $this->createMock(ClientCheckWhitelistRepository::class);
        $this->clientCheckWhitelistChangeLogManager = $this->createMock(ClientCheckWhitelistChangeLogManager::class);
        $this->manager = new ClientCheckWhitelistManager(
            $this->entityManagerMock,
            $this->clientCheckWhitelistRepositoryMock,
            $this->clientCheckWhitelistChangeLogManager,
        );
    }

    /**
     * @dataProvider dataProviderCreateIfNotExists
     */
    public function testCreateIfNotExists(
        bool $isClientCheckWhitelistExistsResult,
        int $persistExactly
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $profile = $this->fixturesHelper->createPersonProfile($blacklist);
        $categoryGroup = $this->fixturesHelper->createCategoryGroup();

        $this->clientCheckWhitelistRepositoryMock
            ->expects($this->once())
            ->method('isClientCheckWhitelistExists')
            ->with($this->callback(function (ClientCheckWhitelist $clientCheckWhitelist) use ($client, $profile, $categoryGroup, $blacklist) {
                $this->assertSame($client, $clientCheckWhitelist->getClient());
                $this->assertSame($blacklist, $clientCheckWhitelist->getBlacklist());
                $this->assertEquals($profile->getExternalId(), $clientCheckWhitelist->getProfileExternalId());
                $this->assertSame($categoryGroup, $clientCheckWhitelist->getCategoryGroup());

                return true;
            }))
            ->willReturn($isClientCheckWhitelistExistsResult)
        ;

        $this->entityManagerMock
            ->expects($this->exactly($persistExactly))
            ->method('persist')
        ;

        $this->clientCheckWhitelistChangeLogManager
            ->expects($this->exactly($persistExactly))
            ->method('log')
        ;

        $this->manager->createIfNotExists($client, $profile, $categoryGroup);
    }

    public function dataProviderCreateIfNotExists(): array
    {
        return [
            'Whitelist NOT Exists' => [
                'isClientCheckWhitelistExistsResult' => false,
                'persistExactly' => 1,
            ],
            'Whitelist Exists' => [
                'isClientCheckWhitelistExistsResult' => true,
                'persistExactly' => 0,
            ],
        ];
    }

    public function testRemoveAll(): void
    {
        $profileExternalId = '123';
        $client = $this->fixturesHelper->createClientNatural();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $categoryGroup = $this->fixturesHelper->createCategoryGroup();

        $list = [
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup, $profileExternalId),
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup, $profileExternalId),
        ];
        $this->clientCheckWhitelistRepositoryMock
            ->expects($this->once())
            ->method('findAllByClient')
            ->with($client)
            ->willReturn($list)
        ;

        $invocationIndex = 0;
        $this->entityManagerMock
            ->expects($this->exactly(2))
            ->method('remove')
            ->willReturnCallback(function ($entity) use (&$invocationIndex, $list) {
                $this->assertSame($list[$invocationIndex], $entity);
                $invocationIndex++;
            })
        ;

        $this->clientCheckWhitelistChangeLogManager
            ->expects($this->exactly(2))
            ->method('log')
        ;

        $this->manager->removeAll($client);
    }

    public function testRemoveAllByBlacklistAndProfileExternalId(): void
    {
        $profileExternalId = '123';
        $client = $this->fixturesHelper->createClientNatural();
        $blacklist = $this->fixturesHelper->createBlacklist();
        $categoryGroup = $this->fixturesHelper->createCategoryGroup();

        $list = [
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup, $profileExternalId),
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup, $profileExternalId),
        ];
        $this->clientCheckWhitelistRepositoryMock
            ->expects($this->once())
            ->method('findAllByBlacklistAndProfileExternalId')
            ->with($blacklist, $profileExternalId)
            ->willReturn($list)
        ;

        $invocationIndex = 0;
        $this->entityManagerMock
            ->expects($this->exactly(2))
            ->method('remove')
            ->willReturnCallback(function ($entity) use (&$invocationIndex, $list) {
                $this->assertSame($list[$invocationIndex], $entity);
                $invocationIndex++;
            })
        ;

        $this->clientCheckWhitelistChangeLogManager
            ->expects($this->exactly(2))
            ->method('log')
        ;

        $this->manager->removeAllByBlacklistAndProfileExternalId($blacklist, $profileExternalId);
    }

    public function testSyncWithProfile(): void
    {
        $profileExternalId1 = '1234';
        $profileExternalId2 = '5678';

        $client = $this->fixturesHelper->createClientNatural();
        $this->setPrivateProperty($client, 'id', 111);

        $blacklist = $this->fixturesHelper->createBlacklist();

        $categoryGroup1 = $this->fixturesHelper->createCategoryGroup();
        $this->setPrivateProperty($categoryGroup1, 'id', $profileExternalId1);

        $categoryGroup2 = $this->fixturesHelper->createCategoryGroup();
        $this->setPrivateProperty($categoryGroup2, 'id', $profileExternalId2);

        $category = $this->fixturesHelper->createCategory(1, $categoryGroup1);

        $profile = (new Profile())
            ->setBlacklist($blacklist)
            ->setExternalId($profileExternalId1)
            ->setType(Profile::TYPE_PERSON)
            ->addCategory($category)
        ;

        $list = [
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup1, $profileExternalId1),
            $this->fixturesHelper->createClientCheckWhitelist($client, $blacklist, $categoryGroup2, $profileExternalId1),
        ];

        $this->clientCheckWhitelistRepositoryMock
            ->expects($this->once())
            ->method('findAllByBlacklistAndProfileExternalId')
            ->with($blacklist, $profile->getExternalId())
            ->willReturn($list)
        ;

        $this->entityManagerMock
            ->expects($this->exactly(1))
            ->method('remove')
        ;

        $this->clientCheckWhitelistChangeLogManager
            ->expects($this->exactly(1))
            ->method('log')
        ;

        $this->manager->syncWithProfile($profile);
    }
}
