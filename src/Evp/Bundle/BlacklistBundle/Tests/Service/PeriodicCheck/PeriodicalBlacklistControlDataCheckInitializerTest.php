<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\PeriodicCheck;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankTransferBundle\Tests\Functional\TransferInEvent\DummyPublisher;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Exception\BlacklistProfileDataCheckLogInputCsvFileValidatorException;
use Evp\Bundle\BlacklistBundle\Exception\PeriodicalBlacklistControlDataCheckInitializerException;
use Evp\Bundle\BlacklistBundle\Service\ClientPeriodicCheckResultAdapterFactory;
use Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckInitializer;
use Evp\Bundle\RabbitMqExtensionBundle\Service\DeferredRemoteJobPublisher;
use Paysera\Bundle\FileManagementBundle\Service\FallbackAdapter;
use Paysera\Bundle\TransferSurveillanceBundle\Tests\Functional\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class PeriodicalBlacklistControlDataCheckInitializerTest extends PersistableWebTestCase
{
    protected $client;
    private DummyPublisher $publisher;
    private DeferredRemoteJobPublisher $deferredPublisher;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private PeriodicalBlacklistControlDataCheckInitializer $initializer;
    private UploadedFile $file;
    private Blacklist $blacklist;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $container = $this->client->getContainer();

        $this->publisher = new DummyPublisher();
        $container->set('dummy_publisher', $this->publisher);

        $container->set(
            'evp_rabbit_mq_extension.deferred_remote_job_publisher', new DeferredRemoteJobPublisher(
                $container->get('doctrine.orm.default_entity_manager'),
                $this->publisher
            )
        );

        $this->deferredPublisher = $container->get('evp_rabbit_mq_extension.deferred_remote_job_publisher');
        $this->entityManager = $container->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        /** @var MockObject $adapter */
        $adapter = $this->createMock(FallbackAdapter::class);
        $adapter->expects($this->any())->method('write')->willReturn('true');

        /** @var MockObject $factory */
        $factory = $this->createMock(ClientPeriodicCheckResultAdapterFactory::class);
        $factory->expects($this->any())->method('getAdapter')->willReturn($adapter);

        $this->getContainer()->set(
            'evp_blacklist.adapter_factory.client_periodic_check_result',
            $factory
        );

        $this->initializer = $this->getContainer()
            ->get('evp_blacklist.periodical_check.periodical_blacklist_control_data_check_initializer');

        $this->file = new UploadedFile(
            __DIR__ . '/../../Data/initial.csv',
            'initial.csv',
            'text/csv'
        );

        $this->blacklist = $this->fixturesHelper->createBlacklist('dow_jones_v1', 'dow_jones_v1');
    }

    /**
     * @return void
     * @throws BlacklistProfileDataCheckLogInputCsvFileValidatorException
     * @throws PeriodicalBlacklistControlDataCheckInitializerException
     */
    public function testInitializeManipulatedTrue(): void
    {
        $this->initializeManipulated();
        $jobs = $this->publisher->getJobsPublished();

        $this->assertNotEmpty($jobs);
        $this->assertEquals('gateway.blacklist_periodic_check_process_data', $jobs[0][0]);
        $this->assertArrayHasKey('check_log_id', $jobs[0][1]);
        $this->assertArrayHasKey('path', $jobs[0][1]);
        $this->assertArrayHasKey('manipulate', $jobs[0][1]);
    }

    /**
     * @return void
     * @throws BlacklistProfileDataCheckLogInputCsvFileValidatorException
     * @throws PeriodicalBlacklistControlDataCheckInitializerException
     */
    public function testInitializeManipulatedFalse(): void
    {
        $this->initializeManipulated(false);
        $jobs = $this->publisher->getJobsPublished();

        $this->assertEquals('gateway.blacklist_periodic_check_process_data', $jobs[0][0]);
        $this->assertArrayHasKey('check_log_id', $jobs[0][1]);
        $this->assertArrayHasKey('path', $jobs[0][1]);
        $this->assertArrayHasKey('manipulate', $jobs[0][1]);
    }

    /**
     * @param bool $manipulated
     * @return void
     * @throws BlacklistProfileDataCheckLogInputCsvFileValidatorException
     * @throws PeriodicalBlacklistControlDataCheckInitializerException
     */
    private function initializeManipulated(bool $manipulated = true): void
    {
        $this->initializer->initialize($this->blacklist->getKey(), $this->file, $manipulated);
        $this->entityManager->flush();
        $this->deferredPublisher->onCommit();
    }
}
