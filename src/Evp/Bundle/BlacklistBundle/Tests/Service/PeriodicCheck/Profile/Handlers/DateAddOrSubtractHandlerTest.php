<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\PeriodicCheck\Profile\Handlers;

use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\DateAddOrSubtractHandler;
use PHPUnit\Framework\TestCase;

class DateAddOrSubtractHandlerTest extends TestCase
{
    public function testUnsupportedDateFormat(): void
    {
        $this->expectException(\InvalidArgumentException::class);

        $handler = new DateAddOrSubtractHandler();
        $profile = (new Profile())->setBirthday('14-05-2022');

        $result = $handler->handle($profile);
    }

    /**
     * @dataProvider getDatesWithDifferentSeparators
     */
    public function testDifferentSeparators(string $birthDay): void
    {
        $handler = new DateAddOrSubtractHandler();
        $profile = (new Profile())->setBirthday($birthDay);

        preg_match('/^\d{4}(.)\d{2}.\d{2}$/m', $birthDay, $matches);
        [, $originalSeparator] = $matches;

        $result = $handler->handle($profile);
        $this->assertEquals(4, strpos($result->getBirthday(), $originalSeparator));
    }

    public function getDatesWithDifferentSeparators(): array
    {
        return [
            ['2022.11.23'],
            ['2022/11/23'],
            ['2022-11-23'],
        ];
    }

    public function testManipulatesDateField(): void
    {
        $possibleResults = ['2022-11-22', '2022-11-24', '2022-10-23', '2022-12-23', '2021-11-23', '2023-11-23'];

        $handler = new DateAddOrSubtractHandler();
        $profile = (new Profile())->setBirthday('2022-11-23');

        $result = $handler->handle($profile);
        $this->assertTrue(in_array($result->getBirthday(), $possibleResults));
    }
}
