<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankBundle\Entity\AccountInfo;
use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPaypal;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPerson;
use Evp\Bundle\BankTransferBundle\Repository\ClientTypePredictionRepository;
use Evp\Bundle\BankTransferBundle\Service\TransferRoutingInformationRetriever;
use Evp\Bundle\BlacklistBundle\Entity\BankCode;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BankCodeRepository;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\DowJonesAiManager;
use Evp\Bundle\BlacklistBundle\Service\ProfileQueryBuilder;
use Evp\Bundle\BlacklistBundle\Service\SearchRequestManager;
use Evp\Bundle\BlacklistBundle\Service\SearchResultsProcessor;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\QuestionnaireBundle\Entity\ClientLegal\RealBeneficiary;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\User\UserClient;
use Generator;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferPayerExcludedChecker;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class BlacklistManagerTest extends TestCase
{
    /**
     * @var EntityManager | MockObject
     */
    private $entityManager;

    /**
     * @var ProfileQueryBuilder | MockObject
     */
    private $profileQueryBuilder;

    /**
     * @var BlacklistRepository | MockObject
     */
    private $blacklistRepository;

    /**
     * @var TransferRoutingInformationRetriever | MockObject
     */
    private $transferRoutingInformationRetriever;

    /**
     * @var AccountInfoResolver | MockObject
     */
    private $accountInfoResolver;

    /**
     * @var SearchResultsProcessor | MockObject
     */
    private $searchResultsProcessor;

    /**
     * @var BankCodeRepository | MockObject
     */
    private $bankCodeRepository;

    /**
     * @var LoggerInterface | MockObject
     */
    private $logger;

    /**
     * @var boolean
     */
    private $enabled;

    /**
     * @var BlacklistManager
     */
    private $blacklistManager;

    /**
     * @var UserClient|MockObject
     */
    private $userClient;

    /**
     * @var ConfigurationManager|MockObject
     */
    private $configurationManager;

    /**
     * @var ClientTypePredictionRepository|MockObject
     */
    private $clientTypePredictionRepository;

    /**
     * @var DowJonesAiManager|MockObject
     */
    private $dowJonesAiManager;

    /**
     * @var SearchRequestManager|MockObject
     */
    private $searchRequestManager;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->profileQueryBuilder = $this->createMock(ProfileQueryBuilder::class);
        $this->blacklistRepository = $this->createMock(BlacklistRepository::class);
        $this->transferRoutingInformationRetriever = $this->createMock(TransferRoutingInformationRetriever::class);
        $this->accountInfoResolver = $this->createMock(AccountInfoResolver::class);
        $this->searchResultsProcessor = $this->createMock(SearchResultsProcessor::class);
        $this->bankCodeRepository = $this->createMock(BankCodeRepository::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->configurationManager = $this->createMock(ConfigurationManager::class);
        $this->userClient = $this->createMock(UserClient::class);
        $this->clientTypePredictionRepository = $this->createMock(ClientTypePredictionRepository::class);
        $this->dowJonesAiManager = $this->createMock(DowJonesAiManager::class);
        $this->searchRequestManager = $this->createMock(SearchRequestManager::class);
        $this->enabled = true;

        $transferPayerExcludedChecker = new TransferPayerExcludedChecker([
            ['name' => 'N/A', 'payment' => 'bpb_xk'],
            ['name' => 'NOT_FILLED', 'payment' => 'test_bank'],
        ]);

        $this->profileQueryBuilder
            ->expects(self::any())
            ->method('build')
            ->willReturn([])
        ;

        $this->blacklistManager = new BlacklistManager(
            $this->entityManager,
            $this->profileQueryBuilder,
            $this->blacklistRepository,
            $this->transferRoutingInformationRetriever,
            $this->accountInfoResolver,
            $this->searchResultsProcessor,
            $this->bankCodeRepository,
            $this->userClient,
            $this->configurationManager,
            $this->clientTypePredictionRepository,
            $this->enabled,
            $this->logger,
            $transferPayerExcludedChecker,
            $this->dowJonesAiManager,
            $this->searchRequestManager
        );
    }

    /**
     * @param string|null $sanctionListKey
     * @param Blacklist|null $sanctionList
     *
     * @dataProvider sanctionListKeysProvider
     */
    public function testGetProfileCheckBlacklistResults(
        string $sanctionListKey = null,
        Blacklist $sanctionList = null
    ) {
        $countryCode = 'LT';
        $transferIn = (new TransferIn())
            ->setPayer(
                new PartyIban(
                    '********************',
                    'Test User'
                )
            )
        ;
        $resultProfile = (new Profile())
            ->setFirstName(
                $transferIn->getPayer()->getDisplayName()
            )
            ->setCountry($countryCode)
        ;
        $configurationName = 'testConfiguration';

        if (
            $sanctionListKey !== null
            && $sanctionList !== null
        ) {
            $resultProfile->setBlacklist($sanctionList);
        }

        $this->clientTypePredictionRepository
            ->expects($this->never())
            ->method('findOneByTransfer')
            ->with($transferIn)
            ->willReturn(null)
        ;

        $this->configurationManager
            ->expects($this->once())
            ->method('getActiveConfigurationName')
            ->willReturn($configurationName)
        ;

        $this->profileQueryBuilder
            ->expects($this->once())
            ->method('build')
            ->with(
                $resultProfile,
                $configurationName
            )
        ;

        if ($sanctionListKey) {
            $this->blacklistRepository
                ->expects($this->once())
                ->method('findOneByKey')
                ->with($sanctionListKey)
                ->willReturn($sanctionList)
            ;
        } else {
            $this->blacklistRepository
                ->expects($this->never())
                ->method('findOneByKey')
            ;
        }

        $this->accountInfoResolver
            ->expects($this->once())
            ->method('getAccountInfoForTransferParty')
            ->with(
                $transferIn->getPayer()
            )
            ->willReturn(
                (new AccountInfo())->setCountryCode($countryCode)
            )
        ;

        if (
            $sanctionListKey !== null
            && $sanctionList === null
        ) {
            $this->logger
                ->expects($this->once())
                ->method('warning')
                ->with(
                    'Sanction list not found!',
                    [
                        $sanctionListKey,
                    ]
                )
            ;
        }

        $this->blacklistManager->getProfileCheckBlacklistResults(
            $transferIn,
            $sanctionListKey
        );
    }

    /**
     * @param Party $payer
     * @param string $profileName
     * @param bool $aiFilterEnabled
     *
     * @dataProvider getProfileCheckBlacklistResultsWithExcludedPayersProvider
     */
    public function testGetProfileCheckBlacklistResultsWithExcludedPayers(
        Party $payer,
        string $profileName,
        bool $aiFilterEnabled
    ): void {
        $countryCode = 'LT';
        $transferIn = (new TransferIn())
            ->setPayer($payer)
        ;
        $resultProfile = (new Profile())
            ->setFirstName($profileName)
            ->setCountry($countryCode)
        ;
        $configurationName = 'testConfiguration';

        $this->accountInfoResolver
            ->expects($this->once())
            ->method('getAccountInfoForTransferParty')
            ->with(
                $transferIn->getPayer()
            )
            ->willReturn(
                (new AccountInfo())->setCountryCode($countryCode)
            )
        ;

        $this->configurationManager
            ->expects($this->once())
            ->method('getActiveConfigurationName')
            ->willReturn($configurationName)
        ;

        $this->profileQueryBuilder
            ->expects($this->once())
            ->method('build')
            ->with(
                $resultProfile,
                $configurationName
            )
        ;

        if ($aiFilterEnabled === true) {
            $this->dowJonesAiManager
                ->expects($this->once())
                ->method('filterOutNotConfidentDowJonesProfiles')
                ->with(
                    '0',
                    $resultProfile,
                    []
                )
            ;
        } else {
            $this->dowJonesAiManager
                ->expects($this->never())
                ->method('filterOutNotConfidentDowJonesProfiles')
            ;
        }

        $this->blacklistManager->getProfileCheckBlacklistResults(
            $transferIn,
            Blacklist::KEY_DOW_JONES_V1,
            $aiFilterEnabled
        );
    }

    public function testGetTransferCheckResultsWithSanctionedBank()
    {
        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->willReturn([$this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SAN)])
        ;

        $transfer = $this->createTransferOutBankWithBeneficiaryBic('ABCDLT12345');
        $results = $this->blacklistManager->getTransferCheckResults($transfer);
        $this->assertCount(1, $results);
    }

    public function testGetTransferCheckResultsWithNotSanctionedBank()
    {
        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->willReturn([$this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SOR)])
        ;

        $transfer = $this->createTransferOutBankWithBeneficiaryBic('DCBALT12345');
        $results = $this->blacklistManager->getTransferCheckResults($transfer);
        $this->assertCount(0, $results);
    }

    /**
     * @param bool $aiFilterEnabled
     *
     * @dataProvider isAiEnabledProvider
     */
    public function testGetClientCheckResults(
        bool $aiFilterEnabled
    ): void {
        $client = new ClientNatural();
        $dob = new \DateTime();
        $client->setCovenanteeId(1);
        $blacklist = new Blacklist(Blacklist::KEY_DOW_JONES_V1, 'Dow Jones');
        $resultProfile = (new Profile())->setBlacklist($blacklist)->setBirthday($dob->format('Y-m-d'));

        $this->userClient
            ->expects(self::once())
            ->method('getPerson')
            ->willReturn((new UserInformation())->setDob($dob))
        ;

        $this->searchResultsProcessor
            ->expects($this->once())
            ->method('removeWhitelistedResults')
            ->with(
                [],
                $client
            )
            ->willReturn([])
        ;

        if ($aiFilterEnabled === true) {
            $this->dowJonesAiManager
                ->expects($this->once())
                ->method('filterOutNotConfidentDowJonesProfiles')
                ->with(
                    (string) $client->getCovenanteeId(),
                    $resultProfile,
                    []
                )
            ;
        } else {
            $this->dowJonesAiManager
                ->expects($this->never())
                ->method('filterOutNotConfidentDowJonesProfiles')
            ;
        }

        $this->blacklistManager->getClientCheckResults(
            $blacklist,
            $client,
            $aiFilterEnabled
        );
    }

    /**
     * @dataProvider aiFilteringDataProvider
     */
    public function testGetRealBeneficiaryCheckResults(
        bool $aiFilterEnabled,
        ?string $aiConfidence = null,
        bool $ignoreAlternativeNames = false
    ): void {
        $code = '123';
        $name = 'test';
        $configurationName = 'production';
        $blacklist = new Blacklist(
            Blacklist::KEY_DOW_JONES_V1,
            'DowJones'
        );
        $realBeneficiary = (new RealBeneficiary())->setType('person')->setCode($code)->setName($name);
        $resultProfile = (new Profile())
            ->setName($name)
            ->setBlacklist($blacklist)
            ->setCode($code)
        ;
        $client = new ClientNatural();
        $client->setCovenanteeId(1);

        $this->configurationManager
            ->expects($this->once())
            ->method('getActiveConfigurationName')
            ->willReturn($configurationName)
        ;

        $this->profileQueryBuilder
            ->expects($this->once())
            ->method('build')
            ->with(
                $resultProfile,
                $configurationName
            )
        ;

        if ($aiFilterEnabled === true) {
            $this->dowJonesAiManager
                ->expects($this->once())
                ->method('filterOutNotConfidentDowJonesProfiles')
                ->with(
                    (string) $client->getCovenanteeId(),
                    $resultProfile,
                    [],
                    $aiConfidence,
                    $ignoreAlternativeNames
                )
            ;
        } else {
            $this->dowJonesAiManager
                ->expects($this->never())
                ->method('filterOutNotConfidentDowJonesProfiles')
            ;
        }

        $this->searchResultsProcessor
            ->expects($this->once())
            ->method('removeWhitelistedResults')
            ->with(
                [],
                $client
            )
            ->willReturn([])
        ;

        $this->blacklistManager->getRealBeneficiaryCheckResults(
            $blacklist,
            $realBeneficiary,
            $client,
            $aiFilterEnabled,
            $ignoreAlternativeNames,
            $aiConfidence
        );
    }

    /**
     * @dataProvider getTransfersOut
     */
    public function testBankBlacklistTransferOutMissingDetails(Transfer $transfer): void
    {
        $this->assertEmpty($this->blacklistManager->getTransferOutBankBlacklistResults($transfer) );
    }

    public function getTransfersOut(): array
    {
        $transferUnsupportedBeneficiary = new TransferOut();
        $transferUnsupportedBeneficiary->setBeneficiary(new PartyPaypal());

        $transferMissingBic = new TransferOut();
        $transferMissingBic->setBeneficiary(new PartyIban());

        return [
            'no beneficiary' => [
                new TransferOut()
            ],
            'unsupported beneficiary' => [
                $transferUnsupportedBeneficiary
            ],
            'missing bic' => [
                $transferMissingBic
            ],
        ];
    }

    public function testBankBlacklistTransferOutNoBanksFound(): void
    {
        $transfer = new TransferOut();
        $transfer->setBeneficiary((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects($this->once())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([]);

        $this->assertEmpty($this->blacklistManager->getTransferOutBankBlacklistResults($transfer) );
    }

    public function testBankBlacklistTransferOutNoBlacklistedBanksFound(): void
    {
        $transfer = new TransferOut();
        $transfer->setBeneficiary((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects($this->once())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([]);

        $this->assertEmpty($this->blacklistManager->getTransferOutBankBlacklistResults($transfer) );
    }

    public function testBankBlacklistTransferOutUnsupportedCategoryGroup(): void
    {
        $transfer = new TransferOut();
        $transfer->setBeneficiary((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([$this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_PEP)]);

        $this->assertEmpty($this->blacklistManager->getTransferOutBankBlacklistResults($transfer) );
    }

    public function testBankBlacklistTransferOut(): void
    {
        $transfer = new TransferOut();
        $transfer->setBeneficiary((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_PEP),
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SAN),
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SAN),
            ]);

        $this->assertCount(2, $this->blacklistManager->getTransferOutBankBlacklistResults($transfer) );
    }

    /**
     * @dataProvider getTransfersIn
     */
    public function testBankBlacklistTransferInMissingDetails(Transfer $transfer): void
    {
        $this->assertEmpty($this->blacklistManager->getTransferInBankBlacklistResults($transfer) );
    }

    public function getTransfersIn(): array
    {
        $transferUnsupportedPayer = new TransferOut();
        $transferUnsupportedPayer->setPayer(new PartyPaypal());

        $transferMissingBic = new TransferOut();
        $transferMissingBic->setPayer(new PartyIban());

        return [
            'no beneficiary' => [
                new TransferOut()
            ],
            'unsupported payer' => [
                $transferUnsupportedPayer
            ],
            'missing bic' => [
                $transferMissingBic
            ],
        ];
    }

    public function testBankBlacklistTransferInNoBlacklistedBanksFound(): void
    {
        $transfer = new TransferOut();
        $transfer->setPayer((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects($this->once())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([]);

        $this->assertEmpty($this->blacklistManager->getTransferInBankBlacklistResults($transfer) );
    }

    public function testBankBlacklistTransferInUnsupportedCategoryGroup(): void
    {
        $transfer = new TransferOut();
        $transfer->setPayer((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([$this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_PEP)]);

        $this->assertEmpty($this->blacklistManager->getTransferInBankBlacklistResults($transfer) );
    }

    public function testBankBlacklistTransferIn(): void
    {
        $transfer = new TransferOut();
        $transfer->setPayer((new PartyIban())->setBic('BIC123'));

        $this->bankCodeRepository
            ->expects(self::any())
            ->method('findAllByCodeStartingWith')
            ->with('BIC123')
            ->willReturn([
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_PEP),
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SAN),
                $this->createBankCodeWithTopGroupKey(CategoryGroup::GROUP_KEY_SAN),
            ]);

        $this->assertCount(2, $this->blacklistManager->getTransferInBankBlacklistResults($transfer) );
    }

    public function sanctionListKeysProvider(): array
    {
        return [
            [
                Blacklist::KEY_DOW_JONES_V1,
                new Blacklist(
                    Blacklist::KEY_DOW_JONES_V1,
                    'DowJones'
                ),
            ],
            [
                Blacklist::KEY_PAYSERA_V1,
                new Blacklist(
                    Blacklist::KEY_PAYSERA_V1,
                    'Paysera'
                ),
            ],
            [
                'unknownSanctionListKey',
                null,
            ],
            [
                null,
                null,
            ],
        ];
    }

    public function getProfileCheckBlacklistResultsWithExcludedPayersProvider(): array
    {
        return [
            [
                (new PartyPerson('N/A', '', '', 'bpb_xk')),
                '',
                false,
            ],
            [
                (new PartyPerson('Name', '', '', 'bpb_xk')),
                'Name',
                false,
            ],
            [
                (new PartyPerson('N/A', '', '', 'another')),
                'N/A',
                false,
            ],
            [
                (new PartyPerson('Name', '', '', 'another')),
                'Name',
                false,
            ],
            [
                (new PartyIban('', 'N/A')),
                'N/A',
                false,
            ],
            [
                (new PartyIban('', 'Name')),
                'Name',
                false,
            ],
            [
                (new PartyPerson('NOT_FILLED', '', '', 'test_bank')),
                '',
                false,
            ],
            [
                (new PartyPerson('Name', '', '', 'test_bank')),
                'Name',
                false,
            ],
            [
                (new PartyPerson('Name', '', '', 'test_bank')),
                'Name',
                true,
            ],
        ];
    }

    public function isAiEnabledProvider(): array
    {
        return [
            'AI filtering - ON' => [
                true,
            ],
            'AI filtering - OFF' => [
                false,
            ],
        ];
    }

    public function aiFilteringDataProvider(): Generator
    {
        yield 'AI filtering - ON (with default parameters)' => [
            'aiFilterEnabled' => true,
        ];

        yield 'AI filtering - ON (with aiConfidence)' => [
            'aiFilterEnabled' => true,
            'aiConfidence' => '93',
        ];

        yield 'AI filtering - ON (ignoring alternative names)' => [
            'aiFilterEnabled' => true,
            'aiConfidence' => '97',
            'ignoreAlternativeNames' => true,
        ];

        yield 'AI filtering - OFF' => [
            'aiFilterEnabled' => false,
        ];
    }

    /**
     * @param string $bic
     * @return TransferOutBank
     */
    private function createTransferOutBankWithBeneficiaryBic($bic)
    {
        $transfer = new TransferOutBank();
        $beneficiary = new PartyAccountCountry();
        $beneficiary->setBic($bic);
        $beneficiary->setName('Name');
        // @phpstan-ignore-next-line
        $transfer->setBeneficiary($beneficiary);

        return $transfer;
    }

    /**
     * @param string $key
     * @return BankCode
     */
    private function createBankCodeWithTopGroupKey($key)
    {
        $categoryGroup = (new CategoryGroup())->setKey($key);
        $category = (new Category())->setGroup($categoryGroup);
        $profile = (new Profile())->setCategories([$category]);
        return (new BankCode())->setItem($profile);
    }
}
