<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\BlacklistProfileFilter;

use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\AlternativeName;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\BlacklistProfileFilter\NameMatchingFilter;
use Evp\Bundle\BlacklistBundle\Service\NamesComparisonResultService;
use Paysera\Component\AIService\AINameComparisonService;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class NameMatchingFilterTest extends TestCase
{
    /**
     * @var AINameComparisonService|\PHPUnit_Framework_MockObject_MockObject
     */
    private $naturalNameComparisonService;

    /**
     * @var AINameComparisonService|\PHPUnit_Framework_MockObject_MockObject
     */
    private $legalNameComparisonService;

    /**
     * @var NamesComparisonResultService|\PHPUnit_Framework_MockObject_MockObject
     */
    private $nameComparisonResultService;

    private NameMatchingFilter $filter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->naturalNameComparisonService = $this->createMock(AINameComparisonService::class);
        $this->legalNameComparisonService = $this->createMock(AINameComparisonService::class);
        $this->nameComparisonResultService = $this->createMock(NamesComparisonResultService::class);
        $this->filter = new NameMatchingFilter(
            $this->naturalNameComparisonService,
            $this->legalNameComparisonService,
            $this->nameComparisonResultService,
            $this->createMock(LoggerInterface::class)
        );
    }

    /**
     * @dataProvider provideApplyTestCases
     *
     * @param string $profileName
     * @param string $profileType
     * @param array $matchedProfiles
     * @param bool $ignoreAlternativeNames
     * @param string|null $alternativeName
     * @param string $comparisonDecision
     * @param int $expectedCount
     *
     * @return void
     */
    public function testApply(
        string $profileName,
        string $profileType,
        array $matchedProfiles,
        bool $ignoreAlternativeNames,
        ?string $alternativeName,
        string $comparisonDecision,
        int $expectedCount
    ): void {
        $profile = $this->createMockProfile($profileName, $profileType);
        $context = $this->setUpMockContext($ignoreAlternativeNames);

        $namesComparisonResult = $this->setUpNameComparisonResult($comparisonDecision);

        $nameComparisonService = $profileType === Profile::TYPE_PERSON
            ? $this->naturalNameComparisonService
            : $this->legalNameComparisonService;

        $nameComparisonService
            ->method('askAI')
            ->willReturn($namesComparisonResult);

        foreach ($matchedProfiles as $matchedProfile) {
            if ($alternativeName !== null) {
                $this->setUpAlternativeName($matchedProfile->getFoundProfile(), $alternativeName);
            }
        }

        $this->nameComparisonResultService
            ->expects($this->exactly(count($matchedProfiles)))
            ->method('saveComparisonResults')
        ;

        $result = $this->filter->apply($profile, $matchedProfiles, $context);

        $this->assertCount($expectedCount, $result);
        if ($expectedCount > 0) {
            foreach ($matchedProfiles as $matchedProfile) {
                $this->assertContains($matchedProfile, $result);
            }
        }
    }

    public function provideApplyTestCases(): array
    {
        return [
            'Matching names with ignored alternative names (Person)' => [
                'John Doe',
                Profile::TYPE_PERSON,
                [$this->setUpMockMatchedProfile('John Doe')],
                true,
                null,
                NamesComparisonResultDto::DECISION_SAME_PERSON,
                1,
            ],
            'Non-matching names when ignoring alternative names (Person)' => [
                'John Doe',
                Profile::TYPE_PERSON,
                [$this->setUpMockMatchedProfile('Jane Doe')],
                true,
                null,
                NamesComparisonResultDto::DECISION_SAME_PERSON,
                1,
            ],
            'Matching alternative name when not ignored (Person)' => [
                'John Smith',
                Profile::TYPE_PERSON,
                [$this->setUpMockMatchedProfile('Jane Doe')],
                false,
                'John Smith',
                NamesComparisonResultDto::DECISION_SAME_PERSON,
                1,
            ],
            'Non-matching profiles filtered out (Person)' => [
                'John Doe',
                Profile::TYPE_PERSON,
                [
                    $this->setUpMockMatchedProfile('Jane Doe'),
                    $this->setUpMockMatchedProfile('Robert Smith'),
                ],
                true,
                null,
                NamesComparisonResultDto::DECISION_DIFFERENT_PERSON,
                0,
            ],
            'Matching names with ignored alternative names (Company)' => [
                'ACME Corp.',
                Profile::TYPE_COMPANY,
                [$this->setUpMockMatchedProfile('ACME Corp.')],
                true,
                null,
                NamesComparisonResultDto::DECISION_SAME_PERSON,
                1,
            ],
            'Non-matching names when ignoring alternative names (Company)' => [
                'ACME Corp.',
                Profile::TYPE_COMPANY,
                [$this->setUpMockMatchedProfile('Global Inc.')],
                true,
                null,
                NamesComparisonResultDto::DECISION_DIFFERENT_PERSON,
                0,
            ],
            'Matching alternative name when not ignored (Company)' => [
                'Global Enterprises',
                Profile::TYPE_COMPANY,
                [$this->setUpMockMatchedProfile('Global Inc.')],
                false,
                'Global Enterprises',
                NamesComparisonResultDto::DECISION_SAME_PERSON,
                1,
            ],
            'Non-matching profiles filtered out (Company)' => [
                'ACME Corp.',
                Profile::TYPE_COMPANY,
                [
                    $this->setUpMockMatchedProfile('Tech Solutions'),
                    $this->setUpMockMatchedProfile('Innovative Startups'),
                ],
                true,
                null,
                NamesComparisonResultDto::DECISION_DIFFERENT_PERSON,
                0,
            ],
        ];
    }

    private function createMockProfile(string $displayName, string $type): Profile
    {
        $profile = $this->createMock(Profile::class);
        $profile->method('getDisplayName')->willReturn($displayName);
        $profile->method('getType')->willReturn($type);

        return $profile;
    }

    private function setUpMockMatchedProfile(string $displayName): BlacklistResult
    {
        $foundProfile = $this->createMock(Profile::class);
        $foundProfile->method('getDisplayName')->willReturn($displayName);
        $foundProfile->method('getAlternativeNames')->willReturn([]);

        $matchedProfile = $this->createMock(BlacklistResult::class);
        $matchedProfile->method('getFoundProfile')->willReturn($foundProfile);

        return $matchedProfile;
    }

    private function setUpMockContext(bool $shouldIgnoreAlternativeNames): ProfileFilterContext
    {
        $context = $this->createMock(ProfileFilterContext::class);
        $context->method('shouldIgnoreAlternativeNames')->willReturn($shouldIgnoreAlternativeNames);

        return $context;
    }

    private function setUpNameComparisonResult(string $decision): NamesComparisonResultDto
    {
        $namesComparisonResult = $this->createMock(NamesComparisonResultDto::class);
        $namesComparisonResult->method('getDecision')->willReturn($decision);

        return $namesComparisonResult;
    }

    private function setUpAlternativeName(Profile $foundProfile, string $alternativeName): void
    {
        $alternativeNameMock = $this->createMock(AlternativeName::class);
        $alternativeNameMock->method('getDisplayName')->willReturn($alternativeName);
        $foundProfile->method('getAlternativeNames')->willReturn([$alternativeNameMock]);
    }
}
