<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\DowJonesIncrementalImportProcessor;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\MessageProcessor\DowJonesIncrementalImportMessageProcessor;
use Evp\Bundle\BlacklistBundle\Repository\ProfileAssociationRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRelationshipRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\DowJonesIncrementalImportProcessor\DowJonesIncrementalImportAssociationProcessor;
use Evp\Bundle\BlacklistBundle\Service\DowJonesIncrementalImportProcessor\DowJonesIncrementalImportProfileProcessor;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Evp\Bundle\BlacklistBundle\Service\SanctionsVersionManager;
use Evp\Bundle\BlacklistBundle\Service\Transformer\ProviderCategoryTransformer;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class DowJonesIncrementalImportAssociationProcessorTest extends PersistableWebTestCase
{
    private const CURRENT_ACTIVE_VERSION = 100;
    private const FEED_FILE_VERSION = 200;

    private EntityManagerInterface $entityManager;
    private ConfigurationManager $configurationManager;
    private ProviderCategoryTransformer $providerCategoryTransformer;
    private BlacklistCategoryImporter $categoryImporter;
    private ProfileRepository $profileRepository;
    private ProfileAssociationRepository $profileAssociationRepository;
    private ProfileRelationshipRepository $profileRelationshipRepository;
    private FixturesHelper $fixturesHelper;
    private DowJonesIncrementalImportProfileProcessor $importProfileProcessor;
    private DowJonesIncrementalImportAssociationProcessor $importAssociationProcessor;

    /** @var ClientCheckWhitelistManager|MockObject */
    private ClientCheckWhitelistManager $clientCheckWhitelistManager;

    /** @var ObjectPersisterInterface|MockObject */
    private ObjectPersisterInterface $objectPersister;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->clientCheckWhitelistManager = $this->createMock(ClientCheckWhitelistManager::class);

        $this->objectPersister = $this->createMock(ObjectPersisterInterface::class);
        $this->getContainer()->set('fos_elastica.object_persister.sanctions_secondary_v1.profile', $this->objectPersister);

        $feedReader = $this->getMockBuilder(DowJonesFeedReader::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['resolveFeedFileVersion', 'getFeedFileByVersion'])
            ->getMock()
        ;
        $feedReader->expects($this->any())
            ->method('resolveFeedFileVersion')
            ->willReturn(self::FEED_FILE_VERSION)
        ;
        $feedReader->expects($this->any())
            ->method('getFeedFileByVersion')
            ->willReturn('/' . __DIR__ . '/../Resources/PFA2_201712022200_D.zip')
        ;
        $this->getContainer()->set('evp_blacklist.dowjones_full_feed_reader', $feedReader);

        $sanctionsVersionManager = $this->createMock(SanctionsVersionManager::class);
        $sanctionsVersionManager
            ->expects($this->any())
            ->method('getCurrentActiveVersion')
            ->willReturn(self::CURRENT_ACTIVE_VERSION)
        ;
        $this->getContainer()->set('evp_blacklist.service.sanctions_version_manager_v1', $sanctionsVersionManager);

        /** @noinspection MissingService */
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->configurationManager = $this->getContainer()->get('evp_blacklist.configuration_manager');
        $this->providerCategoryTransformer = $this->getContainer()->get('evp_blacklist.transformer.provider_category');
        $this->categoryImporter = $this->getContainer()->get('evp_blacklist.category_importer');
        $this->profileRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile');
        $this->profileAssociationRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile_association');
        $this->profileRelationshipRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile_relationship');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->importProfileProcessor = new DowJonesIncrementalImportProfileProcessor(
            $this->getContainer()->get('evp_blacklist.repository.blacklist'),
            $this->profileRepository,
            $this->clientCheckWhitelistManager,
            $this->configurationManager,
            $this->getContainer()->get('evp_blacklist.profile_manager'),
            $this->getContainer()->get('evp_blacklist.transformer.provider_profile'),
            $this->objectPersister,
            $this->getContainer()->get('logger')
        );

        $this->importAssociationProcessor = $this->getContainer()->get('evp_blacklist.dow_jones_incremental_import_processor.association');
    }

    public function testProcess(): void
    {
        $expectedCount = 28;
        $expectedRelationsCount = 37;

        $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1);
        $this->entityManager->flush();

        $this->clientCheckWhitelistManager
            ->expects($this->never())
            ->method('removeAllByBlacklistAndProfileExternalId')
        ;

        $this->objectPersister->expects($this->never())->method('deleteManyByIdentifiers');

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);
        $this->importCategories($version);
        $this->importRelationships($version);
        $this->processFeedFileProfiles($version);
        $this->entityManager->flush();

        $this->processAssociations($version);

        $relationsCount = (int) $this->profileRepository->createQueryBuilder('p')
            ->select('COUNT(pa)')
            ->innerJoin('p.associations', 'pa')
            ->getQuery()
            ->getSingleScalarResult()
        ;

        $this->assertEquals($expectedCount, $this->profileAssociationRepository->count([]));
        $this->assertEquals($expectedRelationsCount, $relationsCount);
    }

    private function importCategories(int $version): void
    {
        $categories = [];

        foreach ($this->configurationManager->getFeedDataProvider()->getCategories($version) as $providerCategory) {
            $categories[] = $this->providerCategoryTransformer->transform($providerCategory);
        }

        $this->categoryImporter->import($categories);
    }

    private function importRelationships(int $version): void
    {
        foreach ($this->configurationManager->getFeedDataProvider()->getRelationships($version) as $relationship) {
            if ($this->profileRelationshipRepository->findOneByCode($relationship->getCode()) === null) {
                $this->entityManager->persist($relationship);
            }
        }

        $this->entityManager->flush();
    }

    private function processFeedFileProfiles(int $version): void
    {
        foreach ($this->configurationManager->getFeedDataProvider()->getProfiles($version) as $providerProfile) {
            $data = [
                DowJonesIncrementalImportMessageProcessor::KEY_PROVIDER_PROFILE => serialize($providerProfile),
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->importProfileProcessor->process($data);
            $this->entityManager->flush();
        }
    }

    private function processAssociations(int $version): void
    {
        foreach ($this->configurationManager->getFeedDataProvider()->getAssociations($version) as $associationsData) {
            $data = [
                DowJonesIncrementalImportMessageProcessor::KEY_OPERATION => DowJonesIncrementalImportMessageProcessor::OPERATION_IMPORT_ASSOCIATION,
                DowJonesIncrementalImportMessageProcessor::KEY_ASSOCIATIONS_DATA => serialize($associationsData),
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->importAssociationProcessor->process($data);
            $this->entityManager->flush();
        }
    }
}
