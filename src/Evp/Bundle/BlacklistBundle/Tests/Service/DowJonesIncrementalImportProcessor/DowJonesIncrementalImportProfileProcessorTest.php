<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\DowJonesIncrementalImportProcessor;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\MessageProcessor\DowJonesIncrementalImportMessageProcessor;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\DowJonesIncrementalImportProcessor\DowJonesIncrementalImportProfileProcessor;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Evp\Bundle\BlacklistBundle\Service\SanctionsVersionManager;
use Evp\Bundle\BlacklistBundle\Service\Transformer\ProviderCategoryTransformer;
use FOS\ElasticaBundle\Persister\ObjectPersisterInterface;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class DowJonesIncrementalImportProfileProcessorTest extends PersistableWebTestCase
{
    private const CURRENT_ACTIVE_VERSION = 100;
    private const FEED_FILE_VERSION = 200;

    private EntityManagerInterface $entityManager;
    private ConfigurationManager $configurationManager;
    private ProviderCategoryTransformer $providerCategoryTransformer;
    private BlacklistCategoryImporter $categoryImporter;
    private ProfileRepository $profileRepository;
    private FixturesHelper $fixturesHelper;
    private DowJonesIncrementalImportProfileProcessor $processor;

    /** @var ClientCheckWhitelistManager|MockObject */
    private ClientCheckWhitelistManager $clientCheckWhitelistManager;

    /** @var ObjectPersisterInterface|MockObject */
    private ObjectPersisterInterface $objectPersister;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->clientCheckWhitelistManager = $this->createMock(ClientCheckWhitelistManager::class);

        $this->objectPersister = $this->createMock(ObjectPersisterInterface::class);
        $this->getContainer()->set('fos_elastica.object_persister.sanctions_secondary_v1.profile', $this->objectPersister);

        $feedReader = $this->getMockBuilder(DowJonesFeedReader::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['resolveFeedFileVersion', 'getFeedFileByVersion'])
            ->getMock()
        ;
        $feedReader->expects($this->any())
            ->method('resolveFeedFileVersion')
            ->willReturn(self::FEED_FILE_VERSION)
        ;
        $feedReader->expects($this->any())
            ->method('getFeedFileByVersion')
            ->willReturn('/' . __DIR__ . '/../Resources/PFA2_201712022200_D.zip')
        ;
        $this->getContainer()->set('evp_blacklist.dowjones_full_feed_reader', $feedReader);

        $sanctionsVersionManager = $this->createMock(SanctionsVersionManager::class);
        $sanctionsVersionManager
            ->expects($this->any())
            ->method('getCurrentActiveVersion')
            ->willReturn(self::CURRENT_ACTIVE_VERSION)
        ;
        $this->getContainer()->set('evp_blacklist.service.sanctions_version_manager_v1', $sanctionsVersionManager);

        /** @noinspection MissingService */
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->configurationManager = $this->getContainer()->get('evp_blacklist.configuration_manager');
        $this->providerCategoryTransformer = $this->getContainer()->get('evp_blacklist.transformer.provider_category');
        $this->categoryImporter = $this->getContainer()->get('evp_blacklist.category_importer');
        $this->profileRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->processor = new DowJonesIncrementalImportProfileProcessor(
            $this->getContainer()->get('evp_blacklist.repository.blacklist'),
            $this->profileRepository,
            $this->clientCheckWhitelistManager,
            $this->configurationManager,
            $this->getContainer()->get('evp_blacklist.profile_manager'),
            $this->getContainer()->get('evp_blacklist.transformer.provider_profile'),
            $this->objectPersister,
            $this->getContainer()->get('logger')
        );
    }

    public function testProcess(): void
    {
        $expectedCount = 19;

        $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1);
        $this->entityManager->flush();

        $this->clientCheckWhitelistManager
            ->expects($this->never())
            ->method('removeAllByBlacklistAndProfileExternalId')
        ;

        $this->clientCheckWhitelistManager
            ->expects($this->exactly($expectedCount))
            ->method('syncWithProfile')
        ;

        $this->objectPersister->expects($this->never())->method('deleteManyByIdentifiers');

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);
        $this->importCategories($version);
        $this->processFeedFileProfiles($version);

        $this->assertEquals($expectedCount, $this->profileRepository->count([]));
    }

    public function testDeleteOldProfiles(): void
    {
        $expectedCount = 19;

        $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1);
        $this->entityManager->flush();

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);
        $this->importCategories($version);

        // Emulate profiles created before last FullImport (Shouldn't be deleted)
        $this->processFeedFileProfiles(self::CURRENT_ACTIVE_VERSION - 1);

        // Emulate profiles created during last FullImport (Should be deleted)
        $this->processFeedFileProfiles(self::CURRENT_ACTIVE_VERSION);

        // Emulate profiles created after last FullImport (Should be deleted)
        $this->processFeedFileProfiles($version - 1);

        $profileIdsThatShouldBeDeleted = [];
        foreach ($this->profileRepository->findAll() as $profile) {
            if ($profile->getVersion() >= self::CURRENT_ACTIVE_VERSION) {
                $profileIdsThatShouldBeDeleted[$profile->getExternalId()][0][] = $profile->getId();
            }
        }

        // Check that we collected only profiles for last two versions and not included the first one
        $this->assertCount(2, current($profileIdsThatShouldBeDeleted)[0]);

        $this->clientCheckWhitelistManager
            ->expects($this->never())
            ->method('removeAllByBlacklistAndProfileExternalId')
        ;

        $this->clientCheckWhitelistManager
            ->expects($this->exactly($expectedCount))
            ->method('syncWithProfile')
        ;

        $this->objectPersister
            ->expects($this->exactly($expectedCount))
            ->method('deleteManyByIdentifiers')
            ->withConsecutive(...$profileIdsThatShouldBeDeleted)
        ;

        // Run the IncrementalImport with actual version
        $this->processFeedFileProfiles($version);

        $this->assertEquals($expectedCount * 4, $this->profileRepository->count([]));
    }

    private function importCategories(int $version): void
    {
        $categories = [];

        foreach ($this->configurationManager->getFeedDataProvider()->getCategories($version) as $providerCategory) {
            $categories[] = $this->providerCategoryTransformer->transform($providerCategory);
        }

        $this->categoryImporter->import($categories);
    }

    private function processFeedFileProfiles(int $version): void
    {
        foreach ($this->configurationManager->getFeedDataProvider()->getProfiles($version) as $providerProfile) {
            $data = [
                DowJonesIncrementalImportMessageProcessor::KEY_PROVIDER_PROFILE => serialize($providerProfile),
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->processor->process($data);
            $this->entityManager->flush();
        }
    }
}
