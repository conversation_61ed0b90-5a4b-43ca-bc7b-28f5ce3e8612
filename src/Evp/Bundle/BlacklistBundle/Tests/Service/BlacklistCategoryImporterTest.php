<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Repository\CategoryGroupRepository;
use Evp\Bundle\BlacklistBundle\Repository\CategoryRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryManager;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class BlacklistCategoryImporterTest extends TestCase
{
    /**
     * @var BlacklistCategoryImporter
     */
    private $categoryImporter;

    /**
     * @var CategoryRepository|MockObject
     */
    private $categoryRepository;

    /**
     * @var EntityManagerInterface|MockObject
     */
    private $entityManager;

    /**
     * @var BlacklistCategoryManager
     */
    private $categoryManager;

    /**
     * @var CategoryGroupRepository|MockObject
     */
    private $categoryGroupRepository;

    public function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->categoryManager = new BlacklistCategoryManager();
        $this->categoryRepository = $this->createMock(CategoryRepository::class);
        $this->categoryGroupRepository = $this->createMock(CategoryGroupRepository::class);
        $this->categoryImporter = new BlacklistCategoryImporter(
            $this->entityManager,
            $this->categoryManager,
            $this->categoryRepository,
            $this->categoryGroupRepository
        );
    }

    public function testImport()
    {
        $this->categoryRepository
            ->expects($this->any())
            ->method('findAll')
            ->willReturn($this->getLocalCategories())
        ;

        $actualNewCategories = [];
        $this->entityManager
            ->expects($this->any())
            ->method('persist')
            ->will($this->returnCallback(
                function(Category $newCategory) use (&$actualNewCategories) {
                    $actualNewCategories[] = $newCategory;
                }
            ))
        ;
        $this->categoryImporter->import($this->getExternalCategories());

        $expectedNewCategories = $this->getNewCategories();

        $this->assertEquals(
            $this->transformToArray($expectedNewCategories),
            $this->transformToArray($actualNewCategories)
        );
    }

    /**
     * @param Category[] $categories
     * @return array
     */
    private function transformToArray(array $categories)
    {
        $result = [];
        foreach ($categories as $category) {
            $result[$category->getUniqueKey()] = $category->getName();
        }

        return $result;
    }

    private function getNewCategories()
    {
        return [
            (new Category())
                ->setName('Politically Exposed Person (PEP)')
                ->setDepth(1)
                ->setExternalId(1),
            (new Category())
                ->setName('Additional Domestic Screening Requirement')
                ->setDepth(2)
                ->setExternalId(20),
            (new Category())
                ->setName('Sanctions Lists')
                ->setDepth(2)
                ->setExternalId(1),
            (new Category())
                ->setName('Stimulants Control Act Violation')
                ->setDepth(3)
                ->setExternalId(28),
        ];
    }

    /**
     * @return Category[]
     */
    private function getLocalCategories()
    {
        return [
            (new Category())
                ->setName('Special Interest Person (SIP)')
                ->setDepth(1)
                ->setExternalId(3)
                ->setChildren([
                    (new Category())
                        ->setName('Other Official Lists')
                        ->setDepth(2)
                        ->setExternalId(2),
                    (new Category())
                        ->setName('Organised Crime Japan')
                        ->setDepth(2)
                        ->setExternalId(21)
                        ->setChildren([
                            (new Category())
                                ->setName('Battery or Assault')
                                ->setDepth(3)
                                ->setExternalId(29),
                        ])
                ]),
        ];
    }

    /**
     * @return Category[]
     */
    private function getExternalCategories()
    {
        $pepCategory = (new Category())
            ->setName('Politically Exposed Person (PEP)')
        ;
        $sipCategory = (new Category())
            ->setName('Special Interest Person (SIP)')
        ;
        $ocjCategory = (new Category())
            ->setName('Organised Crime Japan')
        ;

        return [
            $pepCategory
                ->setDepth(1)
                ->setExternalId(1)
                ->setChildren([
                    (new Category())
                        ->setName('Additional Domestic Screening Requirement')
                        ->setDepth(2)
                        ->setExternalId(20)
                        ->setParent($pepCategory),
                ]),
            $sipCategory
                ->setDepth(1)
                ->setExternalId(3)
                ->setChildren([
                    (new Category())
                        ->setName('Sanctions Lists')
                        ->setDepth(2)
                        ->setExternalId(1)
                        ->setParent($sipCategory),
                    (new Category())
                        ->setName('Other Official Lists')
                        ->setDepth(2)
                        ->setExternalId(2)
                        ->setParent($sipCategory),
                    $ocjCategory
                        ->setDepth(2)
                        ->setExternalId(21)
                        ->setParent($sipCategory)
                        ->setChildren([
                            (new Category())
                                ->setName('Stimulants Control Act Violation')
                                ->setDepth(3)
                                ->setExternalId(28)
                                ->setParent($ocjCategory),
                            (new Category())
                                ->setName('Battery or Assault')
                                ->setDepth(3)
                                ->setExternalId(29)
                                ->setParent($ocjCategory),
                        ])
                ]),
        ];
    }
}
