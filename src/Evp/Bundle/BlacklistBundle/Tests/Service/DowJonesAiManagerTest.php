<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Evp\Bundle\BlacklistBundle\Entity\AlternativeName;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\DowJonesAiManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Tests\BaseTestCase;
use Paysera\Client\DowJonesAi\DowJonesAi;
use Paysera\Client\DowJonesAi\Entity\DowJonesAIRequest;
use Paysera\Client\DowJonesAi\Entity\DowJonesAIResult;
use Paysera\Client\DowJonesAi\Entity\TransferDatum;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use ReflectionProperty;

class DowJonesAiManagerTest extends BaseTestCase
{
    /**
     * @var LoggerInterface|MockObject
     */
    private $logger;

    /**
     * @var DowJonesAi|MockObject
     */
    private $dowJonesAiClient;

    private DowJonesAiManager $dowJonesAiManager;

    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->dowJonesAiClient = $this->createMock(DowJonesAi::class);

        $this->dowJonesAiManager = new DowJonesAiManager(
            $this->dowJonesAiClient,
            '80',
            $this->logger
        );
    }

    /**
     * @param string $searchTransferDatumId
     * @param Profile $searchProfile
     * @param BlacklistResult[] $matchedResults
     * @param Client|null $searchClient
     * @param DowJonesAIResult|null $dowJonesAIResult
     * @param BlacklistResult[] $expectedResult
     * @param bool $expectedToCallUserTypeAi
     * @param bool $expectedToCallDowJonesAi
     * @param bool $isUserTypeAiException
     * @param bool $isDowJonesAiException
     * @param string|null $exceptionMessage
     *
     * @dataProvider blacklistSearchResultProvider
     */
    public function testFilterOutNotConfidentDowJonesProfiles(
        string $searchTransferDatumId,
        Profile $searchProfile,
        array $matchedResults,
        ?Client $searchClient,
        ?DowJonesAIResult $dowJonesAIResult,
        array $expectedResult,
        bool $expectedToCallDowJonesAi,
        bool $isDowJonesAiException,
        ?string $exceptionMessage = null
    ): void {
        if ($exceptionMessage !== null) {
            $this->logger
                ->expects($this->once())
                ->method('warning')
                ->with(
                    'AI client error, DJ ES results not filtered by confidence!',
                    [
                        $exceptionMessage,
                        $searchTransferDatumId
                    ]
                )
            ;
        }

        if ($expectedToCallDowJonesAi === true) {
            if ($isDowJonesAiException === true) {
                $this->dowJonesAiClient
                    ->expects($this->once())
                    ->method('createScore')
                    ->with(
                        (new DowJonesAIRequest())
                            ->setTransferDatum($this->prepareTransferDatum($searchProfile, $searchTransferDatumId))
                            ->setSearchResults($this->prepareSearchResults($searchProfile, $matchedResults))
                    )
                    ->willThrowException(new \Exception())
                ;
            } else {
                $this->dowJonesAiClient
                    ->expects($this->once())
                    ->method('createScore')
                    ->with(
                        (new DowJonesAIRequest())
                            ->setTransferDatum($this->prepareTransferDatum($searchProfile, $searchTransferDatumId))
                            ->setSearchResults($this->prepareSearchResults($searchProfile, $matchedResults))
                    )
                    ->willReturn($dowJonesAIResult)
                ;
            }
        } else {
            $this->dowJonesAiClient
                ->expects($this->never())
                ->method('createScore')
            ;
        }

        $result = $this->dowJonesAiManager->filterOutNotConfidentDowJonesProfiles(
            $searchTransferDatumId,
            $searchProfile,
            $matchedResults
        );

        $this->assertEquals(
            $expectedResult,
            $result
        );
    }

    public function blacklistSearchResultProvider(): array
    {
        return [
            'Empty array given' => [
                '1',
                (new Profile())->setFirstName('Just test person name'),
                [],
                null,
                null,
                [],
                false,
                false,
            ],
            'User Type AI available, Dow Jones AI not available, no filtering by confidence' => [
                '3',
                $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                ],
                null,
                new DowJonesAIResult(),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                ],
                true,
                true,
                'Cannot fetch data from Dow Jones AI. Search data: id - 3, name - John Smith',
            ],
            'Locking for John Smith with confidence (some records filtered out)' => [
                '4',
                $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(4, 'Smith John', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(5, 'John John', Profile::TYPE_PERSON, 'lt')
                    ),
                ],
                null,
                new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '2',
                                'transfer_datum_id' => '2',
                                'confidence' => 5
                            ],
                            [
                                'search_result_id' => '3',
                                'transfer_datum_id' => '2',
                                'confidence' => 80
                            ],
                            [
                                'search_result_id' => '4',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '5',
                                'transfer_datum_id' => '2',
                                'confidence' => 94
                            ],
                        ],
                    ],
                    'scores'
                ),
                [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                        )
                        ->setConfidence(80),
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(5, 'John John', Profile::TYPE_PERSON, 'lt')
                        )
                        ->setConfidence(94),
                ],
                true,
                false,
            ],
            'Incorrect response format from DJ, result not filtered by confidence' => [
                '5',
                $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                    ),
                ],
                null,
                new DowJonesAIResult([], 'scores'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                    ),
                ],
                true,
                false,
                'Something wrong with response format from Dow Jones AI. Search data: id - 5, name - John Smith',
            ],
            'All results filtered out, because of too low confidence' => [
                '7',
                $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(4, 'Smith John', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(5, 'John John', Profile::TYPE_PERSON, 'lt')
                    ),
                ],
                null,
                new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '2',
                                'transfer_datum_id' => '2',
                                'confidence' => 5
                            ],
                            [
                                'search_result_id' => '3',
                                'transfer_datum_id' => '2',
                                'confidence' => 79
                            ],
                            [
                                'search_result_id' => '4',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '5',
                                'transfer_datum_id' => '2',
                                'confidence' => 40
                            ],
                        ],
                    ],
                    'scores'
                ),
                [],
                true,
                false,
            ],
            'Filtering with client data provided, no request to user type AI endpoint' => [
                '8',
                $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(3, 'Smith', Profile::TYPE_PERSON, 'lv')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(4, 'Smith John', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(5, 'John John', Profile::TYPE_PERSON, 'lt')
                    ),
                ],
                new ClientNatural(),
                new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '2',
                                'transfer_datum_id' => '2',
                                'confidence' => 100
                            ],
                            [
                                'search_result_id' => '3',
                                'transfer_datum_id' => '2',
                                'confidence' => 79
                            ],
                            [
                                'search_result_id' => '4',
                                'transfer_datum_id' => '2',
                                'confidence' => 0
                            ],
                            [
                                'search_result_id' => '5',
                                'transfer_datum_id' => '2',
                                'confidence' => 40
                            ],
                        ],
                    ],
                    'scores'
                ),
                [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                        )
                        ->setConfidence(100),
                ],
                true,
                false,
            ],
            'Filtering when matched profiles have alternative names. Alternative names are taken into account when filtering' => [
                'searchTransferDatumId' => '9',
                'searchProfile' => $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(2, 'John', Profile::TYPE_PERSON, 'by')
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            3,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(function (array $name) {
                                return $this->prepareAlternativeName($name['id'], $name['first_name'], $name['last_name']);
                            }, [
                                ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smithy'],
                                ['id' => 2, 'first_name' => 'Johny', 'last_name' => 'Smith']
                            ])
                        )
                    ),
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            4,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'es',
                            array_map(function (array $name) {
                                return $this->prepareAlternativeName($name['id'], $name['first_name'], $name['last_name']);
                            }, [
                                ['id' => 3, 'first_name' => 'John', 'last_name' => 'Smithy']
                            ])
                        )
                    )
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 80
                            ],
                            [
                                'search_result_id' => '2',
                                'transfer_datum_id' => '2',
                                'confidence' => 5
                            ],
                            [
                                'search_result_id' => '3',
                                'transfer_datum_id' => '2',
                                'confidence' => 50
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(3, 1),
                                'transfer_datum_id' => '2',
                                'confidence' => 85
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(3, 2),
                                'transfer_datum_id' => '2',
                                'confidence' => 90
                            ],
                            [
                                'search_result_id' => '4',
                                'transfer_datum_id' => '2',
                                'confidence' => 95
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(4, 3),
                                'transfer_datum_id' => '2',
                                'confidence' => 10
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(1, 'John Smith', Profile::TYPE_PERSON, 'lt')
                        )
                        ->setConfidence(80),
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                3,
                                'Smith',
                                Profile::TYPE_PERSON,
                                'lv',
                                array_map(function (array $name) {
                                    return $this->prepareAlternativeName($name['id'], $name['first_name'], $name['last_name']);
                                }, [
                                        ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smithy'],
                                        ['id' => 2, 'first_name' => 'Johny', 'last_name' => 'Smith'],
                                   ])
                            )
                        )
                        ->setConfidence(85),
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                4,
                                'Smith',
                                Profile::TYPE_PERSON,
                                'es',
                                array_map(function (array $name) {
                                    return $this->prepareAlternativeName($name['id'], $name['first_name'], $name['last_name']);
                                }, [
                                        ['id' => 3, 'first_name' => 'John', 'last_name' => 'Smithy'],
                                   ])
                            )
                        )
                        ->setConfidence(95)
                ],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'Profile with 2 alternative names, both alternative names longer than expected minimum of characters' => [
                'searchTransferDatumId' => '10',
                'searchProfile' => $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smithy'],
                                    ['id' => 2, 'first_name' => 'Jo', 'last_name' => 'S'],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 1),
                                'transfer_datum_id' => '2',
                                'confidence' => 85,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 2),
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                1,
                                'Smith',
                                Profile::TYPE_PERSON,
                                'lv',
                                array_map(
                                    function (array $name) {
                                        return $this->prepareAlternativeName(
                                            $name['id'],
                                            $name['first_name'],
                                            $name['last_name']
                                        );
                                    },
                                    [
                                        ['id' => 1, 'first_name' => 'John', 'last_name' => 'Smithy'],
                                        ['id' => 2, 'first_name' => 'Jo', 'last_name' => 'S'],
                                    ]
                                )
                            )
                        )
                        ->setConfidence(85),
                ],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'Profile with 2 alternative names, one alternative name no longer than expected minimum of characters' => [
                'searchTransferDatumId' => '11',
                'searchProfile' => $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'J', 'last_name' => 'S'],
                                    ['id' => 2, 'first_name' => 'John', 'last_name' => 'Sm'],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 2),
                                'transfer_datum_id' => '2',
                                'confidence' => 95,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                1,
                                'Smith',
                                Profile::TYPE_PERSON,
                                'lv',
                                array_map(
                                    function (array $name) {
                                        return $this->prepareAlternativeName(
                                            $name['id'],
                                            $name['first_name'],
                                            $name['last_name']
                                        );
                                    },
                                    [
                                        ['id' => 1, 'first_name' => 'J', 'last_name' => 'S'],
                                        ['id' => 2, 'first_name' => 'John', 'last_name' => 'Sm'],
                                    ]
                                )
                            )
                        )
                        ->setConfidence(95),
                ],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'Profile with 5 alternative names, 3 alternative names no longer than expected minimum of characters' => [
                'searchTransferDatumId' => '11',
                'searchProfile' => $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'J', 'last_name' => 'S'],
                                    ['id' => 2, 'first_name' => 'John', 'last_name' => 'Sm'],
                                    ['id' => 3, 'first_name' => 'John', 'last_name' => ''],
                                    ['id' => 4, 'first_name' => '', 'last_name' => 'Sm'],
                                    ['id' => 5, 'first_name' => '', 'last_name' => ''],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 2),
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 3),
                                'transfer_datum_id' => '2',
                                'confidence' => 90,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                1,
                                'Smith',
                                Profile::TYPE_PERSON,
                                'lv',
                                array_map(
                                    function (array $name) {
                                        return $this->prepareAlternativeName(
                                            $name['id'],
                                            $name['first_name'],
                                            $name['last_name']
                                        );
                                    },
                                    [
                                        ['id' => 1, 'first_name' => 'J', 'last_name' => 'S'],
                                        ['id' => 2, 'first_name' => 'John', 'last_name' => 'Sm'],
                                        ['id' => 3, 'first_name' => 'John', 'last_name' => ''],
                                        ['id' => 4, 'first_name' => '', 'last_name' => 'Sm'],
                                        ['id' => 5, 'first_name' => '', 'last_name' => ''],
                                    ]
                                )
                            )
                        )
                        ->setConfidence(90),
                ],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'Profile with alternative names, but those no longer than expected minimum of characters' => [
                'searchTransferDatumId' => '12',
                'searchProfile' => $this->prepareProfile(0, 'John Smith', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Smith',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'J', 'last_name' => 'S'],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'MB GIRTONAS, matched by alternative name MB of some profile (expected no alternative name AI search)' => [
                'searchTransferDatumId' => '13',
                'searchProfile' => $this->prepareProfile(0, 'MB GIRTONAS', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Some GIRTONAS',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'MB', 'last_name' => ''],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
            'MB, matched by alternative name MB of some profile (expected alternative name AI search)' => [
                'searchTransferDatumId' => '14',
                'searchProfile' => $this->prepareProfile(0, 'MB', Profile::TYPE_PERSON, 'lt'),
                'matchedResults' => [
                    (new BlacklistResult())->setFoundProfile(
                        $this->prepareProfile(
                            1,
                            'Some Random Profile',
                            Profile::TYPE_PERSON,
                            'lv',
                            array_map(
                                function (array $name) {
                                    return $this->prepareAlternativeName(
                                        $name['id'],
                                        $name['first_name'],
                                        $name['last_name']
                                    );
                                },
                                [
                                    ['id' => 1, 'first_name' => 'MB', 'last_name' => ''],
                                ]
                            )
                        )
                    ),
                ],
                'searchClient' => null,
                'dowJonesAIResult' => new DowJonesAIResult(
                    [
                        'scores' => [
                            [
                                'search_result_id' => '1',
                                'transfer_datum_id' => '2',
                                'confidence' => 50,
                            ],
                            [
                                'search_result_id' => $this->prepareAlternativeNameTransferDatumId(1, 1),
                                'transfer_datum_id' => '2',
                                'confidence' => 100,
                            ],
                        ],
                    ],
                    'scores'
                ),
                'expectedResult' => [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            $this->prepareProfile(
                                1,
                                'Some Random Profile',
                                Profile::TYPE_PERSON,
                                'lv',
                                array_map(
                                    function (array $name) {
                                        return $this->prepareAlternativeName(
                                            $name['id'],
                                            $name['first_name'],
                                            $name['last_name']
                                        );
                                    },
                                    [
                                        ['id' => 1, 'first_name' => 'MB', 'last_name' => ''],
                                    ]
                                )
                            )
                        )
                        ->setConfidence(100),
                ],
                'expectedToCallDowJonesAi' => true,
                'isDowJonesAiException' => false,
            ],
        ];
    }

    private function prepareTransferDatum(
        Profile $profile,
        string $transferDatumId,
        ?string $alternativeName = null
    ): TransferDatum {
        $transferDatum = (new TransferDatum())
            ->setId($transferDatumId)
            ->setName($alternativeName ?? $profile->getDisplayName())
        ;

        if (!empty($profile->getCountry())) {
            $transferDatum->setCountry(strtolower($profile->getCountry()));
        }

        return $transferDatum;
    }

    /**
     * @param Profile $searchProfile
     * @param BlacklistResult[] $matchedResults
     *
     * @return TransferDatum[]
     */
    private function prepareSearchResults(Profile $searchProfile, array $matchedResults): array
    {
        $searchResult = array_map(
            function (BlacklistResult $blacklistResult) {
                return $this->prepareTransferDatum(
                    $blacklistResult->getFoundProfile(),
                    (string) $blacklistResult->getFoundProfile()->getId()
                );
            },
            $matchedResults
        );

        return array_merge($searchResult, $this->prepareAlternativeNamesSearchResults($searchProfile, $matchedResults));
    }

    private function prepareAlternativeNamesSearchResults(Profile $searchProfile, array $matchedResults): array
    {
        $searchResults = [];
        foreach ($matchedResults as $matchedResult) {
            foreach ($matchedResult->getFoundProfile()->getAlternativeNames() as $alternativeName) {
                if (
                    strlen($alternativeName->getDisplayName()) < 4
                    && strlen($searchProfile->getDisplayName()) >= 4
                ) {
                    continue;
                }

                $datumId = $this->prepareAlternativeNameTransferDatumId(
                    $matchedResult->getFoundProfile()->getId(),
                    $alternativeName->getId()
                );
                $searchResults[] = $this->prepareTransferDatum(
                    $matchedResult->getFoundProfile(),
                    $datumId,
                    $alternativeName->getDisplayName()
                );
            }
        }

        return $searchResults;
    }

    private function prepareProfile(
        int $id,
        string $name,
        string $type,
        string $country,
        array $alternativeNames = []
    ): Profile {
        $profile = (new Profile())
            ->setFirstName($name)
            ->setType($type)
        ;

        foreach ($alternativeNames as $alternativeName) {
            $profile->addAlternativeName($alternativeName);
        }

        $profileId = new ReflectionProperty(Profile::class, 'id');
        $profileId->setAccessible(true);
        $profileId->setValue($profile, $id);

        return $profile;
    }

    private function prepareAlternativeName(int $id, string $firstName, string $lastName): AlternativeName
    {
        $alternativeName = new AlternativeName();
        $alternativeName->setFirstName($firstName);
        $alternativeName->setLastName($lastName);

        $alternativeNameId = new ReflectionProperty(AlternativeName::class, 'id');
        $alternativeNameId->setAccessible(true);
        $alternativeNameId->setValue($alternativeName, $id);

        return $alternativeName;
    }

    private function prepareAlternativeNameTransferDatumId(int $profileId, int $alternativeNameId): string
    {
        return md5(sprintf(
            '%d:%d',
            $profileId,
            $alternativeNameId
        ));
    }
}
