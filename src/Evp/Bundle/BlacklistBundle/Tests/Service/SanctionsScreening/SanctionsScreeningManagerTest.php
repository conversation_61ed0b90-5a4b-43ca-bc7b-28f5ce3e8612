<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\SanctionsScreening;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningRepository;
use Evp\Bundle\BlacklistBundle\Service\ChangeTracker\ClientSanctionsScreeningChangeTracker;
use Evp\Bundle\BlacklistBundle\Service\SanctionsScreening\SanctionsScreeningManager;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Lock\Factory;
use Symfony\Component\Lock\LockInterface;

class SanctionsScreeningManagerTest extends TestCase
{
    private MockObject $entityManager;
    private MockObject $repository;
    private FixturesHelper $fixturesHelper;
    private SanctionsScreeningManager $sanctionsScreeningManager;

    public function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->repository = $this->createMock(ClientSanctionsScreeningRepository::class);
        $this->fixturesHelper = new FixturesHelper();

        $factory = $this->createMock(Factory::class);
        $lock = $this->createMock(LockInterface::class);

        $factory
            ->expects($this->any())
            ->method('createLock')
            ->willReturn($lock)
        ;
        $lock->expects($this->any())
            ->method('acquire')
            ->willReturn(true)
        ;
        $lock->expects($this->any())
            ->method('release')
        ;

        $this->sanctionsScreeningManager = new SanctionsScreeningManager(
            $this->entityManager,
            $this->repository,
            $this->createMock(ClientSanctionsScreeningChangeTracker::class),
            $factory
        );
    }

    public function testDoNotCreateSanctionScreeningForBlacklistedResult(): void
    {
        $result = $this->fixturesHelper->createClientBlacklistCheckResult();

        $this->repository
            ->expects($this->never())
            ->method('isUnprocessedClientExists')
        ;

        $this->repository
            ->expects($this->never())
            ->method('isClientBlacklistCheckResultExists')
        ;

        $this->entityManager
            ->expects($this->never())
            ->method('persist')
        ;

        $this->sanctionsScreeningManager->create($result);
    }

    public function testCreateSanctionScreeningForBlacklistedResult(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::BASIC_IDENTIFIED);

        $blacklist = $this->fixturesHelper->createBlacklist();
        $result = $this->fixturesHelper->createClientBlacklistCheckResult()
            ->setBlacklisted(true)
            ->setClient($client)
            ->setBlacklist($blacklist)
        ;

        $this->repository
            ->expects($this->once())
            ->method('isUnprocessedClientExists')
            ->with($result->getClient())
            ->willReturn(false)
        ;

        $this->repository
            ->expects($this->once())
            ->method('isClientBlacklistCheckResultExists')
            ->with($result)
            ->willReturn(false)
        ;

        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($this->fixturesHelper->createClientSanctionsScreening($client, $result))
        ;

        $this->sanctionsScreeningManager->create($result);
    }

    public function testCreateSanctionScreeningIfUnprocessedClientExists(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::BASIC_IDENTIFIED);
        $blacklist = $this->fixturesHelper->createBlacklist();
        $result = $this->fixturesHelper->createClientBlacklistCheckResult()
            ->setBlacklisted(true)
            ->setClient($client)
            ->setBlacklist($blacklist)
        ;

        $sanctionsScreening = $this->fixturesHelper->createClientSanctionsScreening($client, $result);

        $this->repository
            ->expects($this->once())
            ->method('isUnprocessedClientExists')
            ->with($result->getClient())
            ->willReturn(true)
        ;

        $this->repository
            ->expects($this->never())
            ->method('isClientBlacklistCheckResultExists')
        ;

        $this->entityManager
            ->expects($this->never())
            ->method('persist')
            ->with($sanctionsScreening)
        ;

        $this->sanctionsScreeningManager->create($result);
    }

    public function testCreateSanctionScreeningIfClientBlacklistCheckResultExists(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::BASIC_IDENTIFIED);
        $blacklist = $this->fixturesHelper->createBlacklist();
        $result = $this->fixturesHelper->createClientBlacklistCheckResult()
            ->setBlacklisted(true)
            ->setClient($client)
            ->setBlacklist($blacklist)
        ;

        $sanctionsScreening = $this->fixturesHelper->createClientSanctionsScreening(
            $client,
            $result,
            ClientSanctionsScreening::STATUS_COMPLETED
        )->setClientBlacklistCheckResult($result);

        $this->repository
            ->expects($this->once())
            ->method('isUnprocessedClientExists')
            ->with($result->getClient())
            ->willReturn(false)
        ;

        $this->repository
            ->expects($this->once())
            ->method('isClientBlacklistCheckResultExists')
            ->with($result)
            ->willReturn(true)
        ;

        $this->entityManager
            ->expects($this->never())
            ->method('persist')
            ->with($sanctionsScreening)
        ;

        $this->sanctionsScreeningManager->create($result);
    }

    public function testDoNotCreateSanctionScreeningForClientWithLevelUnidentified(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::UNIDENTIFIED);

        $blacklist = $this->fixturesHelper->createBlacklist();
        $result = $this->fixturesHelper->createClientBlacklistCheckResult()
            ->setBlacklisted(true)
            ->setClient($client)
            ->setBlacklist($blacklist)
        ;

        $this->repository
            ->expects($this->never())
            ->method('isUnprocessedClientExists')
        ;

        $this->repository
            ->expects($this->never())
            ->method('isClientBlacklistCheckResultExists')
        ;

        $this->entityManager
            ->expects($this->never())
            ->method('persist')
        ;

        $this->sanctionsScreeningManager->create($result);
    }

    public function testDoNotCreateSanctionScreeningForClientWithLevelSemiIdentified(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setLevel(IdentificationLevels::SEMI_IDENTIFIED);

        $blacklist = $this->fixturesHelper->createBlacklist();
        $result = $this->fixturesHelper->createClientBlacklistCheckResult()
            ->setBlacklisted(true)
            ->setClient($client)
            ->setBlacklist($blacklist)
        ;

        $this->repository
            ->expects($this->never())
            ->method('isUnprocessedClientExists')
        ;

        $this->repository
            ->expects($this->never())
            ->method('isClientBlacklistCheckResultExists')
        ;

        $this->entityManager
            ->expects($this->never())
            ->method('persist')
        ;

        $this->sanctionsScreeningManager->create($result);
    }
}
