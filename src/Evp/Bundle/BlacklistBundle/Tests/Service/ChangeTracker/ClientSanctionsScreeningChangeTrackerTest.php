<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\ChangeTracker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningChangeLogRepository;
use Evp\Bundle\BlacklistBundle\Service\ChangeTracker\ClientSanctionsScreeningChangeTracker;
use Evp\Bundle\TestCaseBundle\Context\Functional\Database\PersistableWebTestCase;
use Paysera\Component\Tests\Fixtures\FixturesHelper;

class ClientSanctionsScreeningChangeTrackerTest extends PersistableWebTestCase
{
    private ?ClientSanctionsScreeningChangeTracker $tracker;
    private ?EntityManagerInterface $entityManager;
    private ?FixturesHelper $fixturesHelper;
    private ?ClientSanctionsScreeningChangeLogRepository $changeLogRepository;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $container = $this->client->getContainer();

        $this->tracker = $container->get('evp_blacklist.service.change_tracker.blacklist_client_sanctions_screening');
        $this->entityManager = $container->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->changeLogRepository = $container->get('evp_blacklist.repository.blacklist_client_sanctions_screening_change_log');
    }

    public function testTracker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklistCheckResult = $this->fixturesHelper->createClientBlacklistCheckResult();
        $this->entityManager->flush();

        $sanctionsScreening = $this->fixturesHelper->createClientSanctionsScreening($client, $blacklistCheckResult);

        $this->tracker->trackChanges($sanctionsScreening, true);

        $this->entityManager->flush();

        $logs = $this->changeLogRepository->findBy(['sanctionsScreening' => $sanctionsScreening]);

        $this->assertNotEmpty($logs);
        $this->assertCount(6, $logs);
    }

    public function testTrackerSecondUpdate(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklistCheckResult = $this->fixturesHelper->createClientBlacklistCheckResult();
        $this->entityManager->flush();

        $sanctionsScreening = $this->fixturesHelper->createClientSanctionsScreening($client, $blacklistCheckResult);

        $this->tracker->trackChanges($sanctionsScreening, true);

        $this->entityManager->flush();

        $logs1 = $this->changeLogRepository->findBy(['sanctionsScreening' => $sanctionsScreening]);

        /** @var ClientSanctionsScreening $sanctionsScreeningNew */
        $sanctionsScreeningNew = $this->entityManager
            ->getRepository('EvpBlacklistBundle:ClientSanctionsScreening')
            ->find($sanctionsScreening->getId())
        ;

        $sanctionsScreeningNew->setReviewerId('42')
            ->setReviewAction(ClientSanctionsScreening::REVIEW_ACTION_ASSIGNED);

        $this->entityManager->persist($sanctionsScreeningNew);

        $this->tracker->trackChanges($sanctionsScreeningNew);

        $this->entityManager->flush();

        $logs2 = $this->changeLogRepository->findBy(['sanctionsScreening' => $sanctionsScreeningNew]);

        $actualDifferent = count($logs2) - count($logs1);
        $this->assertEquals(2, $actualDifferent);
    }

}
