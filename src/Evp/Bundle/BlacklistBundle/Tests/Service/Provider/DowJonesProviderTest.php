<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service\Provider;

use Evp\Bundle\BlacklistBundle\Service\CategoriesTreeBuilder;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\AssociationRelationshipXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category1XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category2XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category3XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\EntityAssociationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\EntityProfileXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\OccupationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\PersonAssociationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\PersonProfileXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\SanctionsReferenceXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\XmlParserHelper;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParserResolver;
use Evp\Bundle\BlacklistBundle\Service\Provider\DowJonesProvider;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Evp\Bundle\BlacklistBundle\Service\Resolver\CountryCodeResolver;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DowJonesProviderTest extends TestCase
{
    /**
     * @var DowJonesFeedReader|MockObject
     */
    private $feedReader;
    private string $resourcesPath;

    /**
     * @var DowJonesParserResolver
     */
    private $parserResolver;

    /**
     * @var CategoriesTreeBuilder|MockObject
     */
    private $categoriesTreeBuilder;

    public function setUp(): void
    {
        $this->resourcesPath = __DIR__ . DIRECTORY_SEPARATOR . 'Resources';

        $this->feedReader = $this->createPartialMock(DowJonesFeedReader::class, ['getFeedFileByVersion']);
        $this->parserResolver = new DowJonesParserResolver();
        $this->categoriesTreeBuilder = $this->createMock(CategoriesTreeBuilder::class);
    }

    public function testGetCategories()
    {
        $this->parserResolver->addParser(new Category1XmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new Category2XmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new Category3XmlParser(new XmlParserHelper()));

        $feedFilePath = $this->resourcesPath . DIRECTORY_SEPARATOR . 'PFA2_201712022200_D.zip';
        $this->feedReader
            ->expects($this->once())
            ->method('getFeedFileByVersion')
            ->willReturn($feedFilePath)
        ;

        $categoriesCounted = 0;
        $this->categoriesTreeBuilder
            ->expects($this->once())
            ->method('build')
            ->will($this->returnCallback(
                function($providerCategories) use (&$categoriesCounted) {
                    $categoriesCounted = count($providerCategories);
                }
            ))
        ;
        $this->createProvider()->getCategories('');

        $this->assertEquals(94, $categoriesCounted);
    }

    public function testGetProfiles()
    {
        $countryCodeResolver = new CountryCodeResolver([
            'PANA' => 'PA',
            'SPAIN' => 'ES',
            'NOTK' => null,
            'INDIA' => 'IN',
            'IRAQ' => 'IQ',
        ]);
        $this->parserResolver->addParser(new PersonProfileXmlParser(new XmlParserHelper(), $countryCodeResolver));
        $this->parserResolver->addParser(new EntityProfileXmlParser(new XmlParserHelper(), $countryCodeResolver));
        $this->parserResolver->addParser(new PersonAssociationXmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new EntityAssociationXmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new OccupationXmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new SanctionsReferenceXmlParser(new XmlParserHelper()));
        $this->parserResolver->addParser(new AssociationRelationshipXmlParser(new XmlParserHelper()));

        $feedFilePath = $this->resourcesPath . DIRECTORY_SEPARATOR . 'PFA2_201712022200_D.zip';
        $this->feedReader
            ->expects($this->once())
            ->method('getFeedFileByVersion')
            ->willReturn($feedFilePath)
        ;

        $profiles = $this->createProvider()->getProfiles('');
        $profilesCounted = 0;
        while ($profiles->valid()) {
            $profilesCounted++;
            $profiles->next();
        }
        $this->assertEquals(42, $profilesCounted);
    }

    private function createProvider()
    {
        return new DowJonesProvider(
            'Dow Jones',
            'dow_jones',
            $this->parserResolver,
            $this->categoriesTreeBuilder,
            $this->feedReader
        );
    }
}
