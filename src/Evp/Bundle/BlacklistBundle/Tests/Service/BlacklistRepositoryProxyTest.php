<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\PDO\Exception as DoctrinePDOException;
use Doctrine\DBAL\Exception\ConnectionLost;
use Doctrine\DBAL\Exception\DriverException;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistRepositoryProxy;
use Mockery;
use Mockery\Expectation;
use Mockery\MockInterface;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PDOException;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\Debug\BufferingLogger;

class BlacklistRepositoryProxyTest extends PersistableWebTestCase
{
    private const LOG_MESSAGE_BASE
        = 'Evp\Bundle\BlacklistBundle\Service\BlacklistRepositoryProxy::findOneByKey DB exception, ';
    private static EntityManager $concurrentConnection;
    private BlacklistRepositoryProxy $blacklistRepositoryProxyInstance;

    /**
     * @var BlacklistRepository|MockObject
     */
    private $blacklistRepositoryMock;

    /**
     * @var LoggerInterface|MockObject
     */
    private $loggerMock;

    private function setUpForUnit(): void
    {
        $this->blacklistRepositoryMock = $this->createMock(BlacklistRepository::class);
        $entityManager = $this->createMock(EntityManager::class);
        $entityManager
            ->method('getConnection')
            ->willReturn($this->createMock(Connection::class))
        ;
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->blacklistRepositoryProxyInstance = new BlacklistRepositoryProxy(
            $this->blacklistRepositoryMock,
            $this->loggerMock,
            $entityManager,
            1,
            1
        );
    }

    public function testFindOneByKeyCatchingConnectionLostException(): void
    {
        $this->setUpForUnit();
        $exception = new ConnectionLost(
            'General error: 2006 MySQL server has gone away...',
            new DoctrinePDOException(new PDOException())
        );
        $this->blacklistRepositoryMock
            ->method('findOneByKey')
            ->will($this->throwException($exception));
        $this->expectExceptionObject($exception);
        $this->blacklistRepositoryProxyInstance->findOneByKey('key');
    }

    public function testFindOneByKeyCatchingReadOnlyException(): void
    {
        $this->setUpForUnit();
        $exception = new DriverException(
            'General error: 1290 The MariaDB server is running with the --read-only option...',
            new DoctrinePDOException(new PDOException())
        );
        $this->blacklistRepositoryMock
            ->method('findOneByKey')
            ->will($this->throwException($exception));
        $this->expectExceptionObject($exception);
        $this->loggerMock
            ->expects($this->once())
            ->method('error')
            ->with(
                'BlacklistBundle exceptional case: ' . self::LOG_MESSAGE_BASE . 'aborting retrying attempts.',
                [$exception]
            );
        $this->blacklistRepositoryProxyInstance->findOneByKey('key');
    }

    protected static function getMockedServices(): array
    {
        return [
            'logger' => self::createLoggerUnlocker(),
        ];
    }

    public function testFindOneByKeyRetrying(): void
    {
        $this->createClientWithNewDatabaseNoTransaction();
        $blacklistRepositoryProxy = $this->getContainer()->get('evp_blacklist.service.blacklist_repository_proxy');
        $fixturesHelper = new FixturesHelper($this->getEntityManager());
        $blacklist = $fixturesHelper->createBlacklist('Test Name', 'test_key');
        $this->getEntityManager()->flush();

        $this->lockDb();
        $this->getEntityManager()->getConnection()->beginTransaction();
        $fetchedBlacklist = $blacklistRepositoryProxy->findOneByKey('test_key');

        $this->assertSame($blacklist, $fetchedBlacklist);
    }

    private function lockDb(): void
    {

        $this->getEntityManager()->getConnection()->executeStatement('PRAGMA busy_timeout=1;');
        $this->getEntityManager()->getConnection()->executeStatement('PRAGMA journal_mode=DELETE;');
        $concurrentConnection = EntityManager::create(
            $this->getEntityManager()->getConnection()->getParams(),
            $this->getEntityManager()->getConfiguration()
        );
        $concurrentConnection->getConnection()->executeStatement('BEGIN EXCLUSIVE TRANSACTION;');

        self::$concurrentConnection = $concurrentConnection;
    }

    private static function createLoggerUnlocker(): MockInterface
    {
        $mock = Mockery::mock(BufferingLogger::class)->makePartial();
        /** @var Expectation $expectation */
        $expectation = $mock->shouldReceive('info');
        $expectation
            ->andReturnUsing(static function(string $message) {
                if ($message === self::LOG_MESSAGE_BASE . 'retrying.') {
                    self::$concurrentConnection->getConnection()->executeStatement('END TRANSACTION;');
                }
            })
        ;

        return $mock;
    }
}
