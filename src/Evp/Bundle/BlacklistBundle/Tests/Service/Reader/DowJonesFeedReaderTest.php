<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service\Reader;

use Evp\Bundle\BlacklistBundle\Entity\ProviderCategory;
use Evp\Bundle\BlacklistBundle\Service\Downloader;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category1XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category2XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category3XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\XmlParserHelper;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJones\FeedProviderInterface;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJones\FullFeedProvider;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJones\IncrementalDailyFeedProvider;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class DowJonesFeedReaderTest extends TestCase
{
    /** @var Downloader|MockObject */
    private Downloader $downloader;

    /** @var LoggerInterface|MockObject */
    private LoggerInterface $logger;

    public function setUp(): void
    {
        $this->downloader = $this->createMock(Downloader::class);
        $this->logger = $this->createMock(LoggerInterface::class);
    }

    public function testResolveIncrementalDailyFeedFileVersion(): void
    {
        $this->downloader
            ->expects($this->any())
            ->method('getContent')
            ->willReturn(
                'pfa2_201712082200_i.zip,'.
                'pfa2_201712042200_d.zip,'.
                'pfa2_201712032200_d.zip,'.
                'pfa2_201712022200_d.zip,'.
                'pfa2_201712012200_d.zip,'.
                'pfa2_201711302200_f_splits.zip,'.
                'pfa2_201711302200_f.zip,'.
                'pfa2_201711302200_i.zip'
            )
        ;
        $feedReader = $this->createFeedReader(new IncrementalDailyFeedProvider());

        $this->assertNull($feedReader->resolveFeedFileVersion(201712042200, false));
        $this->assertSame(201712032200, $feedReader->resolveFeedFileVersion(201712022200, false));
    }

    public function testResolveFullFeedFileVersion(): void
    {
        $this->downloader
            ->expects($this->any())
            ->method('getContent')
            ->willReturn(
                'pfa2_201712192200_f_splits.zip,'.
                'pfa2_201712192200_f.zip,'.
                'pfa2_201712182200_f_splits.zip,'.
                'pfa2_201712182200_f.zip,'.
                'pfa2_201712172200_f_splits.zip,'.
                'pfa2_201712172200_f.zip,'.
                'pfa2_201712162200_f_splits.zip'
            )
        ;
        $feedReader = $this->createFeedReader(new FullFeedProvider());

        $this->assertNull($feedReader->resolveFeedFileVersion(201712192200, false));
        $this->assertSame(201712192200, $feedReader->resolveFeedFileVersion(201712182200, false));
    }

    public function testReadFirstElementFromFeedFile(): void
    {
        $file = __DIR__ . '/../Resources/PFA2_201712022200_D.zip';

        $parserHelper = new XmlParserHelper();
        $parserClasses = [
            new Category1XmlParser($parserHelper),
            new Category2XmlParser($parserHelper),
            new Category3XmlParser($parserHelper),
        ];

        $expectedCategoriesParts = $this->getExpectedProviderCategories();
        $feedReader = $this->createFeedReader(new FullFeedProvider());

        foreach ($parserClasses as $key => $parserClass) {
            $expectedCategories = $expectedCategoriesParts[$key];
            $result = iterator_to_array($feedReader->readFirstElementFromFeedFile($file, $parserClass));

            for ($i = 0; $i < count($expectedCategories); $i++) {
                $this->assertEquals($expectedCategories[$i], $result[$i]);
            }
        }
    }

    private function createFeedReader(FeedProviderInterface $providerType): DowJonesFeedReader
    {
        return new DowJonesFeedReader(
            $providerType,
            $this->downloader,
            $this->logger
        );
    }

    /**
     * @return ProviderCategory[][]
     */
    private function getExpectedProviderCategories(): array
    {
        $data = json_decode(file_get_contents(__DIR__ . '/../Resources/PFA2_201712022200_D_categories.json'), true);
        $categoriesParts = [];

        foreach ($data as $depth => $items) {
            $categories = [];

            foreach ($items as $id => $item) {
                $categories[] = (new ProviderCategory())
                    ->setId($id)
                    ->setName($item['name'])
                    ->setDepth($depth)
                    ->setParentId($item['parent'])
                ;
            }

            $categoriesParts[] = $categories;
        }

        return $categoriesParts;
    }
}
