<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;
use Evp\Bundle\BlacklistBundle\Entity\NamesComparisonResult;
use Evp\Bundle\BlacklistBundle\Service\NamesComparisonResultService;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;
use PHPUnit\Framework\TestCase;

class NamesComparisonResultServiceTest extends TestCase
{
    private EntityManager $entityManager;

    private NamesComparisonResultService $service;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->service = new NamesComparisonResultService($this->entityManager);
    }

    public function testSaveComparisonResultsWithOnlyTransfer(): void
    {
        $namesComparisonResultDto = $this->createNamesComparisonResultDto();
        $transfer = $this->createMock(Transfer::class);

        $context = new ProfileFilterContext(false, null, $transfer);

        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(NamesComparisonResult::class));

        $result = $this->service->saveComparisonResults($namesComparisonResultDto, $context);

        $this->assertInstanceOf(NamesComparisonResult::class, $result);
        $this->assertSame($transfer, $result->getTransfer());
        $this->assertNull($result->getBlacklistCheckResult());
    }

    public function testSaveComparisonResultsWithOnlyBlacklist(): void
    {
        $namesComparisonResultDto = $this->createNamesComparisonResultDto();
        $blacklistCheckResult = $this->createMock(ClientBlacklistCheckResult::class);

        $context = new ProfileFilterContext(false, $blacklistCheckResult);

        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->with($this->isInstanceOf(NamesComparisonResult::class));

        $result = $this->service->saveComparisonResults($namesComparisonResultDto, $context);

        $this->assertInstanceOf(NamesComparisonResult::class, $result);
        $this->assertNull($result->getTransfer());
        $this->assertSame($blacklistCheckResult, $result->getBlacklistCheckResult());
    }

    public function testComparisonResultFieldsArePreservedDuringConversion(): void
    {
        $dto = $this->createDetailedNamesComparisonResultDto();
        $context = new ProfileFilterContext(false, null, null);

        $this->entityManager->method('persist');
        $this->entityManager->method('flush');

        $result = $this->service->saveComparisonResults($dto, $context);

        $this->assertSame($dto->getLeftName(), $result->getLeftName());
        $this->assertSame($dto->getRightName(), $result->getRightName());
        $this->assertSame($dto->getModelAnswer(), $result->getModelAnswer());
        $this->assertSame($dto->getDecision(), $result->getDecision());
        $this->assertSame($dto->getFailureReason(), $result->getFailureReason());
        $this->assertSame($dto->getResponseCode(), $result->getResponseCode());
        $this->assertSame($dto->getRequestDuration(), $result->getRequestDuration());
        $this->assertSame($dto->getModel(), $result->getModel());

        $this->assertNull($result->getTransfer());
        $this->assertNull($result->getBlacklistCheckResult());
    }

    private function createNamesComparisonResultDto(): NamesComparisonResultDto
    {
        $dto = new NamesComparisonResultDto();
        $dto->setLeftName('John Doe');
        $dto->setRightName('Jane Doe');
        $dto->setDecision(NamesComparisonResultDto::DECISION_SAME_PERSON);
        $dto->setResponseCode(200);
        $dto->setModel('TestModel');
        $dto->setRequestDuration(123);

        return $dto;
    }

    private function createDetailedNamesComparisonResultDto(): NamesComparisonResultDto
    {
        $dto = new NamesComparisonResultDto();
        $dto->setLeftName('John Smith');
        $dto->setRightName('Jonathan Smith');
        $dto->setModelAnswer('The names appear to belong to the same person with high confidence.');
        $dto->setDecision(NamesComparisonResultDto::DECISION_SAME_PERSON);
        $dto->setFailureReason(null);
        $dto->setResponseCode(200);
        $dto->setRequestDuration(145);
        $dto->setModel('NameComparisonV2');

        return $dto;
    }
}
