<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Service\ProfileSearchProvider;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Mockery\LegacyMockInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Mockery;

class ProfileSearchProviderTest extends TestCase
{
    private $profileRepository;
    private $profileSearchProvider;

    public function setUp(): void
    {
        /** @var ProfileRepository|MockObject $profileRepository */
        $profileRepository = Mockery::mock(ProfileRepository::class);

        $this->profileRepository = $profileRepository;
        $this->profileSearchProvider = new ProfileSearchProvider($this->profileRepository);
    }

    public function testProvide(): void
    {
        $options = ['blacklist' => new Blacklist('key', 'name'), 'idFrom' => 1, 'idTo' => 2, 'limit' => 10];

        $this->profileRepository
            ->shouldReceive('findByFilter')
            ->once()
            ->andReturn([])
        ;

        $result = $this->profileSearchProvider->provide($options);

        $this->assertTrue(is_array($result));
        $this->assertEquals(1, $result[0]);

    }

    public function testGetCount(): void
    {
        $options = ['blacklist' => new Blacklist('key', 'name'),'idFrom' => 1, 'idTo' => 2, 'limit' => 10];

        $this->profileRepository
            ->shouldReceive('findCountByFilter')
            ->once()
            ->andReturn(1)
        ;

        $result = $this->profileSearchProvider->getCount($options);

        $this->assertEquals(1, $result);
    }
}
