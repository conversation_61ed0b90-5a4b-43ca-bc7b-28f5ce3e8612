<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Service;

use Elastica\Result;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistRepository;
use Evp\Bundle\BlacklistBundle\Service\ProfileMatcher;
use Evp\Bundle\BlacklistBundle\Service\SearchResultsProcessor;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use FOS\ElasticaBundle\HybridResult;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class SearchResultsProcessorTest extends TestCase
{
    /**
     * @var SearchResultsProcessor
     */
    private $searchResultsProcessor;

    /**
     * @var ClientCheckWhitelistRepository | MockObject
     */
    private $clientCheckWhitelistRepository;

    public function setUp(): void
    {
        /** @var ProfileMatcher | MockObject $profileMatcher */
        $profileMatcher = $this->createMock(ProfileMatcher::class);
        $this->clientCheckWhitelistRepository = $this->createMock(ClientCheckWhitelistRepository::class);
        $this->searchResultsProcessor = new SearchResultsProcessor(
            $profileMatcher,
            $this->clientCheckWhitelistRepository
        );
    }

    public function testSortSearchResultsByGroupPriority(): void
    {
        $sortedResults = $this->searchResultsProcessor
            ->sortSearchResultsByGroupPriority($this->getSearchResultsData())
        ;

        self::assertEquals('Haleema', $this->getProfileName($sortedResults[0]));
        self::assertEquals('Khan', $this->getProfileName($sortedResults[1]));
        self::assertEquals('Rasheeda', $this->getProfileName($sortedResults[2]));
        self::assertEquals('Samuel', $this->getProfileName($sortedResults[3]));
        self::assertEquals('Hamir', $this->getProfileName($sortedResults[4]));
        self::assertEquals('Nadeem', $this->getProfileName($sortedResults[5]));
        self::assertEquals('Julio', $this->getProfileName($sortedResults[6]));
        self::assertEquals('Tanveer', $this->getProfileName($sortedResults[7]));
    }

    /**
     * @return HybridResult[]
     */
    public function getSearchResultsData(): array
    {
        return [
            new HybridResult(
                new Result(['_score' => 106.8879]),
                (new Profile())
                    ->setName('Khan')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(5))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 106.8879]),
                (new Profile())
                    ->setName('Haleema')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(1)),
                        (new Category())->setGroup((new CategoryGroup())->setPriority(6))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 106.8879]),
                (new Profile())
                    ->setName('Rasheeda')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(8))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 106.8879]),
                (new Profile())
                    ->setName('Samuel')
                    ->setCategories([
                        new Category()
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 97.69655]),
                (new Profile())
                    ->setName('Julio')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(7))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 97.69655]),
                (new Profile())
                    ->setName('Nadeem')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(6))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 97.69655]),
                (new Profile())
                    ->setName('Hamir')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(3))
                    ])
            ),
            new HybridResult(
                new Result(['_score' => 26.412987]),
                (new Profile())
                    ->setName('Tanveer')
                    ->setCategories([
                        (new Category())->setGroup((new CategoryGroup())->setPriority(7))
                    ])
            ),
        ];
    }

    /** @dataProvider removeWhitelistedResultsDataProvider */
    public function testRemoveWhitelistedResults(
        bool $expectResultFiltered,
        ?ClientCheckWhitelist $clientCheckWhitelist,
        array $foundProfile
    ): void {
        $client = new ClientLegal();

        $this->clientCheckWhitelistRepository
            ->expects(self::any())
            ->method('findAllByClient')
            ->willReturn($clientCheckWhitelist ? [$clientCheckWhitelist] : [])
        ;

        $results = [
            (new BlacklistResult())->setFoundProfile(
                (new Profile())
                    ->setBlacklist($foundProfile['blacklist'])
                    ->setExternalId($foundProfile['externalId'])
                    ->setCategories([$foundProfile['category']])
            ),
        ];

        $filteredResults = $this->searchResultsProcessor->removeWhitelistedResults($results, $client);
        $this->assertEquals($expectResultFiltered, count($filteredResults) === 0);
    }

    public function removeWhitelistedResultsDataProvider(): array
    {
        $whitelistedExternalId = '123456';
        $otherExternalId = '234567';

        $whitelistedCategory = (new Category())->setGroup(new CategoryGroup());
        $otherCategory = (new Category())->setGroup(new CategoryGroup());

        $whitelistedBlacklist = new Blacklist('test_1', 'Test name 1');
        $otherBlacklist = new Blacklist('test_2', 'Test name 2');

        $clientCheckWhitelist = (new ClientCheckWhitelist())
            ->setClient(new ClientLegal())
            ->setBlacklist($whitelistedBlacklist)
            ->setProfileExternalId($whitelistedExternalId)
            ->setCategoryGroup($whitelistedCategory->getGroup());

        return [
            'No whitelist' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => null,
                'foundProfile' => [
                    'blacklist' => $whitelistedBlacklist,
                    'externalId' => $whitelistedExternalId,
                    'category' => $whitelistedCategory,
                ],
            ],
            'All fields are same' => [
                'expectResultFiltered' => true,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $whitelistedBlacklist,
                    'externalId' => $whitelistedExternalId,
                    'category' => $whitelistedCategory,
                ],
            ],
            'Different blacklist' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $otherBlacklist,
                    'externalId' => $whitelistedExternalId,
                    'category' => $whitelistedCategory,
                ],
            ],
            'Different external id' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $whitelistedBlacklist,
                    'externalId' => $otherExternalId,
                    'category' => $whitelistedCategory,
                ],
            ],
            'Different category' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $whitelistedBlacklist,
                    'externalId' => $whitelistedExternalId,
                    'category' => $otherCategory,
                ],
            ],
            'Different blacklist and external id' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $otherBlacklist,
                    'externalId' => $otherExternalId,
                    'category' => $whitelistedCategory,
                ],
            ],
            'Different blacklist and category' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $otherBlacklist,
                    'externalId' => $whitelistedExternalId,
                    'category' => $otherCategory,
                ],
            ],
            'Different external id and category' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $whitelistedBlacklist,
                    'externalId' => $otherExternalId,
                    'category' => $otherCategory,
                ],
            ],
            'All different' => [
                'expectResultFiltered' => false,
                'clientCheckWhitelist' => $clientCheckWhitelist,
                'foundProfile' => [
                    'blacklist' => $otherBlacklist,
                    'externalId' => $otherExternalId,
                    'category' => $otherCategory,
                ],
            ],
        ];
    }

    /**
     * @param HybridResult $result
     * @return string
     */
    private function getProfileName(HybridResult $result): string
    {
        /** @var Profile $profile */
        $profile = $result->getTransformed();
        return $profile->getName();
    }
}
