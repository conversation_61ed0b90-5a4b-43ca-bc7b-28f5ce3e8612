<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Listener;

use Evp\Bundle\BankingHistoryIntegrationBundle\Service\GlobalIdentificationManager;
use Evp\Bundle\BlacklistBundle\Listener\ClientListener;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\Event\ClientCodeChangedEvent;
use Evp\Bundle\ClientBundle\Entity\Event\ClientCountryCodeChangedEvent;
use Evp\Bundle\ClientBundle\Entity\Event\ClientEvent;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class ClientListenerTest extends TestCase
{
    private MockObject $remoteJobPublisher;
    private ClientListener $clientListener;

    protected function setUp(): void
    {
        $this->remoteJobPublisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->globalIdentificationManager = $this->createMock(GlobalIdentificationManager::class);
        $this->clientListener = new ClientListener(
            $this->remoteJobPublisher,
            $this->createMock(LoggerInterface::class),
            $this->globalIdentificationManager,
        );
    }

    public function testOnClientChangeNotPersistedClient(): void
    {
        $event = new ClientEvent(new ClientNatural());

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientChange($event);
    }

    public function testOnClientChangeIgnoreUnidentifiedClient(): void
    {
        $client = (new ClientNatural())->setId(1)->setLevel(IdentificationLevels::UNIDENTIFIED);
        $event = new ClientEvent($client);

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientChange($event);
    }

    public function testOnClientChange(): void
    {
        $event = new ClientEvent($this->getClientNatural());

        $this->globalIdentificationManager
            ->expects($this->once())
            ->method('getGlobalIdentificationId')
            ->with('master')
            ->willReturn('0-0-1')
        ;

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                [
                    'clients' => [1],
                    'global_identification_id' => '0-0-1',
                ]
            )
        ;

        $this->clientListener->onClientChange($event);
    }

    public function testOnClientCountryCodeChangedNotPersistedClient(): void
    {
        $event = new ClientCountryCodeChangedEvent(new ClientNatural(), 'lt');

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientCountryCodeChanged($event);
    }

    public function testOnClientCountryCodeChangedIgnoreUnidentifiedClient(): void
    {
        $client = (new ClientNatural())->setId(1)->setLevel(IdentificationLevels::UNIDENTIFIED);
        $event = new ClientCountryCodeChangedEvent($client, 'lt');

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientCountryCodeChanged($event);
    }

    public function testOnClientCountryCodeChanged(): void
    {
        $event = new ClientCountryCodeChangedEvent($this->getClientNatural(), 'lt');

        $this->globalIdentificationManager
            ->expects($this->once())
            ->method('getGlobalIdentificationId')
            ->with('master')
            ->willReturn('0-0-1')
        ;

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                [
                    'clients' => [1],
                    'global_identification_id' => '0-0-1',
                ]
            )
        ;

        $this->clientListener->onClientCountryCodeChanged($event);
    }

    public function testOnClientCodeChanged(): void
    {
        $event = new ClientCodeChangedEvent($this->getClientNatural());

        $this->globalIdentificationManager
            ->expects($this->once())
            ->method('getGlobalIdentificationId')
            ->with('master')
            ->willReturn('0-0-1')
        ;

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                [
                    'clients' => [1],
                    'global_identification_id' => '0-0-1',
                ]
            )
        ;

        $this->clientListener->onClientCodeChanged($event);
    }

    public function testOnClientCodeChangedNotPersistedClient(): void
    {
        $event = new ClientCodeChangedEvent(new ClientNatural());

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientCodeChanged($event);
    }

    public function testOnClientCodeChangedWithInvalidClient()
    {
        $client = (new ClientNatural())
            ->setId(1)
            ->setLevel(IdentificationLevels::UNIDENTIFIED)
        ;

        $event = new ClientCodeChangedEvent($client);

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onClientCodeChanged($event);
    }

    private function getClientNatural(): ClientNatural
    {
        return (new ClientNatural())
            ->setId(1)
            ->setCode('tx123')
            ->setFirstName('John')
            ->setLevel(IdentificationLevels::IDENTIFIED);
    }
}
