<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Listener;

use Evp\Bundle\BankingHistoryIntegrationBundle\Service\GlobalIdentificationManager;
use Evp\Bundle\BlacklistBundle\Listener\RemoteEventListener;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\RabbitMqExtensionBundle\Entity\RemoteEvent;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisher;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class RemoteEventListenerTest extends TestCase
{
    private MockObject $remoteJobPublisher;
    private MockObject $clientRepository;
    private MockObject $globalIdentificationManager;
    private RemoteEventListener $clientListener;

    protected function setUp(): void
    {
        $this->remoteJobPublisher = $this->createMock(RemoteJobPublisher::class);
        $this->clientRepository = $this->createMock(ClientRepository::class);
        $this->globalIdentificationManager = $this->createMock(GlobalIdentificationManager::class);
        $logger = $this->createMock(LoggerInterface::class);

        $this->clientListener = new RemoteEventListener(
            $this->remoteJobPublisher,
            $this->clientRepository,
            $logger,
            $this->globalIdentificationManager,
        );
    }

    public function testOnIdentityDocumentChangedWrongEventDataType(): void
    {
        $event = new RemoteEvent('user', 'user_changed', new \stdClass());

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onIdentityDocumentChanged($event);
    }

    /**
     * @@dataProvider getEventWithMissingData
     */
    public function testOnIdentityDocumentChangedMissingData(array $data): void
    {
        $event = new RemoteEvent('user', 'user_changed', $data);

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onIdentityDocumentChanged($event);
    }

    public function getEventWithMissingData(): array
    {
        return [
            [[]],
            [['user_id' => 1]],
            [['date_of_birth' => '1900-12-12']],
        ];
    }

    public function testOnIdentityDocumentChangedClientNotFound(): void
    {
        $data = ['user_id' => 1, 'date_of_birth' => '1900-12-12'];
        $event = new RemoteEvent('user', 'user_changed', $data);

        $this->clientRepository
            ->expects($this->once())
            ->method('findOneByCovenanteeId')
            ->with(1)
            ->willReturn(null);

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onIdentityDocumentChanged($event);
    }

    public function testOnIdentityDocumentChangedUnidentifiedClient(): void
    {
        $data = ['user_id' => 1, 'date_of_birth' => '1900-12-12'];
        $event = new RemoteEvent('user', 'user_changed', $data);
        $client = (new ClientNatural())->setId(1)->setLevel(IdentificationLevels::UNIDENTIFIED);

        $this->clientRepository
            ->expects($this->once())
            ->method('findOneByCovenanteeId')
            ->with(1)
            ->willReturn($client);

        $this->remoteJobPublisher
            ->expects($this->never())
            ->method('publishJob');

        $this->clientListener->onIdentityDocumentChanged($event);
    }

    public function testOnIdentityDocumentChangedWithDateOfBirth(): void
    {
        $data = ['user_id' => 1, 'date_of_birth' => '1900-12-12'];
        $event = new RemoteEvent('user', 'user_changed', $data);
        $client = (new ClientNatural())->setId(1)->setLevel(IdentificationLevels::IDENTIFIED);

        $this->clientRepository
            ->expects($this->once())
            ->method('findOneByCovenanteeId')
            ->with(1)
            ->willReturn($client)
        ;

        $this->globalIdentificationManager
            ->expects($this->once())
            ->method('getGlobalIdentificationId')
            ->with('master')
            ->willReturn('0-0-1')
        ;

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                [
                    'clients' => [1],
                    'global_identification_id' => '0-0-1',
                ]
            )
        ;

        $this->clientListener->onIdentityDocumentChanged($event);
    }

    public function testOnIdentityDocumentChangedWithoutDateOfBirth(): void
    {
        $data = ['user_id' => 1];
        $event = new RemoteEvent('user', 'user_changed', $data);
        $client = (new ClientNatural())->setId(1)->setLevel(IdentificationLevels::IDENTIFIED);

        $this->clientRepository
            ->expects($this->once())
            ->method('findOneByCovenanteeId')
            ->with(1)
            ->willReturn($client)
        ;

        $this->globalIdentificationManager
            ->expects($this->once())
            ->method('getGlobalIdentificationId')
            ->with('master')
            ->willReturn('0-0-1')
        ;

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                [
                    'clients' => [1],
                    'global_identification_id' => '0-0-1',
                ]
            )
        ;

        $this->clientListener->onIdentityDocumentChanged($event);
    }
}
