<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Listener;

use Evp\Bundle\BlacklistBundle\Listener\WhitelistRemovalListener;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\Event\ClientCountryCodeChangedEvent;
use Evp\Bundle\ClientBundle\Entity\Event\ClientEvent;
use Evp\Bundle\ClientBundle\Entity\Event\ClientLevelChangedEvent;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class WhitelistRemovalListenerTest extends TestCase
{
    private MockObject $clientCheckWhitelistManager;
    private WhitelistRemovalListener $listener;

    protected function setUp(): void
    {
        $this->clientCheckWhitelistManager = $this->createMock(ClientCheckWhitelistManager::class);
        $this->listener = new WhitelistRemovalListener(
            $this->clientCheckWhitelistManager,
            $this->createMock(LoggerInterface::class)
        );
    }

    public function testIgnoresNotPersistedEntity(): void
    {
        $client = $this->createMock(ClientNatural::class);
        $event = new ClientEvent($client);

        $this->clientCheckWhitelistManager
            ->expects($this->never())
            ->method('removeAll');

        $this->listener->onClientUpdate($event);
    }

    public function testOnClientUpdate(): void
    {
        $client = $this->createMock(ClientNatural::class);
        $client->expects(self::any())
            ->method('getId')
            ->willReturn(1);

        $event = new ClientEvent($client);

        $this->clientCheckWhitelistManager
            ->expects($this->once())
            ->method('removeAll');

        $this->listener->onClientUpdate($event);
    }

    public function testOnClientLevelChanged(): void
    {
        $client = $this->createMock(ClientNatural::class);
        $client->expects(self::any())
            ->method('getId')
            ->willReturn(1);

        $event = new ClientLevelChangedEvent($client, IdentificationLevels::FULLY_IDENTIFIED);

        $this->clientCheckWhitelistManager
            ->expects($this->once())
            ->method('removeAll');

        $this->listener->onClientLevelChanged($event);
    }

    public function testIgnoreException(): void
    {
        $client = $this->createMock(ClientNatural::class);
        $client->expects(self::any())
            ->method('getId')
            ->willReturn(1);

        $event = new ClientEvent($client);

        $this->clientCheckWhitelistManager
            ->expects($this->once())
            ->method('removeAll')
            ->willThrowException(new \Exception('something bad happened!'));

        $this->listener->onClientUpdate($event);
    }

    public function testOnClientCountryCodeChanged(): void
    {
        $client = $this->createMock(ClientNatural::class);
        $client->expects(self::any())
            ->method('getId')
            ->willReturn(1);

        $event = new ClientCountryCodeChangedEvent($client, 'UK');

        $this->clientCheckWhitelistManager
            ->expects($this->once())
            ->method('removeAll');

        $this->listener->onClientCountryCodeChanged($event);
    }
}
