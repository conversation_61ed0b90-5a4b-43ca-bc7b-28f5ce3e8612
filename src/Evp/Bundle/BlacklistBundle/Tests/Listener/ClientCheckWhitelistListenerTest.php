<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Listener;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningWhitelistProfilesEvent;
use Evp\Bundle\BlacklistBundle\Listener\ClientCheckWhitelistListener;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemReviewLogRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistRepository;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Throwable;

class ClientCheckWhitelistListenerTest extends PersistableWebTestCase
{
    private EntityManager $entityManager;
    private ClientCheckWhitelistRepository $clientCheckWhitelistRepository;
    private ClientBlacklistCheckResultItemReviewLogRepository $blacklistCheckResultItemReviewLogRepository;
    private FixturesHelper $fixturesHelper;
    private ClientCheckWhitelistListener $listener;
    private ClientBlacklistCheckResultItemRepository $blacklistCheckResultItemRepository;

    /**
     * @throws Throwable
     */
    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->clientCheckWhitelistRepository = $this->getContainer()->get('evp_blacklist.repository.client_check_whitelist');
        $this->blacklistCheckResultItemReviewLogRepository = $this->getContainer()->get('evp_blacklist.repository.client_blacklist_check_result_item_review_log_repository');
        $this->blacklistCheckResultItemRepository = $this->getContainer()->get('evp_blacklist.repository.client_blacklist_check_result_item');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->listener = $this->getContainer()->get('evp_blacklist.client_check_whitelist_listener');
    }

    /**
     * @throws Throwable
     */
    public function testOnReviewActionWhitelisted(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $blacklistCheckResult = $this->fixturesHelper->createClientBlacklistCheckResult();

        $category1 = $this->fixturesHelper->createCategory();
        $category2 = $this->fixturesHelper->createCategory(2);

        $profile1 = $this->fixturesHelper->createPersonProfile(
            $this->fixturesHelper->createBlacklist('name', 'key'),
            'firstName',
            'lastName',
            'lt',
            '123456789',
            Profile::TYPE_PERSON,
            '1',
            $category1
        );

        $profile2 = $this->fixturesHelper->createPersonProfile(
            $this->fixturesHelper->createBlacklist('name2', 'key2'),
            'firstName',
            'lastName',
            'lt',
            '987654321',
            Profile::TYPE_PERSON,
            '2',
            $category2
        );

        $item1 = $this->fixturesHelper->createClientBlacklistCheckResultItem($blacklistCheckResult, $profile1);
        $item2 = $this->fixturesHelper->createClientBlacklistCheckResultItem($blacklistCheckResult, $profile2);

        $sanctionsScreening = $this->fixturesHelper->createClientSanctionsScreening(
            $client,
            $blacklistCheckResult,
            ClientSanctionsScreening::STATUS_IN_REVIEW,
            '1',
            ClientSanctionsScreening::REVIEW_ACTION_ASSIGNED
        );
        $blacklistCheckResult->setItems(new ArrayCollection([$item1, $item2]));
        $this->entityManager->flush();

        $event = new ClientSanctionsScreeningWhitelistProfilesEvent(
            $sanctionsScreening,
            [
                $profile1->getId(),
                $profile2->getId(),
            ]
        );

        $this->entityManager->getConnection()->beginTransaction();
        $this->listener->onReviewActionWhitelisted($event);
        $this->entityManager->getConnection()->commit();
        $this->entityManager->flush();

        $whitelists = $this->clientCheckWhitelistRepository->findAllByClient($client);
        $this->assertCount(2, $whitelists);

        $this->assertSame($category1->getGroup(), $whitelists[0]->getCategoryGroup());
        $this->assertSame($category2->getGroup(), $whitelists[1]->getCategoryGroup());

        $this->assertSame($profile1->getBlacklist(), $whitelists[0]->getBlacklist());
        $this->assertSame($profile1->getExternalId(), $whitelists[0]->getProfileExternalId());

        $this->assertSame($profile2->getBlacklist(), $whitelists[1]->getBlacklist());
        $this->assertSame($profile2->getExternalId(), $whitelists[1]->getProfileExternalId());

        $reviewLogs = $this->blacklistCheckResultItemReviewLogRepository->findAll();
        $this->assertCount(2, $reviewLogs);
    }
}
