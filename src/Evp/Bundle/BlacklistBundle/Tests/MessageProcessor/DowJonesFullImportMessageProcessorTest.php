<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\MessageProcessor;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Event\BlacklistFullImportCheckResultEvent;
use Evp\Bundle\BlacklistBundle\Events;
use Evp\Bundle\BlacklistBundle\MessageProcessor\DowJonesFullImportMessageProcessor;
use Evp\Bundle\BlacklistBundle\Repository\ProfileAssociationRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRelationshipRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Evp\Bundle\BlacklistBundle\Service\SanctionsVersionManager;
use Evp\Bundle\BlacklistBundle\Service\StaticBlacklistImporter;
use Evp\Bundle\BlacklistBundle\Service\Transformer\ProviderCategoryTransformer;
use Evp\Bundle\BlacklistBundle\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class DowJonesFullImportMessageProcessorTest extends PersistableWebTestCase
{
    private EntityManager $entityManager;
    private ProfileRepository $profileRepository;
    private ProfileRelationshipRepository $profileRelationshipRepository;
    private ProfileAssociationRepository $profileAssociationRepository;
    private ConfigurationManager $configurationManager;
    private BlacklistCategoryImporter $categoryImporter;
    private ProviderCategoryTransformer $providerCategoryTransformer;
    private DowJonesFullImportMessageProcessor $dowJonesFullImportMessageProcessor;
    private FixturesHelper $fixturesHelper;

    /** @var SanctionsVersionManager|MockObject */
    private SanctionsVersionManager $sanctionsVersionManager;

    /** @var StaticBlacklistImporter|MockObject */
    private StaticBlacklistImporter $staticBlacklistImporter;

    /** @var EventDispatcherInterface|MockObject */
    private EventDispatcherInterface $eventDispatcher;

    /** @var ClientCheckWhitelistManager|MockObject */
    private ClientCheckWhitelistManager $clientCheckWhitelistManager;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $feedReader = $this->getMockBuilder(DowJonesFeedReader::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['resolveFeedFileVersion', 'getFeedFileByVersion'])
            ->getMock()
        ;
        $feedReader->expects($this->any())
            ->method('resolveFeedFileVersion')
            ->willReturn(1)
        ;
        $feedReader->expects($this->any())
            ->method('getFeedFileByVersion')
            ->willReturn('/' . __DIR__ . '/Resources/PFA2_201712022200_D.zip')
        ;
        $this->getContainer()->set('evp_blacklist.dowjones_full_feed_reader', $feedReader);

        $this->sanctionsVersionManager = $this->createMock(SanctionsVersionManager::class);
        $this->getContainer()->set('evp_blacklist.service.sanctions_version_manager_v1', $this->sanctionsVersionManager);

        $this->staticBlacklistImporter = $this->createMock(StaticBlacklistImporter::class);
        $this->getContainer()->set('evp_blacklist.static_blacklist_importer_secondary', $this->staticBlacklistImporter);

        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->getContainer()->set('event_dispatcher', $this->eventDispatcher);

        $this->clientCheckWhitelistManager = $this->createMock(ClientCheckWhitelistManager::class);

        /** @noinspection MissingService */
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->profileRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile');
        $this->profileRelationshipRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile_relationship');
        $this->profileAssociationRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile_association');
        $this->configurationManager = $this->getContainer()->get('evp_blacklist.configuration_manager');
        $this->categoryImporter = $this->getContainer()->get('evp_blacklist.category_importer');
        $this->providerCategoryTransformer = $this->getContainer()->get('evp_blacklist.transformer.provider_category');
        $this->dowJonesFullImportMessageProcessor = $this->getContainer()->get('evp_blacklist.message_processor.dow_jones_full_import');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->dowJonesFullImportMessageProcessor = new DowJonesFullImportMessageProcessor(
            $this->entityManager,
            $this->getContainer()->get('evp_blacklist.repository.blacklist'),
            $this->profileRepository,
            $this->clientCheckWhitelistManager,
            $this->configurationManager,
            $this->getContainer()->get('evp_blacklist.transformer.provider_profile'),
            $this->getContainer()->get('evp_blacklist.transformer.provider_association'),
            $this->getContainer()->get('evp_blacklist.profile_manager'),
            $this->eventDispatcher,
            $this->getContainer()->get('paysera_lock.lock_factory'),
            $this->getContainer()->get('logger'),
        );
    }

    public function testImportProfileOperation(): void
    {
        $expectedCount = 19;

        $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1);
        $this->entityManager->flush();

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);
        $this->importCategories($version);

        $this->clientCheckWhitelistManager
            ->expects($this->exactly($expectedCount))
            ->method('syncWithProfile')
        ;

        foreach ($this->configurationManager->getFeedDataProvider()->getProfiles($version) as $providerProfile) {
            $data = [
                DowJonesFullImportMessageProcessor::KEY_OPERATION => DowJonesFullImportMessageProcessor::OPERATION_IMPORT_PROFILE,
                DowJonesFullImportMessageProcessor::KEY_PROVIDER_PROFILE => serialize($providerProfile),
                DowJonesFullImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->dowJonesFullImportMessageProcessor->process(json_encode(['data' => $data]));
        }

        $this->assertEquals($expectedCount, $this->profileRepository->count([]));
    }

    public function testImportAssociationOperation(): void
    {
        $expectedCount = 28;
        $expectedRelationsCount = 37;

        $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1);
        $this->entityManager->flush();

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);
        $this->importCategories($version);
        $this->importRelationships($version);

        foreach ($this->configurationManager->getFeedDataProvider()->getProfiles($version) as $providerProfile) {
            $data = [
                DowJonesFullImportMessageProcessor::KEY_OPERATION => DowJonesFullImportMessageProcessor::OPERATION_IMPORT_PROFILE,
                DowJonesFullImportMessageProcessor::KEY_PROVIDER_PROFILE => serialize($providerProfile),
                DowJonesFullImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->dowJonesFullImportMessageProcessor->process(json_encode(['data' => $data]));
        }

        foreach ($this->configurationManager->getFeedDataProvider()->getAssociations($version) as $associationsData) {
            $data = [
                DowJonesFullImportMessageProcessor::KEY_OPERATION => DowJonesFullImportMessageProcessor::OPERATION_IMPORT_ASSOCIATION,
                DowJonesFullImportMessageProcessor::KEY_ASSOCIATIONS_DATA => serialize($associationsData),
                DowJonesFullImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ];

            $this->dowJonesFullImportMessageProcessor->process(json_encode(['data' => $data]));
        }

        $relationsCount = (int) $this->profileRepository->createQueryBuilder('p')
            ->select('COUNT(pa)')
            ->innerJoin('p.associations', 'pa')
            ->getQuery()
            ->getSingleScalarResult()
        ;

        $this->assertEquals($expectedCount, $this->profileAssociationRepository->count([]));
        $this->assertEquals($expectedRelationsCount, $relationsCount);
    }

    public function testImportStaticAndSwitchIndexesOperation(): void
    {
        $dowJonesBlacklist = $this->fixturesHelper->createBlacklist(Blacklist::KEY_DOW_JONES_V1)
            ->setExternal(true)
        ;

        $blacklistOne = $this->fixturesHelper->createBlacklist('one', 'one');
        $blacklistTwo = $this->fixturesHelper->createBlacklist('two', 'two');
        $blacklistThree = $this->fixturesHelper->createBlacklist('three', 'three');

        $this->entityManager->flush();

        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);

        $this->staticBlacklistImporter
            ->expects($this->exactly(3))
            ->method('process')
            ->withConsecutive([$blacklistOne], [$blacklistTwo], [$blacklistThree])
        ;

        $this->sanctionsVersionManager->expects($this->once())->method('switchActiveIndex');
        $this->sanctionsVersionManager->expects($this->once())->method('removeOldIndexes');

        $data = [
            DowJonesFullImportMessageProcessor::KEY_OPERATION => DowJonesFullImportMessageProcessor::OPERATION_IMPORT_STATIC_AND_SWITCH_INDEXES,
            DowJonesFullImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
        ];

        $this->dowJonesFullImportMessageProcessor->process(json_encode(['data' => $data]));

        $this->assertEquals($version, $dowJonesBlacklist->getVersion());
    }

    public function testDispatchFinalEventsOperation(): void
    {
        $version = $this->configurationManager->getFeedDataProvider()->getVersionToImport(0, false);

        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with(
                Events::FULL_PROFILE_IMPORT,
                $this->callback(
                    fn (BlacklistFullImportCheckResultEvent $event) => $event->getVersion() === $version
                        && $event->getProfileQuantity() === 0
                )
            )
        ;

        $data = [
            DowJonesFullImportMessageProcessor::KEY_OPERATION => DowJonesFullImportMessageProcessor::OPERATION_DISPATCH_FINAL_EVENTS,
            DowJonesFullImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
        ];

        $this->dowJonesFullImportMessageProcessor->process(json_encode(['data' => $data]));
    }

    private function importCategories(int $version): void
    {
        $categories = [];

        foreach ($this->configurationManager->getFeedDataProvider()->getCategories($version) as $providerCategory) {
            $categories[] = $this->providerCategoryTransformer->transform($providerCategory);
        }

        $this->categoryImporter->import($categories);
    }

    private function importRelationships(int $version): void
    {
        foreach ($this->configurationManager->getFeedDataProvider()->getRelationships($version) as $relationship) {
            if ($this->profileRelationshipRepository->findOneByCode($relationship->getCode()) === null) {
                $this->entityManager->persist($relationship);
            }
        }

        $this->entityManager->flush();
    }
}
