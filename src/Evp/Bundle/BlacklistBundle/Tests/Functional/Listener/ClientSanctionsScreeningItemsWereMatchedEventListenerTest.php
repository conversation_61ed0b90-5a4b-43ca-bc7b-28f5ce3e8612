<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Listener;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\ItemReviewAction;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningItemsWereMatchedEvent;
use Evp\Bundle\BlacklistBundle\Listener\ClientSanctionsScreeningItemsWereMatchedEventListener;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemReviewLogRepository;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class ClientSanctionsScreeningItemsWereMatchedEventListenerTest extends PersistableWebTestCase
{
    private EntityManager $entityManager;
    private ClientBlacklistCheckResultItemReviewLogRepository $blacklistCheckResultItemReviewLogRepository;
    private FixturesHelper $fixturesHelper;
    private ClientSanctionsScreeningItemsWereMatchedEventListener $listener;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->blacklistCheckResultItemReviewLogRepository = $this->getContainer()->get('evp_blacklist.repository.client_blacklist_check_result_item_review_log_repository');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->listener = $this->getContainer()->get('evp_blacklist.client_sanctions_screening_items_were_matched_event_listener');
    }

    public function testOnClientSanctionsScreeningItemsWereMatchedEvent(): void
    {
        $blacklistCheckResult = $this->fixturesHelper->createClientBlacklistCheckResult();

        $category1 = $this->fixturesHelper->createCategory();
        $category2 = $this->fixturesHelper->createCategory(2);

        $profile1 = $this->fixturesHelper->createPersonProfile(
            $this->fixturesHelper->createBlacklist('name', 'key'),
            'firstName',
            'lastName',
            'lt',
            '123456789',
            Profile::TYPE_PERSON,
            '1',
            $category1
        );

        $profile2 = $this->fixturesHelper->createPersonProfile(
            $this->fixturesHelper->createBlacklist('name2', 'key2'),
            'firstName',
            'lastName',
            'lt',
            '987654321',
            Profile::TYPE_PERSON,
            '2',
            $category2
        );

        $item1 = $this->fixturesHelper->createClientBlacklistCheckResultItem($blacklistCheckResult, $profile1);
        $item2 = $this->fixturesHelper->createClientBlacklistCheckResultItem($blacklistCheckResult, $profile2);

        $blacklistCheckResult->setItems(new ArrayCollection([$item1, $item2]));
        $this->entityManager->flush();

        $event = new ClientSanctionsScreeningItemsWereMatchedEvent([$item1, $item2]);

        $this->listener->onClientSanctionsScreeningItemsWereMatchedEvent($event);

        $this->entityManager->flush();

        $reviewLogs = $this->blacklistCheckResultItemReviewLogRepository->findAll();

        $this->assertCount(2, $reviewLogs);

        $this->assertSame($item1, $reviewLogs[0]->getClientBlacklistCheckResultItem());
        $this->assertSame($item2, $reviewLogs[1]->getClientBlacklistCheckResultItem());

        $this->assertSame(ItemReviewAction::MATCH_ACTION, $reviewLogs[0]->getAction());
        $this->assertSame(ItemReviewAction::MATCH_ACTION, $reviewLogs[1]->getAction());
    }
}
