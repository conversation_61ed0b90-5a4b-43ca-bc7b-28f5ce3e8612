<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Exception;

class ClientSanctionsScreeningAdminControllerTest extends PersistableWebTestCase
{
    private EntityManagerInterface $entityManager;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/list');

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue(
            strpos($this->client->getResponse()->headers->get('Location'), '/login') !== false
        );
    }

    public function testDownloadResultInsufficientPrivileges(): void
    {
        $this->logIn(['SOME_USER']);
        $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/list');

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
    }

    public function testShowList(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();

        $blacklist = $fixturesHelper->createBlacklist();
        $profile = $fixturesHelper->createBlacklistProfile();
        $result = $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $fixturesHelper->createSanctionScreening($client, $result)->setReviewerId('123');
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_LIST',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_VIEW',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/list');

        $responseContent = $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertStringContainsString('John Doe', $responseContent);
        $this->assertStringContainsString('natural', $responseContent);
        $this->assertStringContainsString('1122', $responseContent);
        $this->assertStringContainsString('November 23, 2023 12:34', $responseContent);
        $this->assertStringContainsString('lt', $responseContent);
        $this->assertStringContainsString('1: 100', $responseContent);
        $this->assertStringContainsString('1: DJExternalId123', $responseContent);
        $this->assertStringContainsString('1: John Doe', $responseContent);
        $this->assertStringContainsString('1: lt', $responseContent);
        $this->assertStringContainsString('1: Sanctions Lists (SAN)', $responseContent);
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(0, $crawler->filter('.btn-default.edit_link'));
    }

    public function testShow(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();
        $blacklist = $fixturesHelper->createBlacklist();
        $profile = $fixturesHelper->createBlacklistProfile();
        $companyProfile = $fixturesHelper->createBlacklistCompanyProfile($profile->getBlacklist());

        $result = $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $result->addItem($fixturesHelper->createCheckResultItem($companyProfile, $result));
        $fixturesHelper->createSanctionScreening($client, $result)->setReviewerId('123');

        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_LIST',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_VIEW',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/1/show');
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // 1st matched profile
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("1")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Dow Jones V1")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("DJExternalId123")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("person")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("John")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Doe")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Code123ABC")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("lt")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("2023-11-23")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("+48500100200")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Additional information")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Johnny")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Doee")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Johny")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("Dee")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("2023-12")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(1):contains("1900")')->count());

        // 2nd matched profile
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("1")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("Dow Jones V1")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("DJCompanyID123")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("company")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("Acme Laugh-Powered Products, Inc.")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("ACMEcode123")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("lt")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("Additional information about Acme company")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("Acme Products, Inc.")')->count());
        $this->assertEquals(1, $crawler->filter('.profile:nth-child(2):contains("Acme Laugh-Powered Products")')->count());
    }

    /**
     * @param string $url
     * @param bool $loadList
     * @param bool $setReferer
     *
     * @throws Exception
     *
     * @dataProvider dataProvider
     */
    public function testRefererUrl(string $url, bool $loadList, bool $setReferer): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();
        $blacklist = $fixturesHelper->createBlacklist();
        $profile = $fixturesHelper->createBlacklistProfile();
        $companyProfile = $fixturesHelper->createBlacklistCompanyProfile($profile->getBlacklist());

        $result = $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $result->addItem($fixturesHelper->createCheckResultItem($companyProfile, $result));
        $fixturesHelper->createSanctionScreening($client, $result)->setReviewerId('123');

        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_LIST',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_SANCTION_SCREENING_VIEW',
        ]);

        if ($loadList) {
            $this->client->request('GET', $url);
        }

        if ($setReferer) {
            $crawler = $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/1/show', [], [], ['HTTP_REFERER' => $url]);
        } else {
            $crawler = $this->client->request('GET', '/admin/evp/blacklist/clientsanctionsscreening/1/show');
        }

        $this->assertStringEndsWith('/admin/evp/blacklist/clientsanctionsscreening/1/show', $this->client->getRequest()->getUri());

        $link = $crawler->selectLink('Items to review')->attr('href');
        $this->assertEquals($url, $link);

        $link = $crawler->selectLink('Return to list')->attr('href');
        $this->assertEquals($url, $link);
    }

    /**
     * @return array[]
     */
    public function dataProvider(): array
    {
        return [
            'Link has changed' => [
                '/admin/evp/blacklist/clientsanctionsscreening/list?filter%5B_page%5D=1&filter%5B_per_page%5D=32&filter%5B_sort_by%5D=id&filter%5B_sort_order%5D=DESC&filter%5BclientBlacklistCheckResult__blacklist%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__blacklist%5D%5Bvalue%5D=1&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Bvalue%5D%5Bend%5D=&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Bvalue%5D%5Bstart%5D=&filter%5BclientBlacklistCheckResult__items__profile__country%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__items__profile__country%5D%5Bvalue%5D=&filter%5BclientBlacklistCheckResult__items__profile__externalId%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__items__profile__externalId%5D%5Bvalue%5D=&filter%5BclientType%5D%5Btype%5D=&filter%5BclientType%5D%5Bvalue%5D=&filter%5Bclient__covenanteeId%5D%5Btype%5D=&filter%5Bclient__covenanteeId%5D%5Bvalue%5D=&filter%5BcountryCode%5D%5Btype%5D=&filter%5BcountryCode%5D%5Bvalue%5D=&filter%5BcurrentClientLicensedPartner%5D%5Btype%5D=&filter%5BcurrentClientLicensedPartner%5D%5Bvalue%5D=&filter%5Bstatus%5D%5Btype%5D=2&filter%5Bstatus%5D%5Bvalue%5D=completed',
                true,
                false,
            ],
            'Link has changed due to referer' => [
                '/admin/evp/blacklist/clientsanctionsscreening/list?filter%5B_page%5D=1&filter%5B_per_page%5D=32&filter%5B_sort_by%5D=id&filter%5B_sort_order%5D=DESC&filter%5BclientBlacklistCheckResult__blacklist%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__blacklist%5D%5Bvalue%5D=1&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Bvalue%5D%5Bend%5D=&filter%5BclientBlacklistCheckResult__checkedAt%5D%5Bvalue%5D%5Bstart%5D=&filter%5BclientBlacklistCheckResult__items__profile__country%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__items__profile__country%5D%5Bvalue%5D=&filter%5BclientBlacklistCheckResult__items__profile__externalId%5D%5Btype%5D=&filter%5BclientBlacklistCheckResult__items__profile__externalId%5D%5Bvalue%5D=&filter%5BclientType%5D%5Btype%5D=&filter%5BclientType%5D%5Bvalue%5D=&filter%5Bclient__covenanteeId%5D%5Btype%5D=&filter%5Bclient__covenanteeId%5D%5Bvalue%5D=&filter%5BcountryCode%5D%5Btype%5D=&filter%5BcountryCode%5D%5Bvalue%5D=&filter%5BcurrentClientLicensedPartner%5D%5Btype%5D=&filter%5BcurrentClientLicensedPartner%5D%5Bvalue%5D=&filter%5Bstatus%5D%5Btype%5D=2&filter%5Bstatus%5D%5Bvalue%5D=completed',
                false,
                true,
            ],
            'Link has not changed' => [
                '/admin/evp/blacklist/clientsanctionsscreening/list',
                false,
                false,
            ],
        ];
    }

    private function logIn(array $roles): void
    {
        $session = $this->client->getContainer()->get('session');
        $token = new UsernamePasswordToken(
            'user',
            null,
            'main',
            $roles
        );
        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }
}
