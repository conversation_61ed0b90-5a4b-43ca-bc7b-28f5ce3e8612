<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Routing\Router;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class ClientBlacklistCheckResultAdminControllerTest extends PersistableWebTestCase
{
    private Router $router;
    private EntityManagerInterface $entityManager;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->router = $this->client->getContainer()->get('router');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request('GET', $this->router->generate('admin_evp_blacklist_clientblacklistcheckresult_list'));

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue(
            strpos($this->client->getResponse()->headers->get('Location'), '/login') !== false
        );
    }

    public function testDownloadResultInsufficientPrivileges(): void
    {
        $this->logIn(['SOME_USER']);
        $this->client->request('GET', $this->router->generate('admin_evp_blacklist_clientblacklistcheckresult_list'));

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
    }

    public function testGetList(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();
        $blacklist = $fixturesHelper->createBlacklist();
        $profile = $fixturesHelper->createBlacklistProfile();
        $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_LIST',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_VIEW',
        ]);
        $crawler = $this->client->request('GET', $this->router->generate('admin_evp_blacklist_clientblacklistcheckresult_list'));

        $responseContent = $this->client->getResponse()->getContent();
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertStringContainsString('John Doe', $responseContent);
        $this->assertStringContainsString('yes', $responseContent);
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(0, $crawler->filter('.btn-default.edit_link'));
    }

    public function testGetShow(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();
        $blacklist = $fixturesHelper->createBlacklist();
        $profile = $fixturesHelper->createBlacklistProfile();
        $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_VIEW',
        ]);
        $this->client->request('GET', $this->router->generate('admin_evp_blacklist_clientblacklistcheckresult_show', ['id' => '1']));

        $responseContent = $this->client->getResponse()->getContent();
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString('Categories: Sanctions Lists, Financial Crime, AI Confidence: 100%', $responseContent);
    }

    public function testGetEdit(): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_EDIT',
        ]);

        $this->expectException(RouteNotFoundException::class);
        $this->router->generate('admin_evp_blacklist_clientblacklistcheckresult_edit', ['id' => '1']);
    }

    private function logIn(array $roles): void
    {
        $session = $this->client->getContainer()->get('session');
        $token = new UsernamePasswordToken(
            'user',
            null,
            'main',
            $roles
        );
        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    public function testExportResults(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $client = $fixturesHelper->createClient();
        $blacklist = $fixturesHelper->createBlacklist()->setName('Dow Jones V1');
        $profile = $fixturesHelper->createBlacklistProfile();
        $result = $fixturesHelper->createBlacklistCheckResult($client, $blacklist, $profile);
        $fixturesHelper->createSanctionScreening($client, $result);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_LIST',
            'ROLE_EVP_BLACKLIST_ADMIN_CLIENT_CHECK_LOGS_VIEW',
        ]);


        $crawler = $this->client->request('GET', '/admin/evp/blacklist/clientblacklistcheckresult/list');
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        $buttonCrawlerNode = $crawler->selectButton('Export results');
        $form = $buttonCrawlerNode->form();

        $form->setValues(['export_black_list_results[clientId]' => $client->getCovenanteeId()]);

        ob_start();
        $this->client->submit($form);
        $actualCsv = ob_get_clean();

        $this->assertStringMatchesFormat(
            file_get_contents(__DIR__.'/Resources/files/client_blacklist_check_results.csv'),
            $actualCsv
        );
    }
}
