<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Controller;

use Evp\Bundle\BlacklistBundle\Entity\AlternativeName;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Exception;
use InvalidArgumentException;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use ReflectionClass;
use ReflectionException;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class ProfileAdminControllerTest extends PersistableWebTestCase
{
    private RouterInterface $router;
    private ProfileRepository $repository;
    private BlacklistRepository $blacklistRepository;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $fixturesPath = sprintf('%s/DataFixtures/ProfileAdminController', __DIR__);
        $this->client = $this->createClientWithNewDatabase($fixturesPath);
        $this->router = $this->client->getContainer()->get('router');
        $this->repository = $this->client->getContainer()->get('evp_blacklist.repository.blacklist_profile');
        $this->blacklistRepository = $this->client->getContainer()->get('evp_blacklist.repository.blacklist');
        $this->logIn();
    }

    /**
     * @param string $file
     * @param string $message
     * @param Profile[] $expectedData
     *
     * @throws Exception
     *
     * @dataProvider dataProvider
     */
    public function testImportForm(string $file, string $message, array $expectedData): void
    {
        $crawler = $this->client->request('GET', $this->router->generate('admin_evp_blacklist_profile_import'));
        $this->checkFormRendering();
        $this->submitForm($crawler, $file);
        $this->checkSubmitResult($message);
        $this->checkSavedData($expectedData);
    }

    /**
     * @return array[]
     *
     * @throws ReflectionException
     */
    public function dataProvider(): array
    {
        return [
            'negativeCase-categoryIdShouldBeNumeric' => [
                'negativeCase-categoryIdShouldBeNumeric.csv',
                'Incorrect import data: wrong categories field value.',
                [],
            ],
            'negativeCase-emptyFile' => ['negativeCase-emptyFile.csv', 'An empty file is not allowed.', []],
            'negativeCase-incorrectAlternativeNameFormat' => [
                'negativeCase-incorrectAlternativeNameFormat.csv',
                'Incorrect import data: wrong alternative name field format.',
                [],
            ],
            'negativeCase-rowVsHeadersMismatch' => [
                'negativeCase-rowVsHeadersMismatch.csv',
                'Incorrect import data: row vs header mismatch.',
                [],
            ],
            'negativeCase-allFields-unexpected-type' => [
                'positiveCase-allFields-unexpected-type.csv',
                'Incorrect import data: wrong type field value.',
                [],
            ],
            'positiveCase-allFields' => [
                'positiveCase-allFields.csv',
                'Profiles are imported.',
                $this->getPositiveCaseAllFieldsProfiles(),
            ],
            'positiveCase-allFields-type-person' => [
                'positiveCase-allFields-type-person.csv',
                'Profiles are imported.',
                $this->getPositiveCaseAllFieldsProfiles(),
            ],
            'positiveCase-allFields-type-company' => [
                'positiveCase-allFields-type-company.csv',
                'Profiles are imported.',
                $this->getPositiveCaseAllFieldsProfiles(Profile::TYPE_COMPANY),
            ],
            'positiveCase-allFields-type-bank' => [
                'positiveCase-allFields-type-bank.csv',
                'Profiles are imported.',
                $this->getPositiveCaseAllFieldsProfiles(Profile::TYPE_BANK),
            ],
            'positiveCase-manyCategories' => [
                'positiveCase-manyCategories.csv',
                'Profiles are imported.',
                $this->getPositiveCaseManyCategoriesProfiles(),
            ],
            'positiveCase-onlyRequiredField' => [
                'positiveCase-onlyRequiredField.csv',
                'Profiles are imported.',
                $this->getPositiveCaseOnlyRequiredFieldProfiles(),
            ],
            'positiveCase-partialAlternativeNames' => [
                'positiveCase-partialAlternativeNames.csv',
                'Profiles are imported.',
                $this->getPositiveCasePartialAlternativeNamesProfiles(),
            ],
            'positiveCase-TrickyChars' => [
                'positiveCase-trickyChars.csv',
                'Profiles are imported.',
                $this->getPositiveCaseTrickyCharsProfiles(),
            ],
            'positiveCase-allFields (txt file)' => [
                'positiveCase-allFields.txt',
                'Profiles are imported.',
                $this->getPositiveCaseAllFieldsProfiles(),
            ],
            'negativeCase-allFieldsWithoutHeaders' => [
                'negativeCase-allFieldsWithoutHeaders.txt',
                'Incorrect import data: file contains invalid column headings.',
                [],
            ],
            'negativeCase-invalidFile' => [
                'negativeCase-invalidFile.txt',
                'Incorrect import data: file contains invalid column headings.',
                [],
            ],
        ];
    }

    /** @dataProvider importFormExceptionDataProvider */
    public function testImportFormException(string $blacklistId): void
    {
        $blacklists = $this->blacklistRepository->findNotExternalAndNotDeprecated();
        $allowedBlacklistIds = array_map(
            fn (Blacklist $blacklist) => sprintf('"%d"', $blacklist->getId()),
            $blacklists
        );
        $allowedBlacklistIds = '"", ' . implode(', ', $allowedBlacklistIds);

        $crawler = $this->client->request('GET', $this->router->generate('admin_evp_blacklist_profile_import'));
        $this->checkFormRendering();

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage(sprintf(
            'Input "profile_import[blacklistId]" cannot take "%s" as a value (possible values: %s).',
            $blacklistId,
            $allowedBlacklistIds
        ));

        $this->submitForm($crawler, 'positiveCase-allFields.csv', $blacklistId);
    }

    public function importFormExceptionDataProvider(): array
    {
        return [
            'External' => ['blacklistId' => '2'],
            'Deprecated' => ['blacklistId' => '3'],
            'Not existed' => ['blacklistId' => '400'],
        ];
    }

    /**
     * @return Profile[]
     *
     * @throws ReflectionException
     */
    private function getPositiveCaseManyCategoriesProfiles(): array
    {
        $blacklist = new Blacklist('first-blacklist', 'FirstBlacklist');
        $this->setIdProperty($blacklist, 1);
        $categoryOne = (new Category())->setName('firstCategory');
        $this->setIdProperty($categoryOne, 1);
        $categoryTwo = (new Category())->setName('secondCategory');
        $this->setIdProperty($categoryTwo, 2);

        return [
            (new Profile())
                ->setBlacklist($blacklist)
                ->setVersion('202312131750')
                ->addCategory($categoryOne)
                ->addCategory($categoryTwo),
        ];
    }

    /**
     * @return Profile[]
     *
     * @throws ReflectionException
     */
    private function getPositiveCaseTrickyCharsProfiles(): array
    {
        $blacklist = new Blacklist('first-blacklist', 'FirstBlacklist');
        $this->setIdProperty($blacklist, 1);

        return [
            (new Profile())
                ->setName('фыва пр олдж')
                ->setFirstName('Евгений')
                ->setMiddleName('Noël')
                ->setLastName('Ruairí')
                ->setInfo('фыва')
                ->setBlacklist($blacklist)
                ->setVersion('202312131750')
                ->addAlternativeName(
                    (new AlternativeName())
                        ->setName('Renée')
                        ->setFirstName('олдж')
                        ->setMiddleName('олдж')
                        ->setLastName('Эзоп')
                ),
        ];
    }

    /**
     * @return Profile[]
     *
     * @throws ReflectionException
     */
    private function getPositiveCasePartialAlternativeNamesProfiles(): array
    {
        $blacklist = new Blacklist('first-blacklist', 'FirstBlacklist');
        $this->setIdProperty($blacklist, 1);

        return [
            (new Profile())
                ->setBlacklist($blacklist)
                ->setVersion('202312131750')
                ->addAlternativeName(
                    (new AlternativeName())
                        ->setName('alt name')
                        ->setLastName('alt last name')
                ),
        ];
    }

    /**
     * @return Profile[]
     *
     * @throws ReflectionException
     */
    private function getPositiveCaseOnlyRequiredFieldProfiles(): array
    {
        $blacklist = new Blacklist('first-blacklist', 'FirstBlacklist');
        $this->setIdProperty($blacklist, 1);

        return [
            (new Profile())
                ->setBlacklist($blacklist)
                ->setVersion('202312131750'),
        ];
    }

    /**
     * @return Profile[]
     *
     * @throws ReflectionException
     */
    private function getPositiveCaseAllFieldsProfiles(string $type = Profile::TYPE_PERSON): array
    {
        $blacklist = new Blacklist('first-blacklist', 'FirstBlacklist');
        $this->setIdProperty($blacklist, 1);
        $category = (new Category())->setName('firstCategory');
        $this->setIdProperty($category, 1);

        return [
            $this->getPositiveCaseAllFieldsFirstProfile($category, $blacklist, $type),
            $this->getPositiveCaseAllFieldsSecondProfile($category, $blacklist, $type),
        ];
    }

    private function logIn(): void
    {
        $session = $this->client->getContainer()->get('session');
        $token = new UsernamePasswordToken(
            'admin',
            null,
            'main',
            ['ROLE_ADMIN', 'ROLE_EVP_BLACKLIST_ADMIN_PERSON_CREATE']
        );
        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    private function checkFormRendering(): void
    {
        $this->assertSame(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString('Blacklist name', $this->client->getResponse()->getContent());
        $this->assertStringContainsString('Upload CSV with Profiles data', $this->client->getResponse()->getContent());
    }

    private function submitForm(Crawler $crawler, string $file, string $blacklistId = '1'): void
    {
        $form = $crawler->selectButton('Import')->form();
        $form['profile_import[blacklistId]'] = $blacklistId;
        $form['profile_import[upload]'] = sprintf('%s/Resources/ProfileAdminController/%s', __DIR__, $file);
        $this->client->submit($form);
    }

    private function checkSubmitResult(string $message): void
    {
        $this->assertSame(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString($message, $this->client->getResponse()->getContent());
    }

    /**
     * @param Profile[] $expectedData
     */
    private function checkSavedData(array $expectedData): void
    {
        foreach ($this->repository->findAll() as $key => $entity) {
            $this->assertEqualProfiles($expectedData[$key], $entity);
        }
    }

    /**
     * @param $entity
     * @param int $value
     *
     * @throws ReflectionException
     */
    private function setIdProperty($entity, int $value): void
    {
        $reflection = new ReflectionClass($entity);
        $property = $reflection->getProperty('id');
        $property->setAccessible(true);
        $property->setValue($entity, $value);
    }

    private function assertEqualProfiles(Profile $expectedEntity, Profile $entity): void
    {
        $this->assertMainProfileFields($expectedEntity, $entity);
        $this->assertProfileBlacklist($expectedEntity, $entity);
        $this->assertProfileCategories($expectedEntity, $entity);
        $this->assertProfileAlternativeNames($expectedEntity, $entity);
    }

    private function assertMainProfileFields(Profile $expectedEntity, Profile $entity): void
    {
        $this->assertEquals($expectedEntity->getType(), $entity->getType());
        $this->assertEquals($expectedEntity->getName(), $entity->getName());
        $this->assertEquals($expectedEntity->getFirstName(), $entity->getFirstName());
        $this->assertEquals($expectedEntity->getMiddleName(), $entity->getMiddleName());
        $this->assertEquals($expectedEntity->getLastName(), $entity->getLastName());
        $this->assertEquals($expectedEntity->getCode(), $entity->getCode());
        $this->assertEquals($expectedEntity->getCountry(), $entity->getCountry());
        $this->assertEquals($expectedEntity->getBirthday(), $entity->getBirthday());
        $this->assertEquals($expectedEntity->getPhone(), $entity->getPhone());
        $this->assertEquals($expectedEntity->getInfo(), $entity->getInfo());
        $this->assertEquals($expectedEntity->getVersion(), $entity->getVersion());
        $this->assertNotNull($entity->getExternalId());
    }

    private function assertProfileCategories(Profile $expectedEntity, Profile $entity): void
    {
        foreach ($entity->getCategories() as $key => $category) {
            $this->assertEquals($expectedEntity->getCategories()[$key]->getId(), $category->getId());
            $this->assertEquals($expectedEntity->getCategories()[$key]->getName(), $category->getName());
        }
    }

    private function assertProfileAlternativeNames(Profile $expectedEntity, Profile $entity): void
    {
        foreach ($entity->getAlternativeNames() as $key => $alternativeName) {
            $expectedAlternativeName = $expectedEntity->getAlternativeNames()[$key];
            $this->assertEquals($expectedAlternativeName->getName(), $alternativeName->getName());
            $this->assertEquals($expectedAlternativeName->getFirstName(), $alternativeName->getFirstName());
            $this->assertEquals($expectedAlternativeName->getMiddleName(), $alternativeName->getMiddleName());
            $this->assertEquals($expectedAlternativeName->getLastName(), $alternativeName->getLastName());
        }
    }

    private function assertProfileBlacklist(Profile $expectedEntity, Profile $entity): void
    {
        $this->assertEquals($expectedEntity->getBlacklist()->getName(), $entity->getBlacklist()->getName());
        $this->assertEquals($expectedEntity->getBlacklist()->getKey(), $entity->getBlacklist()->getKey());
        $this->assertEquals($expectedEntity->getBlacklist()->getId(), $entity->getBlacklist()->getId());
    }

    private function getPositiveCaseAllFieldsFirstProfile(
        Category $category,
        Blacklist $blacklist,
        string $type = Profile::TYPE_PERSON
    ): Profile {
        return (new Profile())
            ->setType($type)
            ->setName('Row with all fields #1')
            ->setFirstName('first name 1')
            ->setMiddleName('middle name 1')
            ->setLastName('last name 1')
            ->setCode('12345')
            ->setCountry('lt')
            ->setBirthday('1970-01-01')
            ->setPhone('12345678910')
            ->setInfo('info')
            ->setVersion('202312131750')
            ->addCategory($category)
            ->addAlternativeName(
                (new AlternativeName())
                    ->setName('alt name 1')
                    ->setFirstName('alt first name 1')
                    ->setMiddleName('alt middle name 1')
                    ->setLastName('alt last name 1')
            )
            ->addAlternativeName(
                (new AlternativeName())
                    ->setName('alt name 1.2')
                    ->setFirstName('alt first name 1.2')
                    ->setMiddleName('alt middle name 1.2')
                    ->setLastName('alt last name 1.2')
            )
            ->setBlacklist($blacklist)
        ;
    }

    private function getPositiveCaseAllFieldsSecondProfile(
        Category $category,
        Blacklist $blacklist,
        string $type = Profile::TYPE_PERSON
    ): Profile {
        return (new Profile())
            ->setType($type)
            ->setName('Row with all fields #2')
            ->setFirstName('first name 2')
            ->setMiddleName('middle name 2')
            ->setLastName('last name 2')
            ->setCode('123456')
            ->setCountry('uk')
            ->setBirthday('1970-01-02')
            ->setPhone('123456789101')
            ->setInfo('info2')
            ->setVersion('202312131750')
            ->addCategory($category)
            ->addAlternativeName(
                (new AlternativeName())
                    ->setName('alt name 2')
                    ->setFirstName('alt first name 2')
                    ->setMiddleName('alt middle name 2')
                    ->setLastName('alt last name 2')
            )
            ->addAlternativeName(
                (new AlternativeName())
                    ->setName('alt name 2.2')
                    ->setFirstName('alt first name 2.2')
                    ->setMiddleName('alt middle name 2.2')
                    ->setLastName('alt last name 2.2')
            )
            ->setBlacklist($blacklist)
        ;
    }
}
