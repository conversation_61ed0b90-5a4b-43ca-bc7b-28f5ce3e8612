<?php

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Service\PeriodicalBlacklistControlDataCheck;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Exception\PeriodicalBlacklistControlDataCheckException;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckManager;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class PeriodicalBlacklistControlDataCheckManagerTest extends PersistableWebTestCase
{
    private PeriodicalBlacklistControlDataCheckManager $periodicalBlacklistControlDataCheckManager;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private vfsStreamDirectory $vfsStreamDirectory;
    private ProfileRepository $profileRepository;

    /**
     * @var BlacklistManager|MockObject
     */
    private $blacklistManager;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->vfsStreamDirectory = vfsStream::setup();

        $this->blacklistManager = $this->createMock(BlacklistManager::class);
        $this->getContainer()->set('evp_blacklist.manager', $this->blacklistManager);
        $this->getContainer()->set(
            'evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager',
            new PeriodicalBlacklistControlDataCheckManager(
                $this->getContainer()->get('evp_blacklist.manager'),
                $this->getContainer()->get('paysera.csv_writer'),
                $this->getContainer()->get('evp_blacklist.symbols_hash_generator'),
                $this->getContainer()->get('evp_blacklist.configuration_manager'),
                $this->vfsStreamDirectory->url()
            )
        );

        $this->periodicalBlacklistControlDataCheckManager = $this->getContainer()->get(
            'evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->profileRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist_profile');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testInitDataCheckLog(): void
    {
        $blacklist = $this->fixturesHelper->createBlacklist();

        $dataCheckLog = $this->periodicalBlacklistControlDataCheckManager->initDataCheckLog(
            $blacklist,
            BlacklistProfileDataCheckLog::TYPE_AUTOMATIC,
            false
        );

        $this->assertNotNull($dataCheckLog->getBlacklist());
        $this->assertEquals($blacklist, $dataCheckLog->getBlacklist());
        $this->assertEquals(0, $dataCheckLog->getItemsCount());
        $this->assertEquals(0, $dataCheckLog->getMatchedItemsCount());
        $this->assertNotNull($dataCheckLog->getReference());
        $this->assertEquals(25, strlen($dataCheckLog->getReference()));
    }

    public function testGetDataCheckResultFilePath(): void
    {
        $dataCheckLog = $this->fixturesHelper->createBlacklistProfileDataCheckLog(
            $this->fixturesHelper->createBlacklist()
        );

        $this->assertEquals(
            sprintf(
                '%s/%s_%s.csv',
                $this->vfsStreamDirectory->url(),
                $dataCheckLog->getStartedAt()->format('Y-m-d'),
                $dataCheckLog->getReference()
            ),
            $this->periodicalBlacklistControlDataCheckManager->getDataCheckResultFilePath($dataCheckLog)
        );
    }

    /**
     * @param bool $saveToDatabase
     * @param array $profileData
     * @param Profile[] $expectedProfileAlternatives
     * @param string|null $expectedException
     * @param string|null $expectedExceptionMessage
     *
     * @dataProvider profileAlternativesDataProvider
     */
    public function testGetProfileAlternatives(
        bool $saveToDatabase,
        array $profileData,
        array $expectedProfileAlternatives,
        ?string $expectedException = null,
        ?string $expectedExceptionMessage = null
    ): void {
        $blacklist = $this->fixturesHelper->createBlacklist();
        $profile = $this->fixturesHelper->createPersonProfile(
            $blacklist,
            $profileData['profileName'],
            '',
            $profileData['country'],
            $profileData['externalId']
        );

        foreach ($profileData['alternativeNames'] as $alternativeName) {
            $profile->addAlternativeName(
                $this->fixturesHelper->createProfileAlternativeName(
                    $profile,
                    $alternativeName
                )
            );
        }

        foreach ($profileData['birthdays'] as $birthday) {
            $profile->addAlternativeBirthday(
                $this->fixturesHelper->createProfileAlternativeBirthday(
                    $profile,
                    $birthday
                )
            );
        }

        if ($saveToDatabase === true) {
            $this->entityManager->flush();

            $profile = $this->profileRepository->findAll()[0];
        }

        if ($expectedException !== null) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedExceptionMessage);
        }

        $profileAlternatives = $this->periodicalBlacklistControlDataCheckManager->getProfileAlternatives($profile);

        if ($expectedException !== null) {
            return;
        }

        $this->assertCount(count($expectedProfileAlternatives), $profileAlternatives);

        for ($i = 0; $i < count($expectedProfileAlternatives); $i++) {
            $this->assertEquals(
                $expectedProfileAlternatives[$i]->getDisplayName(),
                $profileAlternatives[$i]->getDisplayName()
            );
            $this->assertEquals(
                $expectedProfileAlternatives[$i]->getType(),
                $profileAlternatives[$i]->getType()
            );
            $this->assertEquals(
                $expectedProfileAlternatives[$i]->getCountry(),
                $profileAlternatives[$i]->getCountry()
            );
            $this->assertEquals(
                $expectedProfileAlternatives[$i]->getBirthday(),
                $profileAlternatives[$i]->getBirthday()
            );
            $this->assertEquals(
                $expectedProfileAlternatives[$i]->getInfo(),
                $profileAlternatives[$i]->getInfo()
            );
        }
    }

    /**
     * @param Profile $profile
     * @param BlacklistResult[] $blacklistResults
     *
     * @dataProvider processAndLogDataProvider
     */
    public function testProcessAndLog(
        Profile $profile,
        array $blacklistResults
    ): void {
        $this->blacklistManager
            ->expects($this->once())
            ->method('getProfileSearchResults')
            ->willReturn($blacklistResults)
        ;
        $dataCheckLog = $this->fixturesHelper->createBlacklistProfileDataCheckLog(
            $this->fixturesHelper->createBlacklist()
        );

        $results = $this->periodicalBlacklistControlDataCheckManager->processAndLog(
            $dataCheckLog,
            $profile
        );

        $this->assertCount(count($blacklistResults), $results);

        $logFilePath = $this->periodicalBlacklistControlDataCheckManager->getDataCheckResultFilePath(
            $dataCheckLog
        );

        $this->assertTrue(file_exists($logFilePath));

        $keyWords = [
            'Id (profileId_DJId_birthdayVersion)',
            'Search name',
            'Search birthday',
            'Action',
            'Blacklist',
            'Matched profile ID',
            'Matched profile name',
            'Matched profile external ID',
            $profile->getInfo(),
            $profile->getDisplayName(),
        ];

        if (count($results) === 0) {
            $keyWords[] = 'none';
        }

        foreach ($blacklistResults as $blacklistResult) {
            $keyWords[] = 'needs_review';
            $keyWords[] = $blacklistResult->getFoundProfile()->getBlacklist()->getName();
            $keyWords[] = $blacklistResult->getFoundProfile()->getDisplayName();
            $keyWords[] = $blacklistResult->getFoundProfile()->getExternalId();
        }

        foreach ($keyWords as $keyWord) {
            $this->assertTrue(
                strpos(file_get_contents($logFilePath), $keyWord) !== false
            );
        }
    }

    public function testProcessAndLogException(): void
    {
        $this->expectException(PeriodicalBlacklistControlDataCheckException::class);
        $this->expectExceptionMessage('Data log entity not initialized!');

        $this->periodicalBlacklistControlDataCheckManager->processAndLog(
            new BlacklistProfileDataCheckLog(),
            new Profile()
        );
    }

    public function processAndLogDataProvider(): array
    {
        return [
            'profile matched' => [
                (new Profile())
                    ->setName('Test name')
                    ->setInfo('ID_1'),
                [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            (new Profile())
                                ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                ->setName('Matched name 1')
                                ->setExternalId('111')
                        ),
                    (new BlacklistResult())
                        ->setFoundProfile(
                            (new Profile())
                                ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                ->setName('Matched name 2')
                                ->setExternalId('222')
                        ),
                    (new BlacklistResult())
                        ->setFoundProfile(
                            (new Profile())
                                ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                ->setName('Matched name 3')
                                ->setExternalId('333')
                        ),
                ],
            ],
            'profile matched - 2' => [
                (new Profile())
                    ->setName('Test name')
                    ->setInfo('ID_2'),
                [
                    (new BlacklistResult())
                        ->setFoundProfile(
                            (new Profile())
                                ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                ->setName('Matched name 2')
                                ->setExternalId('222')
                        ),
                ],
            ],
            'not profile matched' => [
                (new Profile())
                    ->setName('Test name')
                    ->setInfo('ID_3'),
                [],
            ],
        ];
    }

    public function profileAlternativesDataProvider(): array
    {
        return [
            'DB profile with alternative names and birthdays' => [
                true,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [
                        'Origin Name',
                        'Origin N.',
                    ],
                    'birthdays' => [
                        '1890',
                        '0000',
                        '2022-10-06',
                    ],
                ],
                [
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                ],
                null,
                null,
            ],
            'DB profile with alternative names but without birthdays' => [
                true,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [
                        'Origin Name',
                        'Origin N.',
                    ],
                    'birthdays' => [],
                ],
                [
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                ],
                null,
                null,
            ],
            'DB profile without alternative names and without birthdays' => [
                true,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [],
                    'birthdays' => [],
                ],
                [
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                ],
                null,
                null,
            ],
            'Profile not saved to DB' => [
                false,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [],
                    'birthdays' => [],
                ],
                [],
                PeriodicalBlacklistControlDataCheckException::class,
                'Cannot get alternatives for given profile!',
            ],
            'DB profile with alternative names and birthdays (+ incorrect birthday)' => [
                true,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [
                        'Origin Name',
                        'Origin N.',
                    ],
                    'birthdays' => [
                        '1890',
                        '0000',
                        '2022-10-06',
                        '1976 г.р.',
                    ],
                ],
                [
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_0')
                        ->setBirthday('1890'),
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_1')
                        ->setBirthday('0000'),
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123_2')
                        ->setBirthday('2022-10-06'),
                ],
                null,
                null,
            ],
            'DB profile with alternative names and incorrect birthday' => [
                true,
                [
                    'profileName' => 'Original Name',
                    'externalId' => '123',
                    'country' => 'lt',
                    'type' => Profile::TYPE_PERSON,
                    'alternativeNames' => [
                        'Origin Name',
                        'Origin N.',
                    ],
                    'birthdays' => [
                        '1976 г.р.',
                    ],
                ],
                [
                    (new Profile())
                        ->setName('Original Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                    (new Profile())
                        ->setName('Origin Name')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                    (new Profile())
                        ->setName('Origin N.')
                        ->setType(Profile::TYPE_PERSON)
                        ->setCountry('lt')
                        ->setInfo('1_123'),
                ],
                null,
                null,
            ],
        ];
    }
}
