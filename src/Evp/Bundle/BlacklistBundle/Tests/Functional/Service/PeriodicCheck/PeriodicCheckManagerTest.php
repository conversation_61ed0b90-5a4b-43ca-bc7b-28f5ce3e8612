<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Service\PeriodicCheck;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Service\BlacklistProfileDataCheckLogFolder;
use Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\PeriodicCheckManager;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class PeriodicCheckManagerTest extends PersistableWebTestCase
{
    private vfsStreamDirectory $vfsStreamDirectory;
    private EntityManagerInterface $entityManager;
    private PeriodicCheckManager $manager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->vfsStreamDirectory = vfsStream::setup();

        $this->getContainer()->set(
            'evp_blacklist.periodical_check.blacklist_profile_data_check_log_folder',
            new BlacklistProfileDataCheckLogFolder(
                $this->getContainer()->get('filesystem'),
                $this->vfsStreamDirectory->url()
            )
        );

        $this->manager = new PeriodicCheckManager(
            $this->getContainer()->get('paysera.csv_reader'),
            $this->getContainer()->get('paysera.csv_writer'),
            $this->getContainer()->get('evp_blacklist.periodic_check.profile.profile_mapper'),
            $this->getContainer()->get('evp_blacklist.periodic_check.profile.manager'),
            $this->getContainer()->get('evp_blacklist.repository.blacklist_profile_data_check_log'),
            $this->getContainer()->get('evp_blacklist.periodical_check.blacklist_profile_data_check_log_folder'),
        );

        $this->getContainer()->set(
            'evp_blacklist.periodic_check.periodic_check_manager',
            $this->manager
        );

        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testCreateManipulatedProfiles(): void
    {
        $dataCheckLog = $this->fixturesHelper->createBlacklistProfileDataCheckLog(
            $this->fixturesHelper->createBlacklist()
        );
        $this->entityManager->flush();

        $profilesFilepath = $this->vfsStreamDirectory->url() . '/profiles.csv';
        file_put_contents($profilesFilepath, file_get_contents(__DIR__ . '/profiles.csv'));

        $manipulatedProfilesFilepath = $this->manager->createManipulatedProfiles(1, $profilesFilepath);

        $this->assertTrue($this->vfsStreamDirectory->hasChild(sprintf(
            '%s-%s/manipulated.csv',
            $dataCheckLog->getStartedAt()->format('Y-m-d'),
            $dataCheckLog->getReference()
        )));
        $this->assertFileExists($manipulatedProfilesFilepath);

        $linesProfile = file($profilesFilepath);
        $linesManipulated = file($manipulatedProfilesFilepath);

        $this->assertLessThan($linesManipulated, $linesProfile);
    }
}
