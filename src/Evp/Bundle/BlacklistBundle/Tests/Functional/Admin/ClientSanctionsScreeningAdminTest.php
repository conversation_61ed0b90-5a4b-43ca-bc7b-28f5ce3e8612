<?php

declare(strict_types=1);

namespace Functional\Admin;

use Evp\Bundle\BlacklistBundle\Admin\ClientSanctionsScreeningAdmin;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class ClientSanctionsScreeningAdminTest extends PersistableWebTestCase
{
    private ?ClientSanctionsScreeningAdmin $admin;

    protected function setUp(): void
    {
        $this->admin = $this->getContainer()->get('evp_blacklist.admin.client_sanction_screening');
    }

    public function testSessionStorage(): void
    {
        $randomString = uniqid('', true);

        $this->admin->setReferer($randomString);
        $referer = $this->admin->getReferer();

        $this->assertEquals($randomString, $referer);
    }
}
