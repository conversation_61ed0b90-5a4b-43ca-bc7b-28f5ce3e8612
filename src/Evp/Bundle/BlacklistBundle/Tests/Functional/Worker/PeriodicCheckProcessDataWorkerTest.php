<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Functional\Worker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Evp\Bundle\BlacklistBundle\Worker\PeriodicCheckProcessDataWorker;
use Exception;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class PeriodicCheckProcessDataWorkerTest extends PersistableWebTestCase
{
    private vfsStreamDirectory $vfsStreamDirectory;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private MockObject $blacklistManager;
    private PeriodicCheckProcessDataWorker $periodicCheckProcessDataWorker;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->vfsStreamDirectory = vfsStream::setup();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->blacklistManager = $this->createMock(BlacklistManager::class);
        $this->getContainer()->set(
            'evp_blacklist.manager',
            $this->blacklistManager
        );

        $this->periodicCheckProcessDataWorker = $this->getContainer()->get('evp_bundle_blacklist.worker.periodic_check_process_data_worker');
    }

    public function testNoResults(): void
    {
        $blacklist = $this->fixturesHelper->createBlacklist();
        $checkLog = $this->fixturesHelper->createBlacklistProfileDataCheckLog($blacklist);
        $this->entityManager->flush();

        $profilesFilepath = $this->vfsStreamDirectory->url() . '/manipulated.csv';
        file_put_contents($profilesFilepath, file_get_contents(__DIR__ . '/manipulated.csv'));

        $data = [
            'check_log_id' => 1,
            'path' => $profilesFilepath
        ];

        $this->createProfile(
            $blacklist,
            'John',
            'Junior',
            'Smithperson',
            'code1',
            'MT',
            '2022-12-24'
        );

        $this->createProfile(
            $blacklist,
            'Augustus',
            'Vectorius',
            'Examplus',
            'code2',
            'GB',
            '2000-01-15'
        );

        $this->blacklistManager->expects($this->exactly(0))->method('getProfileSearchResults');

        $this->periodicCheckProcessDataWorker->work($data);

        $this->assertEquals(BlacklistProfileDataCheckLog::STATUS_DONE, $checkLog->getStatus());
    }

    private function createProfile(
        Blacklist $blacklist,
        string $firstName,
        string $middleName,
        string $lastName,
        string $code,
        string $country,
        string $birthday
    ): Profile {
        return (new Profile())
            ->setBlacklist($blacklist)
            ->setFirstName($firstName)
            ->setMiddleName($middleName)
            ->setLastName($lastName)
            ->setCode($code)
            ->setCountry($country)
            ->setBirthday($birthday);
    }
}
