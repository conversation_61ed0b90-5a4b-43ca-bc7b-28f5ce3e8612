<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Command\BlacklistSendReportsCommand;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\BlacklistSender;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class BlacklistSendReportsCommandTest extends CommandTestCase
{
    /**
     * @var BlacklistSendReportsCommand
     */
    protected $command;

    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private MockObject $blacklistSender;

    public function setUp(): void
    {
        $this->blacklistSender = $this->createMock(BlacklistSender::class);
        $this->getContainer()->set('evp_blacklist.blacklist_sender', $this->blacklistSender);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.send_reports'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param array $blacklistCheckResultData
     * @dataProvider processDataProvider
     */
    public function testProcess(array $blacklistCheckResultData): void
    {
        $iteration = 1;
        foreach($blacklistCheckResultData as $data) {
            $blacklist = $this->fixturesHelper->createBlacklist('name' . $iteration, 'key' . $iteration);

            $clientBlacklistCheckResult = $this->fixturesHelper
                ->createClientBlacklistCheckResult($data['blacklisted'])
                ->setCheckedAt($data['checkedAt'])
                ->setClient($this->fixturesHelper->createClientNatural($iteration))
                ->setBlacklist($blacklist)
            ;

            $category = $this->fixturesHelper->createCategory($iteration);
            $profile = $this->fixturesHelper->createPersonProfile(
                $blacklist,
                'firstName',
                'lastName',
                'lt',
                null,
                Profile::TYPE_PERSON,
                '1',
                $category
            );

            $this->fixturesHelper->createClientBlacklistCheckResultItem($clientBlacklistCheckResult, $profile);
            $iteration++;
        }
        $this->entityManager->flush();

        $this->blacklistSender
            ->expects($this->exactly(2))
            ->method('send')
        ;

        $this->commandTester->execute(
            [
                'command' => $this->command->getName(),
                '--limit' => 1
            ],
        );
    }

    public function processDataProvider(): array
    {
        return [
            [
                [
                    [
                        'checkedAt' => new DateTime('1 hour ago'),
                        'blacklisted' => true,
                    ],
                    [
                        'checkedAt' => new DateTime('23 hour ago'),
                        'blacklisted' => true,
                    ],
                    [
                        'checkedAt' => new DateTime('2 days ago'),
                        'blacklisted' => true,
                    ],
                    [
                        'checkedAt' => new DateTime('1 hour ago'),
                        'blacklisted' => false,
                    ],
                ]
            ]
        ];
    }
}

