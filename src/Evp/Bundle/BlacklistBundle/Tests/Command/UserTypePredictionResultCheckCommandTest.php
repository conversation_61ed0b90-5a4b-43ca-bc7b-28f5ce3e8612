<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use Evp\Bundle\BlacklistBundle\Command\UserTypePredictionResultCheckCommand;
use Paysera\Client\UserTypeAi\Entity\Prediction;
use Paysera\Client\UserTypeAi\UserTypeAi;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class UserTypePredictionResultCheckCommandTest extends CommandTestCase
{
    /**
     * @var UserTypePredictionResultCheckCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private MockObject $userTypeAiClient;

    public function setUp(): void
    {
        $this->userTypeAiClient = $this->createMock(UserTypeAi::class);

        $this->getContainer()->set('evp_bank_transfer.client.user_type_ai_resolver', $this->userTypeAiClient);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.user_type_prediction_result_check'
        );
    }

    public function testExecute(): void
    {
        $name = 'John';
        $prediction = (new Prediction())
            ->setName($name)
        ;

        $predictionReturn = (new Prediction())
            ->setName($name)
            ->setType('natural')
            ->setConfidence('1')
        ;

        $this->userTypeAiClient
            ->expects($this->once())
            ->method('createPrediction')
            ->with($prediction)
            ->willReturn($predictionReturn)
        ;

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'name' => $name
        ]);

        $this->assertSame(
            sprintf(
                'Result:%s--> Name: %s%s--> Type: %s%s--> Confidence: %s',
                PHP_EOL,
                $predictionReturn->getName(),
                PHP_EOL,
                $predictionReturn->getType(),
                PHP_EOL,
                $predictionReturn->getConfidence()
            ),
            trim($this->commandTester->getDisplay())
        );
    }
}
