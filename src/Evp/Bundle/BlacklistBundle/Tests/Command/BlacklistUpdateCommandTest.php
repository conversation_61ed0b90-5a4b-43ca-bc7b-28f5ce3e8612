<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use Evp\Bundle\BlacklistBundle\Command\BlacklistUpdateCommand;
use Evp\Bundle\BlacklistBundle\Service\BlacklistUpdateManager;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class BlacklistUpdateCommandTest extends CommandTestCase
{
    /**
     * @var BlacklistUpdateCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private MockObject $blacklistUpdateManager;

    public function setUp(): void
    {
        $this->blacklistUpdateManager = $this->createMock(BlacklistUpdateManager::class);
        $this->getContainer()->set('evp_blacklist.update_manager', $this->blacklistUpdateManager);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.blacklist_update'
        );
    }

    public function testExecute(): void
    {
        $this->blacklistUpdateManager
            ->expects($this->once())
            ->method('update')
        ;

        $this->commandTester->execute(['command' => $this->command->getName()]);

        $this->assertSame(
            'Blacklist update completed',
            trim($this->commandTester->getDisplay())
        );
    }
}
