<?php

declare(strict_types=1);

namespace Command\ManageBlacklist;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Command\ManageBlacklist\ManageBlacklistLicensedPartnersCommand;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use RuntimeException;
use Symfony\Component\Console\Tester\CommandTester;

class ManageBlacklistLicensedPartnersCommandTest extends CommandTestCase
{
    /** @var ManageBlacklistLicensedPartnersCommand */
    protected $command;

    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabaseNoTransaction();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->blacklistRepository = $this->getContainer()->get('evp_blacklist.repository.blacklist');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.manage_blacklist.licensed_partners',
        );
    }

    public function testSetLicensedPartnersToBlacklist(): void
    {
        $blacklist = $this->fixturesHelper->createBlacklist();
        $licensedPartnerCodes = ['paysera_lt', 'paysera_al', 'paysera_xk'];

        foreach ($licensedPartnerCodes as $partnerCode) {
            $this->fixturesHelper->createLicensedPartner($partnerCode);
        }

        $this->entityManager->flush();
        $this->runCommand($blacklist->getKey(), $licensedPartnerCodes);

        $blacklist = $this->blacklistRepository->findOneByKey($blacklist->getKey());
        $this->assertCount(3, $blacklist->getLicensedPartners());

        $blacklistLicensedPartnerCodes = array_map(
            fn (LicensedPartner $licensedPartner) => $licensedPartner->getPartnerCode(),
            $blacklist->getLicensedPartners()->toArray()
        );
        $this->assertEquals($licensedPartnerCodes, $blacklistLicensedPartnerCodes);
    }

    public function testUpdateLicensedPartnersOfBlacklist(): void
    {
        $blacklist = $this->fixturesHelper->createBlacklist();
        $licensedPartnerCodes = ['paysera_lt', 'paysera_al', 'paysera_xk'];

        foreach ($licensedPartnerCodes as $partnerCode) {
            $this->fixturesHelper->createLicensedPartner($partnerCode);
        }

        $existedLicensedPartnerCodes = ['paysera_one', 'paysera_two'];
        $existedLicensedPartners = new ArrayCollection();
        foreach ($existedLicensedPartnerCodes as $partnerCode) {
            $existedLicensedPartners->add($this->fixturesHelper->createLicensedPartner($partnerCode));
        }
        $blacklist->setLicensedPartners($existedLicensedPartners);

        $this->entityManager->flush();
        $this->runCommand($blacklist->getKey(), $licensedPartnerCodes);

        $blacklist = $this->blacklistRepository->findOneByKey($blacklist->getKey());
        $this->assertCount(3, $blacklist->getLicensedPartners());

        $blacklistLicensedPartnerCodes = array_map(
            fn (LicensedPartner $licensedPartner) => $licensedPartner->getPartnerCode(),
            $blacklist->getLicensedPartners()->toArray()
        );
        $this->assertEquals($licensedPartnerCodes, $blacklistLicensedPartnerCodes);
    }

    public function testRemoveLicensedPartnersFromBlacklist(): void
    {
        $blacklist = $this->fixturesHelper->createBlacklist();

        $existedLicensedPartnerCodes = ['paysera_one', 'paysera_two'];
        $existedLicensedPartners = new ArrayCollection();
        foreach ($existedLicensedPartnerCodes as $partnerCode) {
            $existedLicensedPartners->add($this->fixturesHelper->createLicensedPartner($partnerCode));
        }
        $blacklist->setLicensedPartners($existedLicensedPartners);

        $this->entityManager->flush();
        $this->runCommand($blacklist->getKey(), []);

        $blacklist = $this->blacklistRepository->findOneByKey($blacklist->getKey());
        $this->assertCount(0, $blacklist->getLicensedPartners());
    }

    public function testBlacklistDoesNotExists(): void
    {
        $blacklistKey = 'test';

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage(sprintf('Blacklist with key "%s" not found', $blacklistKey));
        $this->runCommand($blacklistKey, []);
    }

    public function testLicensedPartnerDoesNotExist(): void
    {
        $licensedPartnerCode = 'test';
        $blacklist = $this->fixturesHelper->createBlacklist();
        $this->entityManager->flush();

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage(sprintf('Licensed partner with code "%s" not found', $licensedPartnerCode));
        $this->runCommand($blacklist->getKey(), [$licensedPartnerCode]);
    }

    private function runCommand(string $blacklistKey, array $licensedPartnerCodes): void
    {
        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'blacklistKey' => $blacklistKey,
            'licensedPartnerCodes' => $licensedPartnerCodes,
        ]);
    }
}
