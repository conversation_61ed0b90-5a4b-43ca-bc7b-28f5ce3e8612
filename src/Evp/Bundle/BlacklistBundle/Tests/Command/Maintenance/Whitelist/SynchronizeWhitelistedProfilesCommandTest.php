<?php

declare(strict_types=1);

namespace Command\Maintenance\Whitelist;

use Evp\Bundle\BankTransferBundle\Tests\Functional\TransferInEvent\DummyPublisher;
use Evp\Bundle\BlacklistBundle\Command\Maintenance\Whitelist\SynchronizeWhitelistedProfilesCommand;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistImportMonitoringService;
use Paysera\Bundle\TransferSurveillanceBundle\Entity\WhitelistedProfile;
use Paysera\Bundle\TransferSurveillanceBundle\Repository\WhitelistedProfileRepository;
use Paysera\Bundle\TransferSurveillanceBundle\Service\Whitelist\WhitelistedProfileManager;
use Paysera\Bundle\TransferSurveillanceBundle\Worker\SynchronizeWhitelistedProfilesWorker;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class SynchronizeWhitelistedProfilesCommandTest extends CommandTestCase
{
    /** @var SynchronizeWhitelistedProfilesCommand */
    protected $command;

    private CommandTester $commandTester;

    /** @var WhitelistedProfileRepository|MockObject */
    private $whitelistedProfileRepository;

    /** @var BlacklistRepository|MockObject */
    private $blacklistRepository;

    /** @var BlacklistImportMonitoringService|MockObject */
    private $monitoringService;

    private WhitelistedProfileManager $whitelistedProfileManager;
    private DummyPublisher $publisher;

    protected function setUp(): void
    {
        $this->blacklistRepository = $this->createMock(BlacklistRepository::class);
        $this->getContainer()->set('evp_blacklist.repository.blacklist', $this->blacklistRepository);

        $this->whitelistedProfileRepository = $this->createMock(WhitelistedProfileRepository::class);
        $this->publisher = new DummyPublisher();

        $this->monitoringService = $this->createMock(BlacklistImportMonitoringService::class);

        $entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');

        $registry = $this->getContainer()->get('doctrine');
        $logger = $this->getContainer()->get('logger');

        $this->whitelistedProfileManager = new WhitelistedProfileManager(
            $this->whitelistedProfileRepository,
            $this->publisher,
            $entityManager,
            $registry,
            $logger,
            10,
            0
        );

        $command = new SynchronizeWhitelistedProfilesCommand(
            $entityManager,
            $registry,
            $this->blacklistRepository,
            $this->whitelistedProfileManager,
            $this->monitoringService,
            $logger,
            10,
            0
        );

        $this->commandTester = $this->createCommandTesterAndNewDatabaseCommon($command);
    }

    public function testCommandPublishJob(): void
    {
        $dowJonesBlacklist = new Blacklist('dow_jones', 'Dow Jones');
        $whiteListedProfile = (new WhitelistedProfile())->setBlacklist($dowJonesBlacklist);

        $this->blacklistRepository
            ->expects($this->once())
            ->method('findOneById')
            ->willReturn($dowJonesBlacklist)
        ;

        $this->whitelistedProfileRepository
            ->expects($this->exactly(2))
            ->method('findAllActiveByBlacklist')
            ->willReturnOnConsecutiveCalls([$whiteListedProfile], [])
        ;

        $this->monitoringService
            ->expects($this->once())
            ->method('storeSynchronizeWhitelistedProfilesData')
        ;

        $this->commandTester->execute(['blacklistId' => $dowJonesBlacklist->getId()]);

        $publishedJobs = count(array_filter(
            $this->publisher->getJobsPublished(),
            fn($job) => $job[0] === SynchronizeWhitelistedProfilesWorker::JOB_KEY
        ));

        $this->assertEquals(1, $publishedJobs);
    }
}
