<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use Evp\Bundle\BlacklistBundle\Command\BlacklistSelfTestCommand;
use Evp\Bundle\BlacklistBundle\Exception\ConfigurationNotFoundException;
use Evp\Bundle\BlacklistBundle\Service\BlacklistSelfCheck;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class BlacklistSelfTestCommandTest extends CommandTestCase
{
    /**
     * @var BlacklistSelfTestCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private MockObject $blacklistSelfCheck;
    public function setUp(): void
    {
        $this->blacklistSelfCheck = $this->createMock(BlacklistSelfCheck::class);
        $this->getContainer()->set('evp_blacklist.self_test', $this->blacklistSelfCheck);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.self_test'
        );
    }

    public function testExecute(): void
    {
        $configuration = $this->getContainer()->getParameter('evp_blacklist.blacklist_configuration_name_v1');

        $this->blacklistSelfCheck
            ->expects($this->once())
            ->method('test')
        ;

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'configuration' => $configuration,
        ]);
    }

    public function testExecuteWithWrongConfiguration(): void
    {
        $configuration = 'some_random_config';

        $this->expectException(ConfigurationNotFoundException::class);
        $this->expectExceptionMessage(sprintf('Invalid configuration provided: %s', $configuration));

        $this->blacklistSelfCheck
            ->expects($this->exactly(0))
            ->method('test')
        ;

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'configuration' => $configuration,
        ]);
    }
}
