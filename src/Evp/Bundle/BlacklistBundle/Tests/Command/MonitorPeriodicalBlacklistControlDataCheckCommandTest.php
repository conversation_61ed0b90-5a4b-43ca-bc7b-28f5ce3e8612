<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use DateInterval;
use DatePeriod;
use DateTime;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Command\MonitorPeriodicalBlacklistControlDataCheckCommand;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog;
use InfluxDB\Database as InfluxDatabase;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorPeriodicalBlacklistControlDataCheckCommandTest extends CommandTestCase
{
    private const STATIC_KEY = 'static_blacklist_key';
    private const EXTERNAL_KEY = 'external_blacklist_key';
    private const DEPRECATED_KEY = 'deprecated_blacklist_key';

    /** @var MonitorPeriodicalBlacklistControlDataCheckCommand */
    protected $command;

    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;

    /** @var MonitoringClient|MockObject */
    private $monitoringClient;

    public function setUp(): void
    {
        $this->monitoringClient = $this->createMock(MonitoringClient::class);
        $this->getContainer()->set('paysera_monitoring.monitoring_client', $this->monitoringClient);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.monitor_periodical_blacklist_control_data_check'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /** @dataProvider processDataProvider */
    public function testProcess(array $expectedMonitoringClientParameters, array $blacklistProfileCheckLogsData): void
    {
        /** @var Blacklist[] $blacklists */
        $blacklists = [
            self::STATIC_KEY => $this->fixturesHelper->createBlacklist('S', self::STATIC_KEY),
            self::EXTERNAL_KEY => $this->fixturesHelper->createBlacklist('E', self::EXTERNAL_KEY),
            self::DEPRECATED_KEY => $this->fixturesHelper->createBlacklist('D', self::DEPRECATED_KEY),
        ];
        $blacklists[self::EXTERNAL_KEY]->setExternal(true);
        $blacklists[self::DEPRECATED_KEY]->setDeprecated(true);

        foreach ($blacklistProfileCheckLogsData as $blacklistKey => $items) {
            foreach ($items as $item) {
                $this->fixturesHelper->createBlacklistProfileDataCheckLog(
                    $blacklists[$blacklistKey],
                    $item['finishedAt'] ?? new DateTime(),
                    $item['total'],
                    $item['matched'],
                    'ABCDE',
                    $item['type'] ?? BlacklistProfileDataCheckLog::TYPE_AUTOMATIC,
                );
            }
        }

        $this->entityManager->flush();

        $preparedMonitoringClientData = [];
        foreach ($expectedMonitoringClientParameters as $blacklistKey => $fields) {
            foreach ($fields as $field => $value) {
                $preparedMonitoringClientData[] = $this->getMonitoringClientParameters($value, $field, $blacklistKey);
            }
        }

        $this->monitoringClient
            ->expects($this->exactly(count($preparedMonitoringClientData)))
            ->method('writeValue')
            ->withConsecutive(...$preparedMonitoringClientData)
        ;

        $this->commandTester->execute(['command' => $this->command->getName(), '--today' => true]);
    }

    public function processDataProvider(): array
    {
        return [
            'Static and external' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 32, 'matched' => 15, 'not_matched' => 17, 'match_percentage' => 46.87],
                    self::EXTERNAL_KEY => ['total' => 20, 'matched' => 12, 'not_matched' => 8, 'match_percentage' => 60.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::STATIC_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 7],
                        ['total' => 8, 'matched' => 3],
                        ['total' => 4, 'matched' => 0],
                    ],
                    self::EXTERNAL_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 7],
                    ],
                ],
            ],
            'No data' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [],
             ],
            'Deprecated blacklist data only' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::DEPRECATED_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 5],
                    ],
                ],
            ],
            'Deprecated blacklist and one static' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 20, 'matched' => 10, 'not_matched' => 10, 'match_percentage' => 50.0],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::DEPRECATED_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 5],
                    ],
                    self::STATIC_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 5],
                    ],
                ],
            ],
            'Only external' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                    self::EXTERNAL_KEY => ['total' => 30, 'matched' => 22, 'not_matched' => 8, 'match_percentage' => 73.33],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::EXTERNAL_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 7],
                        ['total' => 10, 'matched' => 10],
                        ['total' => 10, 'matched' => 10, 'type' => BlacklistProfileDataCheckLog::TYPE_MANUAL],
                    ],
                ],
            ],
            'Only external but data is zeroes' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                    self::EXTERNAL_KEY => ['total' => 30, 'matched' => 0, 'not_matched' => 30, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::EXTERNAL_KEY => [
                        ['total' => 10, 'matched' => 0],
                        ['total' => 10, 'matched' => 0],
                        ['total' => 10, 'matched' => 0],
                        ['total' => 10, 'matched' => 0, 'type' => BlacklistProfileDataCheckLog::TYPE_MANUAL],
                    ],
                ],
            ],
            'Only static' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 20, 'matched' => 12, 'not_matched' => 8, 'match_percentage' => 60.0],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::STATIC_KEY => [
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 7],
                    ],
                ],
            ],
            'Only static but data is zeroes' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 20, 'matched' => 0, 'not_matched' => 20, 'match_percentage' => 0.0],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::STATIC_KEY => [
                        ['total' => 10, 'matched' => 0],
                        ['total' => 10, 'matched' => 0],
                    ],
                ],
            ],
            'Static not today logs exist' => [
                'expectedMonitoringClientParameters' => [
                    self::STATIC_KEY => ['total' => 20, 'matched' => 12, 'not_matched' => 8, 'match_percentage' => 60],
                    self::EXTERNAL_KEY => ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0],
                ],
                'blacklistProfileCheckLogsData' => [
                    self::STATIC_KEY => [
                        ['total' => 10, 'matched' => 7, 'finishedAt' => new DateTime('-2 days')],
                        ['total' => 10, 'matched' => 5],
                        ['total' => 10, 'matched' => 7],
                        ['total' => 10, 'matched' => 7, 'finishedAt' => new DateTime('-1 day')],
                        ['total' => 10, 'matched' => 7, 'type' => BlacklistProfileDataCheckLog::TYPE_CLI],
                    ],
                ],
            ],
        ];
    }

    public function testProcessNotTodayWithoutData(): void
    {
        $this->fixturesHelper->createBlacklist('D', self::DEPRECATED_KEY)->setDeprecated(true);
        $staticBlacklist = $this->fixturesHelper->createBlacklist('S', self::STATIC_KEY);
        $externalBlacklist = $this->fixturesHelper->createBlacklist('E', self::EXTERNAL_KEY)
            ->setExternal(true);

        $this->entityManager->flush();

        /** @var Blacklist[] $blacklists */
        $blacklists = [$staticBlacklist, $externalBlacklist];
        $initialValues = ['total' => 0, 'matched' => 0, 'not_matched' => 0, 'match_percentage' => 0.0];
        $datePeriod = new DatePeriod(
            (new DateTime('tomorrow midnight'))->sub(new DateInterval('P31D')),
            new DateInterval('P1D'),
            new DateTime()
        );

        $preparedMonitoringClientData = [];

        foreach ($blacklists as $blacklist) {
            /** @var DateTime $date */
            foreach ($datePeriod as $date) {
                foreach ($initialValues as $field => $value) {
                    $preparedMonitoringClientData[] = $this->getMonitoringClientParameters(
                        $value,
                        $field,
                        $blacklist->getKey(),
                        $date->format('Y-m-d')
                    );
                }
            }
        }

        $this->monitoringClient
            ->expects($this->exactly(count($preparedMonitoringClientData)))
            ->method('writeValue')
            ->withConsecutive(...$preparedMonitoringClientData)
        ;

        $this->commandTester->execute(['command' => $this->command->getName()]);
    }

    private function getMonitoringClientParameters(
        float $value,
        string $field,
        string $blacklistKey,
        string $date = null
    ): array {
        return [
            'evpbank.blacklist',
            $value,
            [
                'problem' => 'blacklist_control_data_check_statistic',
                'field' => $field,
                'blacklist' => $blacklistKey,
            ],
            [],
            InfluxDatabase::PRECISION_SECONDS,
            (new DateTime($date ?? 'today midnight'))->getTimestamp(),
        ];
    }
}
