<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Tests\Fixtures\FixturesHelper;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Component\Time\Psr20ClockAdapter;
use Evp\Component\Time\StaticClock;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class UpdateClientBlacklistCommandTest extends CommandTestCase
{
    private MockObject $remoteJobPublisher;
    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;

    public function setUp(): void
    {
        $this->remoteJobPublisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set('evp_rabbit_mq_extension.remote_job_publisher', $this->remoteJobPublisher);
        $this->getContainer()->set('evp.component.time.clock',
            new Psr20ClockAdapter(new StaticClock(new DateTime('2023-11-25 12:37:39')))
        );

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.update_client_blacklist'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testNeverCheckedClients(): void
    {
        $this->prepareNeverCheckedClients();

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                ['clients' => ['6']]
            );

        $this->commandTester->execute([]);
    }

    private function prepareNeverCheckedClients(): void
    {
        # client without covenanteeId
        $client1 = $this->fixturesHelper->createClient(null, 'LT1');
        # client without code
        $client2 = $this->fixturesHelper->createClient(2, null);
        # client without bank account
        $client3 = $this->fixturesHelper->createClient(3, 'LT3');
        # client with inactive bank account
        $client4 = $this->fixturesHelper->createClient(4, 'LT4');
        $this->fixturesHelper->createBankAccount($client4, false);
        # already checked client
        $client5 = $this->fixturesHelper->createClient(5, 'LT5');
        $this->fixturesHelper->createBankAccount($client5, true);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client5);
        # client with active bank account
        $client6 = $this->fixturesHelper->createClient(6, 'LT6');
        $this->fixturesHelper->createBankAccount($client6, true);

        $this->entityManager->flush();
    }

    public function testNaturalClients(): void
    {
        $this->prepareNaturalClients();

        $this->remoteJobPublisher->expects($this->exactly(2))->method('publishJob')->withConsecutive(
            [
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                ['clients' => ['6']],
            ],
            [
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                ['clients' => ['7']],
            ]
        );

        $this->commandTester->execute([]);
    }

    private function prepareNaturalClients(): void
    {
        # client without code
        $client1 = $this->fixturesHelper->createClient(1, null);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client1);
        # client with too low identification level
        $client2 = $this->fixturesHelper->createClient(2, 'LT2');
        $client2->setLevel(IdentificationLevels::SEMI_IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client2);
        # client was checked recently
        $client3 = $this->fixturesHelper->createClient(3, 'LT3');
        $client3->setLevel(IdentificationLevels::IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client3);
        # client without covenanteeId
        $client4 = $this->fixturesHelper->createClient(null, 'LT4');
        $client4->setLevel(IdentificationLevels::FULLY_IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client4, '2023-09-25 12:37:39');
        # client without active account
        $client5 = $this->fixturesHelper->createClient(5, 'LT5');
        $client5->setLevel(IdentificationLevels::FULLY_IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client5, '2023-09-25 12:37:39');
        $this->fixturesHelper->createBankAccount($client5, false);
        # clients with active account
        $client6 = $this->fixturesHelper->createClient(6, 'LT6');
        $client6->setLevel(IdentificationLevels::IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client6, '2023-09-25 12:37:39');
        $this->fixturesHelper->createBankAccount($client6, true);
        $client7 = $this->fixturesHelper->createClient(7, 'LT7');
        $client7->setLevel(IdentificationLevels::FULLY_IDENTIFIED);
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client7, '2023-09-25 12:37:39');
        $this->fixturesHelper->createBankAccount($client7, true);

        $this->entityManager->flush();
    }

    public function testLegalClients(): void
    {
        $this->prepareLegalClients();

        $this->remoteJobPublisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                ['clients' => ['4']]
            );

        $this->commandTester->execute([]);
    }

    private function prepareLegalClients(): void
    {
        # client was checked recently
        $client1 = $this->fixturesHelper->createLegalClient(1, 'LT1');
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client1);
        # client without covenanteeId
        $client2 = $this->fixturesHelper->createLegalClient(null, 'LT2');
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client2, '2023-09-25 12:37:39');
        # client without active account
        $client3 = $this->fixturesHelper->createLegalClient(3, 'LT3');
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client3, '2023-09-25 12:37:39');
        $this->fixturesHelper->createBankAccount($client3, false);
        # client with active account
        $client4 = $this->fixturesHelper->createLegalClient(4, 'LT4');
        $this->fixturesHelper->createBlacklistCheckResultWithoutMatch($client4, '2023-09-25 12:37:39');
        $this->fixturesHelper->createBankAccount($client4, true);

        $this->entityManager->flush();
    }
}
