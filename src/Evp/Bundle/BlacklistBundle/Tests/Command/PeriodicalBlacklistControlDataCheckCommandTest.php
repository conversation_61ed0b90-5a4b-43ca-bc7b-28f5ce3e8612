<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Command\PeriodicalBlacklistControlDataCheckCommand;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistProfileDataCheckLogRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckManager;
use Exception;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use ReflectionClass;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Tester\CommandTester;

class PeriodicalBlacklistControlDataCheckCommandTest extends CommandTestCase
{
    /**
     * @var PeriodicalBlacklistControlDataCheckCommand
     */
    protected $command;

    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private BlacklistProfileDataCheckLogRepository $blacklistProfileDataCheckLogRepository;

    private vfsStreamDirectory $vfsStreamDirectory;

    /**
     * @var BlacklistManager|MockObject
     */
    private $blacklistManager;

    public function setUp(): void
    {
        $this->vfsStreamDirectory = vfsStream::setup();

        $this->blacklistManager = $this->createMock(BlacklistManager::class);
        $this->getContainer()->set('evp_blacklist.manager', $this->blacklistManager);
        $this->getContainer()->set(
            'evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager',
            new PeriodicalBlacklistControlDataCheckManager(
                $this->getContainer()->get('evp_blacklist.manager'),
                $this->getContainer()->get('paysera.csv_writer'),
                $this->getContainer()->get('evp_blacklist.symbols_hash_generator'),
                $this->getContainer()->get('evp_blacklist.configuration_manager'),
                $this->vfsStreamDirectory->url()
            )
        );
        $this->getContainer()->set(
            'evp_blacklist.command.periodical_blacklist_control_data_check',
            new PeriodicalBlacklistControlDataCheckCommand(
                $this->getContainer()->get('evp_blacklist.repository.blacklist'),
                $this->getContainer()->get('evp_blacklist.repository.blacklist_profile'),
                $this->getContainer()->get('doctrine.orm.entity_manager'),
                $this->getContainer()->get('doctrine'),
                $this->getContainer()->get('paysera.console_progress_bar_helper'),
                $this->getContainer()->get('evp_blacklist.periodical_check.periodical_blacklist_control_data_check_manager'),
                $this->getContainer()->get('evp_blacklist.configuration_manager'),
                $this->getContainer()->get('logger'),
                5,
                0
            )
        );

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.periodical_blacklist_control_data_check'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->blacklistProfileDataCheckLogRepository = $this->getContainer()->get(
            'evp_blacklist.repository.blacklist_profile_data_check_log'
        );
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param array $initialProfilesData
     * @param array $blacklistResultsData
     * @param BlacklistProfileDataCheckLog $expectedDataCheckLog
     * @param array $commandParameters
     *
     * @dataProvider processDataProvider
     */
    public function testProcess(
        array $initialProfilesData,
        array $blacklistResultsData,
        BlacklistProfileDataCheckLog $expectedDataCheckLog,
        array $commandParameters
    ): void {
        $this->prepareProfiles($initialProfilesData);

        if (count($blacklistResultsData) > 0) {
            $this->blacklistManager
                ->expects($this->exactly(count($blacklistResultsData)))
                ->method('getProfileSearchResults')
                ->willReturnOnConsecutiveCalls(...$blacklistResultsData)
            ;
        } else {
            $this->blacklistManager
                ->expects($this->never())
                ->method('getProfileSearchResults')
            ;
        }

        $this->assertCount(0, $this->blacklistProfileDataCheckLogRepository->findAll());

        $this->commandTester->execute(
            array_merge(
                [
                    'command' => $this->command->getName(),
                ],
                $commandParameters
            )
        );

        if (count($blacklistResultsData) === 0) {
            $this->assertCount(0, $this->blacklistProfileDataCheckLogRepository->findAll());

            return;
        }

        $this->assertCount(1, $this->blacklistProfileDataCheckLogRepository->findAll());

        /** @var BlacklistProfileDataCheckLog $actualDataCheckLog */
        $actualDataCheckLog = $this->blacklistProfileDataCheckLogRepository->findAll()[0];

        $this->assertEquals(
            $expectedDataCheckLog->getMatchedItemsCount(),
            $actualDataCheckLog->getMatchedItemsCount()
        );
        $this->assertEquals(
            $expectedDataCheckLog->getItemsCount(),
            $actualDataCheckLog->getItemsCount()
        );
        $this->assertTrue(
            file_exists(
                sprintf(
                    '%s/%s_%s.csv',
                    $this->vfsStreamDirectory->url(),
                    $actualDataCheckLog->getStartedAt()->format('Y-m-d'),
                    $actualDataCheckLog->getReference()
                )
            )
        );
    }

    public function processDataProvider(): array
    {
        return [
            'Processing only DJ profiles (dow_jones_1)(3/3)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(3),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                ],
            ],
            'Processing only DJ profiles (dow_jones_1)(2/0)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                ],
                [
                    [],
                    [],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(2)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                ],
            ],
            'Processing only DJ profiles (dow_jones_1 --incrementVersion=77777772200)(0/0)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                ],
                [],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(0)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                    '--incrementVersion' => '77777772200'
                ],
            ],
            'Processing only DJ profiles (dow_jones_1 --incrementVersion=12345672200)(5/2)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                    [],
                    [],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(5)
                    ->setMatchedItemsCount(2),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                    '--incrementVersion' => '12345672200',
                ],
            ],
            'Processing only DJ profiles (dow_jones_1 --incrementVersion=12345672200 --profilesChunkSize=2)(5/5)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 5')
                                    ->setExternalId('555')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 4')
                                    ->setExternalId('444')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(5)
                    ->setMatchedItemsCount(5),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                    '--incrementVersion' => '12345672200',
                    '--profilesChunkSize' => '2',
                ],
            ],
            'Processing only DJ profiles, resolving latest version (dow_jones_1)(1/1)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(1)
                    ->setMatchedItemsCount(1),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                ],
            ],
            'Processing only DJ profiles, resolving latest version (latest version incorrect) (dow_jones_1)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222222',
                        'externalId' => '777',
                    ],
                ],
                [],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(0)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                ],
            ],
            'Processing only DJ profiles (provided version incorrect) (dow_jones_1 --incrementVersion=12345672222)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222222',
                        'externalId' => '777',
                    ],
                ],
                [],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(0)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                    '--incrementVersion' => '22222222222',
                ],
            ],
            'Processing only DJ profiles, but only paysera sanction list exists (dow_jones_1)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                ],
                [],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(0)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                ],
            ],
            'Processing only Paysera profiles, but only dj sanction list exists (paysera_v1)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '123',
                        'externalId' => '123',
                    ],
                ],
                [],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(0)
                    ->setMatchedItemsCount(0),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                ],
            ],
            'Processing Paysera profiles (paysera_v1)(3/3)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(3),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                ],
            ],
            'Processing Paysera profiles (paysera_v1 --incrementVersion=1231232200)(3/2)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(2),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                ],
            ],
            'Processing Paysera profiles (paysera_v1 --incrementVersion=12345672200)(3/2)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_PAYSERA_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_PAYSERA_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(2),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                    '--incrementVersion' => '12345672200'
                ],
            ],
            'Processing Paysera profiles (paysera_v1 --profilesChunkSize=2)(3/3)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(3),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                    '--profilesChunkSize' => '2'
                ],
            ],
            'Processing Paysera profiles (paysera_v1 --profilesChunkSize=3)(3/3)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(3),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                    '--profilesChunkSize' => '3'
                ],
            ],
            'Processing Paysera profiles (paysera_v1 --profilesChunkSize=10)(3/3)' => [
                [
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '111',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '222',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '333',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '444',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                        'version' => null,
                        'externalId' => null,
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '12345672200',
                        'externalId' => '555',
                    ],
                    [
                        'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                        'version' => '22222222200',
                        'externalId' => '777',
                    ],
                ],
                [
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 1')
                                    ->setExternalId('111')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 2')
                                    ->setExternalId('222')
                            ),
                    ],
                    [
                        (new BlacklistResult())
                            ->setFoundProfile(
                                (new Profile())
                                    ->setBlacklist(new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'))
                                    ->setName('Matched name 3')
                                    ->setExternalId('333')
                            ),
                    ],
                ],
                (new BlacklistProfileDataCheckLog())
                    ->setItemsCount(3)
                    ->setMatchedItemsCount(3),
                [
                    'blacklistKey' => Blacklist::KEY_PAYSERA_V1,
                    '--profilesChunkSize' => '10'
                ],
            ],
        ];
    }

    /** @dataProvider resolveVersionDataProvider */
    public function testResolveVersion(
        ?string $expectedException,
        ?string $expectedResult,
        ?string $blacklistKey,
        bool $isExternal,
        ?string $version,
        string $lastProfileVersion
    ): void {
        // TODO: Cover repository outputs
        // TODO: Cover all exceptions

        $blacklist = $this->fixturesHelper->createBlacklist($blacklistKey ?? '', $blacklistKey ?? '')
            ->setExternal($isExternal);
        $this->fixturesHelper->createPersonProfile($blacklist)->setVersion($lastProfileVersion);
        $this->entityManager->flush();

        $input = $this->createMock(InputInterface::class);
        $input->expects($this->once())->method('getArgument')->with('blacklistKey')->willReturn($blacklistKey);
        $input->expects($this->once())->method('getOption')->with('incrementVersion')->willReturn($version);

        $dataCheckCommand = new ReflectionClass($this->command);
        $resolveVersionMethod = $dataCheckCommand->getMethod('resolveVersion');
        $resolveVersionMethod->setAccessible(true);

        $result = null;
        $caughtException = null;

        try {
            $result = $resolveVersionMethod->invokeArgs($this->command, [$blacklist, $input]);
        } catch (Exception $exception) {
            $caughtException = $exception;
        }

        if ($expectedException !== null) {
            $this->assertNotNull($caughtException);
            $this->assertSame($expectedException, $caughtException->getMessage());
        } else {
            $this->assertNull($caughtException);
            $this->assertSame($expectedResult, $result);
        }
    }

    public function resolveVersionDataProvider(): array
    {
        return [
            'Single DowJones V1 without version' => [
                'expectedException' => null,
                'expectedResult' => '12345672200',
                'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                'isExternal' => true,
                'version' => null,
                'lastProfileVersion' => '12345672200',
            ],
            'Single DowJones V1 without version last profile with wrong version' => [
                'expectedException' => 'Cannot find latest version for external blacklist!',
                'expectedResult' => null,
                'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                'isExternal' => true,
                'version' => null,
                'lastProfileVersion' => '99999999999',
            ],
            'Single DowJones V1 with correct version' => [
                'expectedException' => null,
                'expectedResult' => '99999992200',
                'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                'isExternal' => true,
                'version' => '99999992200',
                'lastProfileVersion' => '12345672200',
            ],
            'Single DowJones V1 with incorrect version' => [
                'expectedException' => 'Incorrect version provided!',
                'expectedResult' => null,
                'blacklistKey' => Blacklist::KEY_DOW_JONES_V1,
                'isExternal' => true,
                'version' => '99999999999',
                'lastProfileVersion' => '12345672200',
            ],
            'Single other external without version' => [
                'expectedException' => null,
                'expectedResult' => '12345672200',
                'blacklistKey' => 'new external',
                'isExternal' => true,
                'version' => null,
                'lastProfileVersion' => '12345672200',
            ],
            'Single other external without version last profile with wrong version' => [
                'expectedException' => null,
                'expectedResult' => '99999999999',
                'blacklistKey' => 'new external',
                'isExternal' => true,
                'version' => null,
                'lastProfileVersion' => '99999999999',
            ],
            'Single other external with correct version' => [
                'expectedException' => null,
                'expectedResult' => '99999992200',
                'blacklistKey' => 'new external',
                'isExternal' => true,
                'version' => '99999992200',
                'lastProfileVersion' => '12345672200',
            ],
            'Single other external with incorrect version' => [
                'expectedException' => null,
                'expectedResult' => '99999999999',
                'blacklistKey' => 'new external',
                'isExternal' => true,
                'version' => '99999999999',
                'lastProfileVersion' => '12345672200',
            ],
            'Single static without version' => [
                'expectedException' => null,
                'expectedResult' => null,
                'blacklistKey' => 'new static',
                'isExternal' => false,
                'version' => null,
                'lastProfileVersion' => '',
            ],
            'Single static without version profile has version' => [
                'expectedException' => null,
                'expectedResult' => null,
                'blacklistKey' => 'new static',
                'isExternal' => false,
                'version' => null,
                'lastProfileVersion' => '12345672200',
            ],
            'Single static with version' => [
                'expectedException' => null,
                'expectedResult' => '99999999999',
                'blacklistKey' => 'new static',
                'isExternal' => false,
                'version' => '99999999999',
                'lastProfileVersion' => '',
            ],
            'Single static with version profile has version' => [
                'expectedException' => null,
                'expectedResult' => '99999999999',
                'blacklistKey' => 'new static',
                'isExternal' => false,
                'version' => '99999999999',
                'lastProfileVersion' => '12345672200',
            ],
            'No keys. Static with version' => [
                'expectedException' => null,
                'expectedResult' => null,
                'blacklistKey' => null,
                'isExternal' => false,
                'version' => '99999999999',
                'lastProfileVersion' => '12345672200',
            ],
            'No keys. Static without version' => [
                'expectedException' => null,
                'expectedResult' => null,
                'blacklistKey' => null,
                'isExternal' => false,
                'version' => null,
                'lastProfileVersion' => '12345672200',
            ],
            'No keys. External with version' => [
                'expectedException' => null,
                'expectedResult' => '99999999999',
                'blacklistKey' => null,
                'isExternal' => true,
                'version' => '99999999999',
                'lastProfileVersion' => '12345672200',
            ],
            'No keys. External without version' => [
                'expectedException' => null,
                'expectedResult' => '12345672200',
                'blacklistKey' => null,
                'isExternal' => true,
                'version' => null,
                'lastProfileVersion' => '12345672200',
            ],
        ];
    }

    private function prepareProfiles(
        array $initialProfilesData
    ): void {
        $dowJonesBlacklist = $this->fixturesHelper->createBlacklist('DJ')->setExternal(true);
        $payseraBlacklist = $this->fixturesHelper->createBlacklist('Paysera', Blacklist::KEY_PAYSERA_V1);
        $category = $this->fixturesHelper->createCategory();

        foreach ($initialProfilesData as $initialProfileData) {
            $this->fixturesHelper->createPersonProfile(
                $initialProfileData['blacklistKey'] === Blacklist::KEY_DOW_JONES_V1
                    ? $dowJonesBlacklist
                    : $payseraBlacklist,
                'Name',
                'Surname',
                'lt',
                $initialProfileData['externalId'],
                Profile::TYPE_PERSON,
                $initialProfileData['version'],
                $category
            );
        }

        $this->entityManager->flush();
    }
}
