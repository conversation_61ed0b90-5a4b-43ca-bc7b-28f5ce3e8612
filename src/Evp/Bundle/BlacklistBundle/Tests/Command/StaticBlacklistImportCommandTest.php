<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\Command;

use Evp\Bundle\BlacklistBundle\Command\StaticBlacklistImportCommand;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\StaticBlacklistImporter;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class StaticBlacklistImportCommandTest extends CommandTestCase
{
    /**
     * @var StaticBlacklistImportCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private MockObject $staticBlacklistImporter;
    private MockObject $blacklistRepository;

    public function setUp(): void
    {
        $this->staticBlacklistImporter = $this->createMock(StaticBlacklistImporter::class);
        $this->blacklistRepository = $this->createMock(BlacklistRepository::class);

        $this->getContainer()->set('evp_blacklist.static_blacklist_importer', $this->staticBlacklistImporter);
        $this->getContainer()->set('evp_blacklist.repository.blacklist', $this->blacklistRepository);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_blacklist.command.static_blacklist_import'
        );
    }

    public function testExecute(): void
    {
        $blacklists = [
            new Blacklist(Blacklist::KEY_DOW_JONES_V1, '123'),
            new Blacklist(Blacklist::KEY_PAYSERA_LT, '1234'),
        ];

        $this->blacklistRepository
            ->expects($this->once())
            ->method('findNotExternalAndNotDeprecated')
            ->willReturn($blacklists)
        ;

        $this->staticBlacklistImporter
            ->expects($this->exactly(count($blacklists)))
            ->method('process')
        ;

        $this->commandTester->execute(['command' => $this->command->getName()]);
    }
}
