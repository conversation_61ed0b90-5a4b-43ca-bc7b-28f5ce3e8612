<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\EndToEnd;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Persistence\ObjectManager;
use Evp\Bundle\BlacklistBundle\Entity\AlternativeName;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\CategoryRepository;
use Faker\Factory;
use Faker\Generator;

class ProfilesFixtureFactory
{
    private Generator $faker;
    private ObjectManager $manager;
    private CategoryRepository $categoryRepository;

    public function __construct(
        ObjectManager $manager,
        int $seed = 1234
    ) {
        $this->manager = $manager;
        $this->faker = Factory::create();

        /** @var CategoryRepository $categoryRepository */
        $categoryRepository = $this->manager->getRepository(Category::class);
        $this->categoryRepository = $categoryRepository;

        $this->faker->seed($seed);
    }


    public function createCategories()
    {
        foreach ($this->getCategories() as $categoryItem) {
            $category = new Category();
            $categoryGroup = $this->getCategoryGroups()[$categoryItem['group']];
            $this->manager->persist($categoryGroup);
            $category->setParent(null)
                ->setName($categoryItem['name'])
                ->setDepth($categoryItem['depth'])
                ->setExternalId($categoryItem['external_id'])
                ->setGroup($categoryGroup)
            ;

            $this->manager->persist($category);
        }
        $this->manager->flush();
    }

    public function createProfiles(
        $key = 'dow_jones',
        $name = 'Dow Jones'
    ) {

        $version = intval((new DateTime())->format('YmdHi'));

        $blacklist = new Blacklist($key, $name);

        // @phpstan-ignore-next-line
        $blacklist->setVersion($version);

        foreach ($this->getProfiles() as $profileItem) {
            /** @var Category $category */
            $category = $this->categoryRepository->findOneByDepthAndExternalId(
                $profileItem['category_depth'],
                $profileItem['category_external_id']
            );

            $profile = new Profile();
            $profile->setType($profileItem['type'])
                ->setFirstName($profileItem['first_name'])
                ->setLastName($profileItem['last_name'])
                ->setName($profileItem['name'])
                ->setCountry($profileItem['country'])
                ->setBirthday($profileItem['birthday'])
                ->setCode($profileItem['code'])
                ->setExternalId($profileItem['external_id'])
                ->setCategories([$category])
                ->setBlacklist($blacklist)
                ->setVersion($blacklist->getVersion())
                ->setAlternativeNames(
                    $this->getAlternativeNames(
                        $profileItem['alternative_names'],
                        $profile
                    )
                );

            $this->manager->persist($profile);
        }
        $this->manager->persist($blacklist);
        $this->manager->flush();
    }

    private function getAlternativeNames(array $alternativeNames, Profile $profile): ArrayCollection
    {
        $result = new ArrayCollection();

        if ((!isset($alternativeNames[0])) || count($alternativeNames[0]) === 0) {
            return $result;
        }

        foreach ($alternativeNames as $alternativeNameItem) {
            if (
                !isset($alternativeNameItem['name'])
                && !isset($alternativeNameItem['first_name'])
                && !isset($alternativeNameItem['last_name'])
            ) {
                continue;
            }

            $alternativeName = (new AlternativeName())
                ->setProfile($profile)
            ;

            if (isset($alternativeNameItem['name'])) {
                $alternativeName->setName($alternativeNameItem['name']);
            }

            if (isset($alternativeNameItem['first_name'])) {
                $alternativeName->setFirstName($alternativeNameItem['first_name']);
            }

            if (isset($alternativeNameItem['last_name'])) {
                $alternativeName->setLastName($alternativeNameItem['last_name']);
            }

            $result->add($alternativeName);
            $this->manager->persist($alternativeName);
        }


        return $result;
    }

    private function getCategoryGroups(): array
    {
        return [
            'san' => (new CategoryGroup())
                ->setKey('san')
                ->setName('Sanctions Lists (SAN)')
                ->setPriority(1),
            'sor' => (new CategoryGroup())
                ->setKey('sor')
                ->setName('Sanctions Ownership Research (SOR)')
                ->setPriority(2),
        ];
    }

    private function getCategories(): array
    {
        return [
            [
                'id' => 282,
                'parent_id' => null,
                'name' => 'Politically Exposed Person (PEP)',
                'depth' => 1,
                'external_id' => 1,
                'group' => 'san',
            ],
            [
                'id' => 285,
                'parent_id' => null,
                'name' => 'Special Interest Person (SIP)',
                'depth' => 1,
                'external_id' => 3,
                'group' => 'san',
            ],
            [
                'id' => 298,
                'parent_id' => 285,
                'name' => 'Organised Crime Japan',
                'depth' => 2,
                'external_id' => 21,
                'group' => 'san',
            ],
            [
                'id' => 320,
                'parent_id' => 285,
                'name' => 'Tax Crime',
                'depth' => 2,
                'external_id' => 31,
                'group' => 'san',
            ],
            [
                'id' => 322,
                'parent_id' => null,
                'name' => 'Special Interest Entity (SIE)',
                'depth' => 1,
                'external_id' => 4,
                'group' => 'san',
            ],
            [
                'id' => 358,
                'parent_id' => 322,
                'name' => 'Sanctions Control and Ownership',
                'depth' => 2,
                'external_id' => 34,
                'group' => 'san',
            ],
            [
                'id' => 317,
                'parent_id' => 298,
                'name' => 'Other',
                'depth' => 3,
                'external_id' => 46,
                'group' => 'san',
            ],
            [
                'id' => 321,
                'parent_id' => 320,
                'name' => 'Lower Threshold',
                'depth' => 3,
                'external_id' => 83,
                'group' => 'san',
            ],
            [
                'id' => 323,
                'parent_id' => 322,
                'name' => 'Sanctions Lists',
                'depth' => 2,
                'external_id' => 3,
                'group' => 'san',
            ],
            [
                'id' => 284,
                'parent_id' => null,
                'name' => 'Relative or Close Associate (RCA)',
                'depth' => 1,
                'external_id' => 2,
                'group' => 'san',
            ],
            [
                'id' => 345,
                'parent_id' => 322,
                'name' => 'Terror',
                'depth' => 2,
                'external_id' => 12,
                'group' => 'san',
            ],
            [
                'id' => 334,
                'parent_id' => 322,
                'name' => 'Other Official Lists',
                'depth' => 2,
                'external_id' => 4,
                'group' => 'sor',
            ],
            [
                'id' => 359,
                'parent_id' => 358,
                'name' => 'OFAC Related - Majority Owned',
                'depth' => 3,
                'external_id' => 92,
                'group' => 'sor',
            ],
            [
                'id' => 360,
                'parent_id' => 358,
                'name' => 'OFAC Related - Minority Owned',
                'depth' => 3,
                'external_id' => 93,
                'group' => 'sor',
            ],
        ];
    }

    private function getProfiles(): array
    {
        return [
            [
                'type' => 'person',
                'first_name' => 'Kristina',
                'last_name' => 'Tutic',
                'name' => null,
                'country' => null,
                'birthday' => null,
                'code' => null,
                'category_id' => '334',
                'category_depth' => '2',
                'category_external_id' => '4',
                'external_id' => '11984848',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Younes',
                'last_name' => 'Dahhaky',
                'name' => null,
                'country' => 'ma',
                'birthday' => '1982-01-08',
                'code' => null,
                'category_id' => '345',
                'category_depth' => '2',
                'category_external_id' => '12',
                'external_id' => '1453921',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Tawfiq',
                'last_name' => 'Younes',
                'name' => null,
                'country' => 'ma',
                'birthday' => '1982-02-01',
                'code' => null,
                'category_id' => '345',
                'category_depth' => '2',
                'category_external_id' => '12',
                'external_id' => '1453921888',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Kotryna',
                'last_name' => 'Dulkytė',
                'name' => null,
                'country' => 'lt',
                'birthday' => null,
                'code' => null,
                'category_id' => '284',
                'category_depth' => '1',
                'category_external_id' => '2',
                'external_id' => '12203556',
                'alternative_names' => [
                    [
                        'first_name' => 'Kotryna',
                        'last_name' => 'Dulkyte',
                    ],
                ],
            ],
            [
                'type' => 'person',
                'first_name' => 'Anwar',
                'last_name' => 'Abdullah',
                'name' => null,
                'country' => 'us',
                'birthday' => '1988-03-01',
                'code' => null,
                'category_id' => '321',
                'category_depth' => '3',
                'category_external_id' => '83',
                'external_id' => '12162118',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Tan Allen Piguet Asset Management',
                'country' => 'cn',
                'birthday' => null,
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '12241516',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Rostelecom',
                'country' => 'cn',
                'birthday' => null,
                'code' => '1027700198767',
                'category_id' => '360',
                'category_depth' => '3',
                'category_external_id' => '93',
                'external_id' => '1053182',
                'alternative_names' => [
                    [
                        'name' => 'ПАО Ростелеком',
                    ],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'GPB Neftegaz Services',
                'country' => null,
                'birthday' => null,
                'code' => '34285355',
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '4316851',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Gazprom Media Holding',
                'country' => 'ru',
                'birthday' => null,
                'code' => '5087746018960',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '1045112',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Gražvydas',
                'last_name' => 'Liubinas',
                'name' => null,
                'country' => 'lt',
                'birthday' => null,
                'code' => null,
                'category_id' => '282',
                'category_depth' => '1',
                'category_external_id' => '1',
                'external_id' => '11347048',
                'alternative_names' => [
                    [
                        'first_name' => 'Grazvydas',
                        'last_name' => 'Liubinas',
                    ]
                ],
            ],
            [
                'type' => 'person',
                'first_name' => 'Mohamed',
                'last_name' => 'Talbi',
                'name' => null,
                'country' => 'ma',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '4799318',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Hugo Dante',
                'last_name' => 'Grajales Cantú',
                'name' => null,
                'country' => 'mx',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '1325335',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Saâd Abdallah',
                'last_name' => 'Djaballah',
                'name' => null,
                'country' => 'dz',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '191704',
                'alternative_names' => [
                    [
                        'first_name' => 'Abdallah',
                        'last_name' => 'Djaballah',
                    ],
                ],
            ],
            [
                'type' => 'person',
                'first_name' => 'Jinming',
                'last_name' => 'Ou',
                'name' => null,
                'country' => 'cn',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '1738133',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Maxima',
                'last_name' => 'Tanner',
                'name' => null,
                'country' => 'at',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '12055591',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Nikolaos',
                'last_name' => 'Tsourakis',
                'name' => null,
                'country' => 'at',
                'birthday' => '1970-11-02',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '1322567',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Anatolii',
                'last_name' => 'Serhiiovych Matviienko',
                'name' => null,
                'country' => 'ua',
                'birthday' => '1953-03-22',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '124936',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Dalia',
                'last_name' => 'Bajerčiūtė',
                'name' => null,
                'country' => 'lt',
                'birthday' => '1956-01-07',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '175445',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'MONIN',
                'last_name' => 'SERGEI ALEKSANDROVICH',
                'name' => null,
                'country' => 'lt',
                'birthday' => '1956-01-07',
                'code' => null,
                'category_id' => '317',
                'category_depth' => '3',
                'category_external_id' => '46',
                'external_id' => '175445748',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Stuart',
                'last_name' => 'Towanda',
                'name' => null,
                'country' => null,
                'birthday' => null,
                'code' => null,
                'category_id' => '334',
                'category_depth' => '2',
                'category_external_id' => '4',
                'external_id' => '292917133',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'person',
                'first_name' => 'Wilma',
                'last_name' => 'Geymonat',
                'name' => null,
                'country' => null,
                'birthday' => '1917',
                'code' => null,
                'category_id' => '334',
                'category_depth' => '2',
                'category_external_id' => '4',
                'external_id' => '*********',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Rostelecom Information Technologies',
                'country' => null,
                'birthday' => null,
                'code' => '1095030001131',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '3392907',
                'alternative_names' => [
                    ['name' => 'Rostelekom Informatsionnye Tekhnologii'],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Uab Elorona',
                'country' => null,
                'birthday' => null,
                'code' => '1095030001131',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '11909937',
                'alternative_names' => [
                    ['name' => 'Uab Rolneta'],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Keencloud Limited',
                'country' => 'gb',
                'birthday' => null,
                'code' => '1095030001131',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '624552',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Karatbars International Gmbh',
                'country' => null,
                'birthday' => null,
                'code' => '1095030001131',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '3111479',
                'alternative_names' => [
                    ['name' => 'Karatbars Leaders and Investors Pool International'],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Omega',
                'country' => null,
                'birthday' => null,
                'code' => '1097746627230',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '3276772',
                'alternative_names' => [
                    ['name' => 'Акционерное общество Омега'],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'CONCRED GmbH',
                'country' => 'at',
                'birthday' => null,
                'code' => '1097746627230',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '2312507',
                'alternative_names' => [
                    ['name' => 'EUROCRED GmbH'],
                ],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Supreme GmbH',
                'country' => 'in',
                'birthday' => null,
                'code' => '1097746627230',
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '3428206',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'AKC GmbH',
                'country' => 'ch',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '4484423',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Level12 GmbH',
                'country' => 'at',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '11000761',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'SPEEDCAB GmbH',
                'country' => 'at',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '11986023',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Mirna International',
                'country' => 'gb',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '658784',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'JFD International',
                'country' => 'us',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '687629',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Park International',
                'country' => 'us',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '736035',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Elmont International',
                'country' => 'ch',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '770509',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Oppenheimer International',
                'country' => 'gb',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '791655',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'IBS International',
                'country' => 'es',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '792292',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'Sibo Pakuotes UAB',
                'country' => 'lt',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '12338350',
                'alternative_names' => [[]],
            ],
            [
                'type' => 'company',
                'first_name' => null,
                'last_name' => null,
                'name' => 'BELPISCEPROM',
                'country' => 'lt',
                'birthday' => null,
                'code' => null,
                'category_id' => '359',
                'category_depth' => '3',
                'category_external_id' => '92',
                'external_id' => '12338351',
                'alternative_names' => [[]],
            ],
        ];
    }
}
