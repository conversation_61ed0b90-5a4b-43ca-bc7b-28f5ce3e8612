<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Tests\EndToEnd\HitsAndMiss;

use DateTime;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\User\UserClient;
use Mockery;
use Mockery\Expectation;
use Mockery\LegacyMockInterface;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class ProfilesHitsAndMissTest extends PersistableWebTestCase
{
    private const PERSONAL_INFO = 'personalInfo';
    private static string $currentClientCheckCase;

    /**
     * @dataProvider transferCheckDataProvider
     *
     * @param string $number
     * @param string|null $expectedProfileDisplayName
     * @param int $covenanteeId
     */
    public function testTransferCheck(string $number, ?string $expectedProfileDisplayName, int $covenanteeId): void
    {
        $blacklistManager = $this->getContainer()->get('evp_blacklist.manager');
        $transferRepository = $this->getContainer()->get('evp_bank_transfer.repository.transfer_out');
        $clientNaturalRepository = $this->getContainer()->get('evp_client.repository.client_natural');
        $client = $clientNaturalRepository->findOneByCovenanteeId($covenanteeId);
        $transferOut = $transferRepository->findOneByClientAndNumber($client, $number);
        $result = $blacklistManager->getProfileCheckBlacklistResults($transferOut);
        $displayName = isset($result[0]) ? $result[0]->getFoundProfile()->getDisplayName() : null;
        $this->assertEquals($expectedProfileDisplayName, $displayName);
    }

    public function transferCheckDataProvider(): array
    {
        return [
            'Expected hit: GPB Neftegaz Services' => ['1', 'GPB Neftegaz Services', 3501],
            'Expected hit: GPB Neftegaz' => ['2', 'GPB Neftegaz Services', 3501],
            'Expected hit: Rostelekom' => ['3', 'Rostelecom Information Technologies', 3501],
            'Expected hit: Ростелеком' => ['4', 'Rostelecom', 3501],
            'Expected hit: Gazprom Media Holding' => ['5', 'Gazprom Media Holding', 3501],
            'Expected hit: Grazvydas Liubinas' => ['6', 'Gražvydas Liubinas', 3501],
            'Expected hit: Mohamed Talbi' => ['7', 'Mohamed Talbi', 3501],
            'Expected hit: Hugo Cantú' => ['8', 'Hugo Dante Grajales Cantú', 3501],
            'Expected hit despite a spelling error: Sibo Pykuotes UAB' => ['9', 'Sibo Pakuotes UAB', 3501],
            'Expected hit despite a spelling error: Nikolaos Tsourlakis' => ['10', 'Nikolaos Tsourakis', 3501],
            'Expected hit despite short word: D matviienko' => ['11', 'Anatolii Serhiiovych Matviienko', 3501],
            'Expected hit : MONIN SERGEI ALEKSANDROVICH 123103, KHOROSHEVSKOGO SEREBRIANOGO BORA 4-IA LINIIA, 131A MOSKVA G RU'
            => ['12', 'MONIN SERGEI ALEKSANDROVICH', 3501],
            'Expected hit : with transfer id 13 and beneficiary name - BeLPISCEPROM SiA'
            => ['13', 'BELPISCEPROM', 3501],
            'Expected miss: Anwar Djaballah' => ['1', null, 4602],
            'Expected miss: UAB Pigu' => ['2', null, 4602],
            'Expected miss: Agnė Dulkytė' => ['4', null, 4602],
            'Expected miss: Younes Sadoune' => ['5', null, 4602],
            'Expected miss: Mohamed Hakim Haddad' => ['6', null, 4602],
            'Expected miss: Kristina Nudina' => ['7', null, 4602],
            'Expected miss: Liraland_H Limited' => ['8', null, 4602],
            'Expected miss: HAABERSTI HALDUS OU' => ['9', null, 4602],
            //'Expected miss: TB International GmbH' => ['11', null, 4602],
            'Expected miss: MAXIMA LATVIJA SIA' => ['13', null, 4602],
            //'Expected miss: "UAB "Omega servisas"' => ['14', null, 4602],
            //'Expected miss (metaphone() 100 % match tele2 === dalia): UAB Tele2' => ['15, null, 4602],
        ];
    }

    /**
     * @dataProvider clientCheckDataProvider
     *
     * @param ClientNatural $client
     * @param UserInformation $userInfo
     * @param string|null $expectedMatch
     */
    public function testClientCheck(ClientNatural $client, UserInformation $userInfo, ?string $expectedMatch): void
    {
        self::$currentClientCheckCase = $this->dataName();
        $blacklist = $this->getContainer()->get('evp_blacklist.repository.blacklist')
            ->findOneByKey('dow_jones');
        $resultSet = $this->getContainer()->get('evp_blacklist.manager')
            ->getClientCheckResults($blacklist, $client);
        $actualMatch = isset($resultSet[0]) ? $resultSet[0]->getFoundProfile()->getDisplayName() : null;
        $this->assertEquals($expectedMatch, $actualMatch);
    }

    public static function clientCheckDataProvider(): array
    {
        return [
            'DJ Profile with DoB=null and similar name, should hit' => [
                'client' => (new ClientNatural())
                    ->setFirstName(' Stuart')
                    ->setLastName('Towanda'),
                self::PERSONAL_INFO => (new UserInformation())
                    ->setDob(new DateTime('1980-04-25')),
                'expectedMatch' => 'Stuart Towanda'
            ],
            'DJ Profile with only year DoB that match and similar name, should hit' => [
                'client' => (new ClientNatural())
                    ->setFirstName('Wilma')
                    ->setLastName('Geymonat'),
                self::PERSONAL_INFO => (new UserInformation())
                    ->setDob(new DateTime('1917-12-30')),
                'expectedMatch' => 'Wilma Geymonat'
            ],
            'DJ Profile with similar name but different DoB, should miss' => [
                'client' => (new ClientNatural())
                    ->setFirstName('Dalia')
                    ->setLastName('Bajerčiūtė'),
                self::PERSONAL_INFO => (new UserInformation())
                    ->setDob(new DateTime('2001-02-17')),
                'expectedMatch' => null
            ],
            'DJ Profile with similar name and DoB, should hit' => [
                'client' => (new ClientNatural())
                    ->setFirstName('Dalia')
                    ->setLastName('Bajerčiūtė'),
                self::PERSONAL_INFO => (new UserInformation())
                    ->setDob(new DateTime('1956-01-07')),
                'expectedMatch' => 'Dalia Bajerčiūtė'
            ],
        ];
    }

    protected static function createUserClient(): LegacyMockInterface
    {
        $mock = Mockery::mock(UserClient::class);

        /** @var Expectation $expectation */
        $expectation = $mock->shouldReceive('getPerson');
        $expectation->andReturnUsing(
            fn() => self::clientCheckDataProvider()[self::$currentClientCheckCase][self::PERSONAL_INFO]
        );

        return $mock;
    }

    protected static function getMockedServices(): array
    {
        return [
            'evp_blacklist.user_client' => self::createUserClient(),
        ];
    }
}
