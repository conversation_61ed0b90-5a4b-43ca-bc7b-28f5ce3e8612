<?php

namespace Evp\Bundle\BlacklistBundle\Entity;

use Paysera\Component\Serializer\Entity\Filter;

class ProfileFilter extends Filter
{
    const ORDER_BY_ID = 'id';

    /**
     * @var Blacklist
     */
    private $blacklist;

    /**
     * @var string
     */
    private $externalId;

    /**
     * @var string
     */
    private $version;

    /**
     * @var string
     */
    private $versionFrom;

    /**
     * @var int
     */
    private $idFrom;

    /**
     * @var int
     */
    private $idTo;

    public function __construct()
    {
        $this->setOrderBy(self::ORDER_BY_ID);
        $this->setOrderAsc(true);
        $this->setOffset(0);
    }

    /**
     * @return Blacklist
     */
    public function getBlacklist()
    {
        return $this->blacklist;
    }

    /**
     * @param Blacklist $blacklist
     * @return $this
     */
    public function setBlacklist(Blacklist $blacklist)
    {
        $this->blacklist = $blacklist;

        return $this;
    }

    /**
     * @return string
     */
    public function getExternalId()
    {
        return $this->externalId;
    }

    /**
     * @param string $externalId
     * @return $this
     */
    public function setExternalId($externalId)
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * @return string
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param string $version
     * @return $this
     */
    public function setVersion($version)
    {
        $this->version = $version;

        return $this;
    }

    /**
     * @return string
     */
    public function getVersionFrom()
    {
        return $this->versionFrom;
    }

    /**
     * @param string $versionFrom
     * @return $this
     */
    public function setVersionFrom($versionFrom)
    {
        $this->versionFrom = $versionFrom;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getIdFrom()
    {
        return $this->idFrom;
    }

    /**
     * @param int $idFrom
     * @return $this
     */
    public function setIdFrom(int $idFrom)
    {
        $this->idFrom = $idFrom;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getIdTo()
    {
        return $this->idTo;
    }

    /**
     * @param int $idTo
     * @return $this
     */
    public function setIdTo(int $idTo)
    {
        $this->idTo = $idTo;

        return $this;
    }
}
