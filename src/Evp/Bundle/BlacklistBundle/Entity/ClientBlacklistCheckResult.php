<?php

namespace Evp\Bundle\BlacklistBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Evp\Bundle\ClientBundle\Entity\Client;

/**
 * ClientBlacklistCheckResult
 */
class ClientBlacklistCheckResult
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var Client
     */
    private $client;

    private Blacklist $blacklist;

    /**
     * @var ArrayCollection|ClientBlacklistCheckResultItem[]
     */
    private $items;

    /**
     * @var ArrayCollection|ClientSanctionsScreening[]
     */
    private Collection $clientSanctionScreenings;

    /**
     * @var \DateTime
     */
    private $checkedAt;

    /**
     * @var bool
     */
    private $blacklisted;

    /**
     * ClientBlacklistCheckResult constructor.
     */
    public function __construct()
    {
        $this->blacklisted = false;
        $this->checkedAt = new \DateTime();
        $this->items = new ArrayCollection();
        $this->clientSanctionScreenings = new ArrayCollection();
    }

    /**
     * @return ArrayCollection|ClientSanctionsScreening[]
     */
    public function getClientSanctionScreenings(): Collection
    {
        return $this->clientSanctionScreenings;
    }

    /**
     * @param int $id
     *
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return Client
     */
    public function getClient()
    {
        return $this->client;
    }

    /**
     * @param Client $client
     *
     * @return $this
     */
    public function setClient(Client $client)
    {
        $this->client = $client;

        return $this;
    }

    public function getBlacklist(): Blacklist
    {
        return $this->blacklist;
    }

    public function setBlacklist(Blacklist $blacklist): self
    {
        $this->blacklist = $blacklist;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCheckedAt()
    {
        return $this->checkedAt;
    }

    /**
     * @param \DateTime $checkedAt
     *
     * @return $this
     */
    public function setCheckedAt(\DateTime $checkedAt)
    {
        $this->checkedAt = $checkedAt;

        return $this;
    }

    /**
     * @return bool
     */
    public function isBlacklisted()
    {
        return $this->blacklisted;
    }

    /**
     * @param bool $blacklisted
     *
     * @return $this
     */
    public function setBlacklisted($blacklisted)
    {
        $this->blacklisted = $blacklisted;

        return $this;
    }

    /**
     * @return ArrayCollection|ClientBlacklistCheckResultItem[]
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * @param ArrayCollection|ClientBlacklistCheckResultItem[] $items
     * @return $this
     */
    public function setItems($items): self
    {
        $this->items = $items;

        return $this;
    }

    public function addItem(ClientBlacklistCheckResultItem $item): self
    {
        if (!$this->items->contains($item)) {
            $this->items->add($item);
        }

        return $this;
    }
}
