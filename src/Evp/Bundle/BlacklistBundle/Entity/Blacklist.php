<?php

namespace Evp\Bundle\BlacklistBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;

class Blacklist
{
    const KEY_PAYSERA = 'paysera';
    const KEY_DOW_JONES = 'dow_jones';
    const KEY_PAYSERA_V1 = 'paysera_v1';
    const KEY_DOW_JONES_V1 = 'dow_jones_v1';
    const KEY_PAYSERA_LT = 'paysera_lt';
    const KEY_PAYSERA_AL = 'paysera_al';
    const KEY_PAYSERA_XK = 'paysera_xk';
    const KEY_PAYSERA_GE = 'paysera_ge';

    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $key;

    /**
     * @var string
     */
    private $version;

    private bool $external; // Protect profiles related to this blacklist from modification / deletion manually.
    private bool $protected; // Protect blacklist from modification / deletion.
    private bool $deprecated; // Old blacklists that are not used anymore. Exclude them from UI, etc…
    private bool $periodicCheckNeeded; // Should we run periodic check for this blacklist?
    private bool $periodicCheckReportNeeded; // Should we generate PDF report for this blacklist after periodic check?

    /**
     * @var LicensedPartner[]|ArrayCollection
     */
    private $licensedPartners;

    /**
     * @param string $key
     * @param string $name
     */
    public function __construct($key, $name)
    {
        $this->key = $key;
        $this->name = $name;

        $this->external = false;
        $this->protected = false;
        $this->deprecated = false;
        $this->periodicCheckNeeded = false;
        $this->periodicCheckReportNeeded = false;
        $this->licensedPartners = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $key
     * @return $this
     */
    public function setKey($key)
    {
        $this->key = $key;

        return $this;
    }

    /**
     * @return string
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * @return string
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param string $version
     * @return $this
     */
    public function setVersion($version)
    {
        $this->version = $version;

        return $this;
    }

    public function setExternal(bool $external): self
    {
        $this->external = $external;

        return $this;
    }

    public function isExternal(): bool
    {
        return $this->external;
    }

    public function setProtected(bool $protected): self
    {
        $this->protected = $protected;

        return $this;
    }

    public function isProtected(): bool
    {
        return $this->protected;
    }

    public function setDeprecated(bool $deprecated): self
    {
        $this->deprecated = $deprecated;

        return $this;
    }

    public function isDeprecated(): bool
    {
        return $this->deprecated;
    }

    public function setPeriodicCheckNeeded(bool $periodicCheckNeeded): self
    {
        $this->periodicCheckNeeded = $periodicCheckNeeded;

        return $this;
    }

    public function isPeriodicCheckNeeded(): bool
    {
        return $this->periodicCheckNeeded;
    }

    public function setPeriodicCheckReportNeeded(bool $periodicCheckReportNeeded): self
    {
        $this->periodicCheckReportNeeded = $periodicCheckReportNeeded;

        return $this;
    }

    public function isPeriodicCheckReportNeeded(): bool
    {
        return $this->periodicCheckReportNeeded;
    }

    /**
     * @return LicensedPartner[]|ArrayCollection
     */
    public function getLicensedPartners()
    {
        return $this->licensedPartners;
    }

    /**
     * @param LicensedPartner[]|ArrayCollection $licensedPartners
     */
    public function setLicensedPartners($licensedPartners): self
    {
        $this->licensedPartners = $licensedPartners;

        return $this;
    }

    public function __toString()
    {
        return $this->getName();
    }
}
