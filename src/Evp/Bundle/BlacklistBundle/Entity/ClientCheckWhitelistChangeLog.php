<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Entity;

use DateTimeImmutable;

class ClientCheckWhitelistChangeLog
{
    public const ACTION_CREATE = 'create';
    public const ACTION_MODIFY = 'modify';
    public const ACTION_DELETE = 'delete';
    public const FIELD_CLIENT = 'client';
    public const FIELD_BLACKLIST = 'blacklist';
    public const FIELD_PROFILE_EXTERNAL_ID = 'profileExternalId';
    public const FIELD_CATEGORY_GROUP = 'categoryGroup';
    public const TRACKABLE_FIELDS = [
        self::FIELD_CLIENT,
        self::FIELD_BLACKLIST,
        self::FIELD_PROFILE_EXTERNAL_ID,
        self::FIELD_CATEGORY_GROUP,
    ];

    private int $id;
    private ?int $clientCheckWhitelist;
    private DateTimeImmutable $date;
    private string $action;
    private ?string $field;
    private ?string $value;
    private int $userId;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getClientCheckWhitelist(): ?int
    {
        return $this->clientCheckWhitelist;
    }

    public function setClientCheckWhitelist(?int $clientCheckWhitelist): self
    {
        $this->clientCheckWhitelist = $clientCheckWhitelist;
        return $this;
    }

    public function getDate(): DateTimeImmutable
    {
        return $this->date;
    }

    public function setDate(DateTimeImmutable $date): self
    {
        $this->date = $date;
        return $this;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;
        return $this;
    }

    public function getField(): ?string
    {
        return $this->field;
    }

    public function setField(?string $field): self
    {
        $this->field = $field;
        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): self
    {
        $this->value = $value;
        return $this;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;
        return $this;
    }
}
