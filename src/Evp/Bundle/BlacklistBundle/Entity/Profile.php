<?php

namespace Evp\Bundle\BlacklistBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Evp\Bundle\BankTransferBundle\Entity\ClientTypePrediction;

class Profile
{
    const TYPE_BANK = 'bank';
    const TYPE_COMPANY = 'company';
    const TYPE_PERSON = 'person';

    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $firstName;

    /**
     * @var string
     */
    private $middleName;

    /**
     * @var string
     */
    private $lastName;

    /**
     * @var string
     */
    private $code;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string
     */
    private $birthday;

    /**
     * @var string
     */
    private $phone;

    /**
     * @var Blacklist
     */
    private $blacklist;

    /**
     * @var string
     */
    private $version;

    /**
     * @var string
     */
    private $info;

    /**
     * @var string
     */
    private $externalId;

    /**
     * @var ArrayCollection|BankCode[]
     */
    private $bankCodes;

    /**
     * @var ArrayCollection|Category[]
     */
    private $categories;

    /**
     * @var ArrayCollection|AlternativeName[]
     */
    private $alternativeNames;

    /**
     * @var Collection|AlternativeBirthday[]
     */
    private Collection $alternativeBirthdays;

    private ?ClientTypePrediction $clientTypePrediction;

    /**
     * @var ArrayCollection|ProfileRole[]
     */
    private $roles;

    /**
     * @var ArrayCollection|ProfileImage[]
     */
    private $images;

    /**
     * @var ArrayCollection|ProfileSource[]
     */
    private $sources;

    /**
     * @var ArrayCollection|ProfileSanctionsReference[]
     */
    private $sanctionsReferences;

    /**
     * @var ArrayCollection|ProfileNote[]
     */
    private $notes;

    /**
     * @var ArrayCollection|ProfileAddress[]
     */
    private $addresses;

    /**
     * @var ArrayCollection|ProfileDateDetail[]
     */
    private $dateDetails;

    /**
     * @var ArrayCollection|ProfileBirthPlace[]
     */
    private $birthPlaces;

    /**
     * @var ArrayCollection|ProfileIdNumber[]
     */
    private $idNumbers;

    /**
     * @var ArrayCollection|ProfileAssociation[]
     */
    private $associations;

    public function __construct()
    {
        $this->version = '';
        $this->clientTypePrediction = null;
        $this->setType(self::TYPE_PERSON);
        $this->bankCodes = new ArrayCollection();
        $this->categories = new ArrayCollection();
        $this->alternativeNames = new ArrayCollection();
        $this->alternativeBirthdays = new ArrayCollection();
        $this->roles = new ArrayCollection();
        $this->images = new ArrayCollection();
        $this->sources = new ArrayCollection();
        $this->sanctionsReferences = new ArrayCollection();
        $this->notes = new ArrayCollection();
        $this->addresses = new ArrayCollection();
        $this->dateDetails = new ArrayCollection();
        $this->birthPlaces = new ArrayCollection();
        $this->idNumbers = new ArrayCollection();
        $this->associations = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param string $type
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $firstName
     * @return $this
     */
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;

        return $this;
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * @param string $lastName
     * @return $this
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;

        return $this;
    }

    /**
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * @param string $middleName
     * @return $this
     */
    public function setMiddleName($middleName)
    {
        $this->middleName = $middleName;

        return $this;
    }

    /**
     * @return string
     */
    public function getMiddleName()
    {
        return $this->middleName;
    }

    /**
     * @param string $code
     * @return $this
     */
    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    /**
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param string $country
     * @return $this
     */
    public function setCountry($country)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param string $birthday
     * @return $this
     */
    public function setBirthday($birthday)
    {
        $this->birthday = $birthday;

        return $this;
    }

    /**
     * @return string
     */
    public function getBirthday()
    {
        return $this->birthday;
    }

    /**
     * @param string $phone
     * @return $this
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @param Blacklist $blacklist
     * @return $this
     */
    public function setBlacklist(Blacklist $blacklist)
    {
        $this->blacklist = $blacklist;

        return $this;
    }

    /**
     * @return Blacklist
     */
    public function getBlacklist()
    {
        return $this->blacklist;
    }

    /**
     * @param string $version
     * @return $this
     */
    public function setVersion($version)
    {
        $this->version = $version;

        return $this;
    }

    /**
     * @return string
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @return string
     */
    public function getDisplayName()
    {
        return $this->searchName();
    }

    /**
     * @param string $info
     * @return $this
     */
    public function setInfo($info)
    {
        $this->info = $info;

        return $this;
    }

    /**
     * @return string
     */
    public function getInfo()
    {
        return $this->info;
    }

    /**
     * @return string
     */
    public function getExternalId()
    {
        return $this->externalId;
    }

    /**
     * @param string $externalId
     * @return $this
     */
    public function setExternalId($externalId)
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * @param BankCode $bankCode
     * @return $this
     */
    public function addBankCode(BankCode $bankCode)
    {
        $bankCode->setItem($this);
        $this->bankCodes->add($bankCode);

        return $this;
    }

    /**
     * @return ArrayCollection|BankCode[]
     */
    public function getBankCodes()
    {
        return $this->bankCodes;
    }

    /**
     * @param Category $category
     * @return $this
     */
    public function addCategory(Category $category)
    {
        $this->categories->add($category);

        return $this;
    }

    /**
     * @return ArrayCollection|Category[]
     */
    public function getCategories()
    {
        return $this->categories;
    }

    /**
     * @param Category[] $categories
     * @return $this
     */
    public function setCategories($categories)
    {
        $this->categories = new ArrayCollection();
        foreach ($categories as $category) {
            $this->addCategory($category);
        }

        return $this;
    }

    /**
     * @return CategoryGroup|null
     */
    public function getTopCategoryGroup()
    {
        if (count($this->getCategories()) > 0) {
            return $this->getCategories()->get(0)->getGroup();
        }

        return null;
    }

    /**
     * @return int[]
     */
    public function getCategoryGroupIds()
    {
        $result = [];
        foreach ($this->getCategories() as $category) {
            if ($category->getGroup() !== null) {
                $result[] = $category->getGroup()->getId();
            }
        }

        return array_values(array_unique($result));
    }

    /**
     * @return string
     */
    public function getAdditionalInfo()
    {
        $info = sprintf(
            '%s %s %s',
            $this->getBirthday(),
            $this->getCode(),
            $this->getInfo()
        );
        $info = preg_replace('#\s+#', ' ', $info);
        $info = trim($info);

        return $info;
    }

    /**
     * @return ArrayCollection|AlternativeName[]
     */
    public function getAlternativeNames()
    {
        return $this->alternativeNames;
    }

    /**
     * @param AlternativeName $alternativeName
     * @return $this
     */
    public function addAlternativeName(AlternativeName $alternativeName): self
    {
        if (!$this->alternativeNames->contains($alternativeName)) {
            $this->alternativeNames->add($alternativeName);
            $alternativeName->setProfile($this);
        }

        return $this;
    }

    /**
     * @param Collection $alternativeNames
     * @return $this
     */
    public function setAlternativeNames(Collection $alternativeNames): self
    {
        $this->alternativeNames->clear();
        foreach ($alternativeNames as $alternativeName) {
            $this->addAlternativeName($alternativeName);
        }

        return $this;
    }

    /**
     * @return Collection|AlternativeBirthday[]
     */
    public function getAlternativeBirthdays(): Collection
    {
        return $this->alternativeBirthdays;
    }

    public function addAlternativeBirthday(AlternativeBirthday $alternativeBirthday): self
    {
        if (!$this->alternativeBirthdays->contains($alternativeBirthday)) {
            $this->alternativeBirthdays->add($alternativeBirthday);
            $alternativeBirthday->setProfile($this);
        }

        return $this;
    }

    /**
     * @param Collection $alternativeBirthdays
     * @return $this
     */
    public function setAlternativeBirthdays(Collection $alternativeBirthdays): self
    {
        $this->alternativeBirthdays->clear();
        foreach ($alternativeBirthdays as $alternativeBirthday) {
            $this->addAlternativeBirthday($alternativeBirthday);
        }

        return $this;
    }

    /**
     * @return string
     */
    public function searchName()
    {
        if ($this->name !== null) {
            // Company and bank
            $name = $this->name;
        } else {
            // Person
            $name = sprintf(
                '%s %s %s',
                $this->getFirstName(),
                $this->getMiddleName(),
                $this->getLastName()
            );
        }
        $name = preg_replace('#\s+#u', ' ', $name);
        $name = trim($name);

        return !empty($name) ? $name : null;
    }

    public function __toString()
    {
        return (string) $this->id;
    }

    /**
     * @return ClientTypePrediction|null
     */
    public function getClientTypePrediction(): ?ClientTypePrediction
    {
        return $this->clientTypePrediction;
    }

    /**
     * @param ClientTypePrediction $clientTypePrediction
     * @return $this
     */
    public function setClientTypePrediction(ClientTypePrediction $clientTypePrediction): self
    {
        $this->clientTypePrediction = $clientTypePrediction;
        return $this;
    }

    /**
     * @return ArrayCollection|ProfileRole[]
     */
    public function getRoles()
    {
        return $this->roles;
    }

    /**
     * @param ProfileRole[] $roles
     */
    public function setRoles(array $roles): self
    {
        foreach ($roles as $role) {
            $this->addRole($role);
        }

        return $this;
    }

    public function addRole(ProfileRole $role): self
    {
        $this->roles->add($role);
        $role->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileImage[]
     */
    public function getImages()
    {
        return $this->images;
    }

    /**
     * @param ProfileImage[] $images
     */
    public function setImages(array $images): self
    {
        foreach ($images as $image) {
            $this->addImage($image);
        }

        return $this;
    }

    public function addImage(ProfileImage $image): self
    {
        $this->images->add($image);
        $image->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileSource[]
     */
    public function getSources()
    {
        return $this->sources;
    }

    /**
     * @param ProfileSource[] $sources
     */
    public function setSources(array $sources): self
    {
        foreach ($sources as $source) {
            $this->addSource($source);
        }

        return $this;
    }

    public function addSource(ProfileSource $source): self
    {
        $this->sources->add($source);
        $source->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileSanctionsReference[]
     */
    public function getSanctionsReferences()
    {
        return $this->sanctionsReferences;
    }

    /**
     * @param ProfileSanctionsReference[] $sanctionsReferences
     */
    public function setSanctionsReferences(array $sanctionsReferences): self
    {
        foreach ($sanctionsReferences as $sanctionsReference) {
            $this->addSanctionsReference($sanctionsReference);
        }

        return $this;
    }

    public function addSanctionsReference(ProfileSanctionsReference $sanctionsReference): self
    {
        $this->sanctionsReferences->add($sanctionsReference);
        $sanctionsReference->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileNote[]
     */
    public function getNotes()
    {
        return $this->notes;
    }

    /**
     * @param ProfileNote[] $notes
     */
    public function setNotes(array $notes): self
    {
        foreach ($notes as $note) {
            $this->addNote($note);
        }

        return $this;
    }

    public function addNote(ProfileNote $note): self
    {
        $this->notes->add($note);
        $note->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileAddress[]
     */
    public function getAddresses()
    {
        return $this->addresses;
    }

    /**
     * @param ProfileAddress[] $addresses
     */
    public function setAddresses(array $addresses): self
    {
        foreach ($addresses as $address) {
            $this->addAddress($address);
        }

        return $this;
    }

    public function addAddress(ProfileAddress $address): self
    {
        $this->addresses->add($address);
        $address->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileDateDetail[]
     */
    public function getDateDetails()
    {
        return $this->dateDetails;
    }

    /**
     * @param ProfileDateDetail[] $dateDetails
     */
    public function setDateDetails(array $dateDetails): self
    {
        foreach ($dateDetails as $dateDetail) {
            $this->addDateDetail($dateDetail);
        }

        return $this;
    }

    public function addDateDetail(ProfileDateDetail $dateDetail): self
    {
        $this->dateDetails->add($dateDetail);
        $dateDetail->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileBirthPlace[]
     */
    public function getBirthPlaces()
    {
        return $this->birthPlaces;
    }

    /**
     * @param ProfileBirthPlace[] $birthPlaces
     */
    public function setBirthPlaces(array $birthPlaces): self
    {
        foreach ($birthPlaces as $birthPlace) {
            $this->addBirthPlace($birthPlace);
        }

        return $this;
    }

    public function addBirthPlace(ProfileBirthPlace $birthPlace): self
    {
        $this->birthPlaces->add($birthPlace);
        $birthPlace->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileIdNumber[]
     */
    public function getIdNumbers()
    {
        return $this->idNumbers;
    }

    /**
     * @param ProfileIdNumber[] $idNumbers
     */
    public function setIdNumbers(array $idNumbers): self
    {
        foreach ($idNumbers as $idNumber) {
            $this->addIdNumber($idNumber);
        }

        return $this;
    }

    public function addIdNumber(ProfileIdNumber $idNumber): self
    {
        $this->idNumbers->add($idNumber);
        $idNumber->setProfile($this);

        return $this;
    }

    /**
     * @return ArrayCollection|ProfileAssociation[]
     */
    public function getAssociations()
    {
        return $this->associations;
    }

    /**
     * @param ProfileAssociation[] $associates
     */
    public function setAssociations(array $associates): self
    {
        $this->associations = new ArrayCollection();
        foreach ($associates as $associate) {
            $this->addAssociation($associate);
        }

        return $this;
    }

    public function addAssociation(ProfileAssociation $associate): self
    {
        $this->associations->add($associate);

        return $this;
    }
}
