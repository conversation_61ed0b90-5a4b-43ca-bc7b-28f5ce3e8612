<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Entity;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;

class NamesComparisonResult extends NamesComparisonResultDto
{
    private int $id;

    private ?ClientBlacklistCheckResult $blacklistCheckResult = null;

    private ?Transfer $transfer = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getTransfer(): ?Transfer
    {
        return $this->transfer;
    }

    public function setTransfer(?Transfer $transfer): self
    {
        $this->transfer = $transfer;

        return $this;
    }

    public function getBlacklistCheckResult(): ?ClientBlacklistCheckResult
    {
        return $this->blacklistCheckResult;
    }

    public function setBlacklistCheckResult(?ClientBlacklistCheckResult $blacklistCheckResult): self
    {
        $this->blacklistCheckResult = $blacklistCheckResult;

        return $this;
    }
}
