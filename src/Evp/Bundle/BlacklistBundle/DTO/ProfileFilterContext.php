<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\DTO;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;

class ProfileFilterContext
{
    private bool $ignoreAlternativeNames;

    private ?ClientBlacklistCheckResult $blacklistCheckResult;

    private ?Transfer $transfer;

    public function __construct(
        bool $ignoreAlternativeNames,
        ?ClientBlacklistCheckResult $blacklistCheckResult = null,
        ?Transfer $transfer = null
    ) {
        $this->ignoreAlternativeNames = $ignoreAlternativeNames;
        $this->transfer = $transfer;
        $this->blacklistCheckResult = $blacklistCheckResult;
    }

    public function setIgnoreAlternativeNames(bool $ignoreAlternativeNames): self
    {
        $this->ignoreAlternativeNames = $ignoreAlternativeNames;

        return $this;
    }

    public function shouldIgnoreAlternativeNames(): bool
    {
        return $this->ignoreAlternativeNames;
    }

    public function getBlacklistCheckResult(): ?ClientBlacklistCheckResult
    {
        return $this->blacklistCheckResult;
    }

    public function setBlacklistCheckResult(?ClientBlacklistCheckResult $blacklistCheckResult): self
    {
        $this->blacklistCheckResult = $blacklistCheckResult;

        return $this;
    }

    public function getTransfer(): ?Transfer
    {
        return $this->transfer;
    }

    public function setTransfer(?Transfer $transfer): self
    {
        $this->transfer = $transfer;

        return $this;
    }
}
