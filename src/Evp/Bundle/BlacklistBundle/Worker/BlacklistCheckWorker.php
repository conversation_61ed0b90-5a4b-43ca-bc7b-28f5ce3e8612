<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Worker;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Evp\Bundle\BankingHistoryIntegrationBundle\Service\GlobalIdentificationManager;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResultItem;
use Evp\Bundle\BlacklistBundle\Event\ClientBlacklistCheckResultEvent;
use Evp\Bundle\BlacklistBundle\Events;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistFilterService;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\ClientBundle\Repository\PartnerClientRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\QuestionnaireBundle\Entity\ClientLegal\RealBeneficiary;
use Evp\Bundle\QuestionnaireBundle\Repository\QuestionnaireRepository;
use Evp\Bundle\QuestionnaireBundle\Worker\QuestionnaireSubmitProcessingWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Exception\InvalidDataException;
use Evp\Bundle\RabbitMqExtensionBundle\Service\JobWorkerInterface;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Exception;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class BlacklistCheckWorker implements JobWorkerInterface
{
    public const JOB_CHECK_BLACKLIST_USERS = 'gateway.check_blacklist_users';
    public const JOB_CHECK_BLACKLIST_USER_GENERATE_REPORT = 'gateway.check_blacklist_user_generate_report';

    public const PURPOSE_BLACKLIST_DATA_EXPORT = 'blacklist_data_export';

    private const PROBLEM_CLIENT_IS_NULL = 'client_is_null';
    private const PROBLEM_CLIENT_IS_INVALID = 'client_is_invalid';
    private const PROBLEM_NO_PARTNER_CLIENT = 'no_partner_client';
    private const PROBLEM_NO_BLACKLISTS = 'no_blacklists';

    private ClientRepository $clientRepository;
    private LoggerInterface $logger;
    private EntityManagerInterface $entityManager;
    private BlacklistFilterService $blacklistFilterService;
    private BlacklistRepository $blacklistRepository;
    private QuestionnaireRepository $questionnaireRepository;
    private PartnerClientRepository $partnerClientRepository;
    private EventDispatcherInterface $eventDispatcher;
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private string $aiConfidence;
    private MonitoringClient $monitoringClient;
    private string $monitoringKey;
    private GlobalIdentificationManager $globalIdentificationManager;
    private int $globalIdentificationIdWaitTimeout;

    public function __construct(
        EntityManagerInterface $entityManager,
        ClientRepository $clientRepository,
        LoggerInterface $logger,
        BlacklistFilterService $blacklistFilterService,
        BlacklistRepository $blacklistRepository,
        QuestionnaireRepository $questionnaireRepository,
        PartnerClientRepository $partnerClientRepository,
        EventDispatcherInterface $eventDispatcher,
        RemoteJobPublisherInterface $remoteJobPublisher,
        string $aiConfidence,
        MonitoringClient $monitoringClient,
        string $monitoringKey,
        GlobalIdentificationManager $globalIdentificationManager,
        int $globalIdentificationIdWaitTimeout
    ) {
        $this->clientRepository = $clientRepository;
        $this->logger = $logger;
        $this->entityManager = $entityManager;
        $this->blacklistFilterService = $blacklistFilterService;
        $this->blacklistRepository = $blacklistRepository;
        $this->questionnaireRepository = $questionnaireRepository;
        $this->partnerClientRepository = $partnerClientRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->aiConfidence = $aiConfidence;
        $this->monitoringClient = $monitoringClient;
        $this->monitoringKey = $monitoringKey;
        $this->globalIdentificationManager = $globalIdentificationManager;
        $this->globalIdentificationIdWaitTimeout = $globalIdentificationIdWaitTimeout;
    }

    /**
     * @param array $data
     *
     * @throws InvalidDataException
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function work($data)
    {
        $this->logger->info('Updating users blacklist', [$data]);

        $clients = $data['clients'] ?? [];
        $purpose = $data['purpose'] ?? null;
        $globalIdentificationId = $data['global_identification_id'] ?? null;

        if ($globalIdentificationId !== null) {
            $this->globalIdentificationManager->waitForGlobalIdentificationIdSync(
                $globalIdentificationId,
                $this->globalIdentificationIdWaitTimeout,
                GlobalIdentificationManager::ROUTE_TO_SLAVE
            );
        }

        foreach ($clients as $key => $clientId) {
            if ($key !== 0 && $key % 20 === 0) {
                $this->entityManager->flush();
                $this->entityManager->clear();
            }

            if ($clientId === null) {
                continue;
            }

            $this->checkClient((int) $clientId, $purpose);
        }
    }

    /**
     * @throws Exception
     */
    private function checkClient(int $clientId, ?string $purpose): void
    {
        $client = $this->clientRepository->findOneById($clientId);
        $logContext = ['client_id' => $clientId];

        if ($client === null) {
            $this->logger->error(sprintf('BlacklistCheckWorker %s', self::PROBLEM_CLIENT_IS_NULL), $logContext);
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $clientId,
                ['problem' => self::PROBLEM_CLIENT_IS_NULL]
            );
            return;
        }

        $logContext['user_id'] = $client->getCovenanteeId();

        if (!$this->isValid($client)) {
            $this->logger->error(sprintf('BlacklistCheckWorker %s', self::PROBLEM_CLIENT_IS_INVALID), $logContext);
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $clientId,
                ['problem' => self::PROBLEM_CLIENT_IS_INVALID]
            );
            return;
        }

        $partnerClient = $this->partnerClientRepository->findOneLatestByClient($client, new DateTime());
        if ($partnerClient === null) {
            $this->logger->error(sprintf('BlacklistCheckWorker %s', self::PROBLEM_NO_PARTNER_CLIENT), $logContext);
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $clientId,
                ['problem' => self::PROBLEM_NO_PARTNER_CLIENT]
            );
            return;
        }

        $code = $partnerClient->getPartnerCode();
        $logContext[0] = $code;
        $blacklists = $this->blacklistRepository->findNotDeprecatedPeriodicCheckNeededByLicensedPartnerCode($code);
        if (count($blacklists) === 0) {
            $this->logger->error(sprintf('BlacklistCheckWorker %s', self::PROBLEM_NO_BLACKLISTS), $logContext);
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $clientId,
                ['problem' => self::PROBLEM_NO_BLACKLISTS]
            );
            return;
        }

        $realBeneficiaries = $this->resolveRealBeneficiaries($client);

        foreach ($blacklists as $blacklist) {
            $blacklistCheckResult = $this->checkClientAgainstBlacklist($client, $blacklist, $realBeneficiaries);
            $this->publishQuestionnaireJob($blacklistCheckResult);
            $this->entityManager->flush();

            $this->runNeededEventsAndJobs($blacklistCheckResult, $purpose);
            $this->entityManager->flush();
        }
    }

    /**
     * @param RealBeneficiary[] $realBeneficiaries
     * @throws Exception
     */
    private function checkClientAgainstBlacklist(
        Client $client,
        Blacklist $blacklist,
        iterable $realBeneficiaries
    ): ClientBlacklistCheckResult {
        $blacklistCheckResult = (new ClientBlacklistCheckResult())->setClient($client)->setBlacklist($blacklist);
        $this->entityManager->persist($blacklistCheckResult);

        $blacklistResults = $this->getBlacklistResults($client, $blacklist, $realBeneficiaries, $blacklistCheckResult);

        $blacklistCheckResult->setBlacklisted(count($blacklistResults) > 0);
        $blacklistCheckResult->setCheckedAt(new DateTime());
        $this->saveBlacklistCheckResultItems($blacklistCheckResult, $blacklistResults);

        return $blacklistCheckResult;
    }

    /**
     * @param RealBeneficiary[] $realBeneficiaries
     * @return BlacklistResult[]
     * @throws Exception
     */
    private function getBlacklistResults(Client $client, Blacklist $blacklist, iterable $realBeneficiaries, ClientBlacklistCheckResult $blacklistCheckResult): array
    {
        $blacklistResults = $this->blacklistFilterService->getClientCheckResults(
            $blacklist,
            $client,
            true,
            $this->aiConfidence,
            false,
            $blacklistCheckResult
        );

        foreach ($realBeneficiaries as $beneficiary) {
            $realBeneficiaryResults = $this->blacklistFilterService->getRealBeneficiaryCheckResults(
                $blacklist,
                $beneficiary,
                $client,
                true,
                false,
                $this->aiConfidence,
                $blacklistCheckResult
            );

            $blacklistResults = array_merge($blacklistResults, $realBeneficiaryResults);
        }

        return $blacklistResults;
    }

    /**
     * @param BlacklistResult[] $blacklistResults
     */
    private function saveBlacklistCheckResultItems(
        ClientBlacklistCheckResult $blacklistCheckResult,
        array $blacklistResults
    ): void {
        foreach ($blacklistResults as $blacklistResult) {
            $blacklistCheckResultItem = (new ClientBlacklistCheckResultItem())
                ->setClientBlacklistCheckResult($blacklistCheckResult)
                ->setProfile($blacklistResult->getFoundProfile())
                ->setConfidence($blacklistResult->getConfidence())
            ;

            $this->entityManager->persist($blacklistCheckResultItem);
        }
    }

    private function runNeededEventsAndJobs(ClientBlacklistCheckResult $blacklistCheckResult, ?string $purpose): void
    {
        if ($blacklistCheckResult->isBlacklisted()) {
            $this->eventDispatcher->dispatch(
                Events::CLIENT_BLACKLISTED,
                new ClientBlacklistCheckResultEvent($blacklistCheckResult)
            );
        }

        if (!$blacklistCheckResult->isBlacklisted() && $purpose === self::PURPOSE_BLACKLIST_DATA_EXPORT) {
            $this->remoteJobPublisher->publishJob(
                BlacklistCheckGenerateReportFileWorker::JOB_CHECK_BLACKLIST_USER_GENERATE_REPORT_FILE,
                [
                    'clientId' => $blacklistCheckResult->getClient()->getId(),
                    'clientBlacklistCheckResultId' => $blacklistCheckResult->getId()
                ]
            );
        }
    }

    private function publishQuestionnaireJob(ClientBlacklistCheckResult $blacklistCheckResult): void
    {
        if (
            $blacklistCheckResult->getBlacklist()->getKey() === Blacklist::KEY_DOW_JONES_V1
            && $blacklistCheckResult->getClient()->getClientType() === Client::TYPE_NATURAL
        ) {
            $this->remoteJobPublisher->publishJob(
                QuestionnaireSubmitProcessingWorker::JOB_KEY,
                [
                    'client_id' => $blacklistCheckResult->getClient()->getId(),
                ]
            );
        }
    }

    /**
     * @return RealBeneficiary[]
     * @throws NonUniqueResultException
     */
    private function resolveRealBeneficiaries(Client $client): iterable
    {
        if (!$client instanceof ClientLegal) {
            return [];
        }

        $questionnaire = $this->questionnaireRepository->findLastByClient($client);
        if ($questionnaire === null || $questionnaire->getLegalQuestionnaire() === null) {
            return [];
        }

        return $questionnaire->getLegalQuestionnaire()->getRealBeneficiaries();
    }

    private function isValid(Client $client): bool
    {
        if (
            $client instanceof ClientNatural
            && $client->getDisplayName() === null
            && $client->getCode() === null
            && $client->getLevel() === IdentificationLevels::UNIDENTIFIED
        ) {
            return false;
        }

        return true;
    }
}
