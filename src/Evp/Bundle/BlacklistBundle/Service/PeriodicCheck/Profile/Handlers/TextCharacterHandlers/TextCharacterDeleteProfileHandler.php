<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers;

class TextCharacterDeleteProfileHandler extends AbstractTextCharacterProfileHandler
{
    /**
     * @param string $textEntry
     * @return string
     */
    public function manipulate(string $textEntry): string
    {
        return substr_replace(
            $textEntry,
            '',
            rand(0, strlen($textEntry) - 1),
            1
        );
    }
}
