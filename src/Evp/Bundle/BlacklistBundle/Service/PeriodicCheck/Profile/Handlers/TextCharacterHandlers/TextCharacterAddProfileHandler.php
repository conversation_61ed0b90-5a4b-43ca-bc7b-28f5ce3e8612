<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\PeriodicCheck\Profile\Handlers\TextCharacterHandlers;

use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Exception\PeriodicCheckProfileHandlerException;

class TextCharacterAddProfileHandler extends AbstractTextCharacterProfileHandler
{
    /**
     * @param string $textEntry
     * @return string
     */
    public function manipulate(string $textEntry): string
    {
        $randLetters = [chr(rand(65,90)), chr(rand(97,122))];
        $randLetter = $randLetters[rand(0,1)];
        $randPosition = rand(0, strlen($textEntry));

        return substr_replace($textEntry, $randLetter, $randPosition, 0);
    }

    /**
     * @param Profile $profile
     * @return Profile
     * @throws PeriodicCheckProfileHandlerException
     */
    protected function manipulateDisplayName(Profile $profile): Profile
    {
        if ($profile->getFirstName()) {
            return $profile->setFirstName($this->manipulate((string) $profile->getFirstName()));
        }

        if ($profile->getMiddleName()) {
            return $profile->setMiddleName($this->manipulate((string) $profile->getMiddleName()));
        }

        if ($profile->getLastName()) {
            return $profile->setLastName($this->manipulate((string) $profile->getLastName()));
        }

        throw new PeriodicCheckProfileHandlerException('Profile does not have either name or display name.');
    }
}
