<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use DateTime;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Evp\Bundle\BlacklistBundle\Entity\AlternativeBirthday;
use Evp\Bundle\BlacklistBundle\Entity\AlternativeName;
use Evp\Bundle\BlacklistBundle\Entity\BankCode;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\CategoryRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Exception;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Bridge\Doctrine\RegistryInterface;

class StaticBlacklistImporter
{
    private ProfileRepository $profileRepository;
    private EntityManager $entityManager;
    private BlacklistRepository $blacklistRepository;
    private CategoryRepository $categoryRepository;
    private ProfileManager $profileManager;
    private string $blacklistName;
    private RegistryInterface $registry;
    private ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository;
    private LoggerInterface $logger;

    public function __construct(
        ProfileRepository $profileRepository,
        EntityManager $entityManager,
        BlacklistRepository $blacklistRepository,
        CategoryRepository $categoryRepository,
        ProfileManager $profileManager,
        RegistryInterface $registry,
        ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository,
        LoggerInterface $logger,
        string $blacklistName
    ) {
        $this->profileRepository = $profileRepository;
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
        $this->categoryRepository = $categoryRepository;
        $this->blacklistName = $blacklistName;
        $this->profileManager = $profileManager;
        $this->registry = $registry;
        $this->clientBlacklistCheckResultItemRepository = $clientBlacklistCheckResultItemRepository;
        $this->logger = $logger;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function process(Blacklist $blacklist, ?string $newBlacklistKey = null)
    {
        $updatedProfiles = [];
        $blacklistedProfileList = $this->profileRepository->findAllByBlacklistKey($blacklist->getKey());
        $newBlacklist = $this->getBlacklist($newBlacklistKey);

        if ($newBlacklistKey !== null && $newBlacklist === null) {
            $blacklist = new Blacklist(
                $newBlacklistKey,
                $this->blacklistName
            );
            $this->entityManager->persist($blacklist);
            $this->saveBlacklist($blacklist);
        } elseif ($newBlacklist !== null) {
            $blacklist = $newBlacklist;
        }

        $this->entityManager->beginTransaction();

        $version = intval((new DateTime())->format('YmdHi'));
        $blacklist->setVersion($version);

        try {
            foreach ($blacklistedProfileList as $record) {
                $isUpdatedProfile = array_key_exists($record['externalId'], $updatedProfiles);
                $newProfile = $isUpdatedProfile
                    ? $updatedProfiles[$record['externalId']]
                    : $this->createNewRecord($blacklist, $record)
                ;

                if ($newBlacklistKey === null) {
                    $profile = $this->entityManager->getReference(Profile::class, $record['id']);
                    $clientBlacklistCheckResultItems = $this->clientBlacklistCheckResultItemRepository
                        ->findByBlacklistProfile($profile)
                    ;
                    foreach ($clientBlacklistCheckResultItems as $clientBlacklistCheckResultItem) {
                        $clientBlacklistCheckResultItem->setProfile($newProfile);
                    }
                    $this->profileManager->deleteProfile($profile);
                }

                if (!$isUpdatedProfile) {
                    $updatedProfiles[$record['externalId']] = $newProfile;
                }
            }
            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (Exception $exception) {
            $this->entityManager->rollback();
            throw $exception;
        }
    }

    private function createNewRecord(Blacklist $blacklist, array $copiedRecord): Profile
    {
        $profile = (new Profile())
            ->setName($copiedRecord['name'])
            ->setFirstName($copiedRecord['firstName'])
            ->setMiddleName($copiedRecord['middleName'])
            ->setLastName($copiedRecord['lastName'])
            ->setBirthday($copiedRecord['birthday'])
            ->setCode($copiedRecord['code'])
            ->setCountry($copiedRecord['country'])
            ->setInfo($copiedRecord['info'])
            ->setExternalId($copiedRecord['externalId'])
            ->setPhone($copiedRecord['phone'])
            ->setType($copiedRecord['type'])
            ->setBlacklist($blacklist)
            ->setVersion($blacklist->getVersion())
        ;

        $categories = [];
        foreach ($copiedRecord['categories'] as $categoryData) {
            $category = $this->getCategory($categoryData['id']);
            if (!$category) {
                $this->logger->info('No category in StaticBlacklistImporter.', [$copiedRecord]);
                continue;
            }
            $categories[] = $category;
        }
        $profile->setCategories($categories);

        foreach ($copiedRecord['alternativeNames'] as $alternativeName) {
            $newAlternateName = (new AlternativeName())
                ->setName($alternativeName['name'])
                ->setFirstName($alternativeName['firstName'])
                ->setMiddleName($alternativeName['middleName'])
                ->setLastName($alternativeName['lastName'])
            ;
            $profile->addAlternativeName($newAlternateName);
        }

        foreach ($copiedRecord['bankCodes'] as $bankCode) {
            $newBankCode = (new BankCode())
                ->setCode($bankCode['code'])
            ;
            $profile->addBankCode($newBankCode);
        }

        foreach ($copiedRecord['alternativeBirthdays'] as $alternativeBirthday) {
            $alternativeBirthday = (new AlternativeBirthday())
                ->setDate($alternativeBirthday['date'])
            ;
            $profile->addAlternativeBirthday($alternativeBirthday);
        }
        $this->profileManager->persistProfile($profile);

        return $profile;
    }

    private function getBlacklist(?string $blacklistId): ?Blacklist
    {
        if ($blacklistId === null) {
            return null;
        }
        $blacklist = $this->blacklistRepository->findOneByKey($blacklistId);
        if (!$this->entityManager->contains($blacklist)) {
            /** @var BlacklistRepository $repository */
            $repository = $this->entityManager->getRepository(Blacklist::class);
            $blacklist = $repository->findOneByKey($blacklistId);
        }
        return $blacklist;
    }

    private function getCategory(int $categoryId): ?Category
    {
        $category = $this->categoryRepository->findOneById($categoryId);
        if (!$this->entityManager->contains($category)) {
            /** @var CategoryRepository $repository */
            $repository = $this->entityManager->getRepository(Category::class);
            $category = $repository->findOneById($categoryId);
        }
        return $category;
    }

    /**
     * @throws ORMException
     */
    private function saveBlacklist(Blacklist $blacklist, int $retry = 0)
    {
        try {
            $this->entityManager->flush();
        } catch (Exception $exception) {
            if ($retry > 3) {
                throw $exception;
            }
            if (!$this->entityManager->isOpen()) {
                $this->registry->resetManager();
                $this->entityManager->persist($blacklist);
                $this->saveBlacklist($blacklist, ++$retry);
            }
        }
    }
}
