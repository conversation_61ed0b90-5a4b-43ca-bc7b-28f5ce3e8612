<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\BlacklistProfileFilter;

use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;

interface ProfileFilterInterface
{
    /**
     * Applies the filter to the matched profiles.
     *
     * @param Profile $profile
     * @param BlacklistResult[] $matchedProfiles
     * @param ProfileFilterContext $context
     *
     * @return BlacklistResult[]
     */
    public function apply(Profile $profile, array $matchedProfiles, ProfileFilterContext $context): array;
}
