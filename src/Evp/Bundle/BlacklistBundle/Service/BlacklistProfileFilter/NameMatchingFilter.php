<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\BlacklistProfileFilter;

use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\NamesComparisonResultService;
use Exception;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;
use Paysera\Component\AIService\AINameComparisonService;
use Psr\Log\LoggerInterface;

class NameMatchingFilter implements ProfileFilterInterface
{
    private AINameComparisonService $naturalNameComparisonService;
    private AINameComparisonService $legalNameComparisonService;
    private NamesComparisonResultService $namesComparisonResultService;
    private LoggerInterface $logger;

    public function __construct(
        AINameComparisonService $naturalNameComparisonService,
        AINameComparisonService $legalNameComparisonService,
        NamesComparisonResultService $namesComparisonResultService,
        LoggerInterface $logger
    ) {
        $this->naturalNameComparisonService = $naturalNameComparisonService;
        $this->legalNameComparisonService = $legalNameComparisonService;
        $this->namesComparisonResultService = $namesComparisonResultService;
        $this->logger = $logger;
    }

    public function apply(Profile $profile, array $matchedProfiles, ProfileFilterContext $context): array
    {
        $filteredProfiles = [];

        foreach ($matchedProfiles as $matchedProfile) {
            $foundProfile = $matchedProfile->getFoundProfile();

            if ($this->isProfileMatching(
                $profile->getDisplayName(),
                $foundProfile->getDisplayName(),
                $profile->getType(),
                $context
            )) {
                $filteredProfiles[] = $matchedProfile;
                continue;
            }

            if (!$context->shouldIgnoreAlternativeNames()) {
                foreach ($foundProfile->getAlternativeNames() as $alternativeName) {
                    if ($this->isProfileMatching(
                        $profile->getDisplayName(),
                        $alternativeName->getDisplayName(),
                        $profile->getType(),
                        $context
                    )) {
                        $filteredProfiles[] = $matchedProfile;
                        continue 2;
                    }
                }
            }
        }

        return $filteredProfiles;
    }

    private function isProfileMatching(
        string $profileName,
        string $otherName,
        string $profileType,
        ProfileFilterContext $context
    ): bool {
        $nameComparisonService = $profileType === Profile::TYPE_PERSON
            ? $this->naturalNameComparisonService
            : $this->legalNameComparisonService
        ;

        $namesComparisonResult = $nameComparisonService->askAI($profileName, $otherName);

        try {
            $this->namesComparisonResultService->saveComparisonResults($namesComparisonResult, $context);
        } catch (Exception $e) {
            $this->logger->error(
                'Failed to save name comparison results',
                [
                    'profile_name' => $profileName,
                    'other_name' => $otherName,
                    'error' => $e->getMessage(),
                ]
            );
        }

        return $namesComparisonResult->getDecision() !== NamesComparisonResultDto::DECISION_DIFFERENT_PERSON;
    }
}
