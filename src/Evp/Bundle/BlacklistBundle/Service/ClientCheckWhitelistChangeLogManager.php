<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelistChangeLog;
use Paysera\Bundle\SecurityBundle\Entity\ScopesAwareUser;
use Psr\Log\LoggerInterface;
use Sonata\UserBundle\Entity\BaseUser;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;

class ClientCheckWhitelistChangeLogManager
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;
    private TokenStorage $tokenStorage;

    public function __construct(
        EntityManagerInterface $entityManager,
        LoggerInterface $logger,
        TokenStorage $tokenStorage
    ) {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
        $this->tokenStorage = $tokenStorage;
    }

    public function log(
        ClientCheckWhitelist $clientCheckWhitelist,
        string $action
    ): void {
        $userId = $this->getUserId();

        switch ($action) {
            case ClientCheckWhitelistChangeLog::ACTION_CREATE:
            case ClientCheckWhitelistChangeLog::ACTION_DELETE:
                $this->trackAndLogChanges($clientCheckWhitelist, null, $userId, $action);
                break;
            case ClientCheckWhitelistChangeLog::ACTION_MODIFY:
                $originalData = $this->entityManager->getUnitOfWork()->getOriginalEntityData($clientCheckWhitelist);
                $this->trackAndLogChanges($clientCheckWhitelist, $originalData, $userId, $action);
                break;
            default:
                break;
        }
    }

    private function trackAndLogChanges(
        ClientCheckWhitelist $clientCheckWhitelist,
        ?array $originalData,
        int $userId,
        string $action
    ): void {
        $classMetadata = $this->entityManager->getClassMetadata(ClientCheckWhitelist::class);
        $fieldNames = array_merge(
            $classMetadata->getFieldNames(),
            $classMetadata->getAssociationNames()
        );
        $isUpdate = $originalData !== null;

        foreach ($fieldNames as $fieldName) {
            if (!in_array($fieldName, ClientCheckWhitelistChangeLog::TRACKABLE_FIELDS, true)) {
                continue;
            }

            $fieldValue = $classMetadata->getFieldValue($clientCheckWhitelist, $fieldName);
            $updatedFieldValue = $isUpdate ? $originalData[$fieldName] ?? null : null;

            if ($fieldValue === $updatedFieldValue) {
                continue;
            }

            $trackedFieldValue = $isUpdate ? $updatedFieldValue : $fieldValue;

            if (is_bool($trackedFieldValue)) {
                $trackedFieldValue = $trackedFieldValue ? 'true' : 'false';
            }
            if (is_object($trackedFieldValue) && method_exists($trackedFieldValue, 'getId')) {
                $trackedFieldValue = (string)call_user_func([$trackedFieldValue, 'getId']);
            }

            if (!is_string($trackedFieldValue)) {
                $this->logger->info('Failed to create client check whitelist change log, value is not string', [$clientCheckWhitelist->getId()]);
                continue;
            }

            $log = $this->createLog(
                $clientCheckWhitelist,
                $action,
                $userId
            );

            $log
                ->setField($fieldName)
                ->setValue($trackedFieldValue)
            ;

            $this->entityManager->persist($log);
        }
    }

    private function createLog(
        ClientCheckWhitelist $clientCheckWhitelist,
        string $action,
        int $userId
    ): ClientCheckWhitelistChangeLog {
        return (new ClientCheckWhitelistChangeLog())
            ->setClientCheckWhitelist($clientCheckWhitelist->getId() ?? null)
            ->setDate(new DateTimeImmutable())
            ->setAction($action)
            ->setUserId($userId)
        ;
    }

    private function getUserId(): int
    {
        $token = $this->tokenStorage->getToken();
        $user = $token !== null ? $token->getUser() : null;

        if ($user instanceof BaseUser) {
            return $user->getId();
        }

        if ($user instanceof ScopesAwareUser) {
            return (int) $user->getUsername();
        }

        return 0;
    }
}
