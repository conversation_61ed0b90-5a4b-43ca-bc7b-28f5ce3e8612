<?php

namespace Evp\Bundle\BlacklistBundle\Service\Provider;

use Evp\Bundle\BlacklistBundle\Entity\ProfileRelationship;
use Evp\Bundle\BlacklistBundle\Entity\ProfileRole;
use Evp\Bundle\BlacklistBundle\Entity\ProfileSanctionsReference;
use Evp\Bundle\BlacklistBundle\Entity\ProviderCategory;
use Evp\Bundle\BlacklistBundle\Entity\ProviderProfile;
use Evp\Bundle\BlacklistBundle\Service\CategoriesTreeBuilder;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\AssociationRelationshipXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category1XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category2XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\Category3XmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\EntityAssociationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\EntityProfileXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\OccupationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\PersonAssociationXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\PersonProfileXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParser\SanctionsReferenceXmlParser;
use Evp\Bundle\BlacklistBundle\Service\Parser\DowJonesParserResolver;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Generator;

class DowJonesProvider
{
    private $blacklistKey;
    private $blacklistName;
    private $parserResolver;
    private $categoriesTreeBuilder;
    private $feedReader;

    /**
     * @param string $blacklistName
     * @param string $blacklistKey
     * @param DowJonesParserResolver $parserResolver
     * @param CategoriesTreeBuilder $categoriesTreeBuilder
     * @param DowJonesFeedReader $feedReader
     */
    public function __construct(
        $blacklistName,
        $blacklistKey,
        DowJonesParserResolver $parserResolver,
        CategoriesTreeBuilder $categoriesTreeBuilder,
        DowJonesFeedReader $feedReader
    ) {
        $this->blacklistName = $blacklistName;
        $this->blacklistKey = $blacklistKey;
        $this->parserResolver = $parserResolver;
        $this->categoriesTreeBuilder = $categoriesTreeBuilder;
        $this->feedReader = $feedReader;
    }

    /**
     * @return string
     */
    public function getBlacklistKey()
    {
        return $this->blacklistKey;
    }

    /**
     * @return string
     */
    public function getBlacklistName()
    {
        return $this->blacklistName;
    }

    /**
     * @param int $version
     * @return ProviderProfile[]|Generator
     */
    public function getProfiles($version)
    {
        $feedFile = $this->feedReader->getFeedFileByVersion($version);
        $parserClasses = [
            PersonProfileXmlParser::class,
            EntityProfileXmlParser::class,
        ];

        $occupationCategories = $this->getOccupations($feedFile);
        $sanctionsReferenceDetails = $this->getSanctionsReferenceDetails($feedFile);

        foreach ($parserClasses as $parserClass) {
            $parser = $this->parserResolver->getParserByClassName($parserClass);
            foreach ($this->feedReader->readFeedFile($feedFile, $parser) as $providerProfile) {
                $this->resolveOccupationCategory($providerProfile->getRoles(), $occupationCategories);
                $this->resolveSanctionReferenceDetails(
                    $providerProfile->getSanctionsReferences(),
                    $sanctionsReferenceDetails
                );

                yield $providerProfile;
            }
        }
    }

    /**
     * @param int $version
     * @return null|int
     */
    public function getVersionToImport($version, bool $force)
    {
        return $this->feedReader->resolveFeedFileVersion($version, $force);
    }

    /**
     * @param int $version
     * @return ProviderCategory[]
     */
    public function getCategories($version)
    {
        $feedFile = $this->feedReader->getFeedFileByVersion($version);
        $parserClasses = [
            Category1XmlParser::class,
            Category2XmlParser::class,
            Category3XmlParser::class,
        ];
        /** @var ProviderCategory[] $providerCategories */
        $providerCategories = [];
        foreach ($parserClasses as $parserClass) {
            $parser = $this->parserResolver->getParserByClassName($parserClass);
            foreach ($this->feedReader->readFirstElementFromFeedFile($feedFile, $parser) as $providerCategory) {
                $providerCategories[] = $providerCategory;
            }
        }

        return $this->categoriesTreeBuilder->build($providerCategories);
    }

    public function getAssociations(int $version): Generator
    {
        $feedFile = $this->feedReader->getFeedFileByVersion($version);
        $parserClasses = [
            PersonAssociationXmlParser::class,
            EntityAssociationXmlParser::class,
        ];

        foreach ($parserClasses as $parserClass) {
            $parser = $this->parserResolver->getParserByClassName($parserClass);

            foreach ($this->feedReader->readFirstElementFromFeedFile($feedFile, $parser) as $associationsData) {
                yield $associationsData;
            }
        }
    }

    public function getSanctionsReferenceDetails($feedFile): array
    {
        $referenceDetails = [];
        $parser = $this->parserResolver->getParserByClassName(SanctionsReferenceXmlParser::class);

        foreach ($this->feedReader->readFirstElementFromFeedFile($feedFile, $parser) as $referenceDetail) {
            if (count($referenceDetail) < 1) {
                continue;
            }

            $referenceDetails = $referenceDetails + $referenceDetail;
        }

        return $referenceDetails;
    }

    public function getRelationships(int $version): Generator
    {
        $feedFile = $this->feedReader->getFeedFileByVersion($version);
        $parser = $this->parserResolver->getParserByClassName(AssociationRelationshipXmlParser::class);

        foreach ($this->feedReader->readFirstElementFromFeedFile($feedFile, $parser) as $relationship) {
            yield $relationship;
        }
    }

    public function getOccupations($feedFile): array
    {
        $occupations = [];
        $parser = $this->parserResolver->getParserByClassName(OccupationXmlParser::class);

        foreach ($this->feedReader->readFirstElementFromFeedFile($feedFile, $parser) as $occupation) {
            if (count($occupation) < 1) {
                continue;
            }

            $occupations = $occupations + $occupation;
        }

        return $occupations;
    }

    private function resolveSanctionReferenceDetails(array $sanctionsReferences, array $details): void
    {
        /** @var ProfileSanctionsReference $sanctionsReference */
        foreach ($sanctionsReferences as $sanctionsReference) {
            $code = $sanctionsReference->getDescriptionCode();
            $sanctionsReference
                ->setStatus($details[$code]['status'] ?? null)
                ->setName($details[$code]['name'] ?? null)
                ->setDescription($details[$code]['description2Id'] ?? null)
            ;
        }
    }

    private function resolveOccupationCategory(array $roles, array $occupations): void
    {
        /** @var ProfileRole $role */
        foreach ($roles as $role) {
            $role->setCategory($occupations[$role->getCategoryCode()] ?? null);
        }
    }
}
