<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelistChangeLog;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\ClientCheckWhitelistRepository;
use Evp\Bundle\ClientBundle\Entity\Client;

class ClientCheckWhitelistManager
{
    private EntityManagerInterface $entityManager;
    private ClientCheckWhitelistRepository $clientCheckWhitelistRepository;
    private ClientCheckWhitelistChangeLogManager $clientCheckWhitelistChangeLogManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        ClientCheckWhitelistRepository $clientCheckWhitelistRepository,
        ClientCheckWhitelistChangeLogManager $clientCheckWhitelistChangeLogManager
    ) {
        $this->entityManager = $entityManager;
        $this->clientCheckWhitelistRepository = $clientCheckWhitelistRepository;
        $this->clientCheckWhitelistChangeLogManager = $clientCheckWhitelistChangeLogManager;
    }

    public function createIfNotExists(Client $client, Profile $profile, CategoryGroup $categoryGroup): void
    {
        // Added type externalId casting to string because profile can contain int value due to no strict type hinting
        $clientCheckWhitelist = (new ClientCheckWhitelist())
            ->setClient($client)
            ->setBlacklist($profile->getBlacklist())
            ->setProfileExternalId((string) $profile->getExternalId())
            ->setCategoryGroup($categoryGroup)
        ;

        if ($this->clientCheckWhitelistRepository->isClientCheckWhitelistExists($clientCheckWhitelist)) {
            return;
        }

        $this->entityManager->persist($clientCheckWhitelist);
        $this->clientCheckWhitelistChangeLogManager->log(
            $clientCheckWhitelist,
            ClientCheckWhitelistChangeLog::ACTION_CREATE
        );
    }

    public function removeAll(Client $client): void
    {
        $clientCheckWhitelists = $this->clientCheckWhitelistRepository->findAllByClient($client);

        foreach ($clientCheckWhitelists as $clientCheckWhitelist) {
            $this->clientCheckWhitelistChangeLogManager->log(
                $clientCheckWhitelist,
                ClientCheckWhitelistChangeLog::ACTION_DELETE
            );
            $this->entityManager->remove($clientCheckWhitelist);
        }
    }

    public function removeAllByBlacklistAndProfileExternalId(Blacklist $blacklist, string $profileExternalId): void
    {
        $clientCheckWhitelists = $this->clientCheckWhitelistRepository->findAllByBlacklistAndProfileExternalId(
            $blacklist,
            $profileExternalId
        );

        foreach ($clientCheckWhitelists as $clientCheckWhitelist) {
            $this->clientCheckWhitelistChangeLogManager->log(
                $clientCheckWhitelist,
                ClientCheckWhitelistChangeLog::ACTION_DELETE
            );
            $this->entityManager->remove($clientCheckWhitelist);
        }
    }

    public function syncWithProfile(Profile $profile): void
    {
        // Added type externalId casting to string because profile can contain int value due to no strict type hinting
        $clientCheckWhitelists = $this->clientCheckWhitelistRepository->findAllByBlacklistAndProfileExternalId(
            $profile->getBlacklist(),
            (string) $profile->getExternalId()
        );
        $profileCategoryGroupIds = $profile->getCategoryGroupIds();

        $whitelistsGroupedByClient = [];
        $whitelistsGroupIdsGroupedByClient = [];

        foreach ($clientCheckWhitelists as $clientCheckWhitelist) {
            if ($clientCheckWhitelist->getClient() === null) {
                continue;
            }

            $clientId = $clientCheckWhitelist->getClient()->getId();

            $whitelistsGroupedByClient[$clientId][] = $clientCheckWhitelist;
            $whitelistsGroupIdsGroupedByClient[$clientId][] = $clientCheckWhitelist->getCategoryGroup()->getId();
        }

        foreach ($whitelistsGroupedByClient as $clientId => $whitelists) {
            $whitelistCategoryGroupIds = $whitelistsGroupIdsGroupedByClient[$clientId];
            $this->syncClientWhitelistsWithProfile($whitelists, $whitelistCategoryGroupIds, $profileCategoryGroupIds);
        }
    }

    /**
     * @param ClientCheckWhitelist[] $whitelists
     * @param int[] $whitelistsGroupIds
     * @param int[] $profileGroupIds
     */
    private function syncClientWhitelistsWithProfile(
        array $whitelists,
        array $whitelistsGroupIds,
        array $profileGroupIds
    ): void {
        $isProfileGroupIdsAdded = count(array_diff($profileGroupIds, $whitelistsGroupIds)) > 0;
        if ($isProfileGroupIdsAdded) {
            foreach ($whitelists as $whitelist) {
                $this->clientCheckWhitelistChangeLogManager->log(
                    $whitelist,
                    ClientCheckWhitelistChangeLog::ACTION_DELETE
                );
                $this->entityManager->remove($whitelist);
            }
            return;
        }

        foreach ($whitelists as $whitelist) {
            if (!in_array($whitelist->getCategoryGroup()->getId(), $profileGroupIds)) {
                $this->clientCheckWhitelistChangeLogManager->log(
                    $whitelist,
                    ClientCheckWhitelistChangeLog::ACTION_DELETE
                );
                $this->entityManager->remove($whitelist);
            }
        }
    }
}
