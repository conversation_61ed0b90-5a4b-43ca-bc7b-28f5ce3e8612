<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use Evp\Bundle\BlacklistBundle\DTO\AlternativeNameSearchResult;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientSearchResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Exception\DowJonesAiManagerException;
use Evp\Bundle\ClientBundle\Entity\Client;
use Exception;
use Paysera\Client\DowJonesAi\DowJonesAi;
use Paysera\Client\DowJonesAi\Entity\DowJonesAIRequest;
use Paysera\Client\DowJonesAi\Entity\ProfileScoring;
use Paysera\Client\DowJonesAi\Entity\TransferDatum;
use Psr\Log\LoggerInterface;

class DowJonesAiManager
{
    private const SUGGESTED_SEARCH_PROFILE_ALTERNATIVE_NAME_LENGTH = 4;

    private DowJonesAi $dowJonesAiClient;
    private string $aiConfidence;
    private LoggerInterface $logger;

    public function __construct(
        DowJonesAi $dowJonesAiClient,
        string $aiConfidence,
        LoggerInterface $logger
    ) {
        $this->dowJonesAiClient = $dowJonesAiClient;
        $this->aiConfidence = $aiConfidence;
        $this->logger = $logger;
    }

    /**
     * @param string $searchTransferDatumId
     * @param Profile $searchProfile
     * @param array $matchedResults
     * @param string|null $aiConfidence
     * @param bool $ignoreAlternativeNames
     * @return array|BlacklistResult[]
     */
    public function filterOutNotConfidentDowJonesProfiles(
        string $searchTransferDatumId,
        Profile $searchProfile,
        array $matchedResults,
        ?string $aiConfidence = null,
        bool $ignoreAlternativeNames = false
    ): array {
        if (count($matchedResults) === 0) {
            return $matchedResults;
        }

        try {
            $searchTransferDatum = $this->prepareTransferDatum(
                $searchProfile,
                $searchTransferDatumId
            );

            /**
             * @TODO Investigate details of SUPPORT-91494 and SUPPORT-94708
             * After UI and DJ AI ready to display the information about real beneficiaries and profile alternative
             * names (see details in tickets related to SUPPORT-94708) - we should enable profile alternative names
             * check again.
             */
            if ($ignoreAlternativeNames === false) {
                $alternativeNamesSearchResults = $this->prepareAlternativeNamesSearchResults(
                    $searchProfile,
                    $matchedResults
                );

                $searchResults = array_merge(
                    $this->prepareSearchResults($matchedResults),
                    array_map(
                        fn(AlternativeNameSearchResult $searchResult) => $searchResult->getTransferDatum(),
                        $alternativeNamesSearchResults
                    )
                );
            } else {
                $searchResults = $this->prepareSearchResults($matchedResults);
            }

            $dowJonesAiProfileScores = $this->getDowJonesAiResult(
                $searchTransferDatum,
                $searchResults
            );

            $this->logger->debug(
                'DowJonesAiManager: debugging dow jones ai profile scores',
                [
                    [$searchTransferDatum->getData()],
                    $this->getDenormalizedProfileScores($dowJonesAiProfileScores, $searchResults)
                ]
            );

            if ($ignoreAlternativeNames === false) {
                // changing the temporary ids to the real profile ids in order to filter the results by confidence
                $this->adjustAlternativeNamesProfileIds($alternativeNamesSearchResults, $dowJonesAiProfileScores);
            }
        } catch (DowJonesAiManagerException $exception) {
            $this->logger->warning(
                'AI client error, DJ ES results not filtered by confidence!',
                [
                    $exception->getMessage(),
                    $searchTransferDatumId,
                ]
            );

            return $matchedResults;
        }

        return $this->filterOutMatchedResultsByConfidence(
            $matchedResults,
            $dowJonesAiProfileScores,
            $aiConfidence
        );
    }

    /**
     * @param ProfileScoring[] $profileScores
     * @param TransferDatum[] $searchResults
     *
     * @return array
     */
    private function getDenormalizedProfileScores(array $profileScores, array $searchResults): array
    {
        return array_map(
            fn(ProfileScoring $profileScoring, TransferDatum $transferDatum) => [
                'transfer-datum-id' => $profileScoring->getTransferDatumId(),
                'search-result-id' => $profileScoring->getSearchResultId(),
                'confidence' => $profileScoring->getConfidence(),
                'search-result-data' => $transferDatum->getData(),
            ],
            $profileScores,
            $searchResults
        );
    }

    /**
     * @param Profile $profile
     * @param ClientSearchResult[] $matchedResults
     * @param string|null $alternativeName
     *
     * @return ClientSearchResult[]
     */
    public function filterOutNotConfidentClients(
        Profile $profile,
        array $matchedResults,
        ?string $alternativeName = null
    ): array {
        if (count($matchedResults) === 0) {
            return $matchedResults;
        }

        try {
            $searchTransferDatum = $this->prepareTransferDatum(
                $profile,
                (string)$profile->getId(),
                $alternativeName ?? $profile->getDisplayName()
            );

            $searchResults = $this->prepareSearchResultsClient($matchedResults);

            $dowJonesAiProfileScores = $this->getDowJonesAiResult(
                $searchTransferDatum,
                $searchResults
            );

            foreach ($matchedResults as $matchedResult) {
                if (count($matchedResult->getRealBeneficiaries()) === 0) {
                    continue;
                }
                $id = 0;
                $searchResults = [];
                foreach ($matchedResult->getRealBeneficiaries() as $realBeneficiary) {
                    $searchResults[] = (new TransferDatum())
                        ->setId((string)++$id)
                        ->setName($realBeneficiary['name'])
                    ;
                }
                $realBeneficiaryScores = $this->getDowJonesAiResult(
                    $searchTransferDatum,
                    $searchResults
                );

                foreach ($realBeneficiaryScores as $realBeneficiaryScore) {
                    if ($matchedResult->getConfidence() < $realBeneficiaryScore->getConfidence()) {
                        $matchedResult->setConfidence($realBeneficiaryScore->getConfidence());
                    }
                }
            }
        } catch (DowJonesAiManagerException $exception) {
            $this->logger->warning(
                'AI client error, DJ ES results not filtered by confidence!',
                [
                    $exception->getMessage(),
                ]
            );

            return $matchedResults;
        }

        $finalResults = [];
        foreach ($matchedResults as $matchedResult) {
            foreach ($dowJonesAiProfileScores as $dowJonesAiProfileScore) {
                if (
                    $matchedResult->getId() === (int) $dowJonesAiProfileScore->getSearchResultId()
                    && $dowJonesAiProfileScore->getConfidence() >= (int) $this->aiConfidence
                ) {
                    $finalResults[] = $matchedResult->setConfidence($dowJonesAiProfileScore->getConfidence());

                    continue 2;
                }
            }
        }

        return $finalResults;
    }

    /**
     * @param BlacklistResult[] $matchedResults
     * @param ProfileScoring[] $dowJonesAiProfileScores
     * @param string|null $aiConfidence
     *
     * @return BlacklistResult[]
     */
    private function filterOutMatchedResultsByConfidence(
        array $matchedResults,
        array $dowJonesAiProfileScores,
        ?string $aiConfidence = null
    ): array {
        $finalResults = [];

        foreach ($matchedResults as $matchedResult) {
            foreach ($dowJonesAiProfileScores as $dowJonesAiProfileScore) {
                if (
                    $matchedResult->getFoundProfile()->getId() === (int) $dowJonesAiProfileScore->getSearchResultId()
                    && $dowJonesAiProfileScore->getConfidence() >= ((int) ($aiConfidence ?? $this->aiConfidence))
                ) {
                    $finalResults[] = $matchedResult->setConfidence((int) $dowJonesAiProfileScore->getConfidence());

                    continue 2;
                }
            }
        }

        return $finalResults;
    }

    /**
     * @param array $dowJonesAiCheckResult
     *
     * @return ProfileScoring[]
     */
    private function getProfileScoring(array $dowJonesAiCheckResult): array
    {
        return array_map(
            function (array $dowJonesAiProfileScoringArray) {
                return new ProfileScoring($dowJonesAiProfileScoringArray);
            },
            $dowJonesAiCheckResult
        );
    }

    /**
     * @param BlacklistResult[] $matchedResults
     *
     * @return TransferDatum[]
     */
    private function prepareSearchResults(
        array $matchedResults
    ): array {
        return array_map(
            function (BlacklistResult $blacklistResult) {
                return $this->prepareTransferDatum(
                    $blacklistResult->getFoundProfile(),
                    (string) $blacklistResult->getFoundProfile()->getId()
                );
            },
            $matchedResults
        );
    }

    private function prepareSearchResultsClient(array $matchedResults): array
    {
        return array_map(
            function (ClientSearchResult $client) {
                if ($client->getType() === Client::TYPE_NATURAL) {
                    $displayName = sprintf("%s %s", $client->getFirstName(), $client->getLastName());
                } else {
                    $displayName = $client->getLegalName();
                }
                return (new TransferDatum())
                    ->setId((string)$client->getId())
                    ->setName($displayName)
                ;
            },
            $matchedResults
        );
    }

    private function prepareTransferDatum(
        Profile $profile,
        string $transferDatumId,
        ?string $alternativeName = null
    ): TransferDatum {
        return (new TransferDatum())
            ->setId($transferDatumId)
            ->setName($alternativeName ?? $profile->getDisplayName())
        ;
    }

    /**
     * @param TransferDatum $searchTransferDatum
     * @param TransferDatum[] $searchResults
     *
     * @return ProfileScoring[]
     *
     * @throws DowJonesAiManagerException
     */
    private function getDowJonesAiResult(
        TransferDatum $searchTransferDatum,
        array $searchResults
    ): array {
        try {
            $dowJonesAiResultItems = $this->dowJonesAiClient
                ->createScore(
                    (new DowJonesAIRequest())
                        ->setTransferDatum($searchTransferDatum)
                        ->setSearchResults($searchResults)
                )
                ->getItems()
            ;
        } catch (Exception $exception) {
            $this->logger->error(
                'Cannot fetch data from Dow Jones AI',
                [
                    $exception->getMessage(),
                ]
            );
            throw new DowJonesAiManagerException(
                sprintf(
                    'Cannot fetch data from Dow Jones AI. Search data: id - %s, name - %s',
                    $searchTransferDatum->getId(),
                    $searchTransferDatum->getName()
                )
            );
        }

        if (!is_array($dowJonesAiResultItems)) {
            throw new DowJonesAiManagerException(
                sprintf(
                    'Something wrong with response format from Dow Jones AI. Search data: id - %s, name - %s',
                    $searchTransferDatum->getId(),
                    $searchTransferDatum->getName()
                )
            );
        }

        return $this->getProfileScoring($dowJonesAiResultItems);
    }

    /**
     * @param Profile $searchProfile
     * @param BlacklistResult[] $matchedResults
     *
     * @return AlternativeNameSearchResult[]
     */
    private function prepareAlternativeNamesSearchResults(
        Profile $searchProfile,
        array $matchedResults
    ): array {
        $searchResults = [];
        $searchProfileName = $searchProfile->getDisplayName();

        foreach ($matchedResults as $matchedResult) {
            $foundProfile = $matchedResult->getFoundProfile();

            foreach ($foundProfile->getAlternativeNames() as $alternativeName) {
                $name = $alternativeName->getDisplayName();

                if (
                    $name === null
                    || (
                        $searchProfileName !== null
                        && mb_strlen($name) < self::SUGGESTED_SEARCH_PROFILE_ALTERNATIVE_NAME_LENGTH
                        && mb_strlen($searchProfileName) >= self::SUGGESTED_SEARCH_PROFILE_ALTERNATIVE_NAME_LENGTH
                    )
                ) {
                    continue;
                }

                // generating a temporary unique id for a request to the dow-jones-ai app
                $datumId = $this->prepareAlternativeNameTransferDatumId(
                    $foundProfile->getId(),
                    $alternativeName->getId()
                );
                $transferDatum = $this->prepareTransferDatum(
                    $foundProfile,
                    $datumId,
                    $name
                );

                $searchResults[] = new AlternativeNameSearchResult($transferDatum, (string) $foundProfile->getId());
            }
        }

        return $searchResults;
    }

    private function prepareAlternativeNameTransferDatumId(int $blacklistProfileId, int $alternativeNameId): string
    {
        return md5(sprintf(
            '%d:%d',
            $blacklistProfileId,
            $alternativeNameId
        ));
    }

    /**
     * @param AlternativeNameSearchResult[] $alternativeNamesSearchResults
     * @param ProfileScoring[] $dowJonesAiProfileScores
     */
    private function adjustAlternativeNamesProfileIds(
        array $alternativeNamesSearchResults,
        array $dowJonesAiProfileScores
    ): void {
        if (count($alternativeNamesSearchResults) === 0) {
            return;
        }

        foreach ($dowJonesAiProfileScores as $score) {
            foreach ($alternativeNamesSearchResults as $alternativeNameSearchResult) {
                if ($score->getSearchResultId() === $alternativeNameSearchResult->getTransferDatum()->getId()) {
                    $score->setSearchResultId($alternativeNameSearchResult->getBlackListProfileId());

                    continue 2;
                }
            }
        }
    }
}
