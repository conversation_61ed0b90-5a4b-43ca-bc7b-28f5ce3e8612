<?php

namespace Evp\Bundle\BlacklistBundle\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;

class BlacklistUpdateManager
{
    private $entityManager;
    private $providerManager;
    private $blacklistRepository;

    public function __construct(
        EntityManager $entityManager,
        BlacklistRepository $blacklistRepository,
        ProviderManager $providerManager
    ) {
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
        $this->providerManager = $providerManager;
    }

    /**
     * Updates all blacklists
     */
    public function update()
    {
        $version = $this->generateVersion();
        foreach ($this->providerManager->getProviders() as $provider) {
            $blacklist = $this->blacklistRepository->findOneByKey($provider->getBlacklistKey());
            if ($blacklist === null) {
                $blacklist = new Blacklist($provider->getBlacklistKey(), $provider->getBlacklistName());
                $this->entityManager->persist($blacklist);
                $this->entityManager->flush();
            }

            $profiles = $provider->getProfiles();
            if (!empty($profiles)) {
                $this->save($provider, $profiles, $version);
            }

            $this->cleanOldVersion($blacklist, $version);
        }
    }

    /**
     * Generates version
     *
     * @return string
     */
    protected function generateVersion()
    {
        $dateFormat = new \DateTime();
        return $dateFormat->format('dHis') . rand(0, 9);
    }

    /**
     * Removes old data version
     *
     * @param Blacklist $blacklist
     * @param string $version
     */
    protected function cleanOldVersion(Blacklist $blacklist, $version)
    {
        $profilesQuery = $this->entityManager
            ->getRepository('EvpBlacklistBundle:Profile')
            ->findAllByBlacklistToBeCleaned(
                $blacklist,
                $version
            )
        ;

        $batchSize = 100;
        $i = 0;
        foreach ($profilesQuery->iterate() as $profile) {
            $this->entityManager->remove($profile[0]);
            $this->entityManager->detach($profile[0]);
            if (($i % $batchSize) == 0) {
                $this->entityManager->flush();
            }
            $i++;
        }

        $this->entityManager->flush();
    }

    /**
     * @param ProfileProviderInterface $provider
     * @param Profile[] $profiles
     * @param string $version
     *
     * @throws \InvalidArgumentException
     */
    protected function save(ProfileProviderInterface $provider, array $profiles, $version)
    {
        $counter = 0;
        $blacklist = null;
        foreach ($profiles as $profile) {
            if ($counter % 100 === 0) {
                if ($counter !== 0) {
                    $this->entityManager->flush();
                    $this->entityManager->clear();
                }
                $blacklist = $this->blacklistRepository->findOneByKey($provider->getBlacklistKey());
                if ($blacklist === null) {
                    throw new \InvalidArgumentException(
                        sprintf('BlackList provider not found by key (%s)', $provider->getBlacklistKey())
                    );
                }
            }
            $counter++;

            $profile->setBlacklist($blacklist);
            $profile->setVersion($version);
            $this->entityManager->persist($profile);
        }

        $this->entityManager->flush();
    }
}
