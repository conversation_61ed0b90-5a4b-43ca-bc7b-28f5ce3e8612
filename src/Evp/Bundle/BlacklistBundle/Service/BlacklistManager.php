<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use Doctrine\ORM\ORMException;
use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Repository\ClientTypePredictionRepository;
use Evp\Bundle\BankTransferBundle\Service\TransferRoutingInformationRetriever;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BankCodeRepository;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\CountryCodeAware;
use Evp\Bundle\QuestionnaireBundle\Entity\ClientLegal\RealBeneficiary;
use Evp\Component\RestClientCommon\Exception\NotFoundRestException;
use Evp\Component\UserRestClient\User\UserClient;
use Exception;
use FOS\ElasticaBundle\HybridResult;
use InvalidArgumentException;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferPayerExcludedChecker;
use Psr\Log\LoggerInterface;
use Doctrine\ORM\EntityManager;

class BlacklistManager
{
    private EntityManager $entityManager;
    private ProfileQueryBuilder $profileQueryBuilder;
    private BlacklistRepository $blacklistRepository;
    private TransferRoutingInformationRetriever $transferRoutingInformationRetriever;
    private AccountInfoResolver $accountInfoResolver;
    private SearchResultsProcessor $searchResultsProcessor;
    private BankCodeRepository $bankCodeRepository;
    private UserClient $userClient;
    private bool $enabled;
    private ConfigurationManager $configurationManager;
    private ClientTypePredictionRepository $clientTypePredictionRepository;
    private LoggerInterface $logger;
    private TransferPayerExcludedChecker $transferPayerExcludedChecker;
    private DowJonesAiManager $dowJonesAiManager;
    private SearchRequestManager $searchRequestManager;

    public function __construct(
        EntityManager $entityManager,
        ProfileQueryBuilder $profileQueryBuilder,
        BlacklistRepository $blacklistRepository,
        TransferRoutingInformationRetriever $transferRoutingInformationRetriever,
        AccountInfoResolver $accountInfoResolver,
        SearchResultsProcessor $searchResultsProcessor,
        BankCodeRepository $bankCodeRepository,
        UserClient $userClient,
        ConfigurationManager $configurationManager,
        ClientTypePredictionRepository $clientTypePredictionRepository,
        bool $enabled,
        LoggerInterface $logger,
        TransferPayerExcludedChecker $transferPayerExcludedChecker,
        DowJonesAiManager $dowJonesAiManager,
        SearchRequestManager $searchRequestManager
    ) {
        $this->entityManager = $entityManager;
        $this->profileQueryBuilder = $profileQueryBuilder;
        $this->blacklistRepository = $blacklistRepository;
        $this->transferRoutingInformationRetriever = $transferRoutingInformationRetriever;
        $this->accountInfoResolver = $accountInfoResolver;
        $this->searchResultsProcessor = $searchResultsProcessor;
        $this->bankCodeRepository = $bankCodeRepository;
        $this->userClient = $userClient;
        $this->configurationManager = $configurationManager;
        $this->clientTypePredictionRepository = $clientTypePredictionRepository;
        $this->enabled = $enabled;
        $this->logger = $logger;
        $this->transferPayerExcludedChecker = $transferPayerExcludedChecker;
        $this->dowJonesAiManager = $dowJonesAiManager;
        $this->searchRequestManager = $searchRequestManager;
    }

    /**
     * @param Blacklist $blacklist
     * @param Client $client
     *
     * @return bool
     * @throws Exception
     */
    public function isClientBlacklisted(Blacklist $blacklist, Client $client): bool
    {
        return $this->isBlacklisted(
            $this->prepareBlacklistCheckProfile($blacklist, $client)
        );
    }

    /**
     * @param Profile $profile
     * @return bool
     * @throws Exception
     */
    public function isBlacklisted(Profile $profile): bool
    {
        return $this->searchIsEnabled()
            && count(
                $this->getProfileSearchResults(
                    $profile,
                    $this->configurationManager->getActiveConfigurationName(),
                    true
                )
            ) > 0
        ;
    }

    /**
     * @param Transfer $transfer
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getTransferCheckResults(Transfer $transfer): array
    {
        $bankCheckResults = [];
        if ($transfer instanceof TransferOutBank) {
            $bankCheckResults = $this->getTransferOutBankBlacklistResults($transfer);
        }

        $profileCheckResults = $this->getProfileCheckBlacklistResults($transfer);

        return array_merge($bankCheckResults, $profileCheckResults);
    }

    /**
     * @param Transfer $transfer
     * @return BlacklistResult[]
     */
    public function getTransferOutBankBlacklistResults(Transfer $transfer): array
    {
        $blacklistResults = [];

        $beneficiary = $transfer->getBeneficiary();
        if (
            $beneficiary instanceof PartyBank
            && $beneficiary->getBic() !== null
        ) {
            $blacklistResults = $this->getBankBlacklistResults($beneficiary->getBic());
        }

        return $blacklistResults;
    }

    public function getTransferInBankBlacklistResults(Transfer $transfer): array
    {
        $blacklistResults = [];

        $payer = $transfer->getPayer();
        if (
            $payer instanceof PartyBank
            && $payer->getBic() !== null
        ) {
            $blacklistResults = $this->getBankBlacklistResults($payer->getBic());
        }

        return $blacklistResults;
    }

    private function getBankBlacklistResults(string $bic): array
    {
        $blacklistResults = [];

        $bankCodes = $this->bankCodeRepository->findAllByCodeStartingWith(
            rtrim($bic, 'X')
        );

        foreach ($bankCodes as $bankCode) {
            if ($bankCode->getItem()->getTopCategoryGroup()->getKey() === CategoryGroup::GROUP_KEY_SAN) {
                $blacklistResults[] = (new BlacklistResult())->setFoundProfile($bankCode->getItem());
            }
        }

        return $blacklistResults;
    }

    /**
     * @param Transfer $transfer
     * @param string|null $sanctionListKey
     * @param bool $aiFilterEnabled
     * @param string|null $aiConfidence
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getProfileCheckBlacklistResults(
        Transfer $transfer,
        string $sanctionListKey = null,
        bool $aiFilterEnabled = false,
        ?string $aiConfidence = null
    ): array {
        $clientTypePrediction = null;

        if ($transfer->getId() !== null) {
            $clientTypePrediction = $this->clientTypePredictionRepository->findOneByTransfer($transfer);
        }

        // @TODO fetching of $sanctionList - temporary solution, see SUPPORT-40893
        $sanctionList = ($sanctionListKey !== null) ? $this->getSanctionListByKey($sanctionListKey) : null;

        $profile = (new Profile())
            ->setFirstName($this->getNameByTransferType($transfer))
            ->setCountry($this->getCountryByTransferType($transfer)) // TODO strtoupper
        ;

        if ($clientTypePrediction !== null) {
            $profile->setClientTypePrediction($clientTypePrediction);
        }

        if ($sanctionList !== null) {
            $profile->setBlacklist($sanctionList);
        }

        $profileCheckResults = $this->getProfileSearchResults(
            $profile,
            $this->configurationManager->getActiveConfigurationName(),
            true
        );

        if ($aiFilterEnabled === true) {
            $searchTransferDatumId = !empty($transfer->getId())
                ? (string) $transfer->getId()
                : '0'
            ;

            return $this->dowJonesAiManager->filterOutNotConfidentDowJonesProfiles(
                $searchTransferDatumId,
                $profile,
                $profileCheckResults,
                $aiConfidence
            );
        }

        return $profileCheckResults;
    }

    /**
     * @param Blacklist $blacklist
     * @param Client $client
     * @param bool $aiFilterEnabled
     * @param string|null $aiConfidence
     * @param bool $ignoreAlternativeNames
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getClientCheckResults(
        Blacklist $blacklist,
        Client $client,
        bool $aiFilterEnabled = false,
        ?string $aiConfidence = null,
        bool $ignoreAlternativeNames = false
    ): array {
        $profile = $this->prepareBlacklistCheckProfile($blacklist, $client);

        $foundProfiles = $this->getProfileSearchResults(
            $profile,
            $this->configurationManager->getActiveConfigurationName(),
            true
        );

        if ($aiFilterEnabled === true) {
            $foundProfiles = $this->dowJonesAiManager->filterOutNotConfidentDowJonesProfiles(
                (string) $client->getCovenanteeId(),
                $profile,
                $foundProfiles,
                $aiConfidence,
                $ignoreAlternativeNames
            );
        }

        return $this->searchResultsProcessor->removeWhitelistedResults($foundProfiles, $client);
    }

    /**
     * @param Blacklist $blacklist
     * @param RealBeneficiary $beneficiary
     * @param Client $client
     * @param bool $aiFilterEnabled
     * @param bool $ignoreAlternativeNames
     * @param string|null $aiConfidence
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getRealBeneficiaryCheckResults(
        Blacklist $blacklist,
        RealBeneficiary $beneficiary,
        Client $client,
        bool $aiFilterEnabled = false,
        bool $ignoreAlternativeNames = false,
        ?string $aiConfidence = null
    ): array {
        $profile = $this->prepareBlacklistCheckRealBeneficiary($blacklist, $beneficiary);

        $foundProfiles = $this->getProfileSearchResults(
            $profile,
            $this->configurationManager->getActiveConfigurationName(),
            true
        );

        if ($aiFilterEnabled === true) {
            $foundProfiles = $this->dowJonesAiManager->filterOutNotConfidentDowJonesProfiles(
                (string) $client->getCovenanteeId(),
                $profile,
                $foundProfiles,
                $aiConfidence,
                $ignoreAlternativeNames
            );
        }

        return $this->searchResultsProcessor->removeWhitelistedResults($foundProfiles, $client);
    }

    /**
     * Search person in blacklist
     *
     * @param Profile $profile
     * @param string $configurationName
     * @param bool $transliterate
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getProfileCheckResults(Profile $profile, string $configurationName, bool $transliterate = false): array
    {
        $checkResults = [];

        if (!$this->searchIsEnabled()) {
            return $checkResults;
        }

        $boolQuery = $this->profileQueryBuilder->build($profile, $configurationName, $transliterate);

        if (empty($boolQuery['query'])) {
            // nothing to search
            return $checkResults;
        }

        $checkResults = array_merge(
            $checkResults,
            $this->prepareRequestResults(
                $profile,
                $this->searchRequestManager->sendSearchRequest($boolQuery)
            )
        );

        return $checkResults;
    }

    /**
     * @param Profile $profile
     * @param string $configurationName
     * @return BlacklistResult[]
     */
    public function getExactSearchProfileCheckResults(Profile $profile, string $configurationName): array
    {
        $checkResults = [];
        if (!$this->searchIsEnabled()) {
            return $checkResults;
        }

        $query = $this->profileQueryBuilder->buildExactSearchQuery($profile, $configurationName);
        if (!empty($query['query'])) {
            $checkResults = $this->prepareRequestResultsWithoutMatching(
                $this->searchRequestManager->sendSearchRequest($query)
            );
        }

        return $checkResults;
    }

    /**
     * @param Profile $profile
     * @param string $configurationName
     * @param bool $transliterate
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getProfileSearchResults(Profile $profile, string $configurationName, bool $transliterate = false): array
    {
        $checkResults = [];
        if (!$this->searchIsEnabled()) {
            return $checkResults;
        }

        $checkResults = $this->getProfileCheckResults($profile, $configurationName, $transliterate);

        if (count($checkResults) <= 0) {
            $checkResults = $this->getExactSearchProfileCheckResults($profile, $configurationName);
        }

        return $checkResults;
    }

    /**
     * todo: remove unused code from https://darbai.evp.lt/browse/MOKEJIMAI-8574
     * @param Profile $profile
     * @param string $blacklistKey
     * @throws InvalidArgumentException|ORMException
     */
    public function saveItem(Profile $profile, string $blacklistKey): void
    {
        $blacklist = $this->blacklistRepository->findOneByKey($blacklistKey);
        if ($blacklist === null) {
            throw new InvalidArgumentException(
                sprintf('Blacklist provider not found by key (%s)', $blacklistKey)
            );
        }

        $profile->setBlacklist($blacklist);
        $profile->setVersion('');
        $this->entityManager->persist($profile);
    }

    /**
     * @param Profile $profile
     * @param HybridResult[] $profileSearchResults
     * @return BlacklistResult[]
     */
    private function prepareRequestResults(Profile $profile, array $profileSearchResults): array
    {
        if (count($profileSearchResults) > 0) {
            $profileSearchResults = $this->searchResultsProcessor->sortSearchResultsByGroupPriority(
                $profileSearchResults
            );
            $profileSearchResults = $this->searchResultsProcessor->filterOutDuplicateProfiles($profileSearchResults);

            return $this->searchResultsProcessor->matchProfilesFromResults($profile, $profileSearchResults);
        }

        return [];
    }

    /**
     * @param HybridResult[] $profileSearchResults
     * @return BlacklistResult[]
     */
    private function prepareRequestResultsWithoutMatching(array $profileSearchResults): array
    {
        if (count($profileSearchResults) <= 0) {
            return [];
        }
        $searchResults = $this->searchResultsProcessor->sortSearchResultsByGroupPriority(
            $profileSearchResults
        );
        $searchResults = $this->searchResultsProcessor->filterOutDuplicateProfiles($searchResults);

        return $this->searchResultsProcessor->formResultsWithoutMatching($searchResults);
    }

    /**
     * @param Blacklist $blacklist
     * @param Client $client
     * @return Profile
     */
    private function prepareBlacklistCheckProfile(Blacklist $blacklist, Client $client): Profile
    {
        $profile = new Profile();
        $profile->setBlacklist($blacklist);
        $profile->setCode($client->getCode());

        if ($client instanceof ClientNatural) {
            $profile->setFirstName($client->getFirstName());
            $profile->setLastName($client->getLastName());
            try {
                $personInformation = $this->userClient->getPerson($client->getCovenanteeId());
            } catch (NotFoundRestException $exception) {
                $personInformation = null;
            }
            if ($personInformation !== null && $personInformation->getDob() !== null) {
                $profile->setBirthday($personInformation->getDob()->format('Y-m-d'));
            }
        } elseif ($client instanceof ClientLegal) {
            $profile->setName($client->getName());
        }

        if ($client instanceof CountryCodeAware) {
            $profile->setCountry($client->getCountryCode()); // TODO strtoupper
        }

        return $profile;
    }

    /**
     * @param Blacklist $blacklist
     * @param RealBeneficiary $beneficiary
     * @return Profile
     */
    private function prepareBlacklistCheckRealBeneficiary(Blacklist $blacklist, RealBeneficiary $beneficiary): Profile
    {
        $profile = new Profile();
        $profile->setBlacklist($blacklist);

        $profile->setCode($beneficiary->getCode());
        $profile->setName($beneficiary->getName());

        return $profile;
    }

    /**
     * @return bool
     */
    private function searchIsEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * @param Transfer $transfer
     * @return string|null
     */
    private function getNameByTransferType(Transfer $transfer): ?string
    {
        if ($transfer instanceof TransferIn) {
            if ($this->transferPayerExcludedChecker->isExcludedPayer($transfer)) {
                return '';
            }

            return $transfer->getPayer()->getDisplayName();
        }

        return $transfer->getBeneficiary()->getDisplayName();
    }

    /**
     * @param Transfer $transfer
     * @return string|null
     */
    private function getCountryByTransferType(Transfer $transfer): ?string
    {
        if ($transfer instanceof TransferIn) {
            return $this->accountInfoResolver->getAccountInfoForTransferParty($transfer->getPayer())->getCountryCode();
        }

        return $this->transferRoutingInformationRetriever->getCountry($transfer);
    }

    /**
     * @TODO temporary solution, see SUPPORT-40893
     *
     * @param string $sanctionListKey
     *
     * @return Blacklist|null
     */
    private function getSanctionListByKey(
        string $sanctionListKey
    ): ?Blacklist {
        $sanctionList = $this->blacklistRepository->findOneByKey($sanctionListKey);

        if ($sanctionList === null) {
            $this->logger->warning('Sanction list not found!', [
                $sanctionListKey,
            ]);
        }

        return $sanctionList;
    }
}
