<?php

namespace Evp\Bundle\BlacklistBundle\Service;

use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\CountryCodeAware;
use Evp\Bundle\QuestionnaireBundle\Entity\ClientLegal\RealBeneficiary;
use Exception;
use Paysera\Bundle\FeatureFlagBundle\Service\StoredFeatureFlag;

class BlacklistFilterService
{
    public const FEATURE_FLAG_KYC_CLIENT_PERIODIC_CHECK_EXTERNAL_AI_FILTER = 'KYC_CLIENT_PERIODIC_CHECK_EXTERNAL_AI_FILTER';

    private ProfileFilterRegistry $profileFilterRegistry;
    private BlacklistManager $blacklistManager;
    private StoredFeatureFlag $storedFeatureFlag;

    public function __construct(
        ProfileFilterRegistry $profileFilterRegistry,
        BlacklistManager $blacklistManager,
        StoredFeatureFlag $storedFeatureFlag
    ) {
        $this->profileFilterRegistry = $profileFilterRegistry;
        $this->blacklistManager = $blacklistManager;
        $this->storedFeatureFlag = $storedFeatureFlag;
    }

    /**
     * @param Blacklist $blacklist
     * @param Client $client
     * @param bool $aiFilterEnabled
     * @param string $aiConfidence
     * @param bool $ignoreAlternativeNames
     * @param ClientBlacklistCheckResult $blacklistCheckResult
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getClientCheckResults(
        Blacklist $blacklist,
        Client $client,
        bool $aiFilterEnabled,
        string $aiConfidence,
        bool $ignoreAlternativeNames,
        ClientBlacklistCheckResult $blacklistCheckResult
    ): array {
        $profile = $this->prepareBlacklistCheckProfile($blacklist, $client);

        $foundProfiles = $this->blacklistManager->getClientCheckResults(
            $blacklist,
            $client,
            $aiFilterEnabled,
            $aiConfidence,
            $ignoreAlternativeNames
        );

        if (empty($foundProfiles)) {
            return [];
        }
        $profileFilterContext = new ProfileFilterContext($ignoreAlternativeNames, $blacklistCheckResult);

        return $this->applyFilters($profile, $foundProfiles, $profileFilterContext);
    }

    /**
     * @param Blacklist $blacklist
     * @param RealBeneficiary $beneficiary
     * @param Client $client
     * @param bool $aiFilterEnabled
     * @param bool $ignoreAlternativeNames
     * @param string $aiConfidence
     * @param ClientBlacklistCheckResult $blacklistCheckResult
     * @return BlacklistResult[]
     * @throws Exception
     */
    public function getRealBeneficiaryCheckResults(
        Blacklist $blacklist,
        RealBeneficiary $beneficiary,
        Client $client,
        bool $aiFilterEnabled,
        bool $ignoreAlternativeNames,
        string $aiConfidence,
        ClientBlacklistCheckResult $blacklistCheckResult
    ): array {
        $profile = $this->prepareBlacklistCheckRealBeneficiary($blacklist, $beneficiary);

        $foundProfiles = $this->blacklistManager->getRealBeneficiaryCheckResults(
            $blacklist,
            $beneficiary,
            $client,
            $aiFilterEnabled,
            $ignoreAlternativeNames,
            $aiConfidence
        );

        if (empty($foundProfiles)) {
            return [];
        }
        $profileFilterContext = new ProfileFilterContext($ignoreAlternativeNames, $blacklistCheckResult);

        return $this->applyFilters($profile, $foundProfiles, $profileFilterContext);
    }


    private function applyFilters($profile, array $foundProfiles, ProfileFilterContext $context): array
    {
        if (!$this->storedFeatureFlag->isEnabled(self::FEATURE_FLAG_KYC_CLIENT_PERIODIC_CHECK_EXTERNAL_AI_FILTER)) {
            return $foundProfiles;
        }

        return $this->profileFilterRegistry->applyProfileFilters(
            $profile,
            $foundProfiles,
            $context
        );
    }

    private function prepareBlacklistCheckProfile(Blacklist $blacklist, Client $client): Profile
    {
        $profile = new Profile();
        $profile->setBlacklist($blacklist);
        $profile->setCode($client->getCode());

        if ($client instanceof ClientNatural) {
            $profile->setType(Profile::TYPE_PERSON);
            $profile->setFirstName($client->getFirstName());
            $profile->setLastName($client->getLastName());
        } elseif ($client instanceof ClientLegal) {
            $profile->setType(Profile::TYPE_COMPANY);
            $profile->setName($client->getName());
        }

        if ($client instanceof CountryCodeAware) {
            $profile->setCountry($client->getCountryCode());
        }

        return $profile;
    }

    /**
     * @param Blacklist $blacklist
     * @param RealBeneficiary $beneficiary
     * @return Profile
     */
    public function prepareBlacklistCheckRealBeneficiary(Blacklist $blacklist, RealBeneficiary $beneficiary): Profile
    {
        $profile = new Profile();
        $profile->setBlacklist($blacklist);
        $profile->setType($beneficiary->getType());
        $profile->setCode($beneficiary->getCode());
        $profile->setName($beneficiary->getName());

        return $profile;
    }
}
