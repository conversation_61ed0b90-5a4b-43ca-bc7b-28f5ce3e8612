<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\SanctionsScreening;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningRepository;
use Evp\Bundle\BlacklistBundle\Service\ChangeTracker\ClientSanctionsScreeningChangeTracker;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Symfony\Component\Lock\Factory;

class SanctionsScreeningManager
{
    private const LOCK_KEY = 'lock_sanctions_screening_by_client';
    private const LOCK_TTL = 1;

    private EntityManagerInterface $entityManager;
    private ClientSanctionsScreeningRepository $repository;
    private ClientSanctionsScreeningChangeTracker $tracker;
    private Factory $lock;

    public function __construct(
        EntityManagerInterface $entityManager,
        ClientSanctionsScreeningRepository $repository,
        ClientSanctionsScreeningChangeTracker $tracker,
        Factory $lock
    ) {
        $this->entityManager = $entityManager;
        $this->repository = $repository;
        $this->tracker = $tracker;
        $this->lock = $lock;
    }

    public function create(ClientBlacklistCheckResult $result): bool
    {
        if ($result->isBlacklisted() === false) {
            return false;
        }

        $client = $result->getClient();

        if (
            $client instanceof ClientNatural
            && !in_array(
                $client->getLevel(),
                [
                    IdentificationLevels::BASIC_IDENTIFIED,
                    IdentificationLevels::IDENTIFIED,
                    IdentificationLevels::FULLY_IDENTIFIED,
                ],
                true
            )
        ) {
            return false;
        }

        $blacklist = $result->getBlacklist();

        $lockResource = sprintf('%s_%d_%d', self::LOCK_KEY, $client->getId(), $blacklist->getId());
        $lock = $this->lock->createLock($lockResource, self::LOCK_TTL, false);

        if (
            !$lock->acquire()
            || $this->repository->isUnprocessedClientExists($client, $blacklist)
            || $this->repository->isClientBlacklistCheckResultExists($result)
        ) {
            return false;
        }

        $sanctionScreening = (new ClientSanctionsScreening())
            ->setClientBlacklistCheckResult($result)
            ->setClient($client)
        ;
        $this->entityManager->persist($sanctionScreening);
        $this->tracker->trackChanges($sanctionScreening, true);

        return true;
    }
}
