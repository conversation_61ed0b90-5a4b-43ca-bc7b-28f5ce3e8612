<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use Doctrine\ORM\EntityManager;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Entity\NamesComparisonResult;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;

class NamesComparisonResultService
{
    private Serializer $serializer;
    private EntityManager $entityManager;

    public function __construct(
        EntityManager $entityManager
    ) {
        $this->serializer = new Serializer([new ObjectNormalizer()]);
        $this->entityManager = $entityManager;
    }

    public function saveComparisonResults(
        NamesComparisonResultDto $namesComparisonResultDto,
        ProfileFilterContext $context
    ): NamesComparisonResult {
        $namesComparisonResult = $this->serializer->denormalize(
            $this->serializer->normalize($namesComparisonResultDto),
            NamesComparisonResult::class
        );

        if ($context->getTransfer()) {
            $namesComparisonResult->setTransfer($context->getTransfer());
        }

        if ($context->getBlacklistCheckResult()) {
            $namesComparisonResult->setBlacklistCheckResult($context->getBlacklistCheckResult());
        }

        $this->entityManager->persist($namesComparisonResult);

        return $namesComparisonResult;
    }
}
