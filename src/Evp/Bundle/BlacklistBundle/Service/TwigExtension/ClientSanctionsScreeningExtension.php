<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\TwigExtension;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResultItem;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\ItemReviewAction;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ClientSanctionsScreeningExtension extends AbstractExtension
{
    private string $adminUrl;
    private EntityManager $entityManager;

    public function __construct(string $adminUrl, EntityManager $entityManager)
    {
        $this->adminUrl = $adminUrl;
        $this->entityManager = $entityManager;
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('getExternalUrlToClientProfile', [$this, 'getExternalUrlToClientProfile']),
            new TwigFunction('getReadableFormatFromSnakeCase', [$this, 'getReadableFormatFromSnakeCase']),
            new TwigFunction('getReviewerDetails', [$this, 'getReviewerDetails']),
            new TwigFunction('getClientBlacklistCheckResultItemsToDisplay', [$this, 'getClientBlacklistCheckResultItemsToDisplay']),
            new TwigFunction('getReviewedClientBlacklistCheckResultItems', [$this, 'getReviewedClientBlacklistCheckResultItems']),
            new TwigFunction('formatActionName', [$this, 'formatActionName']),
        ];
    }

    public function getExternalUrlToClientProfile(string $userId): string
    {
        return $this->adminUrl . 'en/user/all-information/?page=1&filter%5Buser_id%5D=' . $userId;
    }

    public function getReadableFormatFromSnakeCase(string $input): string
    {
        return ucwords(str_replace('_', ' ', $input));
    }

    public function getReviewerDetails(?string $reviewerId): ?string
    {
        if ($reviewerId === null) {
            return null;
        }

        /** @var ?User $reviewer */
        $reviewer = $this->entityManager->getRepository(User::class)->find($reviewerId);

        if ($reviewer !== null) {
            return !empty(trim($reviewer->getFullname())) ? $reviewer->getFullname() : $reviewer->getUsername();
        }

        return null;
    }

    /**
     * @return ClientBlacklistCheckResultItem[]
     */
    public function getClientBlacklistCheckResultItemsToDisplay(ClientSanctionsScreening $sanctionsScreening): array
    {
        /** @var ClientBlacklistCheckResultItemRepository $repository */
        $repository = $this->entityManager->getRepository(ClientBlacklistCheckResultItem::class);
        return $repository->findByBlacklistCheckResultId($sanctionsScreening->getClientBlacklistCheckResult()->getId());
    }

    /**
     * @return array<int, array{externalId: string, action: string, userId: int}>
     */
    public function getReviewedClientBlacklistCheckResultItems(ClientSanctionsScreening $sanctionsScreening): array
    {
        /** @var ClientBlacklistCheckResultItemRepository $repository */
        $repository = $this->entityManager->getRepository(ClientBlacklistCheckResultItem::class);
        return $repository->findReviewedProfileExternalIdsByBlacklistCheckResultId(
            $sanctionsScreening->getClientBlacklistCheckResult()->getId()
        );
    }

    public function formatActionName(string $action): string
    {
        $actionNameMapping = [
            ItemReviewAction::MATCH_ACTION => 'Matched',
            ItemReviewAction::FALSE_MATCH_ACTION => 'Not Matched',
        ];

        return $actionNameMapping[$action] ?? $action;
    }
}
