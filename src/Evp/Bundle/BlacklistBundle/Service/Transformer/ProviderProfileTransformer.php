<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\Transformer;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\BankCode;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Entity\ProviderProfile;
use Evp\Bundle\BlacklistBundle\Repository\CategoryRepository;
use Paysera\Component\Serializer\Transformer\TransformerInterface;
use Paysera\Component\Serializer\Exception\InvalidDataException;

class ProviderProfileTransformer implements TransformerInterface
{
    private CategoryRepository $categoryRepository;
    private EntityManagerInterface $entityManager;

    public function __construct(
        CategoryRepository $categoryRepository,
        EntityManagerInterface $entityManager
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->entityManager = $entityManager;
    }

    /**
     * @param ProviderProfile $data
     * @return Profile
     * @throws InvalidDataException
     */
    public function transform($data): Profile
    {
        $profile = (new Profile())
            ->setName($data->getName())
            ->setType($data->getType())
            ->setExternalId($data->getId())
            ->setCountry($data->getCountry()) // TODO: strtoupper
            ->setFirstName($data->getFirstName())
            ->setMiddleName($data->getMiddleName())
            ->setLastName($data->getLastName())
            ->setName($data->getName())
            ->setBirthday($data->getBirthday())
            ->setAlternativeNames(new ArrayCollection($data->getAlternativeNames()))
            ->setCode($data->getIdentificationCode())
        ;

        $this->handleBankCodes($data, $profile);
        $this->handleCategories($data, $profile);
        $this->handleAddresses($data, $profile);
        $this->handleDateDetails($data, $profile);
        $this->handleIdNumbers($data, $profile);
        $this->handleNotes($data, $profile);
        $this->handleSanctionsReferences($data, $profile);
        $this->handleSources($data, $profile);

        if ($profile->getType() === ProviderProfile::TYPE_PERSON) {
            $this->handleAlternativeBirthdays($data, $profile);
            $this->handleBirthPlaces($data, $profile);
            $this->handleImages($data, $profile);
            $this->handleRoles($data, $profile);
        }

        return $profile;
    }

    private function handleBankCodes(ProviderProfile $providerProfile, Profile $profile): void
    {
        if (count($providerProfile->getBankCodes()) > 0) {
            foreach ($providerProfile->getBankCodes() as $bankCode) {
                $profile->addBankCode((new BankCode())->setCode($bankCode));
            }
        }
    }

    /**
     * @throws InvalidDataException
     */
    private function handleCategories(ProviderProfile $providerProfile, Profile $profile): void
    {
        if (count($providerProfile->getCategories()) > 0) {
            foreach ($providerProfile->getCategories() as $categoryIds) {
                $category = $this->getCategory($categoryIds);

                if ($category === null) {
                    throw new InvalidDataException('Category not found', ['categoryIds' => $categoryIds]);
                }

                $profile->addCategory($category);
            }
        }
    }

    private function handleAlternativeBirthdays(ProviderProfile $providerProfile, Profile $profile): void
    {
        if (count($providerProfile->getAlternativeBirthdays()) > 0) {
            foreach ($providerProfile->getAlternativeBirthdays() as $alternativeBirthday) {
                $profile->addAlternativeBirthday($alternativeBirthday);
            }
        }
    }

    private function handleAddresses(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getAddresses() as $address) {
            $profile->addAddress($address);
        }
    }

    private function handleBirthPlaces(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getBirthPlaces() as $birthPlace) {
            $profile->addBirthPlace($birthPlace);
        }
    }

    private function handleDateDetails(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getDateDetails() as $dateDetail) {
            $profile->addDateDetail($dateDetail);
        }
    }

    private function handleIdNumbers(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getIdNumbers() as $idNumber) {
            $profile->addIdNumber($idNumber);
        }
    }

    private function handleImages(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getImages() as $image) {
            $profile->addImage($image);
        }
    }

    private function handleNotes(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getNotes() as $note) {
            $profile->addNote($note);
        }
    }

    private function handleRoles(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getRoles() as $role) {
            $profile->addRole($role);
        }
    }

    private function handleSanctionsReferences(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getSanctionsReferences() as $reference) {
            if ($reference->getDescription() !== null) {
                $resolvedDescription = $this->categoryRepository->findOneByDepthAndExternalId(
                    2,
                    $reference->getDescription()
                );
                $resolvedDescription !== null
                    ? $reference->setDescription($resolvedDescription->getFullCategoryName() ?? null)
                    : $reference->setDescription(null);
            }

            $profile->addSanctionsReference($reference);
        }
    }

    private function handleSources(ProviderProfile $providerProfile, Profile $profile): void
    {
        foreach ($providerProfile->getSources() as $source) {
            $profile->addSource($source);
        }
    }

    private function getCategory($categoryIds): ?Category
    {
        $category = $this->categoryRepository
            ->findOneByDepthAndExternalId(count($categoryIds), end($categoryIds))
        ;
        if (!$this->entityManager->contains($category)) {
            /** @var CategoryRepository $repository */
            $repository = $this->entityManager->getRepository(Category::class);
            $category = $repository->findOneByDepthAndExternalId(count($categoryIds), end($categoryIds));
        }

        return $category;
    }
}
