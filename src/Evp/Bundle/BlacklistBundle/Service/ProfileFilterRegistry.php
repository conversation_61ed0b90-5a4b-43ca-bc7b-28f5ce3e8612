<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service;

use Evp\Bundle\BlacklistBundle\DTO\ProfileFilterContext;
use Evp\Bundle\BlacklistBundle\Service\BlacklistProfileFilter\ProfileFilterInterface;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;

class ProfileFilterRegistry
{
    /** @var ProfileFilterInterface[] */
    private array $filters = [];

    public function addFilter(ProfileFilterInterface $filter): void
    {
        $this->filters[] = $filter;
    }

    /**
     * Applies all filters to the matched profiles.
     *
     * @param Profile $profile
     * @param BlacklistResult[] $matchedProfiles
     * @param ProfileFilterContext $context
     *
     * @return BlacklistResult[]
     */
    public function applyProfileFilters(Profile $profile, array $matchedProfiles, ProfileFilterContext $context): array
    {
        foreach ($this->filters as $filter) {
            $matchedProfiles = $filter->apply($profile, $matchedProfiles, $context);
        }

        return $matchedProfiles;
    }
}
