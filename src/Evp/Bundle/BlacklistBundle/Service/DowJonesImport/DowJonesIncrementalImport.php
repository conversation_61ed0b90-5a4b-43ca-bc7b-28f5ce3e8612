<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Service\DowJonesImport;

use DateTime;
use Doctrine\DBAL\Exception\DriverException;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\ProviderProfile;
use Evp\Bundle\BlacklistBundle\MessageProcessor\DowJonesIncrementalImportMessageProcessor;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\CategoryRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRelationshipRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistCategoryImporter;
use Evp\Bundle\BlacklistBundle\Service\BlacklistRepositoryProxy;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\Reader\DowJonesFeedReader;
use Evp\Bundle\BlacklistBundle\Service\Transformer\ProviderCategoryTransformer;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Exception;
use PDOException;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Throwable;

class DowJonesIncrementalImport
{
    private const EMAILED_LOG_PREFIX = 'BlacklistBundle exceptional case: ';

    private DowJonesFeedReader $dowJonesFeedReader;
    private EntityManagerInterface $entityManager;
    private BlacklistCategoryImporter $categoryImporter;
    private BlacklistRepositoryProxy $blacklistRepositoryProxy;
    private CategoryRepository $categoryRepository;
    private ProfileRepository $profileRepository;
    private ProfileRelationshipRepository $profileRelationshipRepository;
    private ProviderCategoryTransformer $providerCategoryTransformer;
    private RegistryInterface $registry;
    private ConfigurationManager $configurationManager;
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private LoggerInterface $logger;

    private int $maxRetries;
    private int $delay;
    private int $latestVersion;
    private array $versionsToCheck;

    public function __construct(
        DowJonesFeedReader $dowJonesFeedReader,
        EntityManagerInterface $entityManager,
        BlacklistCategoryImporter $categoryImporter,
        BlacklistRepositoryProxy $blacklistRepositoryProxy,
        CategoryRepository $categoryRepository,
        ProfileRepository $profileRepository,
        ProfileRelationshipRepository $profileRelationshipRepository,
        ProviderCategoryTransformer $providerCategoryTransformer,
        RegistryInterface $registry,
        ConfigurationManager $configurationManager,
        RemoteJobPublisherInterface $remoteJobPublisher,
        LoggerInterface $logger
    ) {
        $this->dowJonesFeedReader = $dowJonesFeedReader;
        $this->entityManager = $entityManager;
        $this->categoryImporter = $categoryImporter;
        $this->blacklistRepositoryProxy = $blacklistRepositoryProxy;
        $this->categoryRepository = $categoryRepository;
        $this->profileRepository = $profileRepository;
        $this->profileRelationshipRepository = $profileRelationshipRepository;
        $this->providerCategoryTransformer = $providerCategoryTransformer;
        $this->registry = $registry;
        $this->configurationManager = $configurationManager;
        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->logger = $logger;

        $this->latestVersion = 0;
        $this->versionsToCheck = [];
    }

    /**
     * @throws Throwable
     */
    public function import(int $maxRetries, int $delay, bool $force = false): void
    {
        $this->logger->info('Starting incremental import process');

        // Disable logging to reduce memory usage
        $this->configurationManager->getClient()->setLogger(new NullLogger());
        $this->entityManager->getConnection()->getConfiguration()->setSQLLogger();

        $blacklist = $this->getBlacklist();
        if ($blacklist === null) {
            $this->emailedLog('Blacklist not found.');
            return;
        }

        $this->maxRetries = $maxRetries;
        $this->delay = $delay;
        $this->latestVersion = (int)$blacklist->getVersion();

        $this->processFeedsAndUpdateProfiles($blacklist, $force);
        $this->logger->info('Incremental import process finished');
    }

    /**
     * @throws Throwable
     */
    private function processFeedsAndUpdateProfiles(Blacklist $blacklist, bool $force): void
    {
        try {
            $updated = $this->processFeeds($blacklist, $force);
        } catch (Throwable $exception) {
            $this->emailedLog('Exception during processing feeds', [$exception]);
            throw $exception;
        }

        if (!$updated) {
            $this->logger->info('No action - already up-to-date.');
            return;
        }

        $this->publishCheckUpdatedProfilesJobs();
        $this->removeFeedFiles($blacklist);
    }

    /**
     * @throws Exception
     */
    private function processFeeds(Blacklist $blacklist, bool $force): bool
    {
        $this->logger->info('Processing feeds...');

        $forceVersionDate = intval((new DateTime())->format('YmdHi'));
        $feedDataProvider = $this->configurationManager->getIncrementalFeedDataProvider();
        $version = $feedDataProvider->getVersionToImport($blacklist->getVersion(), $force);

        if ($version === null) {
            $this->publishFinalEvents(
                $this->latestVersion,
                [
                    ProviderProfile::ACTION_CREATE => 0,
                    ProviderProfile::ACTION_CHANGE => 0,
                    ProviderProfile::ACTION_DELETE => 0,
                ],
            );

            return false;
        }

        while ($version !== null) {
            $this->logger->info(
                sprintf('New version found %d', $version),
                ['foundVersion' => $version, 'databaseVersion' => $blacklist->getVersion()]
            );

            $realVersion = $force ? $forceVersionDate : $version;
            $this->latestVersion = $version;
            $this->versionsToCheck[] = $realVersion;

            $this->logger->info('Downloading profiles zip file...');
            $this->dowJonesFeedReader->getFeedFileByVersion($version);

            $this->importNewCategories($version);
            $this->importRelationships($version);
            $this->publishImportProfileJobs($version, $realVersion);
            $this->publishImportAssociationJobs($version, $realVersion);
            $blacklist = $this->updateBlacklistVersion($realVersion);

            $version = $feedDataProvider->getVersionToImport($blacklist->getVersion(), $force);
        }

        return true;
    }

    private function publishFinalEvents(int $version, array $profileActionCounts): void
    {
        $this->remoteJobPublisher->publishJob(DowJonesIncrementalImportMessageProcessor::JOB_KEY, [
            DowJonesIncrementalImportMessageProcessor::KEY_OPERATION => DowJonesIncrementalImportMessageProcessor::OPERATION_FINAL_EVENTS,
            DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            DowJonesIncrementalImportMessageProcessor::KEY_PROFILE_ACTION_COUNTS => $profileActionCounts,
        ]);
    }

    private function publishImportProfileJobs(int $version, ?int $realVersion): void
    {
        $this->logger->info(sprintf('Publishing import profile jobs for version %d', $version));

        $profileActionCounts = [
            ProviderProfile::ACTION_CREATE => 0,
            ProviderProfile::ACTION_CHANGE => 0,
            ProviderProfile::ACTION_DELETE => 0,
        ];

        foreach ($this->configurationManager->getIncrementalFeedDataProvider()->getProfiles($version) as $providerProfile) {
            if (array_key_exists($providerProfile->getAction(), $profileActionCounts)) {
                $profileActionCounts[$providerProfile->getAction()]++;
            }

            $this->remoteJobPublisher->publishJob(DowJonesIncrementalImportMessageProcessor::JOB_KEY, [
                DowJonesIncrementalImportMessageProcessor::KEY_OPERATION => DowJonesIncrementalImportMessageProcessor::OPERATION_IMPORT_PROFILE,
                DowJonesIncrementalImportMessageProcessor::KEY_PROVIDER_PROFILE => serialize($providerProfile),
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $realVersion ?? $version,
            ]);
        }

        $this->publishFinalEvents($version, $profileActionCounts);
    }

    private function publishImportAssociationJobs(int $version, ?int $realVersion): void
    {
        $this->logger->info('Publish import association jobs started');

        foreach ($this->configurationManager->getIncrementalFeedDataProvider()->getAssociations($version) as $associationsData) {
            $this->remoteJobPublisher->publishJob(DowJonesIncrementalImportMessageProcessor::JOB_KEY, [
                DowJonesIncrementalImportMessageProcessor::KEY_OPERATION => DowJonesIncrementalImportMessageProcessor::OPERATION_IMPORT_ASSOCIATION,
                DowJonesIncrementalImportMessageProcessor::KEY_ASSOCIATIONS_DATA => serialize($associationsData),
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $realVersion ?? $version,
            ]);
        }
    }

    private function publishCheckUpdatedProfilesJobs(): void
    {
        $this->logger->info('Publishing check updated profiles jobs');

        foreach ($this->versionsToCheck as $version) {
            $this->remoteJobPublisher->publishJob(DowJonesIncrementalImportMessageProcessor::JOB_KEY, [
                DowJonesIncrementalImportMessageProcessor::KEY_OPERATION => DowJonesIncrementalImportMessageProcessor::OPERATION_CHECK_UPDATED_PROFILES,
                DowJonesIncrementalImportMessageProcessor::KEY_BLACKLIST_VERSION => $version,
            ]);
        }
    }

    /**
     * @throws Exception
     */
    private function importNewCategories(int $version, int $retry = 1): void
    {
        try {
            $this->logger->info(sprintf('Looking for new categories for version %d', $version));
            $categoriesCounted = $this->categoryRepository->countCategories();
            $categories = [];

            foreach ($this->configurationManager->getIncrementalFeedDataProvider()->getCategories($version) as $providerCategory) {
                $categories[] = $this->providerCategoryTransformer->transform($providerCategory);
            }

            $this->categoryImporter->import($categories);
            $this->logger->info(sprintf(
                'New categories added: %d',
                $this->categoryRepository->countCategories() - $categoriesCounted
            ));
        } catch (DriverException | PDOException $exception) {
            $this->handleReadOnlyException($exception, $retry, 'saving new categories');
            $this->importNewCategories($version, ++$retry);
        }
    }

    /**
     * @throws Exception
     */
    private function importRelationships(int $version, int $retry = 1): void
    {
        try {
            $this->logger->info(sprintf('Looking for new relationships for version %d', $version));

            foreach ($this->configurationManager->getIncrementalFeedDataProvider()->getRelationships($version) as $relationship) {
                if ($this->profileRelationshipRepository->findOneByCode($relationship->getCode()) === null) {
                    $this->entityManager->persist($relationship);
                }
            }

            $this->entityManager->flush();
        } catch (DriverException | PDOException $exception) {
            $this->handleReadOnlyException($exception, $retry, 'saving new relationships');
            $this->importRelationships($version, ++$retry);
        }
    }

    /**
     * @throws Exception
     */
    private function updateBlacklistVersion(int $realVersion, int $retry = 1): Blacklist
    {
        try {
            $blacklist = $this->getBlacklist();
            $blacklist->setVersion($realVersion);

            $this->entityManager->flush();
        } catch (DriverException | PDOException $exception) {
            $this->handleReadOnlyException($exception, $retry, 'updating blacklist version');
            return $this->updateBlacklistVersion($realVersion, ++$retry);
        }

        return $blacklist;
    }

    /**
     * @throws Exception
     */
    private function handleReadOnlyException(Exception $exception, int $retry, string $message): void
    {
        if ($retry > $this->maxRetries) {
            $this->emailedLog(sprintf('Failed %s, aborting', $message), [$exception]);
            throw $exception;
        }

        $this->logger->info(sprintf('Failed %s, retrying', $message), [$exception]);
        usleep($this->delay * $retry);

        $this->registry->resetManager();
        $this->entityManager->getConnection()->close();
        $this->entityManager->getConnection()->connect();
    }

    private function removeFeedFiles(Blacklist $blacklist): void
    {
        $this->logger->info('Removing feed files...');

        $versionsToRemove = $this->profileRepository->getVersionsToRemove($blacklist, $this->latestVersion);

        foreach ($versionsToRemove as $versionToRemove) {
            $this->dowJonesFeedReader->removeFeedFileByVersion($versionToRemove);
        }
    }

    private function getBlacklist(): ?Blacklist
    {
        $blacklist = $this->blacklistRepositoryProxy->findOneByKey(
            $this->configurationManager->getIncrementalFeedDataProvider()->getBlacklistKey()
        );

        if (!$this->entityManager->contains($blacklist)) {
            /** @var BlacklistRepository $repository */
            $repository = $this->entityManager->getRepository(Blacklist::class);
            $blacklist = $repository->findOneByKey(
                $this->configurationManager->getIncrementalFeedDataProvider()->getBlacklistKey()
            );
        }

        return $blacklist;
    }

    private function emailedLog(string $message, array $context = []): void
    {
        $this->logger->error(self::EMAILED_LOG_PREFIX . $message, $context);
    }
}
