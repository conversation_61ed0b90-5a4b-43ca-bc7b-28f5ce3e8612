<?php

namespace Evp\Bundle\BlacklistBundle\Service;

use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Helper\TableSeparator;
use Symfony\Component\Console\Output\OutputInterface;

class BlacklistSelfCheck
{
    const HIT_LIST = [
        'GPB Neftegaz Services',
        'GPB Neftegaz',
        'Rostelekom', // TRANSFERS 3173
        'Ростелеком', // TRANSFERS 3137
        'Gazprom Media Holding',
        '<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON>', // taken from transfer 341299158, test record
        '<PERSON>',
    ];

    const MISS_LIST = [
        'Anwar Djaballah',
        'UAB Pigu',
        'Pigu',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        'Liraland_H Limited',
        'HAABERSTI HALDUS OU',
        'VŠĮ GEROS VALIOS',
        'TB International GmbH',
        'Mano Būstas Dainava, UAB',
        'MAXIMA LATVIJA SIA',
        'UAB "Omega servisas"',
    ];

    private $blacklistManager;
    private $logger;

    public function __construct(
        BlacklistManager $blacklistManager,
        LoggerInterface $logger
    ) {
        $this->blacklistManager = $blacklistManager;
        $this->logger = $logger;
    }

    public function test(string $configurationName, OutputInterface $output = null)
    {
        $lineLimiter = 100;
        $output->writeln('Running tests for blacklist expected results');
        $failure = false;
        $blacklistExpectedResults = $this->getProfilesResults(self::HIT_LIST, $configurationName, $output);

        $table = (new Table($output))
            ->setHeaders([
                'Search name',
                'Expected result',
                'Result',
            ])
        ;

        foreach ($blacklistExpectedResults as $blacklistExpectedResult) {
            $profile = $blacklistExpectedResult['profile'];
            $results = $blacklistExpectedResult['results'];

            $tableRow = [
                $profile->getDisplayName(),
                'EXPECT MATCH'
            ];

            $result = '';
            if (count($results) > 0) {
                $result = $this->getResultString($results, $lineLimiter, $output);
            } else {
                $failure = true;
            }

            $tableRow[] = $result;
            $table->addRow($tableRow);
            $table->addRow(new TableSeparator());
        }

        $blacklistExpectedNoResults = $this->getProfilesResults(self::MISS_LIST, $configurationName, $output);
        foreach ($blacklistExpectedNoResults as $blacklistExpectedNoResult) {
            $profile = $blacklistExpectedNoResult['profile'];
            $results = $blacklistExpectedNoResult['results'];

            $tableRow = [
                $profile->getDisplayName(),
                'EXPECT PASS'
            ];

            $result = '';
            if (count($results) > 0) {
                $result = $this->getResultString($results, $lineLimiter, $output);
                $failure = true;
            }

            $tableRow[] = $result;
            $table->addRow($tableRow);
            $table->addRow(new TableSeparator());
        }

        $table->render();

        if (!$failure) {
            $this->logger->info('DowJones self-test passed');
            return;
        }

        throw new RuntimeException('Blacklist self-test did not passed');
    }

    private function getResultString($results, $lineLimiter, $output): string
    {
        $result = '';
        foreach ($results as $blacklistResult) {
            if ($output !== null) {
                $result .= sprintf(
                    '%s;%s%s%s;%s%s',
                    $this->splitLineIfTooLong($blacklistResult->getFoundProfile()->getDisplayName(), $lineLimiter),
                    $this->splitLineIfTooLong($blacklistResult->getScore(), $lineLimiter),
                    PHP_EOL,
                    $this->splitLineIfTooLong($blacklistResult->getFoundProfile()->getCategories()->get(0)->getFullCategoryName(), $lineLimiter),
                    $this->splitLineIfTooLong($blacklistResult->getFoundProfile()->getTopCategoryGroup()->getName(), $lineLimiter),
                    PHP_EOL.PHP_EOL
                );
            }
        }

        return $result;
    }

    private function splitLineIfTooLong($line, $limitter)
    {
        $newLine = wordwrap($line, $limitter, PHP_EOL);
        return $newLine;
    }

    private function getProfilesResults(array $profileNameList, string $configurationName, OutputInterface $output = null)
    {
        $searchResults = [];

        foreach ($profileNameList as $profileName) {
            $profile = new Profile();
            $profile->setFirstName($profileName);

            $blacklistResults = $this->blacklistManager->getProfileSearchResults($profile, $configurationName, true);

            $searchResults[] = [
                'profile' => $profile,
                'results' => $blacklistResults
            ];
        }

        return $searchResults;
    }
}
