<?php

namespace Evp\Bundle\BlacklistBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\ClientBundle\Entity\Client;

/**
 * @method ClientCheckWhitelist[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method ClientCheckWhitelist[] findAll()
 */
class ClientCheckWhitelistRepository extends EntityRepository
{
    public function findOneById(int $id): ?ClientCheckWhitelist
    {
        return $this->find($id);
    }

    /**
     * @return ClientCheckWhitelist[]
     */
    public function findAllByClient(Client $client): array
    {
        return $this->findBy(['client' => $client]);
    }

    /**
     * @return ClientCheckWhitelist[]
     */
    public function findAllByBlacklistAndProfileExternalId(Blacklist $blacklist, string $profileExternalId): array
    {
        return $this->findBy([
            'blacklist' => $blacklist,
            'profileExternalId' => $profileExternalId,
        ]);
    }

    public function isClientCheckWhitelistExists(ClientCheckWhitelist $clientCheckWhitelist): bool
    {
        $count = (int)$this->createQueryBuilder('w')
            ->select('COUNT(w.id)')
            ->where('w.client = :client')
            ->andWhere('w.blacklist = :blacklist')
            ->andWhere('w.categoryGroup = :categoryGroup')
            ->andWhere('w.profileExternalId = :profileExternalId')
            ->setParameters([
                'client' => $clientCheckWhitelist->getClient(),
                'blacklist' => $clientCheckWhitelist->getBlacklist(),
                'categoryGroup' => $clientCheckWhitelist->getCategoryGroup(),
                'profileExternalId' => $clientCheckWhitelist->getProfileExternalId(),
            ])
            ->getQuery()
            ->getSingleScalarResult()
        ;

        return $count > 0;
    }
}
