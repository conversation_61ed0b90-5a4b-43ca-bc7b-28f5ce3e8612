<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Repository;

use DateTimeImmutable;
use Doctrine\DBAL\Driver\Exception as DBALDriverException;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;

class NamesComparisonResultRepository extends EntityRepository
{
    public function getFirstIdToDate(DateTimeImmutable $dateTime): int
    {
        try {
            return (int)$this->createQueryBuilder('n')
                ->select('n.id')
                ->andWhere('n.createdAt < :dateTime')
                ->setParameter('dateTime', $dateTime)
                ->setMaxResults(1)
                ->orderBy('n.id', 'DESC')
                ->getQuery()
                ->getSingleScalarResult()
            ;
        } catch (NoResultException|NonUniqueResultException $exception) {
            return 0;
        }
    }

    public function countGroupedByFailureReason(int $fromId): array
    {
        $results = $this->createQueryBuilder('n')
            ->select('n.failureReason AS failureReason, COUNT(n.id) AS count')
            ->andWhere('n.id >= :fromId')
            ->andWhere('n.failureReason IS NOT NULL')
            ->groupBy('n.failureReason')
            ->setParameter('fromId', $fromId)
            ->getQuery()
            ->getResult()
        ;

        return array_map(
            fn ($count) => (int) $count,
            array_column($results, 'count', 'failureReason')
        );
    }

    public function countGroupedByDecision(int $fromId): array
    {
        $results = $this->createQueryBuilder('n')
            ->select('n.decision AS decision, COUNT(n.id) AS count')
            ->andWhere('n.id >= :fromId')
            ->andWhere('n.decision IS NOT NULL')
            ->groupBy('n.decision')
            ->setParameter('fromId', $fromId)
            ->getQuery()
            ->getResult()
        ;

        return array_map(
            fn ($count) => (int) $count,
            array_column($results, 'count', 'decision')
        );
    }

    public function countUniqueTransfers(int $fromId): int
    {
        $sql = sprintf(
            "SELECT COUNT(*)
            FROM (
                SELECT DISTINCT n.transfer_id 
                FROM %s n
                WHERE n.id > :fromId
            ) t;",
            $this->getClassMetadata()->getTableName(),
        );

        try {
            $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
            $stmt->execute(['fromId' => $fromId]);
            return (int)$stmt->fetchOne();
        } catch (DBALException|DBALDriverException $exception) {
            return 0;
        }
    }

    public function countUniqueCheckResults(int $fromId): int
    {
        $sql = sprintf(
            "SELECT COUNT(*)
            FROM (
                SELECT DISTINCT n.check_result_id 
                FROM %s n
                WHERE n.id > :fromId
            ) t;",
            $this->getClassMetadata()->getTableName(),
        );

        try {
            $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
            $stmt->execute(['fromId' => $fromId]);
            return (int)$stmt->fetchOne();
        } catch (DBALException|DBALDriverException $exception) {
            return 0;
        }
    }

    public function countUniquePreventedMatchTransfers(int $fromId): int
    {
        $tableName = $this->getClassMetadata()->getTableName();

        $sql = sprintf(
            "SELECT COUNT(*)
            FROM (
                SELECT DISTINCT r.transfer_id
                FROM %s r
                WHERE r.id > :fromId
                AND r.transfer_id NOT IN (
                    SELECT sub_r.transfer_id
                    FROM %s sub_r
                    WHERE sub_r.id > :fromId
                    AND sub_r.decision != :decision
                )
            ) t;",
            $tableName,
            $tableName
        );

        try {
            $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
            $stmt->execute(['fromId' => $fromId, 'decision' => NamesComparisonResultDto::DECISION_SAME_PERSON]);
            return (int)$stmt->fetchOne();
        } catch (DBALException|DBALDriverException $exception) {
            return 0;
        }
    }

    public function countUniquePreventedClientMatch(int $fromId): int
    {
        $tableName = $this->getClassMetadata()->getTableName();

        $sql = sprintf(
            "SELECT COUNT(*)
            FROM (
                SELECT DISTINCT r.check_result_id
                FROM %s r
                WHERE r.id > :fromId
                AND r.check_result_id NOT IN (
                    SELECT sub_r.check_result_id
                    FROM %s sub_r
                    WHERE sub_r.id > :fromId
                    AND sub_r.decision != :decision
                )
            ) t;",
            $tableName,
            $tableName
        );

        try {
            $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
            $stmt->execute(['fromId' => $fromId, 'decision' => NamesComparisonResultDto::DECISION_SAME_PERSON]);
            return (int)$stmt->fetchOne();
        } catch (DBALException|DBALDriverException $exception) {
            return 0;
        }
    }
}
