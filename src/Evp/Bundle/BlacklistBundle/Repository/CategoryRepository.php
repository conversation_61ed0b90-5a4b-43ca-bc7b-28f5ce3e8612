<?php

namespace Evp\Bundle\BlacklistBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Mapping\ClassMetadata;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Component\Cache\DoctrineRelatedCache;

class CategoryRepository extends EntityRepository
{
    /**
     * @var DoctrineRelatedCache
     */
    private $categoriesCache;

    public function __construct($entityManager, ClassMetadata $class)
    {
        parent::__construct($entityManager, $class);
        $this->categoriesCache = new DoctrineRelatedCache($entityManager);
    }

    public function findOneById(int $id)
    {
        /** @var ?Category $category */
        $category = $this->find($id);
        return $category;
    }

    public function countCategories()
    {
        return (int)$this->createQueryBuilder('c')
            ->select('COUNT(c.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param int $depth
     * @param int $externalId
     * @return null|Category
     */
    public function findOneByDepthAndExternalId($depth, $externalId)
    {
        $categoryCacheKey = $depth . '-' . $externalId;

        return $this->categoriesCache->getWithCache($categoryCacheKey, function () use ($depth, $externalId) {
            return $this->createQueryBuilder('c')
                ->andWhere('c.depth = :depth')
                ->andWhere('c.externalId = :externalId')
                ->setParameters([
                    'depth' => $depth,
                    'externalId' => $externalId,
                ])
                ->getQuery()
                ->getOneOrNullResult()
            ;
        });
    }
}
