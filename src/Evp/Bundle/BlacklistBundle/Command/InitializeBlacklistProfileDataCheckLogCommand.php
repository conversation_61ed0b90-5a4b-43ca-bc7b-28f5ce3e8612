<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckInitializer;
use LogicException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class InitializeBlacklistProfileDataCheckLogCommand extends Command
{
    private PeriodicalBlacklistControlDataCheckInitializer $periodicalBlacklistControlDataCheckInitializer;
    private EntityManagerInterface $entityManager;

    public function __construct(
        PeriodicalBlacklistControlDataCheckInitializer $periodicalBlacklistControlDataCheckInitializer,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct();
        $this->periodicalBlacklistControlDataCheckInitializer = $periodicalBlacklistControlDataCheckInitializer;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:initialize-blacklist-profile-data-check-log')
            ->setDescription('Initialize BlacklistProfileDataCheckLog')
            ->addArgument(
                'blacklist-key',
                InputArgument::REQUIRED,
                'Partner Title.'
            )
            ->addArgument(
                'path-to-csv-file',
                InputArgument::REQUIRED,
                'Path to a csv file with profiles data.'
            )
            ->addOption(
                'manipulate-data',
                null,
                InputOption::VALUE_NONE,
                'Manipulate data.'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklistKey = $input->getArgument('blacklist-key');
        $pathToCsvFile = $input->getArgument('path-to-csv-file');
        $manipulateData = $input->getOption('manipulate-data');
        if (!is_file($pathToCsvFile)) {

            throw new LogicException('File not found');
        }
        $loadedFile = file($pathToCsvFile);
        if (!is_array($loadedFile)) {

            throw new LogicException('File is invalid');
        }
        $uploadedFile = new UploadedFile(
            $pathToCsvFile,
            basename($pathToCsvFile),
        );
        $log = $this->periodicalBlacklistControlDataCheckInitializer->initialize(
            $blacklistKey,
            $uploadedFile,
            $manipulateData
        );
        $this->entityManager->flush();
        $output->writeln(sprintf('Initialized BlacklistProfileDataCheckLog with id \'%s\'', $log->getId()));
        return 0;
    }
}
