<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use Doctrine\DBAL\Exception\DriverException;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Service\ClientActivityIndicator;
use Exception;
use PDOException;
use Psr\Clock\ClockInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Evp\Bundle\BlacklistBundle\Worker\BlacklistCheckWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultRepository;
use DateInterval;
use Throwable;

class UpdateClientBlacklistCommand extends Command
{
    private const LIMIT = 150000;
    private const MAX_ATTEMPTS = 10;
    private const DELAY = 1;

    private ClientBlacklistCheckResultRepository $clientBlacklistCheckRepository;
    private RemoteJobPublisherInterface $remoteJobPublisher;
    private ClientActivityIndicator $clientActivityIndicator;
    private string $naturalClientCheckFrequency;
    private string $legalClientCheckFrequency;
    private EntityManagerInterface $entityManager;
    private int $publishedItems;
    private LoggerInterface $logger;
    private ClockInterface $clock;
    private RegistryInterface $registry;
    private int $limit;
    private int $maxAttempts;
    private int $delayBetweenAttempts;

    public function __construct(
        ClientBlacklistCheckResultRepository $clientBlacklistCheckRepository,
        RemoteJobPublisherInterface $remoteJobPublisher,
        ClientActivityIndicator $clientActivityIndicator,
        string $naturalClientCheckFrequency,
        string $legalClientCheckFrequency,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger,
        ClockInterface $clock,
        RegistryInterface $registry
    ) {
        parent::__construct();

        $this->clientBlacklistCheckRepository = $clientBlacklistCheckRepository;
        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->clientActivityIndicator = $clientActivityIndicator;
        $this->naturalClientCheckFrequency = $naturalClientCheckFrequency;
        $this->entityManager = $entityManager;
        $this->legalClientCheckFrequency = $legalClientCheckFrequency;
        $this->publishedItems = 0;
        $this->logger = $logger;
        $this->clock = $clock;
        $this->registry = $registry;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:update-clients')
            ->setDescription('Updates Clients blacklist table')
            ->addOption(
                'naturalPersonCheckFrequency',
                null,
                InputOption::VALUE_OPTIONAL,
                'Overrides the default natural person\'s check frequency'
            )
            ->addOption(
                'legalPersonCheckFrequency',
                null,
                InputOption::VALUE_OPTIONAL,
                'Overrides the default legal person\'s check frequency'
            )
            ->addOption(
                'lastExportedClientId',
                null,
                InputOption::VALUE_OPTIONAL,
                'Sets the clientId from which new clients can be exported'
            )
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_OPTIONAL,
                'Sets the maximum number of items to publish',
                self::LIMIT
            )
            ->addOption(
                'maxAttempts',
                null,
                InputOption::VALUE_OPTIONAL,
                'Sets maximum number of attempts to retrieve clients data (in case of failure)',
                self::MAX_ATTEMPTS
            )
            ->addOption(
                'delay',
                null,
                InputOption::VALUE_OPTIONAL,
                'Sets number of seconds to wait before retry (in case of failure)',
                self::DELAY
            )
        ;
    }

    /**
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->entityManager->getConnection()->getConfiguration()->setSQLLogger();
        $this->logger->info('blacklist-update-clients - STARTED');

        $this->limit = (int)$input->getOption('limit');
        $this->maxAttempts = (int)$input->getOption('maxAttempts');
        $this->delayBetweenAttempts = (int)$input->getOption('delay');

        $providedLegalPersonCheckFrequency = $input->getOption('legalPersonCheckFrequency');
        if ($providedLegalPersonCheckFrequency !== null) {
            $this->legalClientCheckFrequency = $providedLegalPersonCheckFrequency;
        }

        $providedNaturalPersonCheckFrequency = $input->getOption('naturalPersonCheckFrequency');
        if ($providedNaturalPersonCheckFrequency !== null) {
            $this->naturalClientCheckFrequency = $providedNaturalPersonCheckFrequency;
        }

        $lastExportedClientId = (int)$input->getOption('lastExportedClientId');

        $this->handleNotExistingClientsIds($lastExportedClientId);
        $this->handleNaturalClientIdsThatNeedReevaluation($lastExportedClientId);
        $this->handleLegalClientIdsThatNeedReevaluation($lastExportedClientId);

        if ($this->publishedItems > $this->limit) {
            $this->logger->info('blacklist-update-clients - limit has been exceeded');
        }

        $this->logger->info('blacklist-update-clients - DONE');
        $output->writeln('All records has been pushed to job');
        return 0;
    }

    private function handleNotExistingClientsIds(int $lastExportedClientId): void
    {
        $this->logger->info('blacklist-update-clients - notExistingClientsIds');

        while ($lastExportedClientId !== null && $this->publishedItems <= $this->limit) {
            $lastExportedClientId = $this->publishJobs(
                $this->getDataWithRetries(
                    fn () => $this->clientBlacklistCheckRepository->findAllWithCodeNotExistingClientsIdsFromClientId(
                        $lastExportedClientId
                    )
                )
            );
        }
    }

    /**
     * @throws Exception
     */
    private function handleNaturalClientIdsThatNeedReevaluation(int $lastExportedClientId): void
    {
        $this->logger->info('blacklist-update-clients - naturalClientIdsThatNeedReevaluation');

        while ($lastExportedClientId !== null && $this->publishedItems <= $this->limit) {
            $lastExportedClientId = $this->publishJobs(
                $this->getNaturalClientIdsThatNeedReevaluation(
                    $lastExportedClientId,
                    $this->naturalClientCheckFrequency
                )
            );
        }
    }

    /**
     * @throws Exception
     */
    private function handleLegalClientIdsThatNeedReevaluation(int $lastExportedClientId): void
    {
        $this->logger->info('blacklist-update-clients - legalClientIdsThatNeedReevaluation');

        while ($lastExportedClientId !== null && $this->publishedItems <= $this->limit) {
            $lastExportedClientId = $this->publishJobs(
                $this->getLegalClientIdsThatNeedReevaluation(
                    $lastExportedClientId,
                    $this->legalClientCheckFrequency
                )
            );
        }
    }

    /**
     * @throws Exception
     */
    private function getNaturalClientIdsThatNeedReevaluation(int $fromClientId, string $checkFrequency): array
    {
        $personLookupDate = $this->clock->now()->sub(new DateInterval($checkFrequency));

        return $this->getDataWithRetries(
            fn () => $this->clientBlacklistCheckRepository->findAllIdentifiedWithCodeNaturalPersonIdsByDateClientId(
                $personLookupDate,
                $fromClientId
            )
        );
    }

    /**
     * @throws Exception
     */
    private function getLegalClientIdsThatNeedReevaluation(int $fromClientId, string $checkFrequency): array
    {
        $personLookupDate = $this->clock->now()->sub(new DateInterval($checkFrequency));

        return $this->getDataWithRetries(
            fn () => $this->clientBlacklistCheckRepository->findAllLegalPersonIdsByDateClientId(
                $personLookupDate,
                $fromClientId
            )
        );
    }

    private function publishJobs(array $toBeCheckedRecordsIds): ?int
    {
        $activeClientIds = [];
        foreach ($toBeCheckedRecordsIds as $clientId) {
            $isActive = $this->getDataWithRetries(
                fn () => $this->clientActivityIndicator->isActiveClientById($clientId)
            );
            if ($isActive) {
                $activeClientIds[] = $clientId;
            }
        }

        if (count($activeClientIds) > 0) {
            foreach ($activeClientIds as $activeClientId) {
                $this->remoteJobPublisher->publishJob(
                    BlacklistCheckWorker::JOB_CHECK_BLACKLIST_USERS,
                    ['clients' => [$activeClientId['id']]]
                );
            }

            $this->publishedItems = $this->publishedItems + count($activeClientIds);
            $this->logger->info(
                'blacklist-update-clients - activeClientIds publish DONE',
                [$this->publishedItems]
            );
        }

        if (count($activeClientIds) === 0 && count($toBeCheckedRecordsIds) > 0) {
            $this->logger->info(
                sprintf(
                    'blacklist-update-clients - activeClientIds is 0 and toBeCheckedRecordsIds is %s',
                    count($toBeCheckedRecordsIds)
                )
            );
        }

        $this->entityManager->clear();

        if (gc_enabled()) {
            gc_collect_cycles();
        }

        return isset($toBeCheckedRecordsIds[count($toBeCheckedRecordsIds) - 1]['id'])
            ? (int) $toBeCheckedRecordsIds[count($toBeCheckedRecordsIds) - 1]['id']
            : null
        ;
    }

    /**
     * @return mixed
     */
    private function getDataWithRetries(callable $operation)
    {
        for ($attempt = 1; $attempt <= $this->maxAttempts; $attempt++) {
            try {
                return $operation();
            } catch (DriverException | PDOException $exception) {
                $this->logger->info(
                    'blacklist-update-clients - attempt to retrieve client data',
                    [$attempt]
                );

                if ($attempt === $this->maxAttempts) {
                    $this->logger->info(
                        'blacklist-update-clients - failed while retrieving clients data, aborting',
                        [$exception->getMessage()]
                    );
                }

                $this->reloadEntityManager();
                sleep($this->delayBetweenAttempts * $attempt);
            } catch (Throwable $exception) {
                $this->logger->info(
                    'blacklist-update-clients - error',
                    [
                        $exception->getMessage(),
                        $exception->getCode(),
                    ]
                );

                throw $exception;
            }
        }

        return [];
    }

    private function reloadEntityManager(): void
    {
        $this->registry->resetManager();
        $this->entityManager->getConnection()->close();
        $this->entityManager->getConnection()->connect();
    }
}
