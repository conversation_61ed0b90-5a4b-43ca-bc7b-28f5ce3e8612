<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use DateTime;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistedClientData;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistedClientProfileData;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistSender;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class BlacklistSendReportsCommand extends Command
{
    private ClientBlacklistCheckResultRepository $blacklistCheckResultRepository;
    private BlacklistSender $blacklistSender;

    public function __construct(
        ClientBlacklistCheckResultRepository $blacklistCheckResultRepository,
        BlacklistSender $blacklistSender
    ) {
        parent::__construct();

        $this->blacklistCheckResultRepository = $blacklistCheckResultRepository;
        $this->blacklistSender = $blacklistSender;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:send-reports')
            ->setDescription('Send blacklist client reports')
            ->addArgument('date', InputArgument::OPTIONAL, 'A date/time string', '-1 day')
            ->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'batch size', 500)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $dateFrom = new DateTime($input->getArgument('date'));
        $dateTo = new DateTime();
        $limit = (int)$input->getOption('limit');
        $offset = 0;

        do {
            $clients = [];
            $results = $this->blacklistCheckResultRepository
                ->getLatestBlacklistedCheckResultByPeriodByLimitPerClient(
                    $dateFrom,
                    $dateTo,
                    $offset,
                    $limit
                )
            ;
            foreach ($results as $result) {
                $clientData = (new BlacklistedClientData())
                    ->setClientName($result->getClient()->getDisplayName())
                    ->setClientCovenanteeId($result->getClient()->getCovenanteeId())
                ;
                foreach ($result->getItems() as $item) {
                    $groupKey = $item->getProfile()->getTopCategoryGroup() ? $item->getProfile()->getTopCategoryGroup()->getKey() : '';
                    $categoryInformation = $item->getProfile()->getCategories()->count() ? $item->getProfile()->getCategories()->first()->getName() : '';
                    $profileData = (new BlacklistedClientProfileData())
                        ->setBlacklistName($item->getProfile()->getBlacklist()->getName())
                        ->setExternalId($item->getProfile()->getExternalId())
                        ->setName($item->getProfile()->getDisplayName())
                        ->setTopCategoryKey($groupKey)
                        ->setCategoryInformation($categoryInformation)
                    ;
                    $clientData->addProfile($profileData);
                }
                $clients[] = $clientData;
            }
            if (count($clients) > 0) {
                $this->blacklistSender->send($clients);
            }
            if (count($results) < $limit) {
                break;
            }
            $offset += $limit;
        } while (count($results) > 0);

        return 0;
    }
}
