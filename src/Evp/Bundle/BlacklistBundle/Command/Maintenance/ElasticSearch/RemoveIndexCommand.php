<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch;

use Evp\Bundle\BlacklistBundle\Exception\SanctionsVersionManagerException;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RemoveIndexCommand extends Command
{
    private ConfigurationManager $configurationManager;
    private LoggerInterface $logger;

    public function __construct(
        ConfigurationManager $configurationManager,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->configurationManager = $configurationManager;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('evp:blacklist:maintenance:elastic-search:remove-index')
            ->setDescription('Allows to remove not active index!')
            ->addArgument('indexName', InputArgument::REQUIRED, 'Name of index to remove')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $indexName = $input->getArgument('indexName');

        try {
            $this->configurationManager->getSanctionsVersionManager()->removeIndex($indexName);
        } catch (SanctionsVersionManagerException $exception) {
            $output->writeln($exception->getMessage());

            return 1;
        }

        $this->logger->info('BlacklistBundle: Removing index completed!', [$indexName]);
        $output->writeln(sprintf('Index %s removed!', $indexName));
        return 0;
    }
}
