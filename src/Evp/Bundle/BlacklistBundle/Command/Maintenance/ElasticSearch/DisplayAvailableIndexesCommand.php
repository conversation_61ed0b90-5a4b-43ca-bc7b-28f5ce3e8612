<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch;

use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DisplayAvailableIndexesCommand extends Command
{
    private ConfigurationManager $configurationManager;

    public function __construct(ConfigurationManager $configurationManager)
    {
        parent::__construct();

        $this->configurationManager = $configurationManager;
    }

    protected function configure()
    {
        $this
            ->setName('evp:blacklist:maintenance:elastic-search:display-available-indexes')
            ->setDescription('Allows to display available indexes!')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Indexes:');

        foreach ($this->configurationManager->getSanctionsVersionManager()->getIndexes() as $indexName => $aliases) {
            $output->writeln(
                sprintf(
                    '> %s %s',
                    $indexName,
                    count($aliases['aliases']) ? sprintf('(%s)', implode(', ', array_keys($aliases['aliases']))) : ''
                )
            );
        }

        return 0;
    }
}
