<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Maintenance\ElasticSearch;

use Evp\Bundle\BlacklistBundle\Exception\SanctionsVersionManagerException;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\SanctionsVersionManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MarkIndexAsImportingCommand extends Command
{
    private const SANCTIONS_IMPORTING = 'sanctions_importing';

    private ConfigurationManager $configurationManager;
    private LoggerInterface $logger;

    public function __construct(
        ConfigurationManager $configurationManager,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->configurationManager = $configurationManager;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('evp:blacklist:maintenance:elastic-search:mark-index-as-importing')
            ->setDescription('Allows to mark/unmark index as importing!')
            ->addArgument('indexName', InputArgument::REQUIRED, 'Name of index to mark/unmark')
            ->addOption(
                'unmark',
                null,
                InputOption::VALUE_NONE,
                'Will remove importing alias from the given index'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $indexName = $input->getArgument('indexName');
        $unmark = $input->getOption('unmark');

        try {
            if (!$this->getSanctionsVersionManager()->checkIfBlacklistIndexExists($indexName)) {
                $output->writeln('Index does not exists!');

                return 1;
            }

            if (!$unmark && $this->checkIfOtherImportingIndexesExists()) {
                $output->writeln('There exists other importing index, please unmark it and then continue!');

                return 1;
            }

            $this->getSanctionsVersionManager()->markBlacklistIndexAsImporting($indexName, $unmark);
        } catch (SanctionsVersionManagerException $exception) {
            $output->writeln($exception->getMessage());

            return 1;
        }

        $this->logger->info('BlacklistBundle: Mark index as importing - completed!', [$indexName, $unmark]);
        $output->writeln(sprintf('Index %s %s as importing!', $indexName, $unmark ? 'unmarked' : 'marked'));
        return 0;
    }

    /**
     * @return bool
     *
     * @throws SanctionsVersionManagerException
     */
    private function checkIfOtherImportingIndexesExists(): bool
    {
        foreach (array_keys($this->getSanctionsVersionManager()->getIndexes()) as $indexName) {
            if (
                $this->getSanctionsVersionManager()->checkIfBlacklistIndexHasAlias(
                    $indexName,
                    self::SANCTIONS_IMPORTING
                )
            ) {
                return true;
            }
        }

        return false;
    }

    private function getSanctionsVersionManager(): SanctionsVersionManager
    {
        return $this->configurationManager->getSanctionsVersionManager();
    }
}
