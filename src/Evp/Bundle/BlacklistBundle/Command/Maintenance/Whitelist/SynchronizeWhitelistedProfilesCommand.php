<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Maintenance\Whitelist;

use DateTime;
use Doctrine\DBAL\Exception\DriverException;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistImportMonitoringService;
use Exception;
use Paysera\Bundle\TransferSurveillanceBundle\Service\Whitelist\WhitelistedProfileManager;
use PDOException;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SynchronizeWhitelistedProfilesCommand extends Command
{
    private const EMAILED_LOG_PREFIX = 'TransferSurveillanceBundle exceptional case: ';

    private EntityManagerInterface $entityManager;
    private RegistryInterface $registry;
    private BlacklistRepository $blacklistRepository;
    private WhitelistedProfileManager $whitelistedProfileManager;
    private BlacklistImportMonitoringService $blacklistImportMonitoringService;
    private LoggerInterface $logger;
    private int $maxRetries;
    private int $delay;

    public function __construct(
        EntityManagerInterface $entityManager,
        RegistryInterface $registry,
        BlacklistRepository $blacklistRepository,
        WhitelistedProfileManager $whitelistedProfileManager,
        BlacklistImportMonitoringService $blacklistImportMonitoringService,
        LoggerInterface $logger,
        int $maxRetries,
        int $delay
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->registry = $registry;
        $this->blacklistRepository = $blacklistRepository;
        $this->whitelistedProfileManager = $whitelistedProfileManager;
        $this->blacklistImportMonitoringService = $blacklistImportMonitoringService;
        $this->logger = $logger;
        $this->maxRetries = $maxRetries;
        $this->delay = $delay;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:maintenance:whitelist:synchronize-whitelisted-profiles')
            ->setDescription('Command for synchronize whitelisted profiles')
            ->addArgument('blacklistId', InputArgument::REQUIRED, 'Blacklist id')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklistId = (int) $input->getArgument('blacklistId');
        $output->writeln(sprintf('Starting checking whitelists for blacklist: %d', $blacklistId));

        try {
            $blacklist = $this->getBlacklistWithRetries($blacklistId);

            $this->whitelistedProfileManager->synchronizeProfiles($blacklist);
        } catch (Exception $exception) {
            $message = 'SynchronizeWhitelistedProfiles failed: ' . $exception->getMessage();
            $this->logger->error(self::EMAILED_LOG_PREFIX . $message, [$blacklistId, $exception]);
            $output->writeln($message);

            return 1;
        }

        $syncDate = intval((new DateTime())->format('YmdHi'));
        $this->blacklistImportMonitoringService->storeSynchronizeWhitelistedProfilesData($syncDate);

        $output->writeln(sprintf('Whitelists with blacklistId "%s" was synchronized', $blacklistId));
        return 0;
    }

    /**
     * @throws DriverException
     * @throws PDOException
     * @throws RuntimeException
     */
    private function getBlacklistWithRetries(int $blacklistId, int $retry = 1): Blacklist
    {
        try {
            $blacklist = $this->blacklistRepository->findOneById($blacklistId);

            if ($blacklist === null) {
                throw new RuntimeException(sprintf('Blacklist with id %d not found', $blacklistId));
            }

            return $blacklist;
        } catch (DriverException | PDOException $exception) {
            $message = 'SynchronizeWhitelistedProfiles failed get blacklist';

            if ($retry > $this->maxRetries) {
                $this->logger->error(self::EMAILED_LOG_PREFIX . $message . ', aborting', [$exception]);
                throw $exception;
            }

            $this->logger->info(self::EMAILED_LOG_PREFIX . $message . ', retrying', [$exception]);
            sleep($this->delay * $retry);

            $this->registry->resetManager();
            $this->entityManager->getConnection()->close();
            $this->entityManager->getConnection()->connect();

            return $this->getBlacklistWithRetries($blacklistId, ++$retry);
        }
    }
}
