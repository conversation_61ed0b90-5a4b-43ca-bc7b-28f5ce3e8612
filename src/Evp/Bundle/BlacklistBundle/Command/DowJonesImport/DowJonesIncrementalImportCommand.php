<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\DowJonesImport;

use Evp\Bundle\BlacklistBundle\Service\DowJonesImport\DowJonesIncrementalImport;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

class DowJonesIncrementalImportCommand extends Command
{
    private DowJonesIncrementalImport $dowJonesIncrementalImport;

    public function __construct(DowJonesIncrementalImport $dowJonesIncrementalImport)
    {
        parent::__construct();

        $this->dowJonesIncrementalImport = $dowJonesIncrementalImport;
    }

    protected function configure()
    {
        $this
            ->setName('evp:blacklist:dow-jones-import:incremental-import')
            ->addOption('force', null, InputOption::VALUE_NONE, 'Forcing option')
            ->addOption(
                'maxRetries',
                null,
                InputOption::VALUE_OPTIONAL,
                'Maximum amount of retries to perform on profile saving failure.',
                10
            )
            ->addOption(
                'delay',
                null,
                InputOption::VALUE_OPTIONAL,
                'Number of microseconds to wait before first retry in case of failure.',
                500_000
            )
            ->setDescription('Process and import Dow Jones incremental feed data (Daily)')
        ;
    }

    /**
     * @throws Throwable
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $force = $input->getOption('force');
        $maxRetries = (int)$input->getOption('maxRetries');
        $delay = (int)$input->getOption('delay');

        $this->dowJonesIncrementalImport->import($maxRetries, $delay, $force);
        return 0;
    }
}
