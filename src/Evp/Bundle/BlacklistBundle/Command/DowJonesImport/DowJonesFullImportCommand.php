<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\DowJonesImport;

use Evp\Bundle\BlacklistBundle\Service\DowJonesImport\DowJonesFullImport;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class DowJonesFullImportCommand extends Command
{
    private const PRELIMINARILY_EXPECTED_IMPORTED_RECORDS = 2_000_000;

    private DowJonesFullImport $dowJonesFullImport;

    public function __construct(DowJonesFullImport $dowJonesFullImport)
    {
        parent::__construct();

        $this->dowJonesFullImport = $dowJonesFullImport;
    }

    protected function configure()
    {
        $this
            ->setName('evp:blacklist:dow-jones-import:full-import')
            ->addOption(
                'force',
                null,
                InputOption::VALUE_NONE,
                'Option to force import despite import being up to date'
            )
            ->addOption(
                'expectedCount',
                null,
                InputOption::VALUE_OPTIONAL,
                'The least expected amount of profiles to be published to import. Throw error if there are less than this amount.',
                self::PRELIMINARILY_EXPECTED_IMPORTED_RECORDS
            )
            ->addOption(
                'maxRetries',
                null,
                InputOption::VALUE_OPTIONAL,
                'Maximum amount of retries to perform on profile saving failure.',
                10
            )
            ->addOption(
                'delay',
                null,
                InputOption::VALUE_OPTIONAL,
                'Number of microseconds to wait before first retry in case of failure.',
                500_000
            )
            ->setDescription('Process and import Dow Jones full feed data (Monthly)')
        ;
    }

    /**
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $force = $input->getOption('force');
        $expectedCount = (int)$input->getOption('expectedCount');
        $maxRetries = (int)$input->getOption('maxRetries');
        $delay = (int)$input->getOption('delay');

        $this->dowJonesFullImport->importAllProfiles($output, $expectedCount, $maxRetries, $delay, $force);
        return 0;
    }
}
