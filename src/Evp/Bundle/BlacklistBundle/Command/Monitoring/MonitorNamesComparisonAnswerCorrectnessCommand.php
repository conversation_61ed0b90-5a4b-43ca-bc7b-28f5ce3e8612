<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Monitoring;

use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\AIService\AINameComparisonService;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MonitorNamesComparisonAnswerCorrectnessCommand extends Command
{
    private const LOG_PREFIX = 'BlacklistBundle exceptional case: ';
    private const PROBLEM_NATURAL_HEALTHCHECK = 'natural_healthcheck';
    private const PROBLEM_LEGAL_HEALTHCHECK = 'legal_healthcheck';

    private AINameComparisonService $naturalNameComparisonService;
    private AINameComparisonService $legalNameComparisonService;
    private ConsoleProgressBarHelper $progressBarHelper;
    private MonitoringClient $monitoringClient;
    private LoggerInterface $logger;
    private string $monitoringKey;
    private array $naturalCases;
    private array $legalCases;

    public function __construct(
        AINameComparisonService $naturalNameComparisonService,
        AINameComparisonService $legalNameComparisonService,
        ConsoleProgressBarHelper $progressBarHelper,
        MonitoringClient $monitoringClient,
        LoggerInterface $logger,
        string $monitoringKey,
        array $naturalCases,
        array $legalCases
    ) {
        parent::__construct();

        $this->naturalNameComparisonService = $naturalNameComparisonService;
        $this->legalNameComparisonService = $legalNameComparisonService;
        $this->progressBarHelper = $progressBarHelper;
        $this->monitoringClient = $monitoringClient;
        $this->logger = $logger;
        $this->monitoringKey = $monitoringKey;
        $this->naturalCases = $naturalCases;
        $this->legalCases = $legalCases;
    }

    protected function configure(): void
    {
        $this->setDescription('Monitor names comparison answer correctness');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $startTime = microtime(true);
        $results = $this->getResults($output);

        $this->writeMonitoring($output, $results);
        $this->printTable($output, $results);

        $output->writeln(sprintf('Finished in %.2f seconds', microtime(true) - $startTime));
        return 0;
    }

    private function getResults(OutputInterface $output): array
    {
        $results = [];
        $progressBar = $this->createProgressBar($output, count($this->naturalCases) + count($this->legalCases));

        foreach ($this->naturalCases as $case) {
            $progressBar->advance();

            $namesComparisonResult = $this->naturalNameComparisonService->askAI($case['name1'], $case['name2']);
            $answer = $namesComparisonResult->getDecision() === NamesComparisonResultDto::DECISION_SAME_PERSON;
            $results[] = array_merge($case, ['answer' => $answer, 'type' => 'natural']);
        }

        foreach ($this->legalCases as $case) {
            $progressBar->advance();

            $namesComparisonResult = $this->legalNameComparisonService->askAI($case['name1'], $case['name2']);
            $answer = $namesComparisonResult->getDecision() === NamesComparisonResultDto::DECISION_SAME_PERSON;
            $results[] = array_merge($case, ['answer' => $answer, 'type' => 'legal']);
        }

        $progressBar->finish();
        $output->writeln('');

        return $results;
    }

    private function writeMonitoring(OutputInterface $output, array $results): void
    {
        $totalNatural = count(array_filter($results, fn($result) => $result['type'] === 'natural'));
        $totalLegal = count(array_filter($results, fn($result) => $result['type'] === 'legal'));
        $correctNatural = count(array_filter($results, fn($result) => $result['expected'] === $result['answer'] && $result['type'] === 'natural'));
        $correctLegal = count(array_filter($results, fn($result) => $result['expected'] === $result['answer'] && $result['type'] === 'legal'));
        $percentageNatural = round($totalNatural === 0 ? 0 : $correctNatural / $totalNatural * 100, 2);
        $percentageLegal = round($totalLegal === 0 ? 0 : $correctLegal / $totalLegal * 100, 2);

        $this->monitoringClient->writeValue(
            $this->monitoringKey,
            $percentageNatural,
            ['problem' => self::PROBLEM_NATURAL_HEALTHCHECK],
        );
        $this->monitoringClient->writeValue(
            $this->monitoringKey,
            $percentageLegal,
            ['problem' => self::PROBLEM_LEGAL_HEALTHCHECK],
        );

        if ($percentageNatural < 100) {
            $this->logger->error(self::LOG_PREFIX . 'Incorrect answers detected for natural names', [$percentageNatural]);
        }

        if ($percentageLegal < 100) {
            $this->logger->error(self::LOG_PREFIX . 'Incorrect answers detected for legal names', [$percentageLegal]);
        }

        $output->writeln(sprintf('Correct answers for natural names: %d/%d (%.0f%%)', $correctNatural, $totalNatural, $percentageNatural));
        $output->writeln(sprintf('Correct answers for legal names: %d/%d (%.0f%%)', $correctLegal, $totalLegal, $percentageLegal));
    }

    private function printTable(OutputInterface $output, array $results): void
    {
        $table = new Table($output);
        $table->setHeaders(['Case', 'Type', 'Name 1', 'Name 2', 'Expected', 'Answer', 'Status']);

        foreach ($results as $key => $result) {
            [$name1, $name2, $expected, $answer, $type] = array_values($result);
            $status = $expected === $answer ? 'Correct' : '<fg=red>Incorrect</>';

            $table->addRow([$key + 1, $type, $name1, $name2, $this->boolToString($expected), $this->boolToString($answer), $status]);
        }

        $table->render();
    }

    private function createProgressBar(OutputInterface $output, int $count): ProgressBar
    {
        $progressBar = $this->progressBarHelper->createProgressBar($output, $count);
        $progressBar->setRedrawFrequency(1);
        $progressBar->start();

        return $progressBar;
    }

    private function boolToString(bool $value): string
    {
        return $value ? 'true' : 'false';
    }
}
