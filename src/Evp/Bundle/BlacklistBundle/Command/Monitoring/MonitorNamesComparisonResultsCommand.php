<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\Monitoring;

use DateTimeImmutable;
use Evp\Bundle\BlacklistBundle\Repository\NamesComparisonResultRepository;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\AIService\DTO\NamesComparisonResultDto;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class MonitorNamesComparisonResultsCommand extends Command
{
    private const PROBLEM_FAILURE_REASON = 'failure_reason';
    private const PROBLEM_DECISION = 'decision';
    private const PROBLEM_TRANSFERS_INVOLVED = 'transfers_involved';
    private const PROBLEM_CLIENT_INVOLVED = 'client_involved';

    private NamesComparisonResultRepository $namesComparisonResultRepository;
    private MonitoringClient $monitoringClient;
    private string $monitoringKey;

    public function __construct(
        NamesComparisonResultRepository $namesComparisonResultRepository,
        MonitoringClient $monitoringClient,
        string $monitoringKey
    ) {
        parent::__construct();

        $this->namesComparisonResultRepository = $namesComparisonResultRepository;
        $this->monitoringClient = $monitoringClient;
        $this->monitoringKey = $monitoringKey;
    }

    protected function configure(): void
    {
        $this->setDescription('Monitor names comparison results');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $startTime = microtime(true);

        $todayMidnightDate = new DateTimeImmutable('today midnight');
        $fromId = $this->namesComparisonResultRepository->getFirstIdToDate($todayMidnightDate);

        $this->processFailureReason($fromId);
        $this->processDecision($fromId);
        $this->processTransfersInvolved($fromId);
        $this->processBlacklistInvolved($fromId);

        $output->writeln(sprintf('Finished in %.2f seconds', microtime(true) - $startTime));
        return 0;
    }

    private function processFailureReason(int $fromId): void
    {
        $failureReasons = [
            NamesComparisonResultDto::FAILURE_REASON_NO_DATA,
            NamesComparisonResultDto::FAILURE_REASON_TIMEOUT,
            NamesComparisonResultDto::FAILURE_REASON_CLIENT_ERROR,
            NamesComparisonResultDto::FAILURE_REASON_SERVER_ERROR,
            NamesComparisonResultDto::FAILURE_REASON_CONNECTION_ERROR,
            NamesComparisonResultDto::FAILURE_REASON_UNKNOWN_ERROR,
            NamesComparisonResultDto::FAILURE_REASON_UNEXPECTED_RESPONSE,
            NamesComparisonResultDto::FAILURE_REASON_UNCLASSIFIED,
        ];

        $counts = $this->namesComparisonResultRepository->countGroupedByFailureReason($fromId);

        foreach ($failureReasons as $reason) {
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $counts[$reason] ?? 0,
                ['problem' => self::PROBLEM_FAILURE_REASON, 'reason' => $reason],
            );
        }
    }

    private function processDecision(int $fromId): void
    {
        $decisions = [
            NamesComparisonResultDto::DECISION_SAME_PERSON,
            NamesComparisonResultDto::DECISION_DIFFERENT_PERSON,
            NamesComparisonResultDto::DECISION_ERROR,
        ];

        $counts = $this->namesComparisonResultRepository->countGroupedByDecision($fromId);

        foreach ($decisions as $decision) {
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $counts[$decision] ?? 0,
                ['problem' => self::PROBLEM_DECISION, 'decision' => $decision],
            );
        }
    }

    private function processTransfersInvolved(int $fromId): void
    {
        $counts = [
            'total' => $this->namesComparisonResultRepository->countUniqueTransfers($fromId),
            'prevented' => $this->namesComparisonResultRepository->countUniquePreventedMatchTransfers($fromId),
        ];
        $counts['not_prevented'] = $counts['total'] - $counts['prevented'];

        foreach ($counts as $type => $count) {
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $count,
                ['problem' => self::PROBLEM_TRANSFERS_INVOLVED, 'type' => $type],
            );
        }
    }

    private function processBlacklistInvolved(int $fromId): void
    {
        $counts = [
            'total' => $this->namesComparisonResultRepository-> countUniqueCheckResults($fromId),
            'prevented' => $this->namesComparisonResultRepository->countUniquePreventedClientMatch($fromId),
        ];
        $counts['not_prevented'] = $counts['total'] - $counts['prevented'];

        foreach ($counts as $type => $count) {
            $this->monitoringClient->writeValue(
                $this->monitoringKey,
                $count,
                ['problem' => self::PROBLEM_CLIENT_INVOLVED, 'type' => $type],
            );
        }
    }
}
