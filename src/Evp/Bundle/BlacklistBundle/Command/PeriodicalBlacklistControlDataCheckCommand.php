<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use DateTime;
use Doctrine\DBAL\Exception\DriverException;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\BlacklistProfileDataCheckLog;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Entity\ProfileFilter;
use Evp\Bundle\BlacklistBundle\Exception\PeriodicalBlacklistControlDataCheckException;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\ConfigurationManager;
use Evp\Bundle\BlacklistBundle\Service\PeriodicalBlacklistControlDataCheck\PeriodicalBlacklistControlDataCheckManager;
use Exception;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use PDOException;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use Symfony\Bridge\Doctrine\RegistryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PeriodicalBlacklistControlDataCheckCommand extends Command
{
    private const BLACKLIST_VERSION_PREFIX = '2200';
    private const PROFILES_CHUNK_SIZE = 500;
    private const PROFILES_LIMIT = 10000;

    private BlacklistRepository $blacklistRepository;
    private ProfileRepository $profileRepository;
    private EntityManagerInterface $entityManager;
    private RegistryInterface $registry;
    private ConsoleProgressBarHelper $consoleProgressBarHelper;
    private PeriodicalBlacklistControlDataCheckManager $periodicalBlacklistControlDataCheckManager;
    private ConfigurationManager $configurationManager;
    private OutputInterface $output;
    private int $profilesChunkSize;
    private int $processLimit;
    private int $maxAttempts;
    private int $delayBetweenAttempts;
    private LoggerInterface $logger;

    public function __construct(
        BlacklistRepository $blacklistRepository,
        ProfileRepository $profileRepository,
        EntityManagerInterface $entityManager,
        RegistryInterface $registry,
        ConsoleProgressBarHelper $consoleProgressBarHelper,
        PeriodicalBlacklistControlDataCheckManager $periodicalBlacklistControlDataCheckManager,
        ConfigurationManager $configurationManager,
        LoggerInterface $logger,
        int $maxAttempts,
        int $delayBetweenAttempts
    ) {
        parent::__construct();

        $this->blacklistRepository = $blacklistRepository;
        $this->profileRepository = $profileRepository;
        $this->entityManager = $entityManager;
        $this->registry = $registry;
        $this->consoleProgressBarHelper = $consoleProgressBarHelper;
        $this->periodicalBlacklistControlDataCheckManager = $periodicalBlacklistControlDataCheckManager;
        $this->configurationManager = $configurationManager;
        $this->logger = $logger;
        $this->maxAttempts = $maxAttempts;
        $this->delayBetweenAttempts = $delayBetweenAttempts;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:periodical-blacklist-control-data-check')
            ->setDescription('Provides periodical blacklist profiles data check')
            ->addArgument(
                'blacklistKey',
                InputArgument::OPTIONAL,
                'If not provided command will test all blacklists.'
            )
            ->addOption(
                'incrementVersion',
                '',
                InputOption::VALUE_OPTIONAL,
                'Increment version. Will be used only for external blacklists if blacklist key is not provided.'
            )
            ->addOption(
                'profilesChunkSize',
                '',
                InputOption::VALUE_OPTIONAL,
                'Profiles chunk size to process data control check',
                self::PROFILES_CHUNK_SIZE
            )
            ->addOption(
                'limit',
                '',
                InputOption::VALUE_OPTIONAL,
                'Max amount of items for processing',
                self::PROFILES_LIMIT
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->logger->info('Periodical blacklist data check started!');

        $this->configurationManager->getClient()->setLogger(new NullLogger());
        $this->entityManager->getConnection()->getConfiguration()->setSQLLogger();
        $this->output = $output;

        $blacklistKey = $input->getArgument('blacklistKey');
        $this->profilesChunkSize = (int) $input->getOption('profilesChunkSize');
        $this->processLimit = (int) $input->getOption('limit');

        try {
            $blacklists = $this->resolveBlacklists($blacklistKey);
        } catch (Exception $exception) {
            $output->writeln($exception->getMessage());
            $this->logger->error('Periodical blacklist data check error!', [$exception->getMessage()]);
            return 1;
        }

        $blacklistKeys = join(', ', array_map(fn (Blacklist $blacklist) => $blacklist->getKey(), $blacklists));
        $output->writeln(sprintf( 'Blacklists to process: %s', $blacklistKeys));
        $this->logger->info(sprintf('Blacklists to process: %s', $blacklistKeys));

        foreach ($blacklists as $blacklist) {
            $output->writeln(sprintf("\nProcessing blacklist %s", $blacklist->getKey()));
            $this->logger->info(sprintf('Processing blacklist %s', $blacklist->getKey()));

            try {
                $version = $this->resolveVersion($blacklist, $input);
                $this->runProcessBlacklistWithRetries($blacklist, $version);
            } catch (Exception $exception) {
                $output->writeln($exception->getMessage());
                $this->logger->error('Periodical blacklist data check error!', [$exception->getMessage()]);
            }
        }

        $output->writeln(sprintf('%sProcessing finished!', PHP_EOL));
        $this->logger->info('Periodical blacklist data check finished!');
        return 0;
    }

    /**
     * @throws PeriodicalBlacklistControlDataCheckException
     */
    private function runProcessBlacklistWithRetries(Blacklist $blacklist, ?string $version): void
    {
        for ($attempt = 1; $attempt <= $this->maxAttempts; $attempt++) {
            try {
                $this->processBlacklist($blacklist, $version);
                return;
            } catch (DriverException | PDOException $exception) {
                $this->output->writeln(sprintf("\n-- Error occurred while processing blacklist. [%s]", $attempt));
                $this->logger->error(__METHOD__, [$attempt, $blacklist->getKey(), $exception]);

                $this->reloadEntityManagerAndRepositories();
                sleep($this->delayBetweenAttempts * $attempt);
            }
        }
    }

    /**
     * @throws PeriodicalBlacklistControlDataCheckException
     * @throws DriverException
     */
    private function processBlacklist(Blacklist $blacklist, ?string $version): void
    {
        $progressBar = $this->initProgressBar($blacklist, $version);
        $dataCheckLog = $this->initDataCheckLog($blacklist);

        $profileFilter = (new ProfileFilter())
            ->setBlacklist($blacklist)
            ->setVersion($version)
            ->setLimit($this->profilesChunkSize)
        ;

        do {
            $profileFilter->setOffset($progressBar->getProgress());
            $profiles = $this->profileRepository->findByFilter($profileFilter);

            foreach ($profiles as $profile) {
                $this->processProfile($dataCheckLog, $profile);

                $this->advanceWithRealMemoryUsage($progressBar);
                if ($this->areLimitsReached($progressBar)) {
                    break 2;
                }
            }

            $this->entityManager->clear();
        } while (count($profiles) > 0 && !$this->areLimitsReached($progressBar));

        if ($dataCheckLog->getItemsCount() === 0) {
            return;
        }

        $dataCheckLog
            ->setBlacklist($this->blacklistRepository->findOneByKey($blacklist->getKey()))
            ->setFinishedAt(new DateTime())
            ->setStatus(BlacklistProfileDataCheckLog::STATUS_DONE)
        ;

        $this->entityManager->persist($dataCheckLog);
        $this->entityManager->flush();
    }

    /**
     * @throws PeriodicalBlacklistControlDataCheckException
     */
    private function processProfile(BlacklistProfileDataCheckLog $dataCheckLog, Profile $profile): void
    {
        $profileAlternatives = $this->periodicalBlacklistControlDataCheckManager->getProfileAlternatives(
            $profile
        );

        foreach ($profileAlternatives as $profileAlternative) {
            $results = $this->periodicalBlacklistControlDataCheckManager->processAndLog(
                $dataCheckLog,
                $profileAlternative
            );

            $dataCheckLog->incrementItemsCount();
            if (count($results) > 0) {
                $dataCheckLog->incrementMatchedItemsCount();
            }
        }
    }

    private function areLimitsReached(ProgressBar $progressBar): bool
    {
        return $progressBar->getProgress() > $progressBar->getMaxSteps()
            || $progressBar->getProgress() > $this->processLimit;
    }

    /**
     * @return Blacklist[]
     * @throws Exception
     */
    private function resolveBlacklists(?string $blacklistKey): array
    {
        if ($blacklistKey === null) {
            return $this->blacklistRepository->findNotDeprecated();
        }

        $blacklist = $this->blacklistRepository->findOneByKey($blacklistKey);

        if ($blacklist === null) {
            throw new Exception('Cannot find blacklist by given blacklist key!');
        }

        if ($blacklist->isDeprecated()) {
            throw new Exception('Blacklist is deprecated!');
        }

        return [$blacklist];
    }

    /**
     * @throws Exception
     */
    private function resolveVersion(Blacklist $blacklist, InputInterface $input): ?string
    {
        $blacklistKey = $input->getArgument('blacklistKey');
        $version = $input->getOption('incrementVersion');

        if (!$blacklist->isExternal()) {
            return $blacklistKey === null ? null : $version;
        }

        if ($version !== null && !$this->isVersionValid($blacklist, $version)) {
            $this->logger->warning('Periodical blacklist data check: Incorrect version provided!', [$version]);
            throw new Exception('Incorrect version provided!');
        }

        if ($version === null) {
            $version = (string) $this->profileRepository->findLatestVersion($blacklist);

            if (!$this->isVersionValid($blacklist, $version)) {
                throw new Exception('Cannot find latest version for external blacklist!');
            }
        }

        return $version;
    }

    private function isVersionValid(Blacklist $blacklist, string $version): bool
    {
        if ($blacklist->getKey() === Blacklist::KEY_DOW_JONES_V1) {
            return substr($version, -4) === self::BLACKLIST_VERSION_PREFIX;
        }

        return true;
    }

    private function initProgressBar(Blacklist $blacklist, ?string $version): ProgressBar
    {
        $profileFilter = (new ProfileFilter())
            ->setBlacklist($blacklist)
            ->setVersion($version)
        ;

        $countByFilter = $this->profileRepository->findCountByFilter($profileFilter);
        $progressBar = $this->consoleProgressBarHelper->createProgressBar($this->output, $countByFilter);
        $progressBar->start();

        return $progressBar;
    }

    private function initDataCheckLog(Blacklist $blacklist): BlacklistProfileDataCheckLog
    {
        $dataCheckLog = $this->periodicalBlacklistControlDataCheckManager->initDataCheckLog(
            $blacklist,
            BlacklistProfileDataCheckLog::TYPE_AUTOMATIC,
            false
        );

        return $dataCheckLog->setStatus(BlacklistProfileDataCheckLog::STATUS_PROCESSING);
    }

    private function advanceWithRealMemoryUsage(ProgressBar $progressBar): void
    {
        $progressBar->setMessage(sprintf(
            '[%s/%s bytes]',
            memory_get_usage(),
            memory_get_usage(true)
        ));
        $progressBar->advance();
    }

    private function reloadEntityManagerAndRepositories(): void
    {
        $this->registry->resetManager();
        $this->entityManager->getConnection()->close();
        $this->entityManager->getConnection()->connect();

        // Need to correct track entities after connection reset
        /** @var BlacklistRepository $blacklistRepository */
        $blacklistRepository = $this->entityManager->getRepository(Blacklist::class);
        $this->blacklistRepository = $blacklistRepository;
    }
}
