<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use Evp\Bundle\BlacklistBundle\Entity\BlacklistResult;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Exception;
use InvalidArgumentException;
use Paysera\Client\DowJonesAi\DowJonesAi;
use Paysera\Client\DowJonesAi\Entity\DowJonesAIRequest;
use Paysera\Client\DowJonesAi\Entity\ProfileScoring;
use Paysera\Client\DowJonesAi\Entity\TransferDatum;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Stopwatch\Stopwatch;

class BlacklistSearchCommand extends Command
{
    private BlacklistRepository $blacklistRepository;
    private BlacklistManager $blacklistManager;
    private array $availableBlacklistConfigurations;
    private string $defaultConfiguratioName;
    private DowJonesAi $dowJonesAiClient;

    public function __construct(
        BlacklistRepository $blacklistRepository,
        BlacklistManager $blacklistManager,
        array $availableBlacklistConfigurations,
        string $defaultConfigurationName,
        DowJonesAi $dowJonesAiClient
    ) {
        parent::__construct();

        $this->blacklistRepository = $blacklistRepository;
        $this->blacklistManager = $blacklistManager;
        $this->availableBlacklistConfigurations = $availableBlacklistConfigurations;
        $this->defaultConfiguratioName = $defaultConfigurationName;
        $this->dowJonesAiClient = $dowJonesAiClient;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:search')
            ->setDescription("Check's if given name is in blacklist")
            ->addArgument('name', InputArgument::REQUIRED, 'What to look for?')
            ->addArgument('country', InputArgument::OPTIONAL, 'Country code (example: lt)', '')
            ->addArgument('blacklist', InputArgument::OPTIONAL, 'Blacklist')
            ->addOption('birthday', '', InputOption::VALUE_OPTIONAL, 'Birthday in format YYYY-MM-DD')
            ->addOption('type', '', InputOption::VALUE_OPTIONAL, 'Client type (natural, legal)')
            ->addOption(
                'configuration',
                '',
                InputOption::VALUE_OPTIONAL,
                'Configuration name to use'
            )
            ->addOption(
                'aiConfidence',
                '',
                InputOption::VALUE_OPTIONAL,
                'Will display search results with DJ AI confidence per each profile',
                false
            )
            ->addOption(
                'experimental',
                '',
                InputOption::VALUE_NONE,
                'Search blacklist with experimental features and settings'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $name = $input->getArgument('name');
        $aiConfidence = $input->getOption('aiConfidence') === null;

        if ($name === null) {
            $output->writeln('Please, provide name to be able to proceed with search!');

            return 2;
        }

        $profile = new Profile();
        $profile->setFirstName($name);

        $country = $input->getArgument('country');
        $country = preg_replace('/\s+/', '', $country);
        if (!empty($country)) {
            $profile->setCountry($country); // TODO strtoupper
        }

        $blacklist = $input->getArgument('blacklist');
        if ($blacklist) {
            $blacklist = $this->blacklistRepository->findOneByKey($blacklist);

            if (!$blacklist) {
                throw new InvalidArgumentException('Blacklist does not exist.');
            }

            $profile->setBlacklist($blacklist);
        }

        $birthday = $input->getOption('birthday');
        if ($birthday !== null) {
            $profile->setBirthday($birthday);
        }

        $configurationNameOption = $input->getOption('configuration') ?? $this->defaultConfiguratioName;
        if (!in_array($configurationNameOption, $this->availableBlacklistConfigurations, true)) {
            $output->writeln('Specified configuration doesn\'t exist');
            return 2;
        }

        $stopWatch = new Stopwatch(true);
        $stopWatch->start('DowJonesPerformanceTrack');

        $experimental = $input->getOption('experimental');

        $blacklistResults = $this->blacklistManager->getProfileSearchResults($profile, $configurationNameOption, $experimental);

        if ($aiConfidence === true) {
            try {
                $dowJonesAiCheckResult = $this->getDowJonesAIResult(
                    $profile,
                    $blacklistResults
                );
            } catch (Exception $exception) {
                $output->writeln(
                    sprintf(
                        'Cannot fetch data from DJ AI. Error: %s',
                        $exception->getMessage()
                    )
                );

                throw $exception;
            }
        }

        $stopWatch->stop('DowJonesPerformanceTrack');

        if (count($blacklistResults) > 0) {
            $table = (new Table($output))
                ->setHeaders(['Blacklist', 'Profile ID', 'Found', 'Type', 'Country', 'Score', 'Category', 'Group'])
            ;
            $output->writeln(sprintf('Blacklisted! Searched for [%s]', $profile->getDisplayName()));
            foreach ($blacklistResults as $blacklistResult) {
                $tableRow = [
                    $blacklistResult->getFoundProfile()->getBlacklist()->getName(),
                    $blacklistResult->getFoundProfile()->getId(),
                    $blacklistResult->getFoundProfile()->getDisplayName(),
                    $blacklistResult->getFoundProfile()->getType(),
                    $blacklistResult->getFoundProfile()->getCountry(),
                    $blacklistResult->getScore(),
                    $blacklistResult->getFoundProfile()->getCategories()->get(0)->getFullCategoryName(),
                    $blacklistResult->getFoundProfile()->getTopCategoryGroup()->getName(),
                ];
                $table->addRow($tableRow);
            }
            $table->render();

            if ($aiConfidence === true) {
                $this->displaySearchDatum($profile, $output);
                $this->displayDowJonesAIResult($dowJonesAiCheckResult, $output);
            }
        } else {
            $output->writeln('Not found in blacklist');
        }

        $output->writeln(sprintf('Configuration used: %s', $configurationNameOption));
        $stopWatchEvent = $stopWatch->getEvent('DowJonesPerformanceTrack');
        $output->writeln(sprintf('Profile search took: %s s', $stopWatchEvent->getDuration() / 1000));
        $output->writeln(sprintf('Profile search Used: %s MB', $stopWatchEvent->getMemory() / 1000000));

        return 0;
    }

    private function getDowJonesAIResult(
        Profile $profile,
        array $blacklistResults
    ): array {
        if (count($blacklistResults) === 0) {
            return [];
        }

        $searchDatum = $this->getSearchDatum($profile);

        /** @var TransferDatum[] $searchResults */
        $searchResults = array_map(
            function (BlacklistResult $blacklistResult) {
                $profileDatum = (new TransferDatum())
                    ->setId((string) $blacklistResult->getFoundProfile()->getId())
                    ->setName($blacklistResult->getFoundProfile()->getDisplayName())
                ;

                if (!empty($blacklistResult->getFoundProfile()->getCountry())) {
                    $profileDatum->setCountry(strtolower($blacklistResult->getFoundProfile()->getCountry()));
                }

                return $profileDatum;
            },
            $blacklistResults
        );

        return $this->dowJonesAiClient
            ->createScore(
                (new DowJonesAIRequest())
                    ->setTransferDatum($searchDatum)
                    ->setSearchResults($searchResults)
            )
            ->getItems()
        ;
    }

    private function displaySearchDatum(Profile $profile, OutputInterface $output): void
    {
        $output->writeln('Search parameters for Dow Jones AI:');

        $searchDatum = $this->getSearchDatum($profile);

        $searchTable = (new Table($output))
            ->setHeaders(
                [
                    'Name',
                    'Country',
                ]
            )
        ;

        $searchTable->addRow([
            $searchDatum->getName(),
            $searchDatum->getCountry(),
            $searchDatum->getClientType(),
        ]);

        $searchTable->render();
    }

    private function displayDowJonesAIResult(
        array $dowJonesAiCheckResult,
        OutputInterface $output
    ): void {
        if (count($dowJonesAiCheckResult) === 0) {
            $output->writeln('No data from DJ AI!');

            return;
        }

        $output->writeln('Dow Jones AI result:');

        $table = (new Table($output))->setHeaders(['Profile ID', 'Confidence']);

        foreach ($dowJonesAiCheckResult as $dowJonesAiCheckItem) {
            $profileScoring = new ProfileScoring($dowJonesAiCheckItem);

            $table->addRow(
                [
                    $profileScoring->getSearchResultId(),
                    $profileScoring->getConfidence(),
                ]
            );
        }

        $table->render();
    }

    private function getSearchDatum(Profile $profile): TransferDatum
    {
        $searchDatum = (new TransferDatum())
            ->setId('0')
            ->setName($profile->getFirstName())
        ;

        if (!empty($profile->getCountry())) {
            $searchDatum->setCountry(strtolower($profile->getCountry()));
        }

        return $searchDatum;
    }
}
