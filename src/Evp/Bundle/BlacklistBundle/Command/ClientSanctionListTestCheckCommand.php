<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command;

use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Service\BlacklistManager;
use Exception;
use Paysera\Component\CsvReader\CsvReader;
use Paysera\Component\CsvWriter\CsvWriter;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ClientSanctionListTestCheckCommand extends Command
{
    private CsvReader $csvReader;
    private CsvWriter $csvWriter;
    private BlacklistManager $blacklistManager;
    private string $configurationName;
    private LoggerInterface $logger;

    public function __construct(
        CsvReader $csvReader,
        CsvWriter $csvWriter,
        BlacklistManager $blacklistManager,
        string $configurationName,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->csvReader = $csvReader;
        $this->csvWriter = $csvWriter;
        $this->blacklistManager = $blacklistManager;
        $this->configurationName = $configurationName;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:client-sanction-list-test-check')
            ->setDescription('Checks a list of clients against sanctions list and outputs data')
            ->addArgument(
                'path',
                InputArgument::REQUIRED,
                'Path to input file'
            )
            ->addOption(
                'startingLine',
                null,
                InputOption::VALUE_OPTIONAL,
                'The line number from where to start processing the file.',
                1
            )
            ->addOption(
                'startingId',
                null,
                InputOption::VALUE_OPTIONAL,
                'Similarly to \'startingLine\', start processing from ID. Combining with \'starting\' isn\'t recommended.',
                ''
            )
            ->addOption(
                'outputPath',
                null,
                InputOption::VALUE_OPTIONAL,
                'Output path for csv result data.',
                '/tmp/output.csv'
            )
            ->addOption(
                'overwriteOutputFile',
                null,
                InputOption::VALUE_NONE,
                'Overwrite instead of append to output file'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $path = $input->getArgument('path');
        $outputPath = $input->getOption('outputPath');
        $startingLine = $input->getOption('startingLine');
        $overwriteOutputFile = $input->getOption('overwriteOutputFile');
        $startingId = $input->getOption('startingId');

        $output->writeln('Sanction list input file processing started');
        $lineNumber = 0;
        $lastId = '';
        $foundStartingId = empty($startingId);
        $progressBar = new ProgressBar($output);
        try {
            $readLines = $this->csvReader->readLines($path, false);
            $writeHandle = $this->csvWriter->open($outputPath, $overwriteOutputFile);
            $progressBar->start();
            foreach ($readLines as $line) {
                $progressBar->advance();
                $lineNumber++;
                if ($lineNumber < $startingLine) {
                    continue;
                }
                $mappedLine = $this->mapInputLineToArray($line);
                $lastId = $mappedLine['guid'];
                if ($startingId === $mappedLine['guid']) {
                    $foundStartingId = true;
                }
                if (!$foundStartingId) {
                    continue;
                }
                $profile = $this->mapArrayToProfile($mappedLine);
                $results = $this->blacklistManager->getProfileSearchResults($profile, $this->configurationName, true);

                if (count($results) > 0) {
                    foreach ($results as $blacklistResult) {
                        $this->csvWriter->writeFields(
                            $writeHandle,
                            $this->getOutputLine(
                                $mappedLine['guid'],
                                'needs_review',
                                $blacklistResult->getFoundProfile(),
                            )
                        );
                    }
                } else {
                    $this->csvWriter->writeFields(
                        $writeHandle,
                        $this->getOutputLine($mappedLine['guid'], 'none'),
                    );
                }
            }
            $progressBar->finish();
            $output->writeln('');
        } catch (Exception $exception) {
            $message = sprintf(
                'Sanction list input file processing failed. Last Id: %s, last line number: %s',
                $lastId,
                $lineNumber
            );
            $output->writeln($message);
            $this->logger->warning($message);
            throw $exception;
        } finally {
            if (isset($writeHandle)) {
                $this->csvWriter->close($writeHandle);
            }
        }
        $output->writeln('Sanction list input file processing finished');

        return 0;
    }

    private function mapInputLineToArray($data): array
    {
        return [
            'guid' => $data[0],
            'name' => $data[1],
            'client_type' => $data[2],
            'country_code' => $data[3],
            'client_code' => $data[4],
            'dob' => $data[5],
        ];
    }

    private function mapArrayToProfile($data): Profile
    {
        return (new Profile())
            ->setName($data['name'])
            ->setType($data['client_type'])
            ->setCountry($data['country_code'])
            ->setCode($data['client_code'])
            ->setBirthday($data['dob'])
        ;
    }

    private function getOutputLine(string $guid, string $result, Profile $profile = null): array
    {
        return [
            $guid,
            $result,
            $profile !== null ? $profile->getExternalId() : '',
            $profile !== null ? $profile->getBlacklist()->getName() : '',
            $profile !== null ? $profile->getCountry() : '',
            $profile !== null ? $profile->getType() : '',
            $profile !== null ? $profile->getBirthday() : '',
            $profile !== null ? implode(',', $profile->getCategories()->getValues()) : '',
            $profile !== null ? $profile->getDisplayName() : '',
        ];
    }
}
