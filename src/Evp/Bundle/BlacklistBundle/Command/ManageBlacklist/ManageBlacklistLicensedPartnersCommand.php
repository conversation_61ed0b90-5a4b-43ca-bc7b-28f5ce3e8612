<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\ManageBlacklist;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use RuntimeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ManageBlacklistLicensedPartnersCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;
    private LicensedPartnerRepository $licensedPartnerRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        BlacklistRepository $blacklistRepository,
        LicensedPartnerRepository $licensedPartnerRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
        $this->licensedPartnerRepository = $licensedPartnerRepository;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:manage-blacklist:licensed-partners')
            ->setDescription('Command to manage blacklist licensed partners')
            ->addArgument('blacklistKey', InputArgument::REQUIRED, 'Blacklist key')
            ->addArgument('licensedPartnerCodes', InputArgument::IS_ARRAY, 'Licensed partner codes')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklist = $this->resolveBlacklist($input);
        $licensedPartners = $this->resolveLicensedPartners($input);

        $blacklist->setLicensedPartners(new ArrayCollection($licensedPartners));
        $this->entityManager->flush();

        $output->writeln(sprintf('Blacklist "%s" has been updated', $blacklist->getName()));
        return 0;
    }

    private function resolveBlacklist(InputInterface $input): Blacklist
    {
        $blacklistKey = $input->getArgument('blacklistKey');
        $blacklist = $this->blacklistRepository->findOneByKey($blacklistKey);

        if ($blacklist === null) {
            throw new RuntimeException(sprintf('Blacklist with key "%s" not found', $blacklistKey));
        }

        return $blacklist;
    }

    /**
     * @return LicensedPartner[]
     */
    private function resolveLicensedPartners(InputInterface $input): array
    {
        $licensedPartnerCodes = array_unique($input->getArgument('licensedPartnerCodes'));
        $licensedPartners = [];

        foreach ($licensedPartnerCodes as $partnerCode) {
            $licensedPartner = $this->licensedPartnerRepository->findOneByPartnerCode($partnerCode);
            if ($licensedPartner === null) {
                throw new RuntimeException(sprintf('Licensed partner with code "%s" not found', $partnerCode));
            }

            $licensedPartners[] = $licensedPartner;
        }

        return $licensedPartners;
    }
}
