<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\ManageBlacklist;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use RuntimeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ManageBlacklistCreateCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        BlacklistRepository $blacklistRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:manage-blacklist:create')
            ->setDescription('Command to create blacklist')
            ->addArgument('key', InputArgument::REQUIRED, 'Key')
            ->addArgument('name', InputArgument::REQUIRED, 'Name')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $key = $this->resolveKey($input);
        $name = $input->getArgument('name');
        $version = $this->generateVersion();

        $blacklist = (new Blacklist($key, $name))->setVersion($version);

        $this->entityManager->persist($blacklist);
        $this->entityManager->flush();

        $output->writeln(sprintf('Blacklist with key "%s" was created', $key));
        return 0;
    }

    private function resolveKey(InputInterface $input): string
    {
        $key = $input->getArgument('key');

        if ($this->blacklistRepository->findOneByKey($key) !== null) {
            throw new RuntimeException(sprintf('Blacklist with key "%s" already exists', $key));
        }

        return $key;
    }

    private function generateVersion(): string
    {
        return (new DateTime())->format('YmdHi');
    }
}
