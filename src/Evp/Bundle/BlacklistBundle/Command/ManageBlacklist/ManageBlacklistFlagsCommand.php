<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\ManageBlacklist;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use RuntimeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;

class ManageBlacklistFlagsCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;

    private bool $external;
    private bool $protected;
    private bool $deprecated;
    private bool $periodicCheckNeeded;
    private bool $periodicCheckReportNeeded;

    public function __construct(
        EntityManagerInterface $entityManager,
        BlacklistRepository $blacklistRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
    }

    protected function configure(): void
    {
        // Flags will be set to true if option is present and false if not
        $this
            ->setName('evp:blacklist:manage-blacklist:flags')
            ->setDescription('Manage blacklist flags')
            ->addArgument('blacklistKey', InputArgument::REQUIRED, 'Blacklist key')
            ->addOption('external', null, InputOption::VALUE_NONE)
            ->addOption('protected', null, InputOption::VALUE_NONE)
            ->addOption('deprecated', null, InputOption::VALUE_NONE)
            ->addOption('periodicCheckNeeded', null, InputOption::VALUE_NONE)
            ->addOption('periodicCheckReportNeeded', null, InputOption::VALUE_NONE)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklist = $this->resolveBlacklist($input);
        $this->resolveFlags($input);

        $confirmed = $this->confirm($input, $output, $blacklist);
        if (!$confirmed) {
            $output->writeln('Aborted');
            return 1;
        }

        $this->updateBlacklist($blacklist);
        $this->entityManager->flush();

        $output->writeln('Done');
        return 0;
    }

    private function confirm(InputInterface $input, OutputInterface $output, Blacklist $blacklist): bool
    {
        $output->writeln(sprintf(
            'You are about to update %s (ID %d) blacklist flags',
            $blacklist->getName(),
            $blacklist->getId()
        ));

        $table = new Table($output);
        $table->setHeaders(['Field', 'Current', 'New']);
        $table->addRows([
            ['External', $blacklist->isExternal(), $this->external],
            ['Protected', $blacklist->isProtected(), $this->protected],
            ['Deprecated', $blacklist->isDeprecated(), $this->deprecated],
            ['Periodic check needed', $blacklist->isPeriodicCheckNeeded(), $this->periodicCheckNeeded],
            ['Periodic check report needed', $blacklist->isPeriodicCheckReportNeeded(), $this->periodicCheckReportNeeded],
        ]);
        $table->render();

        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion('Are you sure you want to continue? (y/n) ', true);

        return $helper->ask($input, $output, $question);
    }

    private function updateBlacklist(Blacklist $blacklist): void
    {
        $blacklist->setExternal($this->external);
        $blacklist->setProtected($this->protected);
        $blacklist->setDeprecated($this->deprecated);
        $blacklist->setPeriodicCheckNeeded($this->periodicCheckNeeded);
        $blacklist->setPeriodicCheckReportNeeded($this->periodicCheckReportNeeded);
    }

    private function resolveBlacklist(InputInterface $input): Blacklist
    {
        $blacklistKey = $input->getArgument('blacklistKey');
        $blacklist = $this->blacklistRepository->findOneByKey($blacklistKey);

        if ($blacklist === null) {
            throw new RuntimeException(sprintf('Blacklist with key %s not found', $blacklistKey));
        }

        return $blacklist;
    }

    private function resolveFlags(InputInterface $input): void
    {
        $this->external = $input->getOption('external');
        $this->protected = $input->getOption('protected');
        $this->deprecated = $input->getOption('deprecated');
        $this->periodicCheckNeeded = $input->getOption('periodicCheckNeeded');
        $this->periodicCheckReportNeeded = $input->getOption('periodicCheckReportNeeded');
    }
}
