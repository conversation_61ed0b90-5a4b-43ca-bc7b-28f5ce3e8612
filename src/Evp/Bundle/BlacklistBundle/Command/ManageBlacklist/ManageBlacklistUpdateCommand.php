<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\ManageBlacklist;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use RuntimeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;

class ManageBlacklistUpdateCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        BlacklistRepository $blacklistRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:manage-blacklist:update')
            ->setDescription('Command to update blacklist')
            ->addArgument('blacklistId', InputArgument::REQUIRED, 'Blacklist id')
            ->addOption('key', null, InputArgument::OPTIONAL, 'New blacklist key')
            ->addOption('name', null, InputArgument::OPTIONAL, 'New blacklist name')
            ->addOption('new-version', null, InputArgument::OPTIONAL, 'New blacklist version')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklist = $this->resolveBlacklist($input);
        $newKey = $this->resolveKey($input);
        $newName = $input->getOption('name');
        $newVersion = $input->getOption('new-version');

        if ($blacklist->isProtected()) {
            throw new RuntimeException('Blacklist is protected and cannot be updated');
        }

        $confirmed = $this->confirm($input, $output, $blacklist, $newKey, $newName, $newVersion);
        if (!$confirmed) {
            $output->writeln('Aborted');
            return 1;
        }

        $this->updateBlacklist($blacklist, $newKey, $newName, $newVersion);
        $output->writeln('Blacklist was updated');
        return 0;
    }

    private function updateBlacklist(Blacklist $blacklist, ?string $newKey, ?string $newName, ?string $newVersion): void
    {
        if ($newKey !== null) {
            $blacklist->setKey($newKey);
        }

        if ($newName !== null) {
            $blacklist->setName($newName);
        }

        if ($newVersion !== null) {
            $blacklist->setVersion($newVersion);
        }

        $this->entityManager->flush();
    }

    private function confirm(
        InputInterface $input,
        OutputInterface $output,
        Blacklist $blacklist,
        ?string $newKey,
        ?string $newName,
        ?string $newVersion
    ): bool {
        $output->writeln(sprintf(
            'Blacklist %s (ID %d) will be updated with following data',
            $blacklist->getName(),
            $blacklist->getId()
        ));

        $table = new Table($output);
        $table->setHeaders(['Field', 'Current', 'New']);
        $table->addRows([
            ['Key', $blacklist->getKey(), $newKey ?? $blacklist->getKey()],
            ['Name', $blacklist->getName(), $newName ?? $blacklist->getName()],
            ['Version', $blacklist->getVersion(), $newVersion ?? $blacklist->getVersion()],
        ]);
        $table->render();

        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion('Are you sure you want to continue? (y/n) ', true);

        return $helper->ask($input, $output, $question);
    }

    private function resolveBlacklist(InputInterface $input): Blacklist
    {
        $blacklistId = (int) $input->getArgument('blacklistId');
        $blacklist = $this->blacklistRepository->findOneById($blacklistId);

        if ($blacklist === null) {
            throw new RuntimeException(sprintf('Blacklist with id %d not found', $blacklistId));
        }

        return $blacklist;
    }

    private function resolveKey(InputInterface $input): ?string
    {
        $key = $input->getOption('key');

        if ($key === null) {
            return null;
        }

        if ($this->blacklistRepository->findOneByKey($key) !== null) {
            throw new RuntimeException(sprintf('Blacklist with key "%s" already exists', $key));
        }

        return $key;
    }
}
