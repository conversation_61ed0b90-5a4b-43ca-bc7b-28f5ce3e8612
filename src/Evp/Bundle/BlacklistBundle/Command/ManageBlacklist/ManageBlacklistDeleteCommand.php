<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Command\ManageBlacklist;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\ProfileFilter;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use RuntimeException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;

class ManageBlacklistDeleteCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private BlacklistRepository $blacklistRepository;
    private ProfileRepository $profileRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        BlacklistRepository $blacklistRepository,
        ProfileRepository $profileRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->blacklistRepository = $blacklistRepository;
        $this->profileRepository = $profileRepository;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:blacklist:manage-blacklist:delete')
            ->setDescription('Command to delete blacklist')
            ->addArgument('blacklistId', InputArgument::REQUIRED, 'Blacklist id')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $blacklist = $this->resolveBlacklist($input);

        $this->checkBlacklist($blacklist);
        $this->checkBlacklistProfiles($blacklist);

        if (!$this->confirm($input, $output, $blacklist)) {
            $output->writeln('Aborted');
            return 1;
        }

        $this->entityManager->remove($blacklist);
        $this->entityManager->flush();
        $output->writeln('Blacklist deleted');
        return 0;
    }

    private function confirm(InputInterface $input, OutputInterface $output, Blacklist $blacklist): bool
    {
        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion(
            sprintf('Are you sure you want to delete blacklist %s? (y/n) ', $blacklist->getName()),
            true
        );

        return $helper->ask($input, $output, $question);
    }

    private function checkBlacklist(Blacklist $blacklist): void
    {
        if ($blacklist->isProtected()) {
            throw new RuntimeException('Blacklist is protected and cannot be deleted');
        }
    }

    private function checkBlacklistProfiles(Blacklist $blacklist): void
    {
        $profileFilter = (new ProfileFilter())->setBlacklist($blacklist);
        $blacklistProfiles = $this->profileRepository->findCountByFilter($profileFilter);

        if ($blacklistProfiles > 0) {
            throw new RuntimeException(sprintf(
                'Blacklist has %s profiles. It is not possible to delete blacklist with profiles',
                number_format($blacklistProfiles)
            ));
        }
    }

    private function resolveBlacklist(InputInterface $input): Blacklist
    {
        $blacklistId = (int) $input->getArgument('blacklistId');
        $blacklist = $this->blacklistRepository->findOneById($blacklistId);

        if ($blacklist === null) {
            throw new RuntimeException(sprintf('Blacklist with id %d not found', $blacklistId));
        }

        return $blacklist;
    }
}
