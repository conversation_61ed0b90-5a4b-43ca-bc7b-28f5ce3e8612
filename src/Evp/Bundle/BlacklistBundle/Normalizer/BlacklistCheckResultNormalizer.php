<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Normalizer;

use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResult;
use Paysera\Component\Serializer\Normalizer\NormalizerInterface;

class BlacklistCheckResultNormalizer implements NormalizerInterface
{
    /**
     * @param ClientBlacklistCheckResult $entity
     * @return array
     */
    public function mapFromEntity($entity)
    {
        $data = [
            'id' => $entity->getId(),
            'checked_at' => $entity->getCheckedAt(),
            'blacklist' => [
                'id' => $entity->getBlacklist()->getId(),
                'key' => $entity->getBlacklist()->getKey(),
            ],
            'blacklisted' => $entity->isBlacklisted()
        ];

        $resultItems = [];

        foreach ($entity->getItems() as $item) {
            $resultItems[] = [
                'profile_id' => $item->getProfile()->getId(),
                'profile_first_name' => $item->getProfile()->getFirstName(),
                'profile_middle_name' => $item->getProfile()->getMiddleName(),
                'profile_last_name' => $item->getProfile()->getLastName(),
                'profile_country' => $item->getProfile()->getCountry(),
                'profile_external_id' => $item->getProfile()->getExternalId(),
                'confidence' => $item->getConfidence(),
                'category_name' => $item->getProfile()->getTopCategoryGroup()->getName()
            ];
        }

        $data['result_items'] = $resultItems;

        return $data;
    }
}
