<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Admin;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\AdminBundle\Admin\BaseAdmin;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResultItem;
use Evp\Bundle\BlacklistBundle\Entity\ClientBlacklistCheckResultItemReviewLog;
use Evp\Bundle\BlacklistBundle\Entity\ClientSanctionsScreening;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Entity\PartnerClient;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\Filter\ChoiceType as ChoiceTypeFilter;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Datagrid\ProxyQuery;
use Sonata\Form\Type\EqualType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class ClientSanctionsScreeningAdmin extends BaseAdmin
{
    private const REFERER_URL = 'ClientSanctionsScreeningAdminRefererUrl';

    private EntityManagerInterface $entityManager;
    private TokenStorageInterface $tokenStorage;
    private Session $session;

    public function configure(): void
    {
        parent::configure();

        $this->datagridValues['_sort_by'] = 'id';
        $this->datagridValues['_sort_order'] = 'DESC';
    }

    public function setSession(Session $session): void
    {
        $this->session = $session;
    }

    public function setReferer(string $referer): void
    {
        $parsedReferer = parse_url($referer);
        $pathAndQueryReferer = null;
        if (array_key_exists('path', $parsedReferer)) {
            $pathAndQueryReferer = $parsedReferer['path'];
            if (array_key_exists('query', $parsedReferer)) {
                $pathAndQueryReferer .= '?' . $parsedReferer['query'];
            }
        }
        if ($pathAndQueryReferer !== null) {
            $this->session->set(self::REFERER_URL, $pathAndQueryReferer);
        }
    }

    public function getReferer(): string
    {
        return (string)$this->session->get(self::REFERER_URL);
    }

    public function setEntityManager(EntityManagerInterface $entityManagerInterface): void
    {
        $this->entityManager = $entityManagerInterface;
    }

    public function getEntityManager(): EntityManagerInterface
    {
        return $this->entityManager;
    }

    public function setTokenStorage(TokenStorageInterface $tokenStorage): void
    {
        $this->tokenStorage = $tokenStorage;
    }

    public function getTokenStorage(): TokenStorageInterface
    {
        return $this->tokenStorage;
    }

    protected function configureFormFields(FormMapper $form)
    {
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('id', 'text')
            ->add('clientBlacklistCheckResult.blacklist', 'text', [
                'label' => 'Blacklist',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_blacklist.html.twig'
            ])
            ->add('client', 'string', [
                'label' => 'Client',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_client.html.twig',
            ])
            ->add('status', 'text', [
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list__reformatted_string_field.html.twig',
            ])
            ->add('clientBlacklistCheckResult.checkedAt', 'datetime', [
                'label' => 'Checked at',
            ])
            ->add('client.countryCode', 'text', [
                'label' => 'Country code',
            ])
            ->add('profile_confidence', 'text', [
                'label' => 'AI result confidence',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_confidence.html.twig'
            ])
            ->add('profile_id', 'text', [
                'label' => 'Profile External ID',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_ids.html.twig',
            ])
            ->add('profile_name', 'text', [
                'label' => 'Blacklist profile name',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_names.html.twig',
            ])
            ->add('profile_countries', 'text', [
                'label' => 'Blacklist profile country',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_countries.html.twig',
            ])
            ->add('profile_dates', 'text', [
                'label' => 'Blacklist profile date of birth',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_dates.html.twig',
            ])
            ->add('profile_categories', 'text', [
                'label' => 'Blacklist profile category',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list_field_profile_categories.html.twig',
            ])
            ->add('reviewerId', 'text', [
                'label' => 'Reviewer',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list__field_reviewer.html.twig',
            ])
            ->add('_actions', 'actions', [
                'actions' => [
                    'startReview' => [
                        'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:list__action_start_review.html.twig',
                    ],
                ],
            ]);
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('id')
            ->add('clientBlacklistCheckResult.blacklist.name', 'text', [
                'label' => 'Blacklist',
            ])
            ->add('client', 'string', [
                'label' => 'Client',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_client.html.twig',
            ])
            ->add('status', 'string', [
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__reformatted_string_field.html.twig'
            ])
            ->add('reviewAction', 'string', [
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__reformatted_string_field.html.twig'
            ])
            ->add('reviewerId', 'text', [
                'label' => 'Reviewer',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_reviewer.html.twig',
            ])
            ->add('client.countryCode')
            ->add('matchedProfiles', 'text', [
                'label' => 'Matched profiles',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show_matched_profiles.html.twig'
            ])
            ->add('actionLog', null, [
                'label' => 'Review Action Log',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_action_log.html.twig'
            ])
            ->add('reviewedItems', null, [
                'label' => 'Reviewed Items',
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_reviewed_items.html.twig'
            ])
            ->add('comments', null, [
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_comments.html.twig'
            ])
            ->add('relatedWhitelists', 'string', [
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_related_whitelists.html.twig'
            ])
            ->add('actions', null, [
                'virtual_field' => true,
                'template' => 'EvpBlacklistBundle:Admin\ClientSanctionScreening:show__field_review_actions.html.twig'
            ])
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('clientBlacklistCheckResult.blacklist', 'doctrine_orm_choice', [
                'label' => 'Blacklist',
                'field_type' => EntityType::class,
                'field_options' => [
                    'class' => Blacklist::class,
                    'query_builder' => function (BlacklistRepository $blacklistRepository) {
                        return $blacklistRepository->getNotDeprecatedPayseraSanctionListQueryBuilder();
                    },
                ],
            ])
            ->add('covenanteeId', 'doctrine_orm_callback', [
                'label' => 'Client ID',
                'callback' => [$this, 'covenanteeIdCallback'],
            ])
            ->add('clientType', 'doctrine_orm_callback', [
                'label' => 'Client Category',
                'callback' => [$this, 'clientTypeCallback'],
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Natural clients' => Client::TYPE_NATURAL,
                        'Legal clients' => Client::TYPE_LEGAL,
                    ],
                ],
            ])
            ->add('currentClientLicensedPartner', 'doctrine_orm_callback', [
                'label' => 'Client licensed partner',
                'callback' => [$this, 'clientLicensedPartnerCallback'],
                'field_type' => EntityType::class,
                'field_options' => [
                    'class' => LicensedPartner::class,
                    'query_builder' => function (LicensedPartnerRepository $licensedPartnerRepository) {
                        return $licensedPartnerRepository->getFindAllQueryBuilder();
                    },
                ],
            ])
            ->add('status', 'doctrine_orm_choice', [
                    'field_type' => 'choice',
                    'field_options' => [
                        'choices' => $this->getStatusChoices(),
                    ],
                ]
            )
            ->add(
                'clientBlacklistCheckResult.checkedAt', 'doctrine_orm_datetime_range', [
                    'field_type' => 'sonata_type_datetime_range_picker',
                    'label' => 'Checked at'
                ]
            )
            ->add('countryCode', 'doctrine_orm_callback', [
                'callback' => [$this, 'countryCodeCallback'],
                'field_type' => 'text',
            ])
            ->add('profileExternalId', 'doctrine_orm_callback', [
                'field_type' => 'text',
                'label' => 'Profile External ID',
                'callback' => [$this, 'profileExternalIdCallback']
            ])
            ->add('profileCountry', 'doctrine_orm_callback', [
                'field_type' => 'text',
                'label' => 'Profile country',
                'callback' => [$this, 'profileCountryCallback'],
                'operator_type' => ChoiceType::class,
                'operator_options' => [
                    'choices'=> [
                        'contains' => ChoiceTypeFilter::TYPE_CONTAINS,
                        'not contains' => ChoiceTypeFilter::TYPE_NOT_CONTAINS,
                        'is equals to' => ChoiceTypeFilter::TYPE_EQUAL,
                    ],
                ],
            ])
        ;

        parent::configureDatagridFilters($filter);
    }

    protected function configureDefaultFilterValues(array &$filterValues): void
    {
        $filterValues['status'] = [
            'type' => EqualType::TYPE_IS_NOT_EQUAL,
            'value' => ClientSanctionsScreening::STATUS_COMPLETED,
        ];
    }

    public function configureActionButtons($action, $object = null): array
    {
        $list = parent::configureActionButtons($action, $object);

        return $list;
    }

    protected function configureRoutes(RouteCollection $collection): void
    {
        $collection->clearExcept(['list', 'show']);
        $collection
            ->add('startReview', $this->getRouterIdParameter() . '/start-review')
            ->add('review', $this->getRouterIdParameter() . '/review');

        parent::configureRoutes($collection);
    }

    public function countryCodeCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $queryBuilder = $query->getQueryBuilder();
        $this->joinClientTable($queryBuilder, $alias);

        $queryBuilder
            ->andWhere('cn.countryCode = :code OR cl.countryCode = :code')
            ->setParameter('code', $value['value'])
        ;

        return true;
    }

    public function profileExternalIdCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $profileId = $value['value'];

        $queryBuilder = $query->getQueryBuilder();
        $queryBuilder
            ->join(ClientBlacklistCheckResultItem::class, 'cbcri', Join::WITH, sprintf('cbcri.clientBlacklistCheckResult = %s.clientBlacklistCheckResult', $alias))
            ->leftJoin(ClientBlacklistCheckResultItemReviewLog::class, 'cbcrirl', 'WITH', 'cbcrirl.clientBlacklistCheckResultItem = cbcri')
            ->andWhere('cbcrirl.id IS NULL')
            ->andWhere('cbcri.profile = :profileId')
            ->setParameter('profileId', $profileId)
        ;

        return true;
    }

    public function profileCountryCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $countryCode = mb_strtolower($value['value']);
        $operator = $value['type'] ?? null;

        $choices = [
            ChoiceTypeFilter::TYPE_CONTAINS,
            ChoiceTypeFilter::TYPE_NOT_CONTAINS,
            ChoiceTypeFilter::TYPE_EQUAL,
        ];

        if (!in_array($operator, $choices, true)) {
            return false;
        }

        $qb = $query->getQueryBuilder();
        $qb
            ->join(ClientBlacklistCheckResultItem::class, 'cbcri', Join::WITH, sprintf('cbcri.clientBlacklistCheckResult = %s.clientBlacklistCheckResult', $alias))
            ->join(Profile::class, 'bp', Join::WITH, 'cbcri.profile = bp')
            ->leftJoin(ClientBlacklistCheckResultItemReviewLog::class, 'cbcrirl', 'WITH', 'cbcrirl.clientBlacklistCheckResultItem = cbcri')
            ->andWhere('cbcrirl.id IS NULL')
        ;

        if ($operator === ChoiceTypeFilter::TYPE_CONTAINS) {
            $qb->andWhere($qb->expr()->like('bp.country', ':countryCode'))
                ->setParameter('countryCode', '%' . $countryCode . '%');
        } elseif ($operator === ChoiceTypeFilter::TYPE_NOT_CONTAINS) {
            $qb->andWhere($qb->expr()->notLike('bp.country', ':countryCode'))
                ->setParameter('countryCode', '%' . $countryCode . '%');
        } elseif ($operator === ChoiceTypeFilter::TYPE_EQUAL) {
            $qb->andWhere('bp.country = :countryCode')
                ->setParameter('countryCode', $countryCode);
        }

        return true;
    }

    public function clientLicensedPartnerCallback($queryBuilder, $alias, $field, $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        /** @var LicensedPartner $licensedPartner */
        $licensedPartner = $value['value'];

        $queryBuilder
            ->join(PartnerClient::class, 'pc', Join::WITH, sprintf('pc.client = %s.client', $alias))
            ->andWhere('pc.partnerCode = :partnerCode')
            ->andWhere('pc.assignedFrom <= :currentDate')
            ->andWhere('pc.assignedTo > :currentDate OR pc.assignedTo IS NULL')
            ->setParameter('partnerCode', $licensedPartner->getPartnerCode())
            ->setParameter('currentDate', new DateTime())
        ;

        return true;
    }

    public function clientTypeCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $queryBuilder = $query->getQueryBuilder();
        $this->joinClientTable($queryBuilder, $alias);

        $queryBuilder
            ->andWhere('c INSTANCE OF :clientType')
            ->setParameter('clientType', $value['value'])
        ;

        return true;
    }

    public function covenanteeIdCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $query->getQueryBuilder()
            ->leftJoin(Client::class, 'c', Join::WITH, sprintf('%s.client = c', $alias))
            ->andWhere('c.covenanteeId = :covenanteeId')
            ->setParameter('covenanteeId', $value['value'])
        ;

        return true;
    }

    private function joinClientTable(QueryBuilder $queryBuilder, string $alias): void
    {
        if (in_array('c', $queryBuilder->getAllAliases(), true)) {
            return;
        }

        $queryBuilder
            ->leftJoin(sprintf('%s.client', $alias), 'c')
            ->leftJoin(ClientNatural::class, 'cn', Join::WITH, 'c.id = cn.id')
            ->leftJoin(ClientLegal::class, 'cl', Join::WITH, 'c.id = cl.id')
        ;
    }

    public function getStatusChoices(): array
    {
        return array_combine(
            array_map(
                fn(string $status) => ucwords(str_replace('_', ' ', $status)),
                ClientSanctionsScreening::STATUSES
            ),
            ClientSanctionsScreening::STATUSES
        );
    }
}
