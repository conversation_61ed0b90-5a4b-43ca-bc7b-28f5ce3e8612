<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Admin;

use Doctrine\ORM\Query\Expr\Join;
use Evp\Bundle\AdminBundle\Admin\BaseAdmin;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Form\ExportBlackListResultsType;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\ClientBundle\Entity\Client;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Datagrid\ProxyQuery;
use Sonata\DoctrineORMAdminBundle\Filter\BooleanFilter;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\FormFactory;

class ClientCheckLogsAdmin extends BaseAdmin
{
    private const PAGER_TYPE_SIMPLE = 'simple';

    public $form;
    private FormFactory $formFactory;

    public function configure(): void
    {
        parent::configure();

        $this->datagridValues['_sort_by'] = 'id';
        $this->datagridValues['_sort_order'] = 'DESC';
    }

    public function getPagerType(): string
    {
        return self::PAGER_TYPE_SIMPLE;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('id', 'text')
            ->add('blacklist', 'string', [
                'label' => 'Blacklist',
            ])
            ->add('client', 'string', [
                'label' => 'Client',
                'template' => 'EvpBlacklistBundle:Admin\ClientCheckLogs:list_field_client.html.twig',
            ])
            ->add('blacklisted', 'boolean', [
                'label' => 'Is blacklisted',
            ])
            ->add('checkedAt', 'datetime', [
                'label' => 'Checked at',
            ])
            ->add('_actions', 'actions', [
                'actions' => [
                    'show' => [],
                ],
            ])
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('blacklist', 'doctrine_orm_choice', [
                'field_type' => EntityType::class,
                'field_options' => [
                    'class' => Blacklist::class,
                    'query_builder' => function (BlacklistRepository $blacklistRepository) {
                        return $blacklistRepository->getNotDeprecatedPayseraSanctionListQueryBuilder();
                    },
                ],
            ])
            ->add('covenanteeId', 'doctrine_orm_callback', [
                'label' => 'Client ID',
                'callback' => [$this, 'covenanteeIdCallback'],
            ])
            ->add(
                'checkedAt', 'doctrine_orm_datetime_range', [
                    'field_type' => 'sonata_type_datetime_range_picker',
                    'label' => 'Checked at',
                ]
            )
            ->add('blacklisted', BooleanFilter::class, [
                'label' => 'Is blacklisted',
            ])
        ;

        parent::configureDatagridFilters($filter);
    }

    protected function configureDefaultFilterValues(array &$filterValues)
    {
    }

    public function configureActionButtons($action, $object = null): array
    {
        $list = parent::configureActionButtons($action, $object);

        $form = $this->formFactory->create(ExportBlackListResultsType::class);
        $this->form = $form->createView();

        $list['export_results']['template'] = 'EvpBlacklistBundle:Admin/ClientSanctionScreening:export_blacklist_check_results.html.twig';

        if ($action === 'show') {
            unset($list['export_results']);
        }

        return $list;
    }

    protected function configureRoutes(RouteCollection $collection): void
    {
        $collection->clearExcept(['list', 'show']);

        parent::configureRoutes($collection);
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('id')
            ->add('client', 'string', [
                'label' => 'Client',
                'template' => 'EvpBlacklistBundle:Admin\ClientCheckLogs:show__field_client.html.twig',
            ])
            ->add('blacklist', 'string', [
                'label' => 'Blacklist',
            ])
            ->add('items', null, [
                'label' => 'Matched profiles',
                'template' => 'EvpBlacklistBundle:Admin\ClientCheckLogs:list_field_matched_profiles.html.twig',
            ])
            ->add('clientScreeningSessions', null, [
                'label' => 'Client Screening Session',
                'template' => 'EvpBlacklistBundle:Admin\ClientCheckLogs:list_field_client_sanction_screening.html.twig',
            ])
            ->add('blacklisted', 'boolean', [
                'label' => 'Is blacklisted',
            ])
            ->add('checkedAt', 'datetime', [
                'label' => 'Checked at',
            ])
        ;
    }

    public function setFormFactory(FormFactory $formFactory): void
    {
        $this->formFactory = $formFactory;
    }

    public function covenanteeIdCallback(ProxyQuery $query, string $alias, string $field, array $value): bool
    {
        if (empty($value['value'])) {
            return false;
        }

        $query->getQueryBuilder()
            ->leftJoin(Client::class, 'c', Join::WITH, sprintf('%s.client = c', $alias))
            ->andWhere('c.covenanteeId = :covenanteeId')
            ->setParameter('covenanteeId', $value['value'])
        ;

        return true;
    }
}
