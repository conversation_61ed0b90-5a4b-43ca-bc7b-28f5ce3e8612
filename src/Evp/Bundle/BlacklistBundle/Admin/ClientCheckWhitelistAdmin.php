<?php

namespace Evp\Bundle\BlacklistBundle\Admin;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\AdminBundle\Admin\BaseAdmin;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\CategoryGroup;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelist;
use Evp\Bundle\BlacklistBundle\Entity\ClientCheckWhitelistChangeLog;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\ReviewActionWorkflow\ReviewActionTransitions;
use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\Workflows;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningWhitelistProfilesEvent;
use Evp\Bundle\BlacklistBundle\Events;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientBlacklistCheckResultItemRepository;
use Evp\Bundle\BlacklistBundle\Repository\ClientSanctionsScreeningRepository;
use Evp\Bundle\BlacklistBundle\Repository\ProfileRepository;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistChangeLogManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Exception;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Sonata\Form\Validator\ErrorElement;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Workflow\Registry;

class ClientCheckWhitelistAdmin extends BaseAdmin
{
    private ProfileRepository $profileRepository;
    private ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository;
    private ClientSanctionsScreeningRepository $clientSanctionsScreeningRepository;
    private EventDispatcherInterface $eventDispatcher;
    private Registry $workflowRegistry;
    private EntityManagerInterface $entityManager;
    private ClientCheckWhitelistChangeLogManager $clientCheckWhitelistChangeLogManager;

    public function setDependencies(
        ProfileRepository $profileRepository,
        ClientBlacklistCheckResultItemRepository $clientBlacklistCheckResultItemRepository,
        ClientSanctionsScreeningRepository $clientSanctionsScreeningRepository,
        EventDispatcherInterface $eventDispatcher,
        Registry $workflowRegistry,
        EntityManagerInterface $entityManager,
        ClientCheckWhitelistChangeLogManager $clientCheckWhitelistChangeLogManager
    ) {
        $this->profileRepository = $profileRepository;
        $this->clientBlacklistCheckResultItemRepository = $clientBlacklistCheckResultItemRepository;
        $this->clientSanctionsScreeningRepository = $clientSanctionsScreeningRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->workflowRegistry = $workflowRegistry;
        $this->entityManager = $entityManager;
        $this->clientCheckWhitelistChangeLogManager = $clientCheckWhitelistChangeLogManager;
    }

    public function postPersist($object)
    {
        $this->entityManager->getConnection()->beginTransaction();

        try {
            $this->deactivateRelatedEntities($object);
            if ($object instanceof ClientCheckWhitelist) {
                $this->clientCheckWhitelistChangeLogManager->log(
                    $object,
                    ClientCheckWhitelistChangeLog::ACTION_CREATE
                );
            }
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Exception $exception) {
            $this->entityManager->getConnection()->rollBack();
        }
    }

    public function preUpdate($object)
    {
        if (!$object instanceof ClientCheckWhitelist) {
            return;
        }
        $this->entityManager->getConnection()->beginTransaction();

        try {
            $this->clientCheckWhitelistChangeLogManager->log(
                $object,
                ClientCheckWhitelistChangeLog::ACTION_MODIFY
            );
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Exception $exception) {
            $this->entityManager->getConnection()->rollBack();
        }
    }

    public function postUpdate($object)
    {
        $this->entityManager->getConnection()->beginTransaction();

        try {
            $this->deactivateRelatedEntities($object);
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Exception $exception) {
            $this->entityManager->getConnection()->rollBack();
        }
    }

    public function preRemove($object)
    {
        if (!$object instanceof ClientCheckWhitelist) {
            return;
        }
        $this->entityManager->getConnection()->beginTransaction();

        try {
            $this->clientCheckWhitelistChangeLogManager->log(
                $object,
                ClientCheckWhitelistChangeLog::ACTION_DELETE
            );
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Exception $exception) {
            $this->entityManager->getConnection()->rollBack();
        }
    }

    protected function configureRoutes(RouteCollection $collection)
    {
    }

    protected function configureFormFields(FormMapper $form)
    {
        $form
            ->add('client', ModelAutocompleteType::class, [
                'property' => 'name',
                'to_string_callback' => function (Client $client) {
                    return $this->getFormattedClient($client);
                },
                'safe_label' => true
            ])
            ->add('blacklist', EntityType::class, [
                'class' => Blacklist::class,
                'choice_label' => 'name',
                'query_builder' => function (BlacklistRepository $repository) {
                    return $repository->getNotDeprecatedPayseraSanctionListQueryBuilder();
                },
            ])
            ->add('profileExternalId', TextType::class)
            ->add('categoryGroup', EntityType::class, [
                'class' => CategoryGroup::class,
                'choice_label' => 'name',
            ]);
    }

    protected function configureListFields(ListMapper $list)
    {
        $list
            ->addIdentifier('id', 'text')
            ->add('client', 'string', [
                'template' => 'EvpBlacklistBundle:Admin/Client/Field:list_client_name_with_covenantee.html.twig',
            ])
            ->add('blacklist')
            ->add('profileExternalId')
            ->add('categoryGroup', null, ['associated_property' => 'name']);
    }

    protected function configureDatagridFilters(DatagridMapper $filter)
    {
        $filter
            ->add('client', ModelAutocompleteFilter::class, [], null, [
                'property' => 'name',
                'to_string_callback' => function (Client $client) {
                    return $this->getFormattedClient($client);
                },
            ])
            ->add('blacklist', 'doctrine_orm_choice', [
                'field_type' => EntityType::class,
                'field_options' => [
                    'class' => Blacklist::class,
                    'query_builder' => function (BlacklistRepository $blacklistRepository) {
                        return $blacklistRepository->getNotDeprecatedPayseraSanctionListQueryBuilder();
                    },
                ],
            ])
            ->add('profileExternalId')
            ->add('categoryGroup', null, [], EntityType::class, [
                'class' => CategoryGroup::class,
                'choice_label' => 'name',
            ]);

        parent::configureDatagridFilters($filter);
    }

    private function getFormattedClient(Client $entity)
    {
        return sprintf(
            '<strong>%d</strong>: %s',
            $entity->getCovenanteeId(),
            $entity->__toString()
        );
    }

    public function validate(ErrorElement $errorElement, $object): void
    {
        if ($object->getClient() === null) {
            $errorElement->with('client')
                ->addViolation('Please select a client.');
        }

        if ($object->getBlacklist() === null) {
            $errorElement->with('blacklist')
                ->addViolation('Please select a blacklist.');
        }

        if ($object->getProfileExternalId() === null) {
            $errorElement->with('profileExternalId')
                ->addViolation('Profile external id cannot be empty.');
        }

        if ($object->getCategoryGroup() === null) {
            $errorElement->with('categoryGroup')
                ->addViolation('Please select a category group.');
        }

        $errorElement->end();
    }

    private function deactivateRelatedEntities(ClientCheckWhitelist $clientCheckWhitelist): void
    {
        $profiles = $this->profileRepository->findByClientAndBlacklistKeyAndExternalIdAndVersion(
            $clientCheckWhitelist->getClient(),
            $clientCheckWhitelist->getBlacklist()->getKey(),
            $clientCheckWhitelist->getProfileExternalId()
        );

        $sanctionScreenings = $this->clientSanctionsScreeningRepository->getByClient(
            $clientCheckWhitelist->getClient()
        );

        foreach ($sanctionScreenings as $sanctionsScreening) {
            $this->eventDispatcher->dispatch(
                Events::SANCTION_SCREENING_WHITELIST_PROFILES,
                new ClientSanctionsScreeningWhitelistProfilesEvent(
                    $sanctionsScreening,
                    array_map(fn ($profile) => $profile->getId(), $profiles)
                )
            );

            $itemsToReview = $this->clientBlacklistCheckResultItemRepository
                ->findNotReviewedByBlacklistCheckResultId(
                    $sanctionsScreening->getClientBlacklistCheckResult()->getId()
                )
            ;

            if (count($itemsToReview) === 0) {
                $workflow = $this->workflowRegistry->get(
                    $sanctionsScreening,
                    Workflows::REVIEW_ACTION_WORKFLOW_NAME
                );

                if ($workflow->can($sanctionsScreening, ReviewActionTransitions::TO_COMPLETED)) {
                    $workflow->apply($sanctionsScreening, ReviewActionTransitions::TO_COMPLETED);
                }
            }
        }
    }
}
