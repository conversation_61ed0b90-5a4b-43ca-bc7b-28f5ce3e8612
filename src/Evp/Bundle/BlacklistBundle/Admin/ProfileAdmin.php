<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Admin;

use Evp\Bundle\AdminBundle\Admin\BaseAdmin;
use Evp\Bundle\BlacklistBundle\Entity\Blacklist;
use Evp\Bundle\BlacklistBundle\Entity\Category;
use Evp\Bundle\BlacklistBundle\Entity\Profile;
use Evp\Bundle\BlacklistBundle\Form\ProfileAlternativeBirthdayType;
use Evp\Bundle\BlacklistBundle\Form\ProfileAlternativeNameType;
use Evp\Bundle\BlacklistBundle\Repository\BlacklistRepository;
use Evp\Bundle\BlacklistBundle\Service\ProfileIdentificationGenerator;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ChoiceFieldMaskType;
use Sonata\AdminBundle\Form\Type\CollectionType;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\Form\Validator\ErrorElement;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Validator\Constraints\Date;

class ProfileAdmin extends BaseAdmin
{
    private const PAGER_TYPE_SIMPLE = 'simple';

    private ProfileIdentificationGenerator $profileIdentificationGenerator;

    public function __construct(
        string $code,
        string $class,
        string $baseControllerName,
        ProfileIdentificationGenerator $profileIdentificationGenerator
    ) {
        parent::__construct($code, $class, $baseControllerName);

        $this->profileIdentificationGenerator = $profileIdentificationGenerator;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('blacklist', null, [
                'required' => true,
                'class' => Blacklist::class,
                'query_builder' => function (BlacklistRepository $repository) {
                    return $repository->getStaticPayseraSanctionListQueryBuilder();
                },
            ])
            ->add('type', ChoiceFieldMaskType::class, [
                'choices' => [
                    ucfirst(Profile::TYPE_PERSON) => Profile::TYPE_PERSON,
                    ucfirst(Profile::TYPE_COMPANY) => Profile::TYPE_COMPANY,
                    ucfirst(Profile::TYPE_BANK) => Profile::TYPE_BANK,
                ],
                'required' => true,
                'map' => [
                    Profile::TYPE_PERSON => ['name', 'firstName', 'middleName', 'lastName'],
                    Profile::TYPE_COMPANY => ['name'],
                    Profile::TYPE_BANK => ['name'],
                ],
            ])
            ->add('name', TextType::class, [
                'required' => false,
            ])
            ->add('firstName', TextType::class, [
                'required' => false,
            ])
            ->add('middleName', TextType::class, [
                'required' => false,
            ])
            ->add('lastName', TextType::class, [
                'required' => false,
            ])
            ->add('code', TextType::class, [
                'required' => false,
            ])
            ->add('country', TextType::class, [
                'required' => false,
            ])
            ->add('birthday', TextType::class, [
                'required' => false,
                'constraints' => [
                    new Date([
                        'message' => 'This field is invalid. Please enter a valid date in the format: YYYY-MM-DD',
                    ]),
                ],
            ])
            ->add('phone', TextType::class, [
                'required' => false,
            ])
            ->add('info', TextType::class, [
                'required' => false,
            ])
            ->add('externalId', TextType::class, [
                'data' => $this->getExternalIdData($this->getSubject()),
                'attr' => [
                    'readonly' => true, // Can't use disabled because it will not be sent to the server
                ],
            ])
            ->add('categories', EntityType::class, [
                'class' => Category::class,
                'multiple' => true,
                'required' => true,
            ])
            ->add('alternativeNames', CollectionType::class, [
                'entry_type' => ProfileAlternativeNameType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
            ])
            ->add('alternativeBirthdays', CollectionType::class, [
                'entry_type' => ProfileAlternativeBirthdayType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('id', 'text', [
                'route' => ['name' => 'show'],
            ])
            ->add('blacklist', 'many_to_one')
            ->add('categories', 'many_to_many')
            ->add('type')
            ->add('name')
            ->add('firstName')
            ->add('middleName')
            ->add('lastName')
            ->add('code', 'text')
            ->add('country')
            ->add('birthday')
            ->add('phone', 'text')
            ->add('info')
            ->add('externalId', 'text')
            ->add('_action', null, [
                'actions' => [
                    'show' => [],
                    'edit' => [
                        'template' => 'EvpBlacklistBundle:Admin\Profile:list__action_edit.html.twig',
                    ],
                    'delete' => [
                        'template' => 'EvpBlacklistBundle:Admin\Profile:list__action_delete.html.twig',
                    ],
                ],
            ])
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('blacklist', 'doctrine_orm_choice', [
                    'field_type' => EntityType::class,
                    'field_options' => [
                        'class' => Blacklist::class,
                        'query_builder' => function (BlacklistRepository $blacklistRepository) {
                            return $blacklistRepository->getQueryBuilder();
                        },
                    ],
                ]
            )
            ->add('type', 'doctrine_orm_choice', [
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        ucfirst(Profile::TYPE_PERSON) => Profile::TYPE_PERSON,
                        ucfirst(Profile::TYPE_COMPANY) => Profile::TYPE_COMPANY,
                        ucfirst(Profile::TYPE_BANK) => Profile::TYPE_BANK,
                    ],
                ],
            ])
            ->add('name', 'doctrine_orm_string')
            ->add('firstName', 'doctrine_orm_string')
            ->add('middleName', 'doctrine_orm_string')
            ->add('lastName', 'doctrine_orm_string')
            ->add('code', 'doctrine_orm_string')
            ->add('country', 'doctrine_orm_string')
            ->add('birthday', 'doctrine_orm_string')
            ->add('phone', 'doctrine_orm_string')
            ->add('info', 'doctrine_orm_string')
            ->add('externalId', 'doctrine_orm_string')
        ;

        parent::configureDatagridFilters($filter);
    }

    /**
     * @param string $action
     * @param Profile|null $object
     */
    public function configureActionButtons($action, $object = null): array
    {
        $list = parent::configureActionButtons($action, $object);

        if ($this->hasAccess('create')) {
            $list['import']['template'] = 'EvpBlacklistBundle:Admin\Profile:import_button.html.twig';
        }

        if (
            $action === 'show'
            && ($object->getBlacklist()->isExternal() || $object->getBlacklist()->isDeprecated())
        ) {
            unset($list['edit']);
        }

        return $list;
    }

    public function configureRoutes(RouteCollection $collection): void
    {
        $collection->add('import', null, [], [], [], '', [], ['GET', 'POST']);
    }

    public function getBatchActions(): array
    {
        $actions = parent::getBatchActions();

        if ($this->hasRoute('delete') && $this->hasAccess('delete')) {
            $actions['delete'] = [
                'label' => 'Delete',
                'ask_confirmation' => true,
            ];
        }

        return $actions;
    }

    public function validate(ErrorElement $errorElement, $object): void
    {
        if ($object->getCategories()->isEmpty()) {
            $this->addViolation($errorElement, 'categories', 'Please select at least one category.');
        }

        if ($object->getName() === null
            && $object->getFirstName() == null
            && $object->getMiddleName() == null
            && $object->getLastName() == null
        ) {
            $this->addViolation(
                $errorElement,
                'name',
                'Please enter the name or the first/middle/last names'
            );
            $this->addViolation(
                $errorElement,
                'firstName',
                'Please enter the name or the first/middle/last names'
            );
            $this->addViolation(
                $errorElement,
                'middleName',
                'Please enter the name or the first/middle/last names'
            );
            $this->addViolation(
                $errorElement,
                'lastName',
                'Please enter the name or the first/middle/last names'
            );
        }
    }

    public function getPagerType(): string
    {
        return self::PAGER_TYPE_SIMPLE;
    }

    private function addViolation(ErrorElement $errorElement, string $field, string $message): void
    {
        $errorElement
            ->with($field)
            ->addViolation($message)
            ->end()
        ;
    }

    private function getExternalIdData(?Profile $profile): string
    {
        if ($profile !== null && $profile->getId() !== null) {
            return $profile->getExternalId();
        }

        return $this->profileIdentificationGenerator->generateExternalId();
    }
}
