<?php

declare(strict_types=1);

namespace Evp\Bundle\BlacklistBundle\Listener;

use Evp\Bundle\BlacklistBundle\Enum\ClientSanctionsScreening\ItemReviewAction;
use Evp\Bundle\BlacklistBundle\Event\ClientSanctionsScreeningWhitelistProfilesEvent;
use Evp\Bundle\BlacklistBundle\Repository\CategoryGroupRepository;
use Evp\Bundle\BlacklistBundle\Service\ClientBlacklistCheckResultItemReviewLogManager;
use Evp\Bundle\BlacklistBundle\Service\ClientCheckWhitelistManager;
use Psr\Log\LoggerInterface;

class ClientCheckWhitelistListener
{
    private ClientCheckWhitelistManager $manager;
    private CategoryGroupRepository $categoryGroupRepository;
    private LoggerInterface $logger;
    private ClientBlacklistCheckResultItemReviewLogManager $clientBlacklistCheckResultItemReviewLogManager;

    public function __construct(
        ClientCheckWhitelistManager $manager,
        CategoryGroupRepository $categoryGroupRepository,
        LoggerInterface $logger,
        ClientBlacklistCheckResultItemReviewLogManager $clientBlacklistCheckResultItemReviewLogManager
    ) {
        $this->manager = $manager;
        $this->categoryGroupRepository = $categoryGroupRepository;
        $this->logger = $logger;
        $this->clientBlacklistCheckResultItemReviewLogManager = $clientBlacklistCheckResultItemReviewLogManager;
    }

    public function onReviewActionWhitelisted(ClientSanctionsScreeningWhitelistProfilesEvent $event): void
    {
        $sanctionsScreening = $event->getClientSanctionsScreening();
        $profilesToWhitelist = $event->getProfilesToWhiteList();
        $clientBlacklistCheckResult = $sanctionsScreening->getClientBlacklistCheckResult();
        $blacklistCheckResultItems = $clientBlacklistCheckResult->getItems();

        foreach ($blacklistCheckResultItems as $item) {
            if (!in_array($item->getProfile()->getId(), $profilesToWhitelist, true)) {
                continue;
            }

            $categoryGroupIds = $item->getProfile()->getCategoryGroupIds();

            if (empty($categoryGroupIds)) {
                $this->logger->warning(
                    'Corresponding category group not found for blacklist result item',
                    [
                        $sanctionsScreening->getId(),
                        $clientBlacklistCheckResult->getId(),
                        $item->getId(),
                    ]
                );

                continue;
            }

            $categoryGroups = $this->categoryGroupRepository->findBy(['id' => $categoryGroupIds]);

            foreach ($categoryGroups as $categoryGroup) {
                $this->manager->createIfNotExists($sanctionsScreening->getClient(), $item->getProfile(), $categoryGroup);
            }

            $this->clientBlacklistCheckResultItemReviewLogManager->create(
                $item,
                ItemReviewAction::FALSE_MATCH_ACTION
            );
        }
    }
}