<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientRestBundle\Service;

use Evp\Bundle\ClientBundle\Service\RequestAwareUserInformationProvider;
use Evp\Bundle\ClientRestBundle\Entity\Filter\ClientFilter;
use Evp\Bundle\ClientRestBundle\Exception\ClientManagerException;
use Evp\Bundle\ClientBundle\Service\DefaultClientActivator;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\ClientBundle\Entity\ClientRequest;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\Client;
use Doctrine\ORM\EntityManager;
use Paysera\Bundle\ApiBundle\Entity\PagedQuery;
use Paysera\Pagination\Entity\Pager;

class ClientManager
{
    private ClientRepository $clientRepository;
    private EntityManager $entityManager;
    private DefaultClientActivator $defaultClientActivator;
    private RequestAwareUserInformationProvider $requestAwareUserInformationProvider;

    public function __construct(
        ClientRepository $clientRepository,
        EntityManager $entityManager,
        DefaultClientActivator $defaultClientActivator,
        RequestAwareUserInformationProvider $requestAwareUserInformationProvider
    ) {
        $this->clientRepository = $clientRepository;
        $this->entityManager = $entityManager;
        $this->defaultClientActivator = $defaultClientActivator;
        $this->requestAwareUserInformationProvider = $requestAwareUserInformationProvider;
    }

    /**
     * @throws ClientManagerException
     */
    public function createClient(ClientRequest $clientRequest): Client
    {
        $client = $clientRequest->getClient();

        if (!$client instanceof ClientLegal && !$client instanceof ClientNatural) {
            throw new ClientManagerException('Invalid client type', ClientManagerException::ERROR_INVALID_TYPE);
        }

        $existingClient = $this->clientRepository->findOneByCovenanteeId($client->getCovenanteeId());

        if ($existingClient !== null) {
            throw new ClientManagerException('Client already exist', ClientManagerException::ERROR_CLIENT_EXISTS);
        }

        $userInformation = $this->requestAwareUserInformationProvider->getUserInformation($clientRequest);

        $account = $this->defaultClientActivator->activateClient($client, $userInformation);
        $account->setActive($clientRequest->isAccountActive());

        $this->entityManager->persist($client);

        return $client;
    }

    public function getClients(ClientFilter $filter, Pager $pager): PagedQuery
    {
        $configuredQuery = $this->clientRepository->buildConfiguredQuery($filter, $pager->getOrderingPairs());

        return new PagedQuery($configuredQuery, $pager);
    }
}
