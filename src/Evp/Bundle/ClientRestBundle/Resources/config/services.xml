<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/*.xml"/>
    </imports>
    <services>
        <service id="evp_client_rest.client_restriction_checker"
                 class="Evp\Bundle\ClientRestBundle\Service\ClientRestrictionChecker">
            <argument type="service" id="security.token_storage"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="logger"/>
        </service>
        <service id="evp_client_rest.client_provider"
                 class="Evp\Bundle\ClientRestBundle\Service\ClientProvider">
            <tag name="monolog.logger" channel="evp_client_rest.client_provider"/>

            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="evp_client_rest.client_restriction_checker"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_client_rest.client_application_provider"
                 class="Evp\Bundle\ClientRestBundle\Service\ClientApplicationProvider">
            <tag name="monolog.logger" channel="evp_client_rest.client_application_provider"/>

            <argument type="service" id="evp_client.repository.client_application"/>
        </service>

        <service id="evp_client_rest.client_from_scopes_provider"
                 class="Evp\Bundle\ClientRestBundle\Service\CurrentClientFromScopesProvider">
            <argument type="service" id="paysera_security.service.properties_extractor"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="security.token_storage"/>
        </service>

        <service class="Evp\Bundle\ClientRestBundle\Service\ContextClientProvider"
                 id="evp_client_rest.context_client_provider">
            <argument type="service" id="paysera_security.service.properties_extractor"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="security.token_storage"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="evp_bank_transfer_rest.manage_transfers_checker"/>
        </service>

        <service class="Evp\Bundle\ClientRestBundle\Service\FallbackClientProvider"
                 id="evp_client_rest.fallback_client_provider">
            <argument type="service" id="evp_client_rest.context_client_provider" />
            <argument type="service" id="evp_client_rest.client_from_scopes_provider" />
        </service>

        <service class="Evp\Bundle\ClientRestBundle\Service\ClientManager"
                 id="evp_client_rest.service.client_manager">
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_client.default_client_activator"/>
            <argument type="service" id="Evp\Bundle\ClientBundle\Service\RequestAwareUserInformationProvider"/>
        </service>

        <service id="evp_client_rest.result_provider.client_restriction" parent="paysera_rest.result_provider">
            <argument type="service" id="evp_client.repository.client_restriction"/>
        </service>

        <service id="Evp\Bundle\ClientRestBundle\Service\ClientMergeManager">
            <argument type="service" id="evp_client_notification.notification_manager"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_client.default_client_activator"/>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_questionnaire_service.questionnaire"/>
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_card.repository.card_account"/>
            <argument type="service" id="paysera_unidentified_client_hold.unidentified_client_hold_manager"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
