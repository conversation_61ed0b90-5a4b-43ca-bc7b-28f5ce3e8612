<?php

declare(strict_types=1);

namespace Evp\Bundle\BankPermissionBundle\Service;

use Doctrine\Persistence\ObjectManager;
use Evp\Bundle\BankPermissionBundle\Entity\Permission;
use Evp\Bundle\BankPermissionBundle\Event\PermissionEvent;
use Evp\Bundle\BankPermissionBundle\PermissionEvents;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class PermissionEntityManager
{
    private ObjectManager $objectManager;
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(ObjectManager $objectManager, EventDispatcherInterface $eventDispatcher)
    {
        $this->objectManager = $objectManager;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function persistPermission(Permission $permission): void
    {
        $this->objectManager->persist($permission);
        $this->eventDispatcher->dispatch(PermissionEvents::PERMISSION_PERSISTED, new PermissionEvent($permission));
    }
}
