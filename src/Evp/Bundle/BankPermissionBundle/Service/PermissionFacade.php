<?php

namespace Evp\Bundle\BankPermissionBundle\Service;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\AccountRepository;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission;
use Doctrine\ORM\NoResultException;
use Evp\Bundle\BankPermissionBundle\Entity\Permission;
use Evp\Bundle\BankPermissionBundle\Exception\FactoringAccountPermissionException;
use Evp\Bundle\BankPermissionBundle\Exception\PermissionManagerException;
use Evp\Bundle\BankPermissionBundle\Repository\PermissionRepository;
use Evp\Bundle\BankPermissionBundle\Repository\SignPermissionRepository;
use Evp\Bundle\ClientBundle\Entity\Client;
use Psr\Log\LoggerInterface;
use Doctrine\ORM\EntityManager;
use Throwable;

class PermissionFacade
{
    private $entityManager;
    private $permissionManager;
    private $accountRepository;
    private $permissionRepository;
    private $permissionEntityManager;
    private $signPermissionRepository;
    private $levelLimitsProvider;
    private $levelLimitsHelper;
    private $logger;

    public function __construct(
        EntityManager $entityManager,
        PermissionManagerInterface $permissionManager,
        AccountRepository $accountRepository,
        PermissionRepository $permissionRepository,
        PermissionEntityManager $permissionEntityManager,
        SignPermissionRepository $signPermissionRepository,
        LevelLimitsProvider $levelLimitsProvider,
        LevelLimitsHelper $levelLimitsHelper,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->permissionManager = $permissionManager;
        $this->accountRepository = $accountRepository;
        $this->permissionRepository = $permissionRepository;
        $this->permissionEntityManager = $permissionEntityManager;
        $this->signPermissionRepository = $signPermissionRepository;
        $this->levelLimitsProvider = $levelLimitsProvider;
        $this->levelLimitsHelper = $levelLimitsHelper;
        $this->logger = $logger;
    }

    /**
     * Add new limits to permission
     *
     * @param int            $clientId
     * @param SignPermission $permission
     * @param string         $startAt
     *
     * @return SignPermission
     *
     * @throws PermissionManagerException
     */
    public function addNewLimits($clientId, SignPermission $permission, $startAt = null)
    {
        $client = $this->findClientAndValidate($clientId);

        if (!$startAt) {
            $startAt = new \DateTime();
        } else {
            $startAt = new \DateTime($startAt);
        }

        $oldPermission = $this->permissionManager->getPermissionByReplacedId($permission->getId());
        if (!$oldPermission) {
            $oldPermission = $this->permissionManager->getPermissionById($permission->getId());
        }

        if (!$this->permissionManager->hasAdministratePermission($client, $oldPermission->getAccount())) {
            throw new PermissionManagerException('This client has no administrate permission');
        }

        if ($oldPermission->getValidFrom() > new \DateTime()) {
            $this->updatePermissionLimits($oldPermission, $permission);

            $oldPermission->setValidFrom($startAt);
            $oldPermission->getReplacedPermission()->setValidTo($startAt);

            $this->entityManager->flush();
            return $oldPermission;

        } else {
            $newPermission = $oldPermission->getClone();
            $this->updatePermissionLimits($newPermission, $permission);

            $this->permissionManager->addPermission($newPermission, $startAt, $oldPermission);
            $this->entityManager->flush();
            return $newPermission;
        }

    }

    /**
     * Adds new permission
     *
     * @param Permission $permission
     * @param string     $accountNumber
     * @param int[]      $clientIdList
     * @param int        $clientId
     *
     * @return Permission
     *
     * @throws PermissionManagerException
     * @throws FactoringAccountPermissionException
     */
    public function addAccountPermission(Permission $permission, $accountNumber, $clientIdList, $clientId = null)
    {
        $account = $this->findAccountByNumber($accountNumber);
        if ($clientId !== null) {
            $client = $this->findClientAndValidate($clientId);
            if (!$this->permissionManager->hasAdministratePermission($client, $account)) {
                throw new PermissionManagerException('This client has no administrate permission');
            }
        }
        if ($account->isFactoringAccount()) {
            throw new FactoringAccountPermissionException();
        }

        $permission->setAccount($account);
        foreach ($clientIdList as $clientId) {
            $permission->addClient($this->findClientAndValidate($clientId));
        }

        $this->permissionManager->addPermission($permission);
        $this->entityManager->flush();
        return $permission;
    }

    public function addMissingPermissions(int $clientId, string $accountNumber, DateTime $until = null): bool
    {
        try {
            $account = $this->findAccountByNumber($accountNumber);
            $client = $this->findClientAndValidate($clientId);
            if (!$this->permissionManager->hasAdministratePermission($client, $account)) {
                throw new PermissionManagerException('This client has no administrate permission');
            }

            foreach ($this->getClientMissedPermissions($account, $client) as $permission) {
                $permission->setAccount($account);
                $permission->addClient($client);

                if ($permission instanceof SignPermission) {
                    $levelLimits = $this->levelLimitsProvider->getStandardLimitsByClient($client);
                    $permission->setDayLimit($levelLimits->getDayLimit());
                    $permission->setMonthLimit($levelLimits->getMonthLimit());
                    $permission->setYearLimit($levelLimits->getYearLimit());
                    $permission->setLevel(SignPermission::LEVEL_A);
                }
                if ($until !== null) {
                    $permission->setValidTo($until);
                }

                $this->permissionManager->addPermission($permission);
            }

            $this->entityManager->flush();
        } catch (Throwable $genericException) {
            $this->logger->info('Adding missed permission failed', ['message' => $genericException->getMessage()]);

            return false;
        }

        return true;
    }

    /**
     * Removes permission
     *
     * @param int $permissionId
     * @param int $clientId
     *
     * @return bool
     *
     * @throws PermissionManagerException
     */
    public function removeAccountPermission($permissionId, $clientId = null)
    {
        $permission = $this->permissionManager->getPermissionById($permissionId);
        if ($permission === null) {
            throw new PermissionManagerException('Permission not found by id');
        }

        if ($clientId !== null) {
            $client = $this->findClientAndValidate($clientId);
            if (!$this->permissionManager->hasAdministratePermission($client, $permission->getAccount())) {
                throw new PermissionManagerException('This client has no administrate permission');
            }
        }

        $this->permissionManager->invalidatePermission($permission);
        $this->entityManager->flush();

        return true;
    }

    public function addSignForAutoPermission($accountNumber, $clientId)
    {
        $account = $this->findAccountByNumber($accountNumber);
        $client = $this->findClientAndValidate($clientId);

        $permissions = $this->permissionRepository->findActiveByClientAccountAndType(
            $client,
            $account,
            'Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission'
        );
        if (count($permissions) === 0) {
            $signPermission = new SignPermission();
            $signPermission->setValidFrom(new \DateTime());
            $signPermission->setForAutomaticTransfers(true);
            $signPermission->setLevel(SignPermission::LEVEL_A);
            $signPermission->addClient($client);
            $signPermission->setAccount($account);

            $levelLimits = $this->levelLimitsProvider->getStandardLimitsByClient($account->getClient());
            $this->levelLimitsHelper->applyLevelLimitsForSignPermission($levelLimits, $signPermission);

            try {
                $signPermission->setReplacedPermission(
                    $this->signPermissionRepository->findLastForAutoTransfers($client, $account)
                );
            } catch (NoResultException $e) {
                // skip - no replaced permission
            }

            $this->permissionEntityManager->persistPermission($signPermission);
            $this->entityManager->flush();

            return $signPermission;
        } else {
            return $permissions[0];
        }
    }

    public function removeSignForAutoPermission($accountNumber, $clientId)
    {
        $account = $this->findAccountByNumber($accountNumber);
        $client = $this->findClientAndValidate($clientId);

        $permissions = $this->permissionRepository->findActiveByClientAccountAndType(
            $client,
            $account,
            'Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission'
        );
        foreach ($permissions as $permission) {
            if ($permission instanceof SignPermission && $permission->isForAutomaticTransfers()) {
                $this->permissionManager->invalidatePermission($permission);
            }
        }
        $this->entityManager->flush();

        return true;
    }

    /**
     * Get account permissions
     *
     * @param int    $clientId
     * @param int    $accountId
     * @param string $type
     * @param bool   $future
     * @param bool   $needsReadPermission
     *
     * @return \Evp\Bundle\BankPermissionBundle\Entity\Permission[]
     *
     * @throws PermissionManagerException
     */
    public function getAccountPermissions($clientId, $accountId, $type = null, $future = false, $needsReadPermission = true)
    {
        if ($type === '') {
            $type = null;
        }

        $client = $this->entityManager->find('EvpClientBundle:Client', $clientId);
        if (!$client) {
            throw new PermissionManagerException('Client with specified ID not found');
        }

        $account = $this->entityManager->find('EvpBankAccountBundle:Account', $accountId);
        if (!$account) {
            throw new PermissionManagerException('Account with specified ID not found');
        }
        if ($needsReadPermission && !$this->permissionManager->hasReadPermission($client, $account)) {
            throw new PermissionManagerException('Client has no permission to read this account', null, null, 'insufficient_permissions');
        }

        if ($future) {
            return $this->permissionRepository->findFutureByClientAccountAndType($client, $account, $type);
        } else {
            return $this->permissionRepository->findActiveByClientAccountAndType($client, $account, $type);
        }
    }

    /**
     * Update permissions
     *
     * @param \Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission $to
     * @param \Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission $from
     *
     * @return \Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission
     */
    protected function updatePermissionLimits(SignPermission $to, SignPermission $from)
    {
        if ($from->getDayLimit() && !$from->getDayLimit()->isZero()) {
            $to->setDayLimit($from->getDayLimit());
        } else {
            $to->setDayLimit(null);
        }
        if ($from->getMonthLimit() && !$from->getMonthLimit()->isZero()) {
            $to->setMonthLimit($from->getMonthLimit());
        } else {
            $to->setMonthLimit(null);
        }
        if ($from->getYearLimit() && !$from->getYearLimit()->isZero()) {
            $to->setYearLimit($from->getYearLimit());
        } else {
            $to->setYearLimit(null);
        }
        return $to;
    }

    /**
     * Finds client by ID. If not found, throws exception. Also throws if it has no agreement or if it's disabled
     *
     * @param int $clientId
     *
     * @return Client
     *
     * @throws \Evp\Bundle\BankApiBundle\Exception\BankApiException
     * @throws \Doctrine\ORM\NoResultException
     */
    protected function findClientAndValidate($clientId)
    {
        $client = $this->entityManager->getRepository('EvpClientBundle:Client')->find($clientId);
        if ($client === null) {
            throw new PermissionManagerException(sprintf(
                'Client with specified ID (%d) not found',
                $clientId
            ));
        }
        if ($client->getTransferServiceAgreement() && !$client->getTransferServiceAgreement()->isEnabled()) {
            throw new PermissionManagerException('Client has no enabled transfer service agreement');
        }
        return $client;
    }

    /**
     * @param Account $account
     * @param Client $client
     * @return Permission[]
     */
    private function getClientMissedPermissions(Account $account, Client $client): array
    {
        $missedPermissions = [];
        if (!$this->permissionManager->hasReadPermission($client, $account)) {
            $missedPermissions[] = new Permission\ReadPermission();
        }
        if (!$this->permissionManager->hasWritePermission($client, $account)) {
            $missedPermissions[] = new Permission\WritePermission();
        }
        if (!$this->permissionManager->hasSignPermission($client, $account)) {
            $missedPermissions[] = new SignPermission();
        }

        return $missedPermissions;
    }

    protected function findAccountByNumber(string $accountNumber): Account
    {
        /** @var Account|null $account */
        $account = $this->accountRepository->findOneByNumber($accountNumber);
        if ($account === null) {
            throw new PermissionManagerException('Account not found by number: ' . $accountNumber);
        }

        return $account;
    }
}
