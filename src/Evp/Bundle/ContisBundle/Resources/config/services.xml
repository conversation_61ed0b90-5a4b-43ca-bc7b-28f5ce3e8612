<?xml version="1.0" ?>

<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/api.xml"/>
        <import resource="services/repositories.xml"/>
        <import resource="services/normalizers.xml"/>
        <import resource="services/transformers.xml"/>
        <import resource="services/managers.xml"/>
        <import resource="services/resolvers.xml"/>
        <import resource="services/commands.xml"/>
        <import resource="services/admins.xml"/>
        <import resource="services/listeners.xml"/>
        <import resource="services/workers.xml"/>
        <import resource="services/clients.xml"/>
        <import resource="services/controllers.xml"/>
        <import resource="services/processors.xml"/>
    </imports>

    <parameters>
        <parameter key="evp_contis.currency">EUR</parameter>
        <parameter key="evp_contis.paysera_client_id">727218</parameter>

        <parameter key="evp_contis.contis_beneficiary_iban_gb">**********************</parameter>
        <parameter key="evp_contis.contis_beneficiary_iban_lt">********************</parameter>
        <parameter key="evp_contis.contis_beneficiary_accounts" type="collection">
            <parameter key="gb" type="collection">
                <parameter key="name">CONTIS FS VISA</parameter>
                <parameter key="iban">%evp_contis.contis_beneficiary_iban_gb%</parameter>
                <parameter key="bic">RBOSGB2L</parameter>
            </parameter>
            <parameter key="lt" type="collection">
                <parameter key="name">Paysera Ltd</parameter>
                <parameter key="iban">%evp_contis.contis_beneficiary_iban_lt%</parameter>
                <parameter key="bic">UFPOLT21XXX</parameter>
            </parameter>
        </parameter>
        <parameter key="evp_contis.contis_beneficiary_ibans" type="collection">
            <parameter key="gb">%evp_contis.contis_beneficiary_iban_gb%</parameter>
            <parameter key="lt">%evp_contis.contis_beneficiary_iban_lt%</parameter>
        </parameter>

        <parameter key="evp_contis.paysera_account_number_at_contis_old" type="string">3157116</parameter>

        <parameter key="evp_contis.paysera_account_number">EVP8710001771695</parameter>
        <parameter key="evp_contis.paysera_account_name">EVP International</parameter>
        <parameter key="evp_contis.card_balance_default_limit_amount">10000</parameter>
        <parameter key="evp_contis.default_limit_period">86400</parameter>
        <parameter key="evp_contis.card_prime_cost">3.5</parameter>
        <parameter key="evp_contis.card_cancel_commission_amount">3</parameter>
        <parameter key="evp_contis.wait_days_before_notifying_about_unpaid_monthly_charge">1</parameter>
        <parameter key="evp_contis.warn_about_cancellation_after_unpaid_months">2</parameter>
        <parameter key="evp_contis.cancel_card_after_unpaid_months">3</parameter>
        <parameter key="evp_contis.cancel_card_after_number_of_unpaid_charges">3</parameter>
        <parameter key="evp_contis.card_enable_commission_amount">0</parameter>
        <parameter key="evp_contis.account_count_limit">1</parameter>
        <parameter key="evp_contis.top_up_operation_name">Contis top up</parameter>
        <parameter key="evp_contis.cards_maturity_interval">%env(CONTIS_CARDS_MATURITY_INTERVAL)%</parameter>
        <parameter key="evp_contis.card_design_black">PAYSERBK</parameter>
        <parameter key="evp_contis.card_design_white">PAYSERWH</parameter>
        <parameter key="evp_contis.failed_deposits_needed_master_account_threshold" type="string">50000</parameter>
        <parameter key="evp_contis.notification_email_for_temporary_limited_cards" type="string"><EMAIL></parameter>
        <parameter key="evp_contis.shipping_address_override_change_restriction_check">true</parameter>
        <parameter key="evp_contis.account_activity_transaction_types" type="collection">
            <parameter type="constant">Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardTransaction::TYPE_CREDIT_PURCHASE</parameter>
            <parameter type="constant">Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardTransaction::TYPE_CARD_WITHDRAW</parameter>
            <parameter type="constant">Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardTransaction::TYPE_CARD_PURCHASE_REFUND</parameter>
            <parameter type="constant">Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardTransaction::TYPE_ORIGINAL_CREDIT</parameter>
            <parameter type="constant">Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardTransaction::TYPE_CARD_TRANSFER</parameter>
        </parameter>
        <parameter key="evp_contis.max_cards_for_client_natural">9</parameter>
        <parameter key="evp_contis.max_cards_for_account_natural">10</parameter>
        <parameter key="evp_contis.max_cards_for_account_legal">10</parameter>
        <parameter key="evp_contis.min_main_cardholder_age">16</parameter>
        <parameter key="evp_contis.min_additional_cardholder_age">13</parameter>

        <parameter key="evp_contis.account_activity_threshold" type="string">45</parameter>
        <parameter key="evp_contis.account_activity_threshold_before_expiration_month" type="string">60</parameter>

        <parameter key="contis_inactive_account_first_warning">contis_inactive_account_first_warning</parameter>
        <parameter key="contis_inactive_account_second_warning">contis_inactive_account_second_warning</parameter>
        <parameter key="contis_inactive_account_closure">contis_inactive_account_closure</parameter>
        <parameter key="contis_inactive_account_closure_unsuccessful">contis_inactive_account_closure_unsuccessful</parameter>
        <parameter key="contis_inactive_account_first_warning_date_interval">P60D</parameter>
        <parameter key="contis_inactive_account_second_warning_date_interval">P85D</parameter>
        <parameter key="contis_inactive_account_closure_date_interval">P91D</parameter>
        <parameter key="contis_statement_batch_limit">200</parameter>
        <parameter key="evp_contis.notification_email_for_received_funds_in_closed_contis_accounts" type="string"><EMAIL></parameter>
        <parameter key="evp_contis.notification_email_sender" type="string"><EMAIL></parameter>
        <parameter key="evp_contis.negative_balance_top_up_threshold" type="string">30</parameter>
        <parameter key="evp_contis.notification_email_for_new_debit_transactions_in_closed_contis_accounts" type="string"><EMAIL></parameter>
        <parameter key="evp_contis.unpaid_card_cancellation_notification_key">contis_unpaid_charge_card_cancellation_warning</parameter>
        <parameter key="evp_contis.kosovo_iso_country_codes" type="collection">
            <parameter key="name" type="string">Kosovo</parameter>
            <parameter key="alpha2" type="string">XK</parameter>
            <parameter key="alpha3" type="string">XXK</parameter>
            <parameter key="numeric" type="string">926</parameter>
            <parameter key="currency" type="collection">
                <parameter type="string">EUR</parameter>
            </parameter>
        </parameter>
        <parameter key="evp_contis.legal_client_agreement_code" type="string">%env(CONTIS_LEGAL_CLIENT_AGREEMENT_CODE)%</parameter>
        <parameter key="evp_contis.card_change_delivery_address_commission_amount">10</parameter>
        <parameter key="evp_contis.sca_fail_after_interval">PT6M</parameter>
        <parameter key="evp_contis.balance_update_tolerance_interval">PT1S</parameter>
        <parameter key="evp_contis.batch_refund_statement_tran_from_account_number">********</parameter>
        <parameter key="evp_contis.batch_refund_statement_client_ref_number_date_format">d.m</parameter>

        <parameter key="evp_contis.sftp.host">%env(CONTIS_STATEMENTS_SFTP_HOST)%</parameter>
        <parameter key="evp_contis.sftp.username">%env(CONTIS_STATEMENTS_SFTP_USERNAME)%</parameter>
        <parameter key="evp_contis.sftp.password">null</parameter>
        <parameter key="evp_contis.sftp.private_key">%kernel.root_dir%/%env(CONTIS_STATEMENTS_SFTP_PRIVATE_KEY)%</parameter>
        <parameter key="evp_contis.sftp.passphrase">%env(CONTIS_STATEMENTS_SFTP_PASSPHRASE)%</parameter>
        <parameter key="evp_contis.sftp.port">%env(CONTIS_STATEMENTS_SFTP_PORT)%</parameter>
        <parameter key="evp_contis.sftp.statements_root_dir">/Transactions</parameter>
        <parameter key="evp_contis.sftp.reports_root_dir">/Reports</parameter>
        <parameter key="evp_contis.sftp.cs_reports_root_dir">/cs_reports</parameter>

        <parameter key="evp_contis.eea_countries" type="collection">
            <parameter type="string">IE</parameter><!-- Ireland -->
            <parameter type="string">LT</parameter><!-- Lithuania -->
            <parameter type="string">BG</parameter><!-- Bulgaria -->
            <parameter type="string">RO</parameter><!-- Romania -->
            <parameter type="string">LV</parameter><!-- Latvia -->
            <parameter type="string">EE</parameter><!-- Estonia -->
            <parameter type="string">CZ</parameter><!-- Czech Republic -->
            <parameter type="string">SK</parameter><!-- Slovakia -->
            <parameter type="string">NL</parameter><!-- Netherlands -->
            <parameter type="string">HR</parameter><!-- Croatia -->
            <parameter type="string">HU</parameter><!-- Hungary -->
            <parameter type="string">NO</parameter><!-- Norway -->
            <parameter type="string">PL</parameter><!-- Poland -->
            <parameter type="string">DK</parameter><!-- Denmark -->
            <parameter type="string">SE</parameter><!-- Sweden -->
            <parameter type="string">AT</parameter><!-- Austria -->
            <parameter type="string">BE</parameter><!-- Belgium -->
            <parameter type="string">CY</parameter><!-- Cyprus -->
            <parameter type="string">FI</parameter><!-- Finland -->
            <parameter type="string">DE</parameter><!-- Germany -->
            <parameter type="string">FR</parameter><!-- France -->
            <parameter type="string">GR</parameter><!-- Greece -->
            <parameter type="string">IS</parameter><!-- Iceland -->
            <parameter type="string">IT</parameter><!-- Italy -->
            <parameter type="string">LI</parameter><!-- Liechtenstein -->
            <parameter type="string">LU</parameter><!-- Luxembourg -->
            <parameter type="string">MT</parameter><!-- Malta -->
            <parameter type="string">PT</parameter><!-- Portugal -->
            <parameter type="string">ES</parameter><!-- Spain -->
            <parameter type="string">SI</parameter><!-- Slovenia -->
        </parameter>
    </parameters>

    <services>

        <service id="evp_contis.wallet_oauth_client" class="Paysera_WalletApi">
            <argument>%env(WALLET_OAUTH_CLIENT_MAC_ID)%</argument>
            <argument>%env(WALLET_OAUTH_CLIENT_MAC_SECRET)%</argument>
            <argument type="service">
                <service class="Paysera_WalletApi_Util_Router">
                    <argument>%env(WALLET_OAUTH_CLIENT_API_ENDPOINT)%</argument>
                    <argument>%env(WALLET_OAUTH_CLIENT_AUTH_ENDPOINT)%</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Paysera_WalletApi_Container"/>
            </argument>
        </service>

        <service id="evp_contis.wallet_client" class="Paysera_WalletApi_Client_WalletClient">
            <factory service="evp_contis.wallet_oauth_client" method="walletClient"/>
        </service>

        <service id="evp_contis.card_ordering_validator"
                 class="Evp\Bundle\ContisBundle\Service\CardOrderingValidator">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument id="validator" type="service"/>
            <argument id="translator" type="service"/>
            <argument id="paysera_contis_rest_client.validator.postcode" type="service"/>
            <argument>%paysera_card_integration.delivery_countries%</argument>
            <argument>%paysera_card_integration.tnt_delivery_countries%</argument>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="collection">
                <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_TNT</argument>
            </argument>
            <argument type="service" id="evp_client.service.client_identification_document_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.card_factory"
                 class="Evp\Bundle\ContisBundle\Service\CardFactory">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_contis.transformer.shipping_address_common" type="service"/>
            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="evp_bank_account.repository.account" type="service"/>
            <argument id="evp_commons.universal_transliterator" type="service"/>
            <argument id="evp_contis.manager.card_order_charge" type="service"/>
            <argument id="evp_contis.shipping_address_resolver" type="service"/>
            <argument id="evp_contis.contis_design_reference_resolver" type="service"/>
            <argument id="evp_client.user_information_provider" type="service"/>
            <argument id="evp_contis.repository.office_address" type="service" />
        </service>

        <service id="evp_contis.user_client" class="Evp\Component\UserRestClient\User\UserClient">
            <factory service="evp_user_client.user_rest_factory" method="userClient"/>
        </service>
        <service id="evp_contis.service_client" class="Evp\Component\UserRestClient\Service\ServiceClient">
            <factory service="evp_user_client.user_rest_factory" method="serviceClient"/>
        </service>

        <service id="evp_contis.card_restriction_checker"
                 class="Evp\Bundle\ContisBundle\Service\CardRestrictionChecker">
            <argument type="service" id="evp_client_rest.client_restriction_checker"/>
            <argument type="service" id="evp_bank_account_rest.restriction_checker"/>
        </service>

        <service id="evp_contis.account_balance_provider"
                 class="Evp\Bundle\ContisBundle\Service\AccountBalanceProvider">
            <!--tagged in Extension configuration-->

            <argument type="service" id="evp_bank_account.local_account_balance_provider"/>
            <argument type="service" id="evp_contis.last_known_account_balance_provider"/>
            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.account_balance_helper"/>
        </service>

        <service id="evp_contis.last_known_account_balance_provider"
                 class="Evp\Bundle\ContisBundle\Service\LastKnownAccountBalanceProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_bank_account.local_account_balance_provider"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.account_balance_helper"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.account_state_helper"
                 class="Evp\Bundle\ContisBundle\Service\AccountStateHelper">
            <argument id="paysera_contis_rest_client.account_client" type="service"/>
            <argument id="evp_contis.repository.contis_account" type="service"/>
            <argument id="evp_bank_transfer.repository.transfer" type="service"/>
            <argument id="evp_contis.account_balance_helper" type="service"/>
            <argument id="evp_contis.card_holder_to_close_account_resolver" type="service"/>
        </service>

        <service id="evp_contis.card_state_helper"
                 class="Evp\Bundle\ContisBundle\Service\CardStateHelper">
            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="evp_contis.account_state_helper" type="service"/>
            <argument id="evp_contis.repository.contis_account" type="service"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="collection">
                <argument type="constant">Evp\Bundle\ContisBundle\Entity\Card::STATUS_NEW</argument>
                <argument type="constant">Evp\Bundle\ContisBundle\Entity\Card::STATUS_PENDING</argument>
                <argument type="constant">Evp\Bundle\ContisBundle\Entity\Card::STATUS_ORDERED</argument>
            </argument>
        </service>

        <service id="evp_contis.contis_account_provider"
                 class="Evp\Bundle\ContisBundle\Service\ContisAccountProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <!--tagged in Extension configuration-->

            <argument type="service" id="evp_bank_account.local_account_provider"/>
            <argument type="service" id="evp_bank_account.local_account_balance_provider"/>
            <argument type="service" id="evp_contis.manager.transaction"/>
            <argument type="service" id="evp_bank_account.credit_manager"/>
            <argument>%evp_contis.currency%</argument>
            <argument type="service" id="evp_bank_account.internal_account_transfer_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.turnover_by_contis_limit_provider"
                 class="Evp\Bundle\ContisBundle\Service\TurnoverByContisLimitProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument id="evp_contis.repository.transaction" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_contis.card_deposit_calculator"
                 class="Evp\Bundle\ContisBundle\Service\CardDepositCalculator">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.card_deposit_by_contis_limit_calculator"/>
            <argument type="service" id="evp_bank_permission.provider.level_limits"/>
            <argument type="service" id="evp_contis.repository.transaction"/>
            <argument type="service">
                <service class="Paysera\Component\MoneyHelper\MoneyHelper"/>
            </argument>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_currency.currency_converter_bridge" />
        </service>

        <service id="evp_contis.card_deposit_by_contis_limit_calculator"
                 class="Evp\Bundle\ContisBundle\Service\CardDepositByContisLimitCalculator">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="evp_contis.limit_manager"/>
            <argument type="service" id="evp_contis.turnover_by_contis_limit_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.client_limit_provider.identification_level_based"
                 class="Evp\Bundle\ContisBundle\Service\ClientLimitProvider\IdentificationLevelBasedLimitProvider">
            <argument type="service" id="evp_bank_permission.level_provider"/>
            <argument type="service" id="evp_identification_level_common.provider.country_limits"/>
        </service>

        <service id="evp_bank_account.conversion_provider.contis_account"
                 class="Evp\Bundle\ContisBundle\Service\ContisAccountConversionProvider">
            <tag name="evp_currency.currency_conversion_provider" key="contis"/>

            <argument type="service" id="evp_bank_account.conversion_provider.local_account"/>
            <argument type="service" id="evp_contis.contis_account_provider"/>
        </service>

        <service id="evp_contis.rabbit_mq_plain_client" class="Evp\Bundle\RabbitMqExtensionBundle\Service\PlainClient">
            <argument type="service" id="old_sound_rabbit_mq.connection.records_queue"/>
        </service>

        <service id="evp_contis.statement_importer" class="Evp\Bundle\ContisBundle\Service\StatementImporter">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.normalizer.statement"/>
            <argument type="service" id="evp_contis.contis_statement_creator"/>
            <argument type="service">
                <service class="Goodby\CSV\Import\Standard\Lexer">
                    <argument type="service">
                        <service class="Goodby\CSV\Import\Standard\LexerConfig">
                            <call method="setFromCharset">
                                <argument>UCS-2LE</argument>
                            </call>
                            <call method="setToCharset">
                                <argument>UTF-8</argument>
                            </call>
                        </service>
                    </argument>
                </service>
            </argument>
            <argument type="service" id="evp_contis.publisher.records_queue"/>
            <argument>%contis_statement_batch_limit%</argument>
            <argument type="collection">
                <argument type="string">%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS)%</argument>
                <argument type="string">%evp_contis.paysera_account_number_at_contis_old%</argument>
            </argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.refund_statement_importer" class="Evp\Bundle\ContisBundle\Service\RefundStatementImporter">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.normalizer.statement"/>
            <argument type="service" id="evp_contis.contis_statement_creator"/>
            <argument type="service" id="evp_contis.refund_statement_file_manager"/>
            <argument type="service" id="evp_contis.statement_importer"/>
            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service" id="evp_contis.refund_transaction_manager"/>
            <argument type="service" id="evp_bundle_contis.repository.statement_refund_import_repository"/>
            <argument type="service" id="logger"/>
            <argument>%evp_contis.currency%</argument>
        </service>

        <service id="evp_contis.refund_statement_file_manager" class="Evp\Bundle\ContisBundle\Service\RefundStatementFileManager">
            <argument type="service" id="evp_contis.normalizer.statement"/>
            <argument type="service" id="evp_operator_widget.encoding_converter"/>
        </service>

        <service id="evp_contis.refund_transaction_manager" class="Evp\Bundle\ContisBundle\Service\RefundTransactionManager">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.manager.transaction"/>
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
        </service>

        <service id="evp_contis.contis_statement_validator"
                 class="Evp\Bundle\ContisBundle\Service\ContisStatementValidator">
            <argument type="service" id="validator"/>
        </service>

        <service id="evp_contis.contis_statement_creator"
                 class="Evp\Bundle\ContisBundle\Service\ContisStatementCreator">
            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service" id="evp_contis.contis_statement_validator"/>
            <argument type="service" id="evp_bank_account.operation_manager"/>
            <argument type="service" id="evp_contis.statement_properties_resolver"/>
            <argument type="service" id="evp_contis.manager.contis_account_top_up"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_job_publisher"/>
            <argument type="service" id="evp_contis.manager.transaction"/>
        </service>

        <service id="evp_contis.processor.contis_operation"
                 class="Evp\Bundle\ContisBundle\Service\ContisOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor" class="Evp\Bundle\ContisBundle\Entity\ContisOperation"/>

            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.statement_properties_resolver"/>

            <!--functionality is disabled at this moment. We do not care what client does with his money when they are loaded to his card-->
            <!--<argument>CONTIS_CARD_IN_EUR</argument>-->
            <!--<argument>CONTIS_CARD_OUT_EUR</argument>-->
        </service>

        <service id="evp_contis.contis_statement_assembler"
                 class="Evp\Bundle\ContisBundle\Service\ContisStatementAssembler">
            <tag name="evp_bank_account.statement.assembler" class="Evp\Bundle\ContisBundle\Entity\ContisOperation"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.statement_properties_resolver"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.repository.card"/>
        </service>

        <service id="evp_contis.statement_properties_resolver"
                 class="Evp\Bundle\ContisBundle\Service\StatementPropertiesResolver">

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="collection">
                <argument type="string">01</argument>
                <argument type="string">04</argument>
                <argument type="string">07</argument>
                <argument type="string">21</argument>
                <argument type="string">22</argument>
                <argument type="string">27</argument>
                <argument type="string">32</argument>
                <argument type="string">34</argument>
                <argument type="string">35</argument>
                <argument type="string">37</argument>
                <argument type="string">44</argument>
                <argument type="string">45</argument>
            </argument>
            <argument type="collection">
                <argument type="string">02</argument>
                <argument type="string">05</argument>
                <argument type="string">11</argument>
                <argument type="string">20</argument>
                <argument type="string">23</argument>
                <argument type="string">30</argument>
                <argument type="string">31</argument>
                <argument type="string">33</argument>
                <argument type="string">36</argument>
                <argument type="string">38</argument>
                <argument type="string">39</argument>
            </argument>
            <argument type="string">05</argument>
            <argument type="collection">
                <argument type="string">04</argument>
                <argument type="string">35</argument>
                <argument type="string">37</argument>
                <argument type="string">45</argument>
            </argument>
            <argument type="string">29</argument> <!--Only if not from EVP account-->
            <argument type="collection">
                <argument>%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS)%</argument>
                <argument>%evp_contis.paysera_account_number_at_contis_old%</argument>
            </argument>
            <argument type="collection">
                <argument type="string">********</argument>
                <argument type="string">********</argument>
            </argument>
        </service>

        <service id="evp_contis.card_monthly_charge_manager"
                 class="Evp\Bundle\ContisBundle\Service\CardMonthlyChargeManager">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.repository.card_monthly_charge"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="evp_bank_charge.processor.charge"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_charge.charge_factory"/>
            <argument type="service" id="evp_bank_charge.charge_account_locker"/>
            <argument type="service" id="evp_contis.repository.contis_account_fee"/>
            <argument type="service" id="evp_bank_charge.repository.charge"/>
        </service>

        <service id="evp_contis.locking_card_charge_processor"
                 class="Evp\Bundle\ContisBundle\Service\LockingCardChargeProcessor">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_bank_charge.charge_account_locker" type="service"/>
            <argument id="evp_bank_charge.processor.charge" type="service"/>
        </service>

        <service id="evp_contis.processor.pending_card"
                 class="Evp\Bundle\ContisBundle\Service\PendingCardProcessor">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_contis_rest_client.card_client"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="evp_contis.resolver.error_codes"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_contis.card_ordering_validator"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.processor.card_address_line_helper"
                 class="Evp\Bundle\ContisBundle\Service\CardAddressLineHelper">
            <argument>50</argument>
            <argument type="collection">
                <argument type="string">,</argument>
                <argument type="string"> </argument>
            </argument>
            <argument type="service" id="evp_commons.universal_transliterator"/>
        </service>

        <service id="evp_contis.processor.tnt_address_line_helper"
                 class="Evp\Bundle\ContisBundle\Service\CardAddressLineHelper">
            <argument>30</argument>
            <argument type="collection">
                <argument type="string">,</argument>
                <argument type="string"> </argument>
            </argument>
            <argument type="service" id="evp_commons.universal_transliterator"/>
        </service>

        <service id="evp_contis.card_order_error_handler"
                 class="Evp\Bundle\ContisBundle\Service\CardIssuing\CardOrderErrorHandler">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_contis.shipping_address_error_helper"/>
            <argument type="service" id="evp_contis.resolver.error_codes"/>
            <argument type="service" id="logger"/>

            <tag name="monolog.logger" channel="evp_contis.contis"/>
        </service>

        <service id="evp_contis.processor.queued_cards" class="Evp\Bundle\ContisBundle\Service\CardIssuing\QueuedCardsProcessor">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.wallet_client"/>
            <argument type="service" id="evp_user_client.user_rest_factory"/>
            <argument type="service" id="evp_client.user_information_provider" />
            <argument type="service" id="evp_commons.universal_transliterator"/>
            <argument type="service">
                <service class="Evp\Component\Country\CountryCodeMapper"/>
            </argument>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="logger"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%evp_contis.cards_maturity_interval%</argument>
                </service>
            </argument>
            <argument type="service" id="evp_contis.processor.card_address_line_helper"/>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="evp_contis.card_order_error_handler"/>
            <argument type="service" id="evp_contis.recurring_card_address_handler"/>
            <argument type="service" id="evp_contis.iso_country_code_provider"/>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_contis.shipment_code_provider.first_match_shipment_code_provider"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
            <argument type="service" id="evp_contis.service.contis_user_provider" />
            <argument type="string">%evp_contis.legal_client_agreement_code%</argument>
        </service>

        <service id="evp_contis.card_order_restrictions_checker"
                 class="Evp\Bundle\ContisBundle\Service\CardOrderRestrictionsChecker">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.repository.card"/>
            <argument>%evp_contis.max_cards_for_client_natural%</argument>
            <argument>%evp_contis.max_cards_for_account_natural%</argument>
            <argument>%evp_contis.max_cards_for_account_legal%</argument>
            <argument id="evp_contis.user_client" type="service"/>
            <argument>%evp_contis.min_main_cardholder_age%</argument>
            <argument>%evp_contis.min_additional_cardholder_age%</argument>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.card_order_warnings_checker"
                 class="Evp\Bundle\ContisBundle\Service\CardOrderWarningsChecker">
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_contis.repository.card"/>
        </service>

        <service id="evp_contis.card_management_controller"
                 class="Evp\Bundle\ContisBundle\Controller\CardManagementController">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <tag name="controller.service_arguments"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.resolver.error_codes"/>
            <argument type="service" id="evp_contis.card_state_helper"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%evp_contis.cards_maturity_interval%</argument>
                </service>
            </argument>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_contis.manager.xpay_token"/>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="evp_contis.repository.contis_account" />

            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="evp_contis.service.client_identity_data_updater"/>
        </service>

        <service id="evp_contis.form.block_card" class="Evp\Bundle\ContisBundle\Form\BlockCardType">
            <tag name="form.type" alias="evp_contis_block_card_form"/>
        </service>

        <service id="evp_contis.form.unblock_three_d_secure_password"
                 class="Evp\Bundle\ContisBundle\Form\UnblockThreeDSecurePasswordType">
            <tag name="form.type" alias="evp_contis_unblock_three_d_secure_password_form"/>
        </service>

        <service id="evp_contis.form.cancel_card" class="Evp\Bundle\ContisBundle\Form\CancelCardType">
            <tag name="form.type" alias="evp_contis_cancel_card_form"/>

            <argument type="service" id="translator"/>
        </service>

        <service id="evp_contis.form.synchronize_account" class="Evp\Bundle\ContisBundle\Form\SynchronizeAccountType">
            <tag name="form.type" alias="evp_contis_synchronize_account_form"/>
        </service>

        <service id="evp_contis.form.update_client_identity_data"
                 class="Evp\Bundle\ContisBundle\Form\UpdateClientIdentityDataType">
            <tag name="form.type" alias="evp_contis_update_client_identity_data_form"/>

            <argument type="service" id="translator"/>
        </service>

        <service id="evp_contis.pending_transactions_reservation_provider"
                 class="Evp\Bundle\ContisBundle\Service\PendingTransactionsReservationStatementProvider">
            <tag name="evp_bank_account.reservation_statement_provider"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.repository.reservation_statement"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_account.service.reservation_statement_mapper" />
        </service>

        <service id="evp_contis.card_checker"
                 class="Evp\Bundle\ContisBundle\Service\CardChecker">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="paysera_contis_rest_client.card_client"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.card_monthly_charge_manager"/>
            <argument type="service" id="validator"/>
            <argument type="service">
                <service class="Paysera\Component\Validator\ValidatorHelper"/>
            </argument>
            <argument type="service" id="evp_contis.manager.card_order_charge"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.repository.card_delivery_preference"/>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_contis.resolver.card_status"/>
        </service>

        <service id="evp_contis.repository.transfer_out_bank"
                 class="Evp\Bundle\ContisBundle\Service\TransferOutBankRepository">

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\ContisDebtProvider"
                 id="evp_contis.service.contis_debt_provider">
            <argument id="evp_contis.repository.contis_account" type="service"/>
            <argument id="evp_rabbit_mq_extension.remote_job_publisher" type="service"/>
        </service>

        <service id="evp_contis.account_finalizer" class="Evp\Bundle\ContisBundle\Service\AccountFinalizer">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_bank_account.local_account_balance_provider"/>
            <argument type="service" id="evp_contis.manager.transaction"/>
            <argument type="service" id="evp_contis.account_state_helper"/>
            <argument type="service" id="evp_bank_account.account_type_change_manager"/>
            <argument type="service" id="evp_contis_accounting.account_finalization_helper" />
            <argument type="service" id="evp_contis.card_state_helper" />
            <argument type="service" id="evp_contis.manager.contis_account_top_up"/>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_contis.resolver.error_codes"/>
            <argument type="service" id="evp_contis.primary_consumer_synchronizer"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="evp_contis.broken_transaction_fixer"
                 class="Evp\Bundle\ContisBundle\Service\BrokenTransactionFixer">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="paysera_contis_rest_client.card_client"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.manager.transaction"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.chargeable_account_resolver"
                 class="Evp\Bundle\ContisBundle\Service\ChargeableAccountResolver">
            <argument id="evp_bank_account.repository.account" type="service"/>
        </service>

        <service id="evp_contis.card_issue_price_provider"
                 class="Evp\Bundle\ContisBundle\Service\CardPriceProvider">
            <argument type="service" id="paysera_card_integration.repository.card_delivery_price"/>
            <argument type="service" id="evp_contis.repository.card_issue_price"/>
        </service>

        <service id="evp_contis.evp_contis.client_aware_delivery_price_provider"
                 class="Evp\Bundle\ContisBundle\Service\ClientAwareDeliveryPriceProvider">
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="security.token_storage"/>
            <argument type="service" id="evp_contis.card_issue_price_provider"/>
            <argument type="service" id="evp_contis.repository.card"/>
        </service>

        <service id="evp_contis.service.client_age_helper" class="Evp\Bundle\ContisBundle\Service\ClientAgeHelper">
            <argument type="service" id="evp_contis.user_client"/>
            <argument type="service" id="evp.component.time.clock"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\ContisIncomeHandler"
                 id="evp_contis.service.contis_income_handler">
            <tag name="evp_bank_account.account_income_handler" priority="10"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.registered_mail_extension" class="Evp\Bundle\ContisBundle\Twig\RegisteredMailExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="evp_contis.registered_mail_runtime" class="Evp\Bundle\ContisBundle\Twig\RegisteredMailRuntime">
            <argument type="service" id="paysera_card_integration.repository.registered_mail"/>
            <tag name="twig.runtime"/>
        </service>

        <service id="evp_contis.contis_account_number_extension"
                 class="Evp\Bundle\ContisBundle\Twig\ContisAccountNumberExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="evp_contis.contis_account_number_runtime"
                 class="Evp\Bundle\ContisBundle\Twig\ContisAccountNumberRuntime">
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <tag name="twig.runtime"/>
        </service>

        <service id="evp_contis.contis_account_status_extension"
                 class="Evp\Bundle\ContisBundle\Twig\ContisAccountStatusExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="evp_contis.contis_account_status_runtime"
                 class="Evp\Bundle\ContisBundle\Twig\ContisAccountStatusRuntime">
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <tag name="twig.runtime"/>
        </service>

        <service id="evp_contis.tnt_shipment_status_extension"
                 class="Evp\Bundle\ContisBundle\Twig\TntShipmentStatusExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="evp_contis.tnt_shipment_status_runtime"
                 class="Evp\Bundle\ContisBundle\Twig\TntShipmentStatusRuntime">
            <argument type="service" id="paysera_card_integration.repository.tnt_shipment"/>
            <tag name="twig.runtime"/>
        </service>

        <service id="evp_contis.twig.array_value_extension"
                 class="Evp\Bundle\ContisBundle\Twig\ArrayValueExtension">
            <tag name="twig.extension"/>
        </service>

        <service id="evp_contis.service.refund_validator" class="Evp\Bundle\ContisBundle\Service\RefundValidator">
            <tag name="paysera_refund_validator"/>
        </service>

        <service id="evp_contis.unpaid_cards_processor"
                 class="Evp\Bundle\ContisBundle\Service\UnpaidCardsProcessor">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument>%evp_contis.cancel_card_after_unpaid_months%</argument>
            <argument>%evp_contis.cancel_card_after_number_of_unpaid_charges%</argument>
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="evp_contis.repository.card_monthly_charge"/>
            <argument type="service" id="evp_contis.locking_card_charge_processor"/>
            <argument type="service" id="evp_bank_account.account_balance_manager"/>
            <argument type="service" id="evp_contis.repository.contis_account_fee"/>
            <argument type="service" id="evp_contis.account_state_helper"/>
            <argument type="service" id="evp_contis.date_interval_helper"/>
            <argument type="service" id="evp_contis.cancel_other_alive_cards_to_enable_contis_account_closure_processor"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.unpaid_cards_notifier"
                 class="Evp\Bundle\ContisBundle\Service\UnpaidCardsNotifier">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="evp_contis.repository.card_monthly_charge" type="service"/>
            <argument id="evp_rest_user_api_client" type="service"/>
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.card_cancel_commission_amount%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
            <argument>%evp_contis.wait_days_before_notifying_about_unpaid_monthly_charge%</argument>
            <argument>%evp_contis.warn_about_cancellation_after_unpaid_months%</argument>
            <argument>%evp_contis.cancel_card_after_unpaid_months%</argument>
            <argument id="evp_contis.date_interval_helper" type="service"/>
            <argument id="evp_contis.unpaid_card_cancellation_notification_sender" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_contis.voter.card"
                 class="Evp\Bundle\ContisBundle\Voter\CardRoleVoter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_contis.manager.card_permissions"/>
        </service>

        <service id="evp_contis.scope_voter.card"
                 class="Evp\Bundle\ContisBundle\Voter\CardScopeVoter"
                 parent="paysera_security.security.context_aware_scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_contis.manager.card_permissions"/>
            <argument type="service" id="evp_client.client_extractor"/>
        </service>

        <service id="evp_contis.scope_voter.card_deprecated"
                 class="Evp\Bundle\ContisBundle\Voter\DeprecatedCardScopeVoter"
                 parent="paysera_security.security.scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.card_restriction_checker"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.date_interval_helper" class="Evp\Bundle\ContisBundle\Service\DateIntervalHelper"/>

        <service class="Evp\Bundle\ContisBundle\Service\ContisAccountManager"
                 id="evp_contis.account_manager">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="event_dispatcher"/>
            <argument>%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS)%</argument>
            <argument>%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_SORT_CODE)%</argument>
            <argument>%evp_contis.currency%</argument>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.repository.account_agreement_code"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%evp_contis.balance_update_tolerance_interval%</argument>
                </service>
            </argument>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\LimitManager"
                 id="evp_contis.limit_manager">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="validator" type="service"/>
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.card_balance_default_limit_amount%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
            <argument>%evp_contis.default_limit_period%</argument>
        </service>

        <service id="evp_contis.service.account_repository"
                 class="Evp\Bundle\ContisBundle\Service\AccountRepository" lazy="true">
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\CardHolderToCloseAccountResolver"
                 id="evp_contis.card_holder_to_close_account_resolver">
            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="paysera_contis_rest_client.consumer_client" type="service"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\RecurringCardAddressHandler"
                 id="evp_contis.recurring_card_address_handler">
            <argument id="evp_contis.repository.shipping_address" type="service"/>
            <argument>3</argument>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\CardholderAssimilator"
                 id="evp_contis.cardholder_assimilator">
            <tag name="monolog.logger" channel="evp_contis.cardholder_update"/>

            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_client.user_information_provider"/>
            <argument type="service" id="evp_contis.iso_country_code_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\ContisAccountLimitManager"
                 id="evp_contis.account_limit_manager">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_contis.repository.limit"/>
            <argument type="service" id="paysera_contis_master_account.repository.contis_decreased_account_limit"/>
            <argument type="service" id="paysera_contis_rest_client.account_client" />
            <argument type="service" id="evp_contis.repository.contis_account" />
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.card_balance_default_limit_amount%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
            <argument type="service" id="paysera_contis_master_account.resolver.auto_top_up_configuration"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.factory.contact_details_info"
                 class="Evp\Bundle\ContisBundle\Service\ContactDetailsInfoFactory">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument id="evp_contis.processor.card_address_line_helper" type="service"/>
            <argument type="service">
                <service class="Evp\Component\Country\CountryCodeMapper"/>
            </argument>
            <argument type="service" id="evp_commons.universal_transliterator"/>
        </service>

        <service id="evp_contis.semaphore_store" class="Symfony\Component\Lock\Store\SemaphoreStore"/>
        <service id="evp_contis.semaphore_lock_factory"
                 class="Symfony\Component\Lock\Factory">
            <argument type="service" id="evp_contis.semaphore_store"/>
        </service>

        <service id="evp_contis.contis_design_reference_resolver"
                 class="Evp\Bundle\ContisBundle\Service\ContisDesignReferenceResolver">
            <argument type="service" id="evp_contis.repository.contis_card_design_type"/>
        </service>

        <service id="evp_contis.inactive_account_canceller"
                 class="Evp\Bundle\ContisBundle\Service\InactiveAccountCanceller">
            <argument id="evp_contis.manager.card" type="service"/>
            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="evp_contis.manager.contis_account_top_up" type="service"/>
            <argument id="evp_contis.account_balance_helper" type="service"/>
            <argument id="evp_contis.account_manager" type="service"/>
            <argument id="evp_contis.card_state_helper" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_contis.account_balance_helper"
                 class="Evp\Bundle\ContisBundle\Service\AccountBalanceHelper">
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.negative_balance_top_up_threshold%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.entity_resolver.card_by_id"
                 class="Paysera\Bundle\RestBundle\Resolver\RepositoryAwareEntityResolver">
            <argument type="service" id="evp_contis.repository.card"/>
            <argument>id</argument>
        </service>

        <service id="evp_contis.account_synchronizer"
                 class="Evp\Bundle\ContisBundle\Service\AccountSynchronizer">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_contis.account_state_helper"/>
            <argument type="service" id="evp_contis.account_finalizer"/>
            <argument type="service" id="evp_contis.manager.account_notification"/>
            <argument type="service" id="evp_contis.inactive_account_canceller"/>
            <argument type="service" id="evp_contis.repository.contis_fraudulent_account"/>
            <argument type="service" id="evp_bank_account.local_statement_provider"/>
            <argument type="service" id="evp_bank_account.repository.statement"/>
            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%contis_inactive_account_closure_date_interval%</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%contis_inactive_account_first_warning_date_interval%</argument>
                </service>
            </argument>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.manager.reservation_statement"/>
        </service>

        <service id="evp_contis.account_type_contis_helper"
                 class="Evp\Bundle\ContisBundle\Service\AccountTypeContisHelper">
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_contis.authorization_result_mapper"
                 class="Evp\Bundle\ContisBundle\Service\AuthorizationResultMapper">
        </service>

        <service id="evp_contis.soap_client_cipher_testing"
                 class="Evp\Bundle\ContisBundle\Service\CipherTestingSoapClient">
            <argument>%evp_contis_client.soap.wsdl%</argument>
            <argument type="collection">
                <argument key="options" type="collection">
                    <argument key="trace">true</argument>
                    <argument key="keep_alive">false</argument>
                    <argument key="connection_timeout">2</argument>
                    <argument key="cache_wsdl">%evp_contis_client.soap.cache_wsdl%</argument>
                </argument>
                <argument key="stream_context" type="collection">
                    <argument key="ssl" type="collection">
                        <argument type="constant" key="crypto_method">STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT</argument>
                        <argument key="ciphers">ECDHE-ECDSA-AES128-GCM-SHA256</argument>
                    </argument>
                </argument>
            </argument>
        </service>

        <service id="evp_contis.card_client_cipher_testing" class="%evp_contis_client.class.contis_card_client%">
            <argument type="service" id="evp_contis.soap.authenticated_client_cipher_testing"/>
            <argument type="service" id="evp_contis_client.normalizer.card"/>
        </service>

        <service id="evp_contis.soap.authenticated_client_cipher_testing"
                 class="Evp\Component\ContisClient\SoapClient\AuthenticatedClient">
            <tag name="monolog.logger" channel="evp_contis_client.authenticated_client"/>

            <argument type="service" id="evp_contis.soap_client_cipher_testing"/>
            <argument type="service" id="evp_contis_client.login_token_provider"/>
            <argument type="service" id="evp_contis_client.request_handler"/>
            <argument type="service" id="evp_contis_client.encrypter.argument"/>
            <argument type="service" id="evp_contis_client.hasher.sha256"/>
            <argument type="service" id="evp_contis_client.sanitizer.xml_response"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.shipping_address_resolver"
                 class="Evp\Bundle\ContisBundle\Service\ShippingAddressResolver">
            <argument type="service" id="evp_contis.repository.shipping_address"/>
            <argument type="service" id="evp_contis.manager.card_delivery_preference"/>
            <argument type="service" id="evp_contis.repository.card_delivery_preference"/>
            <argument type="service" id="evp_contis.recurring_card_address_handler"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.iso_country_code_provider"
                 class="Evp\Bundle\ContisBundle\Service\ISOCountryCodeProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument type="service">
                <service class="libphonenumber\PhoneNumberUtil">
                    <factory class="libphonenumber\PhoneNumberUtil" method="getInstance"/>
                </service>
            </argument>
            <argument type="service">
                <service class="League\ISO3166\ISO3166">
                    <argument type="expression">
                        service('evp_contis.provider.iso_country_code_list').getCountries()
                    </argument>
                </service>
            </argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.unpaid_card_cancellation_notification_sender"
                 class="Evp\Bundle\ContisBundle\Service\UnpaidCardCancellationNotificationSender">
            <argument type="service" id="paysera.transfer_surveillance.client.user_notification"/>
            <argument>%evp_contis.unpaid_card_cancellation_notification_key%</argument>
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.card_cancel_commission_amount%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.tnt_sender_address_info_provider"
                 class="Evp\Bundle\ContisBundle\Service\TntSenderAddressInfoProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument>%evp_contis.tnt_sender_company%</argument>
            <argument>%evp_contis.tnt_sender_street_address%</argument>
            <argument>%evp_contis.tnt_sender_city%</argument>
            <argument>%evp_contis.tnt_sender_postcode%</argument>
            <argument>%evp_contis.tnt_sender_country%</argument>
            <argument>%evp_contis.tnt_sender_account%</argument>
            <argument>%evp_contis.tnt_sender_contact_name%</argument>
            <argument>%evp_contis.tnt_sender_contact_dial_code%</argument>
            <argument>%evp_contis.tnt_sender_contact_phone%</argument>
            <argument>%evp_contis.tnt_sender_contact_email%</argument>
        </service>

        <service id="evp_contis.manager.tnt_package_info_provider"
                 class="Evp\Bundle\ContisBundle\Service\TntPackageInfoProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument>%evp_contis.tnt_package_description%</argument>
            <argument>%evp_contis.tnt_package_weight%</argument>
            <argument>%evp_contis.tnt_package_length%</argument>
            <argument>%evp_contis.tnt_package_height%</argument>
            <argument>%evp_contis.tnt_package_width%</argument>
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.tnt_package_goods_value%</argument>
                    <argument>%evp_contis.tnt_package_goods_currency%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.cancel_other_alive_cards_to_enable_contis_account_closure_processor"
                 class="Evp\Bundle\ContisBundle\Processor\CancelOtherAliveCardsProcessor">
            <argument type="service" id="evp_contis.manager.card"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.provider.iso_country_code_list"
                 class="Evp\Bundle\ContisBundle\Service\ISOCountryCodeListProvider">
            <argument type="service">
                <service class="League\ISO3166\ISO3166"/>
            </argument>
            <argument type="collection">
                <argument>%evp_contis.kosovo_iso_country_codes%</argument>
            </argument>
        </service>

        <service id="evp_contis.non_transaction_token_xlsx_parser"
                 class="Evp\Bundle\ContisBundle\Service\NonTransactionTokenXlsxParser">
            <argument type="service">
                <service class="PhpOffice\PhpSpreadsheet\Reader\Xlsx"/>
            </argument>
            <argument type="service" id="evp_contis.filesystem.sftp.reports"/>
            <argument type="string">Non_Transacting_Tokens_%s.xlsx</argument>
        </service>

        <service id="evp_contis.non_transaction_token_notifier"
                 class="Evp\Bundle\ContisBundle\Service\NonTransactionTokenNotifier">
            <argument type="service">
                <service class="Evp\Bundle\ContisBundle\Service\NonTransactionTokenNotificationChecker"/>
            </argument>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_event_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service class="Evp\Bundle\ContisBundle\Service\ShipmentCodeProvider\ShipmentCodeProvider"
                 id="evp_contis.shipment_code_provider.shipment_code_provider">
            <argument type="collection">
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_70</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_TNT</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_80</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_DHL</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_75</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGISTERED_POST</argument>
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::COUNTRY_ROMANIA</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_75</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGISTERED_POST</argument>
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::COUNTRY_BULGARIA</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_72</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGULAR</argument>
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::COUNTRY_LITHUANIA</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_76</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGULAR</argument>
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::COUNTRY_KOSOVO</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_73</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGULAR</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                        <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_71</argument>
                        <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGISTERED_POST</argument>
                    </service>
                </argument>
            </argument>
        </service>
        <service class="Evp\Bundle\ContisBundle\Service\ShipmentCodeProvider\MainOfficeShipmentCodeProvider"
                 id="evp_contis.shipment_code_provider.main_office_shipment_code_provider">
            <argument type="service">
                <service class="Evp\Bundle\ContisBundle\Entity\ShipmentCode">
                    <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::SHIPMENT_METHOD_CODE_74</argument>
                    <argument type="constant">Paysera\Bundle\CardIntegrationBundle\DeliveryTypes::DELIVERY_TYPE_REGULAR</argument>
                    <argument type="constant">Evp\Bundle\ContisBundle\Entity\ShipmentCode::COUNTRY_LITHUANIA</argument>
                </service>
            </argument>
        </service>
        <service class="Evp\Bundle\ContisBundle\Service\ShipmentCodeProvider\FirstMatchShipmentCodeProvider"
                 id="evp_contis.shipment_code_provider.first_match_shipment_code_provider">
            <argument type="collection">
                <argument type="service" id="evp_contis.shipment_code_provider.main_office_shipment_code_provider"/>
                <argument type="service" id="evp_contis.shipment_code_provider.shipment_code_provider"/>
            </argument>
        </service>

        <service id="evp_contis.shipping_address_error_helper" class="Evp\Bundle\ContisBundle\Service\ShippingAddressErrorHelper">
            <argument type="service" id="evp_contis.resolver.error_codes"/>
            <argument>3</argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.pending_challenges_processor" class="Evp\Bundle\ContisBundle\Service\PendingChallengesProcessor">
            <argument type="service" id="paysera_contis_push_notifications.repository.strong_customer_authentication"/>
            <argument type="service" id="paysera_contis_rest_client.security_client"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.resolver.error_codes"/>
        </service>

        <service id="evp_contis.processor.virtual_queued_cards" class="Evp\Bundle\ContisBundle\Service\CardIssuing\QueuedVirtualCardsProcessor">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="evp_contis.account_manager"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument type="service" id="evp_contis.manager.card_status_change"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher"/>
            <argument type="service" id="evp_user_client.user_rest_factory"/>
            <argument type="service" id="evp_client.user_information_provider"/>
            <argument type="service" id="evp_contis.iso_country_code_provider"/>
            <argument type="service" id="evp_commons.universal_transliterator"/>
            <argument type="service" id="paysera_contis_rest_client.normalizer.virtual_consumer"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.primary_consumer_synchronizer"
                 class="Evp\Bundle\ContisBundle\Service\PrimaryConsumerSynchronizer">
            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
        </service>

        <service id="evp_contis.manager.reservation_statement"
                 class="Evp\Bundle\ContisBundle\Service\ReservationStatementManager">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument type="service" id="evp_contis.repository.reservation_statement"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="paysera_contis_push_notifications.transaction_notification_mapper"/>
            <argument type="service" id="paysera_contis_rest_client.account_client"/>
            <argument type="service" id="evp_contis.authorization_result_mapper"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.dhl_pickup_info_provider"
                 class="Evp\Bundle\ContisBundle\Service\DhlPickupInfoProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>
            <argument>%evp_contis.tnt_sender_company%</argument>
            <argument>%evp_contis.tnt_sender_street_address%</argument>
            <argument>%evp_contis.tnt_sender_city%</argument>
            <argument>%evp_contis.dhl_sender_postcode%</argument>
            <argument>%evp_contis.tnt_sender_country%</argument>
            <argument>%evp_contis.tnt_sender_contact_name%</argument>
            <argument>%evp_contis.dhl_sender_contact_phone%</argument>
            <argument>%evp_contis.dhl_sender_account_number%</argument>
        </service>

        <service id="evp_contis.pdf_merger"
                 class="Evp\Bundle\ContisBundle\Service\PdfMerger">
            <argument type="service">
                <service class="setasign\Fpdi\Fpdi"/>
            </argument>
        </service>

        <service id="evp_contis.provider.dhl_export_declaration"
                 class="Evp\Bundle\ContisBundle\Service\DhlExportDeclarationProvider">
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.tnt_package_goods_value%</argument>
                    <argument>%evp_contis.tnt_package_goods_currency%</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Paysera\Component\DhlClient\Entity\Weight">
                    <call method="setGrossValue">
                        <argument>%evp_contis.tnt_package_weight%</argument>
                    </call>
                    <call method="setNetValue">
                        <argument>%evp_contis.tnt_package_weight%</argument>
                    </call>
                </service>
            </argument>
            <argument>%evp_contis.tnt_sender_country%</argument>
            <argument>%evp_contis.dhl_export_reason%</argument>
        </service>

        <service id="evp_contis.services.provider.dhl_output_image_properties"
                 class="Evp\Bundle\ContisBundle\Service\DhlOutputImagePropertiesProvider"/>

        <service id="evp_contis.card_design_helper"
                 class="Evp\Bundle\ContisBundle\Service\CardDesignHelper">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="paysera_card_design.repository.card_design"/>
            <argument type="service" id="evp_contis.repository.contis_card_design_type"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.refunds_extractor"
                 class="Evp\Bundle\ContisBundle\Service\RefundsProcessor\RefundsExtractor">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.refunds_xlsx_file_parser"/>
            <argument type="string">xlsx</argument>
            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.filesystem.sftp.cs_reports"/>
            <argument type="service" id="evp_contis.filesystem.local"/>
            <argument>%evp_contis.sftp.host%</argument>
            <argument>%evp_contis.sftp.cs_reports_root_dir%</argument>
        </service>

        <service id="evp_contis.refunds_xlsx_file_parser"
                 class="Evp\Bundle\ContisBundle\Service\RefundsProcessor\RefundsXlsxFileParser">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service">
                <service class="PhpOffice\PhpSpreadsheet\Reader\Xlsx"/>
            </argument>
            <argument type="service" id="paysera_contis_rest_client.sanitizer.account_number"/>
            <argument type="service" id="evp_contis.repository.contis_account"/>
            <argument>%evp_contis.currency%</argument>
            <argument>€</argument>
            <argument> </argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.refunds_transaction_ids_provider"
                 class="Evp\Bundle\ContisBundle\Service\RefundsProcessor\RefundsTransactionIdsProvider">
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.repository.statement"/>
            <argument type="service" id="evp_contis.repository.statement_refund_import_prepared_transaction_id"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service id="evp_contis.form.synchronize_account_balance"
                 class="Evp\Bundle\ContisBundle\Form\SynchronizeAccountBalanceType">
            <tag name="form.type" alias="evp_contis_synchronize_account_balance_form"/>
        </service>
        <service id="evp_contis.terms_and_conditions_factory"
                 class="Evp\Bundle\ContisBundle\Service\TermsAndConditionsFactory">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_contis.publisher.records_queue"
                 class="Evp\Bundle\ContisBundle\Service\RecordsQueuePublisher">
            <argument type="service" id="evp_contis.refund_statement_file_manager"/>
            <argument type="service" id="evp_contis.rabbit_mq_plain_client"/>
            <argument type="string">/queue/Mokejimai.Contis</argument>
        </service>

        <service id="evp_contis.xlsx_reader"
                 class="PhpOffice\PhpSpreadsheet\Reader\Xlsx"
                 shared="false"/>

        <service id="evp_contis.service.contis_user_provider"
                 class="Evp\Bundle\ContisBundle\Service\ContisUserProvider">
            <argument type="service" id="evp_client.service.client_identification_document_provider"/>
            <argument type="service" id="evp_questionnaire_service.repositories.questionnaire"/>
            <argument type="service" id="evp_user_client.user_rest_factory"/>
            <argument type="service" id="evp_contis.service.contis_user_data_mapper"/>
            <argument>%evp_contis.eea_countries%</argument>
        </service>

        <service id="evp_contis.service.contis_user_data_mapper"
                 class="Evp\Bundle\ContisBundle\Service\ContisUserDataMapper">
            <argument type="service" id="evp_currency.maba.money_calculator"/>
            <argument type="service" id="evp_currency.currency_converter.official.cached"/>
        </service>

        <service id="evp_contis.service.client_identity_data_updater"
                 class="Evp\Bundle\ContisBundle\Service\ClientIdentityDataUpdater">
            <argument type="service" id="evp_contis.service.contis_user_provider"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
        </service>

        <service id="evp_contis.filesystem.sftp.statements" class="League\Flysystem\Filesystem">
            <argument type="service">
                <service class="League\Flysystem\PhpseclibV2\SftpAdapter">
                    <argument type="service">
                        <service class="League\Flysystem\PhpseclibV2\SftpConnectionProvider">
                            <argument>%evp_contis.sftp.host%</argument>
                            <argument>%evp_contis.sftp.username%</argument>
                            <argument>%evp_contis.sftp.password%</argument>
                            <argument>%evp_contis.sftp.private_key%</argument>
                            <argument>%evp_contis.sftp.passphrase%</argument>
                            <argument>%evp_contis.sftp.port%</argument>
                        </service>
                    </argument>
                    <argument type="string">%evp_contis.sftp.statements_root_dir%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.filesystem.sftp.reports" class="League\Flysystem\Filesystem">
            <argument type="service">
                <service class="League\Flysystem\PhpseclibV2\SftpAdapter">
                    <argument type="service">
                        <service class="League\Flysystem\PhpseclibV2\SftpConnectionProvider">
                            <argument>%evp_contis.sftp.host%</argument>
                            <argument>%evp_contis.sftp.username%</argument>
                            <argument>%evp_contis.sftp.password%</argument>
                            <argument>%evp_contis.sftp.private_key%</argument>
                            <argument>%evp_contis.sftp.passphrase%</argument>
                            <argument>%evp_contis.sftp.port%</argument>
                        </service>
                    </argument>
                    <argument type="string">%evp_contis.sftp.reports_root_dir%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.filesystem.sftp.cs_reports" class="League\Flysystem\Filesystem">
            <argument type="service">
                <service class="League\Flysystem\PhpseclibV2\SftpAdapter">
                    <argument type="service">
                        <service class="League\Flysystem\PhpseclibV2\SftpConnectionProvider">
                            <argument>%evp_contis.sftp.host%</argument>
                            <argument>%evp_contis.sftp.username%</argument>
                            <argument>%evp_contis.sftp.password%</argument>
                            <argument>%evp_contis.sftp.private_key%</argument>
                            <argument>%evp_contis.sftp.passphrase%</argument>
                            <argument>%evp_contis.sftp.port%</argument>
                        </service>
                    </argument>
                    <argument type="string">%evp_contis.sftp.cs_reports_root_dir%</argument>
                </service>
            </argument>
        </service>

        <service id="evp_contis.filesystem.local" class="League\Flysystem\Filesystem">
            <argument type="service">
                <service class="League\Flysystem\Local\LocalFilesystemAdapter">
                    <argument type="string">/</argument>
                </service>
            </argument>
        </service>

    </services>
</container>
