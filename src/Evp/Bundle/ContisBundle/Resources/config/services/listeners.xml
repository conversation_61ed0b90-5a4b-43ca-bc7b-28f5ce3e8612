<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>

        <service id="evp_contis.listener.contis_balance_parallel" class="Evp\Bundle\ContisBundle\Listener\ContisBalanceParallelListener">
            <tag name="kernel.event_listener" event="evp_contis.limit.changed" method="onLimitChanged" />
            <tag name="kernel.event_listener" event="evp_contis.limit.deleted" method="onLimitChanged" />
            <tag name="kernel.event_listener" event="evp_contis.card_activated" method="onCardActivated" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />

        </service>

        <service id="evp_contis.listener.card_monthly_charge" class="Evp\Bundle\ContisBundle\Listener\CardMonthlyChargeListener">
            <tag name="kernel.event_listener" event="evp_contis.card_activated" method="onCardActivated" />
            <tag name="kernel.event_listener" event="evp_contis.card_cancelled" method="onCardCancelled" />
            <tag name="kernel.event_listener" event="evp_contis.card_expired" method="onCardExpired" />

            <argument type="service" id="evp_contis.card_monthly_charge_manager" />
        </service>

        <service id="evp_contis.listener.close_account" class="Evp\Bundle\ContisBundle\Listener\CloseAccountListener">
            <tag name="kernel.event_listener" event="evp_contis.card_cancelled" method="onCardCancelled" />
            <tag name="kernel.event_listener" event="evp_contis.card_expired" method="onCardExpired" />
            <tag name="kernel.event_listener" event="evp_contis.contis_account_closed" method="onContisAccountClosed" />
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_contis.account_finalizer" />
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.listener.remote_event" class="Evp\Bundle\ContisBundle\Listener\RemoteEventListener">
            <tag name="kernel.event_listener" event="remote.gateway_contis_card_created" method="onContisCardCreated"/>
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_rest_payment_api_client"/>
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service">
                <service class="Evp\Component\Money\Money">
                    <argument>%evp_contis.card_prime_cost%</argument>
                    <argument>%evp_contis.currency%</argument>
                </service>
            </argument>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.listener.card_existence_check" class="Evp\Bundle\ContisBundle\Listener\CardExistenceCheckListener">
            <tag name="kernel.event_listener" event="evp_bank_account.account.closed" method="onAccountClosed" />

            <argument type="service" id="evp_contis.repository.card" />
        </service>

        <service id="evp_contis.listener.client_restriction" class="Evp\Bundle\ContisBundle\Listener\ClientRestrictionListener">
            <tag name="monolog.logger" channel="evp_contis.client_restrictions" />
            <tag name="kernel.event_listener" event="evp_client.restriction_created" method="onClientRestrictionCreated" />
            <tag name="kernel.event_listener" event="evp_client.restriction_deleted" method="onClientRestrictionDeleted" />

            <argument type="service" id="paysera_contis_rest_client.account_client" />
            <argument type="service" id="evp_contis.manager.transaction" />
            <argument type="service" id="evp_contis.repository.contis_account" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
            <argument type="service" id="logger" />
            <!-- set in Evp/Bundle/ContisBundle/DependencyInjection/EvpContisExtension.php -->
            <argument>%evp_contis.disable_remote_calls%</argument>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_contis.repository.card"/>
        </service>

        <service id="evp_contis.listener.card_charge_discount" class="Evp\Bundle\ContisBundle\Listener\CardChargeDiscountListener">
            <tag name="kernel.event_listener" event="evp_bank_charge.charge.status_done" method="onChargeDone" />
            <tag name="monolog.logger" channel="evp_contis.contis"/>

            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.listener.client_email_change_listener"
                 class="Evp\Bundle\ContisBundle\Listener\ClientEmailChangeListener">
            <tag name="monolog.logger" channel="evp_contis" />
            <tag name="kernel.event_listener" event="remote.user_email_registered" method="onConfirmed"/>
            <tag name="kernel.event_listener" event="remote.user_email_made_main" method="onMadeMain"/>

            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_client.user_information_provider"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.resolver.error_codes" />
        </service>

        <service id="evp_contis.listener.client_phone_change_listener"
                 class="Evp\Bundle\ContisBundle\Listener\ClientPhoneChangeListener">
            <tag name="monolog.logger" channel="evp_contis" />
            <tag name="kernel.event_listener" event="remote.user_phone_registered" method="onConfirmed"/>
            <tag name="kernel.event_listener" event="remote.user_phone_made_main" method="onMadeMain"/>

            <argument type="service" id="evp_contis.repository.card"/>
            <argument type="service" id="paysera_contis_rest_client.consumer_client"/>
            <argument type="service" id="evp_client.user_information_provider"/>
            <argument type="service" id="evp_contis.iso_country_code_provider"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_contis.resolver.error_codes" />
        </service>

        <service id="evp_contis.listener.card_status_changed" class="Evp\Bundle\ContisBundle\Listener\CardStatusChangeListener">
            <tag name="kernel.event_listener" event="evp_contis.card_status_changed" method="onStatusChanged"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="paysera_card_integration.manager.delivery_item"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_contis.listener.contis_account_status_changed" class="Evp\Bundle\ContisBundle\Listener\ContisAccountStatusChangeListener">
            <tag name="kernel.event_listener" event="evp_contis.contis_account_status_change" method="onStatusChange"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service id="evp_contis.listener.challenge_remote_event_listener"
                 class="Evp\Bundle\ContisBundle\Listener\ChallengeRemoteEventListener">
            <tag name="kernel.event_listener" event="remote.challenge_challenge_expired" method="onChallengeExpired"/>
            <tag name="kernel.event_listener" event="remote.challenge_challenge_revoked" method="onChallengeRevoked" />
            <tag name="kernel.event_listener" event="remote.challenge_challenge_unlocked" method="onChallengeUnlocked" />
            <tag name="monolog.logger" channel="evp_contis" />

            <argument type="service" id="logger"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_contis.pending_challenges_processor"/>
        </service>

        <service id="evp_bundle_contis.listener.card_xpay_tokens_status_update" class="Evp\Bundle\ContisBundle\Listener\CardXPayTokensStatusUpdateListener">
            <tag name="kernel.event_listener" event="evp_contis.card_marked_damaged" method="onCardMarkedDamaged" />
            <tag name="kernel.event_listener" event="evp_contis.card_status_changed" method="onCardStatusChanged" />

            <argument type="service" id="evp_rabbit_mq_extension.remote_job_publisher" />
        </service>

        <service id="evp_contis.listener.card_close_event"
                 class="Evp\Bundle\ContisBundle\Listener\CardCloseListener">
            <tag name="kernel.event_listener" event="transform_account_to_local" method="onCardClose" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_card.repository.card_close_operation"/>
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
