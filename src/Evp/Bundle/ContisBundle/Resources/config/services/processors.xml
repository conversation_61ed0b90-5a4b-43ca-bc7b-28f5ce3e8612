<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_contis.processor.card_close_operation"
                 class="Evp\Bundle\ContisBundle\Processor\CardCloseOperationProcessor">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\CardBundle\Entity\CardCloseOperation"/>
            <tag name="evp_accounting.partner_operation_processor"
                 class="Evp\Bundle\CardBundle\Entity\CardCloseOperation"/>

            <argument id="evp_bundle_client.service.partner_client_manager" type="service"/>
            <argument id="evp_bank_transfer.service.template_provider" type="service"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_contis_accounting.service.partner_accounting_data_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>
    </services>
</container>
