<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Tests\Processor;

use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\CardBundle\Entity\CardCloseOperation;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisAccountingBundle\Service\PartnerAccountingDataProvider;
use Evp\Component\Money\Money;
use Paysera\Bundle\PartnerBundle\Entity\Partner;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;
use Evp\Bundle\ContisBundle\Processor\CardCloseOperationProcessor;
use PHPUnit\Framework\TestCase;

class CardCloseOperationProcessorTest extends TestCase
{
    private CardCloseOperationProcessor $cardCloseOperationProcessor;
    private PartnerClientManager $partnerClientManager;
    private EntityManagerInterface $entityManager;

    private const DATE_FORMAT = 'Y-m-d H:i:s';
    private const PAYSERA_LT_COVENANTEE_ID = 1;
    private const PAYSERA_AL_COVENANTEE_ID = 2;
    private const PAYSERA_AL_CLIENT_COVENANTEE_ID = 3;

    protected function setUp(): void
    {
        $this->partnerClientManager = $this->createMock(PartnerClientManager::class);
        $templateProvider = $this->createMock(TemplateProvider::class);
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->partnerAccountingDataProvider = $this->createMock(PartnerAccountingDataProvider::class);

        $this->cardCloseOperationProcessor = new CardCloseOperationProcessor(
            $this->partnerClientManager,
            $templateProvider,
            $this->entityManager,
            $this->partnerAccountingDataProvider,
            Partner::PAYSERA_LITHUANIA,
        );

        $templateProvider->method('getTemplateForContisFundsOnWayOut')->willReturn('W2P_TRANSFER_FUNDS_ON_WAY_OUT');
        $templateProvider->method('getReceivedTransferInTroughPartner')->willReturn('RECEIVED_PAYSERA_LT_EUR');
        $templateProvider->method('getWebToPayReceived')->willReturn('W2P_RECEIVED');
    }

    /**
     * @dataProvider ltDataProvider
     */
    public function testOnDefaultPartner(
        CardCloseOperation $operation,
        array $expectedData
    ): void {
        $this->assertExpectedDataWasPersisted($expectedData);

        $this->partnerClientManager
            ->expects($this->once())
            ->method('getPartnerCodeByAccount')
            ->willReturn(Partner::PAYSERA_LITHUANIA)
        ;
        $this->partnerAccountingDataProvider
            ->expects($this->once())
            ->method('getConvenanteeId')
            ->willReturn((string)self::PAYSERA_LT_COVENANTEE_ID)
        ;

        $this->cardCloseOperationProcessor->process($operation);
    }

    /**
     * @dataProvider alDataProvider
     */
    public function testOnOtherPartner(
        CardCloseOperation $operation,
        array $expectedData
    ): void {
        $this->assertExpectedDataWasPersisted($expectedData);

        $this->partnerClientManager
            ->expects($this->once())
            ->method('getPartnerCodeByAccount')
            ->willReturn(Partner::PAYSERA_ALBANIA)
        ;
        $this->partnerAccountingDataProvider
            ->expects($this->once())
            ->method('getConvenanteeId')
            ->willReturn((string)self::PAYSERA_AL_COVENANTEE_ID)
        ;
        $this->cardCloseOperationProcessor->process($operation);
    }

    public function ltDataProvider(): array
    {
        $amountMoney = new Money('200', 'EUR');
        $date = DateTime::createFromFormat(self::DATE_FORMAT, '2023-02-01 00:00:00');
        $client = (new ClientNatural())->setCovenanteeId(self::PAYSERA_LT_COVENANTEE_ID);
        $ltAccount = (new Account())->setClient($client);

        return [
            'paysera_lt client card close' => [
                (new CardCloseOperation())
                    ->setAccount($ltAccount)
                    ->setCurrency($amountMoney->getCurrency())
                    ->setAmount($amountMoney->getAmount())
                    ->setDate(DateTimeImmutable::createFromMutable($date)),
                [
                    (new RemoteOperationRequest())
                        ->setType(RemoteOperationRequest::TYPE_OPERATION)
                        ->setReference('1')
                        ->setAmountMoney($amountMoney)
                        ->setDetails(RemoteOperationRequest::TYPE_ACCOUNT_FINALIZATION)
                        ->setCovenanteeId(self::PAYSERA_LT_COVENANTEE_ID)
                        ->setPartnerCode(Partner::PAYSERA_LITHUANIA)
                        ->setTemplateName('W2P_TRANSFER_FUNDS_ON_WAY_OUT'),
                ]
            ],
        ];
    }

    public function alDataProvider(): array
    {
        $amountMoney = new Money('200', 'EUR');
        $date = DateTime::createFromFormat(self::DATE_FORMAT, '2023-02-01 00:00:00');
        $client = (new ClientNatural())->setCovenanteeId(self::PAYSERA_AL_CLIENT_COVENANTEE_ID);
        $alAccount = (new Account())->setClient($client);

        return [
            'paysera_al client card close' => [
                (new CardCloseOperation())
                    ->setAccount($alAccount)
                    ->setCurrency($amountMoney->getCurrency())
                    ->setAmount($amountMoney->getAmount())
                    ->setDate(DateTimeImmutable::createFromMutable($date)),
                [
                    (new RemoteOperationRequest())
                        ->setType(RemoteOperationRequest::TYPE_OPERATION)
                        ->setReference('1')
                        ->setAmountMoney($amountMoney)
                        ->setDetails(RemoteOperationRequest::TYPE_ACCOUNT_FINALIZATION)
                        ->setCovenanteeId(self::PAYSERA_AL_COVENANTEE_ID)
                        ->setPartnerCode(Partner::PAYSERA_LITHUANIA)
                        ->setTemplateName('W2P_TRANSFER_FUNDS_ON_WAY_OUT'),
                    (new RemoteOperationRequest())
                        ->setType(RemoteOperationRequest::TYPE_OPERATION)
                        ->setReference('1')
                        ->setAmountMoney($amountMoney)
                        ->setDetails(RemoteOperationRequest::TYPE_ACCOUNT_FINALIZATION)
                        ->setCovenanteeId(self::PAYSERA_AL_CLIENT_COVENANTEE_ID)
                        ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                        ->setTemplateName('W2P_RECEIVED'),
                    (new RemoteOperationRequest())
                        ->setType(RemoteOperationRequest::TYPE_OPERATION)
                        ->setReference('1')
                        ->setAmountMoney($amountMoney)
                        ->setDetails(RemoteOperationRequest::TYPE_ACCOUNT_FINALIZATION)
                        ->setCovenanteeId(self::PAYSERA_AL_CLIENT_COVENANTEE_ID)
                        ->setPartnerCode(Partner::PAYSERA_ALBANIA)
                        ->setTemplateName('RECEIVED_PAYSERA_LT_EUR'),
                ]
            ]
        ];
    }

    /**
     * @param array $expectedData
     * @param int $i
     * @return void
     */
    public function assertExpectedDataWasPersisted(array $expectedData, int $i = 0): void
    {
        $this->entityManager
            ->expects($this->exactly(count($expectedData)))
            ->method('persist')
            ->with($this->callback(function ($actualEntity) use ($expectedData, &$i) {
                $this->assertInstanceOf(RemoteOperationRequest::class, $actualEntity);
                $this->assertSame($actualEntity->getType(), $expectedData[$i]->getType());
                $this->assertSame(
                    $actualEntity->getAmountMoney()->getAmount(),
                    $expectedData[$i]->getAmountMoney()->getAmount()
                );
                $this->assertSame($actualEntity->getDetails(), $expectedData[$i]->getDetails());
                $this->assertSame($actualEntity->getCovenanteeId(), $expectedData[$i]->getCovenanteeId());
                $this->assertSame($actualEntity->getPartnerCode(), $expectedData[$i]->getPartnerCode());
                $this->assertSame($actualEntity->getTemplateName(), $expectedData[$i]->getTemplateName());
                $i++;

                return true;
            }));
    }
}
