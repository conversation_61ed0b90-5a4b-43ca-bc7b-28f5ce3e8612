<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Tests\Listener;

use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\CardBundle\Entity\CardCloseOperation;
use Evp\Bundle\CardBundle\Repository\CardCloseOperationRepository;
use Evp\Component\Money\Money;
use Evp\Bundle\ContisBundle\Entity\CardCloseOperationData;
use Evp\Bundle\ContisBundle\Event\CardCloseEvent;
use Evp\Bundle\ContisBundle\Listener\CardCloseListener;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class CardCloseListenerTest extends TestCase
{
    private CardCloseListener $cardCloseListener;
    private EntityManagerInterface $entityManager;

    private const DATE_FORMAT = 'Y-m-d H:i:s';

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);

        $this->cardCloseListener = new CardCloseListener(
            $this->entityManager,
            $this->createMock(CardCloseOperationRepository::class),
            $this->createMock(LoggerInterface::class),
        );

        $this->event = $this->createMock(CardCloseEvent::class);
    }

    /**
     * @dataProvider dataProvider
     */
    public function testOnDefaultPartner(
        CardCloseOperationData $operationData,
        array $expectedData
    ): void {
        $this->event->method('getData')->willReturn($operationData);

        $this->entityManager
            ->expects($this->exactly(count($expectedData)))
            ->method('persist')
            ->with($this->callback(function ($actualEntity) use ($expectedData) {
                $this->assertInstanceOf(CardCloseOperation::class, $actualEntity);
                return true;
            }));

        $this->cardCloseListener->onCardClose($this->event);
    }

    public function dataProvider(): array
    {
        $amountMoney = new Money('200', 'EUR');
        $date = DateTime::createFromFormat(self::DATE_FORMAT, '2023-02-01 00:00:00');
        $ltAccount = (new Account());

        return [
            'paysera_lt client card close' => [
                (new CardCloseOperationData())
                    ->setAmountMoney($amountMoney)
                    ->setDate($date)
                    ->setAccount($ltAccount),
                [
                    (new CardCloseOperation())
                        ->setAccount($ltAccount)
                        ->setCurrency($amountMoney->getCurrency())
                        ->setAmount($amountMoney->getAmount())
                        ->setDate(DateTimeImmutable::createFromMutable($date)),
                ]
            ],
        ];
    }
}
