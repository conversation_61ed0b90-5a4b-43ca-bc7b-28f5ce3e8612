<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Tests\Controller;

use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\AccountRepository;
use Evp\Bundle\BankAccountBundle\Service\AccountAliasManager;
use Evp\Bundle\BankApiBundle\Controller\SoapController;
use Evp\Bundle\BankChargeBundle\Entity\Charge;
use Evp\Bundle\BankChargeBundle\Exception\ChargeProcessorException;
use Evp\Bundle\BankChargeBundle\Processor\ChargeProcessor;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManager;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPerson;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ContisBundle\ApiResponseErrors;
use Evp\Bundle\ContisBundle\Entity\Card;
use Evp\Bundle\ContisBundle\Entity\CardCharge;
use Evp\Bundle\ContisBundle\Entity\CardStatusChange;
use Evp\Bundle\ContisBundle\Entity\ContisAccount;
use Evp\Bundle\ContisBundle\Entity\ContisAccountFee;
use Evp\Bundle\ContisBundle\Entity\Limit;
use Evp\Bundle\ContisBundle\Entity\Transaction;
use Evp\Bundle\ContisBundle\Repository\CardRepository;
use Evp\Bundle\ContisBundle\Repository\CardStatusChangeRepository;
use Evp\Bundle\ContisBundle\Repository\LimitRepository;
use Evp\Bundle\ContisBundle\Service\CardHolderToCloseAccountResolver;
use Evp\Bundle\ContisBundle\Service\CardManager;
use Evp\Bundle\ContisBundle\Service\CardStateHelper;
use Evp\Bundle\ContisBundle\Service\ContisAccountManager;
use Evp\Bundle\ContisBundle\Service\ContisConsumerManager;
use Evp\Bundle\ContisBundle\Service\FailureCodeResolver;
use Evp\Bundle\ContisBundle\Service\ShipmentCodeManager;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\QuestionnaireBundle\Service\RestrictionChecker;
use Evp\Component\GatewayCommon\IssuedPaymentCard\Entity\Card as CardCommon;
use Evp\Component\GatewayCommon\IssuedPaymentCard\Entity\ChargeInfo;
use Evp\Component\GatewayCommon\IssuedPaymentCard\Entity\Limit as LimitCommon;
use Evp\Component\GatewayCommon\IssuedPaymentCard\Entity\SecurityCode;
use Evp\Component\GatewayCommon\IssuedPaymentCard\Entity\ShippingAddress;
use Evp\Component\GatewayCommon\IssuedPaymentCard\FailureCodes;
use Evp\Component\Money\Money;
use Evp\Component\SensitiveValue\Entity\SensitiveValue;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserCommon\UserService\Entity\Service;
use Evp\Component\UserCommon\UserService\Entity\UserServices;
use Evp\Component\UserRestClient\IdentificationDocument\IdentityReportClient;
use Evp\Component\UserRestClient\Service\ServiceClient;
use Evp\Component\UserRestClient\User\UserClient;
use Evp\Component\UserRestClient\UserRestFactory;
use Paysera\Bundle\CardIntegrationBundle\DeliveryTypes;
use Paysera\Bundle\ContisRestClientBundle\Entity\AccountBalanceResult;
use Paysera\Bundle\ContisRestClientBundle\Entity\Card\CardResult;
use Paysera\Bundle\ContisRestClientBundle\Entity\Card\SensitiveInfo;
use Paysera\Bundle\ContisRestClientBundle\Entity\Consumer\ConsumerResult;
use Paysera\Bundle\ContisRestClientBundle\ErrorCodes;
use Paysera\Bundle\ContisRestClientBundle\Exception\ContisClientException as ContisRestClientException;
use Paysera\Bundle\ContisRestClientBundle\Service\AccountClient;
use Paysera\Bundle\ContisRestClientBundle\Service\CardClient;
use Paysera\Bundle\ContisRestClientBundle\Service\ConsumerClient;
use Paysera\Bundle\RestBundle\Exception\ApiException;
use Paysera\Bundle\TransferSurveillanceBundle\Entity\TransferInspection;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferInspectionProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Client;
use Symfony\Component\HttpFoundation\Response;

class CardControllerTest extends CardControllerBaseTestCase
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * @var CardStateHelper
     */
    private $cardStateHelper;

    /**
     * @var AccountClient|MockObject
     */
    private $contisRestAccountClient;

    /**
     * @var ConsumerClient|MockObject
     */
    private $contisRestConsumerClient;

    /**
     * @var CardHolderToCloseAccountResolver
     */
    private $cardHolderToCloseAccountResolver;


    /**
     * @var TransferInspectionProvider
     */
    private $transferInspectionProvider;

    /**
     * @var LimitRepository
     */
    private $limitRepository;

    /**
     * @var CardRepository
     */
    private $cardRepository;

    /**
     * @var AccountRepository
     */
    private $accountRepository;

    /**
     * @var PermissionManager
     */
    private $permissionManager;

    /**
     * @var RestrictionChecker
     */
    private $restrictionChecker;

    /**
     * @var CardStatusChangeRepository
     */
    private $cardStatusChangeRepository;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->contisRestAccountClient = $this->createMock(AccountClient::class);
        $this->contisRestConsumerClient = $this->createMock(ConsumerClient::class);
        $this->cardHolderToCloseAccountResolver = $this->createMock(CardHolderToCloseAccountResolver::class);

        $this->client->getContainer()->set(
            'paysera_contis_rest_client.account_client',
            $this->contisRestAccountClient
        );
        $this->client->getContainer()->set(
            'paysera_contis_rest_client.consumer_client',
            $this->contisRestConsumerClient
        );
        $this->client->getContainer()->set(
            'evp_contis.card_holder_to_close_account_resolver',
            $this->cardHolderToCloseAccountResolver
        );
        $this->client->getContainer()->set(
            'evp_bank_account.account_alias_manager',
            $this->createMock(AccountAliasManager::class)
        );

        $this->restrictionChecker = $this->client->getContainer()->get('evp_client_rest.client_restriction_checker');
        $this->permissionManager = $this->client->getContainer()->get('evp_bank_permission.permission_manager');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->transferInspectionProvider = $this->getContainer()->get(
            'paysera_transfer_surveillance.transfer_inspection_provider'
        );
        $this->limitRepository = $this->client->getContainer()->get('evp_contis.repository.limit');
        $this->cardRepository = $this->client->getContainer()->get('evp_contis.repository.card');
        $this->accountRepository = $this->client->getContainer()->get('evp_bank_account.repository.account');
        $this->restrictionChecker = $this->client->getContainer()->get('evp_client_rest.client_restriction_checker');
        $this->cardStatusChangeRepository = $this->client
            ->getContainer()
            ->get('evp_contis.repository.card_status_change')
        ;
    }

    protected function createClientWithNewDatabase($dataFixturesDir = null)
    {
        if ($dataFixturesDir === null) {
            $dataFixturesDir = __DIR__ . '/../DataFixtures/Common';
        }
        return parent::createClientWithNewDatabase($dataFixturesDir);
    }

    public function testAddCard_no_charge_info_provided_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setShippingAddress($shippingAddress);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals('Charge account not provided', $result['error_description']);
    }

    public function testAddCard_charge_account_not_found_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101_not_existing');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::NOT_FOUND, $result['error']);
        $this->assertEquals('Provided account for commission charge was not found', $result['error_description']);
    }

    public function testAddCard_shipping_address_not_provided_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $cardCommon->setChargeInfo($chargeInfo);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals('Shipping address not provided', $result['error_description']);
    }

    public function testAddCard_no_permission_for_charge_account_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000102'); //client has no permission for this account
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
        $this->assertEquals('Client has no administrate permission on account', $result['error_description']);
    }

    public function testAddCard_card_owner_not_found_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1111); //not existing
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals(
            'Client not found or type is not ClientNatural by given card_owner_id',
            $result['error_description']
        );
    }

    public function testAddCard_provided_card_account_does_not_exist_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103_not_existing');

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::NOT_FOUND, $result['error']);
    }

    public function testAddCard_provided_card_account_is_of_wrong_type_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP6410001000101');

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::NOT_FOUND, $result['error']);
        $this->assertEquals('Provided account of type \'contis\' not found', $result['error_description']);
    }

    public function testAddCard_no_permissions_to_provided_card_account_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP6410001000104');

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
        $this->assertEquals('Client has no administrate permission on account', $result['error_description']);
    }

    public function testAddCard_charge_failure_returns_error_and_no_card_and_charge_created()
    {
        $this->mockCardOwnerInformation('lt', 19);
        $this->mockGetUserServices();

        $chargeProcessor = $this->getMockBuilder(ChargeProcessor::class)
            ->disableOriginalConstructor()
            ->getMock()
        ;
        $chargeProcessor->expects($this->any())
            ->method('processCharge')
            ->will($this->throwException(new ChargeProcessorException()))
        ;
        $this->client->getContainer()->set('evp_bank_charge.processor.charge', $chargeProcessor);

        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));

        $cardCountBefore = $this->getCardCount();
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals('Unable to charge provided account for card order', $result['error_description']);

        $cardCountAfter = $this->getCardCount();
        $this->assertEquals($cardCountBefore, $cardCountAfter);
        $charge = $this->findLastCharge();
        $this->assertNull($charge);
    }

    public function testAddCard_charge_missing_funds_returns_error_and_no_card_and_charge_created()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('bg', 19);
        $this->mockGetUserServices();

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));

        $cardCountBefore = $this->getCardCount();
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('not_enough_funds', $result['error']);
        $this->assertEquals('Not enough funds in charge account', $result['error_description']);

        $cardCountAfter = $this->getCardCount();
        $this->assertEquals($cardCountBefore, $cardCountAfter);
        $charge = $this->findLastCharge();
        $this->assertNull($charge);
    }

    public function testAddCard_if_no_account_owner_and_no_account_owner_provided_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals(
            'Account number or owner to create account for must be provided',
            $result['error_description']
        );
    }

    public function testAddCard_if_account_owner_does_not_exist_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(1111); //not existing

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals('Client not found by given account_owner_id', $result['error_description']);
    }

    public function testAddCard_when_card_count_limit_reached_then_return_specific_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 20);

        $account = $this->findAccountByNumber('EVP9610001000103');
        for ($i = 0; $i < 10; $i++) {
            $card = new Card();
            $card->setStatus(Card::STATUS_ORDERED);
            $card->setAccount($account);
            $card->setMain(false);
            $this->client->getContainer()->get('doctrine.orm.entity_manager')->persist($card);
        }
        $this->entityManager->flush();

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $cardCountBefore = $this->getCardCount();
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('too_many_cards', $result['error']);

        $cardCountAfter = $this->getCardCount();
        $this->assertEquals($cardCountBefore, $cardCountAfter);
        $charge = $this->findLastCharge();
        $this->assertNull($charge);
    }

    public function testAddCard_when_contis_account_provided_then_card_created_for_this_account()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 14); //min 13 for additional CardHolder
        $this->mockGetUserServices();

        $this->fillAccount('EVP6410001000101', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));

        $accountCountBefore = $this->getAccountCount();
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('EVP9610001000103', $result['account_number']);

        $accountCountAfter = $this->getAccountCount();
        $this->assertEquals($accountCountBefore, $accountCountAfter); //check that account was not created
        $card = $this->findLastCard();
        $this->assertNotNull($card);
        $this->assertEquals(Card::STATUS_NEW, $card->getStatus());
        $this->assertEquals('EVP9610001000103', $card->getAccount()->getNumber());
        $this->assertNotNull($card->getShippingAddress());
        $this->assertNotNull($this->findLastCharge());
        $this->assertFalse($card->isMain());
        $charge = $this->findLastCharge();
        $this->assertEquals('EVP6410001000101', $charge->getAccount()->getNumber());
        $this->assertEquals(Charge::STATUS_DONE, $charge->getStatus());

        $this->assertEquals(
            $cardCommon->getIssuePrice()->add($cardCommon->getDeliveryPrice()),
            $charge->getAmountMoney()
        );

    }

    public function testAddCard_when_account_owner_provided_then_card_and_account_created()
    {
        $this->mockGetCurrentClientByUserId(2);
        $this->mockCardOwnerInformation('lt', 18, 1); //min 18 years and 1 day old for main CardHolder
        $this->mockGetUserServices();
        $this->mockUserRestFactory();

        $this->fillAccount('EVP6310001000301', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6310001000301');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(2);
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));
        $cardCommon->setVisualType('blue_orange_v1');

        $accountCountBefore = $this->getAccountCount();
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->entityManager->clear();

        $accountCountAfter = $this->getAccountCount();
        //check that account was created with card
        $this->assertEquals($accountCountAfter - $accountCountBefore, 1);
        /** @var Account $lastAccount */
        $lastAccount = $this->findLastEntity('EvpBankAccountBundle:Account');
        $this->assertEquals($result['account_number'], $lastAccount->getNumber());
        $this->assertEquals('contis', $lastAccount->getType());
        $charge = $this->findLastCharge();
        $this->assertEquals('EVP6310001000301', $charge->getAccount()->getNumber());
        $this->assertEquals(Charge::STATUS_DONE, $charge->getStatus());

        $this->assertEquals(
            $cardCommon->getIssuePrice()->add($cardCommon->getDeliveryPrice()),
            $charge->getAmountMoney()
        );
        $this->assertTrue(
            $this->permissionManager->hasAdministratePermission(
                $this->restrictionChecker->getCurrentClient(),
                $lastAccount
            )
        );
        $card = $this->findLastCard();
        $this->assertNotNull($card);
        $this->assertEquals(Card::STATUS_NEW, $card->getStatus());
        $this->assertTrue($card->isMain());
    }

    public function testAddCard_from_legal_account_contis_account_exists()
    {
        $this->mockGetCurrentClientByUserId(12);
        $this->mockCardOwnerInformation('lt', 17); //min 16 for additional CardHolder for Legals
        $this->mockGetUserServices();

        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000201');

        $shippingAddress = (new ShippingAddress())
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(15);
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setAccountNumber('EVP7910001000201');
        $cardCommon->setAccountOwnerId('11');
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(3, 'EUR'));

        $this->sendAddCardRequest($cardCommon);

        $this->assertEquals(
            $cardCommon->getIssuePrice()->add($cardCommon->getDeliveryPrice()),
            $this->findLastCharge()->getAmountMoney()
        );
    }

    public function testAddCard_from_legal_account_default_country()
    {
        $this->mockGetCurrentClientByUserId(12);
        $this->mockCardOwnerInformation('bg', 24);
        $this->mockGetUserServices();

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(12);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000201');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000112');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(7, 'EUR'));

        $this->sendAddCardRequest($cardCommon);

        $this->assertEquals(
            $cardCommon->getIssuePrice()->add($cardCommon->getDeliveryPrice()),
            $this->findLastCharge()->getAmountMoney()
        );
    }

    public function testAddCard_when_contis_account_provided_and_card_owner_too_young_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->fillAccount('EVP6410001000101', new Money(100, 'EUR'));

        $this->mockCardOwnerInformation('lt', 12); //min 13 for additional CardHolder

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('card_owner_age_violation', $result['error']);
    }

    public function testAddCard_when_account_owner_provided_and_card_owner_too_young_then_error()
    {
        $this->mockGetCurrentClientByUserId(2);
        $this->mockCardOwnerInformation('lt', 16); //min 16 for main CardHolder

        $this->fillAccount('EVP6310001000301', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6310001000301');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(2);
        $cardCommon->setVisualType('blue_orange_v1')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('card_owner_age_violation', $result['error']);
    }

    public function testAddCard_if_account_owner_is_different_than_card_owner_for_first_card_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(1);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::FORBIDDEN, $result['error']);
        $this->assertEquals(
            'Card owner and account owner must be the same when creating first card for ClientNatural',
            $result['error_description']
        );
    }

    public function testAddCard_if_identification_level_not_satisfied_for__own_issued_card__then_error()
    {
        $this->mockGetCurrentClientByUserId(3); //user without code (unidentified)

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(3);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP7910001000302');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(3);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::FORBIDDEN, $result['error']);
        $this->assertEquals(
            'Permission own_issued_card not granted. Required level: basic_identified.',
            $result['error_description']
        );
    }

    public function testAddCard_if_identification_level_not_satisfied_for_own_issued_card_account__then_error()
    {
        $this->markTestSkipped(
            'Cannot make working test at current implementation of identification level resolution '
            . 'at evpbank, because own_issued_card is thrown anyway at first, '
            . 'and also ClientNatural can create card account only for himself'
        );
    }

    public function testAddCard_if_too_many_cards_created_for_account_then_error()
    {
        $clientIdsOffset = 100;
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 19);
        $this->mockGetUserServices();

        $this->fillAccount('EVP6410001000101', new Money(500, 'EUR'));

        for ($i = 0; $i < 10; $i++) {
            $this->createNaturalClient($clientIdsOffset + $i);
        }

        $cardCommon = new CardCommon();
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));

        //1 card already exists
        $result = [];
        for ($i = 0; $i < 10; $i++) {
            $cardCommon->setCardOwnerId($clientIdsOffset + $i);
            $this->sendAddCardRequest($cardCommon);
            $result = json_decode($this->client->getResponse()->getContent(), true);
            if ($i < 4) {
                $this->assertEquals(200, $this->client->getResponse()->getStatusCode()); //first 4 cards created
            }
        }

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode()); //cannot create 6th card
        $this->assertEquals('too_many_cards', $result['error']);
    }

    public function testAddCard_if_card_with_this_client_already_exists_for_this_account_then_error()
    {
        $card = $this->findCard(7);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $this->mockGetCurrentClientByUserId(14);
        $this->mockCardOwnerInformation('bg', 19);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(15);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000114');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('BG')
            ->setCity('Sofia')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP7910001000314');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('duplicate_card_owner', $result['error']);
    }

    public function testAddCard_if_not_activated_main_card_exists_for_that_account_then_error()
    {
        $card = $this->findCard(1);
        $card->setStatus(Card::STATUS_ORDERED);
        $this->entityManager->flush();

        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 19);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('main_card_not_activated', $result['error']);
    }

    public function testAddCard_if_expired_card_without_replacement_exists_for_that_account_then_error()
    {
        $card = $this->findCard(1);
        $card->setStatus(Card::STATUS_EXPIRED);
        $this->entityManager->flush();

        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 19);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('expired_main_card_not_actively_replaced', $result['error']);
    }

    public function testAddCard_if_contis_account_in_frozen_status_then_error()
    {
        $card = $this->findCard(1);
        $card->setStatus(Card::STATUS_ACTIVATED);

        $contisAccountManager = $this->getContainer()->get('evp_contis.account_manager');
        $contisAccount = $contisAccountManager->createContisAccount(
            $card->getAccount(),
            'contis_account_number'
        );
        $contisAccount->setStatus(ContisAccount::STATUS_FROZEN);

        $this->entityManager->flush();

        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 19);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103')
            ->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR)
        ;

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('card_contis_account_wrong_status', $result['error']);
    }

    public function testAddCard_when_too_many_card_accounts_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->fillAccount('EVP6310001000301', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(1);

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_REQUEST, $result['error']);
        $this->assertEquals('Cannot create card account, max account count reached', $result['error_description']);
    }

    /**
     * @dataProvider provider_testAddCard_invalid_shipping_country
     *
     * @param string $countryCode
     * @param mixed $expectedResponseStatus
     * @param string $expectedErrorCode
     */
    public function testAddCard_invalid_shipping_country(
        string $countryCode,
        $expectedResponseStatus,
        $expectedErrorCode
    ) {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('bg', 14); // min 13 for additional CardHolder
        $this->mockGetUserServices();

        $this->fillAccount('EVP6410001000101', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry($countryCode)
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals($expectedResponseStatus, $this->client->getResponse()->getStatusCode());
        $this->assertEquals($expectedErrorCode, $result['error']);
    }

    public function provider_testAddCard_invalid_shipping_country()
    {
        return [
            ['^^', 400, ApiResponseErrors::CARD_ORDER_INVALID_DELIVERY_TYPE_COUNTRY_COMBINATION],
            ['GG', 400, 'card_delivery_address_not_in_eea'],
        ];
    }

    /**
     * @dataProvider providerAddCardInvalidDeliveryType
     * @param string $expectedError
     * @param string|null $deliveryType
     * @return void
     */
    public function testAddCardInvalidDeliveryType(string $expectedError, ?string $deliveryType): void
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('bg', 14);
        $this->mockGetUserServices();

        $this->fillAccount('EVP6410001000101', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType($deliveryType);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals($expectedError, $result['error']);
    }

    public function providerAddCardInvalidDeliveryType(): array
    {
        return [
            'test with invalid delivery type' => [
                'expectedError' => ApiResponseErrors::DELIVERY_TYPE_INVALID,
                'deliveryType' => DeliveryTypes::DELIVERY_TYPE_TNT,
            ],
            'test with delivery type null' => [
                'expectedError' => ApiResponseErrors::DELIVERY_TYPE_INVALID,
                'deliveryType' => null,
            ],
        ];
    }

    public function testAddCard_if_current_natural_client_has_no_permission_to_create_card_account_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(1);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setAccountOwnerId(
            11
        ); //current client is not related (cannot administrate main account) to this client

        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights_to_create_account_for_account_owner', $result['error']);
        $this->assertEquals('Insufficient permissions to create an account.', $result['error_description']);
    }

    public function testGetCard_card_returned_if_exists()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 1;
        $this->sendGetCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals($cardId, $result['id']);
        $this->assertEquals('EVP9610001000103', $result['account_number']);
    }

    public function testGetCard_if_cards_does_not_exist_then_error_returned()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardRequest(99);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::NOT_FOUND, $result['error']);
        $this->assertEquals('Card not found', $result['error_description']);
    }

    public function testGetCard_if_no_permission_for_card_account_then_error_returned()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardRequest(12);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('forbidden', $result['error']);
        $this->assertEquals('Access Denied.', $result['error_description']);
    }

    public function testGetCards_card_list_returned_by_account_numbers()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardsRequest(['account_numbers' => ['EVP9610001000103']]);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertNotEmpty($result['cards']);
        $this->assertCount(1, $result['cards']);
        $this->assertEquals('EVP9610001000103', $result['cards'][0]['account_number']);
    }

    public function testGetCards_card_list_returned_by_card_owner()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardsRequest(['card_owner_id' => '1']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertNotEmpty($result['cards']);
        $this->assertEquals(3, count($result['cards']));
        $this->assertEquals('EVP9610001000103', $result['cards'][0]['account_number']);
        $this->assertEquals('EVP6410001000106', $result['cards'][1]['account_number']);
        $this->assertEquals('EVP6410001000106', $result['cards'][2]['account_number']);
    }

    public function testGetCards_card_list_returned_by_account_owner()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardsRequest(['card_owner_id' => '1', 'statuses' => ['new']]);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertNotEmpty($result['cards']);
        $this->assertCount(1, $result['cards']);
        $this->assertEquals('EVP9610001000103', $result['cards'][0]['account_number']);
    }

    public function testGetCards_filtered_by_status()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardsRequest(['card_owner_id' => '1', 'statuses' => ['not_existing']]);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEmpty($result['cards']);
        $this->assertCount(0, $result['cards']);
    }

    public function testGetCards_if_nothing_provided_then_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetCardsRequest(['statuses' => 'new']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_PARAMETERS, $result['error']);
    }

    public function testGetCards_returned_only_cards_that_client_has_permissions_to_also_required_fields_are_returned()
    {
        $this->mockGetCurrentClientByUserId(4);

        $this->sendGetCardsRequest(
            [
                'account_numbers' => ['EVP9610001000103', 'EVP6410001000106', 'EVP9510001000109'],
                'fields' => 'cards.account.client,*',
            ]
        );
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertCount(1, $result['cards']);
        $this->assertEquals('EVP9510001000109', $result['cards'][0]['account_number']);
        $this->assertNotEmpty($result['cards'][0]['account']['client']);
    }

    public function testGetShippingAddress_returns_shipping_address()
    {
        $this->mockGetCurrentClientByUserId(1);

        $this->sendGetShippingAddressRequest(['account_number' => 'EVP9610001000103']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('shipping address 1', $result['address']);
    }

    public function testGetShippingAddress_returns_shipping_address_client_legal()
    {
        $this->mockGetCurrentClientByUserId(11);

        $this->sendGetShippingAddressRequest(['account_number' => 'EVP7910001000201']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('shipping address 3', $result['address']);
    }

    public function testGetShippingAddress_if_access_denied_then_error()
    {
        $this->mockGetCurrentClientByUserId(2);

        $this->sendGetShippingAddressRequest(['account_number' => 'EVP9610001000103']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
    }

    public function testDeactivateCard_if_no_access_to_card_then_error_and_status_not_changed()
    {
        $this->mockGetCurrentClientByUserId(2);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $this->sendCardDeactivationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('forbidden', $result['error']);

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testDeactivateCard_if_card_status_not_equal_activated_or_deactivated_then_error_and_status_not_changed()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ORDERED);
        $this->entityManager->flush();

        $this->sendCardDeactivationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(409, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_STATE, $result['error']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ORDERED, $card->getStatus());
    }

    public function testDeactivateCard_if_card_status_activated_then_card_deactivated()
    {
        $this->mockGetCurrentClientByUserId(1);

        $contisRestCardClient = $this->createMock(CardClient::class);
        $contisRestCardClient->expects($this->once())
            ->method('setAsBlocked')
        ;

        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestCardClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_DEACTIVATED);
        $this->entityManager->flush();

        $this->sendCardDeactivationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_DEACTIVATED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_DEACTIVATED, $card->getStatus());

        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $this->sendCardDeactivationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_DEACTIVATED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_DEACTIVATED, $card->getStatus());
    }

    /**
     * @dataProvider providerForDeactivateCard_if_card_status_activated_but_change_status_request_is_not_valid
     *
     * @param int $expectedErrorCode
     * @param string $expectedErrorMessage
     * @param CardResult $cardInfo
     */
    public function testDeactivateCard_if_card_status_activated_but_change_status_request_is_not_valid(
        int $expectedErrorCode,
        string $expectedErrorMessage,
        CardResult $cardInfo
    ) {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $cardManager = $this->createMock(CardManager::class);
        $cardManager->expects($this->once())
            ->method('deactivateCard')
            ->willThrowException(
                new ContisRestClientException("Request to change the status of new card is not valid.", 41)
            )
        ;
        $cardManager->expects($this->once())
            ->method('getCardInfoAtContis')
            ->willReturn($cardInfo)
        ;

        $this->client->getContainer()->set('evp_contis.manager.card', $cardManager);

        $this->sendCardDeactivationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals($expectedErrorCode, $this->client->getResponse()->getStatusCode());
        $this->assertEquals($expectedErrorMessage, $result['error']);
    }

    /**
     * @return mixed[]
     */
    public function providerForDeactivateCard_if_card_status_activated_but_change_status_request_is_not_valid()
    {
        return [
            'ChangeStatusFailsWithCardStatusCVV2TriesExceed' => [
                'expectedErrorCode' => 400,
                'expectedErrorMessage' => FailureCodes::CVV2_TRIES_EXCEEDED,
                'cardInfo' => (new CardResult())
                    ->setCardStatus(CardResult::STATUS_CVV_TRIES_EXCEEDED),
            ],
            'ChangeStatusFailsWithCardStatusPinTriesExceed' => [
                'expectedErrorCode' => 400,
                'expectedErrorMessage' => FailureCodes::PIN_TRIES_EXCEEDED,
                'cardInfo' => (new CardResult())
                    ->setCardStatus(CardResult::STATUS_PIN_TRIES_EXCEEDED),
            ],
        ];
    }

    public function testCancelCard_if_no_access_to_card_then_error_and_status_not_changed()
    {
        $this->mockGetCurrentClientByUserId(2);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $this->sendCardCancellationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('forbidden', $result['error']);

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testCancelCard_if_card_cancelled_then_error_and_status_not_changed()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_CANCELLED);
        $this->entityManager->flush();

        $this->sendCardCancellationRequest($cardId);

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_CANCELLED, $card->getStatus());
    }

    public function testCancelMainCardIfAccountHasAdditionalCards()
    {
        $this->mockGetCurrentClientByUserId(17);

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('getCard')
            ->willReturn(CardResult::create()->setCardStatus(CardResult::STATUS_NORMAL))
        ;

        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 10;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();

        $this->sendCardCancellationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(
            ApiResponseErrors::ADDITIONAL_CARD_EXISTS,
            $result['error']
        );
        $this->assertEquals(
            'Contis main card can not be cancelled, account has additional cards.',
            $result['error_description']
        );

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testCancelCard_if_card_status_activated_then_card_cancelled_and_funds_withdrawn_from_contis()
    {
        $this->mockGetCurrentClientByUserId(1);

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->exactly(2))
            ->method('getCard')
            ->willReturn(
                CardResult::create()
                    ->setCardStatus(CardResult::STATUS_NORMAL)
                    ->setIsDamaged(false)
            )
        ;

        $contisRestClient->expects($this->once())
            ->method('cancel')
        ;

        $this->contisRestConsumerClient->expects($this->once())
            ->method('getById')
            ->willReturn(
                (new ConsumerResult())->setStatus(ConsumerResult::STATUS_LOCKED_OUT)->setConsumerId(1)
            )
        ;

        $this->contisRestConsumerClient->expects($this->once())->method('setAsLockout');

        $consumerResult = (new ConsumerResult())->setStatus(ConsumerResult::STATUS_ACTIVE)->setConsumerId(1);
        $this->cardHolderToCloseAccountResolver->expects($this->any())
            ->method('resolveCardHolderToCloseAccount')
            ->willReturn($consumerResult)
        ;

        $this->contisRestAccountClient->expects($this->once())
            ->method('unloadConsumerAccount')
            ->willReturn(9999)
        ;

        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);
        $this->cardStateHelper = $this->getContainer()->get('evp_contis.card_state_helper');

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_CANCELLED);

        $contisAccountManager = $this->getContainer()->get('evp_contis.account_manager');
        $contisAccountManager->createContisAccount(
            $card->getAccount(),
            'contis_account_number'
        );

        $this->entityManager->flush();

        $this->sendCardCancellationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_CANCELLED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_CANCELLED, $card->getStatus());

        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();
        $availableBalance = new Money(100, 'EUR');

        $accountBalanceResult = (new AccountBalanceResult())
            ->setAvailableBalance($availableBalance)
            ->setDebitHoldBalance(Money::createZero('EUR'))
            ->setAccountStatus(AccountBalanceResult::STATUS_LIVE)
        ;
        $this->contisRestAccountClient->expects($this->any())
            ->method('getBalance')
            ->willReturn($accountBalanceResult)
        ;

        $this->sendCardCancellationRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_CANCELLED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_CANCELLED, $card->getStatus());
        //funds withdrawn, because all cards are cancelled/expired:
        /** @var Transaction $transaction */
        $transaction = $this->findLastEntity('EvpContisBundle:Transaction');
        $this->assertEquals($availableBalance, $transaction->getAmountMoney());
        $this->assertEquals(Transaction::STATUS_DONE, $transaction->getStatus());
        $this->assertEquals(Transaction::TYPE_WITHDRAWAL, $transaction->getType());

        if ($this->cardStateHelper->isCardChargeableWhenCancelled($card)) {
            $charge = $this->findLastCharge();
            $this->assertEquals(
                new Money(5, 'EUR'),
                $charge->getAmountMoney()
            );
        }
    }

    public function testReplaceLostCard()
    {
        $this->mockGetCurrentClientByUserId(1);

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->exactly(2))
            ->method('getCard')
            ->willReturn(
                CardResult::create()
                    ->setCardStatus(CardResult::STATUS_NORMAL)
                    ->setIsDamaged(false)
            )
        ;

        $contisRestClient->expects($this->once())
            ->method('cancel')
        ;

        $this->contisRestConsumerClient->expects($this->once())
            ->method('getById')
            ->willReturn(
                (new ConsumerResult())->setStatus(ConsumerResult::STATUS_LOCKED_OUT)->setConsumerId(1)
            )
        ;

        $this->contisRestConsumerClient->expects($this->once())->method('setAsLockout');
        $consumerResult = (new ConsumerResult())->setStatus(ConsumerResult::STATUS_ACTIVE)->setConsumerId(1);

        $this->contisRestAccountClient->expects($this->once())
            ->method('unloadConsumerAccount')
            ->willReturn(9999)
        ;

        $this->cardHolderToCloseAccountResolver->expects($this->any())
            ->method('resolveCardHolderToCloseAccount')
            ->willReturn($consumerResult)
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_CANCELLED);

        $contisAccountManager = $this->getContainer()->get('evp_contis.account_manager');
        $contisAccountManager->createContisAccount(
            $card->getAccount(),
            'contis_account_number'
        );

        $this->entityManager->flush();

        $this->sendReplaceLostCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_CANCELLED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_CANCELLED, $card->getStatus());

        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_ACTIVATED);
        $this->entityManager->flush();
        $availableBalance = new Money(100, 'EUR');
        $accountBalanceResult = (new AccountBalanceResult())
            ->setAvailableBalance($availableBalance)
            ->setDebitHoldBalance(Money::createZero('EUR'))
            ->setAccountStatus(AccountBalanceResult::STATUS_LIVE)
        ;
        $this->contisRestAccountClient->expects($this->any())
            ->method('getBalance')
            ->willReturn($accountBalanceResult)
        ;

        $this->sendReplaceLostCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_CANCELLED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_CANCELLED, $card->getStatus());
        //funds withdrawn, because all cards are cancelled/expired:
        /** @var Transaction $transaction */
        $transaction = $this->findLastEntity('EvpContisBundle:Transaction');
        $this->assertEquals($availableBalance, $transaction->getAmountMoney());
        $this->assertEquals(Transaction::STATUS_DONE, $transaction->getStatus());
        $this->assertEquals(Transaction::TYPE_WITHDRAWAL, $transaction->getType());

        $charge = $this->findLastCharge();
        $this->assertNull($charge);

        /** @var CardStatusChange $cardStatusHistory */
        $cardStatusHistory = $this->cardStatusChangeRepository->findOneByCard($card);
        $this->assertEquals(CardStatusChange::REASON_CANCEL_REORDER_CARD, $cardStatusHistory->getReason());
    }

    public function testEnableCard_do_not_allow_to_enable_permanently_disabled_card()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_CANCELLED);
        $this->entityManager->flush();

        $this->sendEnableCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(409, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('invalid_state', $result['error']);

        $charge = $this->findLastCharge();
        $this->assertNull($charge);
    }

    public function testEnableCard_enables_card_and_creates_charges()
    {
        $this->mockGetCurrentClientByUserId(1);

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('getCard')
            ->willReturn(CardResult::create()->setCardStatus(CardResult::STATUS_BLOCK))
        ;
        $contisRestClient->expects($this->once())
            ->method('setAsNormal')
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_DEACTIVATED);
        $this->entityManager->flush();

        $contisAccount = (new ContisAccount())
            ->setAccount($card->getAccount())
            ->setAccountNumber('test123')
            ->setStatus(ContisAccount::STATUS_LIVE)
            ->setBalance(Money::create('1', 'EUR'))
            ->setReservedBalance(Money::createZero('EUR'))
        ;
        $this->entityManager->persist($contisAccount);
        $this->entityManager->flush();

        $this->sendEnableCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_ACTIVATED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testEnableCard_enables_card_card_already_active_at_contis()
    {
        $this->mockGetCurrentClientByUserId(1);

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('getCard')
            ->willReturn(CardResult::create()->setCardStatus(CardResult::STATUS_NORMAL))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_DEACTIVATED);
        $this->entityManager->flush();

        $contisAccount = (new ContisAccount())
            ->setAccount($card->getAccount())
            ->setAccountNumber('test123')
            ->setStatus(ContisAccount::STATUS_LIVE)
            ->setBalance(Money::create('1', 'EUR'))
            ->setReservedBalance(Money::createZero('EUR'))
        ;
        $this->entityManager->persist($contisAccount);
        $this->entityManager->flush();

        $this->sendEnableCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_ACTIVATED, $result['status']);
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testEnableCardWithNeagativeBalanceAccountThrowsException()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $cardId = 3;
        $card = $this->findCard($cardId);
        $card->setStatus(Card::STATUS_DEACTIVATED);
        $this->entityManager->flush();
        $this->assertEquals(Card::STATUS_DEACTIVATED, $card->getStatus());

        $contisAccount = (new ContisAccount())
            ->setAccount($card->getAccount())
            ->setAccountNumber('test123')
            ->setStatus(ContisAccount::STATUS_LIVE)
            ->setBalance(Money::createZero('EUR')->sub(Money::create('1', 'EUR')))
            ->setReservedBalance(Money::createZero('EUR'))
        ;
        $this->entityManager->persist($contisAccount);
        $this->entityManager->flush();

        $this->sendEnableCardRequest($cardId);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(
            ApiResponseErrors::CARD_ACTIVATION_NOT_ALLOWED_DUE_TO_NEGATIVE_ACCOUNT_BALANCE,
            $result['error']
        );

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_DEACTIVATED, $card->getStatus());
    }

    public function testRetrievePin_wrong_client_cannot_retrieve_pin()
    {
        $this->mockGetCurrentClientByUserId(2);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2(new SensitiveValue('123'));

        $this->sendRetrievePinRequest(1, $securityCode);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('forbidden', $result['error']);
    }

    public function testRetrievePin_if_card_status_new_then_invalid_state_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2(new SensitiveValue('123'));

        $this->sendRetrievePinRequest(1, $securityCode);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(409, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('invalid_state', $result['error']);
    }

    public function testRetrievePin_wrong_security_code_cannot_retrieve_pin()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('viewPin')
            ->will($this->throwException(new ContisRestClientException('CVV2 is not valid.', 315)))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2(new SensitiveValue('123'));

        $this->sendRetrievePinRequest(3, $securityCode);

        $content = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertSame(FailureCodes::INVALID_CVV2, $content['error']);
    }

    public function testRetrievePin_if_pin_retrieval_blocked_at_contis_but_card_holder_status_ok_corresponding_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('viewPin')
            ->willThrowException(new ContisRestClientException(
                $this->client->getContainer()->getParameter(
                    'evp_contis.failure_code_holder_status_ok_cvv2_tries_exceeded'
                ),
                6119
            ))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2(new SensitiveValue('123'));

        $this->sendRetrievePinRequest(3, $securityCode);

        $content = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertSame(FailureCodes::CVV2_TRIES_EXCEEDED, $content['error']);
    }

    public function testRetrievePin_if_pin_retrieval_blocked_at_contis_then_corresponding_error()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('viewPin')
            ->willThrowException(new ContisRestClientException(
                $this->client->getContainer()->getParameter(
                    'evp_contis.failure_code_holder_status_ok_cvv2_tries_exceeded'
                ),
                6119
            ))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2(new SensitiveValue('123'));

        $this->sendRetrievePinRequest(3, $securityCode);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('cvv2_tries_exceeded', $result['error']);
    }

    /**
     * @dataProvider retrievePinSuccessDataProvider
     * @param SensitiveValue $cvv2Value
     */
    public function testRetrievePinSuccess(SensitiveValue $cvv2Value)
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->getMockBuilder(CardClient::class)->disableOriginalConstructor()->getMock();
        $contisRestClient->expects($this->once())
            ->method('viewPin')
            ->will($this->returnValue('1234'))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $securityCode = new SecurityCode();
        $securityCode->setCvv2($cvv2Value);

        $this->sendRetrievePinRequest(3, $securityCode);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('1234', $result['pin_code']);
    }

    public function retrievePinSuccessDataProvider(): array
    {
        return [
            'test with cvv2 value string' => [
                'cvv2Value' => new SensitiveValue('123'),
            ],
            'test with cvv2 value int' => [
                'cvv2Value' => new SensitiveValue(123),
            ],
        ];
    }

    public function testActivateCard_wrong_client_returns_error()
    {
        $this->mockGetCurrentClientByUserId(2);

        $cardId = 1;
        $this->sendCardActivationWithCvvRequest($cardId, ['cvv2' => '777']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('forbidden', $result['error']);
        $card = $this->findCard($cardId);
        $this->assertNotEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testActivateCard_wrong_card_status_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $cardId = 1;
        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_NEW, $card->getStatus());

        $this->sendCardActivationWithCvvRequest($cardId, ['cvv2' => '777']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(409, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiException::INVALID_STATE, $result['error']);

        $card = $this->findCard($cardId);
        $this->assertNotEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    public function testActivateCardWithCvv_card_activated_and_monthly_charge_created()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->createMock(CardClient::class);

        $cardSensitiveInfo = (new SensitiveInfo())
            ->setCardId(33)
            ->setCvv(new SensitiveValue('777'))
            ->setDob(new DateTimeImmutable('2000-01-01'))
        ;
        $contisRestClient->expects($this->once())
            ->method('activate')
            ->with($this->equalTo(clone $cardSensitiveInfo))
        ;
        $contisRestClient->expects($this->once())
            ->method('getCard')
            ->will($this->returnValue(CardResult::create()->setCardStatus(CardResult::STATUS_INACTIVE)))
        ;
        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $this->createContisAccountFee($card);

        $this->assertEquals(Card::STATUS_ORDERED, $card->getStatus());

        $this->sendCardActivationWithCvvRequest($cardId, ['cvv2' => '777']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_ACTIVATED, $result['status']);

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
        /** @var CardCharge $cardCharge */
        $cardCharge = $this->findLastEntity('EvpContisBundle:CardCharge');
        $this->assertEquals(CardCharge::TYPE_MONTHLY_FEE, $cardCharge->getType());
    }

    public function testActivateCardWithCvvAndWrongDateOfBirth()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockGetUserClient();

        $contisRestClient = $this->createMock(CardClient::class);

        $contisRestClient->expects($this->exactly(2))
            ->method('activate')
            ->willReturnOnConsecutiveCalls(
                $this->throwException(new ContisRestClientException('"DOB" Invalid', 3481)),
                $this->anything()
            )
        ;

        $contisRestClient->expects($this->exactly(2))
            ->method('getCard')
            ->will($this->returnValue(CardResult::create()->setCardStatus(CardResult::STATUS_INACTIVE)))
        ;

        $contisConsumerManager = $this->createMock(ContisConsumerManager::class);
        $contisConsumerManager->expects($this->once())
            ->method('updateConsumerDob')
        ;

        $failureCodeResolver = $this->createMock(FailureCodeResolver::class);
        $failureCodeResolver->expects($this->once())
            ->method('resolve')
            ->willReturn(ErrorCodes::INVALID_DOB)
        ;

        $this->client->getContainer()->set('paysera_contis_rest_client.card_client', $contisRestClient);
        $this->client->getContainer()->set('evp_contis.manager.card', null);
        $this->client->getContainer()->set('evp_contis.manager.contis_consumer', $contisConsumerManager);
        $this->client->getContainer()->set('evp_contis.resolver.error_codes', $failureCodeResolver);

        $cardId = 3;
        $card = $this->findCard($cardId);
        $this->createContisAccountFee($card);

        $this->sendCardActivationWithCvvRequest($cardId, ['cvv2' => '777']);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(Card::STATUS_ACTIVATED, $result['status']);

        $card = $this->findCard($cardId);
        $this->assertEquals(Card::STATUS_ACTIVATED, $card->getStatus());
    }

    private function createContisAccountFee(Card $card)
    {
        $contisAccountManager = $this->getContainer()->get('evp_contis.account_manager');
        $contisAccount = $contisAccountManager->createContisAccount(
            $card->getAccount(),
            '123456'
        );

        $contisAccountFee = (new ContisAccountFee())
            ->setContisAccount($contisAccount)
            ->setAmountMoney(new Money(0.45, 'EUR'))
        ;
        $this->getEntityManager()->persist($contisAccountFee);
        $this->getEntityManager()->flush();
    }

    public function testGetLimit()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitCommon = new LimitCommon();
        $limitCommon->setAmount(new Money('100.00', 'EUR'));
        $limitCountBefore = $this->getLimitCount();
        $this->sendSetLimitRequest('EVP9610001000103', $limitCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();
        /** @var Limit $limit */
        $limit = $this->findLastEntity('EvpContisBundle:Limit');
        $this->assertEquals(1, $limitCountAfter - $limitCountBefore);
        $this->assertEquals('100.00', $result['amount']['amount']);
        $this->assertEquals('EUR', $result['amount']['currency']);
        $this->assertNotNull($limit->getAccount());
        $this->assertEquals('EVP9610001000103', $limit->getAccount()->getNumber());
    }

    public function testGetLimit_no_permission_to_account_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitCommon = new LimitCommon();
        $limitCommon->setAmount(new Money(50, 'EUR'));
        $limitCountBefore = $this->getLimitCount();
        $this->sendGetLimitRequest('EVP6410001000104');
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();
        $this->assertEquals($limitCountBefore, $limitCountAfter);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
        $this->assertEquals('Client has no read permission on account', $result['error_description']);
    }

    public function testSetLimit_limit_created_if_not_exists()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitCommon = new LimitCommon();
        $limitCommon->setAmount(new Money('100.00', 'EUR'));
        $limitCountBefore = $this->getLimitCount();
        $this->sendSetLimitRequest('EVP9610001000103', $limitCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();
        /** @var Limit $limit */
        $limit = $this->findLastEntity('EvpContisBundle:Limit');
        $this->assertEquals(1, $limitCountAfter - $limitCountBefore);
        $this->assertEquals('100.00', $result['amount']['amount']);
        $this->assertEquals('EUR', $result['amount']['currency']);
        $this->assertNotNull($limit->getAccount());
        $this->assertEquals('EVP9610001000103', $limit->getAccount()->getNumber());
    }

    public function testSetLimit_limit_modified_if_exists()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitRepository = $this->client->getContainer()->get('evp_contis.repository.limit');
        $account = $this->findAccountByNumber('EVP6410001000106');
        $this->entityManager->flush();

        $limitCommon = new LimitCommon();
        $limitCommon->setAmount(new Money('50.00', 'EUR'));
        $limitCountBefore = $this->getLimitCount();
        $this->sendSetLimitRequest('EVP6410001000106', $limitCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();
        $this->assertEquals($limitCountBefore, $limitCountAfter);
        $this->assertEquals('50.00', $result['amount']['amount']);
        $this->assertEquals('EUR', $result['amount']['currency']);

        /** @var Limit $limit */
        $limit = $limitRepository->findOneBy(['account' => $account]);
        $this->assertEquals(new Money('50.00', 'EUR'), $limit->getAmount());
    }

    public function testSetLimit_no_permission_to_account_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitCommon = new LimitCommon();
        $limitCommon->setAmount(new Money(50, 'EUR'));
        $limitCountBefore = $this->getLimitCount();
        $this->sendSetLimitRequest('EVP6410001000104', $limitCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();
        $this->assertEquals($limitCountBefore, $limitCountAfter);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
        $this->assertEquals('Client has no administrate permission on account', $result['error_description']);
    }

    public function testDeleteLimit_for_card_account()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitRepository = $this->client->getContainer()->get('evp_contis.repository.limit');
        $account = $this->findAccountByNumber('EVP6410001000106');

        $this->sendLimitDeletionRequest('EVP6410001000106');
        $this->assertEquals(204, $this->client->getResponse()->getStatusCode());

        /** @var Limit $limit */
        $limit = $limitRepository->findOneBy(['account' => $account]);
        $this->assertNull($limit->getAmount());
    }

    public function testDeleteLimit_no_permission_for_account_returns_error()
    {
        $this->mockGetCurrentClientByUserId(1);

        $limitCountBefore = $this->getLimitCount();
        $this->sendLimitDeletionRequest('EVP6410001000104');
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $limitCountAfter = $this->getLimitCount();

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('no_rights', $result['error']);
        $this->assertEquals('Client has no administrate permission on account', $result['error_description']);

        $this->assertEquals($limitCountBefore, $limitCountAfter);
    }

    private function createNaturalClient($userId)
    {
        $client = new ClientNatural();
        $client->setCode('34567');
        $client->setLocale('lt');
        $client->setCovenanteeId($userId);
        $client->setLevel(IdentificationLevels::BASIC_IDENTIFIED);
        $this->client
            ->getContainer()
            ->get('doctrine.orm.entity_manager')
            ->persist($client)
        ;
        $this->entityManager->flush();
        return $client;
    }

    /**
     * @param int $cardId
     *
     * @return Card|null
     */
    private function findCard(int $cardId)
    {
        return $this->cardRepository->findOneById($cardId);
    }

    /**
     * @param string $accountNumber
     *
     * @return Account|null
     */
    private function findAccountByNumber($accountNumber)
    {
        return $this->accountRepository->findOneByNumber($accountNumber);
    }

    /**
     * @return Card|null
     */
    private function findLastCard()
    {
        return $this->findLastEntity('EvpContisBundle:Card');
    }

    /**
     * @return Charge|null
     */
    private function findLastCharge()
    {
        return $this->findLastEntity('EvpBankChargeBundle:Charge');
    }

    private function findLastEntity($entity)
    {
        $entities = $this->client
            ->getContainer()
            ->get('doctrine.orm.entity_manager')
            ->getRepository($entity)
            ->findBy([], ['id' => 'DESC'], 1)
        ;
        if (!$entities) {
            return null;
        }
        return $entities[0];
    }

    /**
     * @return int
     */
    private function getAccountCount()
    {
        return count($this->accountRepository->findAll());
    }

    /**
     * @return int
     */
    private function getCardCount()
    {
        return count($this->cardRepository->findAll());
    }

    /**
     * @return int
     */
    private function getLimitCount()
    {
        return count($this->limitRepository->findAll());
    }

    /**
     * @param string $accountNumber
     * @param Money $amount
     *
     * @return mixed
     */
    private function fillAccount($accountNumber, Money $amount)
    {
        $transferIn = new TransferIn();
        $transferIn->setAmountMoney($amount);
        $transferIn->setBeneficiary(new PartyAccount($accountNumber, 'Name'));
        $transferIn->setDate(new DateTime());
        $transferIn->setOperationDate(new DateTime());
        $transferIn->setDetails('Account fill');
        $transferIn->setPayer(new PartyPerson('Testas Testauskas'));
        $this->createTransferInspectionWithActionNone($transferIn);

        $transferIn = $this->getBankApiController()->makeTransfer(1, $transferIn)->getReturnValue();
        $this->assertSame(TransferIn::STATUS_PREPARED, $transferIn->getStatus());
        $this->client->getContainer()->get('evp_bank_transfer.transfer_processor.batch.in')->process();
        return $transferIn->getId();
    }

    /**
     * @return SoapController
     */
    private function getBankApiController()
    {
        $soapController = new SoapController();
        $soapController->setContainer($this->client->getContainer());
        return $soapController;
    }

    private function mockGetUserServices()
    {
        $service = Service::create()
            ->setService('issued_payment_card')
            ->setStatus(Service::STATUS_ENABLED)
        ;

        $userServices = new UserServices();
        $userServices->setServices([$service]);

        $serviceClient = $this->getMockBuilder(ServiceClient::class)->disableOriginalConstructor()->getMock();
        $serviceClient
            ->expects($this->any())
            ->method('getUserServices')
            ->will($this->returnValue($userServices))
        ;
        $serviceClient
            ->expects($this->any())
            ->method('getUserService')
            ->will($this->returnValue($service))
        ;
        $this->client->getContainer()->set('evp_contis.service_client', $serviceClient);
    }

    private function mockUserRestFactory(): void
    {
        $userClientMock = $this->createMock(UserClient::class);
        $userClientMock->method('getUser')->willReturn(new UserInformation());

        $identityReportClientMock = $this->createMock(IdentityReportClient::class);

        $userRestFactoryMock = $this->createMock(UserRestFactory::class);
        $userRestFactoryMock->method('userClient')->willReturn($userClientMock);
        $userRestFactoryMock->method('identityReportClient')->willReturn($identityReportClientMock);


        $this->client->getContainer()->set('evp_user_client.user_rest_factory', $userRestFactoryMock);
    }

    /**
     * @param CardCommon $cardCommon
     */
    private function sendAddCardRequest(CardCommon $cardCommon)
    {
        $cardNormalizer = $this->client->getContainer()->get('evp_gateway.normalizer.issued_payment_card');

        $this->client->request(
            'POST',
            $this->getRouter()->generate('EvpContisBundle_add_card'),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($cardNormalizer->mapFromEntity($cardCommon))
        );
    }

    private function sendAddVirtualCardRequest(CardCommon $cardCommon): void
    {
        $cardNormalizer = $this->client->getContainer()->get('evp_gateway.normalizer.issued_payment_card');

        $this->client->request(
            'POST',
            $this->getRouter()->generate('EvpContisBundle_add_virtual_card'),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($cardNormalizer->mapFromEntity($cardCommon))
        );
    }

    /**
     * @param int $cardId
     */
    private function sendGetCardRequest($cardId)
    {
        $this->client->request(
            'GET',
            $this->getRouter()->generate('EvpContisBundle_get_card', ['cardId' => $cardId])
        );
    }

    /**
     * @param array $cardFilter
     */
    private function sendGetCardsRequest($cardFilter)
    {
        $this->client->request(
            'GET',
            $this->getRouter()->generate('EvpContisBundle_get_cards') . '?' . http_build_query($cardFilter, '', '&')
        );
    }

    /**
     * @param array $accountQuery
     */
    private function sendGetShippingAddressRequest($accountQuery)
    {
        $this->client->request(
            'GET',
            $this->getRouter()
                ->generate('EvpContisBundle_get_shipping_address') . '?' . http_build_query($accountQuery, '', '&')
        );
    }

    /**
     * @param int $cardId
     */
    private function sendCardDeactivationRequest($cardId)
    {
        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_disable_card_temporarily', ['cardId' => $cardId])
        );
    }

    /**
     * @param int $cardId
     */
    private function sendCardCancellationRequest($cardId)
    {
        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_disable_card_permanently', ['cardId' => $cardId])
        );
    }

    /**
     * @param int $cardId
     */
    private function sendEnableCardRequest($cardId)
    {
        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_enable_card', ['cardId' => $cardId]),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json']
        );
    }

    /**
     * @param int $cardId
     * @param SecurityCode $securityCode
     */
    private function sendRetrievePinRequest($cardId, SecurityCode $securityCode)
    {
        $securityCodeNormalizer = $this->client->getContainer()->get('evp_gateway.normalizer.security_code');

        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_retrieve_pin', ['cardId' => $cardId]),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($securityCodeNormalizer->mapFromEntity($securityCode))
        );
    }

    private function sendCardActivationWithCvvRequest($cardId, $content)
    {
        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_activate_card_with_cvv', ['cardId' => $cardId]),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($content)
        );
    }

    /**
     * @param string $accountNumber
     */
    private function sendGetLimitRequest($accountNumber)
    {
        $this->client->request(
            'GET',
            $this->getRouter()->generate('EvpContisBundle_get_limit', ['accountNumber' => $accountNumber]),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json']
        );
    }

    /**
     * @param string $accountNumber
     * @param LimitCommon $limitCommon
     */
    private function sendSetLimitRequest($accountNumber, LimitCommon $limitCommon)
    {
        $limitNormalizer = $this->client->getContainer()->get('evp_gateway.normalizer.issued_payment_card_limit');

        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_set_limit', ['accountNumber' => $accountNumber]),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($limitNormalizer->mapFromEntity($limitCommon))
        );
    }

    /**
     * @param string $accountNumber
     */
    private function sendLimitDeletionRequest($accountNumber)
    {
        $this->client->request(
            'DELETE',
            $this->getRouter()
                ->generate('EvpContisBundle_delete_limit', ['accountNumber' => $accountNumber])
        );
    }

    /**
     * @param Transfer $transfer
     */
    private function createTransferInspectionWithActionNone(Transfer $transfer)
    {
        $transferInspection = new TransferInspection();
        $transferInspection->setStatus(TransferInspection::STATUS_PROCESSED);
        $transferInspection->setAction(TransferInspection::ACTION_NONE);

        $this->transferInspectionProvider->setTransferInspection($transfer, $transferInspection);
    }

    /**
     * @param int $cardId
     */
    private function sendReplaceLostCardRequest(int $cardId)
    {
        $this->client->request(
            'PUT',
            $this->getRouter()->generate('EvpContisBundle_replace_card', ['cardId' => $cardId])
        );
    }

    private function mockGetUserClient()
    {
        $userInformation = (new UserInformation())
            ->setDob(new DateTime('2000-01-01'))
        ;

        $userClient = $this->createMock(UserClient::class);
        $userClient
            ->expects($this->any())
            ->method('getPerson')
            ->will($this->returnValue($userInformation))
        ;

        $this->client->getContainer()->set('evp_contis.user_client', $userClient);
    }

    public function testAddVirtualCard()
    {
        $this->mockGetCurrentClientByUserId(1);
        $this->mockCardOwnerInformation('lt', 14); //min 13 for additional CardHolder
        $this->mockGetUserServices();

        $this->fillAccount('EVP6410001000101', new Money(100, 'EUR'));

        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId(2);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber('EVP6410001000101');
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setAccountNumber('EVP9610001000103');
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));
        $cardCommon->setVirtual(true);

        $accountCountBefore = $this->getAccountCount();
        $this->sendAddVirtualCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals('EVP9610001000103', $result['account_number']);

        $accountCountAfter = $this->getAccountCount();
        $this->assertEquals($accountCountBefore, $accountCountAfter);
        $card = $this->findLastCard();
        $this->assertNotNull($card);
        $this->assertTrue($card->isVirtual());
        $this->assertEquals(ShipmentCodeManager::DESIGN_VIRTUAL, $card->getContisDesignReference());

        $this->assertEquals(Card::STATUS_NEW, $card->getStatus());
        $this->assertEquals('EVP9610001000103', $card->getAccount()->getNumber());
        $this->assertNotNull($this->findLastCharge());
        $this->assertFalse($card->isMain());
        $charge = $this->findLastCharge();
        $this->assertEquals('EVP6410001000101', $charge->getAccount()->getNumber());
        $this->assertEquals(Charge::STATUS_DONE, $charge->getStatus());

        $this->assertEquals(
            $cardCommon->getIssuePrice(),
            $charge->getAmountMoney()
        );
    }

    /**
     * @dataProvider dataProviderAddCardClientRestrictedError
     *
     * @param int $cardOwnerId
     * @param string $accountNumber
     * @return void
     */
    public function testAddCardClientRestrictedError(int $cardOwnerId, string $accountNumber)
    {
        $cardCommon = new CardCommon();
        $cardCommon->setCardOwnerId($cardOwnerId);
        $chargeInfo = new ChargeInfo();
        $chargeInfo->setAccountNumber($accountNumber);
        $shippingAddress = new ShippingAddress();
        $shippingAddress
            ->setCountry('LT')
            ->setCity('Vilnius')
            ->setPostalCode('12345')
            ->setAddress('Whatever 13-1')
        ;
        $cardCommon->setChargeInfo($chargeInfo);
        $cardCommon->setShippingAddress($shippingAddress);
        $cardCommon->setDeliveryType(DeliveryTypes::DELIVERY_TYPE_REGULAR);
        $cardCommon->setDeliveryPrice(new Money(3, 'EUR'));
        $cardCommon->setIssuePrice(new Money(0.5, 'EUR'));

        $this->mockGetCurrentClientByUserId($cardOwnerId);
        $this->sendAddCardRequest($cardCommon);
        $result = json_decode($this->client->getResponse()->getContent(), true);

        $this->assertEquals(400, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(ApiResponseErrors::CARD_ORDER_CLIENT_RESTRICTED, $result['error']);
        $this->assertEquals(
            'Card ordering was unsuccessful! The account information could be incomplete or the account is restricted. Please contact Customer support for more information',
            $result['error_description']
        );
    }

    public function dataProviderAddCardClientRestrictedError(): array
    {
        return [
            'natural client' => [
                'cardOwnerId' => 18,
                'accountNumber' => 'EVP7910001000318',
            ],
            'legal client' => [
                'cardOwnerId' => 18,
                'accountNumber' => 'EVP7910001000319',
            ],
        ];
    }
}
