<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Event;

use Evp\Bundle\ContisBundle\Entity\CardCloseOperationData;
use Symfony\Component\EventDispatcher\Event;

class CardCloseEvent extends Event
{
    public const TRANSFORM_ACCOUNT_TO_LOCAL = 'transform_account_to_local';
    private CardCloseOperationData $cardCloseOperationData;

    public function __construct(CardCloseOperationData $cardCloseOperationData)
    {
        $this->cardCloseOperationData = $cardCloseOperationData;
    }

    public function getData(): CardCloseOperationData
    {
        return $this->cardCloseOperationData;
    }
}
