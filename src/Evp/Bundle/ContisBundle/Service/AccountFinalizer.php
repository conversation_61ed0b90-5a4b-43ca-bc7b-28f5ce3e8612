<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Service;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountBalanceProviderInterface;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeChangeManager;
use Evp\Bundle\ContisAccountingBundle\Entity\AccountingOperation;
use Evp\Bundle\ContisAccountingBundle\Service\AccountFinalizationHelper;
use Evp\Bundle\ContisBundle\Entity\Card;
use Evp\Bundle\ContisBundle\Entity\ContisAccount;
use Evp\Bundle\ContisBundle\Entity\ContisAccountStatusChange;
use Evp\Bundle\ContisBundle\Entity\Transaction;
use Evp\Bundle\ContisBundle\Exception\AccountFinalizerException;
use Evp\Bundle\ContisBundle\Exception\HoldContisBalanceException;
use Evp\Bundle\ContisBundle\Exception\NegativeContisBalanceException;
use Evp\Bundle\ContisBundle\Repository\ContisAccountRepository;
use Evp\Bundle\ContisBundle\Worker\AccountsSynchronizeWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisher;
use Evp\Component\GatewayCommon\IssuedPaymentCard\FailureCodes;
use Evp\Component\Money\Money;
use Paysera\Bundle\ContisRestClientBundle\Entity\AccountBalanceResult;
use Paysera\Bundle\ContisRestClientBundle\Entity\AccountInfo;
use Paysera\Bundle\ContisRestClientBundle\Entity\Consumer\ConsumerResult;
use Paysera\Bundle\ContisRestClientBundle\ErrorCodes;
use Paysera\Bundle\ContisRestClientBundle\Exception\ContisClientException;
use Paysera\Bundle\ContisRestClientBundle\Service\AccountClient;
use Paysera\Bundle\ContisRestClientBundle\Service\ConsumerClient;
use Evp\Bundle\ContisBundle\Entity\CardCloseOperationData;
use Evp\Bundle\ContisBundle\Event\CardCloseEvent;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class AccountFinalizer
{
    private EntityManagerInterface $entityManager;
    private ContisAccountRepository $contisAccountRepository;
    private AccountBalanceProviderInterface $localBalanceProvider;
    private TransactionManager $transactionManager;
    private AccountStateHelper $accountStateHelper;
    private AccountTypeChangeManager $accountTypeChangeManager;
    private AccountFinalizationHelper $accountFinalizationHelper;
    private CardStateHelper $cardStateHelper;
    private ContisAccountTopUpManager $contisAccountTopUpManager;
    private ContisAccountManager $contisAccountManager;
    private AccountClient $contisRestAccountClient;
    private ConsumerClient $contisRestConsumerClient;
    private FailureCodeResolver $failureCodeResolver;
    private PrimaryConsumerSynchronizer $primaryConsumerSynchronizer;
    private RemoteJobPublisher $remoteJobPublisher;
    private LoggerInterface $logger;
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        EntityManagerInterface $entityManager,
        ContisAccountRepository $contisAccountRepository,
        AccountBalanceProviderInterface $localBalanceProvider,
        TransactionManager $transactionManager,
        AccountStateHelper $accountStateHelper,
        AccountTypeChangeManager $accountTypeChangeManager,
        AccountFinalizationHelper $accountFinalizationHelper,
        CardStateHelper $cardStateHelper,
        ContisAccountTopUpManager $contisAccountTopUpManager,
        ContisAccountManager $contisAccountManager,
        AccountClient $contisRestAccountClient,
        ConsumerClient $contisRestConsumerClient,
        FailureCodeResolver $failureCodeResolver,
        PrimaryConsumerSynchronizer $primaryConsumerSynchronizer,
        RemoteJobPublisher $remoteJobPublisher,
        LoggerInterface $logger,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->entityManager = $entityManager;
        $this->contisAccountRepository = $contisAccountRepository;
        $this->localBalanceProvider = $localBalanceProvider;
        $this->transactionManager = $transactionManager;
        $this->accountStateHelper = $accountStateHelper;
        $this->accountTypeChangeManager = $accountTypeChangeManager;
        $this->accountFinalizationHelper = $accountFinalizationHelper;
        $this->cardStateHelper = $cardStateHelper;
        $this->contisAccountTopUpManager = $contisAccountTopUpManager;
        $this->contisAccountManager = $contisAccountManager;
        $this->contisRestAccountClient = $contisRestAccountClient;
        $this->contisRestConsumerClient = $contisRestConsumerClient;
        $this->failureCodeResolver = $failureCodeResolver;
        $this->primaryConsumerSynchronizer = $primaryConsumerSynchronizer;
        $this->remoteJobPublisher = $remoteJobPublisher;
        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function finalizeAccountOnCard(Card $card)
    {
        if ($this->cardStateHelper->shouldCloseAccountAfterCardCancelledOrExpired($card)) {
            try {
                $this->finalizeAccountAndCloseAtContis($card->getAccount());
            } catch (AccountFinalizerException $exception) {
                $this->accountFinalizationHelper->handleAccountFinalizerException($exception, $card);
            }
        } elseif ($this->cardStateHelper->shouldTransformAccountToLocalAfterCardCancelled($card)) {
            $this->transformAccountToLocal($card->getAccount());
        }
    }

    public function finalizeClosedAccount(ContisAccount $contisAccount, ?string $reason = null)
    {
        $this->logger->info('Start finalizing Contis account.', ['contis_account_id' => $contisAccount->getId()]);

        try {
            $this->finalizeAccountAndCloseAtContis($contisAccount->getAccount(), null, null, $reason);
        } catch (AccountFinalizerException $exception) {
            $this->accountFinalizationHelper->handleAccountFinalizerException($exception, null, $contisAccount);
        }
    }

    /**
     * Closes account at Contis, changes Account type from "contis" to "local". Withdraws funds from Contis.
     *
     * @param Account $account
     * @param AccountBalanceResult|null $accountBalanceResult
     * @param ConsumerResult|null $consumerResult
     * @param string|null $reason
     * @throws AccountFinalizerException
     * @throws HoldContisBalanceException
     * @throws NegativeContisBalanceException
     */
    public function finalizeAccountAndCloseAtContis(
        Account $account,
        AccountBalanceResult $accountBalanceResult = null,
        ConsumerResult $consumerResult = null,
        string $reason = null
    ) {
        $contisAccount = $this->contisAccountRepository->findOneByAccount($account);
        $contisAccountNumber = $contisAccount->getAccountNumber();
        $canCloseAccount = $this->accountStateHelper->canCloseAccount($account, $accountBalanceResult, $consumerResult);
        $accountBalanceResult = $canCloseAccount->getAccountBalanceResult();
        $consumerResult = $canCloseAccount->getConsumerResult();
        $contisAvailableBalance = $accountBalanceResult->getAvailableBalance();

        if (!$canCloseAccount->canCloseAccount()) {
            if ($canCloseAccount->isReasonNegativeBalance()) {
                throw new NegativeContisBalanceException();
            }

            if ($canCloseAccount->isReasonHoldBalance()) {
                throw new HoldContisBalanceException();
            }

            if ($this->accountStateHelper->canTransformToLocal($account, $accountBalanceResult, $consumerResult)) {
                $this->transformAccountToLocal($account);
            }

            if ($canCloseAccount->isReasonAccountStatus()) {
                $this->logger->info(
                    'Not closing an account in Contis because of its status',
                    [
                        'contis_account_number' => $contisAccountNumber,
                        'status' => $accountBalanceResult->getAccountStatus(),
                    ]
                );
            }

            if ($canCloseAccount->isReasonCardHolderStatus()) {
                $consumerStatus = ($consumerResult === null) ? 'No cardholder found' : $consumerResult->getStatus();

                $this->logger->info(
                    'Not closing an account in Contis because of its main cardholder status',
                    [
                        'contis_account_number' => $contisAccountNumber,
                        'status' => $consumerStatus,
                    ]
                );
            }
            return;
        }

        $withdrawalTransaction = null;
        if ($contisAvailableBalance->isPositive()) {
            if (!$this->accountStateHelper->isAccountInContisLive($contisAccountNumber)) {
                $this->logger->info(
                    'Cannot withdraw when closing an account.',
                    ['account_id' => $account->getId(), 'contis_account_number' => $contisAccountNumber]
                );
                return;
            }

            $withdrawalTransaction = $this
                ->handlePositiveBalance($account, $contisAccountNumber, $contisAvailableBalance)
            ;
        }

        $this->handleNegativeBalance($contisAccount, $contisAvailableBalance);
        if ($consumerResult === null) {
            throw new AccountFinalizerException(
                'Cannot resolve card holder when trying to close an account',
                AccountFinalizerException::REASON_CANNOT_RESOLVE_CARD_HOLDER
            );
        }

        try {
            $this->closeAccountInContis($consumerResult, $accountBalanceResult->getAccountStatus());
        } catch (ContisClientException $contisClientException) {
            if ($this->failureCodeResolver->resolve($contisClientException) === ErrorCodes::CONSUMER_IS_NOT_PRIMARY) {
                $this->primaryConsumerSynchronizer->synchronize($contisAccount);
                $this->synchronizeAccount($contisAccount);
            }

            if ($withdrawalTransaction !== null) {
                $this->logger->info(
                    'Contis balance was withdrawn but cannot close the account',
                    [
                        'account_id' => $account->getId(),
                        'contis_account_number' => $contisAccountNumber,
                        'exception_message' => $contisClientException->getMessage(),
                    ]
                );
            } else {
                $this->logger->info(
                    'Contis account cannot be closed',
                    [
                        'account_id' => $account->getId(),
                        'exception_message' => $contisClientException->getMessage(),
                    ]
                );
            }

            throw $this->createAccountFinalizerException($contisClientException);
        }

        $accountBalanceResult = $this->contisRestAccountClient->getBalance(
            (new AccountInfo())->setAccountNumber($contisAccount->getAccountNumber())
        );

        if ($accountBalanceResult->getAccountStatus() !== AccountBalanceResult::STATUS_CLOSED) {
            $this->logger->error('Account in Contis is not closed after changing consumer status', [
                'account_id' => $contisAccount->getAccount()->getId(),
                'status' => $accountBalanceResult->getAccountStatus(),
            ]);
            return;
        }
        $this->contisAccountManager->updateAccountInfo(
            $contisAccount,
            $reason ?? ContisAccountStatusChange::REASON_FINALIZE_CONTIS_ACCOUNT,
            $accountBalanceResult
        );

        if ($account->getType() === Account::TYPE_CONTIS) {
            $this->transformAccountToLocal($account, $withdrawalTransaction);
        }
    }

    private function handleNegativeBalance(ContisAccount $contisAccount, Money $contisAvailableBalance)
    {
        if (!$contisAvailableBalance->isNegative()) {
            return;
        }

        $this->contisAccountTopUpManager->topUpNegativeAccountFromLocalAccount(
            $contisAccount,
            $contisAvailableBalance->abs()
        );
    }

    /**
     * @param Account $account
     * @param string|null $contisAccountNumber
     * @param Money $contisAvailableBalance
     *
     * @return Transaction|null
     */
    private function handlePositiveBalance(Account $account, string $contisAccountNumber, Money $contisAvailableBalance)
    {
        return $this->transactionManager->withdrawMoney(
            $contisAvailableBalance,
            $account,
            $contisAccountNumber,
            Transaction::PURPOSE_ACCOUNT_FINALIZATION
        );
    }

    /**
     * @param Account $account
     * @param Transaction|null $withdrawalTransaction
     *
     * @throws AccountFinalizerException
     */
    public function transformAccountToLocal(Account $account, Transaction $withdrawalTransaction = null)
    {
        if ($account->getType() === Account::TYPE_LOCAL) {
            throw new AccountFinalizerException('Account already local');
        }

        $this->accountTypeChangeManager->changeType($account, Account::TYPE_LOCAL);
        $this->createFinalizationOperationsForLocalBalances(
            $account,
            $withdrawalTransaction !== null ? $withdrawalTransaction->getCreatedAt() : new DateTime()
        );
    }

    private function createFinalizationOperationsForLocalBalances(Account $account, DateTime $dateForOperation)
    {
        $localBalance = $this->localBalanceProvider->getBalance($account);
        foreach ($localBalance->getAllCurrencies() as $currency) {
            $accountingOperation = (new AccountingOperation())
                ->setAmountMoney($localBalance->getMain($currency)->add($localBalance->getReserved($currency)))
                ->setType(AccountingOperation::TYPE_ACCOUNT_FINALIZATION)
                ->setDate($dateForOperation)
                ->setAccount($account)
                ->setClient($account->getClient())
            ;
            $this->entityManager->persist($accountingOperation);

            $cardCloseOperationData = (new CardCloseOperationData())
                ->setAccount($account)
                ->setDate($dateForOperation)
                ->setAmountMoney($localBalance->getMain($currency)->add($localBalance->getReserved($currency)))
            ;

            $this->eventDispatcher->dispatch(
                CardCloseEvent::TRANSFORM_ACCOUNT_TO_LOCAL,
                new CardCloseEvent($cardCloseOperationData)
            );
        }
    }

    private function closeAccountInContis(ConsumerResult $consumerResult, string $accountStatusAtContis)
    {
        if ($consumerResult->getStatus() === ConsumerResult::STATUS_LOCKED_OUT) {
            $this->logger->info(
                'Not changing consumer status. Old status is already Locked out.',
                [
                    'status' => $consumerResult->getStatus(),
                    'consumer_id' => $consumerResult->getConsumerId(),
                ]
            );
            return;
        }

        if ($accountStatusAtContis === AccountBalanceResult::STATUS_CLOSED) {
            $this->logger->info('Not changing consumer status. Old status is already Closed.', [
                'consumer_id' => $consumerResult->getConsumerId(),
            ]);
            return;
        }

        $this->logger->info('Changing card holder status', [
            'from_status' => $consumerResult->getStatus(),
            'to_status' => ConsumerResult::STATUS_LOCKED_OUT,
            'consumer_id' => $consumerResult->getConsumerId(),
        ]);

        $this->contisRestConsumerClient->setAsLockout($consumerResult->getConsumerId());

        $newConsumerResult = $this->contisRestConsumerClient->getById($consumerResult->getConsumerId());

        if ($newConsumerResult->getStatus() !== ConsumerResult::STATUS_LOCKED_OUT) {
            $this->logger->error(
                'Consumer status is not correct after SetConsumerAsLockout method is called.',
                ['consumer_id' => $newConsumerResult->getConsumerId()]
            );
        }

        $this->logger->info(
            'Consumer status after change',
            [
                'expected_status' => ConsumerResult::STATUS_LOCKED_OUT,
                'actual_status' => $newConsumerResult->getStatus(),
                'consumer_id' => $newConsumerResult->getConsumerId(),
            ]
        );
    }

    /**
     * @param ContisClientException $contisClientException
     *
     * @return AccountFinalizerException
     */
    private function createAccountFinalizerException(ContisClientException $contisClientException)
    {
        $flag = 0;

        if ((int) $this->failureCodeResolver->resolve($contisClientException) === AccountFinalizerException::REASON_NONZERO_BALANCE) {
            $flag |= AccountFinalizerException::REASON_NONZERO_BALANCE;
        }

        if ((int) $this->failureCodeResolver->resolve($contisClientException) === AccountFinalizerException::REASON_PENDING_TRANSACTIONS) {
            $flag |= AccountFinalizerException::REASON_PENDING_TRANSACTIONS;
        }

        if ($this->failureCodeResolver->resolve($contisClientException) === FailureCodes::ACCOUNT_FROZEN_OR_SUSPENDED) {
            $flag |= AccountFinalizerException::REASON_MAIN_ACCOUNT_STATUS_INVALID_FOR_CLOSING;
        }

        if ((int)$this->failureCodeResolver->resolve($contisClientException) === AccountFinalizerException::REASON_INVALID_CARD_HOLDER_STATUS_CHANGE) {
            $flag |= AccountFinalizerException::REASON_INVALID_CARD_HOLDER_STATUS_CHANGE;
        }
        return new AccountFinalizerException($contisClientException->getMessage(), $flag);
    }

    private function synchronizeAccount(ContisAccount $contisAccount): void
    {
        $this->remoteJobPublisher->publishJob(
            AccountsSynchronizeWorker::JOB_KEY,
            [
                'id' => $contisAccount->getId(),
            ]
        );
    }
}
