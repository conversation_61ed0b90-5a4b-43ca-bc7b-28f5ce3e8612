<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Entity;

use DateTime;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Component\Money\Money;

class CardCloseOperationData
{
    private Account $account;
    private DateTime $date;
    private Money $amountMoney;

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function getDate(): DateTime
    {
        return $this->date;
    }

    public function getAmountMoney(): Money
    {
        return $this->amountMoney;
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function setDate(DateTime $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function setAmountMoney(Money $amountMoney): self
    {
        $this->amountMoney = $amountMoney;

        return $this;
    }
}
