<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Processor;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\OperationProcessorInterface;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\CardBundle\Entity\CardCloseOperation;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\ContisAccountingBundle\Service\PartnerAccountingDataProvider;
use Evp\Component\Money\Money;
use Paysera\Bundle\GeorgiaRTGSBundle\Exception\InvalidArgumentException;
use Paysera\Bundle\PartnerBundle\Entity\RemoteOperationRequest;

class CardCloseOperationProcessor implements OperationProcessorInterface
{
    private PartnerClientManager $partnerClientManager;
    private TemplateProvider $templateProvider;
    private EntityManagerInterface $entityManager;
    private PartnerAccountingDataProvider $partnerAccountingDataProvider;
    private string $contisDefaultPartner;

    public function __construct(
        PartnerClientManager $partnerClientManager,
        TemplateProvider $templateProvider,
        EntityManagerInterface $entityManager,
        PartnerAccountingDataProvider $partnerAccountingDataProvider,
        string $contisDefaultPartner
    ) {
        $this->partnerClientManager = $partnerClientManager;
        $this->templateProvider = $templateProvider;
        $this->entityManager = $entityManager;
        $this->partnerAccountingDataProvider = $partnerAccountingDataProvider;
        $this->contisDefaultPartner = $contisDefaultPartner;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function process(Operation $operation): void
    {
        if (!($operation instanceof CardCloseOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    CardCloseOperation::class
                )
            );
        }

        $account = $operation->getAccount();
        $cardCloseDate = $operation->getDate();
        $amountMoney = new Money($operation->getAmount(), $operation->getCurrency());
        $partnerCode = $this->partnerClientManager->getPartnerCodeByAccount(
            $account,
            DateTime::createFromImmutable($cardCloseDate)
        );

        $remoteOperationRequest = (new RemoteOperationRequest())
            ->setOperation($operation)
            ->setPartnerCode($this->contisDefaultPartner)
            ->setType(RemoteOperationRequest::TYPE_OPERATION)
            ->setReference((string)$operation->getId())
            ->setCovenanteeId(
                (int)$this->partnerAccountingDataProvider->getConvenanteeId(
                    $account,
                    DateTime::createFromImmutable($cardCloseDate),
                )
            )
            ->setDetails(RemoteOperationRequest::TYPE_ACCOUNT_FINALIZATION)
            ->setAmountMoney($amountMoney)
            ->setTemplateName($this->templateProvider->getTemplateForContisFundsOnWayOut())
            ->setOperationDate($cardCloseDate)
        ;

        $this->entityManager->persist($remoteOperationRequest);

        if ($partnerCode !== $this->contisDefaultPartner) {
            $w2pReceivedRemoteOperationRequest = (clone $remoteOperationRequest)
                ->setTemplateName($this->templateProvider->getWebToPayReceived())
                ->setPartnerCode($partnerCode)
                ->setCovenanteeId($account->getClient()->getCovenanteeId())
            ;
            $this->entityManager->persist($w2pReceivedRemoteOperationRequest);

            $receivedRemoteOperationRequest = (clone $w2pReceivedRemoteOperationRequest)
                ->setTemplateName(
                    $this->templateProvider->getReceivedTransferInTroughPartner(
                        $this->contisDefaultPartner,
                        $amountMoney->getCurrency(),
                    )
                )
            ;
            $this->entityManager->persist($receivedRemoteOperationRequest);
        }
    }
}
