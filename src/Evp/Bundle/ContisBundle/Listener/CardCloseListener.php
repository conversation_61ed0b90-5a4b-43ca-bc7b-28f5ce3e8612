<?php

declare(strict_types=1);

namespace Evp\Bundle\ContisBundle\Listener;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\CardBundle\Entity\CardCloseOperation;
use Evp\Bundle\CardBundle\Repository\CardCloseOperationRepository;
use Evp\Bundle\ContisBundle\Event\CardCloseEvent;
use Psr\Log\LoggerInterface;

class CardCloseListener
{
    private EntityManagerInterface $entityManager;
    private CardCloseOperationRepository $cardCloseOperationRepository;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        CardCloseOperationRepository $cardCloseOperationRepository,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->cardCloseOperationRepository = $cardCloseOperationRepository;
        $this->logger = $logger;
    }

    public function onCardClose(CardCloseEvent $cardClosedEvent): void
    {
        $account = $cardClosedEvent->getData()->getAccount();
        $cardCloseDate = $cardClosedEvent->getData()->getDate();
        $amountMoney = $cardClosedEvent->getData()->getAmountMoney();

        $existingOperation = $this->cardCloseOperationRepository->findByAccountAndAmount(
            $account,
            $amountMoney
        );

        if ($existingOperation === null) {
            $operation = (new CardCloseOperation())
                ->setAccount($account)
                ->setCurrency($amountMoney->getCurrency())
                ->setAmount($amountMoney->getAmount())
                ->setDate(DateTimeImmutable::createFromMutable($cardCloseDate))
            ;

            $this->entityManager->persist($operation);
        } else {
            $this->logger->error(
                'Tried to create duplicate bank_operation for card account closing',
                [
                    'amount' => $amountMoney,
                    'account_id' => $account->getId(),
                    'operation_id' => $existingOperation->getId(),
                ]
            );
        }
    }
}
