<?php

declare(strict_types=1);

namespace Evp\Bundle\VmiTiesReportBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\VmiTiesReportBundle\Entity\Report;
use Evp\Bundle\VmiTiesReportBundle\Entity\ReportItem;
use Evp\Bundle\VmiTiesReportBundle\Repository\CRSDAC2\ReportingFinancialInstitutionRepository as CrsDac2ReportingFinancialInstitutionRepository;
use Evp\Bundle\VmiTiesReportBundle\Repository\FATCA\ReportingFinancialInstitutionRepository as FatcaReportingFinancialInstitutionRepository;
use Evp\Bundle\VmiTiesReportBundle\Repository\ReportRepository;
use Evp\Bundle\VmiTiesReportBundle\Service\VmiTiesServiceProvider;
use InvalidArgumentException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ChangeReportItemStatusByReportCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private ReportRepository $reportRepository;
    private VmiTiesServiceProvider $vmiTiesServiceProvider;
    private CrsDac2ReportingFinancialInstitutionRepository $crsDac2ReportingFinancialInstitutionRepository;
    private FatcaReportingFinancialInstitutionRepository $fatcaReportingFinancialInstitutionRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        ReportRepository $reportRepository,
        VmiTiesServiceProvider $vmiTiesServiceProvider,
        CrsDac2ReportingFinancialInstitutionRepository $crsDac2ReportingFinancialInstitutionRepository,
        FatcaReportingFinancialInstitutionRepository $fatcaReportingFinancialInstitutionRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->reportRepository = $reportRepository;
        $this->vmiTiesServiceProvider = $vmiTiesServiceProvider;
        $this->crsDac2ReportingFinancialInstitutionRepository = $crsDac2ReportingFinancialInstitutionRepository;
        $this->fatcaReportingFinancialInstitutionRepository = $fatcaReportingFinancialInstitutionRepository;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Sets all report items that are part of a report to a specified status')
            ->addArgument('report-id', InputArgument::REQUIRED, 'Report ID')
            ->addArgument('report-item-status', InputArgument::REQUIRED, 'Report item status to change to')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $reportId = $input->getArgument('report-id');
        $reportItemStatus = $input->getArgument('report-item-status');

        if (!in_array($reportItemStatus, ReportItem::AVAILABLE_STATUSES, true)) {
            throw new InvalidArgumentException('Report item status does not exist');
        }

        $report = $this->reportRepository->findOneById((int)$reportId);

        if ($report === null) {
            throw new InvalidArgumentException('Report does not exist');
        }

        $reportItemRepository = $this->vmiTiesServiceProvider->getReportItemRepository($report->getMessageType());

        foreach ($reportItemRepository->findByReport($report) as $reportItem) {
            $reportItem->setStatus($reportItemStatus);
        }
        if ($report->getMessageType() === Report::MESSAGE_TYPE_CRS_DAC2) {
            $reportingFinancialInstitutions = $this->crsDac2ReportingFinancialInstitutionRepository->findByReport($report);
            foreach ($reportingFinancialInstitutions as $reportingFinancialInstitution) {
                $reportingFinancialInstitution->setStatus($reportItemStatus);
            }
        }
        if ($report->getMessageType() === Report::MESSAGE_TYPE_FATCA) {
            $reportingFinancialInstitutions = $this->fatcaReportingFinancialInstitutionRepository->findByReport($report);
            foreach ($reportingFinancialInstitutions as $reportingFinancialInstitution) {
                $reportingFinancialInstitution->setStatus($reportItemStatus);
            }
        }
        $this->entityManager->flush();
        $output->writeln('Report item statuses set');

        return 0;
    }
}
