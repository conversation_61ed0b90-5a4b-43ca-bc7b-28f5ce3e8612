<?php

declare(strict_types=1);

namespace Evp\Bundle\VmiTiesReportBundle\Command;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\VmiTiesReportBundle\Entity\Report;
use Evp\Bundle\VmiTiesReportBundle\Exception\BuilderException;
use Evp\Bundle\VmiTiesReportBundle\Repository\ReportRepository;
use Evp\Bundle\VmiTiesReportBundle\Service\CESOP\CesopReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\CESOP\ReferenceIdGenerator;
use Evp\Bundle\VmiTiesReportBundle\Service\CRSDAC2\CrsDac2ReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\FATCA\FatcaReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\FATCA\ReportingFinancialInstitutionBuilder;
use Evp\Bundle\VmiTiesReportBundle\Service\MAI55\Mai55ReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\MMRSASK\MmrSaskReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\VmiTiesServiceProvider;
use InvalidArgumentException;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\CESOP\MessageSpecType;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\CRSDAC2\Address;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\CRSDAC2\DocumentSpecification;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\CRSDAC2\MessageSpec;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\FATCA\MessageSpecType as MessageSpecTypeFatca;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\MessageSpecType as MessageSpecTypeMai55;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class CreateNewReportCommand extends Command
{
    public const VALID_MESSAGE_TYPE_INDICATORS = [
        Report::MESSAGE_TYPE_CESOP => [
            MessageSpecType::MESSAGE_TYPE_INDICATOR_CESOP_NEW,
            MessageSpecType::MESSAGE_TYPE_INDICATOR_CESOP_EDIT,
            MessageSpecType::MESSAGE_TYPE_INDICATOR_CESOP_EMPTY,
        ],
        Report::MESSAGE_TYPE_CRS_DAC2 => [
            MessageSpec::MESSAGE_TYPE_INDICATOR_NEW_DATA,
            MessageSpec::MESSAGE_TYPE_INDICATOR_CORRECTION,
            MessageSpec::MESSAGE_TYPE_INDICATOR_EMPTY,
        ],
        Report::MESSAGE_TYPE_MAI55_SLIK => [
            MessageSpecTypeMai55::MESSAGE_TYPE_NEW,
            MessageSpecTypeMai55::MESSAGE_TYPE_UPDATE,
            MessageSpecTypeMai55::MESSAGE_TYPE_NOTHING_TO_REPORT,
        ],
        Report::MESSAGE_TYPE_MAI55_SKIS => [
            MessageSpecTypeMai55::MESSAGE_TYPE_NEW,
            MessageSpecTypeMai55::MESSAGE_TYPE_UPDATE,
            MessageSpecTypeMai55::MESSAGE_TYPE_NOTHING_TO_REPORT,
        ],
        Report::MESSAGE_TYPE_MAI55_SIPL => [
            MessageSpecTypeMai55::MESSAGE_TYPE_NEW,
            MessageSpecTypeMai55::MESSAGE_TYPE_UPDATE,
            MessageSpecTypeMai55::MESSAGE_TYPE_NOTHING_TO_REPORT,
        ],
        Report::MESSAGE_TYPE_FATCA => [
            MessageSpecTypeFatca::MESSAGE_TYPE_INDICATOR_FATCA_NEW,
            MessageSpecTypeFatca::MESSAGE_TYPE_INDICATOR_FATCA_EDIT,
            MessageSpecTypeFatca::MESSAGE_TYPE_INDICATOR_FATCA_EMPTY,
        ],
        Report::MESSAGE_TYPE_MMR_SASK => [],
    ];

    private EntityManagerInterface $entityManager;
    private VmiTiesServiceProvider $vmiTiesServiceProvider;
    private ReferenceIdGenerator $referenceIdGenerator;
    private ReportRepository $reportRepository;
    private string $companyName;
    private string $companyAddress;
    private string $companyCode;
    private string $companyCountryCode;
    private ReportingFinancialInstitutionBuilder $reportingFinancialInstitutionBuilder;

    public function __construct(
        EntityManagerInterface $entityManager,
        VmiTiesServiceProvider $vmiTiesServiceProvider,
        ReferenceIdGenerator $referenceIdGenerator,
        ReportRepository $reportRepository,
        string $companyName,
        string $companyAddress,
        string $companyCode,
        string $companyCountryCode,
        ReportingFinancialInstitutionBuilder $reportingFinancialInstitutionBuilder
    ) {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->vmiTiesServiceProvider = $vmiTiesServiceProvider;
        $this->referenceIdGenerator = $referenceIdGenerator;
        $this->reportRepository = $reportRepository;
        $this->companyName = $companyName;
        $this->companyAddress = $companyAddress;
        $this->companyCode = $companyCode;
        $this->companyCountryCode = $companyCountryCode;
        $this->reportingFinancialInstitutionBuilder = $reportingFinancialInstitutionBuilder;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Create new report')
            ->addArgument('message-type', InputArgument::REQUIRED, 'Message type. Note: CESOP is PMT')
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'Report year', '2024')
            ->addOption('quarter', null, InputOption::VALUE_OPTIONAL, 'Report quarter', '1')
            ->addOption('country', null, InputOption::VALUE_OPTIONAL, 'Report country', 'LT')
            ->addOption(
                'indicator',
                null,
                InputOption::VALUE_OPTIONAL,
                'Report message type indicator. Valid indicators depend on type. Not required for MMR-SASK reports.'
            )
            ->setHelp($this->getHelpContent())
        ;
    }

    /**
     * @throws BuilderException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $messageType = $input->getArgument('message-type');
        $year = $input->getOption('year');
        $quarter = $input->getOption('quarter');
        $country = $input->getOption('country');
        $indicator = $input->getOption('indicator');

        if (!in_array($messageType, Report::MESSAGE_TYPES, true)) {
            throw new InvalidArgumentException('Invalid message type');
        }

        switch ($messageType) {
            case Report::MESSAGE_TYPE_CESOP:
                if (!$this->isIndicatorValid($messageType, $indicator)) {
                    $this->outputIndicatorError($output, $messageType);
                    return 1;
                }
                /** @var CesopReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($messageType);
                $report = $reportManager->createReport(
                    $this->referenceIdGenerator->generateReferenceId(),
                    Report::STATUS_NEW,
                    new DateTime(),
                    $indicator,
                    null,
                    sprintf('%s;%s', $year, $quarter),
                    $country
                );
                break;
            case Report::MESSAGE_TYPE_CRS_DAC2:
                if (!$this->isIndicatorValid($messageType, $indicator)) {
                    $this->outputIndicatorError($output, $messageType);
                    return 1;
                }
                $reportingPeriod = sprintf('%s-12-31', $year);

                $previousReport = $this->reportRepository->findOnePrevious($messageType, $reportingPeriod);
                if (
                    $previousReport !== null
                    && (
                        $previousReport->getStatus() === Report::STATUS_NEW
                        || $previousReport->getStatus() === Report::STATUS_PENDING
                    )
                ) {
                    throw new InvalidArgumentException('Can\'t create new report as previous is not in finished state');
                }
                /** @var CrsDac2ReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($messageType);
                $report = $reportManager->createReport(
                    null,
                    Report::STATUS_NEW,
                    new DateTime(),
                    $indicator,
                    $reportingPeriod,
                    $country
                );

                $previousFinancialInstitution = $reportManager->getPreviousReportingFinancialInstitution($reportingPeriod);
                if ($previousFinancialInstitution !== null) {
                    [$financialInstitution, $companyAddress, $companyIdentification] = $reportManager->copyReportingFinancialInstitution(
                        $report,
                        $previousFinancialInstitution
                    );
                } else {
                    $financialInstitution = $reportManager->createReportingFinancialInstitution(
                        $report,
                        null,
                        null,
                        DocumentSpecification::DOCUMENT_TYPE_INDICATOR_NEW_DATA,
                        'LT',
                        $this->companyName
                    );

                    $companyAddress = $reportManager->createAccountReportAddress(
                        $this->companyAddress,
                        Address::ADDRESS_TYPE_HEADQUARTERS,
                        $this->companyCountryCode
                    );
                    $companyAddress->setReportingFinancialInstitution($financialInstitution);

                    $companyIdentification = $reportManager->createIdentificationNumber(
                        $this->companyCode,
                        $this->companyCountryCode
                    );
                    $companyIdentification->setReportingFinancialInstitution($financialInstitution);
                }

                $this->entityManager->persist($financialInstitution);
                $this->entityManager->persist($companyAddress);
                $this->entityManager->persist($companyIdentification);
                break;
            case Report::MESSAGE_TYPE_MAI55_SIPL:
            case Report::MESSAGE_TYPE_MAI55_SKIS:
            case Report::MESSAGE_TYPE_MAI55_SLIK:
                /** @var Mai55ReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($messageType);

            if (!$this->isIndicatorValid($messageType, $indicator)) {
                $this->outputIndicatorError($output, $messageType);
                return 1;
            }

                $report = $reportManager->createReport(
                    null,
                    Report::STATUS_NEW,
                    new DateTime(),
                    $indicator ?? MessageSpecTypeMai55::MESSAGE_TYPE_NEW,
                    $year
                );
                break;
            case Report::MESSAGE_TYPE_MMR_SASK:
                if ($indicator !== null) {
                    $output->writeln('Indicator is not required for MMR-SASK, will be ignored');
                }
                /** @var MmrSaskReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($messageType);

                $previousReport = $this->reportRepository->findOnePrevious($messageType);
                if ($previousReport === null) {
                    $sequenceNumber = 1;
                } else {
                    if (
                        $previousReport->getStatus() === Report::STATUS_NEW
                        || $previousReport->getStatus() === Report::STATUS_PENDING
                    ) {
                        throw new InvalidArgumentException('Can\'t create new report as previous is not in finished state');
                    }
                    $sequenceNumber = $previousReport->getSequenceNumber() + 1;
                }
                $report = $reportManager->createReport(
                    null,
                    Report::STATUS_NEW,
                    new DateTime(),
                    $sequenceNumber
                );
                break;
            case Report::MESSAGE_TYPE_FATCA:
                if (!$this->isIndicatorValid($messageType, $indicator)) {
                    $this->outputIndicatorError($output, $messageType);
                    return 1;
                }

                /** @var FatcaReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($messageType);
                $reportingPeriod = sprintf('%s-12-31', $year);
                $report = $reportManager->createReport(
                    null,
                    Report::STATUS_NEW,
                    new DateTime(),
                    $indicator,
                    $reportingPeriod,
                    $country
                );

                [$financialInstitution, $companyAddress, $companyIdentification] = $this->reportingFinancialInstitutionBuilder->build($report);

                $this->entityManager->persist($financialInstitution);
                $this->entityManager->persist($companyAddress);
                $this->entityManager->persist($companyIdentification);
                break;
            default:
                return 1;
        }
        $this->entityManager->persist($report);
        $this->entityManager->flush();
        $output->writeln('Success');
        $output->writeln(sprintf('Report ID %s', $report->getId()));

        return 0;
    }

    private function isIndicatorValid(string $messageType, ?string $indicator): bool
    {
        return $indicator !== null
            && in_array($indicator, self::VALID_MESSAGE_TYPE_INDICATORS[$messageType], true)
        ;
    }

    private function outputIndicatorError(OutputInterface $output, string $messageType): void
    {
        $output->writeln('<error>Missing message type indicator or is not valid</error>');
        $output->writeln(
            sprintf(
                'Valid indicators: %s',
                implode(' ', self::VALID_MESSAGE_TYPE_INDICATORS[$messageType])
            )
        );
    }

    private function getHelpContent(): string
    {
        return <<<EOT
        How to prepare and deliver reports:
        - <info>CESOP</info>: https://intranet.paysera.net/display/404/VMI+CESOP+%28PMT%29+Report
        -- Tickets:
        --- 2024 Q1 - https://jira.paysera.net/browse/COMP-386
        --- 2024 Q2 - https://jira.paysera.net/browse/COMP-738
        --- 2024 Q3 - https://jira.paysera.net/browse/COMP-1096
        --- 2024 Q4 - https://jira.paysera.net/browse/COMP-1502
        - <info>CRS-DAC2</info>: https://intranet.paysera.net/display/404/VMI+CRS-DAC2+annual+report+about+non-LT+citizens+accounts
        - <info>MAI55</info>: https://intranet.paysera.net/pages/viewpage.action?pageId=*********
        - <info>FATCA</info>: https://intranet.paysera.net/display/404/VMI+FATCA-LT
        EOT;
    }
}
