<?php

declare(strict_types=1);

namespace Evp\Bundle\VmiTiesReportBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\VmiTiesReportBundle\Entity\Report;
use Evp\Bundle\VmiTiesReportBundle\Repository\ReportRepository;
use Evp\Bundle\VmiTiesReportBundle\Service\VmiTiesCompanyProvider;
use Evp\Bundle\VmiTiesReportBundle\Service\VmiTiesResponseHandler;
use InvalidArgumentException;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\StatusSti\StatusSti;
use Paysera\VmiTiesClient\VmiTiesClient\Serializer\Deserializer;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use XMLReader;

class DownloadReportCommand extends Command
{
    public const FAILED_REPORT = 'Klaidingas';
    public const REJECTED_REPORT = 'Atmestas';
    public const ACCEPTED_REPORT = 'Priimtas';
    public const PROCESSING_REPORT = 'Apdorojamas';
    public const PROVIDED_REPORT = 'Pateiktas';

    public const REPORT_STATUS_TYPE_MAP = [
        self::FAILED_REPORT => Report::STATUS_FAILED,
        self::REJECTED_REPORT => Report::STATUS_REJECTED,
        self::ACCEPTED_REPORT => Report::STATUS_DONE,
        self::PROCESSING_REPORT => Report::STATUS_PENDING,
        self::PROVIDED_REPORT => Report::STATUS_PENDING,
    ];

    private VmiTiesCompanyProvider $vmiTiesCompanyProvider;
    private ReportRepository $reportRepository;
    private Deserializer $stiDeserializer;
    private VmiTiesResponseHandler $vmiTiesResponseHandler;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    public function __construct(
        VmiTiesCompanyProvider $vmiTiesCompanyProvider,
        ReportRepository $reportRepository,
        Deserializer $stiDeserializer,
        VmiTiesResponseHandler $vmiTiesResponseHandler,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->vmiTiesCompanyProvider = $vmiTiesCompanyProvider;
        $this->reportRepository = $reportRepository;
        $this->stiDeserializer = $stiDeserializer;
        $this->vmiTiesResponseHandler = $vmiTiesResponseHandler;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('
            Downloads TIES reports. Supports CESOP, CRS-DAC2, MAI55-SIPL, MAI55-SLIK, MAI55-SKIS and MMR-SASK reports.
            Providing no report ID will download all reports that are pending depending on message-types provided.
            ')
            ->addArgument('report-id', InputArgument::OPTIONAL, 'Report Id')
            ->addOption('allow-delete', null, InputOption::VALUE_NONE, 'Should errors be deleted on next download?')
            ->addOption(
                'message-types',
                null,
                InputOption::VALUE_OPTIONAL | InputOption::VALUE_IS_ARRAY,
                'Message types to automatically process. Not used if report ID is provided.',
                [
                    Report::MESSAGE_TYPE_MMR_SASK,
                ]
            )
            ->addOption(
                'company-type',
                null,
                InputOption::VALUE_OPTIONAL,
                'For which company to download the report',
                VmiTiesCompanyProvider::VMI_COMPANY_TYPE_PAYSERA
            )
            ->addOption(
                'download-only',
                null,
                InputOption::VALUE_NONE,
                'Only download the report response, skip saving to database'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $reportId = $input->getArgument('report-id');
        $allowDelete = $input->getOption('allow-delete');
        $messageTypes = $input->getOption('message-types');
        $companyType = $input->getOption('company-type');
        $downloadOnly = $input->getOption('download-only');

        if (!in_array($companyType, VmiTiesCompanyProvider::VALID_VMI_COMPANY_TYPES, true)) {
            throw new InvalidArgumentException('Company type does not exist');
        }

        if ($reportId !== null) {
            $report = $this->reportRepository->findOneById((int)$reportId);

            if ($report === null) {
                throw new InvalidArgumentException('Failed to find report');
            }
            if ($report->getTransmissionId() === null) {
                throw new InvalidArgumentException('Report has no transmission id');
            }

            $this->downloadReport(
                $report,
                $output,
                $allowDelete,
                $companyType,
                $downloadOnly
            );
        } else {
            $reports = $this->reportRepository->findByStatus(Report::STATUS_PENDING, $messageTypes);

            foreach ($reports as $report) {
                $this->downloadReport(
                    $report,
                    $output,
                    $allowDelete,
                    $companyType,
                    $downloadOnly
                );
            }
        }

        return 0;
    }

    private function downloadReport(
        Report $report,
        OutputInterface $output,
        bool $allowDelete,
        string $companyType,
        bool $downloadOnly
    ): void {
        if ($report->getTransmissionId() === null) {
            $this->writeLog(sprintf('Report %s does not have transmission ID', $report->getId()), $output);
            return;
        }
        $this->writeLog(sprintf('Downloading report %s', $report->getTransmissionId()), $output);
        $vmiTiesClient = $this->vmiTiesCompanyProvider->getVmiTiesClient($companyType);
        $status = $vmiTiesClient->getStatus($report->getTransmissionId());

        if (is_array($status)) {
            $output->writeln('Failed to download, TIES client gave SOAP error, logging to graylog');
            $this->logger->info('TIES client gave SOAP error', array_values($status));
            return;
        }

        switch ($status->getResult()->getStatus()) {
            case self::FAILED_REPORT:
                $this->writeLog(sprintf('Report %s is invalid', $report->getTransmissionId()), $output);
                break;
            case self::REJECTED_REPORT:
                $this->writeLog(sprintf('Report %s is rejected', $report->getTransmissionId()), $output);
                break;
            case self::PROCESSING_REPORT:
                $this->writeLog(sprintf('Report %s is still processing', $report->getTransmissionId()), $output);
                break;
            case self::ACCEPTED_REPORT:
                $this->writeLog(sprintf('Report %s is accepted', $report->getTransmissionId()), $output);
                break;
            case self::PROVIDED_REPORT:
                $this->writeLog(sprintf('Report %s is provided', $report->getTransmissionId()), $output);
                break;
            default:
                $this->writeLog(sprintf('Report %s status is unknown', $report->getTransmissionId()), $output);
                return;
        }
        $payload = $status->getResult()->getPayload();

        if ($downloadOnly) {
            $vmiPayloadExtractor = $this->vmiTiesCompanyProvider->getVmiTiesPayloadExtractor($companyType);
            $vmiPayloadExtractor->extractPayloadZipFile($payload);
            return;
        }

        $report->setStatus(self::REPORT_STATUS_TYPE_MAP[$status->getResult()->getStatus()]);

        $this->entityManager->flush();

        if (empty($payload)) {
            $this->writeLog('No payload found', $output);
            if (in_array($report->getStatus(), Report::FAILED_REPORT_STATUSES, true)) {
                $this->vmiTiesResponseHandler->markAllReportItemsFailed($report);
                $this->entityManager->flush();
            }
            return;
        }

        $vmiPayloadExtractor = $this->vmiTiesCompanyProvider->getVmiTiesPayloadExtractor($companyType);
        $data = $this->getDataFromPayload($vmiPayloadExtractor->extractPayloadZipFile($payload));
        /** @var StatusSti $statusSti */
        $statusSti = $this->stiDeserializer->fromXml($data, StatusSti::class);
        $this->vmiTiesResponseHandler->handleResponse($statusSti, $report, $allowDelete);
        $this->entityManager->flush();
    }

    private function getDataFromPayload(string $data): string
    {
        $xmlReader = new XMLReader();
        $xmlReader->XML($data);

        while ($xmlReader->read()) {
            if ($xmlReader->nodeType === XMLReader::ELEMENT && strpos($xmlReader->name, 'StatusSti') !== false) {
                return $xmlReader->readOuterXml();
            }
        }

        throw new InvalidArgumentException('Failed to find StatusSti');
    }

    private function writeLog(string $text, OutputInterface $output = null): void
    {
        $this->logger->info($text);
        if ($output !== null) {
            $output->writeln($text);
        }
    }
}
