<?php

declare(strict_types=1);

namespace Evp\Bundle\VmiTiesReportBundle\Command;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Evp\Bundle\BankAccountBundle\Repository\AccountRepository;
use Evp\Bundle\VmiTiesReportBundle\Entity\CRSDAC2\AccountHolder;
use Evp\Bundle\VmiTiesReportBundle\Entity\FATCA\AccountHolder as FatcaAccountHolder;
use Evp\Bundle\VmiTiesReportBundle\Entity\FATCA\AccountReport;
use Evp\Bundle\VmiTiesReportBundle\Entity\Report;
use Evp\Bundle\VmiTiesReportBundle\Entity\ReportItem;
use Evp\Bundle\VmiTiesReportBundle\Repository\ReportRepository;
use Evp\Bundle\VmiTiesReportBundle\Service\CRSDAC2\CrsDac2ReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\FATCA\FatcaReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\MAI55\Mai55ReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\MAI55\SiplReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\MAI55\SkisReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\MAI55\SlikReportManager;
use Evp\Bundle\VmiTiesReportBundle\Service\VmiTiesServiceProvider;
use InvalidArgumentException;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\CRSDAC2\CRSDAC2;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\FATCA\FATCALT;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\FATCA\OrganisationPartyType;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\FATCA\PersonPartyType;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\MAI55MessageInterface;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\MAI55SIPL;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\MAI55SKIS;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\MAI55SLIK;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\SIPL\MessageBodyType as MessageBodyTypeSIPL;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\SKIS\MessageBodyType as MessageBodyTypeSKIS;
use Paysera\VmiTiesClient\VmiTiesClient\Entity\MAI55\SLIK\MessageBodyType as MessageBodyTypeSLIK;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ConvertXMLToReportCommand extends Command
{
    private VmiTiesServiceProvider $vmiTiesServiceProvider;
    private ReportRepository $reportRepository;
    private AccountRepository $accountRepository;
    private EntityManager $entityManager;

    public function __construct(
        VmiTiesServiceProvider $vmiTiesServiceProvider,
        ReportRepository $reportRepository,
        AccountRepository $accountRepository,
        EntityManager $entityManager
    ) {
        parent::__construct();
        $this->vmiTiesServiceProvider = $vmiTiesServiceProvider;
        $this->reportRepository = $reportRepository;
        $this->accountRepository = $accountRepository;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Converts XML to report data')
            ->addArgument('type', InputArgument::REQUIRED, 'Report type')
            ->addArgument('path', InputArgument::REQUIRED, 'Path to XML')
            ->addArgument('status', InputArgument::REQUIRED, 'Report status')
            ->addOption('transmission_id', null, InputOption::VALUE_OPTIONAL, 'Transmission id from VMI')
            ->addOption('dry-run', 'd', InputOption::VALUE_NONE, 'Do not commit the db transaction')
        ;
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $type = $input->getArgument('type');
        $path = $input->getArgument('path');
        $status = $input->getArgument('status');

        if (!in_array($type, Report::MESSAGE_TYPES, true)) {
            throw new InvalidArgumentException('Invalid report type provided');
        }

        if (!in_array($status, Report::STATUSES, true)) {
            throw new InvalidArgumentException('Invalid report status provided');
        }

        $deserializer = $this->vmiTiesServiceProvider->getDeserializer($type);
        $progressBar = new ProgressBar($output);

        switch ($type) {
            case Report::MESSAGE_TYPE_MAI55_SIPL:
            case Report::MESSAGE_TYPE_MAI55_SLIK:
            case Report::MESSAGE_TYPE_MAI55_SKIS:
                if ($type === Report::MESSAGE_TYPE_MAI55_SIPL) {
                    /** @var MAI55SIPL $message */
                    $message = $deserializer->fromXml(file_get_contents($path), MAI55SIPL::class);
                } elseif ($type === Report::MESSAGE_TYPE_MAI55_SLIK) {
                    /** @var MAI55SLIK $message */
                    $message = $deserializer->fromXml(file_get_contents($path), MAI55SLIK::class);
                } elseif ($type === Report::MESSAGE_TYPE_MAI55_SKIS) {
                    /** @var MAI55SKIS $message */
                    $message = $deserializer->fromXml(file_get_contents($path), MAI55SKIS::class);
                } else {
                    throw new InvalidArgumentException('Unimplemented type');
                }

                /** @var Mai55ReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($type);
                $messageSpec = $message->getMessageSpec();
                $report = $this->reportRepository->findOneByReferenceId($messageSpec->getMessageRefId());
                if ($report !== null) {
                    $output->writeln('Report creation aborted as report already exists');
                    return 1;
                }

                $report = $reportManager->createReport(
                    $messageSpec->getMessageRefId(),
                    $status,
                    $messageSpec->getTimestamp(),
                    $messageSpec->getMessageTypeIndic(),
                    $messageSpec->getReportingPeriod()->getStartDate()->format('Y')
                );
                $this->entityManager->persist($report);

                $this->convertMai55($message, $reportManager, $report, $progressBar, $status);
                break;
            case Report::MESSAGE_TYPE_CRS_DAC2:
                /** @var CRSDAC2 $message */
                $message = $deserializer->fromXml(file_get_contents($path), CRSDAC2::class);
                /** @var CrsDac2ReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($type);
                $messageSpec = $message->getMessageSpec();
                $report = $this->reportRepository->findOneByReferenceId($messageSpec->getMessageReferenceId());
                if ($report !== null) {
                    $output->writeln('Report creation aborted as report already exists');
                    return 1;
                }

                $report = $reportManager->createReport(
                    $messageSpec->getMessageReferenceId(),
                    $status,
                    $messageSpec->getTimestamp(),
                    $messageSpec->getMessageTypeIndicator(),
                    $messageSpec->getReportingPeriod(),
                    $messageSpec->getReceivingCountry()
                );
                $this->entityManager->persist($report);

                $reportingFinnanceInstitute = $message->getMessageBody()->getReportingFinnanceInstitute();
                $reportingFinancialInstitution = $reportManager->createReportingFinancialInstitution(
                    $report,
                    $reportingFinnanceInstitute->getDocumentSpecification()->getDocumentReferenceId(),
                    $reportingFinnanceInstitute->getDocumentSpecification()->getCorrectionDocumentReferenceId(),
                    $reportingFinnanceInstitute->getDocumentSpecification()->getDocumentTypeIndicator(),
                    $reportingFinnanceInstitute->getResCountryCode(),
                    $reportingFinnanceInstitute->getName()
                );
                $reportingFinancialInstitution->setStatus($this->resolveReportItemStatus($status));
                $this->entityManager->persist($reportingFinancialInstitution);

                foreach ($reportingFinnanceInstitute->getAddresses() as $address) {
                    $reportAddress =  $reportManager->createAccountReportAddress(
                        $address->getAddressFree(),
                        $address->getLegalAddressType(),
                        $address->getCountryCode()
                    );
                    $reportAddress->setReportingFinancialInstitution($reportingFinancialInstitution);
                    $this->entityManager->persist($reportAddress);
                }
                $identificationNumber = $reportManager->createIdentificationNumber(
                    $reportingFinnanceInstitute->getIdentificationNumber()->getNumber(),
                    $reportingFinnanceInstitute->getIdentificationNumber()->getIssuedByCountryCode()
                );
                $identificationNumber->setReportingFinancialInstitution($reportingFinancialInstitution);
                $this->entityManager->persist($identificationNumber);

                $progressBar->start(count($message->getMessageBody()->getReportingGroup()));
                foreach ($message->getMessageBody()->getReportingGroup() as $accountReport) {
                    $account = $this->accountRepository->findOneByIban($accountReport->getAccountNumber()->getIban());

                    $accountReportEntity = $reportManager->createAccountReport(
                        $accountReport->getDocumentSpecification()->getDocumentReferenceId(),
                        $this->resolveReportItemStatus($status),
                        $messageSpec->getTimestamp(),
                        $accountReport->getDocumentSpecification()->getDocumentTypeIndicator(),
                        $accountReport->getDocumentSpecification()->getCorrectionDocumentReferenceId(),
                        $report,
                        $account,
                        $accountReport->getAccountNumber()->getIban(),
                        $accountReport->getAccountBalance()->getAmount(),
                        $accountReport->getAccountBalance()->getCurrency(),
                        $accountReport->getAccountNumber()->isDormantAccount(),
                        $accountReport->getAccountNumber()->isClosedAccount()
                    );
                    $this->entityManager->persist($accountReportEntity);

                    if ($accountReport->getAccountHolder()->getIndividual() !== null) {
                        $individual = $accountReport->getAccountHolder()->getIndividual();
                        $accountHolderEntity = $reportManager->createAccountHolder(
                            $accountReportEntity,
                            AccountHolder::TYPE_INDIVIDUAL,
                            $individual->getResCountryCode(),
                            false,
                            $individual->getName()->getNameType(),
                            $individual->getName()->getFirstName(),
                            $individual->getName()->getLastName(),
                            null,
                            $individual->getBirthInfo() !== null ? $individual->getBirthInfo()->getBirthDate() : null,
                            $accountReport->getAccountHolder()->getAccountHolderType(),
                            null
                        );
                        $this->entityManager->persist($accountHolderEntity);
                        foreach ($individual->getIdentificationNumbers() as $identificationNumber) {
                            $identificationNumberEntity = $reportManager->createIdentificationNumber(
                                $identificationNumber->getNumber(),
                                $identificationNumber->getIssuedByCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($identificationNumberEntity);
                       }
                        foreach ($individual->getAddresses() as $address) {
                            $addressEntity = $reportManager->createAccountReportAddress(
                                $address->getAddressFree(),
                                $address->getLegalAddressType(),
                                $address->getCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($addressEntity);
                        }
                    }
                    if ($accountReport->getAccountHolder()->getOrganisation() !== null) {
                        $organisation = $accountReport->getAccountHolder()->getOrganisation();
                        $accountHolderEntity = $reportManager->createAccountHolder(
                            $accountReportEntity,
                            AccountHolder::TYPE_ORGANISATION,
                            $organisation->getResCountryCode(),
                            false,
                            $organisation->getOrganisationName()->getNameType(),
                            null,
                            null,
                            $organisation->getOrganisationName()->getName(),
                            null,
                            $accountReport->getAccountHolder()->getAccountHolderType(),
                            null
                        );
                        $this->entityManager->persist($accountHolderEntity);
                        foreach ($organisation->getIdentificationNumbers() as $identificationNumber) {
                            $identificationNumberEntity = $reportManager->createIdentificationNumber(
                                $identificationNumber->getNumber(),
                                $identificationNumber->getIssuedByCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($identificationNumberEntity);
                        }
                        foreach ($organisation->getAddresses() as $address) {
                            $addressEntity = $reportManager->createAccountReportAddress(
                                $address->getAddressFree(),
                                $address->getLegalAddressType(),
                                $address->getCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($addressEntity);
                        }
                    }
                    if (
                        $accountReport->getControllingPerson() !== null
                        && $accountReport->getControllingPerson()->getIndividual() !== null
                    ) {
                        $controllingPerson = $accountReport->getControllingPerson();
                        $individual = $accountReport->getControllingPerson()->getIndividual();
                        $accountHolderEntity = $reportManager->createAccountHolder(
                            $accountReportEntity,
                            AccountHolder::TYPE_INDIVIDUAL,
                            $individual->getResCountryCode(),
                            true,
                            $individual->getName()->getNameType(),
                            $individual->getName()->getFirstName(),
                            $individual->getName()->getLastName(),
                            null,
                            $individual->getBirthInfo() !== null ? $individual->getBirthInfo()->getBirthDate() : null,
                            null,
                            $controllingPerson->getControllingPersonType()
                        );
                        $this->entityManager->persist($accountHolderEntity);
                        foreach ($individual->getIdentificationNumbers() as $identificationNumber) {
                            $identificationNumberEntity = $reportManager->createIdentificationNumber(
                                $identificationNumber->getNumber(),
                                $identificationNumber->getIssuedByCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($identificationNumberEntity);
                        }
                        foreach ($individual->getAddresses() as $address) {
                            $addressEntity = $reportManager->createAccountReportAddress(
                                $address->getAddressFree(),
                                $address->getLegalAddressType(),
                                $address->getCountryCode(),
                                $accountHolderEntity
                            );
                            $this->entityManager->persist($addressEntity);
                        }
                    }
                    $progressBar->advance();
                }
                break;
            case Report::MESSAGE_TYPE_FATCA:
                /** @var FATCALT $message */
                $message = $deserializer->fromXml(file_get_contents($path), FATCALT::class);
                /** @var FatcaReportManager $reportManager */
                $reportManager = $this->vmiTiesServiceProvider->getReportManager($type);
                $messageSpec = $message->getMessageSpec();
                $report = $this->reportRepository->findOneByReferenceId($messageSpec->getMessageRefId());
                if ($report !== null) {
                    $output->writeln('Report creation aborted as report already exists');
                    return 1;
                }
                $report = $reportManager->createReport(
                    $messageSpec->getMessageRefId(),
                    $status,
                    $messageSpec->getTimestamp(),
                    $messageSpec->getMessageTypeIndic(),
                    $messageSpec->getReportingPeriod()->getEndDate()->format('Y-m-d'),
                );
                $this->entityManager->persist($report);

                $this->convertFatca($message, $reportManager, $report, $progressBar, $status);
                break;
            default:
                return 1;
        }

        if ($input->getOption('transmission_id') !== null) {
            $report->setTransmissionId($input->getOption('transmission_id'));
        }

        $progressBar->finish();
        $output->writeln('');

        if (!$input->getOption('dry-run')) {
            $this->entityManager->flush();
        }

        $output->writeln('Finished');
        return 0;
    }

    private function resolveReportItemStatus(string $reportStatus): string
    {
        if (in_array($reportStatus, Report::FAILED_REPORT_STATUSES, true)) {
            return ReportItem::STATUS_FAILED;
        }
        if ($reportStatus === Report::STATUS_PENDING) {
            return ReportItem::STATUS_PENDING;
        }
        if ($reportStatus === Report::STATUS_NEW) {
            return ReportItem::STATUS_NEW;
        }
        if ($reportStatus === Report::STATUS_DONE) {
            return ReportItem::STATUS_DONE;
        }

        throw new InvalidArgumentException('Failed to resolve report item status from report status');
    }

    /**
     * @throws ORMException
     */
    private function convertFatca(
        FATCALT $message,
        FatcaReportManager $reportManager,
        Report $report,
        ProgressBar $progressBar,
        string $status
    ): void {
        $messageBody = $message->getMessageBody();
        if ($messageBody === null) {
            return;
        }
        $reportingFinancialInstitution = $messageBody->getReportingFI();
        if ($reportingFinancialInstitution !== null) {
            $financialInstitution = $reportManager->createReportingFinancialInstitution(
                $report,
                $reportingFinancialInstitution->getDocSpec()->getDocRefId(),
                $reportingFinancialInstitution->getDocSpec()->getDocTypeIndic()
            );
            $financialInstitution->setStatus($this->resolveReportItemStatus($status));
            $financialInstitution->setCorrectionReferenceId($reportingFinancialInstitution->getDocSpec()->getCorrDocRefId());

            $previousAddress = $reportingFinancialInstitution->getAddresses()[0];
            $companyAddress = $reportManager->createAccountReportAddress(
                $previousAddress->getAddressFree(),
                $previousAddress->getLegalAddressType(),
                $previousAddress->getCountryCode()
            );
            $companyAddress->setReportingFinancialInstitution($financialInstitution);

            $previousIdentification = $reportingFinancialInstitution->getTins()[0];
            $companyIdentification = $reportManager->createIdentificationNumber(
                $previousIdentification->getTin(),
                $previousIdentification->getIssuedBy()
            );
            $companyIdentification->setReportingFinancialInstitution($financialInstitution);

            $this->entityManager->persist($financialInstitution);
            $this->entityManager->persist($companyAddress);
            $this->entityManager->persist($companyIdentification);
        }

        foreach ($messageBody->getReportingGroup() as $accountReport) {
            $progressBar->advance();
            $account = $this->accountRepository->findOneByIban($accountReport->getAccountNumber()->getAccountNumber());

            if ($account === null) {
                continue;
            }

            $accountReportEntity = $reportManager->createAccountReport(
                $accountReport->getDocSpec()->getDocRefId(),
                $this->resolveReportItemStatus($status),
                $message->getMessageSpec()->getTimestamp(),
                $accountReport->getDocSpec()->getDocTypeIndic(),
                $accountReport->getDocSpec()->getCorrDocRefId(),
                $report,
                $account,
                $accountReport->getAccountNumber()->getAccountNumber(),
                (string)$accountReport->getAccountBalance()->getAmount(),
                $accountReport->getAccountBalance()->getCurrencyCode(),
                $accountReport->getAccountNumber()->getClosedAccount()
            );

            $accountHolder = $accountReport->getAccountHolder();
            if ($accountHolder->getIndividual() !== null) {
                $this->convertFatcaIndividual(
                    $reportManager,
                    $accountReportEntity,
                    $accountHolder->getIndividual(),
                    false
                );
            } elseif ($accountHolder->getOrganisation() !== null) {
                $this->convertFatcaOrganisation(
                    $reportManager,
                    $accountReportEntity,
                    $accountHolder->getOrganisation(),
                    $accountHolder->getAcctHolderType(),
                    false
                );
            } else {
                continue;
            }
            $substantialOwners = $accountReport->getSubstantialOwner();
            foreach ($substantialOwners as $substantialOwner) {
                if ($substantialOwner->getIndividual() !== null) {
                    $this->convertFatcaIndividual(
                        $reportManager,
                        $accountReportEntity,
                        $substantialOwner->getIndividual(),
                        true
                    );
                } elseif ($substantialOwner->getOrganisation() !== null) {
                     $this->convertFatcaOrganisation(
                        $reportManager,
                        $accountReportEntity,
                        $substantialOwner->getOrganisation(),
                        $accountHolder->getAcctHolderType(),
                        true
                    );
                }
            }

            $this->entityManager->persist($accountReportEntity);
        }
    }

    /**
     * @throws ORMException
     */
    private function convertFatcaIndividual(
        FatcaReportManager $reportManager,
        AccountReport $accountReportEntity,
        PersonPartyType $individual,
        bool $isSubstantialOwner
    ): void {
        $residenceCountryCodes = $individual->getResCountryCodes();
        $names = $individual->getNames();
        $birthDate = $individual->getBirthInfo()->getBirthDate();
        $accountHolderEntity = $reportManager->createAccountHolder(
            $accountReportEntity,
            FatcaAccountHolder::TYPE_INDIVIDUAL,
            $residenceCountryCodes[0],
            $isSubstantialOwner,
            $names[0]->getNameType(),
            $names[0]->getFirstName(),
            $names[0]->getLastName(),
            null,
            $birthDate->format('Y-m-d'),
            null
        );
        $this->entityManager->persist($accountHolderEntity);
        $tins = $individual->getTins();
        foreach ($tins as $tin) {
            $identificationNumber = $reportManager->createIdentificationNumber(
                $tin->getTin(),
                $tin->getIssuedBy(),
                $accountHolderEntity
            );
            $this->entityManager->persist($identificationNumber);
        }
        $addresses = $individual->getAddresses();
        foreach ($addresses as $address) {
            $addressEntity = $reportManager->createAccountReportAddress(
                $address->getAddressFree(),
                $address->getLegalAddressType(),
                $address->getCountryCode(),
                $accountHolderEntity
            );
            $this->entityManager->persist($addressEntity);
        }
    }

    /**
     * @throws ORMException
     */
    private function convertFatcaOrganisation(
        FatcaReportManager $reportManager,
        AccountReport $accountReportEntity,
        OrganisationPartyType $organisation,
        string $acctHolderType,
        bool $isSubstantialOwner
    ): void {
        $residenceCountryCodes = $organisation->getResCountryCodes();
        $names = $organisation->getNames();
        $accountHolderEntity = $reportManager->createAccountHolder(
            $accountReportEntity,
            FatcaAccountHolder::TYPE_ORGANISATION,
            $residenceCountryCodes[0],
            $isSubstantialOwner,
            $names[0]->getNameType(),
            null,
            null,
            $names[0]->getName(),
            null,
            $acctHolderType
        );
        $this->entityManager->persist($accountHolderEntity);
        $tins = $organisation->getTins();
        foreach ($tins as $tin) {
            $identificationNumber = $reportManager->createIdentificationNumber(
                $tin->getTin(),
                $tin->getIssuedBy(),
                $accountHolderEntity
            );
            $this->entityManager->persist($identificationNumber);
        }
        $addresses = $organisation->getAddresses();
        foreach ($addresses as $address) {
            $addressEntity = $reportManager->createAccountReportAddress(
                $address->getAddressFree(),
                $address->getLegalAddressType(),
                $address->getCountryCode(),
                $accountHolderEntity
            );
            $this->entityManager->persist($addressEntity);
        }
    }

    /**
     * @throws ORMException
     */
    private function convertMai55(
        MAI55MessageInterface $message,
        Mai55ReportManager $reportManager,
        Report $report,
        ProgressBar $progressBar,
        string $status
    ): void {
        $messageBody = $message->getMessageBody();
        if ($messageBody === null) {
            return;
        }
        foreach ($messageBody->getReportablePersons() as $reportablePerson) {
            $reportablePersonEntity = $reportManager->createReportablePerson(
                $reportablePerson->getDocSpec()->getDocRefId()
            );
            $reportablePersonEntity
                ->setReport($report)
                ->setStatus($this->resolveReportItemStatus($status))
                ->setCorrectionReferenceId($reportablePerson->getDocSpec()->getCorrDocRefId())
                ->setDocumentTypeIndicator($reportablePerson->getDocSpec()->getDocTypeIndic())
            ;
            if ($reportablePerson->getReportableIndividual() !== null) {
                $reportablePersonEntity
                    ->setFirstName($reportablePerson->getReportableIndividual()->getFirstName())
                    ->setLastName($reportablePerson->getReportableIndividual()->getLastName())
                ;
                if ($reportablePerson->getReportableIndividual()->getPersonCode() !== null) {
                    $reportablePersonEntity
                        ->setPersonCode((string)$reportablePerson->getReportableIndividual()->getPersonCode())
                    ;
                }
                $otherIdentificationNumber = $reportablePerson->getReportableIndividual()->getOtherIdentificationNumber();
                if ($otherIdentificationNumber !== null && $otherIdentificationNumber->getPIN() !== null) {
                    $reportablePersonEntity
                        ->setIdentificationCode($otherIdentificationNumber->getPIN()->value())
                        ->setIdentificationCodeType($otherIdentificationNumber->getPIN()->getPINType())
                        ->setIdentificationCodeIssuerCountry($otherIdentificationNumber->getPIN()->getIssuedBy())
                    ;
                }
            }

            if ($messageBody instanceof MessageBodyTypeSIPL && $reportManager instanceof SiplReportManager) {
                if ($reportablePerson->getReportableOrganisation() !== null) {
                    $organisation = $reportablePerson->getReportableOrganisation();
                    $reportablePersonEntity
                        ->setName($organisation->getName())
                    ;
                    if ($organisation->getIN() !== null) {
                        $reportablePersonEntity
                            ->setIdentificationCode((string)$organisation->getIN()->value())
                            ->setIdentificationCodeIssuerCountry($organisation->getIN()->getIssuedBy())
                        ;
                    }
                }
                $reportablePersonEntity->setTotal((string)$reportablePerson->getTotalIncomeAmount());
                foreach ($reportablePerson->getAccount() as $account) {
                    $siplAccount = $reportManager->createSiplAccount($reportablePersonEntity);
                    $siplAccount
                        ->setAccountNumber($account->getAccountNumber())
                        ->setIncomeAmount((string)$account->getIncomeAmount())
                        ->setAccountType($account->getAccountType())
                    ;
                    $this->entityManager->persist($siplAccount);
                }
            } elseif ($messageBody instanceof MessageBodyTypeSLIK && $reportManager instanceof SlikReportManager) {
                $reportablePersonEntity->setTotal((string)$reportablePerson->getTotalAccountBalance());
                foreach ($reportablePerson->getAccount() as $account) {
                    $slikAccount = $reportManager->createSlikAccount($reportablePersonEntity);
                    $slikAccount
                        ->setAccountNumber($account->getAccountNumber())
                        ->setAccountBalance((string)$account->getAccountBalance())
                        ->setAccountType($account->getAccountType())
                    ;
                    $this->entityManager->persist($slikAccount);
                }
            } elseif ($messageBody instanceof MessageBodyTypeSKIS && $reportManager instanceof SkisReportManager) {
                $reportablePersonEntity->setTotal((string)$reportablePerson->getTotalDebtAmount());
                foreach ($reportablePerson->getDebt() as $debt) {
                    $skisDebt = $reportManager->createSkisDebt($reportablePersonEntity);
                    $skisDebt
                        ->setDebtKind($debt->getDebtKind())
                        ->setDebtFactAmount((string)$debt->getDebtFactAmount())
                        ->setDebtAmount((string)$debt->getDebtAmount())
                        ->setAgreementNumber($debt->getAgreementNumber())
                        ->setAgreementDate($debt->getAgreementDate())
                        ->setInterestRate((string)$debt->getInterestRate())
                        ->setJointLiability((string)$debt->getJointLiability())
                        ->setRepaymentTerm($debt->getRepaymentTerm())
                        ->setRepaymentType($debt->getRepaymentType())
                    ;
                    $this->entityManager->persist($skisDebt);
                }
            }
            $this->entityManager->persist($reportablePersonEntity);
            $progressBar->advance();
        }
    }
}
