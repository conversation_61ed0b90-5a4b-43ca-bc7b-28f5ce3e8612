<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Entity;

use DateTimeImmutable;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Component\Money\Money;

class BalanceReportLog
{
    public const DEFAULT_CURRENCY = 'EUR';

    public const STATUS_PENDING = 'pending';
    public const STATUS_QUEUED = 'queued';
    public const STATUS_PUBLISHED = 'published';
    public const STATUS_CONFIRMED = 'confirmed';
    public const STATUS_SKIPPED = 'skipped';
    public const STATUS_FAILED = 'failed';
    public const STATUS_NOT_VALID = 'not_valid';

    public const ACCEPTABLE_LAST_STATUSES_FOR_UPDATE = [
        self::STATUS_PENDING,
        self::STATUS_NOT_VALID,
    ];
    public const PENDING_CONFIRMATION_STATUSES = [
        self::STATUS_QUEUED,
        self::STATUS_PUBLISHED,
    ];

    public const DETAILS_CLIENT_CODE_NOT_FOUND = 'client_code_not_found';
    public const DETAILS_IDENTITY_DOCUMENT_NOT_FOUND = 'identity_document_not_found';
    public const DETAILS_DOM_EXCEPTION = 'dom_exception';
    public const DETAILS_ARREST_NOT_FOUND = 'arrest_not_found';
    public const DETAILS_CREATED_ANOTHER_DURING_WAITING_CONFIRMATION = 'created_another_during_waiting_confirmation';
    public const DETAILS_CREATED_ANOTHER_DURING_FAILED = 'created_another_during_failed';

    private int $id;
    private DateTimeImmutable $createdAt;
    private DateTimeImmutable $updatedAt;
    private Account $account;
    private string $balanceAmount;
    private string $balanceCurrency;
    private string $freeDispositionAmount;
    private string $freeDispositionCurrency;
    private string $chargeAmount;
    private string $chargeCurrency;
    private string $status;
    private ?string $documentId;
    private array $details;

    public function __construct(string $currency = self::DEFAULT_CURRENCY)
    {
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = new DateTimeImmutable();
        $this->status = self::STATUS_PENDING;
        $this->balanceAmount = '0';
        $this->balanceCurrency = $currency;
        $this->freeDispositionAmount = '0';
        $this->freeDispositionCurrency = $currency;
        $this->chargeAmount = '0';
        $this->chargeCurrency = $currency;
        $this->details = [];
        $this->documentId = null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function setAccount(Account $account): BalanceReportLog
    {
        $this->account = $account;

        return $this;
    }

    public function getBalance(): Money
    {
        return new Money($this->balanceAmount, $this->balanceCurrency);
    }

    public function setBalance(Money $balance): BalanceReportLog
    {
        $this->balanceAmount = $balance->getAmount();
        $this->balanceCurrency = $balance->getCurrency();

        return $this;
    }

    public function addBalance(Money $balance): BalanceReportLog
    {
        $balance = (new Money($this->balanceAmount, $this->balanceCurrency))->add($balance);
        $this->balanceAmount = $balance->getAmount();
        $this->balanceCurrency = $balance->getCurrency();

        return $this;
    }

    public function subtractBalance(Money $balance): BalanceReportLog
    {
        $balance = (new Money($this->balanceAmount, $this->balanceCurrency))->sub($balance);
        $this->balanceAmount = $balance->getAmount();
        $this->balanceCurrency = $balance->getCurrency();

        return $this;
    }

    public function getFreeDispositionAmount(): Money
    {
        return new Money($this->freeDispositionAmount, $this->freeDispositionCurrency);
    }

    public function setFreeDispositionAmount(Money $freeDispositionAmount): BalanceReportLog
    {
        $this->freeDispositionAmount = $freeDispositionAmount->getAmount();
        $this->freeDispositionCurrency = $freeDispositionAmount->getCurrency();

        return $this;
    }

    public function getChargeAmount(): Money
    {
        return new Money($this->chargeAmount, $this->chargeCurrency);
    }

    public function setChargeAmount(Money $chargeAmount): BalanceReportLog
    {
        $this->chargeAmount = $chargeAmount->getAmount();
        $this->chargeCurrency = $chargeAmount->getCurrency();

        return $this;
    }

    public function addChargeAmount(Money $chargeAmount): BalanceReportLog
    {
        $chargeAmount = (new Money($this->chargeAmount, $this->chargeCurrency))->add($chargeAmount);
        $this->chargeAmount = $chargeAmount->getAmount();
        $this->chargeCurrency = $chargeAmount->getCurrency();

        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): BalanceReportLog
    {
        $this->status = $status;

        return $this;
    }

    public function getDocumentId(): ?string
    {
        return $this->documentId;
    }

    public function setDocumentId(string $documentId): BalanceReportLog
    {
        $this->documentId = $documentId;

        return $this;
    }

    public function getDetails(): array
    {
        return $this->details;
    }

    public function addDetails(string $details): BalanceReportLog
    {
        $this->details[] = $details;

        return $this;
    }
}
