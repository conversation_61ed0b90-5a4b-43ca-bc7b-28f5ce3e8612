<?php

namespace Evp\Bundle\PlaisBundle\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;

class SkippedIban
{
    private string $iban;

    /**
     * @var ArrayCollection|SkippedDocument[]
     */
    private $skippedDocuments;

    private DateTimeImmutable $createdAt;

    public function __construct()
    {
        $this->skippedDocuments = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function getIban(): string
    {
        return $this->iban;
    }

    public function setIban(string $iban): self
    {
        $this->iban = $iban;
        return $this;
    }

    /**
     * @return ArrayCollection|SkippedDocument[]
     */
    public function getSkippedDocuments()
    {
        return $this->skippedDocuments;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }
}
