<?php

namespace Evp\Bundle\PlaisBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Component\Money\Money;

class Arrest
{
    const STATUS_NEW = 'new';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_DONE = 'done';

    const TYPE_CHARGE = 'charge';
    const TYPE_ARREST = 'arrest';

    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $status;

    /**
     * @var string
     */
    private $comment;

    /**
     * @var string
     */
    private $arrestIdentification;

    /**
     * @var float|null
     */
    private $arrestAmount;

    /**
     * @var string|null
     */
    private $arrestCurrency;

    /**
     * @var \DateTime
     */
    private $createdAt;

    /**
     * @var Hold[]|ArrayCollection
     */
    private $holds;

    /**
     * @var Transfer[]|ArrayCollection
     */
    private $transfers;

    /**
     * @var Account
     */
    private $account;

    /**
     * @var string
     */
    private $type;

    /**
     * @var float|null
     */
    private $freeDispositionAmount;

    /**
     * @var string
     */
    private $freeDispositionComment;

    public function __construct()
    {
        $this->status = self::STATUS_NEW;
        $this->createdAt = new \DateTime();
        $this->transfers = new ArrayCollection();
        $this->holds = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * @param string $comment
     *
     * @return $this
     */
    public function setComment($comment)
    {
        $this->comment = $comment;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param string $status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getArrestIdentification()
    {
        return $this->arrestIdentification;
    }

    /**
     * @param string $arrestIdentification
     *
     * @return $this
     */
    public function setArrestIdentification($arrestIdentification)
    {
        $this->arrestIdentification = $arrestIdentification;
        return $this;
    }

    /**
     * @param Money|null $arrestAmount
     */
    public function setArrestAmount(Money $arrestAmount = null)
    {
        if ($arrestAmount === null) {
            $this->arrestAmount = null;
            $this->arrestCurrency = null;
        } else {
            $this->arrestAmount = $arrestAmount->getAmount();
            $this->arrestCurrency = $arrestAmount->getCurrency();
        }
    }

    /**
     * @return Money|null
     */
    public function getArrestAmount()
    {
        return $this->arrestAmount !== null && $this->arrestCurrency !== null
            ? new Money($this->arrestAmount, $this->arrestCurrency)
            : null
        ;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     *
     * @return $this
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return ArrayCollection|Hold[]
     */
    public function getHolds()
    {
        return $this->holds;
    }

    /**
     * @param ArrayCollection|Hold[] $holds
     *
     * @return $this
     */
    public function setHolds($holds)
    {
        $this->holds = $holds;
        return $this;
    }

    /**
     * @return ArrayCollection|Transfer[]
     */
    public function getTransfers()
    {
        return $this->transfers;
    }

    /**
     * @param ArrayCollection|Transfer[] $transfers
     *
     * @return $this
     */
    public function setTransfers($transfers)
    {
        $this->transfers = $transfers;
        return $this;
    }

    /**
     * @return Account
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * @param Account $account
     * @return $this
     */
    public function setAccount($account)
    {
        $this->account = $account;
        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @param Money|null $freeDispositionAmount
     * @return $this
     */
    public function setFreeDispositionAmount(Money $freeDispositionAmount = null)
    {
        if ($freeDispositionAmount === null) {
            $this->freeDispositionAmount = null;
        } else {
            $this->freeDispositionAmount = $freeDispositionAmount->getAmount();
            $this->arrestCurrency = $freeDispositionAmount->getCurrency();
        }
        return $this;
    }

    /**
     * @return Money|null
     */
    public function getFreeDispositionAmount()
    {
        return $this->freeDispositionAmount !== null && $this->arrestCurrency !== null
            ? new Money($this->freeDispositionAmount, $this->arrestCurrency)
            : null
        ;
    }

    /**
     * @return string
     */
    public function getFreeDispositionComment()
    {
        return $this->freeDispositionComment;
    }

    /**
     * @param string $freeDispositionComment
     * @return $this
     */
    public function setFreeDispositionComment($freeDispositionComment)
    {
        $this->freeDispositionComment = $freeDispositionComment;
        return $this;
    }
}
