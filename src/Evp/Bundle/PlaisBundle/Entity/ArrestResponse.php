<?php

namespace Evp\Bundle\PlaisBundle\Entity;

use DateTime;
use Evp\Component\Money\Money;

class ArrestResponse
{
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_NOT_VALID = 'not_valid';
    const STATUS_RECREATED = 'recreated';
    const STATUS_CONFIRMED = 'confirmed';

    const RESPONSE_STATUS_SUCCESS = 'success';
    const RESPONSE_STATUS_FAILED = 'failed';

    const TYPE_BLOCK = 'BLOCK';
    const TYPE_CCTINIT = 'CCTINIT';
    const TYPE_CARDCHN = 'CARDCHN';
    const TYPE_CARDCAN = 'CARDCAN';
    const TYPE_NS = 'NS';

    const BUSINESS_AREA_PLAIS = 'PLAIS';
    const BUSINESS_AREA_PLAI_BLOCK = 'PlaiBlock';
    const BUSINESS_AREA_PLAI_CHANGE = 'PlaiChange';
    const BUSINESS_AREA_PLAI_NS = 'PlaiNS';

    const ERROR_NO_ID = 'NoID';
    const ERROR_NO_ACCOUNT = 'NoAccount';
    const ERROR_PAYER_ERROR = 'PayerError';
    const ERROR_ILLEGAL_ACCOUNT_TYPE = 'IllegalAccountType';
    const ERROR_PAYEE_ERROR = 'PayeeError';
    const ERROR_NO_FUNDS = 'NoFunds';
    const ERROR_ILLEGAL_CURRENCY = 'IllegalCurrency';
    const ERROR_NOT_UNIQUE = 'NotUnique';
    const ERROR_UNKNOWN_ERROR = 'UnknownError';
    const ERROR_FORMAT_ERROR = 'FormatError';
    const ERROR_ILLEGAL_AMOUNT = 'IllegalAmount';
    const ERROR_CLOSED_ID = 'ClosedID';

    /**
     * @var int
     */
    private $id;

    /**
     * @var Arrest
     */
    private $arrest;

    /**
     * @var string
     */
    private $type;

    /**
     * @var DateTime
     */
    private $createdAt;

    /**
     * @var DateTime
     */
    private $updatedAt;

    /**
     * @var string
     */
    private $responseStatus;

    /**
     * @var string
     */
    private $status;

    /**
     * @var string
     */
    private $docId;

    /**
     * @var string
     */
    private $referenceDocId;

    /**
     * Only if type CCTINIT
     *
     * @var string
     */
    private $endToEndId;

    /**
     * Only if type CCTINIT and transfer amount lower than requested
     *
     * @var string|null
     */
    private $amount;

    /**
     * Only if type CCTINIT and transfer amount lower than requested
     *
     * @var string|null
     */
    private $currency;

    /**
     * @var string
     */
    private $rejectReason;

    /**
     * @var string
     */
    private $referenceSenderBic;

    public function __construct()
    {
        $this->setCreatedAt(new DateTime());
        $this->setStatus(self::STATUS_PENDING);
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return Arrest
     */
    public function getArrest()
    {
        return $this->arrest;
    }

    /**
     * @param Arrest $arrest
     * @return $this
     */
    public function setArrest($arrest)
    {
        $this->arrest = $arrest;
        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param DateTime $createdAt
     * @return $this
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }

    /**
     * @param DateTime $updatedAt
     * @return $this
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * @return string
     */
    public function getResponseStatus()
    {
        return $this->responseStatus;
    }

    /**
     * @param string $responseStatus
     * @return $this
     */
    public function setResponseStatus($responseStatus)
    {
        $this->responseStatus = $responseStatus;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string
     */
    public function getDocId()
    {
        return $this->docId;
    }

    /**
     * @param string $docId
     * @return $this
     */
    public function setDocId($docId)
    {
        $this->docId = $docId;
        return $this;
    }

    /**
     * @return string
     */
    public function getReferenceDocId()
    {
        return $this->referenceDocId;
    }

    /**
     * @param string $referenceDocId
     * @return $this
     */
    public function setReferenceDocId($referenceDocId)
    {
        $this->referenceDocId = $referenceDocId;
        return $this;
    }

    /**
     * @return string
     */
    public function getEndToEndId()
    {
        return $this->endToEndId;
    }

    /**
     * @param string $endToEndId
     * @return $this
     */
    public function setEndToEndId($endToEndId)
    {
        $this->endToEndId = $endToEndId;
        return $this;
    }

    /**
     * @return string
     */
    public function getRejectReason()
    {
        return $this->rejectReason;
    }

    /**
     * @param string $rejectReason
     * @return $this
     */
    public function setRejectReason($rejectReason)
    {
        $this->rejectReason = $rejectReason;
        return $this;
    }

    /**
     * @return string
     */
    public function getReferenceSenderBic()
    {
        return $this->referenceSenderBic;
    }

    /**
     * @param string $referenceSenderBic
     * @return $this
     */
    public function setReferenceSenderBic($referenceSenderBic)
    {
        $this->referenceSenderBic = $referenceSenderBic;
        return $this;
    }

    /**
     * @param Money|null $amountMoney
     * @return $this
     */
    public function setAmountMoney(Money $amountMoney = null)
    {
        if ($amountMoney === null) {
            $this->amount = null;
            $this->currency = null;
        } else {
            $this->amount = $amountMoney->getAmount();
            $this->currency = $amountMoney->getCurrency();
        }
        return $this;
    }

    /**
     * @return Money|null
     */
    public function getAmountMoney()
    {
        return $this->amount !== null && $this->currency !== null
            ? new Money($this->amount, $this->currency)
            : null
        ;
    }
}
