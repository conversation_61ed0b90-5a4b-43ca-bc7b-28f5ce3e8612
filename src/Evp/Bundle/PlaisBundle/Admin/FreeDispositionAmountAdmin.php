<?php

namespace Evp\Bundle\PlaisBundle\Admin;

use Evp\Bundle\AdminBundle\Admin\BaseAdmin;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\CurrencyBundle\Form\EvpMoneyType;
use Evp\Bundle\PlaisBundle\Entity\FreeDispositionAmount;
use Evp\Bundle\PlaisBundle\Exception\FreeDispositionAmountException;
use Evp\Bundle\PlaisBundle\Form\FreeDispositionAmountCancellationType;
use Evp\Bundle\PlaisBundle\Repository\FreeDispositionAmountRepository;
use Evp\Bundle\PlaisBundle\Service\FreeDispositionAmountManager;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormView;
use Symfony\Component\Validator\Constraints\NotNull;
use Symfony\Component\HttpFoundation\Session\Session;

class FreeDispositionAmountAdmin extends BaseAdmin
{
    /**
     * @var FreeDispositionAmountRepository
     */
    protected $freeDispositionAmountRepository;

    /**
     * @var FreeDispositionAmountManager
     */
    protected $freeDispositionAmountManager;

    /**
     * @var FormFactory
     */
    protected $formFactory;

    protected function configureRoutes(RouteCollection $collection)
    {
        $collection->clearExcept(['list', 'show', 'create']);
    }

    protected function configureListFields(ListMapper $list)
    {
        $list
            ->addIdentifier('id', 'text', ['route' => ['name' => 'show']])
            ->add('account', null, ['associated_property' => 'textualRepresentationAdmin'])
            ->add('amountMoney')
            ->add('grantedAmountMoney')
            ->add('status')
            ->add('comment')
            ->add('issuedBy')
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter)
    {
        $filter
            ->add('id')
            ->add('account', ModelAutocompleteFilter::class, [], null, [
                'property' => 'number',
            ])
            ->add('status', 'doctrine_orm_string', [],
                ChoiceType::class, ['choices' => FreeDispositionAmount::getStatusList()]
            )
            ->add('comment')
        ;

        parent::configureDatagridFilters($filter);
    }

    protected function configureShowFields(ShowMapper $showMapper)
    {
        $showMapper
            ->add('id')
            ->add('account', null, ['associated_property' => 'textualRepresentationAdmin'])
            ->add('amountMoney')
            ->add('grantedAmountMoney')
            ->add('status')
            ->add('comment')
            ->add('issuedBy')
        ;
    }

    protected function configureFormFields(FormMapper $form)
    {
        $form
            ->add('account', ModelAutocompleteType::class, [
                'property' => 'number',
                'to_string_callback' => function(Account $account) {
                    return $account->getTextualRepresentationAdmin();
                },
                'required' => true,
                'constraints' => [
                    new NotNull([
                        'message' => 'Please select an account.',
                    ]),
                ],
            ])
            ->add('amountMoney', EvpMoneyType::class, ['label' => 'Free Disposition Amount'])
            ->add('comment', null, ['required' => false])
        ;
    }

    /**
     * @param FreeDispositionAmountRepository $freeDispositionAmountRepository
     * @return $this
     */
    public function setFreeDispositionAmountRepository($freeDispositionAmountRepository)
    {
        $this->freeDispositionAmountRepository = $freeDispositionAmountRepository;

        return $this;
    }

    /**
     * @param FreeDispositionAmountManager $freeDispositionAmountManager
     * @return $this
     */
    public function setFreeDispositionAmountManager($freeDispositionAmountManager)
    {
        $this->freeDispositionAmountManager = $freeDispositionAmountManager;

        return $this;
    }

    /**
     * @param FormFactory $formFactory
     */
    public function setFormFactory(FormFactory $formFactory)
    {
        $this->formFactory = $formFactory;
    }

    /**
     * @return FormView
     */
    public function getCancellationForm()
    {
        /** @var FreeDispositionAmount $freeDispositionAmount */
        $freeDispositionAmount = $this->getSubject();

        $form = $this->formFactory->createBuilder(FreeDispositionAmountCancellationType::class);
        $form->setAction($this->getRouteGenerator()
            ->generate('EvpPlaisBundle_admin_cancel_fda', ['id' => $freeDispositionAmount->getId()]));

        return $form->getForm()->createView();
    }

    public function prePersist($object)
    {
        $object->setIssuedBy(
            $this->getConfigurationPool()->getContainer()->get('security.token_storage')->getToken()->getUser()
        );

        try {
            $this->freeDispositionAmountManager->initiateFreeDispositionAmount($object);
        } catch (FreeDispositionAmountException $exception) {
            $this->addMessage('sonata_flash_error', $exception->getMessage());
            return;
        }

        $this->addMessage('sonata_flash_success', 'Free disposition amount initiated successfully!');
    }

    private function addMessage(string $type, string $message): void
    {
        if ($this->getRequest()->getSession() instanceof Session) {
            $this->getRequest()->getSession()->getFlashBag()->add($type, $message);
        }
    }
}
