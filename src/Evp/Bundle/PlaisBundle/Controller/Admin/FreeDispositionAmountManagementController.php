<?php

namespace Evp\Bundle\PlaisBundle\Controller\Admin;

use Doctrine\ORM\EntityManager;
use Evp\Component\Doctrine\AutoCommit;
use Evp\Bundle\PlaisBundle\Entity\FreeDispositionAmount;
use Evp\Bundle\PlaisBundle\Repository\FreeDispositionAmountRepository;
use Evp\Bundle\PlaisBundle\Service\FreeDispositionAmountManager;
use Evp\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Session\Session;

class FreeDispositionAmountManagementController extends Controller
{
    private $entityManager;
    private $freeDispositionAmountManager;
    private $freeDispositionAmountRepository;
    private $autoCommit;

    public function __construct(
        EntityManager $entityManager,
        FreeDispositionAmountManager $freeDispositionAmountManager,
        FreeDispositionAmountRepository $freeDispositionAmountRepository,
        AutoCommit $autoCommit
    ) {
        $this->entityManager = $entityManager;
        $this->autoCommit = $autoCommit;
        $this->freeDispositionAmountManager = $freeDispositionAmountManager;
        $this->freeDispositionAmountRepository = $freeDispositionAmountRepository;
    }

    public function cancel(Request $request, $id)
    {
        $this->autoCommit->begin();
        /** @var FreeDispositionAmount $freeDispositionAmount */
        $freeDispositionAmount = $this->freeDispositionAmountRepository->find($id);

        if ($this->freeDispositionAmountManager->cancelFreeDispositionAmount($freeDispositionAmount)) {
            $this->entityManager->flush();

            if ($request->getSession() instanceof Session) {
                $request->getSession()->getFlashBag()->add(
                    'sonata_flash_success',
                    'Free disposition amount cancelled successfully!'
                );
            }
        }

        return new RedirectResponse($request->headers->get('referer'));
    }
}
