<?php

namespace Evp\Bundle\PlaisBundle\Repository;

use DateInterval;
use DateTime;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Entity\ArrestResponse;
use Paysera\Bundle\RandomHashBundle\Service\UniqueFieldAwareRepositoryInterface;

/**
 * @method ArrestResponse|null find($id, $lockMode = null, $lockVersion = null)
 */
class ArrestResponseRepository extends EntityRepository implements UniqueFieldAwareRepositoryInterface
{
    /**
     * @param string $docId
     * @return bool
     */
    public function isFieldUnique($docId)
    {
        return $this->createQueryBuilder('ar')
            ->select('COUNT(ar)')
            ->where('ar.docId = :docId')
            ->setParameter('docId', $docId)
            ->getQuery()
            ->getSingleScalarResult() !== 0
        ;
    }

    /**
     * @return ArrestResponse[]
     */
    public function findReadyForProcessing()
    {
        $queryBuilder = $this->createQueryBuilder('r')
            ->select('r')
            ->where('r.status = :status_pending')
            ->setParameter('status_pending', ArrestResponse::STATUS_PENDING)
            ->setMaxResults(500)
            ->orderBy('r.id', 'ASC')
            ->getQuery();

        return $queryBuilder->getResult();
    }

    /**
     * @param string $endToEndId
     *
     * @return bool
     */
    public function isDuplicateCctinitResponse($endToEndId)
    {
        $queryBuilder = $this->createQueryBuilder('r')
            ->select('r')
            ->where('r.type = :type')
            ->andWhere('r.endToEndId = :end_to_end_id')
            ->setParameters(
                [
                    'type' => ArrestResponse::TYPE_CCTINIT,
                    'end_to_end_id' => $endToEndId,
                ]
            )
            ->getQuery();

        $result = $queryBuilder->getResult();

        return !empty($result);
    }

    /**
     * @param Arrest $arrest
     * @param string $arrestResponseType
     * @return int
     */
    public function countSuccessfulByArrestAndResponseType(Arrest $arrest, $arrestResponseType)
    {
        return (int)$this->createQueryBuilder('r')
            ->select('COUNT(r)')
            ->where('r.type = :type')
            ->andWhere('r.status IN (:status)')
            ->andWhere('r.responseStatus = :responseStatus')
            ->andWhere('r.arrest = :arrest')
            ->setParameters(
                [
                    'type' => $arrestResponseType,
                    'status' => [
                        ArrestResponse::STATUS_SENT,
                        ArrestResponse::STATUS_PENDING,
                    ],
                    'responseStatus' => ArrestResponse::RESPONSE_STATUS_SUCCESS,
                    'arrest' => $arrest,
                ]
            )
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param string[] $referenceDocIds
     * @param int|null $limit
     * @return ArrestResponse[]
     */
    public function findByReferenceDocIds(array $referenceDocIds, int $limit = null): array
    {
        $queryBuilder = $this->createQueryBuilder('r')
            ->select('r')
            ->where('r.referenceDocId IN (:referenceDocIds)')
            ->setParameter('referenceDocIds', $referenceDocIds)
            ->getQuery()
        ;

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getResult();
    }

    public function countByReferenceDocId(string $referenceDocId): int
    {
        return (int) $this->createQueryBuilder('r')
            ->select('COUNT(r.id)')
            ->where('r.referenceDocId = :referenceDocId')
            ->setParameter('referenceDocId', $referenceDocId)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param string[] $endToEndIds
     * @param int|null $limit
     * @return ArrestResponse[]
     */
    public function findByEndToEndIds(array $endToEndIds, int $limit = null): array
    {
        $queryBuilder = $this->createQueryBuilder('r')
            ->select('r')
            ->where('r.endToEndId IN (:endToEndIds)')
            ->setParameter('endToEndIds', $endToEndIds)
            ->getQuery()
        ;

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getResult();
    }

    public function getStatisticsForInterval(DateInterval $passedInterval): array
    {
        return $this->createQueryBuilder('r')
            ->select('DATE(r.createdAt) AS date, r.type AS type, COUNT(r.id) AS count')
            ->where('r.createdAt >= :passedInterval')
            ->setParameter('passedInterval', (new DateTime('midnight'))->sub($passedInterval))
            ->groupBy('date')
            ->addGroupBy('type')
            ->getQuery()
            ->getArrayResult()
        ;
    }

    public function countByDate(DateTime $date): int
    {
        return (int) $this->createQueryBuilder('r')
            ->select('COUNT(r.id) AS count')
            ->where('DATE(r.createdAt) = :date')
            ->setParameter('date', $date->format('Y-m-d'))
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @return ArrestResponse[]
     */
    public function findResponsesByStatusAndDateTo(
        string $status,
        DateTime $dateTo,
        int $offset = 0,
        ?int $limit = null
    ): array {
        $queryBuilder = $this->getResponsesByStatusAndDateToQueryBuilder($status, $dateTo)
            ->select('r')
            ->setFirstResult($offset)
        ;

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function countResponsesByStatusAndDateTo(string $status, DateTime $dateTo): int
    {
        return (int) $this->getResponsesByStatusAndDateToQueryBuilder($status, $dateTo)
            ->select('COUNT(r.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    private function getResponsesByStatusAndDateToQueryBuilder(string $status, DateTime $dateTo): QueryBuilder
    {
        return $this->createQueryBuilder('r')
            ->select('r')
            ->where('r.status = :status')
            ->andWhere('r.createdAt <= :dateTo')
            ->setParameters([
                'status' => $status,
                'dateTo' => $dateTo,
            ])
        ;
    }

    /**
     * @param int|null $limit
     *
     * @return ArrestResponse[]
     */
    public function findNotValidResponses(
        ?int $limit = null
    ): array {
        $queryBuilder = $this->findByStatusesQueryBuilder([ArrestResponse::STATUS_NOT_VALID]);

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function getNotValidResponsesCount(): int
    {
        return (int) $this->findByStatusesQueryBuilder([ArrestResponse::STATUS_NOT_VALID])
            ->select('COUNT(ar.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    private function findByStatusesQueryBuilder(array $statuses): QueryBuilder
    {
        return $this->createQueryBuilder('ar')
            ->where('ar.status IN (:statuses)')
            ->setParameter(
                'statuses',
                $statuses
            )
        ;
    }
}
