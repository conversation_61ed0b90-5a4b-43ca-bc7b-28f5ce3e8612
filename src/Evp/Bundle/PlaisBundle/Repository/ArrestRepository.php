<?php

namespace Evp\Bundle\PlaisBundle\Repository;

use DateTime;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\VmiAccountReportBundle\Entity\ReportItem;
use Evp\Component\Money\Money;

/**
 * @method Arrest[] findAll()
 */
class ArrestRepository extends EntityRepository
{
    /**
     * @param Account $account
     * @param string $arrestStatus
     * @param string[] $holdStatuses
     * @return Arrest[]
     */
    public function findArrestsByAccountAndStatuses(Account $account, $arrestStatus, array $holdStatuses)
    {
        return $this->createQueryBuilder('a')
            ->select('a, h')
            ->join('a.holds', 'h')
            ->where('h.status IN (:hold_statuses)')
            ->andWhere('a.account = :account')
            ->andWhere('a.status = :arrest_status')
            ->setParameters(['account' => $account, 'arrest_status' => $arrestStatus, 'hold_statuses' => $holdStatuses])
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param Account $account
     * @param string $arrestStatus
     * @param string[] $holdStatuses
     * @return Arrest|null
     */
    public function findLastArrestByAccountAndStatuses(
        Account $account,
        string $arrestStatus,
        array $holdStatuses
    ): ?Arrest {
        return $this->createQueryBuilder('a')
            ->select('a, h')
            ->join('a.holds', 'h')
            ->where('h.status IN (:hold_statuses)')
            ->andWhere('a.account = :account')
            ->andWhere('a.status = :arrest_status')
            ->orderBy('a.id', 'DESC')
            ->setMaxResults(1)
            ->setParameters(['account' => $account, 'arrest_status' => $arrestStatus, 'hold_statuses' => $holdStatuses])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param Account $account
     * @param string[] $arrestStatuses
     * @param string[] $holdStatuses
     *
     * @return Money[] [Money|null $arrestMoney, Money|null $holdMoney]
     */
    public function getArrestsSumByAccountAndStatuses(Account $account, array $arrestStatuses, array $holdStatuses)
    {
        $result = $this->createQueryBuilder('a')
            ->select(
                'SUM(a.arrestAmount) as arrestSum,
                a.arrestCurrency,
                SUM(h.amount) as holdSum,
                h.amountCurrency as holdCurrency'
            )
            ->join('a.holds', 'h')
            ->where('h.status IN (:hold_statuses)')
            ->andWhere('a.account = :account')
            ->andWhere('a.status IN (:arrest_statuses)')
            ->groupBy('a.arrestCurrency')
            ->groupBy('h.amountCurrency')
            ->setParameters([
                'account' => $account,
                'arrest_statuses' => $arrestStatuses,
                'hold_statuses' => $holdStatuses,
            ])
            ->getQuery()
            ->getResult()
        ;

        $arrestMoney = isset($result[0]['arrestSum'])
            ? new Money($result[0]['arrestSum'], $result[0]['arrestCurrency'])
            : null
        ;

        $holdMoney = isset($result[0]['holdSum'])
            ? new Money($result[0]['holdSum'], $result[0]['holdCurrency'])
            : null
        ;

        return [$arrestMoney, $holdMoney];
    }

    public function findOneByArrestIdentification(string $arrestIdentification): ?Arrest
    {
        /** @var Arrest $arrest */
        $arrest = $this->findOneBy(['arrestIdentification' => $arrestIdentification], ['id' => 'DESC']);

        return $arrest;
    }

    /**
     * @param Hold $hold
     * @return Arrest|null
     */
    public function findArrestByHold(Hold $hold)
    {
        return $this->createQueryBuilder('a')
            ->select('a')
            ->join('a.holds', 'h')
            ->where('h = :hold')
            ->setParameter('hold', $hold)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param Account $account
     * @param string $status
     *
     * @return Arrest[]
     */
    public function findByAccountAndStatus(Account $account, $status)
    {
        /** @var Arrest[] $accountArrests */
        $accountArrests = $this->findBy(['account' => $account, 'status' => $status]);

        return $accountArrests;
    }

    /**
     * @param Account $account
     * @param string[] $arrestStatuses
     * @param string[] $holdStatuses
     * @return int
     */
    public function countByAccountAndStatuses(Account $account, array $arrestStatuses, array $holdStatuses)
    {
        return (int) $this->createQueryBuilder('a')
            ->select('COUNT(a)')
            ->join('a.holds', 'h')
            ->where('h.status IN (:hold_statuses)')
            ->andWhere('a.account = :account')
            ->andWhere('a.status IN (:arrest_statuses)')
            ->setParameters([
                'account' => $account,
                'arrest_statuses' => $arrestStatuses,
                'hold_statuses' => $holdStatuses,
            ])
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param int $arrestId
     * @return Arrest|null
     */
    public function findOneById($arrestId)
    {
        /** @var Arrest|null $arrest */
        $arrest = $this->find($arrestId);

        return $arrest;
    }

    /**
     * @return int[][]
     */
    public function findAllDistinctAccountsFromArrests()
    {
        return $this->createQueryBuilder('a')
            ->select('ba.id')
            ->join('a.account', 'ba')
            ->distinct(true)
            ->getQuery()
            ->getResult(AbstractQuery::HYDRATE_ARRAY)
        ;
    }

    /**
     * @param int|null $offset
     * @param int|null $limit
     *
     * @return Arrest[]
     */
    public function findAllArrestsWithFdaAmountOrDescription(?int $offset = null, ?int $limit = null)
    {
        $queryBuilder = $this->createQueryBuilder('a')
            ->where('a.freeDispositionAmount IS NOT NULL')
            ->orWhere('a.freeDispositionComment IS NOT NULL')
        ;

        if ($offset !== null) {
            $queryBuilder->setFirstResult($offset);
        }

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Arrest[]
     */
    public function findNewArrestsByAccountStatusAndClientLevel(
        bool $closed,
        ?array $excludeLevels,
        int $offset = 0,
        ?int $limit = null
    ): array {
        $queryBuilder = $this->createQueryNewArrestsByAccountStatusAndClientLevel($closed, $excludeLevels)
            ->setFirstResult($offset)
        ;

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function countNewArrestsByAccountStatusAndClientLevel(bool $closed, ?array $excludeLevels): int
    {
        return (int) $this->createQueryNewArrestsByAccountStatusAndClientLevel($closed, $excludeLevels)
            ->select('COUNT(a.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findCountArrestsWithClientPartnerCodes(string $arrestStatus): array
    {
        return $this->createQueryWithActiveLicensePartner($arrestStatus)
            ->select([
                'partner.partnerCode AS partnerCode',
                'COUNT(a.id) AS arrestCount',
            ])
            ->groupBy('partner.partnerCode')
            ->getQuery()
            ->getScalarResult()
        ;
    }

    public function countArrestsForNonLtClients(string $arrestStatus): int
    {
        return (int) $this->createQueryWithActiveLicensePartner($arrestStatus)
            ->select('COUNT(a.id)')
            ->andWhere('partner.partnerCode != :ltPartnerCode')
            ->setParameter('ltPartnerCode', LicensedPartner::PAYSERA_LITHUANIA)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param string $arrestStatus
     * @param int|null $offset
     * @param int|null $limit
     * @return Arrest[]
     */
    public function findAllArrestsForNonLtClients(string $arrestStatus, ?int $offset = null, ?int $limit = null): array
    {
        $queryBuilder = $this->createQueryWithActiveLicensePartner($arrestStatus)
            ->andWhere('partner.partnerCode != :ltPartnerCode')
            ->setParameter('ltPartnerCode', LicensedPartner::PAYSERA_LITHUANIA)
        ;

        if ($offset !== null) {
            $queryBuilder->setFirstResult($offset);
        }

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    private function createQueryWithActiveLicensePartner(string $arrestStatus): QueryBuilder
    {
        $queryBuilder = $this->createQueryBuilder('a');

        return $queryBuilder
            ->leftJoin('a.account', 'account')
            ->leftJoin('account.client', 'client')
            ->leftJoin('EvpClientBundle:PartnerClient', 'partner', Join::WITH, 'partner.client = client')
            ->where('a.status = :arrestStatus')
            ->andWhere('partner.assignedFrom <= :todayDateTime')
            ->andWhere($queryBuilder->expr()->orX(
                'partner.assignedTo > :todayDateTime',
                'partner.assignedTo IS NULL'
            ))
            ->setParameters([
                'todayDateTime' => new DateTime(),
                'arrestStatus' => $arrestStatus,
            ])
        ;
    }

    private function createQueryNewArrestsByAccountStatusAndClientLevel(bool $closed, ?array $excludeLevels): QueryBuilder
    {
        $subQb = $this->getEntityManager()->createQueryBuilder();
        $subQb
            ->select('IDENTITY(ri.account)')
            ->from(ReportItem::class, 'ri')
            ->where($subQb->expr()->orX(
                $subQb->expr()->andX(
                    $subQb->expr()->isNull('ri.report'),
                    $subQb->expr()->neq('ri.status', ':reportItemStatusFailed')
                ),
                $subQb->expr()->andX(
                    $subQb->expr()->isNotNull('ri.report'),
                    $subQb->expr()->eq('ri.status', ':reportItemStatusNew')
                )
            ))
        ;

        $qb = $this->createQueryBuilder('a');
        $qb
            ->innerJoin('a.account', 'account')
            ->where('a.status = :arrestStatus')
            ->andWhere($qb->expr()->notIn('a.account', $subQb->getDQL()))
            ->setParameters([
                'arrestStatus' => Arrest::STATUS_NEW,
                'accountActive' => !$closed,
                'accountClosed' => $closed,
                'reportItemStatusFailed' => ReportItem::STATUS_FAILED,
                'reportItemStatusNew' => ReportItem::STATUS_NEW,
            ])
        ;

        if ($closed) {
            $qb->andWhere($qb->expr()->orX(
                'account.active = :accountActive',
                'account.closed = :accountClosed'
            ));
        } else {
            $qb->andWhere('account.active = :accountActive');
            $qb->andWhere('account.closed = :accountClosed');
        }

        if ($excludeLevels !== null) {
            $qb->innerJoin(ClientNatural::class, 'clientNatural', 'WITH', 'account.client = clientNatural')
                ->andWhere('clientNatural.level NOT IN (:excludeLevels)')
                ->setParameter('excludeLevels', $excludeLevels)
            ;
        }

        return $qb;
    }
}
