<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Functional\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class ArrestedAccountCRUDControllerTest extends PersistableWebTestCase
{
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');

        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    private function logIn(array $roles): void
    {
        $session = $this->client->getContainer()->get('session');

        $user = new User();
        $user
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test')
        ;

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $providerKey = $this->client->getContainer()->getParameter('fos_user.firewall_name');
        $token = new UsernamePasswordToken($user, null, $providerKey, $roles);

        $session->set('_security_main', serialize($token));
        $session->save();

        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request('GET', '/admin/evp/plais/arrested_accounts/list');

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue(
            strpos($this->client->getResponse()->headers->get('Location'), '/login') !== false
        );
    }

    public function testInsufficientPrivileges(): void
    {
        $this->logIn(['SOME_USER']);
        $this->client->request('GET', '/admin/evp/plais/arrested_accounts/list');

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
    }

    public function testWrongPrivileges(): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_LIST',
        ]);
        $this->client->request('GET', '/admin/evp/plais/arrested_accounts/list');

        $this->assertEquals(403, $this->client->getResponse()->getStatusCode());
    }

    public function testDetails(): void
    {
        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_EDIT',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_LIST',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_CREATE',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_VIEW',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_DELETE',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_EXPORT',
            'ROLE_EVP_PLAIS_ADMIN_PLAIS_ARRESTED_ACCOUNT_ALL',
        ]);

        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::FULLY_IDENTIFIED);
        $account = $this->fixturesHelper->createAccount($client, '1');
        $hold = $this->fixturesHelper->createHold($account, new Money('15', 'EUR'), Hold::STATUS_HOLD);
        for ($i = 1; $i <= 2; $i++) {
            $this->fixturesHelper->createArrest(
                $account,
                new Money('20', 'EUR'),
                '12345',
                Arrest::STATUS_NEW,
                Arrest::TYPE_ARREST,
                $hold,
                new Money('20', 'EUR'),
                'Test FreeDispositionComment'
            );
        }
        $this->entityManager->flush();

        $crawler = $this->client->request('GET', '/admin/evp/plais/arrested_accounts/1/display_arrests');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        $this->assertEquals('1', $crawler->filter('th:contains("Id") + td')->text());
        $this->assertEquals('1 (********************)', $crawler->filter('th:contains("Account") + td')->text());
        $this->assertEquals('yes', $crawler->filter('th:contains("Is Active") + td span.label-success')->text());
        $this->assertEquals('no', $crawler->filter('th:contains("Is Closed") + td span.label-danger')->text());
        $this->assertEquals('Testas Testavicius (id: 1)', $crawler->filter('th:contains("Client") + td')->text());

        $rows = [];
        $crawler->filter('table.table-bordered.sonata-ba-list tr')->each(function (Crawler $node) use (&$rows) {
            if($node->filter('td')->count()) {
                $rows[] = [
                    'id' => trim($node->filter('td')->eq(0)->text()),
                    'status' => trim($node->filter('td')->eq(4)->text()),
                    'type' => trim($node->filter('td')->eq(7)->text()),
                ];
            }
        });

        $this->assertCount(2, $rows);

        $this->assertArrayHasKey(0, $rows);
        $this->assertEquals(['id' => '1', 'status' => 'new', 'type' => 'arrest'], $rows[0]);

        $this->assertArrayHasKey(1, $rows);
        $this->assertEquals(['id' => '2', 'status' => 'new', 'type' => 'arrest'], $rows[1]);
    }
}
