<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Listener;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Repository\TransferRepository;
use Evp\Bundle\BankTransferBundle\Service\TransferExecutionTimeCalculator;
use Evp\Bundle\PlaisBundle\Entity\FreeDispositionAmount;
use Evp\Bundle\PlaisBundle\Event\FreeDispositionAmountEvent;
use Evp\Bundle\PlaisBundle\Listener\FreeDispositionAmountListener;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;

class FreeDispositionAmountListenerTest extends PersistableWebTestCase
{
    private LoggerInterface $logger;

    /** @var TransferRepository|MockObject */
    private MockObject $transferRepository;

    /** @var TransferExecutionTimeCalculator|MockObject */
    private MockObject $transferExecutionTimeCalculator;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->transferRepository = $this->createMock(TransferRepository::class);
        $this->transferExecutionTimeCalculator = $this->createMock(TransferExecutionTimeCalculator::class);

        $this->logger->expects($this->once())->method('debug');
    }

    public function testOnFreeDispositionAmountActivated(): void
    {
        $freeDispositionAmount = new FreeDispositionAmount();
        $event = new FreeDispositionAmountEvent($freeDispositionAmount);

        $transfers = [new TransferOut(), new TransferOut()];
        $this->transferRepository->expects($this->once())
            ->method('findWaitingFundsByAccount')
            ->with($freeDispositionAmount->getAccount())
            ->willReturn($transfers)
        ;

        $this->transferExecutionTimeCalculator->expects($this->exactly(count($transfers)))->method('calculate');

        $listener = new FreeDispositionAmountListener($this->transferRepository, $this->transferExecutionTimeCalculator, $this->logger);

        $listener->onFreeDispositionAmountActivated($event);

        foreach ($transfers as $transfer) {
            $this->assertEquals(Transfer::STATUS_SIGNED, $transfer->getStatus());
        }
    }
}
