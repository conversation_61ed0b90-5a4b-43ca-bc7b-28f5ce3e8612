<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ProcessArrestFromFileCommandTest extends CommandTestCase
{
    private CommandTester $commandTester;
    private ArrestResponseRepository $arrestResponseRepository;

    public function setUp(): void
    {
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.process_arrest_from_file'
        );
        $this->arrestResponseRepository = $this->getContainer()->get('evp_plais.repository.arrest_response');
    }

    public function testProcess(): void
    {
        $this->commandTester->execute([
            'command' => $this->command->getName(),
            '--file' => __DIR__ . '/../MessageProcessor/Resources/cardfile.cardcan_c.xml',
        ]);

        $arrestResponse = $this->arrestResponseRepository->findAll();
        $this->assertCount(2, $arrestResponse);
    }
}
