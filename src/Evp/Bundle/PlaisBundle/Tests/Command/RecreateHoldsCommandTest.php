<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\PlaisBundle\Command\RecreateHoldsCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class RecreateHoldsCommandTest extends CommandTestCase
{
    /**
     * @var RecreateHoldsCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private HoldRepository $holdRepository;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.recreate_holds_command'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->holdRepository = $this->getContainer()->get('evp_bank_hold.repository.hold');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testProcess(): void
    {
        $client = $this->fixturesHelper->createClientLegal();
        $account = $this->fixturesHelper->createAccount($client);
        $hold = $this->fixturesHelper->createHold($account, new Money('15', 'EUR'), Hold::STATUS_DONE);
        $this->fixturesHelper->createArrest(
            $account,
            new Money('15', 'EUR'),
            '12345',
            Arrest::STATUS_NEW,
            Arrest::TYPE_CHARGE,
            $hold
        );
        $this->entityManager->flush();

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'accountId' => 1,
        ]);

        /** @var Hold[] $holds */
        $holds = $this->holdRepository->findWaitingFundsByAccount($account);

        $this->assertCount(1, $holds);
    }

    public function testProcessHoldStatusNew(): void
    {
        $client = $this->fixturesHelper->createClientLegal();
        $account = $this->fixturesHelper->createAccount($client);
        $hold = $this->fixturesHelper->createHold($account, new Money('15', 'EUR'));
        $this->fixturesHelper->createArrest(
            $account,
            new Money('15', 'EUR'),
            '12345',
            Arrest::STATUS_NEW,
            Arrest::TYPE_CHARGE,
            $hold
        );
        $this->entityManager->flush();

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'accountId' => 1,
        ]);

        /** @var Hold[] $holds */
        $holds = $this->holdRepository->findWaitingFundsAndHoldByAccount($account);

        $this->assertCount(0, $holds);
    }
}
