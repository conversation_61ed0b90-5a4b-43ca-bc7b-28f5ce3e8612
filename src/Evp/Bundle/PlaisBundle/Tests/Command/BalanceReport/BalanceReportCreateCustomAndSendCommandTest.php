<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\BalanceReport;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\PlaisBundle\Command\BalanceReport\BalanceReportProcessNotValidCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Entity\BalanceReportLog;
use Evp\Bundle\PlaisBundle\Repository\BalanceReportLogRepository;
use Evp\Bundle\PlaisBundle\Service\IdentityDocumentResolver;
use Evp\Bundle\PlaisBundle\Service\Mapper\ActualBalanceExportMapper;
use Evp\Component\Money\Money;
use Evp\Tests\Integration\RabbitMq\Producer\ExtendedProducer;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Validator\Tests\Fixtures\Countable;

class BalanceReportCreateCustomAndSendCommandTest extends CommandTestCase
{
    /**
     * @var BalanceReportProcessNotValidCommand $command
     */
    protected $command;
    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private BalanceReportLogRepository $balanceReportLogRepository;
    private ExtendedProducer $extendedProducer;
    private FixturesHelper $fixturesHelper;

    /** @var IdentityDocumentResolver|MockObject */
    private $identityDocumentResolver;

    public function setUp(): void
    {
        $this->identityDocumentResolver = $this->createMock(IdentityDocumentResolver::class);
        $this->getContainer()->set('evp_plais.identity_document_resolver', $this->identityDocumentResolver);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.balance_report.create_custom_and_send'
        );

        /** @noinspection MissingService */
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->balanceReportLogRepository = $this->getContainer()->get('evp_plais.repository.balance_report_log');
        $this->extendedProducer = $this->getContainer()->get('evp_rabbit_mq_extension.producer.lb');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    private function runCommand(array $options = []): string
    {
        $this->commandTester->execute(array_merge(['command' => $this->command->getName()], $options));
        return $this->commandTester->getDisplay();
    }

    /** @dataProvider successfulSendBalanceDataProvider */
    public function testSuccessfulSendBalance(string $cCodeType, string $clientCode, string $clientType): void
    {
        $client = $clientType === ClientNatural::class
            ? $this->fixturesHelper->createClientNatural()
            : $this->fixturesHelper->createClientLegal()
        ;
        $client->setCode($clientCode);
        $account = $this->fixturesHelper->createAccount($client);

        $clientIdentificationDocument = $this->fixturesHelper->createUserIdentityDocument($client->getCode());
        $this->identityDocumentResolver
            ->method('resolveIdentityDocument')
            ->willReturnMap([
                [$client->getCovenanteeId(), $clientIdentificationDocument],
            ])
        ;

        $this->createArrestWithHold($account, '1');

        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_PUBLISHED);
        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_NOT_VALID);

        $this->entityManager->flush();
        $commandOutput = $this->runCommand(['accountId' => $account->getId(), 'cCodeTag' => $cCodeType]);

        $this->assertCount(2, $this->balanceReportLogRepository->findAll());
        $this->assertCount(2, $this->balanceReportLogRepository->findBy([
            'account' => $account,
            'status' => BalanceReportLog::STATUS_PUBLISHED,
        ]));

        $this->assertEquals("Finished\n", $commandOutput);

        $publishedMessages = $this->extendedProducer->getMessagesBuffer();
        $this->assertTrue(isset($publishedMessages['out.actual_balance']));

        /** @var Countable $actualBalanceMessages */
        $actualBalanceMessages = $publishedMessages['out.actual_balance'];
        $this->assertCount(1, $actualBalanceMessages);

        $ccodeFromMessage = $this->getCCodeTypeFromQueueMessage($publishedMessages['out.actual_balance'][0]);

        $this->assertEquals($cCodeType, $ccodeFromMessage);
    }

    public function successfulSendBalanceDataProvider(): array
    {
        return [
            'Natural with 11 chars code - ACode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ACODE,
                'clientCode' => '*********10',
                'clientType' => ClientNatural::class,
            ],
            'Natural with 11 chars code - ILTUCode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ILTUCODE,
                'clientCode' => '*********10',
                'clientType' => ClientNatural::class,
            ],
            'Natural with 9 chars code - ICode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ICODE,
                'clientCode' => '*********',
                'clientType' => ClientNatural::class,
            ],
            'Legal with 11 chars code - ACode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ACODE,
                'clientCode' => '*********10',
                'clientType' => ClientLegal::class,
            ],
            'Legal with 11 chars code - ILTUCode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ILTUCODE,
                'clientCode' => '*********10',
                'clientType' => ClientLegal::class,
            ],
            'Legal with 9 chars code - ICode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ICODE,
                'clientCode' => '*********',
                'clientType' => ClientLegal::class,
            ],
        ];
    }

    /** @dataProvider failedSendBalanceDataProvider */
    public function testFailedSendBalance(string $cCodeType, string $clientCode, string $clientType): void
    {
        $client = $clientType === ClientNatural::class
            ? $this->fixturesHelper->createClientNatural()
            : $this->fixturesHelper->createClientLegal()
        ;
        $client->setCode($clientCode);
        $account = $this->fixturesHelper->createAccount($client);

        $clientIdentificationDocument = $this->fixturesHelper->createUserIdentityDocument($client->getCode());
        $this->identityDocumentResolver
            ->method('resolveIdentityDocument')
            ->willReturnMap([
                [$client->getCovenanteeId(), $clientIdentificationDocument],
            ])
        ;

        $this->createArrestWithHold($account, '1');

        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_PUBLISHED);
        $notValid = $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_NOT_VALID);
        $this->assertCount(0, $notValid->getDetails());

        $this->entityManager->flush();
        $commandOutput = $this->runCommand(['accountId' => $account->getId(), 'cCodeTag' => $cCodeType]);

        $this->assertCount(2, $this->balanceReportLogRepository->findAll());
        $this->assertCount(1, $this->balanceReportLogRepository->findBy([
            'account' => $account,
            'status' => BalanceReportLog::STATUS_PUBLISHED,
        ]));
        $this->assertCount(1, $this->balanceReportLogRepository->findBy([
            'account' => $account,
            'status' => BalanceReportLog::STATUS_NOT_VALID,
        ]));
        $this->assertCount(1, $notValid->getDetails());

        $this->assertEquals("Finished\n", $commandOutput);

        $publishedMessages = $this->extendedProducer->getMessagesBuffer();
        $this->assertFalse(isset($publishedMessages['out.actual_balance']));
    }

    public function failedSendBalanceDataProvider(): array
    {
        return [
            'Natural with 9 chars code - ACode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ACODE,
                'clientCode' => '*********',
                'clientType' => ClientNatural::class,
            ],
            'Natural with 9 chars code - ILTUCode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ILTUCODE,
                'clientCode' => '*********',
                'clientType' => ClientNatural::class,
            ],
            'Natural with 11 chars code - ICode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ICODE,
                'clientCode' => '*********10',
                'clientType' => ClientNatural::class,
            ],
            'Legal with 9 chars code - ACode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ACODE,
                'clientCode' => '*********',
                'clientType' => ClientLegal::class,
            ],
            'Legal with 9 chars code - ILTUCode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ILTUCODE,
                'clientCode' => '*********',
                'clientType' => ClientLegal::class,
            ],
            'Legal with 11 chars code - ICode tag' => [
                'cCodeType' => ActualBalanceExportMapper::PAYER_CODE_ICODE,
                'clientCode' => '*********10',
                'clientType' => ClientLegal::class,
            ],
        ];
    }

    public function testNoActiveArrestsExists(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setCode('*********');
        $account = $this->fixturesHelper->createAccount($client);

        $arrest = $this->createArrestWithHold($account, '1');
        $arrest->setStatus(Arrest::STATUS_CANCELLED);

        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_PUBLISHED);
        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_NOT_VALID);

        $this->entityManager->flush();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Account does not have active arrests');

        $this->runCommand(['accountId' => $account->getId(), 'cCodeTag' => 'ICode']);
    }

    public function testNoNotValidBalanceReportLogs(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setCode('*********');
        $account = $this->fixturesHelper->createAccount($client);

        $this->createArrestWithHold($account, '1');

        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_PUBLISHED);
        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_FAILED);

        $this->entityManager->flush();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Last balance report log has wrong status');

        $this->runCommand(['accountId' => $account->getId(), 'cCodeTag' => 'ICode']);
    }

    public function testCCodeTagValidation(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $client->setCode('*********');
        $account = $this->fixturesHelper->createAccount($client);

        $this->createArrestWithHold($account, '1');

        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_PUBLISHED);
        $this->fixturesHelper->createBalanceReportLog($account, BalanceReportLog::STATUS_NOT_VALID);

        $this->entityManager->flush();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Provided ccode is not valid');

        $this->runCommand(['accountId' => $account->getId(), 'cCodeTag' => 'UnknownTag']);
    }

    private function createArrestWithHold(Account $account, string $identification): Arrest
    {
        $amount = new Money(100, 'EUR');

        $hold = $this->fixturesHelper->createHold($account, $amount, Hold::STATUS_WAITING_FUNDS);
        $arrest = $this->fixturesHelper->createArrest(
            $account,
            $amount,
            $identification,
            Arrest::STATUS_NEW,
            Arrest::TYPE_ARREST,
            $hold
        );

        return $arrest;
    }

    private function getCCodeTypeFromQueueMessage(string $content): ?string
    {
        $xml = simplexml_load_string($content);

        foreach ($xml->Docs->Doc as $doc) {
            if ($doc->Blockinf->Payer->CCode->children()->count() > 0) {
                return $doc->Blockinf->Payer->CCode->children()->getName();
            }
        }

        return null;
    }
}
