<?php

namespace Evp\Bundle\PlaisBundle\Tests\Command\BalanceReport;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\PlaisBundle\Command\ArrestResponseProcessorCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Entity\BalanceReportLog;
use Evp\Bundle\PlaisBundle\Repository\BalanceReportLogRepository;
use Evp\Bundle\PlaisBundle\Service\BalanceReport\BalanceReportLogManager;
use Evp\Bundle\PlaisBundle\Service\IdentityDocumentResolver;
use Evp\Bundle\PlaisBundle\Tests\Mock\JobPublisherMock;
use Evp\Bundle\PlaisBundle\Worker\BalanceReportPublisherWorker;
use Evp\Component\Money\Money;
use Evp\Tests\Integration\RabbitMq\Producer\ExtendedProducer;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Validator\Tests\Fixtures\Countable;

class BalanceReportSendCommandTest extends CommandTestCase
{
    /**
     * @var ArrestResponseProcessorCommand $command
     */
    protected $command;

    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private BalanceReportLogManager $balanceReportLogManager;
    private BalanceReportPublisherWorker $balanceReportPublisherWorker;
    private BalanceReportLogRepository $balanceReportLogRepository;
    private JobPublisherMock $jobPublisher;
    private ExtendedProducer $producer;
    private FixturesHelper $fixturesHelper;

    /** @var IdentityDocumentResolver|MockObject */
    private IdentityDocumentResolver $identityDocumentResolver;

    public function setUp(): void
    {
        $this->identityDocumentResolver = $this->createMock(IdentityDocumentResolver::class);
        $this->getContainer()->set('evp_plais.identity_document_resolver', $this->identityDocumentResolver);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.balance_report.send'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->balanceReportLogManager = $this->getContainer()->get('evp_plais.manager.balance_report_log');
        $this->balanceReportPublisherWorker = $this->getContainer()->get('evp_plais.worker.balance_report_publisher');
        $this->balanceReportLogRepository = $this->getContainer()->get('evp_plais.repository.balance_report_log');
        $this->producer = $this->getContainer()->get('evp_rabbit_mq_extension.producer.lb');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        /** @var JobPublisherMock $jobPublisher */
        $jobPublisher = $this->getContainer()->get('evp_rabbit_mq_extension.deferred_remote_job_publisher');
        $this->jobPublisher = $jobPublisher;
    }

    protected static function getMockedServices(): array
    {
        return ['evp_rabbit_mq_extension.deferred_remote_job_publisher' => new JobPublisherMock()];
    }

    private function runCommand(): void
    {
        $this->commandTester->execute(array('command' => $this->command->getName()));
    }

    public function testProcessBalanceReports(): void
    {
        $this->loadFixtures();
        $this->runCommand();

        $publishedJobs = $this->jobPublisher->findJobsByJobKey(BalanceReportPublisherWorker::JOB_KEY);
        $this->assertCount(1, $publishedJobs);

        $jobData = reset($publishedJobs)[JobPublisherMock::DATA_KEY];
        $this->balanceReportPublisherWorker->work($jobData);

        $publishedMessages = $this->producer->getMessagesBuffer();
        $this->assertTrue(isset($publishedMessages['out.actual_balance']));

        /** @var Countable $actualBalanceMessages */
        $actualBalanceMessages = $publishedMessages['out.actual_balance'];
        $this->assertCount(1, $actualBalanceMessages);

        $acc1Content = $this->getResource('cardfile_blockinf_acc1.xml');
        $this->assertEquals($this->fixDynamicValues($acc1Content), $this->fixDynamicValues($actualBalanceMessages[0]));

        $this->assertCount(6, $this->balanceReportLogRepository->findAll());
        $this->assertCount(4, $this->balanceReportLogRepository->findAllByStatus(BalanceReportLog::STATUS_PUBLISHED));
        $this->assertCount(2, $this->balanceReportLogRepository->findAllByStatus(BalanceReportLog::STATUS_SKIPPED));
    }

    /**
     * Account - 1
     *      ClientNatural (lt)
     *      BalanceReports:
     *          BalanceReport
     *      Arrests:
     *          Arrest [charge] (new)
     *              Hold (hold)
     *                  HoldAmount: 3.50 EUR (1.00 of it is our charge)
     *          Arrest [arrest] (new)
     *              Free disposition amount: 45 EUR
     *              Hold (waitingFunds)
     *                  HoldAmount: 1478.11 EUR
     *          Arrest [arrest] (done)
     *              Free disposition amount: 200 EUR
     *              Hold (done)
     *                  HoldAmount: 700 EUR
     *          Arrest [arrest] (new)
     *              Hold (done)
     *                  HoldAmount: 150 EUR
     *          Arrest [arrest] (new)
     *              Free disposition amount: 99 EUR
     *              Hold (hold)
     *                  HoldAmount: 128.74 EUR
     *      Result: 1510.35 EUR
     *
     * ------------------------
     *
     * Account - 2
     *      ClientNatural (de)
     *      BalanceReports
     *          BalanceReport
     *          BalanceReport
     *          BalanceReport
     *      Arrests:
     *          Arrest [arrest] (done)
     *              Hold (done)
     *                  HoldAmount: 99 EUR
     *      Result: no report
     *
     * ------------------------
     *
     * Account - 3
     *      ClientLegal (lt)
     *      BalanceReports
     *          BalanceReport
     *          BalanceReport
     *      Arrests:
     *          Arrest [charge] (new)
     *              Hold (waitingFunds)
     *                  HoldAmount: 1.50 EUR (1.00 of it is our charge)
     *      Result: 0.50 EUR
     *
     * ------------------------
     *
     * * Account - 4
     *      ClientLegal (lt)
     *      Account balance: 200 EUR
     *      BalanceReports
     *          BalanceReport
     *      Arrests:
     *          Arrest [arrest] (new)
     *              Free disposition amount: 300 EUR
     *              Hold (done)
     *                  HoldAmount: 1000 EUR
     *          Arrest [charge] (new)
     *              Hold (done)
     *                  HoldAmount: 1.50 EUR (1.00 of it is our charge)
     *      Result: 700.50 EUR
     *
     * ------------------------
     *
     * * Account - 5
     *      ClientLegal (lt)
     *      Account balance: 300 EUR
     *      BalanceReports
     *          BalanceReport
     *      Arrests:
     *          Arrest [arrest] (new)
     *              Free disposition amount: 300 EUR
     *              Hold (done)
     *                  HoldAmount: 1000 EUR
     *          Arrest [charge] (new)
     *              Hold (done)
     *                  HoldAmount: 1.50 EUR (1.00 of it is our charge)
     *      Result: 1000.50 EUR
     *
     * ------------------------
     *
     */
    private function loadFixtures(): void
    {
        $clientNatural = new ClientNatural();
        $clientNatural->setCode('***********');
        $clientNatural->setCovenanteeId(***********);
        $clientNatural->setFirstName('Vardenis');
        $clientNatural->setLastName('Pavardenis');
        $clientNatural->setCountryCode('lt');
        $this->entityManager->persist($clientNatural);

        $account = new Account();
        $account->setClient($clientNatural);
        $account->setActive(true);
        $account->setType(Account::TYPE_LOCAL);
        $account->setNumber('1');
        $account->setIban('********************');
        $this->entityManager->persist($account);

        $accountSecond = new Account();
        $accountSecond->setClient($clientNatural);
        $accountSecond->setActive(true);
        $accountSecond->setType(Account::TYPE_LOCAL);
        $accountSecond->setIban('********************');
        $this->entityManager->persist($accountSecond);

        $clientNatural2 = new ClientNatural();
        $clientNatural2->setCode('*********');
        $clientNatural2->setCovenanteeId(*********);
        $clientNatural2->setFirstName('Marck');
        $clientNatural2->setLastName('Bankuchen');
        $clientNatural2->setCountryCode('de');
        $this->entityManager->persist($clientNatural2);

        $account2 = new Account();
        $account2->setClient($clientNatural2);
        $account2->setActive(true);
        $account2->setType(Account::TYPE_LOCAL);
        $account2->setNumber('2');
        $account2->setIban('********************');
        $this->entityManager->persist($account2);

        $clientlegal = new ClientLegal();
        $clientlegal->setCode('*********');
        $clientlegal->setName('UAB Agurkas');
        $clientlegal->setCountryCode('lt');
        $this->entityManager->persist($clientlegal);

        $account3 = new Account();
        $account3->setClient($clientlegal);
        $account3->setActive(true);
        $account3->setType(Account::TYPE_LOCAL);
        $account3->setNumber('3');
        $account3->setIban('********************');
        $this->entityManager->persist($account3);

        $clientLegal4 = new ClientLegal();
        $clientLegal4->setCode('*********');
        $clientLegal4->setName('UAB Agurkas 4');
        $clientLegal4->setCountryCode('lt');
        $this->entityManager->persist($clientLegal4);

        $account4 = new Account();
        $account4->setClient($clientLegal4);
        $account4->setActive(true);
        $account4->setType(Account::TYPE_LOCAL);
        $account4->setNumber('4');
        $account4->setIban('********************');
        $this->entityManager->persist($account4);

        $clientLegal5 = new ClientLegal();
        $clientLegal5->setCode('*********');
        $clientLegal5->setName('UAB Agurkas 5');
        $clientLegal5->setCountryCode('lt');
        $this->entityManager->persist($clientLegal5);

        $account5 = new Account();
        $account5->setClient($clientLegal5);
        $account5->setActive(true);
        $account5->setType(Account::TYPE_LOCAL);
        $account5->setNumber('5');
        $account5->setIban('********************');
        $this->entityManager->persist($account5);

        $this->entityManager->flush();

        $this->accountManager->fill(new Money('200', 'EUR'), $account4);
        $this->accountManager->fill(new Money('300', 'EUR'), $account5);

        $this->entityManager->flush();

        // Arrest 1
        $arrest = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_CHARGE)
            ->setArrestIdentification('ACC-1')
            ->setAccount($account)
        ;
        $this->entityManager->persist($arrest);

        $hold = new Hold();
        $hold->setDetails('test');
        $hold->setAccount($account);
        $hold->setAmountMoney(new Money('5.14', 'EUR'));
        $hold->addHoldAmountMoney($hold->getAmountMoney());
        $hold->setStatus(Hold::STATUS_DONE);

        $this->entityManager->persist($hold);
        $arrest->getHolds()->add($hold);

        $hold2nd = new Hold();
        $hold2nd->setDetails('test');
        $hold2nd->setAccount($account);
        $hold2nd->setAmountMoney(new Money('3.50', 'EUR'));
        $hold2nd->addHoldAmountMoney($hold2nd->getAmountMoney());
        $hold2nd->setStatus(Hold::STATUS_HOLD);

        $this->entityManager->persist($hold2nd);
        $arrest->getHolds()->add($hold2nd);

        // Arrest 2
        $arrest2 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-11')
            ->setAccount($account)
            ->setFreeDispositionAmount(new Money('45', 'EUR'))
        ;
        $this->entityManager->persist($arrest2);

        $hold1 = new Hold();
        $hold1->setDetails('test');
        $hold1->setAccount($account);
        $hold1->setAmountMoney(new Money('5598.45', 'EUR'));
        $hold1->addHoldAmountMoney(new Money('1478.11', 'EUR'));
        $hold1->setStatus(Hold::STATUS_WAITING_FUNDS);

        $this->entityManager->persist($hold1);
        $arrest2->getHolds()->add($hold1);

        // Arrest 3
        $arrest3 = (new Arrest())
            ->setStatus(Arrest::STATUS_DONE)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-111')
            ->setAccount($account)
            ->setFreeDispositionAmount(new Money('200', 'EUR'))
        ;
        $this->entityManager->persist($arrest3);

        $hold2 = new Hold();
        $hold2->setDetails('test');
        $hold2->setAccount($account);
        $hold2->setAmountMoney(new Money('700', 'EUR'));
        $hold2->addHoldAmountMoney($hold2->getAmountMoney());
        $hold2->setStatus(Hold::STATUS_DONE);

        $this->entityManager->persist($hold2);
        $arrest3->getHolds()->add($hold2);

        // Arrest 4
        $arrest4 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-1111')
            ->setAccount($account)
        ;
        $this->entityManager->persist($arrest4);

        $hold3 = new Hold();
        $hold3->setDetails('test');
        $hold3->setAccount($account);
        $hold3->setAmountMoney(new Money('150', 'EUR'));
        $hold3->addHoldAmountMoney($hold3->getAmountMoney());
        $hold3->setStatus(Hold::STATUS_DONE);

        $this->entityManager->persist($hold3);
        $arrest4->getHolds()->add($hold3);

        // Arrest 5
        $arrest5 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-111111')
            ->setAccount($account)
            ->setFreeDispositionAmount(new Money('99', 'EUR'))
        ;
        $this->entityManager->persist($arrest5);

        $hold4 = new Hold();
        $hold4->setDetails('test');
        $hold4->setAccount($account);
        $hold4->setAmountMoney(new Money('128.74', 'EUR'));
        $hold4->addHoldAmountMoney($hold4->getAmountMoney());
        $hold4->setStatus(Hold::STATUS_HOLD);

        $this->entityManager->persist($hold4);
        $arrest5->getHolds()->add($hold4);

        // Arrest 6
        $arrest6 = (new Arrest())
            ->setStatus(Arrest::STATUS_DONE)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-2')
            ->setAccount($account2)
        ;
        $this->entityManager->persist($arrest6);

        $hold5 = new Hold();
        $hold5->setDetails('test');
        $hold5->setAccount($account2);
        $hold5->setAmountMoney(new Money('99', 'EUR'));
        $hold5->addHoldAmountMoney($hold5->getAmountMoney());
        $hold5->setStatus(Hold::STATUS_DONE);

        $this->entityManager->persist($hold5);
        $arrest6->getHolds()->add($hold5);

        // Arrest 7
        $arrest7 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_CHARGE)
            ->setArrestIdentification('ACC-3')
            ->setAccount($account3)
        ;
        $this->entityManager->persist($arrest7);

        $hold6 = new Hold();
        $hold6->setDetails('test');
        $hold6->setAccount($account3);
        $hold6->setAmountMoney(new Money('3.50', 'EUR'));
        $hold6->addHoldAmountMoney(new Money('1.50', 'EUR'));
        $hold6->setStatus(Hold::STATUS_WAITING_FUNDS);

        $this->entityManager->persist($hold6);
        $arrest7->getHolds()->add($hold6);

        $this->entityManager->flush();

        // Arrest 8
        $arrest8 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-AR-8')
            ->setAccount($account4)
        ;
        $arrest8->setArrestAmount(new Money('1000', 'EUR'));
        $arrest8->setFreeDispositionAmount(new Money('300', 'EUR'));
        $this->entityManager->persist($arrest8);

        $hold7 = (new Hold())
            ->setDetails('Arrest 8')
            ->setAccount($account4)
            ->setAmountMoney(new Money('1000', 'EUR'))
            ->setStatus(Hold::STATUS_HOLD)
        ;
        $hold7->addHoldAmountMoney(new Money('1000', 'EUR'));

        $this->entityManager->persist($hold7);
        $arrest8->getHolds()->add($hold7);

        $this->entityManager->flush();

        // Arrest 9
        $arrest9 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_CHARGE)
            ->setArrestIdentification('ACC-AR-9')
            ->setAccount($account4)
        ;
        $arrest9->setArrestAmount(new Money('1.5', 'EUR'));
        $this->entityManager->persist($arrest9);

        $hold8 = (new Hold())
            ->setDetails('Arrest 9')
            ->setAccount($account4)
            ->setAmountMoney(new Money('1.5', 'EUR'))
            ->setStatus(Hold::STATUS_HOLD)
        ;
        $hold8->addHoldAmountMoney(new Money('1.5', 'EUR'));

        $this->entityManager->persist($hold8);
        $arrest9->getHolds()->add($hold8);

        $this->entityManager->flush();

        // Arrest 10
        $arrest10 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_ARREST)
            ->setArrestIdentification('ACC-AR-10')
            ->setAccount($account5)
        ;
        $arrest10->setArrestAmount(new Money('1000', 'EUR'));
        $arrest10->setFreeDispositionAmount(new Money('300', 'EUR'));
        $this->entityManager->persist($arrest10);

        $hold9 = (new Hold())
            ->setDetails('Arrest 10')
            ->setAccount($account5)
            ->setAmountMoney(new Money('1000', 'EUR'))
            ->setStatus(Hold::STATUS_HOLD)
        ;
        $hold9->addHoldAmountMoney(new Money('1000', 'EUR'));

        $this->entityManager->persist($hold9);
        $arrest10->getHolds()->add($hold9);

        $this->entityManager->flush();

        // Arrest 11
        $arrest11 = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setType(Arrest::TYPE_CHARGE)
            ->setArrestIdentification('ACC-AR-11')
            ->setAccount($account5)
        ;
        $arrest11->setArrestAmount(new Money('1.5', 'EUR'));
        $this->entityManager->persist($arrest11);

        $hold10 = (new Hold())
            ->setDetails('Arrest 11')
            ->setAccount($account5)
            ->setAmountMoney(new Money('1.5', 'EUR'))
            ->setStatus(Hold::STATUS_HOLD)
        ;
        $hold10->addHoldAmountMoney(new Money('1.5', 'EUR'));

        $this->entityManager->persist($hold10);
        $arrest11->getHolds()->add($hold10);

        $this->entityManager->flush();

        $identityDocumentClientNatural = $this->fixturesHelper->createUserIdentityDocument($clientNatural->getCode());

        $this->identityDocumentResolver
            ->method('resolveIdentityDocument')
            ->willReturnMap([
                [$clientNatural->getCovenanteeId(), $identityDocumentClientNatural],
            ])
        ;

        $this->balanceReportLogManager->upsertBalanceReport($account);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account2);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account2);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account2);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account3);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account3);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($accountSecond);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account4);
        $this->entityManager->flush();
        $this->balanceReportLogManager->upsertBalanceReport($account5);
        $this->entityManager->flush();
    }

    private function getResource(string $fileName): string
    {
        return file_get_contents(implode(DIRECTORY_SEPARATOR, array(__DIR__, '..', 'Resources', $fileName)));
    }

    private function fixDynamicValues(string $content): string
    {
        $messageId = '****************';
        $xml = simplexml_load_string($content);

        $content = preg_replace('/<Date>(.*?)<\/Date>/is', "<Date>2016-01-01</Date>", $content);
        $content = str_replace((string)$xml->Header->MsgId, $messageId, $content);

        foreach ($xml->Docs->Doc as $doc) {
            $content = str_replace((string)$doc->Header->DocId, $messageId, $content);
        }

        return $content;
    }
}
