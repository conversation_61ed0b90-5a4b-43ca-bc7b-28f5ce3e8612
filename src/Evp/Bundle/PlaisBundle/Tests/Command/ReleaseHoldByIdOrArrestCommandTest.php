<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\PlaisBundle\Command\ReleaseHoldByIdOrArrestCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ReleaseHoldByIdOrArrestCommandTest extends CommandTestCase
{
    /**
     * @var ReleaseHoldByIdOrArrestCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private HoldRepository $holdRepository;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->holdRepository = $this->getContainer()->get('evp_bank_hold.repository.hold');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.release_hold_by_id_or_arrest'
        );
    }

    /**
     * @param array $options
     * @throws ORMException
     * @throws OptimisticLockException
     *
     * @dataProvider optionsDataProvider
     */
    public function testProcess(array $options): void
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::FULLY_IDENTIFIED);
        $account = $this->fixturesHelper->createAccount($client, '1');
        $hold = $this->fixturesHelper->createHold($account, new Money('15', 'EUR'));
        for ($i = 1; $i <= 2; $i++) {
            $this->fixturesHelper->createArrest(
                $account,
                new Money('20', 'EUR'),
                '12345',
                Arrest::STATUS_NEW,
                Arrest::TYPE_ARREST,
                $hold,
                new Money('20', 'EUR'),
                'Test FreeDispositionComment'
            );
        }
        $this->entityManager->flush();

        /** @var Hold $hold */
        $hold = $this->holdRepository->findOneBy([]);

        $params = ['command' => $this->command->getName()];
        $this->commandTester->execute(array_merge($params, $options));

        $this->assertEquals(Hold::STATUS_DONE, $hold->getStatus());
    }

    public function optionsDataProvider(): array
    {
        return [
            'Case 1: Test with --holdId option' => [
                'options' => ['--holdId' => 1],
            ],
            'Case 2: Test with --arrestId option' => [
                'options' => ['--arrestId' => 1],
            ],
        ];
    }
}
