<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\PlaisBundle\Command\ReleaseHoldForStuckArrestCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ReleaseHoldForStuckArrestCommandTest extends CommandTestCase
{
    /**
     * @var ReleaseHoldForStuckArrestCommand
     */
    protected $command;
    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private HoldRepository $holdRepository;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.release_hold_for_stuck_arrest_command'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->holdRepository = $this->getContainer()->get('evp_bank_hold.repository.hold');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testProcess(): void
    {
        $client = $this->fixturesHelper->createClientNatural(1, IdentificationLevels::FULLY_IDENTIFIED);
        $account = $this->fixturesHelper->createAccount($client, '1');
        $hold = $this->fixturesHelper->createHold($account, new Money('15', 'EUR'));
        for ($i = 1; $i <= 2; $i++) {
            $this->fixturesHelper->createArrest(
                $account,
                new Money('20', 'EUR'),
                '12345',
                Arrest::STATUS_NEW,
                Arrest::TYPE_ARREST,
                $hold,
                new Money('20', 'EUR'),
                'Test FreeDispositionComment'
            );
        }
        $this->entityManager->flush();

        /** @var Hold $hold */
        $hold = $this->holdRepository->findOneBy(['status' => Hold::STATUS_NEW]);

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'arrestId' => 1,
            '--releaseAll' => true,
            '--overwriteReleaseDate' => true
        ]);

        $this->assertTrue($hold->getRelease());
        $this->assertNotNull($hold->getReleaseOn());
    }
}
