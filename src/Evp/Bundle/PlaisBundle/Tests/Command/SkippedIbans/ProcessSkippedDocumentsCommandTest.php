<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\SkippedIbans;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\LbIntegrationBundle\Service\SepaEDocReader;
use Evp\Bundle\PlaisBundle\Command\SkippedIbans\ProcessSkippedDocumentsCommand;
use Evp\Bundle\PlaisBundle\Entity\SkippedDocument;
use Evp\Bundle\PlaisBundle\Repository\SkippedDocumentRepository;
use Evp\Bundle\PlaisBundle\Service\CardfileDocumentManager;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ProcessSkippedDocumentsCommandTest extends CommandTestCase
{
    /** @var ProcessSkippedDocumentsCommand */
    protected $command;
    private SkippedDocumentRepository $skippedDocumentRepository;
    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private SepaEDocReader $eDocReader;
    private CardfileDocumentManager $cardfileDocumentManager;

    protected function setUp(): void
    {
        $this->skippedDocumentRepository = $this->getContainer()->get('evp_plais.repository.skipped_documents');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->eDocReader = $this->getContainer()->get('evp_lb_integration.edoc.reader');
        $this->cardfileDocumentManager = $this->getContainer()->get('evp_plais.service.cardfile_document_manager');

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.skipped_ibans.process_skipped_documents'
        );
    }

    /** @dataProvider successCaseDataProvider */
    public function testSuccessCase(array $inputIbans, array $inputDocuments, string $file): void
    {
        $eDocXmlContent = file_get_contents($file);

        $eDoc = $this->eDocReader->read($eDocXmlContent);
        foreach ($eDoc->getDocuments() as $document) {
            $this->cardfileDocumentManager->saveSkippedDocumentAndIban($document, $eDoc);
        }
        $this->entityManager->flush();

        $skippedDocuments = $this->skippedDocumentRepository->findAll();
        $this->assertEquals(array_map(fn(SkippedDocument $skippedDocument) => $skippedDocument->getDocId(), $skippedDocuments), $inputDocuments);

        $this->commandTester->execute([
            'command' => $this->command->getName(),
            'docId' => $inputDocuments[array_key_first($inputDocuments)],
        ]);

        $skippedDocuments = $this->skippedDocumentRepository->findAll();
        $this->assertCount(0, $skippedDocuments);
    }

    public function successCaseDataProvider(): array
    {
        return [
            'Data for processing' => [
                'ibans' => ['********************'],
                'documents' => ['doc_id'],
                'file' => __DIR__ . '/../../MessageProcessor/Resources/cardfile.cardcan.xml',
            ],
        ];
    }
}
