<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Entity\ArrestResponse;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ArrestResponseProcessorCommandTest extends CommandTestCase
{
    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private ArrestResponseRepository $arrestResponseRepository;

    public function setUp(): void
    {
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.arrest_response_processor'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->arrestResponseRepository = $this->getContainer()->get('evp_plais.repository.arrest_response');
    }

    private function runCommand()
    {
        $this->commandTester->execute(array('command' => $this->command->getName()));
    }

    public function testProcessArrestResponse(): void
    {
        $this->createArrestResponse($this->createArrest());
        $this->createArrestResponse($this->createArrest())->setStatus(ArrestResponse::STATUS_SENT);
        $this->createArrestResponse($this->createArrest());

        $this->entityManager->flush();

        $arrestResponse = $this->arrestResponseRepository->findBy(['status' => ArrestResponse::STATUS_PENDING]);
        $this->assertEquals(2, count($arrestResponse));

        $this->runCommand();

        $arrestResponse = $this->arrestResponseRepository->findBy(['status' => ArrestResponse::STATUS_SENT]);
        $this->assertEquals(3, count($arrestResponse));
    }

    private function createArrest(): Arrest
    {
        $arrest = (new Arrest())
            ->setStatus(Arrest::STATUS_NEW)
            ->setArrestIdentification('123456')
            ->setType(Arrest::TYPE_ARREST)
        ;
        $this->entityManager->persist($arrest);

        return $arrest;
    }

    private function createArrestResponse(Arrest $arrest): ArrestResponse
    {
        $arrestResponse = (new ArrestResponse())
            ->setDocId('EV160704C8C44A1C')
            ->setArrest($arrest)
            ->setType(ArrestResponse::TYPE_BLOCK)
            ->setResponseStatus(ArrestResponse::STATUS_PENDING)
            ->setReferenceSenderBic('SNCTLT21XXX')
            ->setReferenceDocId('RC160303AAACS003')
        ;
        $this->entityManager->persist($arrestResponse);

        return $arrestResponse;
    }
}
