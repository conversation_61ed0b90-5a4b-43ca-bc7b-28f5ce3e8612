<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\PlaisBundle\Command\ProcessNotValidArrestResponsesCommand;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Entity\ArrestResponse;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Evp\Component\Money\Money;
use org\bovigo\vfs\vfsStream;
use org\bovigo\vfs\vfsStreamDirectory;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class ProcessNotValidArrestResponseCommandTest extends CommandTestCase
{
    /**
     * @var ProcessNotValidArrestResponsesCommand
     */
    protected $command;

    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private ArrestResponseRepository $arrestResponseRepository;

    private vfsStreamDirectory $vfsStreamDirectory;

    public function setUp(): void
    {
        $this->vfsStreamDirectory = vfsStream::setup();

        $this->getContainer()->set(
            'evp_plais.command.process_not_valid_arrest_responses',
            new ProcessNotValidArrestResponsesCommand(
                $this->getContainer()->get('evp_plais.repository.arrest_response'),
                $this->getContainer()->get('doctrine.orm.entity_manager'),
                $this->vfsStreamDirectory->url(),
                $this->getContainer()->get('paysera.console_progress_bar_helper'),
            )
        );

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.process_not_valid_arrest_responses'
        );
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->arrestResponseRepository = $this->getContainer()->get('evp_plais.repository.arrest_response');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param int $count
     *
     * @dataProvider countArrestResponsesProvider
     */
    public function testProcessNotValidArrestResponses(int $count): void
    {
        $this->prepareNotValidArrestResponses($count);
        $this->prepareLogFiles($count);

        $this->assertEquals($count, $this->getFolderFilesCount($this->vfsStreamDirectory->url()));

        $this->entityManager->flush();

        $this->assertEquals(
            $count,
            $this->arrestResponseRepository->getNotValidResponsesCount()
        );
        $this->assertCount(
            $count * 2,
            $this->arrestResponseRepository->findAll()
        );

        $this->runCommand();

        $this->assertEquals(0, $this->getFolderFilesCount($this->vfsStreamDirectory->url()));
        $this->assertEquals(
            0,
            $this->arrestResponseRepository->getNotValidResponsesCount()
        );
        $this->assertCount(
            $count * 3,
            $this->arrestResponseRepository->findAll()
        );
        $this->assertCount(
            $count,
            $this->arrestResponseRepository->findBy(['status' => ArrestResponse::STATUS_RECREATED])
        );
        $this->assertCount(
            $count,
            $this->arrestResponseRepository->findBy(['status' => ArrestResponse::STATUS_PENDING])
        );
        $this->assertCount(
            $count,
            $this->arrestResponseRepository->findBy(['status' => ArrestResponse::STATUS_SENT])
        );
    }

    public function countArrestResponsesProvider(): array
    {
        return [
            [
                5,
            ],
            [
                10,
            ],
            [
                4,
            ],
            [
                1,
            ],
            [
                0,
            ],
        ];
    }

    private function runCommand(): void
    {
        $this->commandTester->execute([
            'command' => $this->command->getName(),
            '--purgeResponseLogs' => null,
        ]);
    }

    private function prepareNotValidArrestResponses(int $count): void
    {
        for ($i = 0; $i < $count; $i++) {
            $this->prepareArrestResponse($i, ArrestResponse::STATUS_NOT_VALID);
        }

        for ($i = $count; $i < $count * 2; $i++) {
            $this->prepareArrestResponse($i, ArrestResponse::STATUS_SENT);
        }
    }

    private function prepareArrestResponse(int $prefix, string $responseStatus): void
    {
        $money = new Money($prefix + 1, 'EUR');
        $client = $this->fixturesHelper->createClientNatural($prefix + 1);
        $account = $this->fixturesHelper->createAccount(
            $client,
            'EVP' . $prefix,
            'LT' . $prefix
        );
        $hold = $this->fixturesHelper->createHold(
            $account,
            $money
        );
        $arrest = $this->fixturesHelper->createArrest(
            $account,
            $money,
            '123',
            Arrest::STATUS_NEW,
            Arrest::TYPE_ARREST,
            $hold
        );

        $this->fixturesHelper->createArrestResponse($arrest, $responseStatus);
    }

    private function prepareLogFiles(int $count): void
    {
        for ($i = 0; $i < $count; $i++) {
            $path = sprintf(
                '%s%s%s_log.xml',
                $this->vfsStreamDirectory->url(),
                DIRECTORY_SEPARATOR,
                (string) $i
            );

            file_put_contents($path, 'just test content');
        }
    }

    private function getFolderFilesCount(string $path): int
    {
        if (!file_exists($path)) {
            return 0;
        }

        return count(array_diff(scandir($path), ['.', '..']));
    }
}
