<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\Monitoring;

use Evp\Bundle\PlaisBundle\Command\Monitoring\MonitorNotConfirmedMessagesCommand;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Evp\Bundle\PlaisBundle\Repository\BalanceReportLogRepository;
use Evp\Bundle\PlaisBundle\Tests\Traits\WritePointsTrait;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorNotConfirmedMessagesCommandTest extends CommandTestCase
{
    /** @var MonitorNotConfirmedMessagesCommand */
    protected $command;

    private CommandTester $commandTester;
    private MockObject $arrestResponseRepository;
    private MockObject $balanceReportLogRepository;
    private MockObject $monitoringClient;

    protected function setUp(): void
    {
        $this->monitoringClient = $this->createMock(MonitoringClient::class);
        $this->getContainer()->set('paysera_monitoring.monitoring_client', $this->monitoringClient);

        $this->arrestResponseRepository = $this->createMock(ArrestResponseRepository::class);
        $this->getContainer()->set('evp_plais.repository.arrest_response', $this->arrestResponseRepository);

        $this->balanceReportLogRepository = $this->createMock(BalanceReportLogRepository::class);
        $this->getContainer()->set('evp_plais.repository.balance_report_log', $this->balanceReportLogRepository);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plaid.command.monitoring.not_confirmed_messages_command'
        );
    }

    public function testProcess(): void
    {
        $this->arrestResponseRepository->method('countResponsesByStatusAndDateTo')->willReturn(1);
        $this->balanceReportLogRepository->method('countByStatusesAndDateTo')->willReturn(2);

        $this->monitoringClient->expects($this->exactly(2))->method('writeValue')->withConsecutive(
            ['evpbank.plais_balance_report', 1, ['problem' => 'not_confirmed_for_long_time_arrest_responses']],
            ['evpbank.plais_balance_report', 2, ['problem' => 'not_confirmed_for_long_time_balance_logs']],
        );

        $this->commandTester->execute(['command' => $this->command->getName()]);
    }

    public function testProcessWithZeros(): void
    {
        $this->arrestResponseRepository->method('countResponsesByStatusAndDateTo')->willReturn(0);
        $this->balanceReportLogRepository->method('countByStatusesAndDateTo')->willReturn(0);

        $this->monitoringClient->expects($this->exactly(2))->method('writeValue')->withConsecutive(
            ['evpbank.plais_balance_report', 0, ['problem' => 'not_confirmed_for_long_time_arrest_responses']],
            ['evpbank.plais_balance_report', 0, ['problem' => 'not_confirmed_for_long_time_balance_logs']],
        );

        $this->commandTester->execute(['command' => $this->command->getName()]);
    }
}
