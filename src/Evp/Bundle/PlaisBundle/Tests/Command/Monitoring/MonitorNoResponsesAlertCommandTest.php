<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\Monitoring;

use Evp\Bundle\PlaisBundle\Command\Monitoring\MonitorNoResponsesAlertCommand;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorNoResponsesAlertCommandTest extends CommandTestCase
{
    /** @var MonitorNoResponsesAlertCommand */
    protected $command;
    private CommandTester $commandTester;
    private MockObject $arrestResponseRepository;

    protected function setUp(): void
    {
        $this->arrestResponseRepository = $this->createMock(ArrestResponseRepository::class);
        $this->getContainer()->set('evp_plais.repository.arrest_response', $this->arrestResponseRepository);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.monitoring.alert_no_responses_command'
        );
    }

    public function testProcess(): void
    {
        $this->arrestResponseRepository->method('countByDate')->willReturn(0);
        $this->commandTester->execute(['command' => $this->command->getName()]);

        $this->expectErrorLog('PLAIS exceptional case: there are no responses at 1 PM');
    }

    public function testProcessFailure(): void
    {
        $this->arrestResponseRepository->method('countByDate')->willReturn(1);
        $this->commandTester->execute(['command' => $this->command->getName()]);

        $this->expectNoErrorsLogged();
    }
}
