<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\Monitoring;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Bundle\PlaisBundle\Command\Monitoring\MonitorActiveArrestsCommand;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorActiveArrestsCommandTest extends CommandTestCase
{
    /** @var MonitorActiveArrestsCommand */
    protected $command;

    private CommandTester $commandTester;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private MockObject $monitoringClient;
    private string $monitoringKey;

    protected function setUp(): void
    {
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->monitoringKey = $this->getContainer()->getParameter('evp_plais_balance_report_surveillance.monitor_key');

        $this->monitoringClient = $this->createMock(MonitoringClient::class);
        $this->getContainer()->set('paysera_monitoring.monitoring_client', $this->monitoringClient);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.monitoring.active_arrests_command'
        );
    }

    public function testProcess(): void
    {
        $expectations = array_merge(
            $this->getExpectationsForClientPartnerCodeProblem(),
        );

        $this->monitoringClient->expects($this->exactly(count($expectations)))
            ->method('writeValue')
            ->withConsecutive(...$expectations)
        ;

        $this->entityManager->flush();
        $this->commandTester->execute(['command' => $this->command->getName()]);
    }

    private function getExpectationsForClientPartnerCodeProblem(): array
    {
        $residenceCountries = ['lt', 'al', 'xk', 'ge'];

        foreach ($residenceCountries as $key => $residenceCountry) {
            $covenanteeIdShift = $key * 10;
            $covenanteeId = 100 + $covenanteeIdShift;
            $covenanteeIdEnd = 109 + $covenanteeIdShift;

            $this->createAccountsAndArrests(
                range($covenanteeId, $covenanteeIdEnd),
                IdentificationLevels::IDENTIFIED,
                true,
                false,
                $residenceCountry
            );
        }

        $commonTags = ['problem' => 'active_arrest_client_partner_code'];
        $expectations = [
            [
                'count' => 20,
                'tags' => ['partner_code' => LicensedPartner::PAYSERA_LITHUANIA],
            ], [
                'count' => 20,
                'tags' => ['partner_code' => LicensedPartner::PAYSERA_ALBANIA],
            ], [
                'count' => 20,
                'tags' => ['partner_code' => LicensedPartner::PAYSERA_KOSOVO],
            ], [
                'count' => 20,
                'tags' => ['partner_code' => LicensedPartner::PAYSERA_GEORGIA],
            ], [
                'count' => 0,
                'tags' => ['partner_code' => 'another'],
            ],
        ];

        return $this->prepareExpectations($expectations, $commonTags);
    }

    private function prepareExpectations(array $expectations, array $commonTags): array
    {
        return array_map(
            fn(array $expectation) => [
                'monitoringKey' => $this->monitoringKey,
                'count' => $expectation['count'],
                'tags' => array_merge($expectation['tags'], $commonTags),
            ],
            $expectations
        );
    }

    private function createAccountsAndArrests(
        array $covenanteeIds,
        string $identification,
        bool $active,
        bool $closed,
        string $residenceCountry = 'lt'
    ): void {
        foreach ($covenanteeIds as $covenanteeId) {
            $client = $this->fixturesHelper->createClientNatural(
                $covenanteeId,
                $identification,
                null,
                null,
                null,
                'lt',
                'LT',
                null,
                $residenceCountry
            );

            $account = $this->fixturesHelper->createAccount($client, $covenanteeId, $covenanteeId);
            $account->setActive($active);
            $account->setClosed($closed);

            $this->fixturesHelper->createArrest($account, null, $covenanteeId);
            $this->fixturesHelper->createArrest($account, null, $covenanteeId);
        }
    }
}
