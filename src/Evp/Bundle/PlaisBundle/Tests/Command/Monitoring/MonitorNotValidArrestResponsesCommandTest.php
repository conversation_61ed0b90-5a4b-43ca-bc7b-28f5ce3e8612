<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Tests\Command\Monitoring;

use Evp\Bundle\PlaisBundle\Command\Monitoring\MonitorNotValidArrestResponsesCommand;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorNotValidArrestResponsesCommandTest extends CommandTestCase
{
    private CommandTester $commandTester;

    /** @var MonitoringClient|MockObject */
    private MonitoringClient $monitoringClient;
    private MockObject $arrestResponseRepository;

    /** @var MonitorNotValidArrestResponsesCommand */
    protected $command;

    protected function setUp(): void
    {
        $this->arrestResponseRepository = $this->createMock(ArrestResponseRepository::class);
        $this->getContainer()->set('evp_plais.repository.arrest_response', $this->arrestResponseRepository);

        $this->monitoringClient = $this->createMock(MonitoringClient::class);
        $this->getContainer()->set('paysera_monitoring.monitoring_client', $this->monitoringClient);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_plais.command.monitoring.not_valid_arrest_responses_command'
        );
    }

    public function testProcess(): void
    {
        $this->arrestResponseRepository->method('getNotValidResponsesCount')->willReturn(1);
        $this->monitoringClient->expects($this->once())->method('writeValue')->with(
            'evpbank.plais_balance_report',
            1,
            [
                'problem' => 'not_valid_arrest_responses',
            ]
        );

        $this->commandTester->execute(['command' => $this->command->getName()]);
    }
}
