<?php

namespace Evp\Bundle\PlaisBundle\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Event\HoldCreatedEvent;
use Evp\Bundle\BankHoldBundle\Exception\HoldProcessorException;
use Evp\Bundle\BankHoldBundle\HoldEvents;
use Evp\Bundle\BankHoldBundle\Service\HoldFactory;
use Evp\Bundle\BankHoldBundle\Service\HoldProcessor;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Component\Doctrine\AutoCommit;
use Evp\Component\Money\Money;
use Psr\Log\LoggerInterface;
use SimpleXMLElement;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Translation\TranslatorInterface;

class ArrestHoldManager
{
    private EntityManager $entityManager;
    private TranslatorInterface $translator;
    private HoldProcessor $holdProcessor;
    private HoldFactory $holdFactory;
    private Money $chargeMoney;
    private AutoCommit $autoCommit;
    private EventDispatcherInterface $eventDispatcher;
    private LoggerInterface $logger;

    public function __construct(
        EntityManager $entityManager,
        TranslatorInterface $translator,
        HoldProcessor $holdProcessor,
        HoldFactory $holdFactory,
        Money $chargeMoney,
        AutoCommit $autoCommit,
        EventDispatcherInterface $eventDispatcher,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->translator = $translator;
        $this->autoCommit = $autoCommit;
        $this->holdProcessor = $holdProcessor;
        $this->holdFactory = $holdFactory;
        $this->chargeMoney = $chargeMoney;
        $this->eventDispatcher = $eventDispatcher;
        $this->logger = $logger;
    }

    /**
     * @throws HoldProcessorException
     */
    public function createAndProcessHold(
        Arrest $arrest,
        Account $account,
        SimpleXMLElement $xml,
        string $methodCalledFrom
    ) {
        $this->logger->info(
            __METHOD__,
            [
                $methodCalledFrom,
                $arrest->getId(),
                $account->getId(),
            ]
        );
        $hold = new Hold();
        $hold->setConversionAllowed(true);
        $hold->setIgnoreAvailableCredit(true);
        $hold->setInstallmentHoldAllowed(true);
        $hold->setAccount($account);

        if ($arrest->getArrestAmount() !== null) {
            $hold->setAmountMoney($arrest->getArrestAmount());

            // Add our charge on top of PLAIS charge
            if ($arrest->getType() === Arrest::TYPE_CHARGE) {
                $this->logger->info('Charge hold. Adding charge money amount',
                    [
                        $methodCalledFrom,
                        $arrest->getId(),
                        $account->getId(),
                    ]
                );
                $hold->setAmountMoney(
                    $hold->getAmountMoney()->add($this->chargeMoney)
                );
            }
        }

        $hold->setDetails(
            $this->translator->trans(
                'arrest_hold.details',
                ['%comment%' => $this->getArrestDetails($xml)],
                'EvpPlaisBundle',
                $account->getClient()->getLocale()
            )
        );

        $this->eventDispatcher->dispatch(HoldEvents::HOLD_CREATED, new HoldCreatedEvent($hold));

        $arrest->getHolds()->add($hold);
        $this->entityManager->persist($hold);
        $this->holdProcessor->processHold($hold);
    }

    /**
     * @param Arrest $arrest
     * @return Hold|null
     */
    public function getActiveArrestHold(Arrest $arrest)
    {
        return $this->getArrestHoldByStatusList(
            $arrest,
            [Hold::STATUS_HOLD, Hold::STATUS_WAITING_FUNDS]
        );
    }

    /**
     * @param Arrest $arrest
     * @param string[] $statusList
     *
     * @return Hold|null
     */
    public function getArrestHoldByStatusList(Arrest $arrest, array $statusList)
    {
        foreach ($arrest->getHolds() as $hold) {
            if (in_array($hold->getStatus(), $statusList)) {
                return $hold;
            }
        }

        return null;
    }

    /**
     * @param Arrest $arrest
     */
    public function recreateHold(Arrest $arrest)
    {
        $this->autoCommit->begin();
        $lastHold = $arrest->getHolds()->last();

        if ($lastHold !== null && $lastHold->getStatus() === Hold::STATUS_DONE) {
            $newHold = $this->holdFactory->createFromExisting($lastHold);
            $newHold->setAmountMoney($arrest->getArrestAmount());
            $arrest->getHolds()->add($newHold);
            $this->holdProcessor->processHold($newHold);
        }
    }

    /**
     * @param \SimpleXMLElement $xml
     * @return string
     */
    private function getArrestDetails(SimpleXMLElement $xml)
    {
        $details = [];

        if (in_array((string)$xml->RemittanceInformation->Execute->Type, ['Antstolis', 'TAAR'], true)) {
            $details[] = (string)$xml->Payee->Name;
        }

        if ($xml->RemittanceInformation->Execute->ID !== null) {
            $details[] = (string)$xml->RemittanceInformation->Execute->ID;
        }

        if ($xml->RemittanceInformation->Execute->Reason !== null) {
            $details[] = (string)$xml->RemittanceInformation->Execute->Reason;
        }

        return implode(', ', $details);
    }
}
