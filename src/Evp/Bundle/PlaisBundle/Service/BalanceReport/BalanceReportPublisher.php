<?php

namespace Evp\Bundle\PlaisBundle\Service\BalanceReport;

use DOMException;
use Evp\Bundle\LbIntegrationBundle\Entity\Litas\Doc;
use Evp\Bundle\LbIntegrationBundle\Service\Helper\DocumentIdentificationGenerator;
use Evp\Bundle\PlaisBundle\Entity\BalanceReportLog;
use Evp\Bundle\PlaisBundle\Exception\ClientCodeNotFoundException;
use Evp\Bundle\PlaisBundle\Exception\IdentityDocumentException;
use Evp\Bundle\PlaisBundle\Service\ArrestManager;
use Evp\Bundle\PlaisBundle\Service\Mapper\ActualBalanceExportMapper;
use Evp\Bundle\PlaisBundle\Service\PlaisXmlValidator;
use LibXMLError;
use OldSound\RabbitMqBundle\RabbitMq\Producer;

class BalanceReportPublisher
{
    private ActualBalanceExportMapper $actualBalanceExportMapper;
    private Producer $producer;
    private DocumentIdentificationGenerator $documentIdentificationGenerator;
    private ArrestManager $arrestManager;
    private PlaisXmlValidator $plaisXmlValidator;

    public function __construct(
        ActualBalanceExportMapper $actualBalanceExportMapper,
        Producer $producer,
        DocumentIdentificationGenerator $documentIdentificationGenerator,
        ArrestManager $arrestManager,
        PlaisXmlValidator $plaisXmlValidator
    ) {
        $this->actualBalanceExportMapper = $actualBalanceExportMapper;
        $this->producer = $producer;
        $this->documentIdentificationGenerator = $documentIdentificationGenerator;
        $this->arrestManager = $arrestManager;
        $this->plaisXmlValidator = $plaisXmlValidator;
    }

    /**
     * @param BalanceReportLog[] $logs
     * @param array $accountCCodes
     */
    public function publish(array $logs, array $accountCCodes = []): void
    {
        $documents = [];
        $logsIncludedToReport = [];
        $reportCode = $this->documentIdentificationGenerator->generateCode();

        foreach ($logs as $log) {
            $document = $this->processBalanceReportLog($log, $accountCCodes, $reportCode);
            if ($document === null) {
                continue;
            }

            $documents[] = $document;
            $logsIncludedToReport[] = $log;
        }

        if (count($documents) === 0) {
            return;
        }

        $reportXml = $this->actualBalanceExportMapper->mapToXml($reportCode, $documents);
        $validationErrors = $this->validateReport($reportXml);

        if (count($validationErrors) > 0) {
            $this->setStatusAndValidationErrorsToLogs($logsIncludedToReport, $validationErrors);
            return;
        }

        $this->producer->publish($reportXml, 'out.actual_balance');
    }

    private function processBalanceReportLog(BalanceReportLog $log, array $accountCCodes, string $reportCode): ?Doc
    {
        if ($log->getStatus() !== BalanceReportLog::STATUS_QUEUED) {
            return null;
        }

        if (!$this->arrestManager->hasActiveArrests($log->getAccount())) {
            $log->setStatus(BalanceReportLog::STATUS_SKIPPED);
            $log->addDetails(BalanceReportLog::DETAILS_ARREST_NOT_FOUND);
            return null;
        }

        $cCodeType = $accountCCodes[$log->getAccount()->getId()] ?? null;
        $docId = $this->documentIdentificationGenerator->generateCode();

        try {
            $document = $this->actualBalanceExportMapper->mapToDocument(
                $docId,
                $log->getAccount(),
                $log->getBalance(),
                $cCodeType
            );
        } catch (ClientCodeNotFoundException $exception) {
            $log->setStatus(BalanceReportLog::STATUS_FAILED);
            $log->addDetails(BalanceReportLog::DETAILS_CLIENT_CODE_NOT_FOUND);
            return null;
        } catch (IdentityDocumentException $exception) {
            $log->setStatus(BalanceReportLog::STATUS_FAILED);
            $log->addDetails(BalanceReportLog::DETAILS_IDENTITY_DOCUMENT_NOT_FOUND);
            return null;
        } catch (DOMException $exception) {
            $log->setStatus(BalanceReportLog::STATUS_FAILED);
            $log->addDetails(BalanceReportLog::DETAILS_DOM_EXCEPTION);
            return null;
        }

        $validationErrors = $this->validateDocument($reportCode, $document);
        if (count($validationErrors) > 0) {
            $this->setStatusAndValidationErrorsToLogs([$log], $validationErrors);
            return null;
        }

        $log
            ->setStatus(BalanceReportLog::STATUS_PUBLISHED)
            ->setDocumentId($docId)
        ;

        return $document;
    }

    /**
     * @return array|LibXMLError[]
     */
    private function validateDocument(string $reportCode, Doc $document): array
    {
        $xmlForValidation = $this->actualBalanceExportMapper->mapToXml($reportCode, [$document]);
        return $this->validateReport($xmlForValidation);
    }

    /**
     * @return array|LibXMLError[]
     */
    private function validateReport(string $reportXml): array
    {
        return $this->plaisXmlValidator->validate($reportXml);
    }

    /**
     * @param BalanceReportLog[] $logs
     * @param array|LibXMLError[] $validationErrors
     * @return void
     */
    private function setStatusAndValidationErrorsToLogs(array $logs, array $validationErrors): void
    {
        foreach ($logs as $log) {
            $log->setStatus(BalanceReportLog::STATUS_NOT_VALID);

            foreach ($validationErrors as $validationError) {
                if ($validationError instanceof LibXMLError) {
                    $log->addDetails(trim($validationError->message));
                }
            }
        }
    }
}
