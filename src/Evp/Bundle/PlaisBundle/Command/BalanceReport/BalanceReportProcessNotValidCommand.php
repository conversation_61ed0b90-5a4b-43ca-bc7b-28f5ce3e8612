<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command\BalanceReport;

use Doctrine\ORM\EntityManagerInterface;
use DOMException;
use Evp\Bundle\LbIntegrationBundle\Service\Helper\DocumentIdentificationGenerator;
use Evp\Bundle\PlaisBundle\Entity\BalanceReportLog;
use Evp\Bundle\PlaisBundle\Exception\ClientCodeNotFoundException;
use Evp\Bundle\PlaisBundle\Exception\IdentityDocumentException;
use Evp\Bundle\PlaisBundle\Repository\BalanceReportLogRepository;
use Evp\Bundle\PlaisBundle\Service\Mapper\ActualBalanceExportMapper;
use Evp\Bundle\PlaisBundle\Service\PlaisXmlValidator;
use Exception;
use LibXMLError;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class BalanceReportProcessNotValidCommand extends Command
{
    private const BATCH_SIZE = 500;

    private BalanceReportLogRepository $balanceReportLogRepository;
    private ActualBalanceExportMapper $actualBalanceExportMapper;
    private DocumentIdentificationGenerator $documentIdentificationGenerator;
    private PlaisXmlValidator $plaisXmlValidator;
    private EntityManagerInterface $entityManager;
    private ConsoleProgressBarHelper $progressBarHelper;

    /** @var BalanceReportLog[] */
    private array $validLogs;
    /** @var LibXMLError[] */
    private array $notValidLogs;

    public function __construct(
        BalanceReportLogRepository $balanceReportLogRepository,
        ActualBalanceExportMapper $actualBalanceExportMapper,
        DocumentIdentificationGenerator $documentIdentificationGenerator,
        PlaisXmlValidator $plaisXmlValidator,
        EntityManagerInterface $entityManager,
        ConsoleProgressBarHelper $progressBarHelper
    ) {
        parent::__construct();

        $this->balanceReportLogRepository = $balanceReportLogRepository;
        $this->actualBalanceExportMapper = $actualBalanceExportMapper;
        $this->documentIdentificationGenerator = $documentIdentificationGenerator;
        $this->plaisXmlValidator = $plaisXmlValidator;
        $this->entityManager = $entityManager;
        $this->progressBarHelper = $progressBarHelper;

        $this->validLogs = [];
        $this->notValidLogs = [];
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:plais:balance-report:process-not-valid')
            ->setDescription('Validate not valid balance reports and set pending status for valid ones')
            ->addOption(
                'logsFile',
                'f',
                InputOption::VALUE_OPTIONAL,
                'Store not valid logs validation messages to file instead of print'
            )
            ->addOption(
                'xmlDir',
                'd',
                InputOption::VALUE_OPTIONAL,
                'Save generated XML files for each not valid log with error description'
            )
            ->addOption(
                'limit',
                'l',
                InputOption::VALUE_OPTIONAL,
                'Limit balance reports for recreation'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $countLogs = $this->balanceReportLogRepository->countAllByStatus(BalanceReportLog::STATUS_NOT_VALID);
        if ($countLogs === 0) {
            $output->writeln('There is no logs with status not_valid');
            return 2;
        }

        $logsFile = $input->getOption('logsFile');
        $xmlDir = $input->getOption('xmlDir');
        $limit = $input->getOption('limit');

        if ($logsFile !== null) {
            $this->createDirectoryForFileIfNeeded($logsFile);
        }

        if ($xmlDir !== null && !file_exists($xmlDir)) {
            mkdir($xmlDir, 0777, true);
        }

        if ($limit !== null) {
            if (!is_numeric($limit) || (int) $limit <= 0) {
                throw new Exception("Provide valid limit option");
            }

            $limit = (int) $limit;
        }

        $this->recheckLogs($output, $countLogs, $xmlDir, $limit);
        $this->processValidLogs($output);
        $this->processNotValidLogs($output, $logsFile);

        $output->writeln('Done recreating not valid balance reports');

        return 0;
    }

    private function recheckLogs(OutputInterface $output, int $countLogs, ?string $xmlDir, ?int $limit): void
    {
        $output->writeln('Validating all not_valid balance report logs..');
        $progressBar = $this->createProgressBar($output, $countLogs);

        $reportCode = $this->documentIdentificationGenerator->generateCode();
        $offset = 0;

        do {
            $logs = $this->getLogsBatch($offset);
            $offset += self::BATCH_SIZE;

            foreach ($logs as $log) {
                $progressBar->advance();

                if (array_key_exists($log->getAccount()->getId(), $this->notValidLogs)) {
                    continue;
                }

                $errors = $this->validateLog($reportCode, $log, $xmlDir);

                if (count($errors)) {
                    $this->notValidLogs[$log->getAccount()->getId()] = $errors;
                } else {
                    $this->validLogs[] = $log;
                }

                if ($limit !== null && $progressBar->getProgress() >= $limit) {
                    break 2;
                }
            }
        } while (count($logs) > 0 && $progressBar->getProgress() <= $countLogs);

        $output->writeln("\n");
    }

    /**
     * @param int $offset
     * @return BalanceReportLog[]
     */
    private function getLogsBatch(int $offset): array
    {
        return $this->balanceReportLogRepository->findAllByStatus(
            BalanceReportLog::STATUS_NOT_VALID,
            $offset,
            self::BATCH_SIZE
        );
    }

    private function processValidLogs(OutputInterface $output): void
    {
        if (count($this->validLogs) === 0) {
            return;
        }

        $output->writeln('Processing valid logs: Setting pending status to BalanceReportLogs..');
        $progressBar = $this->createProgressBar($output, count($this->validLogs));

        foreach ($this->validLogs as $validLog) {
            $validLog->setStatus(BalanceReportLog::STATUS_PENDING);
            $progressBar->advance();
        }

        $this->entityManager->flush();

        $progressBar->finish();
        $output->writeln("\n");
    }

    private function processNotValidLogs(OutputInterface $output, ?string $logsFile): void
    {
        $output->writeln('Processing not valid logs..');

        $logsErrorMessagesText = '';

        /** @var LibXMLError[] $errors */
        foreach ($this->notValidLogs as $accountId => $errors) {
            $logsErrorMessagesText .= sprintf("|-- Account id: %d\n", $accountId);

            foreach ($errors as $error) {
                $logsErrorMessagesText .= sprintf("|---- %s\n", trim($error->message));
            }
        }

        if ($logsFile === null) {
            $output->writeln($logsErrorMessagesText);
        } else {
            file_put_contents($logsFile, $logsErrorMessagesText);
            $output->writeln(sprintf("Validation messages for not valid logs were stored to %s", $logsFile));
        }

        $output->writeln("");
    }

    /**
     * @param string $reportCode
     * @param BalanceReportLog $log
     * @param string|null $xmlDir
     * @return LibXMLError[]
     */
    private function validateLog(string $reportCode, BalanceReportLog $log, ?string $xmlDir): array
    {
        try {
            $document = $this->actualBalanceExportMapper->mapToDocument(
                $this->documentIdentificationGenerator->generateCode(),
                $log->getAccount(),
                $log->getBalance()
            );
        } catch (ClientCodeNotFoundException | IdentityDocumentException | DOMException $exception) {
            return [];
        }

        $xmlForValidation = $this->actualBalanceExportMapper->mapToXml($reportCode, [$document]);
        $validationErrors = $this->plaisXmlValidator->validate($xmlForValidation);

        if ($xmlDir !== null && count($validationErrors)) {
            $this->saveXmlWithErrors(
                sprintf("%s/%d.xml", $xmlDir, $log->getAccount()->getId()),
                $xmlForValidation,
                $validationErrors
            );
        }

        return $validationErrors;
    }

    private function createProgressBar(OutputInterface $output, int $count): ProgressBar
    {
        $progressBar = $this->progressBarHelper->createProgressBar($output, $count);
        $progressBar->setRedrawFrequency(1);
        $progressBar->start();

        return $progressBar;
    }

    private function saveXmlWithErrors(string $fileName, string $xml, array $validationErrors): void
    {
        $xml .= "\n\n";

        foreach ($validationErrors as $validationError) {
            $xml .= $validationError->message;
        }

        file_put_contents($fileName, $xml);
    }

    private function createDirectoryForFileIfNeeded(string $filePath): void
    {
        $paths = explode('/', $filePath);
        array_pop($paths);
        $dirPath = implode('/', $paths);

        if (!file_exists($dirPath)) {
            mkdir($dirPath, 0777, true);
        }
    }
}
