<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command\BalanceReport;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\PlaisBundle\Repository\BalanceReportLogRepository;
use InvalidArgumentException;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class BalanceReportChangeStatusCommand extends Command
{
    private BalanceReportLogRepository $balanceReportLogRepository;
    private EntityManagerInterface $entityManager;
    private ConsoleProgressBarHelper $consoleProgressBarHelper;
    private array $allowedStatuses;

    public function __construct(
        BalanceReportLogRepository $balanceReportLogRepository,
        EntityManagerInterface $entityManager,
        ConsoleProgressBarHelper $consoleProgressBarHelper,
        array $allowedStatuses
    ) {
        parent::__construct();
        $this->balanceReportLogRepository = $balanceReportLogRepository;
        $this->entityManager = $entityManager;
        $this->consoleProgressBarHelper = $consoleProgressBarHelper;
        $this->allowedStatuses = $allowedStatuses;
    }

    protected function configure()
    {
        $this
            ->setName('evp:plais:balance-report:change-status')
            ->addArgument('status', InputArgument::REQUIRED, 'Status to change')
            ->addArgument('ids', InputArgument::REQUIRED | InputArgument::IS_ARRAY, 'List of ids to change')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $status = $this->resolveStatus($input);
        $balanceIds = $this->resolveBalanceIds($input);
        $progressBar = $this->consoleProgressBarHelper->createProgressBar($output, count($balanceIds));

        $this->updateBalances($balanceIds, $status, $progressBar);
        $this->entityManager->flush();

        $output->writeln("\n<info>Done</info>");
        return 0;
    }

    private function updateBalances(array $balanceIds, string $status, ProgressBar $progressBar): void
    {
        foreach ($balanceIds as $balanceId) {
            $progressBar->advance();

            $balance = $this->balanceReportLogRepository->findOneById((int) $balanceId);
            if ($balance === null) {
                throw new InvalidArgumentException(sprintf('Balance with id "%s" not found', $balanceId));
            }

            $balance->setStatus($status);
        }
    }

    private function resolveStatus(InputInterface $input): string
    {
        $status = $input->getArgument('status');
        if (!in_array($status, $this->allowedStatuses, true)) {
            throw new InvalidArgumentException(sprintf(
                'Status "%s" is not allowed. Allowed statuses: %s',
                $status,
                implode(', ', $this->allowedStatuses)
            ));
        }

        return $status;
    }

    private function resolveBalanceIds(InputInterface $input): array
    {
        $ids = $input->getArgument('ids');

        if (count($ids) !== count(array_unique($ids))) {
            throw new InvalidArgumentException('Ids must be unique');
        }

        return $ids;
    }
}
