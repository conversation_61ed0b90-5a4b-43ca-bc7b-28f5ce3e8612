<?php

namespace Evp\Bundle\PlaisBundle\Command\SkippedIbans;

use Evp\Bundle\PlaisBundle\Repository\SkippedDocumentRepository;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class SkippedDocumentsDeleteCommand extends Command
{
    private SkippedDocumentRepository $skippedDocumentRepository;

    public function __construct(SkippedDocumentRepository $skippedDocumentRepository) {
        parent::__construct();
        $this->skippedDocumentRepository = $skippedDocumentRepository;
    }

    protected function configure()
    {
        $this
            ->setName('evp:plais:skipped-ibans:delete-documents')
            ->setDescription('Deletes provided documents')
            ->addArgument('documentIds', InputArgument::REQUIRED | InputArgument::IS_ARRAY)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $documentIds = $input->getArgument('documentIds');
        $this->skippedDocumentRepository->deleteByIds($documentIds);
        $output->writeln('Finished deleting');
        return 0;
    }
}
