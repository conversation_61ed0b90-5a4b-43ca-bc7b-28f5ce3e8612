<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Repository\ArrestRepository;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use DateTime;

class ReleaseHoldForStuckArrestCommand extends Command
{
    private EntityManager $entityManager;
    private ArrestRepository $arrestRepository;

    public function __construct(
        EntityManager $entityManager,
        ArrestRepository $arrestRepository
    ) {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->arrestRepository = $arrestRepository;
    }

    protected function configure(): void
    {
        $this
            ->setName('paysera:plais:release-hold-for-stuck-arrest')
            ->setDescription('Releases hold for provided stuck arrest.')
            ->addArgument(
                'arrestId',
                InputArgument::REQUIRED,
                'Arrested account with holds to fix'
            )
            ->addOption(
                'releaseAll',
                null,
                InputOption::VALUE_NONE,
                'Release all holds instead of releasing only not done holds'
            )
            ->addOption(
                'overwriteReleaseDate',
                null,
                InputOption::VALUE_NONE,
                'Overwrite release hold dates that already had been released'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $arrest = $this->arrestRepository->findOneById((int) $input->getArgument('arrestId'));

        if ($arrest === null) {
            $output->writeln('Arrest not found');
            return 1;
        }

        foreach ($arrest->getHolds() as $hold) {
            if (
                ($input->getOption('releaseAll') || $hold->getStatus() !== Hold::STATUS_DONE)
                && ($input->getOption('overwriteReleaseDate') || !$hold->getRelease())
            ) {
                $hold
                    ->setReleaseOn(new DateTime())
                    ->setRelease(true)
                ;
            }
        }

        $arrest->setStatus(Arrest::STATUS_CANCELLED);
        $this->entityManager->flush();

        $output->writeln(sprintf('Hold released for arrest: %s', $arrest->getId()));

        return 0;
    }
}
