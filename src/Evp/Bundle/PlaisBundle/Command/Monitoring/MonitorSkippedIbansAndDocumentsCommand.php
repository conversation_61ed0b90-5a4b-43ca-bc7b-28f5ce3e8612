<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command\Monitoring;

use Evp\Bundle\PlaisBundle\Repository\SkippedDocumentRepository;
use Evp\Bundle\PlaisBundle\Repository\SkippedIbanRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;

class MonitorSkippedIbansAndDocumentsCommand extends Command
{
    private const PROBLEM_EXISTING_SKIPPED_IBANS = 'existing_skipped_ibans';
    private const PROBLEM_EXISTING_SKIPPED_DOCUMENTS = 'existing_skipped_documents';

    private SkippedIbanRepository $skippedIbanRepository;
    private SkippedDocumentRepository $skippedDocumentRepository;
    private MonitoringClient $monitoringClient;
    private LoggerInterface $logger;
    private string $monitoringKey;

    public function __construct(
        SkippedIbanRepository $skippedIbanRepository,
        SkippedDocumentRepository $skippedDocumentRepository,
        MonitoringClient $monitoringClient,
        LoggerInterface $logger,
        string $monitoringKey
    ) {
        parent::__construct();

        $this->skippedIbanRepository = $skippedIbanRepository;
        $this->skippedDocumentRepository = $skippedDocumentRepository;
        $this->monitoringClient = $monitoringClient;
        $this->logger = $logger;
        $this->monitoringKey = $monitoringKey;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:plais:monitoring:skipped-ibans-and-documents')
            ->setDescription('The command sends to Grafana the number of skipped ibans and documents')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->logger->info(sprintf('%s - started', __METHOD__));

        $this->processExistingSkippedIbans();
        $this->processExistingSkippedDocuments();

        $this->logger->info(sprintf('%s - finished', __METHOD__));
        return 0;
    }

    private function processExistingSkippedIbans(): void
    {
        $skippedIbansCount = $this->skippedIbanRepository->count([]);

        $this->monitoringClient->writeValue(
            $this->monitoringKey,
            $skippedIbansCount,
            ['problem' => self::PROBLEM_EXISTING_SKIPPED_IBANS],
        );
    }

    private function processExistingSkippedDocuments(): void
    {
        $skippedDocumentsCount = $this->skippedDocumentRepository->count([]);

        $this->monitoringClient->writeValue(
            $this->monitoringKey,
            $skippedDocumentsCount,
            ['problem' => self::PROBLEM_EXISTING_SKIPPED_DOCUMENTS],
        );
    }
}
