<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command;

use DateTime;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Event\HoldCreatedEvent;
use Evp\Bundle\BankHoldBundle\HoldEvents;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\BankHoldBundle\Service\HoldProcessor;
use Evp\Bundle\PlaisBundle\Entity\Arrest;
use Evp\Bundle\PlaisBundle\Repository\ArrestRepository;
use Evp\Component\Money\Money;
use Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class RecreateHoldForArrestCommand extends Command
{
    protected EntityManager $entityManager;
    protected ArrestRepository $arrestRepository;
    protected HoldRepository $holdRepository;
    protected HoldProcessor $holdProcessor;
    protected EventDispatcherInterface $eventDispatcher;
    protected string $currency;

    public function __construct(
        EntityManager $entityManager,
        ArrestRepository $arrestRepository,
        HoldRepository $holdRepository,
        HoldProcessor $holdProcessor,
        EventDispatcherInterface $eventDispatcher,
        string $currency
    ) {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->arrestRepository = $arrestRepository;
        $this->holdRepository = $holdRepository;
        $this->holdProcessor = $holdProcessor;
        $this->eventDispatcher = $eventDispatcher;
        $this->currency = $currency;
    }

    protected function configure(): void
    {
        $this
            ->setName('evp:plais:recreate-hold-for-arrest')
            ->setDescription('Recreate hold for arrest')
            ->addArgument('arrestId', InputArgument::REQUIRED, 'Arrest id')
            ->addOption('amount', '', InputOption::VALUE_OPTIONAL, 'Hold amount')
            ->addOption('filterHoldDate', '', InputOption::VALUE_OPTIONAL, 'Date for filter reference hold. Y-m-d H:i:s')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $arrestId = (int) $input->getArgument('arrestId');
        $amount = $input->getOption('amount');
        $filterHoldDate = $input->getOption('filterHoldDate');

        $arrest = $this->arrestRepository->findOneById($arrestId);

        if ($arrest === null) {
            throw new Exception('Incorrect arrest ID given or arrest not exists');
        }

        if ($amount !== null) {
            $amount = new Money($amount, $this->currency);
        }

        if ($filterHoldDate !== null) {
            $filterHoldDate = new DateTime($filterHoldDate);
        }

        $this->entityManager->beginTransaction();

        try {
            $this->createHoldAndProcess($arrest, $amount, $filterHoldDate);

            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (Exception $exception) {
            $this->entityManager->rollback();

            $output->writeln($exception->getMessage());
            throw $exception;
        }

        $output->writeln('Done');

        return 0;
    }

    /**
     * @param Arrest $arrest
     * @param Money|null $amount
     * @param DateTime|null $filterHoldDate
     * @return void
     * @throws Exception
     */
    protected function createHoldAndProcess(Arrest $arrest, ?Money $amount, ?DateTime $filterHoldDate): void
    {
        $previousArrestHold = $arrest->getHolds()->last();

        if ($previousArrestHold === null || $previousArrestHold === false) {
            throw new Exception('Cannot get arrest last hold');
        }

        $holdAmount = $amount !== null
            ? $amount
            : $this->resolveHoldAmount($arrest, $filterHoldDate)
        ;

        $hold = new Hold();
        $hold->setConversionAllowed(true);
        $hold->setIgnoreAvailableCredit(true);
        $hold->setInstallmentHoldAllowed(true);
        $hold->setAccount($arrest->getAccount());
        $hold->setAmountMoney($holdAmount);
        $hold->setDetails($previousArrestHold->getDetails());

        $this->eventDispatcher->dispatch(HoldEvents::HOLD_CREATED, new HoldCreatedEvent($hold));

        $arrest->getHolds()->add($hold);
        $this->entityManager->persist($hold);

        $this->holdProcessor->processHold($hold);
    }

    /**
     * @param Arrest $arrest
     * @param DateTime|null $filterHoldDate
     * @return Money
     * @throws Exception
     */
    protected function resolveHoldAmount(Arrest $arrest, ?DateTime $filterHoldDate): Money
    {
        if ($arrest->getType() === Arrest::TYPE_CHARGE) {
            return $arrest->getArrestAmount();
        }

        /** @var Hold $hold */
        foreach (array_reverse($arrest->getHolds()->toArray()) as $hold) {
            if (
                $hold->getStatus() === Hold::STATUS_DONE
                && (
                    $filterHoldDate === null
                    || $hold->getCreatedAt() < $filterHoldDate
                )
            ) {
                return $hold->getAmountMoney();
            }
        }

        throw new Exception("No hold found for resolving amount");
    }
}
