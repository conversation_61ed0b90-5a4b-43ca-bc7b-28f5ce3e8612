<?php

declare(strict_types=1);

namespace Evp\Bundle\PlaisBundle\Command\ArrestResponse;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\PlaisBundle\Repository\ArrestResponseRepository;
use InvalidArgumentException;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Paysera\Component\ConsoleProgressBarHelper\Exception\ConsoleProgressBarHelperException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ArrestResponseChangeStatusCommand extends Command
{
    private ArrestResponseRepository $arrestResponseRepository;
    private EntityManagerInterface $entityManager;
    private ConsoleProgressBarHelper $consoleProgressBarHelper;
    private array $allowedStatuses;

    public function __construct(
        ArrestResponseRepository $arrestResponseRepository,
        EntityManagerInterface $entityManager,
        ConsoleProgressBarHelper $consoleProgressBarHelper,
        array $allowedStatuses
    ) {
        parent::__construct();
        $this->arrestResponseRepository = $arrestResponseRepository;
        $this->entityManager = $entityManager;
        $this->consoleProgressBarHelper = $consoleProgressBarHelper;
        $this->allowedStatuses = $allowedStatuses;
    }

    protected function configure()
    {
        $this
            ->setName('evp:plais:arrest-response:change-status')
            ->addArgument('status', InputArgument::REQUIRED, 'Status to change')
            ->addArgument('ids', InputArgument::REQUIRED | InputArgument::IS_ARRAY, 'List of ids to change')
        ;
    }

    /**
     * @throws ConsoleProgressBarHelperException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $status = $this->resolveStatus($input);
        $arrestResponseIds = $this->resolveArrestResponseIds($input);
        $progressBar = $this->consoleProgressBarHelper->createProgressBar($output, count($arrestResponseIds));

        $this->updateArrestResponses($arrestResponseIds, $status, $progressBar);
        $this->entityManager->flush();

        $output->writeln("\n<info>Done</info>");
        return 0;
    }

    private function updateArrestResponses(array $arrestResponseIds, string $status, ProgressBar $progressBar): void
    {
        foreach ($arrestResponseIds as $arrestResponseId) {
            $progressBar->advance();

            $arrestResponse = $this->arrestResponseRepository->find((int) $arrestResponseId);
            if ($arrestResponse === null) {
                throw new InvalidArgumentException(sprintf('Response with id "%s" not found', $arrestResponseId));
            }

            $arrestResponse->setStatus($status);
        }
    }

    private function resolveStatus(InputInterface $input): string
    {
        $status = $input->getArgument('status');
        if (!in_array($status, $this->allowedStatuses, true)) {
            throw new InvalidArgumentException(sprintf(
                'Status "%s" is not allowed. Allowed statuses: %s',
                $status,
                implode(', ', $this->allowedStatuses)
            ));
        }

        return $status;
    }

    private function resolveArrestResponseIds(InputInterface $input): array
    {
        $ids = $input->getArgument('ids');

        if (count($ids) !== count(array_unique($ids))) {
            throw new InvalidArgumentException('Ids must be unique');
        }

        return $ids;
    }
}
