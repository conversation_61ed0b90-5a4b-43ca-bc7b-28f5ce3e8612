<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="evp_currency.currency_client.cache_time">600</parameter>
        <parameter key="evp_currency.currency_client.cache_type">none</parameter>
    </parameters>

    <services>
        <service id="evp_currency.base_api_converter" abstract="true"
                 class="Evp\Bundle\CurrencyBundle\Service\BaseApiCurrencyConverter">
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="logger"/>
            <argument>3</argument>
        </service>

        <service id="evp_currency.evp_api_buy_currency_converter" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiBuyCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_buy_currency_converter"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.commercial"/>
            </call>
        </service>

        <service id="evp_currency.evp_api_sell_currency_converter" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiSellCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_sell_currency_converter"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.commercial"/>
            </call>
        </service>

        <service id="evp_currency_pricing.client.official.cached"
                 class="Evp\Bundle\CurrencyPricingBundle\Client\OfficialClient">
            <factory service="evp_currency_pricing.client.factory" method="createClient"/>
            <argument type="constant">Evp\Bundle\CurrencyPricingBundle\Client\OfficialClient::CLIENT_NAME_OFFICIAL</argument>
            <argument>%evp_currency.currency_client.cache_type%</argument>
            <argument>%evp_currency.currency_client.cache_time%</argument>
        </service>

        <service id="evp_currency_pricing.client.official_by_partner.cached"
                 class="Evp\Bundle\CurrencyPricingBundle\Client\OfficialByPartnerClient">
            <factory service="evp_currency_pricing.client.factory" method="createClient"/>
            <argument type="constant">Evp\Bundle\CurrencyPricingBundle\Client\OfficialByPartnerClient::CLIENT_NAME_OFFICIAL_BY_PARTNER</argument>
            <argument>%evp_currency.currency_client.cache_type%</argument>
            <argument>%evp_currency.currency_client.cache_time%</argument>
        </service>

    </services>
</container>
