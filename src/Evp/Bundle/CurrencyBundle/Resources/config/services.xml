<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services_api_currency.xml"/>
        <import resource="services/currencies.xml"/>
        <import resource="services/services.xml"/>
        <import resource="services/normalizers.xml"/>
    </imports>

    <parameters>
        <parameter key="evp_currency.math_scale" type="constant">Evp\Component\Money\Money::DEFAULT_SCALE</parameter>

        <parameter key="evp_currency.currency_entity">Evp\Bundle\CurrencyBundle\Entity\Currency</parameter>

        <parameter key="evp_currency.admin.currency.groupname">Currency</parameter>
        <parameter key="evp_currency.admin.currency.class">Evp\Bundle\CurrencyBundle\Admin\CurrencyAdmin</parameter>
        <parameter key="evp_currency.admin.currency.controller"/>
        <parameter key="evp_currency.admin.currency.entity">Evp\Bundle\CurrencyBundle\Entity\Currency</parameter>
        <parameter key="evp_currency.admin.currency.translation_domain">EvpCurrencyBundleAdmin</parameter>

        <parameter key="evp_currency.currency_finalization.migration_interval_spec">PT5H</parameter>
    </parameters>

    <services>
        <!-- BcMath -->
        <service id="evp_currency.bc_math" class="Evp\Component\Money\BcMath">
            <argument>%evp_currency.math_scale%</argument>
        </service>

        <service id="evp_currency.currency_manager"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyManager">
            <argument type="service" id="evp_currency.provider.currency"/>
            <argument type="service">
                <service class="DateInterval">
                    <argument>%evp_currency.currency_finalization.migration_interval_spec%</argument>
                </service>
            </argument>
            <argument type="service" id="evp_currency.currency_feature_resolver"/>
            <argument type="service" id="evp_currency.service.currency_configurator_by_client"/>
        </service>

        <service id="evp_currency.service.currency_configurator_by_client"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyConfiguratorByClient">
            <argument id="evp_currency_rest.currency_feature_flag" type="service"/>
        </service>

        <service id="evp_currency.currency_feature_resolver" class="Evp\Bundle\CurrencyBundle\Service\CurrencyFeatureResolver"/>

        <service id="evp_currency.currency_renewer"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyRenewer">
            <argument type="service" id="evp_currency.currency_converter.fixed.renewed" />

            <call method="addFinalizedCurrency">
                <argument>LVL</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2014-01-01 00:00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Riga</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>LTL</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2015-01-01 00:00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Vilnius</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>BYR</argument>
                <argument>BYN</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2016-06-30 12:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Vilnius</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>HRK</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2023-01-01 00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Zagreb</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_currency.currency_renewer.finalization"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyRenewer">
            <argument type="service" id="evp_currency.currency_converter.fixed.renewed" />

            <call method="addFinalizedCurrency">
                <argument>LVL</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2014-01-01 00:00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Riga</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>LTL</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2014-12-31 19:00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Vilnius</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>BYR</argument>
                <argument>BYN</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2016-06-30 12:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Vilnius</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
            <call method="addFinalizedCurrency">
                <argument>HRK</argument>
                <argument>EUR</argument>
                <argument type="service">
                    <service class="DateTime">
                        <argument>2023-01-01 00:00</argument>
                        <argument type="service">
                            <service class="DateTimeZone">
                                <argument>Europe/Zagreb</argument>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
        </service>


        <service id="evp_currency.admin.currency" class="%evp_currency.admin.currency.class%">
            <tag name="sonata.admin" manager_type="orm" group="%evp_currency.admin.currency.groupname%"
                 label="currency" show_in_dashboard="false"/>

            <argument/>
            <argument>%evp_currency.admin.currency.entity%</argument>
            <argument/>

            <call method="setTranslationDomain">
                <argument>%evp_currency.admin.currency.translation_domain%</argument>
            </call>
        </service>

        <!-- Other currency calculations -->
        <service id="evp_currency.currency_calculations"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyCalculations">
            <argument type="service" id="evp_currency.currency_converter.official"/>
            <argument type="string">0.25</argument>

            <call method="addExpensesPercentage">
                <argument>LTL</argument>
                <argument>EUR</argument>
                <argument>0.02027</argument>
            </call>
        </service>

        <service id="evp_currency.currency_calculations.market_value"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyCalculations"
                 parent="evp_currency.currency_calculations">
            <argument index="0" type="service" id="evp_currency.currency_converter.market_value"/>
        </service>

        <!-- Currency converters -->
        <!-- Official converter -->
        <service id="evp_currency.currency_converter.official" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiOfficialCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_official_currency_converter"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.official"/>
            </call>
        </service>

        <!-- Cached currency converter -->
        <service id="evp_currency.currency_converter.official.cached" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiOfficialCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_official_currency_converter_cached"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.official.cached"/>
            </call>
        </service>

        <service id="evp_currency.currency_converter.official_by_partner.cached" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiOfficialByPartnerCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_official_currency_converter_cached"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.official_by_partner.cached"/>
            </call>
        </service>

        <!-- Market converter -->
        <service id="evp_currency.currency_converter.market_value" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiMarketValueCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_market_value_currency_converter"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.market_value"/>
            </call>
        </service>

        <service id="evp_currency.currency_converter.market_value.with_time" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiMarketValueCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_market_value_currency_converter"/>
            <call method="setClient">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyPricingBundle\Client\OfficialClient" parent="evp_currency_pricing.client.market_value">
                        <argument>true</argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_currency.currency_converter.official_by_partner" parent="evp_currency.base_api_converter"
                 class="Evp\Bundle\CurrencyBundle\Service\EvpApiOfficialByPartnerCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.evp_api_official_by_partner_currency_converter"/>
            <call method="setClient">
                <argument type="service" id="evp_currency_pricing.client.official_by_partner"/>
            </call>
        </service>

        <service id="evp_currency.currency_converter.sell.reliable"
                 class="Evp\Bundle\CurrencyBundle\Service\RoundedCurrencyConverter">
            <argument type="service" id="evp_currency.evp_api_sell_currency_converter"/>
            <argument type="constant">Evp\Bundle\CurrencyBundle\Service\RoundedCurrencyConverter::OPERATION_TYPE_FLOOR</argument>
        </service>
        <service id="evp_currency.currency_converter.buy.reliable"
                 class="Evp\Bundle\CurrencyBundle\Service\RoundedCurrencyConverter">
            <argument type="service" id="evp_currency.evp_api_buy_currency_converter"/>
            <argument type="constant">Evp\Bundle\CurrencyBundle\Service\RoundedCurrencyConverter::OPERATION_TYPE_CEIL</argument>
        </service>

        <service id="evp_currency.currency_converter.fixed.renewed"
                 class="Evp\Bundle\CurrencyBundle\Service\FixedRateCurrencyConverter">
            <tag name="monolog.logger" channel="evp_currency.currency_converter.fixed.renewed"/>
            <argument type="service" id="logger"/>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>LTL</argument>
                        <argument>3.4528</argument>
                        <argument>EUR</argument>
                        <argument>1</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>EUR</argument>
                        <argument>1</argument>
                        <argument>LTL</argument>
                        <argument>3.4528</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>BYR</argument>
                        <argument>10000</argument>
                        <argument>BYN</argument>
                        <argument>1</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>BYN</argument>
                        <argument>1</argument>
                        <argument>BYR</argument>
                        <argument>10000</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>LVL</argument>
                        <argument>0.702804</argument>
                        <argument>EUR</argument>
                        <argument>1</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>EUR</argument>
                        <argument>1</argument>
                        <argument>LVL</argument>
                        <argument>0.702804</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>HRK</argument>
                        <argument>1</argument>
                        <argument>EUR</argument>
                        <argument>0.1327228</argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_currency.currency_converter.fixed"
                 class="Evp\Bundle\CurrencyBundle\Service\FixedCurrencyRateConverter" >
            <argument type="service" id="evp_currency.currency_converter.market_value"/>
            <argument type="service" id="logger"/>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>LTL</argument>
                        <argument>1</argument>
                        <argument>EUR</argument>
                        <argument>0.2896</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>EUR</argument>
                        <argument>1</argument>
                        <argument>LTL</argument>
                        <argument>3.4528</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>BYR</argument>
                        <argument>1</argument>
                        <argument>BYN</argument>
                        <argument>0.0001</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>BYN</argument>
                        <argument>1</argument>
                        <argument>BYR</argument>
                        <argument>10000</argument>
                    </service>
                </argument>
            </call>
            <call method="addConversionRate">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\ConversionRate">
                        <argument>HRK</argument>
                        <argument>1</argument>
                        <argument>EUR</argument>
                        <argument>0.1327228</argument>
                    </service>
                </argument>
            </call>
        </service>

        <!-- Custom form types -->
        <service id="evp_currency.form.money_type" class="Evp\Bundle\CurrencyBundle\Form\EvpMoneyType">
            <tag name="form.type" alias="evp_currency_money"/>

            <argument type="service" id="evp_currency.provider.currency"/>
        </service>

        <service id="evp_currency.form.amount_only_money_type" class="Evp\Bundle\CurrencyBundle\Form\AmountOnlyEvpMoneyType">
            <tag name="form.type" alias="evp_amount_only_money"/>

            <argument type="service" id="evp_currency.provider.currency"/>
        </service>

        <!-- Custom validators -->
        <service id="evp_currency.validator.currency_validator"
                 class="Evp\Bundle\CurrencyBundle\Validator\CurrencyValidator">
            <tag name="validator.constraint_validator" alias="evp_currency_validator"/>
            <argument type="service" id="evp_currency.currency_manager"/>
        </service>

        <service id="evp_currency.validator.valid_currency_validator"
                 class="Evp\Bundle\CurrencyBundle\Validator\ValidCurrencyValidator">
            <tag name="validator.constraint_validator" alias="evp_valid_currency_validator"/>
        </service>

        <service id="evp_currency.validator.fraction_validator"
                 class="Evp\Bundle\CurrencyBundle\Validator\FractionValidator">
            <tag name="validator.constraint_validator" alias="evp_fraction_validator"/>
        </service>

        <service id="evp_currency.validator.positive_amount"
                 class="Evp\Bundle\CurrencyBundle\Validator\PositiveAmountValidator">
            <tag name="validator.constraint_validator" alias="evp_positive_amount_validator"/>
        </service>

        <service id="evp_currency.validator.zero_or_positive"
                 class="Evp\Bundle\CurrencyBundle\Validator\ZeroOrPositiveAmountValidator">
            <tag name="validator.constraint_validator" alias="evp_zero_or_positive_amount_validator"/>
        </service>

        <service id="evp_currency.date_conditional_currency_provider"
                 class="Evp\Component\Configuration\DateConditionalValueProvider">
            <argument type="service">
                <service class="DateTime">
                    <argument>2014-12-31 19:00:00</argument>
                </service>
            </argument>
            <argument>LTL</argument>
            <argument>EUR</argument>
        </service>

        <service id="evp_currency.lithuanian_currency" class="stdClass">
            <factory service="evp_currency.date_conditional_currency_provider" method="getValue"/>
        </service>

        <service id="evp_currency.currency_conversion_manager"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyConversionManager" lazy="true">

            <argument type="service" id="evp_currency.currency_converter.sell.reliable" />
            <argument type="service" id="evp_currency.currency_converter.buy.reliable" />
            <argument type="service" id="evp_bank_account.operation_manager" />
            <argument type="service" id="evp_currency.currency_converter.official_by_partner.cached" />
            <argument type="service" id="paysera_airwallex.airwallex_currency_converter" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_currency.currency_iso_number" class="Evp\Component\Money\CurrencyIsoNumber">
        </service>

        <service id="evp_currency.currency_converting_money_calculator"
                 class="Evp\Component\CurrencyConverter\Converter\CurrencyConvertingMoneyCalculator">
            <argument type="service" id="evp_currency.maba.money_calculator" />
            <argument type="service" id="evp_currency.currency_converter_bridge" />
        </service>

        <service id="evp_currency.provider.currency"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyProvider">
            <argument type="service" id="evp_currency.currency_resolver"/>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>LTL</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>EUR</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>USD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>RUB</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>DKK</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>PLN</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>NOK</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>GBP</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>SEK</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>CZK</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>AUD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>CHF</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>JPY</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>BYR</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>CAD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>HUF</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>RON</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>BGN</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>GEL</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>TRY</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>HRK</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>CNY</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>KZT</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>NZD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>HKD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>INR</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>ILS</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>MXN</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>ZAR</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>RSD</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>SGD</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>PHP</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>BYN</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>THB</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>XAU</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_PRECIOUS_METAL</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>ALL</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>EGP</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>DZD</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>

            <call method="addCurrency">
                <argument type="service">
                    <service class="Evp\Bundle\CurrencyBundle\Entity\Currency">
                        <argument>MAD</argument>
                        <argument type="constant">Evp\Bundle\CurrencyBundle\Entity\Currency::TYPE_FIAT</argument>
                        <argument>true</argument>
                        <argument>true</argument>
                        <argument>false</argument>
                        <argument>false</argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_currency.currency_type_helper"
                 class="Evp\Bundle\CurrencyBundle\Service\CurrencyTypeHelper">
            <argument id="evp_currency.currency_manager" type="service"/>
        </service>

        <service id="evp_currency.conversion_rate_storage"
                 class="Evp\Bundle\CurrencyBundle\Service\ConversionRateStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument>3600</argument>
        </service>

        <service id="evp_currency.official_currency_conversion_rate_provider" class="Evp\Bundle\CurrencyBundle\Service\OfficialCurrencyConversionRateProvider">
            <argument type="service" id="evp_currency_pricing.client.official"/>
            <argument type="service" id="evp_currency.conversion_rate_storage"/>
        </service>
    </services>
</container>
