<?php

namespace Evp\Bundle\CurrencyBundle\Service;

use DateTime;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\CurrencyBundle\Entity\ConversionRate;
use Evp\Component\Money\Money;
use Evp\Bundle\CurrencyPricingBundle\Client\ClientInterface;

class EvpApiSellCurrencyConverter extends BaseApiCurrencyConverter
{
    protected ClientInterface $client;

    public function setClient(ClientInterface $client): self
    {
        $this->client = $client;

        return $this;
    }

    protected function fetchConversion(
        Money $amount,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $partnerCode = null
    ): array
    {
        $covenanteeId = $client ? $client->getCovenanteeId() : null;

        return $this->client->calculateResult(
            $amount,
            $toCurrency,
            $covenanteeId,
            $clientType,
            $date,
            $partnerCode
        );
    }

    protected function fetchRate(
        string $fromCurrency,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $conversionAmount = null,
        ?string $conversionCurrency = null,
        ?string $partnerCode = null
    ): ConversionRate {
        $covenanteeId = $client ? $client->getCovenanteeId() : null;
        $result = $this->client->getExchangeRates($fromCurrency, [$toCurrency], $covenanteeId, $clientType, $date, null, null, $partnerCode);

        return new ConversionRate($fromCurrency, $result['rates'][$toCurrency]['sell'], $toCurrency, 1);
    }
}
