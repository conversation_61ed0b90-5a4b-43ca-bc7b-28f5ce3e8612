<?php

declare(strict_types=1);

namespace Evp\Bundle\CurrencyBundle\Service;

use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\PartnerClientManager;
use Evp\Bundle\CurrencyBundle\Entity\ConversionRate;
use Evp\Component\Money\Money;
use Evp\Bundle\CurrencyBundle\Exception\CurrencyNotFoundException;
use Evp\Bundle\CurrencyBundle\Exception\CurrencyConverterException;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\CurrencyPricingBundle\Exception\CurrencyPricingException;
use Psr\Log\LoggerInterface;
use DateTime;

abstract class BaseApiCurrencyConverter implements CurrencyConverterInterface
{
    protected PartnerClientManager $partnerClientManager;
    protected LoggerInterface $logger;
    private int $maxAttempts;

    public function __construct(
        PartnerClientManager $partnerClientManager,
        LoggerInterface $logger,
        int $maxAttempts
    ) {
        $this->partnerClientManager = $partnerClientManager;
        $this->logger = $logger;
        $this->maxAttempts = $maxAttempts;
    }

    /**
     * Convert money to another currency
     *
     * @param \Evp\Component\Money\Money     $amount
     * @param string                                      $toCurrency
     * @param DateTime|null                              $date
     * @param \Evp\Bundle\ClientBundle\Entity\Client|null $client
     *
     * @throws \Evp\Bundle\CurrencyBundle\Exception\CurrencyConverterException
     * @throws \Evp\Bundle\CurrencyBundle\Exception\CurrencyNotFoundException
     *
     * @return \Evp\Component\Money\Money
     */
    public function convert(Money $amount, $toCurrency, DateTime $date = null, Client $client = null)
    {
        if ($amount->getCurrency() === $toCurrency) {
            $this->logger->debug('Skipping conversion, same currency', [$amount, $toCurrency]);
            return $amount;
        } elseif ($amount->isZero()) {
            $this->logger->debug('Skipping conversion, zero amount', [$amount, $toCurrency]);
            return Money::createZero($toCurrency);
        }
        $clientType = $client instanceof ClientNatural || $client === null ? Client::TYPE_NATURAL : Client::TYPE_LEGAL;
        $partnerCode = $client ? $this->partnerClientManager->getPartnerCode($client) : null;
        $attempt = 1;

        do {
            try {
                $result = $this->fetchConversion($amount, $toCurrency, $client, $clientType, $date, $partnerCode);

                $this->logger->info(
                    'Got conversion result',
                    [
                        'amount' => $amount,
                        'toCurrency' => $toCurrency,
                        'result' => $result,
                    ]
                );

                return new Money($result['amount'], $result['currency']);
            } catch (CurrencyPricingException $exception) {
                if ($exception->getCode() == 404) {
                    throw new CurrencyNotFoundException($exception->getMessage(), $exception->getCode(), $exception);
                }

                if ($attempt < $this->maxAttempts) {
                    sleep(pow(3, $attempt));
                    $attempt++;
                    continue;
                }

                throw new CurrencyConverterException($exception->getMessage(), $exception->getCode(), $exception);
            }
        } while ($attempt <= $this->maxAttempts);
    }

    /**
     * Get rates for provided currencies
     *
     * @param string                                      $fromCurrency
     * @param string                                      $toCurrency
     * @param DateTime                                   $date optional
     * @param \Evp\Bundle\ClientBundle\Entity\Client|null $client
     * @param string|null $conversionAmount
     * @param string|null $conversionCurrency
     *
     * @throws \Evp\Bundle\CurrencyBundle\Exception\CurrencyConverterException
     * @throws \Evp\Bundle\CurrencyBundle\Exception\CurrencyNotFoundException
     *
     * @return \Evp\Bundle\CurrencyBundle\Entity\ConversionRate
     */
    public function getConversionRate(
        $fromCurrency,
        $toCurrency,
        DateTime $date = null,
        Client $client = null,
        $conversionAmount = null,
        $conversionCurrency = null
    ) {
        $clientType = $client instanceof ClientNatural || $client === null ? Client::TYPE_NATURAL : Client::TYPE_LEGAL;
        $partnerCode = $client ? $this->partnerClientManager->getPartnerCode($client) : null;

        try {
            return $this->fetchRate(
                $fromCurrency,
                $toCurrency,
                $client,
                $clientType,
                $date,
                $conversionAmount,
                $conversionCurrency,
                $partnerCode
            );
        } catch (CurrencyPricingException $exception) {
            if ($exception->getCode() == 404) {
                throw new CurrencyNotFoundException($exception->getMessage(), $exception->getCode(), $exception);
            }
            throw new CurrencyConverterException($exception->getMessage(), $exception->getCode(), $exception);
        }
    }

    abstract protected function fetchConversion(
        Money $amount,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $partnerCode = null
    ): array;

    abstract protected function fetchRate(
        string $fromCurrency,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $conversionAmount = null,
        ?string $conversionCurrency = null,
        ?string $partnerCode = null
    ): ConversionRate;
}
