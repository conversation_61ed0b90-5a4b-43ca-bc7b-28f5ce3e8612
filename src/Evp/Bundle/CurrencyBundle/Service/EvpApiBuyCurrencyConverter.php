<?php

namespace Evp\Bundle\CurrencyBundle\Service;

use DateTime;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\CurrencyBundle\Entity\ConversionRate;
use Evp\Component\Money\Money;
use Evp\Bundle\CurrencyPricingBundle\Client\ClientInterface;

class EvpApiBuyCurrencyConverter extends BaseApiCurrencyConverter
{
    protected ClientInterface $client;

    public function setClient(ClientInterface $client): self
    {
        $this->client = $client;

        return $this;
    }

    protected function fetchConversion(
        Money $amount,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date = null,
        ?string $partnerCode = null
    ): array {
        $covenanteeId = $client ? $client->getCovenanteeId() : null;

        return $this->client->calculateNeededAmount($toCurrency, $amount, $covenanteeId, $clientType, $date, $partnerCode);
    }

    /**
     * @param string $fromCurrency
     * @param string $toCurrency
     * @param Client|null $client
     * @param $clientType
     * @param \DateTime|null $date
     * @param string|null $conversionAmount
     * @param string|null $conversionCurrency
     *
     * @return ConversionRate
     */
    protected function fetchRate(
        string $fromCurrency,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $conversionAmount = null,
        ?string $conversionCurrency = null,
        ?string $partnerCode = null
    ): ConversionRate {
        $covenanteeId = $client ? $client->getCovenanteeId() : null;
        $result = $this->client->getExchangeRates(
            $fromCurrency,
            [$toCurrency],
            $covenanteeId,
            $clientType,
            $date,
            $conversionAmount,
            $conversionCurrency,
            $partnerCode
        );

        return new ConversionRate($fromCurrency, $result['rates'][$toCurrency]['buy'], $toCurrency, 1);
    }
}
