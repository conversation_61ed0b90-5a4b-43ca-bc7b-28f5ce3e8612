<?php
declare(strict_types=1);

namespace Evp\Bundle\CurrencyBundle\Service;

use DateTime;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\CurrencyBundle\Entity\ConversionRate;
use Evp\Bundle\CurrencyBundle\Exception\CurrencyConverterException;
use Evp\Component\Money\Money;
use Evp\Bundle\CurrencyPricingBundle\Client\ClientInterface;

class EvpApiOfficialByPartnerCurrencyConverter extends BaseApiCurrencyConverter
{
    protected ClientInterface $client;

    public function setClient(ClientInterface $client): self
    {
        $this->client = $client;

        return $this;
    }

    protected function fetchConversion(
        Money $amount,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $partnerCode = null
    ): array {
        if ($client === null) {
            throw new CurrencyConverterException('Client can not be empty for official by partner currency converter!');
        }

        return $this->client->calculateResult($amount, $toCurrency, $client, $clientType, $date, $partnerCode);
    }

    protected function fetchRate(
        string $fromCurrency,
        string $toCurrency,
        ?Client $client,
        ?string $clientType,
        ?DateTime $date,
        ?string $conversionAmount = null,
        ?string $conversionCurrency = null,
        ?string $partnerCode = null
    ): ConversionRate {
        if ($client === null) {
            throw new CurrencyConverterException('Client can not be empty for official by partner currency converter!');
        }
        $result = $this->client->getExchangeRates($fromCurrency, [$toCurrency], $client, $clientType, $date, $conversionAmount, $conversionCurrency, $partnerCode);

        return new ConversionRate($fromCurrency, $result['rates'][$toCurrency], $toCurrency, 1);
    }
}
