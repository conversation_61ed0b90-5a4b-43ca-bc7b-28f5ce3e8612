<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class IpidNodeSelector
{
    public const US_ANALYTICS_NODE = 'us-node-01';
    public const US_LOGIC_NODE = 'us-node-02';
    public const US_DIRECT_FI_NODE = 'us-node-03';

    public const CN_INDIVIDUAL_NODE = 'cn-node-01';
    public const CN_BUSINESS_NODE = 'cn-node-02';

    public const IBAN_LOGIC_NODE = 'iban-node-01';

    public function getNodeIdForCountry(string $country, ?string $identityType = null): string
    {
        $country = strtoupper($country);

        if ($country === 'CN') {
            if ($identityType === 'business' || $identityType === 'corporate') {
                return self::CN_BUSINESS_NODE;
            }
            return self::CN_INDIVIDUAL_NODE;
        }

        if ($country === 'US') {
            return self::US_LOGIC_NODE;
        }

        return self::IBAN_LOGIC_NODE;
    }
}
