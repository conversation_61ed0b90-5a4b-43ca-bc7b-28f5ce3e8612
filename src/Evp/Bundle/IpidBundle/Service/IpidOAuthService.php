<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\IpidBundle\DTO\IpidOAuthRegistrationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidOAuthTokenResponse;
use Evp\Bundle\IpidBundle\Exception\IpidOAuthException;
use Psr\Log\LoggerInterface;

class IpidOAuthService
{
    private IpidApiClient $apiClient;
    private LoggerInterface $logger;
    private string $clientId;
    private string $clientSecret;

    public function __construct(
        IpidApiClient $apiClient,
        LoggerInterface $logger,
        string $clientId,
        string $clientSecret
    ) {
        $this->apiClient = $apiClient;
        $this->logger = $logger;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
    }

    public function registerClient(IpidOAuthRegistrationRequest $request): IpidOAuthTokenResponse
    {
        $this->logger->info('Registering OAuth client with iPiD API', [
            'client_id' => $this->clientId,
            'scopes' => $request->getScopes(),
        ]);

        try {
            $responseData = $this->apiClient->makeRequest('POST', '/oauth/api/v1/register', [
                'json' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'client_credentials',
                    'scope' => implode(' ', $request->getScopes()),
                    'application_name' => $request->getApplicationName(),
                    'redirect_uri' => $request->getRedirectUri(),
                    'description' => $request->getDescription(),
                ],
            ], false);

            $this->logger->info('OAuth client registered successfully', [
                'access_token_length' => strlen($responseData['access_token'] ?? ''),
                'expires_in' => $responseData['expires_in'] ?? null,
            ]);

            return IpidOAuthTokenResponse::fromArray($responseData);

        } catch (\Exception $e) {
            $this->logger->error('OAuth registration failed', [
                'error' => $e->getMessage(),
                'client_id' => $this->clientId,
            ]);

            throw new IpidOAuthException(
                sprintf('OAuth registration failed: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }

    public function refreshToken(string $refreshToken): IpidOAuthTokenResponse
    {
        try {
            $responseData = $this->apiClient->makeRequest('POST', '/oauth/api/v1/token', [
                'json' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                ],
            ], false);

            return IpidOAuthTokenResponse::fromArray($responseData);

        } catch (\Exception $e) {
            throw new IpidOAuthException(
                sprintf('Token refresh failed: %s', $e->getMessage()),
                0,
                $e
            );
        }
    }
}
