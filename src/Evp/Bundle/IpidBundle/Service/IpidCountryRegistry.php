<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class IpidCountryRegistry
{
    private array $enabledCountries;
    private array $enabledRegions;
    private array $regionMappings;

    public function __construct(array $enabledCountries = [], array $enabledRegions = [])
    {
        $this->enabledCountries = array_map('strtoupper', $enabledCountries);
        $this->enabledRegions = array_map('strtoupper', $enabledRegions);
        $this->initializeRegionMappings();
    }

    public function isCountryEnabled(string $countryCode): bool
    {
        $countryCode = strtoupper($countryCode);

        if (in_array($countryCode, $this->enabledCountries, true)) {
            return true;
        }

        foreach ($this->enabledRegions as $region) {
            if ($this->isCountryInRegion($countryCode, $region)) {
                return true;
            }
        }

        return false;
    }

    public function isRegionEnabled(string $region): bool
    {
        return in_array(strtoupper($region), $this->enabledRegions, true);
    }

    public function getEnabledCountries(): array
    {
        $countries = $this->enabledCountries;

        foreach ($this->enabledRegions as $region) {
            $regionCountries = $this->getCountriesInRegion($region);
            $countries = array_merge($countries, $regionCountries);
        }

        return array_unique($countries);
    }

    public function getEnabledRegions(): array
    {
        return $this->enabledRegions;
    }

    public function isCountryInRegion(string $countryCode, string $region): bool
    {
        $countryCode = strtoupper($countryCode);
        $region = strtoupper($region);

        if (!isset($this->regionMappings[$region])) {
            return false;
        }

        return in_array($countryCode, $this->regionMappings[$region], true);
    }

    public function getCountriesInRegion(string $region): array
    {
        $region = strtoupper($region);

        return $this->regionMappings[$region] ?? [];
    }

    public function getSupportedRegions(): array
    {
        return array_keys($this->regionMappings);
    }

    public function getRegionForCountry(string $countryCode): ?string
    {
        $countryCode = strtoupper($countryCode);

        foreach ($this->regionMappings as $region => $countries) {
            if (in_array($countryCode, $countries, true)) {
                return $region;
            }
        }

        return null;
    }

    public function getValidationSchemeForCountry(string $countryCode): string
    {
        $countryCode = strtoupper($countryCode);

        if ($this->isCountryInRegion($countryCode, 'EU')) {
            return 'VOP';
        }

        if ($countryCode === 'GB' || $countryCode === 'UK') {
            return 'COP';
        }

        return 'GLOBAL';
    }

    private function initializeRegionMappings(): void
    {
        $this->regionMappings = [
            'EU' => [
                'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
                'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL',
                'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE',
                'IS', 'LI', 'NO', 'CH', 'SM', 'VA', 'MC', 'AD'
            ],
            'NORTH_AMERICA' => ['US', 'CA', 'MX'],
            'SOUTH_AMERICA' => ['BR', 'AR', 'CL', 'CO', 'PE', 'UY', 'EC'],
            'ASIA_PACIFIC' => [
                'CN', 'IN', 'ID', 'VN', 'MY', 'KR', 'BD', 'NP', 'PK'
            ],
            'AFRICA' => ['NG', 'UG', 'ZA'],
            'MIDDLE_EAST' => ['SA', 'TR'],
            'UK' => ['GB', 'UK'],
            'IBAN_COUNTRIES' => [
                'AL', 'AD', 'AT', 'AZ', 'BH', 'BE', 'BA', 'BR', 'BG', 'BI',
                'CR', 'HR', 'CY', 'CZ', 'DK', 'DJ', 'DO', 'EG', 'SV', 'EE',
                'FO', 'FI', 'FR', 'GE', 'DE', 'GB', 'GI', 'GR', 'GL', 'GT',
                'HU', 'IS', 'IQ', 'IE', 'IL', 'IT', 'JO', 'KZ', 'XK', 'KW',
                'LV', 'LB', 'LY', 'LI', 'LT', 'LU', 'MK', 'MT', 'MR', 'MU',
                'MD', 'MC', 'ME', 'NL', 'NO', 'PK', 'PS', 'PL', 'PT', 'QA',
                'BY', 'RO', 'RU', 'LC', 'SM', 'ST', 'SA', 'RS', 'SC', 'SK',
                'SI', 'SO', 'ES', 'SD', 'SE', 'CH', 'TL', 'TN', 'TR', 'UA',
                'AE', 'VA', 'VG'
            ]
        ];
    }
} 