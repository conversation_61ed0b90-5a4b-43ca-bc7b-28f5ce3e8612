<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Cache;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Psr\Log\LoggerInterface;

class IpidCacheManager
{
    private IpidApiResponseRepository $apiResponseRepository;
    private LoggerInterface $logger;

    public function __construct(
        IpidApiResponseRepository $apiResponseRepository,
        LoggerInterface $logger
    ) {
        $this->apiResponseRepository = $apiResponseRepository;
        $this->logger = $logger;
    }

    public function calculateRequestHash(IpidValidationRequest $request, TransferOut $transfer): string
    {
        // Hash based on the actual request content that matters for caching
        $data = [
            'transfer_id' => $transfer->getId(),
            'encrypted_payload' => $request->getEncryptedPayload(),
            'node_id' => $request->getNodeId(),
        ];

        // Remove null values to ensure consistent hashing
        $data = array_filter($data, fn($value) => $value !== null);
        ksort($data);

        $hashInput = json_encode($data, JSON_THROW_ON_ERROR);
        $hash = hash('sha256', $hashInput);

        $this->logger->debug('iPiD request hash calculated', [
            'transfer_id' => $transfer->getId(),
            'hash' => $hash,
            'hash_input_length' => strlen($hashInput),
            'included_fields' => array_keys($data)
        ]);

        return $hash;
    }

    public function findValidCachedResponse(string $requestHash, string $country): ?IpidApiResponse
    {
        $response = $this->apiResponseRepository->findValidByRequestHashAndCountry($requestHash, $country);

        return ($response && !$response->isExpired()) ? $response : null;
    }
}
