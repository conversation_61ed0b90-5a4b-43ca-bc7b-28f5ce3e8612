<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Cache;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;

class IpidCacheManager
{
    private IpidApiResponseRepository $apiResponseRepository;

    public function __construct(IpidApiResponseRepository $apiResponseRepository)
    {
        $this->apiResponseRepository = $apiResponseRepository;
    }

    public function calculateBeneficiaryHash(TransferOut $transfer): string
    {
        $beneficiary = $transfer->getBeneficiary();

        $data = [
            'name' => $this->getBeneficiaryName($beneficiary),
            'account' => $this->getBeneficiaryAccount($beneficiary),
            'bic' => $this->getBeneficiaryBic($beneficiary),
            'sort_code' => $this->getBeneficiarySortCode($beneficiary),
            'person_code' => $this->getPersonCode($beneficiary),
        ];

        // Remove null values and sort for consistent hashing
        $data = array_filter($data, fn($value) => $value !== null);
        ksort($data);

        return hash('sha256', json_encode($data, JSON_THROW_ON_ERROR));
    }

    public function findValidCachedResponse(string $beneficiaryHash, string $country): ?IpidApiResponse
    {
        return $this->apiResponseRepository->findValidByRequestHashAndCountry($beneficiaryHash, $country);
    }

    private function getBeneficiaryName($beneficiary): ?string
    {
        if (!$beneficiary) {
            return null;
        }

        if (method_exists($beneficiary, 'getName')) {
            return $beneficiary->getName();
        }

        if (method_exists($beneficiary, 'getDisplayName')) {
            return $beneficiary->getDisplayName();
        }

        return null;
    }

    private function getBeneficiaryAccount($beneficiary): ?string
    {
        if ($beneficiary instanceof PartyIban) {
            return $beneficiary->getIban();
        }

        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getDisplayAccount();
        }

        return null;
    }

    private function getBeneficiaryBic($beneficiary): ?string
    {
        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getBic();
        }

        return null;
    }

    private function getBeneficiarySortCode($beneficiary): ?string
    {
        if ($beneficiary instanceof PartyBank) {
            return $beneficiary->getBankCode();
        }

        return null;
    }

    private function getPersonCode($beneficiary): ?string
    {
        if (method_exists($beneficiary, 'getCode')) {
            $code = $beneficiary->getCode();
            if ($code) {
                return $code;
            }
        }

        if ($beneficiary instanceof PartyBank) {
            $innCode = $beneficiary->getInnCode();
            if ($innCode) {
                return $innCode;
            }

            $kppCode = $beneficiary->getKppCode();
            if ($kppCode) {
                return $kppCode;
            }
        }

        return null;
    }
}
