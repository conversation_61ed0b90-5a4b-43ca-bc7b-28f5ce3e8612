<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Cache;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;

class IpidCacheManager
{
    private IpidApiResponseRepository $apiResponseRepository;

    public function __construct(IpidApiResponseRepository $apiResponseRepository)
    {
        $this->apiResponseRepository = $apiResponseRepository;
    }

    public function calculateRequestHash(IpidValidationRequest $request): string
    {
        $data = [
            'encrypted_payload' => $request->getEncryptedPayload(),
            'node_id' => $request->getNodeId(),
        ];

        $data = array_filter($data, fn($value) => $value !== null);
        ksort($data);

        return hash('sha256', json_encode($data, JSON_THROW_ON_ERROR));
    }

    public function findValidCachedResponse(string $requestHash, string $country): ?IpidApiResponse
    {
        return $this->apiResponseRepository->findValidByRequestHashAndCountry($requestHash, $country);
    }
}
