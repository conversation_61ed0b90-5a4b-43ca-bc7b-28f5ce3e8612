<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Cache;

use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;

class IpidCacheManager
{
    private IpidApiResponseRepository $apiResponseRepository;

    public function __construct(IpidApiResponseRepository $apiResponseRepository)
    {
        $this->apiResponseRepository = $apiResponseRepository;
    }

    public function findValidCachedResponse(int $transferId, string $country): ?IpidApiResponse
    {
        return $this->apiResponseRepository->findValidByTransferIdAndCountry($transferId, $country);
    }
}
