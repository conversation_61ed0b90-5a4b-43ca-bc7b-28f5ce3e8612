<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Cache;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Psr\Log\LoggerInterface;

class IpidCacheManager
{
    private IpidApiResponseRepository $apiResponseRepository;
    private LoggerInterface $logger;

    public function __construct(
        IpidApiResponseRepository $apiResponseRepository,
        LoggerInterface $logger
    ) {
        $this->apiResponseRepository = $apiResponseRepository;
        $this->logger = $logger;
    }

    public function calculateRequestHash(IpidValidationRequest $request, TransferOut $transfer): string
    {
        $data = [
            'transfer_id' => $transfer->getId(),
            'encrypted_payload' => $request->getEncryptedPayload(),
            'node_id' => $request->getNodeId(),
            'public_key' => $request->getPublicKey(),
            'clearing_system_id' => $request->getClearingSystemId(),
            'bic' => $request->getBic(),
        ];

        ksort($data);

        $hashInput = json_encode($data, JSON_THROW_ON_ERROR);
        $hash = hash('sha256', $hashInput);

        if (substr_count($hash, '0') > 20 || substr_count($hash, 'f') > 20) {
            $this->logger->warning('Potentially weak hash generated for iPiD request', [
                'transfer_id' => $transfer->getId(),
                'hash' => $hash,
                'hash_input_length' => strlen($hashInput)
            ]);
        }

        return $hash;
    }

    public function findValidCachedResponse(string $requestHash, string $country): ?IpidApiResponse
    {
        $response = $this->apiResponseRepository->findValidByRequestHashAndCountry($requestHash, $country);

        return ($response && !$response->isExpired()) ? $response : null;
    }
}
