<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use Psr\Log\LoggerInterface;

class IpidValidationService
{
    private IpidTransferValidator $transferValidator;
    private IpidRequestBuilder $requestBuilder;
    private IpidCacheManager $cacheManager;
    private IpidApiClient $apiClient;
    private IpidResponseProcessor $responseProcessor;
    private IpidCountryRegistry $countryRegistry;
    private LoggerInterface $logger;

    public function __construct(
        IpidTransferValidator $transferValidator,
        IpidRequestBuilder $requestBuilder,
        IpidCacheManager $cacheManager,
        IpidApiClient $apiClient,
        IpidResponseProcessor $responseProcessor,
        IpidCountryRegistry $countryRegistry,
        LoggerInterface $logger
    ) {
        $this->transferValidator = $transferValidator;
        $this->requestBuilder = $requestBuilder;
        $this->cacheManager = $cacheManager;
        $this->apiClient = $apiClient;
        $this->responseProcessor = $responseProcessor;
        $this->countryRegistry = $countryRegistry;
        $this->logger = $logger;
    }

    public function validateTransfer(TransferOut $transfer): IpidValidationResult
    {
        $country = null;
        try {
            $country = $this->extractCountryFromTransfer($transfer);

            if (!$this->transferValidator->isCountrySupported($country)) {
                $this->logger->debug('iPiD validation skipped - country not supported', [
                    'transfer_id' => $transfer->getId(),
                    'country' => $country
                ]);
                return $this->createUnsupportedCountryConsent($transfer, $country);
            }

            $this->logger->info('iPiD validation initiated for supported country', [
                'transfer_id' => $transfer->getId(),
                'country' => $country,
                'validation_scheme' => $this->countryRegistry->getValidationSchemeForCountry($country),
                'region' => $this->countryRegistry->getRegionForCountry($country)
            ]);

            $existingConsent = $this->transferValidator->findExistingConsent($transfer);
            if ($existingConsent) {
                $this->logger->info('iPiD validation already completed for transfer', [
                    'transfer_id' => $transfer->getId(),
                    'consent_id' => $existingConsent->getId(),
                    'consent_status' => $existingConsent->getConsentStatus()
                ]);
                return IpidValidationResult::createAlreadyValidated($existingConsent);
            }

            $request = $this->requestBuilder->buildRequest($transfer);
            $requestHash = $this->cacheManager->calculateRequestHash($request);

            // Check cache using request hash
            $cachedResponse = $this->cacheManager->findValidCachedResponse($requestHash, $country);
            if ($cachedResponse) {
                $this->logger->info('Using cached iPiD response', [
                    'transfer_id' => $transfer->getId(),
                    'request_hash' => $requestHash,
                    'cached_response_age' => $cachedResponse->getCreatedAt()->diff(new \DateTime())->days . ' days'
                ]);
                return $this->responseProcessor->processValidationResult($transfer, $cachedResponse);
            }

            // Cache miss - call API
            $response = $this->apiClient->sendValidationRequest($request);

            $ipidApiResponse = $this->responseProcessor->saveApiResponse($response, $requestHash, $country);

            return $this->responseProcessor->processValidationResult($transfer, $ipidApiResponse);

        } catch (\Exception $e) {
            $this->logger->error('iPiD validation error - creating automatic consent', [
                'transfer_id' => $transfer->getId(),
                'error_class' => get_class($e),
                'error_message' => $e->getMessage()
            ]);
            return $this->createErrorConsent($transfer, $country, $e->getMessage());
        }
    }

    private function createUnsupportedCountryConsent(TransferOut $transfer, string $country): IpidValidationResult
    {
        $this->logger->info('Creating automatic consent for unsupported country', [
            'transfer_id' => $transfer->getId(),
            'country' => $country
        ]);

        $consent = $this->responseProcessor->createAutomaticConsent(
            $transfer,
            'unsupported_country',
            'Country not configured for iPiD validation'
        );

        return IpidValidationResult::createUnsupportedCountry($country, $consent);
    }

    private function createErrorConsent(TransferOut $transfer, string $country, string $errorMessage): IpidValidationResult
    {
        $this->logger->info('Creating automatic consent due to validation error', [
            'transfer_id' => $transfer->getId(),
            'country' => $country,
            'error_message' => $errorMessage
        ]);

        $consent = $this->responseProcessor->createAutomaticConsent(
            $transfer,
            'validation_error',
            $errorMessage
        );

        return IpidValidationResult::createError($errorMessage, $consent);
    }

    private function extractCountryFromTransfer(TransferOut $transfer): string
    {
        $beneficiary = $transfer->getBeneficiary();

        if ($beneficiary instanceof PartyIban) {
            $iban = $beneficiary->getIban();
            if ($iban && strlen($iban) >= 2) {
                return strtoupper(substr($iban, 0, 2));
            }
        }

        if ($beneficiary instanceof PartyBank) {
            $bic = $beneficiary->getBic();
            if ($bic && strlen($bic) >= 6) {
                $countryFromBic = strtoupper(substr($bic, 4, 2));
                if (preg_match('/^[A-Z]{2}$/', $countryFromBic)) {
                    return $countryFromBic;
                }
            }
        }

        throw new \InvalidArgumentException('Cannot determine country from transfer beneficiary data.');
    }
}
