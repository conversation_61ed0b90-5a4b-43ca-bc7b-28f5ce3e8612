<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class IpidCorridorSpecification
{
    private const CORRIDOR_SPECS = [
        'US' => [
            'creditor' => ['given_name' => true, 'surname' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'CN' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'BD' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
        'BR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'MX' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'AR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
        'BE' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['iban' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'iban',
        ],
        'IT' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['iban' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'iban',
        ],
        'UY' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
        'PE' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'MY' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['clearing_system_id' => false],
            'account_type' => 'account_id',
        ],
        'TR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true, 'iban' => false],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'flexible',
        ],
        'KR' => [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'account_id',
        ],
    ];

    public function getRequiredFields(string $countryCode): array
    {
        $countryCode = strtoupper($countryCode);

        if (!isset(self::CORRIDOR_SPECS[$countryCode])) {
            return [
                'creditor' => ['name' => true],
                'creditor_identification' => ['registration_id' => false],
                'creditor_account' => ['account_id' => true],
                'creditor_agent' => ['bic' => false],
                'account_type' => 'flexible',
            ];
        }

        return self::CORRIDOR_SPECS[$countryCode];
    }

    public function shouldSplitName(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return isset($spec['creditor']['given_name']) && $spec['creditor']['given_name'];
    }

    public function getPreferredAccountType(string $countryCode): string
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['account_type'] ?? 'flexible';
    }

    public function isRegistrationIdRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_identification']['registration_id'] ?? false;
    }

    public function isBicRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_agent']['bic'] ?? false;
    }

    public function isClearingSystemIdRequired(string $countryCode): bool
    {
        $spec = $this->getRequiredFields($countryCode);

        return $spec['creditor_agent']['clearing_system_id'] ?? false;
    }

    public function getIdentificationTypeForCountry(string $countryCode): string
    {
        $typeMapping = [
            'CN' => 'REGISTRATION_ID',
            'US' => 'TAX_ID',
            'BR' => 'REGISTRATION_ID',
            'BE' => 'REGISTRATION_ID',
            'IT' => 'REGISTRATION_ID',
            'MX' => 'REGISTRATION_ID',
            'AR' => 'REGISTRATION_ID',
        ];

        return $typeMapping[strtoupper($countryCode)] ?? 'REGISTRATION_ID';
    }

    public function isCountrySupported(string $countryCode): bool
    {
        $countryCode = strtoupper($countryCode);

        $supportedCountries = [
            'US', 'IN', 'ID', 'VN', 'NG', 'NP', 'PK', 'CN', 'UK', 'GB', 'KR', 'BD', 'UG',
            'BR', 'MX', 'AR', 'BE', 'IT', 'UY', 'PE', 'MY', 'TR', 'ZA', 'FR', 'NL', 'PL',
            'CL', 'CO', 'EC', 'SA'
        ];

        return in_array($countryCode, $supportedCountries, true);
    }
}
