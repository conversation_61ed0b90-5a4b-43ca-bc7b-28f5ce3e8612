<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TimeoutExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class IpidApiClient
{
    public const DEFAULT_TIMEOUT = 30;
    public const DEFAULT_RETRY_ATTEMPTS = 3;

    public const ERROR_CODE_INVALID_REQUEST = 'IPID_001';
    public const ERROR_CODE_AUTHENTICATION_FAILED = 'IPID_002';
    public const ERROR_CODE_RATE_LIMIT_EXCEEDED = 'IPID_003';
    public const ERROR_CODE_SERVICE_UNAVAILABLE = 'IPID_004';
    public const ERROR_CODE_TIMEOUT = 'IPID_005';

    private HttpClientInterface $httpClient;
    private IpidResponseDenormalizer $responseDenormalizer;
    private string $endpoint;
    private string $apiKey;
    private string $customerId;
    private LoggerInterface $logger;
    private int $timeout;
    private int $retryAttempts;

    public function __construct(
        HttpClientInterface $httpClient,
        IpidResponseDenormalizer $responseDenormalizer,
        string $endpoint,
        string $apiKey,
        string $customerId,
        LoggerInterface $logger,
        int $timeout = self::DEFAULT_TIMEOUT,
        int $retryAttempts = self::DEFAULT_RETRY_ATTEMPTS
    ) {
        $this->httpClient = $httpClient;
        $this->responseDenormalizer = $responseDenormalizer;
        $this->endpoint = $endpoint;
        $this->apiKey = $apiKey;
        $this->customerId = $customerId;
        $this->logger = $logger;
        $this->timeout = $timeout;
        $this->retryAttempts = $retryAttempts;
    }

    public function makeRequest(string $method, string $path, array $options = [], bool $useDefaultHeaders = true): array
    {
        $attempt = 1;
        $lastException = null;
        $fullUrl = $this->endpoint . $path;

        $defaultOptions = [
            'timeout' => $this->timeout,
        ];

        if ($useDefaultHeaders) {
            $defaultOptions['headers'] = $this->buildDefaultHeaders();
        }

        $options = array_merge_recursive($defaultOptions, $options);

        while ($attempt <= $this->retryAttempts) {
            try {
                $this->logger->info('iPiD API request', [
                    'method' => $method,
                    'path' => $path,
                    'attempt' => $attempt,
                ]);

                $response = $this->httpClient->request($method, $fullUrl, $options);

                $statusCode = $response->getStatusCode();

                if ($statusCode >= 200 && $statusCode < 300) {
                    $responseData = $response->toArray();

                    $this->logger->info('iPiD API response successful', [
                        'method' => $method,
                        'path' => $path,
                        'status_code' => $statusCode,
                    ]);

                    return $responseData;
                }

                throw new IpidApiException(
                    "iPiD API returned non-success status: $statusCode",
                    self::ERROR_CODE_SERVICE_UNAVAILABLE
                );

            } catch (TimeoutExceptionInterface $e) {
                $lastException = new IpidApiException(
                    'iPiD API request timeout',
                    self::ERROR_CODE_TIMEOUT,
                    $e
                );
            } catch (ClientExceptionInterface $e) {
                $statusCode = $e->getResponse()->getStatusCode();

                if ($statusCode === 401) {
                    throw new IpidApiException(
                        'iPiD API authentication failed',
                        self::ERROR_CODE_AUTHENTICATION_FAILED,
                        $e
                    );
                }

                if ($statusCode === 429) {
                    $lastException = new IpidApiException(
                        'iPiD API rate limit exceeded',
                        self::ERROR_CODE_RATE_LIMIT_EXCEEDED,
                        $e
                    );
                } else {
                    throw new IpidApiException(
                        'iPiD API client error: ' . $statusCode,
                        self::ERROR_CODE_INVALID_REQUEST,
                        $e
                    );
                }
            } catch (ServerExceptionInterface $e) {
                $lastException = new IpidApiException(
                    'iPiD API server error',
                    self::ERROR_CODE_SERVICE_UNAVAILABLE,
                    $e
                );
            } catch (TransportExceptionInterface $e) {
                $lastException = new IpidApiException(
                    'iPiD API transport error',
                    self::ERROR_CODE_SERVICE_UNAVAILABLE,
                    $e
                );
            }

            if ($attempt < $this->retryAttempts && $this->shouldRetry($lastException)) {
                $this->logger->warning('iPiD API request failed, retrying', [
                    'method' => $method,
                    'path' => $path,
                    'attempt' => $attempt,
                    'exception' => $lastException->getMessage()
                ]);
                sleep(pow(2, $attempt));
            } else {
                break;
            }

            $attempt++;
        }

        $this->logger->error('iPiD API request failed', [
            'method' => $method,
            'path' => $path,
            'attempts' => $attempt - 1,
            'last_exception' => $lastException->getMessage()
        ]);

        throw $lastException;
    }

    public function getPublicKey(string $countryCode): array
    {
        $responseData = $this->makeRequest('GET', '/validation/api/v1/public-key', [
            'query' => ['country_code' => $countryCode],
        ]);

        return $responseData['data'];
    }

    public function sendValidationRequest(IpidValidationRequest $request): IpidValidationResponse
    {
        $responseData = $this->makeRequest('POST', '/validation/api/v1/bank-account/validate', [
            'json' => $request->getBody(),
        ]);

        $validationResponse = $this->responseDenormalizer->denormalize($responseData, null);

        $this->logger->info('iPiD validation response processed', [
            'response_status' => $validationResponse->getStatus()
        ]);

        return $validationResponse;
    }

    private function buildDefaultHeaders(): array
    {
        return [
            'x-api-key' => $this->apiKey,
            'x-customer-id' => $this->customerId,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    private function shouldRetry(\Exception $exception): bool
    {
        if (!$exception instanceof IpidApiException) {
            return false;
        }

        return in_array($exception->getCode(), [
            self::ERROR_CODE_TIMEOUT,
            self::ERROR_CODE_RATE_LIMIT_EXCEEDED,
            self::ERROR_CODE_SERVICE_UNAVAILABLE,
        ], true);
    }
}
