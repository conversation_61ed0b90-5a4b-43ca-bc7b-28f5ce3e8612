<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ServerException;
use Psr\Log\LoggerInterface;

class IpidApiClient
{
    public const DEFAULT_TIMEOUT = 30;
    public const DEFAULT_RETRY_ATTEMPTS = 3;

    public const ERROR_CODE_INVALID_REQUEST = 'IPID_001';
    public const ERROR_CODE_AUTHENTICATION_FAILED = 'IPID_002';
    public const ERROR_CODE_RATE_LIMIT_EXCEEDED = 'IPID_003';
    public const ERROR_CODE_SERVICE_UNAVAILABLE = 'IPID_004';
    public const ERROR_CODE_TIMEOUT = 'IPID_005';

    private Client $httpClient;
    private IpidResponseDenormalizer $responseDenormalizer;
    private string $endpoint;
    private string $clientId;
    private string $clientSecret;
    private LoggerInterface $logger;
    private ?string $accessToken = null;
    private ?int $tokenExpiresAt = null;

    public function __construct(
        Client $httpClient,
        IpidResponseDenormalizer $responseDenormalizer,
        string $endpoint,
        string $clientId,
        string $clientSecret,
        LoggerInterface $logger
    ) {
        $this->httpClient = $httpClient;
        $this->responseDenormalizer = $responseDenormalizer;
        $this->endpoint = $endpoint;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->logger = $logger;
    }

    public function makeRequest(string $method, string $path, array $options = [], bool $useDefaultHeaders = true): array
    {
        $attempt = 1;
        $lastException = null;
        $fullUrl = $this->endpoint . $path;

        $defaultOptions = [
            'timeout' => self::DEFAULT_TIMEOUT,
        ];

        if ($useDefaultHeaders) {
            $defaultOptions['headers'] = $this->buildDefaultHeaders();
        }

        $options = array_merge_recursive($defaultOptions, $options);

        while ($attempt <= self::DEFAULT_RETRY_ATTEMPTS) {
            try {
                $this->logger->info('iPiD API request', [
                    'method' => $method,
                    'path' => $path,
                    'attempt' => $attempt,
                ]);

                $response = $this->httpClient->request($method, $fullUrl, $options);

                $statusCode = $response->getStatusCode();

                if ($statusCode >= 200 && $statusCode < 300) {
                    $responseData = json_decode($response->getBody()->getContents(), true);

                    $this->logger->info('iPiD API response successful', [
                        'method' => $method,
                        'path' => $path,
                        'status_code' => $statusCode,
                    ]);

                    return $responseData;
                }

                throw new IpidApiException(
                    "iPiD API returned non-success status: $statusCode",
                    self::ERROR_CODE_SERVICE_UNAVAILABLE
                );

            } catch (ConnectException $e) {
                $lastException = new IpidApiException(
                    'iPiD API connection timeout',
                    self::ERROR_CODE_TIMEOUT,
                    $e
                );
            } catch (ClientException $e) {
                $statusCode = $e->getResponse()->getStatusCode();

                if ($statusCode === 401 || $statusCode === 403) {
                    throw new IpidApiException(
                        'iPiD API authentication failed',
                        self::ERROR_CODE_AUTHENTICATION_FAILED,
                        $e
                    );
                }

                if ($statusCode === 429) {
                    $lastException = new IpidApiException(
                        'iPiD API rate limit exceeded',
                        self::ERROR_CODE_RATE_LIMIT_EXCEEDED,
                        $e
                    );
                } else {
                    throw new IpidApiException(
                        "iPiD API client error: $statusCode",
                        self::ERROR_CODE_INVALID_REQUEST,
                        $e
                    );
                }
            } catch (ServerException $e) {
                $lastException = new IpidApiException(
                    'iPiD API server error',
                    self::ERROR_CODE_SERVICE_UNAVAILABLE,
                    $e
                );
            } catch (RequestException $e) {
                $lastException = new IpidApiException(
                    'iPiD API request error',
                    self::ERROR_CODE_SERVICE_UNAVAILABLE,
                    $e
                );
            }

            if ($attempt < self::DEFAULT_RETRY_ATTEMPTS && $this->shouldRetry($lastException)) {
                $this->logger->warning('iPiD API request failed, retrying', [
                    'method' => $method,
                    'path' => $path,
                    'attempt' => $attempt,
                    'exception' => $lastException->getMessage()
                ]);
                sleep(pow(2, $attempt));
            } else {
                break;
            }

            $attempt++;
        }

        $this->logger->error('iPiD API request failed', [
            'method' => $method,
            'path' => $path,
            'attempts' => $attempt - 1,
            'last_exception' => $lastException->getMessage()
        ]);

        throw $lastException;
    }

    public function sendValidationRequest(IpidValidationRequest $request): IpidValidationResponse
    {
        $responseData = $this->makeRequest('POST', '/api/v1/bank-account/validate', [
            'json' => $request->getBody(),
        ]);

        $validationResponse = $this->responseDenormalizer->denormalize($responseData, null);

        $this->logger->info('iPiD validation response processed', [
            'response_status' => $validationResponse->getStatus()
        ]);

        return $validationResponse;
    }

    private function buildDefaultHeaders(): array
    {
        $this->ensureValidAccessToken();

        return [
            'Authorization' => 'Bearer ' . $this->accessToken,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    private function ensureValidAccessToken(): void
    {
        if ($this->accessToken && $this->tokenExpiresAt && time() < $this->tokenExpiresAt - 60) {
            return; // Token is still valid (with 60 second buffer)
        }

        $this->refreshAccessToken();
    }

    private function refreshAccessToken(): void
    {
        try {
            $response = $this->httpClient->post($this->endpoint . '/oauth/api/v1/token', [
                'json' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'client_credentials'
                ],
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (!isset($data['access_token'])) {
                throw new IpidApiException('Failed to obtain access token', 'AUTH_ERROR');
            }

            $this->accessToken = $data['access_token'];
            $this->tokenExpiresAt = time() + ($data['expires_in'] ?? 7200);

            $this->logger->info('iPiD access token refreshed successfully');
        } catch (\Exception $e) {
            $this->logger->error('Failed to refresh iPiD access token', [
                'error' => $e->getMessage()
            ]);
            throw new IpidApiException('Authentication failed: ' . $e->getMessage(), 'AUTH_ERROR');
        }
    }

    private function shouldRetry(\Exception $exception): bool
    {
        if (!$exception instanceof IpidApiException) {
            return false;
        }

        return in_array($exception->getCode(), [
            self::ERROR_CODE_TIMEOUT,
            self::ERROR_CODE_RATE_LIMIT_EXCEEDED,
            self::ERROR_CODE_SERVICE_UNAVAILABLE,
        ], true);
    }
}
