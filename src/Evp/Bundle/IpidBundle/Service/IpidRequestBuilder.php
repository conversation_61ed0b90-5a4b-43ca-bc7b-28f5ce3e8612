<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;

class IpidRequestBuilder
{
    private IpidTransferHelper $transferHelper;
    private IpidEncryptionService $encryptionService;
    private IpidApiClient $apiClient;

    public function __construct(
        IpidTransferHelper $transferHelper,
        IpidEncryptionService $encryptionService,
        IpidApiClient $apiClient
    ) {
        $this->transferHelper = $transferHelper;
        $this->encryptionService = $encryptionService;
        $this->apiClient = $apiClient;
    }

    public function buildRequest(TransferOut $transfer): IpidValidationRequest
    {
        try {
            $country = $this->transferHelper->extractCountryFromTransfer($transfer);

            // Get public key for encryption
            $publicKeyData = $this->apiClient->getPublicKey($country);

            // Build the payload
            $creditor = $this->buildCreditor($transfer);
            $creditorAccount = $this->buildCreditorAccount($transfer);
            $creditorAgent = $this->buildCreditorAgent($transfer);

            $payload = new IpidValidationPayload($creditor, $creditorAccount, $creditorAgent);
            $payload->validate();

            // Encrypt the payload
            $encryptedPayload = $this->encryptionService->encrypt(
                $payload->toJson(),
                $publicKeyData['node_public_key']
            );

            // Create the request with simplified structure
            $request = new IpidValidationRequest($encryptedPayload);

            // Set optional fields based on API specification
            if (isset($publicKeyData['node_id'])) {
                $request->setNodeId($publicKeyData['node_id']);
            }

            return $request;

        } catch (\InvalidArgumentException $e) {
            throw new \InvalidArgumentException(
                sprintf('Invalid transfer data for iPiD validation: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (\RuntimeException $e) {
            throw new \RuntimeException(
                sprintf('Failed to build iPiD validation request: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (\Exception $e) {
            throw new \RuntimeException(
                sprintf('Unexpected error building iPiD request for transfer %d: %s',
                    $transfer->getId(),
                    $e->getMessage()
                ),
                0,
                $e
            );
        }
    }

    private function buildCreditor(TransferOut $transfer): IpidCreditor
    {
        $creditor = new IpidCreditor();

        $beneficiaryName = $this->transferHelper->getBeneficiaryName($transfer);
        if (!$beneficiaryName) {
            throw new \InvalidArgumentException('Beneficiary name is required for iPiD validation');
        }

        // Use full name by default - API will handle name matching
        $creditor->setName($beneficiaryName);

        // Add identification if available
        $personCode = $this->transferHelper->extractPersonCodeFromTransfer($transfer);
        if ($personCode) {
            $creditor->addIdentification($personCode, 'registration_id');
        }

        return $creditor;
    }

    private function buildCreditorAccount(TransferOut $transfer): IpidCreditorAccount
    {
        $creditorAccount = new IpidCreditorAccount();

        $beneficiary = $transfer->getBeneficiary();

        if (!$beneficiary) {
            return $creditorAccount;
        }

        if ($beneficiary instanceof PartyIban) {
            $creditorAccount->setIban($beneficiary->getIban());
        }

        if ($beneficiary instanceof PartyBank) {
            ;
            $creditorAccount->setAccountId($beneficiary->getDisplayAccount());
        }

        return $creditorAccount;
    }

    private function buildCreditorAgent(TransferOut $transfer): IpidCreditorAgent
    {
        $creditorAgent = new IpidCreditorAgent();

        $bic = $this->transferHelper->getBeneficiaryBankBic($transfer);
        if ($bic) {
            $creditorAgent->setBic($bic);
        }

        $sortCode = $this->transferHelper->getBeneficiarySortCode($transfer);
        if ($sortCode) {
            $creditorAgent->setClearingSystemId($sortCode);
        }

        return $creditorAgent;
    }

    private function isIban(string $accountNumber): bool
    {
        return preg_match('/^[A-Z]{2}[0-9]{2}/', strtoupper($accountNumber)) === 1;
    }
}
