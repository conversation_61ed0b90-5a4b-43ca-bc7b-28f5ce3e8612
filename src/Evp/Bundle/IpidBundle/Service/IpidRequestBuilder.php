<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;

class IpidRequestBuilder
{
    private IpidTransferHelper $transferHelper;
    private IpidEncryptionService $encryptionService;
    private IpidApiClient $apiClient;
    private IpidNodeSelector $nodeSelector;
    private IpidCorridorSpecification $corridorSpecification;

    public function __construct(
        IpidTransferHelper $transferHelper,
        IpidEncryptionService $encryptionService,
        IpidApiClient $apiClient,
        IpidNodeSelector $nodeSelector,
        IpidCorridorSpecification $corridorSpecification
    ) {
        $this->transferHelper = $transferHelper;
        $this->encryptionService = $encryptionService;
        $this->apiClient = $apiClient;
        $this->nodeSelector = $nodeSelector;
        $this->corridorSpecification = $corridorSpecification;
    }

    public function buildRequest(TransferOut $transfer): IpidValidationRequest
    {
        try {
            $country = $this->transferHelper->extractCountryFromTransfer($transfer);

            $identityType = $this->determineIdentityType($transfer);
            $nodeId = $this->nodeSelector->getNodeIdForCountry($country, $identityType);

            $publicKeyData = $this->apiClient->getPublicKey($country);

            $creditor = $this->buildCreditor($transfer, $country);
            $creditorAccount = $this->buildCreditorAccount($transfer, $country);
            $creditorAgent = $this->buildCreditorAgent($transfer, $country);

            $payload = new IpidValidationPayload($creditor, $creditorAccount, $creditorAgent);
            $payload->validate();

            $encryptedPayload = $this->encryptionService->encrypt($payload->toJson(), $publicKeyData['node_public_key']);

            $request = new IpidValidationRequest($encryptedPayload, $nodeId);

            $this->setMandatoryOptionalFields($request, $transfer, $country);

            return $request;
        } catch (\InvalidArgumentException $e) {
            throw new \InvalidArgumentException(
                sprintf('Invalid transfer data for iPiD validation: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (\RuntimeException $e) {
            throw new \RuntimeException(
                sprintf('Failed to build iPiD validation request: %s', $e->getMessage()),
                0,
                $e
            );
        } catch (\Exception $e) {
            throw new \RuntimeException(
                sprintf('Unexpected error building iPiD request for transfer %d: %s', 
                    $transfer->getId(), 
                    $e->getMessage()
                ),
                0,
                $e
            );
        }
    }

    private function buildCreditor(TransferOut $transfer, string $country): IpidCreditor
    {
        $creditor = new IpidCreditor();

        $beneficiaryName = $this->transferHelper->getBeneficiaryName($transfer);

        if (!$beneficiaryName) {
            throw new \InvalidArgumentException('Beneficiary name is required for iPiD validation');
        }

        if ($this->corridorSpecification->shouldSplitName($country)) {
            $nameParts = $this->splitName($beneficiaryName);
            $creditor->setGivenName($nameParts['given_name']);
            if (!empty($nameParts['surname'])) {
                $creditor->setSurname($nameParts['surname']);
            }
        } else {
            $creditor->setName($beneficiaryName);
        }

        $personCode = $this->transferHelper->extractPersonCodeFromTransfer($transfer);
        if ($personCode) {
            $identificationType = $this->corridorSpecification->getIdentificationTypeForCountry($country);
            $creditor->addIdentification($personCode, $identificationType);
        }

        return $creditor;
    }

    private function buildCreditorAccount(TransferOut $transfer, string $country): IpidCreditorAccount
    {
        $creditorAccount = new IpidCreditorAccount();

        $accountNumber = $this->transferHelper->getBeneficiaryAccount($transfer);

        if (!$accountNumber) {
            throw new \InvalidArgumentException('Beneficiary account number is required for iPiD validation');
        }

        $preferredType = $this->corridorSpecification->getPreferredAccountType($country);

        if ($preferredType === 'iban' && $this->isIban($accountNumber)) {
            $creditorAccount->setIban($accountNumber);
        } elseif ($preferredType === 'account_id' || $preferredType === 'flexible') {
            if ($this->isIban($accountNumber) && $preferredType === 'flexible') {
                $creditorAccount->setIban($accountNumber);
            } else {
                $creditorAccount->setAccountId($accountNumber);
            }
        } else {
            if ($this->isIban($accountNumber)) {
                $creditorAccount->setIban($accountNumber);
            } else {
                $creditorAccount->setAccountId($accountNumber);
            }
        }

        $currency = $this->transferHelper->getCurrency($transfer);
        if ($currency && $this->shouldIncludeCurrency($country)) {
            $creditorAccount->setCurrency($currency);
        }

        return $creditorAccount;
    }

    private function buildCreditorAgent(TransferOut $transfer, string $country): IpidCreditorAgent
    {
        $creditorAgent = new IpidCreditorAgent();

        if ($this->corridorSpecification->isBicRequired($country) || $this->shouldIncludeBic($country)) {
            $bic = $this->transferHelper->getBeneficiaryBankBic($transfer);
            if ($bic) {
                $creditorAgent->setBic($bic);
            }
        }

        if ($this->corridorSpecification->isClearingSystemIdRequired($country) || $this->shouldIncludeClearingSystemId($country)) {
            $sortCode = $this->transferHelper->getBeneficiarySortCode($transfer);
            if ($sortCode) {
                $creditorAgent->setClearingSystemId($sortCode);
            }
        }

        return $creditorAgent;
    }

    private function setMandatoryOptionalFields(IpidValidationRequest $request, TransferOut $transfer, string $country): void
    {
        if ($this->shouldIncludeClearingSystemId($country)) {
            $sortCode = $this->transferHelper->getBeneficiarySortCode($transfer);
            if ($sortCode) {
                $request->setClearingSystemId($sortCode);
            }
        }

        if ($this->shouldIncludeBic($country)) {
            $bic = $this->transferHelper->getBeneficiaryBankBic($transfer);
            if ($bic) {
                $request->setBic($bic);
            }
        }
    }

    private function determineIdentityType(TransferOut $transfer): ?string
    {
        return $this->transferHelper->getBeneficiaryType($transfer);
    }

    private function splitName(string $fullName): array
    {
        $parts = explode(' ', trim($fullName), 2);
        return [
            'given_name' => $parts[0] ?? '',
            'surname' => $parts[1] ?? '',
        ];
    }

    private function formatPhoneNumber(string $phone): string
    {
        if (!str_starts_with($phone, '+')) {
            return '+' . ltrim($phone, '0');
        }
        return $phone;
    }

    private function isIban(string $accountNumber): bool
    {
        return preg_match('/^[A-Z]{2}[0-9]{2}/', strtoupper($accountNumber)) === 1;
    }

    private function shouldIncludeCurrency(string $country): bool
    {
        return in_array(strtoupper($country), ['BR', 'MX', 'AR', 'UY', 'PE', 'MY', 'CN', 'KR'], true);
    }

    private function shouldIncludeBic(string $country): bool
    {
        return $this->corridorSpecification->isBicRequired($country) ||
               in_array(strtoupper($country), ['BD', 'AR', 'BE', 'IT', 'UY', 'TR', 'KR'], true);
    }

    private function shouldIncludeClearingSystemId(string $country): bool
    {
        return $this->corridorSpecification->isClearingSystemIdRequired($country) ||
               in_array(strtoupper($country), ['US', 'CN', 'BR', 'MX', 'PE', 'MY'], true);
    }

    private function shouldIncludePhoneForCountry(string $country): bool
    {
        return in_array(strtoupper($country), ['US', 'CN', 'IN', 'MY', 'NG'], true);
    }

    private function shouldIncludeEmailForCountry(string $country): bool
    {
        return in_array(strtoupper($country), ['US', 'CN', 'IN', 'MY'], true);
    }
}
