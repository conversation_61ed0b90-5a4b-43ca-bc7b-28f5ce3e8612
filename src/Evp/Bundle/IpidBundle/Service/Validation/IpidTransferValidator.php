<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service\Validation;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;

class IpidTransferValidator
{
    private IpidCountryRegistry $countryRegistry;
    private TransferIpidConsentRepository $consentRepository;
    private IpidCorridorSpecification $corridorSpecification;

    public function __construct(
        IpidCountryRegistry $countryRegistry,
        TransferIpidConsentRepository $consentRepository,
        IpidCorridorSpecification $corridorSpecification
    ) {
        $this->countryRegistry = $countryRegistry;
        $this->consentRepository = $consentRepository;
        $this->corridorSpecification = $corridorSpecification;
    }

    public function isCountrySupported(string $country): bool
    {
        $isEnabled = $this->countryRegistry->isCountryEnabled($country);
        $hasCorridorSupport = $this->corridorSpecification->isCountrySupported($country);

        return $isEnabled && $hasCorridorSupport;
    }

    public function findExistingConsent(TransferOut $transfer): ?TransferIpidConsent
    {
        return $this->consentRepository->findByTransferId($transfer->getId());
    }
}
