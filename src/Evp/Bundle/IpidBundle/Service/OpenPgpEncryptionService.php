<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Service;

class OpenPgpEncryptionService implements IpidEncryptionService
{
    private ?string $clientPrivateKey;

    public function __construct(?string $clientPrivateKey = null)
    {
        $this->clientPrivateKey = $clientPrivateKey;
    }

    public function encrypt(string $payload, string $publicKey = null): string
    {
        if ($publicKey === null) {
            throw new \InvalidArgumentException('Public key is required for encryption');
        }

        $gnupg = $this->createGnuPGResource();
        $publicKeyResource = gnupg_import($gnupg, $publicKey);
        if (!$publicKeyResource) {
            throw new \RuntimeException('Failed to import iPiD public key');
        }

        gnupg_addencryptkey($gnupg, $publicKeyResource['fingerprint']);
        
        $encryptedData = gnupg_encrypt($gnupg, $payload);
        if (!$encryptedData) {
            throw new \RuntimeException('Failed to encrypt payload');
        }

        return base64_encode($encryptedData);
    }

    public function decrypt(string $encryptedPayload): string
    {
        if ($this->clientPrivateKey === null) {
            throw new \RuntimeException('Private key not configured for decryption');
        }

        $gnupg = $this->createGnuPGResource();

        $privateKeyResource = gnupg_import($gnupg, $this->clientPrivateKey);
        if (!$privateKeyResource) {
            throw new \RuntimeException('Failed to import private key');
        }

        gnupg_adddecryptkey($gnupg, $privateKeyResource['fingerprint']);
        
        $decodedPayload = base64_decode($encryptedPayload, true);
        if ($decodedPayload === false) {
            throw new \RuntimeException('Invalid base64 encoded payload');
        }

        $decryptedData = gnupg_decrypt($gnupg, $decodedPayload);
        if (!$decryptedData) {
            throw new \RuntimeException('Failed to decrypt payload');
        }

        return $decryptedData;
    }

    private function createGnuPGResource()
    {
        $gnupg = gnupg_init();
        if (!$gnupg) {
            throw new \RuntimeException('Failed to initialize GnuPG');
        }

        gnupg_seterrormode($gnupg, GNUPG_ERROR_EXCEPTION);
        return $gnupg;
    }
} 