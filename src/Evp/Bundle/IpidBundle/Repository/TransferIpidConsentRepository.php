<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;

class TransferIpidConsentRepository extends EntityRepository
{
    public function save(TransferIpidConsent $consent): TransferIpidConsent
    {
        $this->getEntityManager()->persist($consent);
        $this->getEntityManager()->flush();

        return $consent;
    }

    public function findByTransferId(int $transferId): ?TransferIpidConsent
    {
        return $this->findOneBy(['transferId' => $transferId]);
    }

    public function findByTransferIds(array $transferIds): array
    {
        if (empty($transferIds)) {
            return [];
        }

        return $this->createQueryBuilder('tic')
            ->leftJoin('tic.ipidApiResponse', 'iar')
            ->addSelect('iar')
            ->where('tic.transferId IN (:transferIds)')
            ->setParameter('transferIds', $transferIds)
            ->orderBy('tic.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findPendingReviews(): array
    {
        return $this->createQueryBuilder('tic')
            ->leftJoin('tic.ipidApiResponse', 'iar')
            ->addSelect('iar')
            ->where('tic.requiresManualReview = :requiresReview')
            ->andWhere('tic.consentStatus = :status')
            ->setParameter('requiresReview', true)
            ->setParameter('status', TransferIpidConsent::CONSENT_STATUS_PENDING)
            ->orderBy('tic.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findByConsentStatus(string $status): array
    {
        return $this->createQueryBuilder('tic')
            ->leftJoin('tic.ipidApiResponse', 'iar')
            ->addSelect('iar')
            ->where('tic.consentStatus = :status')
            ->setParameter('status', $status)
            ->orderBy('tic.createdAt', 'DESC')
            ->setMaxResults(1000)
            ->getQuery()
            ->getResult();
    }

    public function findRecentConsents(\DateTimeInterface $since): array
    {
        return $this->createQueryBuilder('tic')
            ->leftJoin('tic.ipidApiResponse', 'iar')
            ->addSelect('iar')
            ->where('tic.createdAt >= :since')
            ->setParameter('since', $since)
            ->orderBy('tic.createdAt', 'DESC')
            ->setMaxResults(500)
            ->getQuery()
            ->getResult();
    }

    public function findRecentConsentsWithPagination(\DateTimeInterface $since, int $limit = 50, int $offset = 0): array
    {
        return $this->createQueryBuilder('tic')
            ->leftJoin('tic.ipidApiResponse', 'iar')
            ->addSelect('iar')
            ->where('tic.createdAt >= :since')
            ->setParameter('since', $since)
            ->orderBy('tic.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->getQuery()
            ->getResult();
    }

    public function updateConsentStatus(int $consentId, string $newStatus): bool
    {
        $consent = $this->find($consentId);
        
        if (!$consent) {
            return false;
        }

        $consent->setConsentStatus($newStatus);
        $consent->setUpdatedAt(new \DateTime());
        
        $this->getEntityManager()->flush();
        
        return true;
    }

    public function bulkUpdateConsentStatus(array $consentIds, string $newStatus): int
    {
        if (empty($consentIds)) {
            return 0;
        }

        $qb = $this->getEntityManager()->createQueryBuilder();
        
        $result = $qb
            ->update(TransferIpidConsent::class, 'tic')
            ->set('tic.consentStatus', ':newStatus')
            ->set('tic.updatedAt', ':updatedAt')
            ->where('tic.id IN (:ids)')
            ->setParameter('newStatus', $newStatus)
            ->setParameter('updatedAt', new \DateTime())
            ->setParameter('ids', $consentIds)
            ->getQuery()
            ->execute();

        return $result;
    }

    public function countByStatus(): array
    {
        $qb = $this->createQueryBuilder('tic');
        
        $results = $qb
            ->select('tic.consentStatus as status, COUNT(tic.id) as count')
            ->groupBy('tic.consentStatus')
            ->getQuery()
            ->getArrayResult();

        $counts = [];
        foreach ($results as $result) {
            $counts[$result['status']] = (int) $result['count'];
        }

        return $counts;
    }

    public function findExpiredConsents(\DateTimeInterface $expiryDate): array
    {
        $qb = $this->createQueryBuilder('tic');
        
        return $qb
            ->where('tic.createdAt < :expiry')
            ->andWhere('tic.consentStatus IN (:statuses)')
            ->setParameter('expiry', $expiryDate)
            ->setParameter('statuses', [
                TransferIpidConsent::CONSENT_STATUS_PENDING,
                TransferIpidConsent::CONSENT_STATUS_REQUIRED
            ])
            ->getQuery()
            ->getResult();
    }
} 