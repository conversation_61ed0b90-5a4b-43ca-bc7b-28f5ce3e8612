<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;

class IpidApiResponseRepository extends EntityRepository
{
    public function save(IpidApiResponse $response): IpidApiResponse
    {
        $this->getEntityManager()->persist($response);
        $this->getEntityManager()->flush();

        return $response;
    }

    public function findValidByRequestHashAndCountry(string $requestHash, string $country): ?IpidApiResponse
    {
        $cacheDuration = IpidApiResponse::CACHE_DURATION_DAYS;
        $expiryDate = new \DateTime("-{$cacheDuration} days");

        return $this->createQueryBuilder('iar')
            ->where('iar.requestHash = :requestHash')
            ->andWhere('iar.country = :country')
            ->andWhere('iar.responseStatus = :status')
            ->andWhere('iar.createdAt > :expiry')
            ->setParameter('requestHash', $requestHash)
            ->setParameter('country', $country)
            ->setParameter('status', 'success')
            ->setParameter('expiry', $expiryDate)
            ->orderBy('iar.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findRecentResponses(\DateTimeInterface $since, ?string $country = null): array
    {
        $qb = $this->createQueryBuilder('iar')
            ->where('iar.createdAt >= :since')
            ->setParameter('since', $since)
            ->orderBy('iar.createdAt', 'DESC')
            ->setMaxResults(1000);

        if ($country) {
            $qb->andWhere('iar.country = :country')
               ->setParameter('country', $country);
        }

        return $qb->getQuery()->getResult();
    }

    public function findByResponseCodes(array $responseCodes): array
    {
        if (empty($responseCodes)) {
            return [];
        }

        return $this->createQueryBuilder('iar')
            ->where('iar.responseCode IN (:codes)')
            ->setParameter('codes', $responseCodes)
            ->orderBy('iar.createdAt', 'DESC')
            ->setMaxResults(500)
            ->getQuery()
            ->getResult();
    }

    public function findDuplicateRequests(string $requestHash, string $excludeCountry = null): array
    {
        $qb = $this->createQueryBuilder('iar')
            ->where('iar.requestHash = :requestHash')
            ->setParameter('requestHash', $requestHash);

        if ($excludeCountry) {
            $qb->andWhere('iar.country != :excludeCountry')
               ->setParameter('excludeCountry', $excludeCountry);
        }

        return $qb->orderBy('iar.createdAt', 'DESC')
                  ->getQuery()
                  ->getResult();
    }

    public function getResponseStatistics(\DateTimeInterface $since): array
    {
        $qb = $this->createQueryBuilder('iar')
            ->select([
                'iar.country',
                'iar.responseStatus',
                'COUNT(iar.id) as response_count',
                'AVG(iar.matchScore) as avg_match_score',
                'COUNT(CASE WHEN iar.requiresConsent = true THEN 1 END) as consent_required_count'
            ])
            ->where('iar.createdAt >= :since')
            ->setParameter('since', $since)
            ->groupBy('iar.country', 'iar.responseStatus')
            ->orderBy('iar.country', 'ASC');

        return $qb->getQuery()->getResult();
    }

    public function cleanupExpiredResponses(\DateTimeInterface $expiredBefore): int
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        
        return $qb->delete(IpidApiResponse::class, 'iar')
            ->where('iar.createdAt < :expiredBefore')
            ->andWhere('iar.responseStatus != :successStatus')
            ->setParameter('expiredBefore', $expiredBefore)
            ->setParameter('successStatus', 'success')
            ->getQuery()
            ->execute();
    }

    public function findResponsesRequiringAttention(): array
    {
        return $this->createQueryBuilder('iar')
            ->where('iar.responseCode IN (:reviewCodes)')
            ->andWhere('iar.createdAt >= :recentThreshold')
            ->setParameter('reviewCodes', ['2101', '2104', '4001', '4002'])
            ->setParameter('recentThreshold', new \DateTime('-7 days'))
            ->orderBy('iar.createdAt', 'DESC')
            ->setMaxResults(100)
            ->getQuery()
            ->getResult();
    }

    public function findByMatchScoreRange(float $minScore, float $maxScore, int $limit = 100): array
    {
        return $this->createQueryBuilder('iar')
            ->where('iar.matchScore >= :minScore')
            ->andWhere('iar.matchScore <= :maxScore')
            ->andWhere('iar.matchScore IS NOT NULL')
            ->setParameter('minScore', $minScore)
            ->setParameter('maxScore', $maxScore)
            ->orderBy('iar.matchScore', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
