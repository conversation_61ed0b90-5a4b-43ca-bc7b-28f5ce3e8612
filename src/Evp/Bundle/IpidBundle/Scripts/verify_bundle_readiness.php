<?php

declare(strict_types=1);

/**
 * <PERSON>rip<PERSON> to verify iPiD Bundle is ready for API calls
 */

require_once __DIR__ . '/../../../../../vendor/autoload.php';

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use GuzzleHttp\Client;
use Psr\Log\NullLogger;

echo "🔍 iPiD Bundle Readiness Verification\n";
echo "=====================================\n\n";

// 1. Check required environment variables
echo "1. Checking environment variables...\n";
$requiredEnvVars = [
    'IPID_API_ENDPOINT' => 'API endpoint URL',
    'IPID_API_KEY' => 'API authentication key', 
    'IPID_CUSTOMER_ID' => 'Customer identifier',
    'IPID_CLIENT_PRIVATE_KEY' => 'Client private key for encryption'
];

$envStatus = true;
foreach ($requiredEnvVars as $var => $description) {
    $value = $_ENV[$var] ?? getenv($var);
    if (empty($value)) {
        echo "   ❌ {$var}: Not set ({$description})\n";
        $envStatus = false;
    } else {
        $displayValue = $var === 'IPID_CLIENT_PRIVATE_KEY' ? '[PRIVATE KEY SET]' : $value;
        echo "   ✅ {$var}: {$displayValue}\n";
    }
}

if (!$envStatus) {
    echo "\n❌ Environment variables missing. Please set the required variables.\n";
    echo "\nExample .env configuration:\n";
    echo "IPID_API_ENDPOINT=https://api.ipid.live\n";
    echo "IPID_API_KEY=your-api-key\n";
    echo "IPID_CUSTOMER_ID=your-customer-id\n";
    echo "IPID_CLIENT_PRIVATE_KEY=\"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\"\n\n";
}

// 2. Check class autoloading
echo "\n2. Checking class autoloading...\n";
$requiredClasses = [
    'Evp\Bundle\IpidBundle\Service\IpidApiClient',
    'Evp\Bundle\IpidBundle\Service\IpidRequestBuilder',
    'Evp\Bundle\IpidBundle\DTO\IpidValidationRequest',
    'Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer',
    'GuzzleHttp\Client'
];

$classStatus = true;
foreach ($requiredClasses as $class) {
    if (class_exists($class)) {
        echo "   ✅ {$class}\n";
    } else {
        echo "   ❌ {$class}: Not found\n";
        $classStatus = false;
    }
}

// 3. Test service instantiation
echo "\n3. Testing service instantiation...\n";
try {
    // Create mock dependencies
    $httpClient = new Client();
    $responseDenormalizer = new class implements \Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer {
        public function denormalize(array $data, string $type): object {
            return new stdClass();
        }
    };
    $logger = new NullLogger();
    
    // Test API client
    $apiClient = new IpidApiClient(
        $httpClient,
        $responseDenormalizer,
        'https://api.ipid.live',
        'test-key',
        'test-customer',
        $logger
    );
    echo "   ✅ IpidApiClient instantiated successfully\n";
    
    // Test encryption service mock
    $encryptionService = new class implements \Evp\Bundle\IpidBundle\Service\IpidEncryptionService {
        public function encrypt(string $data, string $publicKey): string {
            return base64_encode($data);
        }
        public function decrypt(string $encryptedData, string $privateKey): string {
            return base64_decode($encryptedData);
        }
    };
    
    // Test request builder
    $requestBuilder = new IpidRequestBuilder($encryptionService, $apiClient);
    echo "   ✅ IpidRequestBuilder instantiated successfully\n";
    
} catch (Exception $e) {
    echo "   ❌ Service instantiation failed: " . $e->getMessage() . "\n";
    $classStatus = false;
}

// 4. Test basic request building
echo "\n4. Testing request building functionality...\n";
try {
    if (isset($requestBuilder)) {
        // Create mock transfer
        $transfer = new class extends TransferOut {
            public function getId(): int { return 12345; }
            public function getBeneficiary() {
                return new class extends PartyIban {
                    public function getIban(): string { return '**********************'; }
                    public function getName(): string { return 'John Smith'; }
                };
            }
        };
        
        // Mock API client to avoid real API calls
        $mockApiClient = new class($httpClient, $responseDenormalizer, 'https://api.ipid.live', 'test', 'test', $logger) extends IpidApiClient {
            public function getPublicKey(string $country): array {
                return ['node_public_key' => 'mock-public-key'];
            }
        };
        
        $testRequestBuilder = new IpidRequestBuilder($encryptionService, $mockApiClient);
        $result = $testRequestBuilder->buildRequest($transfer);
        
        echo "   ✅ Request building successful\n";
        echo "   ✅ Generated encrypted payload: " . substr($result->getEncryptedPayload(), 0, 50) . "...\n";
    }
} catch (Exception $e) {
    echo "   ❌ Request building failed: " . $e->getMessage() . "\n";
}

// 5. Summary
echo "\n📋 Summary\n";
echo "==========\n";

if ($envStatus && $classStatus) {
    echo "🎉 iPiD Bundle is ready for API calls!\n\n";
    echo "✅ All environment variables are set\n";
    echo "✅ All required classes are available\n";
    echo "✅ Services can be instantiated\n";
    echo "✅ Request building works\n\n";
    
    echo "🚀 Next steps:\n";
    echo "1. Configure your environment with real API credentials\n";
    echo "2. Test with a real transfer using IpidValidationService\n";
    echo "3. Monitor API responses and handle errors appropriately\n\n";
    
    exit(0);
} else {
    echo "❌ iPiD Bundle is NOT ready for API calls\n\n";
    echo "Issues found:\n";
    if (!$envStatus) echo "- Missing environment variables\n";
    if (!$classStatus) echo "- Class loading or instantiation issues\n";
    echo "\nPlease fix the issues above before using the bundle.\n\n";
    
    exit(1);
}
