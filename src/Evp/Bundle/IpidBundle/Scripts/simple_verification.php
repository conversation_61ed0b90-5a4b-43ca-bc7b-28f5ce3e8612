<?php

declare(strict_types=1);

/**
 * Simple verification script for iPiD Bundle
 */

require_once __DIR__ . '/../../../../../vendor/autoload.php';

echo "🔍 iPiD Bundle Simple Verification\n";
echo "==================================\n\n";

// 1. Check class loading
echo "1. Checking class autoloading...\n";
$classes = [
    'IpidApiClient' => 'Evp\Bundle\IpidBundle\Service\IpidApiClient',
    'IpidRequestBuilder' => 'Evp\Bundle\IpidBundle\Service\IpidRequestBuilder',
    'IpidValidationRequest' => 'Evp\Bundle\IpidBundle\DTO\IpidValidationRequest',
    'GuzzleHttp Client' => 'GuzzleHttp\Client'
];

$allClassesLoaded = true;
foreach ($classes as $name => $class) {
    if (class_exists($class)) {
        echo "   ✅ {$name}\n";
    } else {
        echo "   ❌ {$name}: Not found\n";
        $allClassesLoaded = false;
    }
}

// 2. Test basic instantiation
echo "\n2. Testing service instantiation...\n";
try {
    $httpClient = new \GuzzleHttp\Client();
    $responseDenormalizer = new \Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer();
    $logger = new \Psr\Log\NullLogger();
    
    $apiClient = new \Evp\Bundle\IpidBundle\Service\IpidApiClient(
        $httpClient,
        $responseDenormalizer,
        'https://api.ipid.live',
        'test-key',
        'test-customer',
        $logger
    );
    echo "   ✅ IpidApiClient created successfully\n";
    
    // Create a mock encryption service
    $encryptionService = new class implements \Evp\Bundle\IpidBundle\Service\IpidEncryptionService {
        public function encrypt(string $data, string $publicKey): string {
            return base64_encode($data);
        }
    };
    
    $requestBuilder = new \Evp\Bundle\IpidBundle\Service\IpidRequestBuilder(
        $encryptionService,
        $apiClient
    );
    echo "   ✅ IpidRequestBuilder created successfully\n";
    
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
    $allClassesLoaded = false;
}

// 3. Test DTO creation
echo "\n3. Testing DTO creation...\n";
try {
    $request = new \Evp\Bundle\IpidBundle\DTO\IpidValidationRequest('test-payload');
    echo "   ✅ IpidValidationRequest created successfully\n";
    
    $creditor = new \Evp\Bundle\IpidBundle\DTO\IpidCreditor();
    $creditor->setName('John Smith');
    echo "   ✅ IpidCreditor created successfully\n";
    
    $creditorAccount = new \Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount();
    $creditorAccount->setIban('**********************');
    echo "   ✅ IpidCreditorAccount created successfully\n";
    
    $creditorAgent = new \Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent();
    $creditorAgent->setBic('WESTGB2L');
    echo "   ✅ IpidCreditorAgent created successfully\n";
    
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
    $allClassesLoaded = false;
}

// 4. Environment variables check
echo "\n4. Checking environment variables...\n";
$envVars = [
    'IPID_API_ENDPOINT',
    'IPID_API_KEY',
    'IPID_CUSTOMER_ID',
    'IPID_CLIENT_PRIVATE_KEY'
];

$envConfigured = true;
foreach ($envVars as $var) {
    $value = $_ENV[$var] ?? getenv($var);
    if (empty($value)) {
        echo "   ⚠️  {$var}: Not set (required for API calls)\n";
        $envConfigured = false;
    } else {
        $display = $var === 'IPID_CLIENT_PRIVATE_KEY' ? '[SET]' : $value;
        echo "   ✅ {$var}: {$display}\n";
    }
}

// 5. Summary
echo "\n📋 Summary\n";
echo "==========\n";

if ($allClassesLoaded) {
    echo "✅ All classes load successfully\n";
    echo "✅ Services can be instantiated\n";
    echo "✅ DTOs can be created\n";
    
    if ($envConfigured) {
        echo "✅ Environment variables are configured\n";
        echo "\n🎉 iPiD Bundle is READY for API calls!\n";
    } else {
        echo "⚠️  Environment variables need configuration\n";
        echo "\n📝 To complete setup, configure these environment variables:\n";
        foreach ($envVars as $var) {
            if (empty($_ENV[$var] ?? getenv($var))) {
                echo "   {$var}=your-value\n";
            }
        }
        echo "\n✅ Bundle code is ready, just needs environment configuration\n";
    }
} else {
    echo "❌ Bundle has issues that need to be resolved\n";
}

echo "\n🚀 Next steps:\n";
echo "1. Set environment variables if not already done\n";
echo "2. Use IpidValidationService to validate transfers\n";
echo "3. Handle API responses appropriately\n";
echo "4. Monitor and log API interactions\n\n";
