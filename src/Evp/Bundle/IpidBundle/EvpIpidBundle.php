<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle;

use Evp\Bundle\IpidBundle\DependencyInjection\EvpIpidExtension;
use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\HttpKernel\Bundle\Bundle;

final class EvpIpidBundle extends Bundle
{
    public function getContainerExtension(): ?ExtensionInterface
    {
        return new EvpIpidExtension();
    }
}

