<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Entity;

class TransferIpidConsent
{
    public const CONSENT_STATUS_GRANTED = 'granted';
    public const CONSENT_STATUS_PENDING = 'pending';
    public const CONSENT_STATUS_DENIED = 'denied';
    public const CONSENT_STATUS_REQUIRED = 'required';

    public const MATCH_LEVEL_STRONG = 'Strong';
    public const MATCH_LEVEL_PARTIAL = 'Partial';
    public const MATCH_LEVEL_WEAK = 'Weak';
    public const MATCH_LEVEL_NO_MATCH = 'no_match';

    public const MATCH_LEVEL_HIGH = 'Strong';
    public const MATCH_LEVEL_MEDIUM = 'Partial';
    public const MATCH_LEVEL_LOW = 'Weak';

    private int $id;
    private int $transferId;
    private ?IpidApiResponse $ipidApiResponse = null;
    private string $consentStatus;
    private string $matchLevel;
    private bool $requiresManualReview = false;
    private ?string $validationNotes = null;
    private \DateTimeInterface $createdAt;
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getTransferId(): int
    {
        return $this->transferId;
    }

    public function setTransferId(int $transferId): self
    {
        $this->transferId = $transferId;
        return $this;
    }

    public function getIpidApiResponse(): ?IpidApiResponse
    {
        return $this->ipidApiResponse;
    }

    public function setIpidApiResponse(?IpidApiResponse $ipidApiResponse): self
    {
        $this->ipidApiResponse = $ipidApiResponse;
        return $this;
    }

    public function getConsentStatus(): string
    {
        return $this->consentStatus;
    }

    public function setConsentStatus(string $consentStatus): self
    {
        $this->consentStatus = $consentStatus;
        return $this;
    }

    public function getMatchLevel(): string
    {
        return $this->matchLevel;
    }

    public function setMatchLevel(string $matchLevel): self
    {
        $this->matchLevel = $matchLevel;
        return $this;
    }

    public function requiresManualReview(): bool
    {
        return $this->requiresManualReview;
    }

    public function setRequiresManualReview(bool $requiresManualReview): self
    {
        $this->requiresManualReview = $requiresManualReview;
        return $this;
    }

    public function getValidationNotes(): ?string
    {
        return $this->validationNotes;
    }

    public function setValidationNotes(?string $validationNotes): self
    {
        $this->validationNotes = $validationNotes;
        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isGranted(): bool
    {
        return $this->consentStatus === self::CONSENT_STATUS_GRANTED;
    }

    public function isDenied(): bool
    {
        return $this->consentStatus === self::CONSENT_STATUS_DENIED;
    }

    public function isPending(): bool
    {
        return $this->consentStatus === self::CONSENT_STATUS_PENDING;
    }

    public function hasHighMatch(): bool
    {
        return $this->matchLevel === self::MATCH_LEVEL_HIGH;
    }
}
