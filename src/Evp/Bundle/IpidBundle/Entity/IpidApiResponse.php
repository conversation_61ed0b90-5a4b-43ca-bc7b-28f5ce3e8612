<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Entity;

class IpidApiResponse
{
    public const CACHE_DURATION_DAYS = 30;

    private int $id;
    private string $requestHash;
    private string $country;
    private ?float $matchScore = null;
    private ?string $matchLevel = null;
    private string $responseStatus;
    private string $responseCode;
    private string $responseMessage;
    private ?bool $requiresConsent = null;
    private \DateTimeInterface $createdAt;

    private ?string $vopIdMatch = null;
    private ?string $vopNameMatch = null;
    private ?bool $copMatched = null;
    private ?string $copReason = null;
    private ?string $reasonCode = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getRequestHash(): string
    {
        return $this->requestHash;
    }

    public function setRequestHash(string $requestHash): self
    {
        $this->requestHash = $requestHash;
        return $this;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getMatchScore(): ?float
    {
        return $this->matchScore;
    }

    public function setMatchScore(?float $matchScore): self
    {
        $this->matchScore = $matchScore;
        return $this;
    }

    public function getMatchLevel(): ?string
    {
        return $this->matchLevel;
    }

    public function setMatchLevel(?string $matchLevel): self
    {
        $this->matchLevel = $matchLevel;
        return $this;
    }

    public function getResponseStatus(): string
    {
        return $this->responseStatus;
    }

    public function setResponseStatus(string $responseStatus): self
    {
        $this->responseStatus = $responseStatus;
        return $this;
    }

    public function getResponseCode(): string
    {
        return $this->responseCode;
    }

    public function setResponseCode(string $responseCode): self
    {
        $this->responseCode = $responseCode;
        return $this;
    }

    public function getResponseMessage(): string
    {
        return $this->responseMessage;
    }

    public function setResponseMessage(string $responseMessage): self
    {
        $this->responseMessage = $responseMessage;
        return $this;
    }

    public function getRequiresConsent(): ?bool
    {
        return $this->requiresConsent;
    }

    public function setRequiresConsent(?bool $requiresConsent): self
    {
        $this->requiresConsent = $requiresConsent;
        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getVopIdMatch(): ?string
    {
        return $this->vopIdMatch;
    }

    public function setVopIdMatch(?string $vopIdMatch): self
    {
        $this->vopIdMatch = $vopIdMatch;
        return $this;
    }

    public function getVopNameMatch(): ?string
    {
        return $this->vopNameMatch;
    }

    public function setVopNameMatch(?string $vopNameMatch): self
    {
        $this->vopNameMatch = $vopNameMatch;
        return $this;
    }

    public function getCopMatched(): ?bool
    {
        return $this->copMatched;
    }

    public function setCopMatched(?bool $copMatched): self
    {
        $this->copMatched = $copMatched;
        return $this;
    }

    public function getCopReason(): ?string
    {
        return $this->copReason;
    }

    public function setCopReason(?string $copReason): self
    {
        $this->copReason = $copReason;
        return $this;
    }

    public function getReasonCode(): ?string
    {
        return $this->reasonCode;
    }

    public function setReasonCode(?string $reasonCode): self
    {
        $this->reasonCode = $reasonCode;
        return $this;
    }

    public function isExpired(\DateTimeInterface $now = null): bool
    {
        $now = $now ?? new \DateTime();
        $expiryDate = (clone $this->createdAt)->modify('+' . self::CACHE_DURATION_DAYS . ' days');
        return $expiryDate <= $now;
    }

    public function isSuccessful(): bool
    {
        return in_array($this->responseCode, ['2000', '2001', '2002'], true);
    }

    public function hasHighMatch(): bool
    {
        return $this->matchScore !== null && $this->matchScore >= 0.8;
    }
}
