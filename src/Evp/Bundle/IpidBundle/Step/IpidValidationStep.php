<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Step;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Psr\Log\LoggerInterface;

class IpidValidationStep implements StepInterface
{
    private IpidValidationService $ipidValidationService;
    private LoggerInterface $logger;

    public function __construct(
        IpidValidationService $ipidValidationService,
        LoggerInterface $logger
    ) {
        $this->ipidValidationService = $ipidValidationService;
        $this->logger = $logger;
    }

    public function apply($transfer): bool
    {
        if (!$transfer instanceof TransferOut) {
            $transferType = is_object($transfer) ? get_class($transfer) : gettype($transfer);
            $transferId = (is_object($transfer) && method_exists($transfer, 'getId')) ? $transfer->getId() : 'unknown';

            $this->logger->debug('iPiD validation step skipped - not a TransferOut entity', [
                'transfer_type' => $transferType,
                'transfer_id' => $transferId
            ]);

            return true;
        }

        try {
            $result = $this->ipidValidationService->validateTransfer($transfer);

            $this->logger->info('iPiD validation step completed successfully', [
                'transfer_id' => $transfer->getId(),
                'validation_status' => $result->getStatus(),
                'is_successful' => $result->isSuccessful(),
                'has_consent' => $result->getConsent() !== null,
                'requires_manual_review' => $result->requiresManualReview()
            ]);
        } catch (\Throwable $exception) {
            $this->logger->error('iPiD validation step encountered an error', [
                'transfer_id' => $transfer->getId(),
                'error_message' => $exception->getMessage(),
                'error_class' => get_class($exception),
                'trace' => $exception->getTraceAsString()
            ]);
        }

        return true;
    }
}
