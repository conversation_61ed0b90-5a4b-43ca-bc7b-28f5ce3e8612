<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Exception;

class IpidApiException extends \Exception
{
    private string $errorCode;

    public function __construct(string $message, string $errorCode, \Throwable $previous = null)
    {
        parent::__construct($message, 0, $previous);
        $this->errorCode = $errorCode;
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }
} 