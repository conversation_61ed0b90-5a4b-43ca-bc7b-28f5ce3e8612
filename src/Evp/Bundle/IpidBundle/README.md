# iPiD (Identity Verification) Bundle

## Overview

The iPiD Bundle provides bank account validation services integrated with the EVP Bank Transfer system. This implementation follows the **iPiD Validate API Specifications v1.8** and **Node API Specifications v2.7** exactly as documented.

## Architecture

### Core Components

1. **Service Layer**
   - `IpidValidationService` - Main validation orchestrator with comprehensive error handling
   - `IpidApiClient` - Centralized HTTP API communication with consistent error handling, retry logic, and configuration
   - `IpidOAuthService` - OAuth operations using IpidApiClient (no configuration duplication)
   - `IpidRequestBuilder` - Builds properly structured requests following corridor specifications
   - `IpidNodeSelector` - Handles node selection per country/method preferences (pure logic, no configuration)
   - `IpidCorridorSpecification` - Country-specific field requirements per section 11.6
   - `OpenPgpEncryptionService` - Handles OpenPGP encryption per specification (encryption always required)

2. **Data Transfer Objects (DTOs)**
   - `IpidValidationRequest` - Complete API request structure (only mandatory fields)
   - `IpidValidationPayload` - Encrypted payload container
   - `IpidCreditor` - Creditor information per corridor specifications
   - `IpidCreditorAccount` - Account details per corridor specifications
   - `IpidCreditorAgent` - Bank/agent information per corridor specifications
   - `IpidValidationResponse` - API response handling with comprehensive error codes

3. **Repository Layer**
   - `TransferIpidConsentRepository` - Consent and validation status persistence
   - `IpidApiResponseRepository` - Response caching (30-day TTL calculated from creation date)

## Comprehensive Error Handling

### Error Categories

#### 1. Success Codes (2xxx)
- **2000**: Validation succeeded
- **2001**: Format validation successful  
- **2002**: Account confirmed as active

#### 2. Validation Errors (21xx)
- **2100**: Account not found - **DENIED**
- **2101**: Account flagged - **REQUIRES MANUAL REVIEW**
- **2102**: Bank/agent invalid - **DENIED**
- **2103**: Account invalid - **DENIED**
- **2104**: Name mismatch - **REQUIRES MANUAL REVIEW**
- **2105**: Format validation failed - **DENIED**
- **2106**: Invalid registration ID - **DENIED**

#### 3. Client Errors (4xx) - Configuration Issues
- **4001**: Not authorized - Check API credentials
- **4002**: Decryption error - Check encryption keys
- **4003**: Request format error - Check payload structure
- **4004**: Invalid corridor - Corridor not supported
- **4005**: Insufficient inputs - Missing required data
- **4006**: Rate limit exceeded - Implement backoff

#### 4. Server Errors (5xx) - Retryable
- **5000**: General system error
- **5001**: Internal iPiD system error
- **5002**: Banking provider system error

### Error Result Types

```php
// Different result types for proper error handling
IpidValidationResult::createSuccess($consent, $apiResponse);
IpidValidationResult::createValidationFailed($consent, $apiResponse);
IpidValidationResult::createServerError($errorMessage, $errorCode); // Retryable
IpidValidationResult::createClientError($errorMessage, $errorCode); // Not retryable
IpidValidationResult::createError($errorMessage); // Generic error
```

### Consent Status Determination

```php
// Automatic consent status based on response codes
switch ($responseCode) {
    case '2100', '2102', '2103', '2105', '2106': // Definitive failures
        return CONSENT_STATUS_DENIED;
        
    case '2101', '2104': // Require manual review/consent
        return CONSENT_STATUS_REQUIRED;
        
    case '2000', '2001', '2002': // Success codes
        // Based on match level: Strong=GRANTED, Partial/Weak=PENDING
}
```

### Manual Review Requirements

Automatic manual review is triggered for:
- Response codes 2101 (Account Flagged) and 2104 (Name Mismatch)
- Partial or weak confidence matches
- Any validation errors that aren't definitive rejections

### Validation Notes Generation

Comprehensive notes are automatically generated for each response code:

```php
// Example validation notes
"Account flagged for manual review - requires compliance verification; 
Explicit customer consent required before proceeding"

"Account not found in banking system"

"Beneficiary name does not match account holder - requires verification; 
Weak match confidence - manual review required"
```

## API Versions Support

### V1 API (validate_api.txt) - OpenPGP Encryption Only
```
POST /validation/api/v1/bank-account/validate
Headers:
  x-api-key: <api_key>
  x-customer-id: <customer_id>

Body:
{
  "encrypted_payload": "base64_encoded_pgp_encrypted_data",
  "node_id": "us-node-02",
  "clearing_system_id": "optional_routing_enhancement",
  "bic": "optional_routing_enhancement"
}
```

### V2 API (node_api.txt) - OpenPGP Encryption + JWS Signature
```
POST /api/v2/bank-account/validate
Headers:
  Authorization: Bearer <access_token>
  x-jws-signature: <jws_signature>

Body:
{
  "encrypted_payload": "base64_encoded_pgp_encrypted_data"
}
```

## Node Selection Logic (Per section 5.6)

### United States
- `us-node-01` - Analytics-based (lower coverage, name check)
- `us-node-02` - Logic-based (account structure validation) **[DEFAULT]**
- `us-node-03` - Direct-to-FI (microdeposit, no name check)

### China
- `cn-node-01` - Individual validation **[DEFAULT]**
- `cn-node-02` - Business validation

### IBAN Countries
- `iban-node-01` - Logic-based validation for all IBAN countries

### Other Countries
- `{country}-node-01` - Default node per country

## Corridor Field Requirements (Per node_api.txt section 11.6)

### United States (US)
- **Creditor**: `given_name` + `surname` (required split)
- **Account**: `account_id` (required), no IBAN
- **Agent**: `clearing_system_id` (optional for routing)
- **Identification**: `TAX_ID` type (optional)

### China (CN)
- **Creditor**: `name` in Chinese characters (preferred)
- **Account**: `account_id` in 62* PAN format
- **Agent**: `clearing_system_id` (optional)
- **Identification**: `REGISTRATION_ID` type (optional)

### IBAN Countries (BE, IT, etc.)
- **Creditor**: `name` (single field)
- **Account**: `iban` (required)
- **Agent**: `bic` (optional)
- **Identification**: `REGISTRATION_ID` type (VAT, Company No.)

### Other Countries
- Fields determined by corridor specifications
- Automatic detection of optimal field combinations

## Key Features

1. **Smart Node Selection**
   - Pure logic-based node selection per country
   - Automatic business vs individual detection for China
   - IBAN country detection and routing
   - No configuration dependencies - self-contained logic

2. **Corridor-Aware Field Mapping**
   - Only sends fields beneficial for each corridor
   - Country-specific name format handling
   - Optimal account type selection (IBAN vs account_id)

3. **Mandatory OpenPGP Encryption**
   - All payloads encrypted using iPiD's public key
   - Dynamic public key retrieval per country
   - No conditional encryption logic - always secure

4. **Centralized HTTP Communication**
   - Single IpidApiClient handles all iPiD API requests (validation and OAuth)
   - Consistent error handling, retry logic, and timeout configuration
   - No duplicate HTTP client setup across services
   - Unified logging and monitoring for all API interactions

5. **Response Caching**
   - 30-day TTL calculated from creation date
   - Request hash-based deduplication
   - Country-specific cache indexing

6. **Entity Configuration**
   - Pure XML-based ORM mapping (no Doctrine attributes)
   - Optimized database schema without unused fields
   - Proper repository inheritance from EntityRepository

7. **Simplified Service Architecture**
   - All services properly configured in XML
   - Direct dependency injection without unnecessary abstractions
   - Clean separation of concerns
   - No configuration duplication between services

8. **Comprehensive Error Handling**
   - All iPiD response codes handled appropriately
   - Specific error types for different scenarios
   - Automatic manual review triggering
   - Detailed validation notes generation

## Usage Example

```php
$result = $validationService->validateTransfer($transfer);

// Handle different result types
if ($result->isSuccessful()) {
    $consent = $result->getConsent();
    $matchLevel = $consent->getMatchLevel();
    $requiresReview = $consent->requiresManualReview();
} elseif ($result->isValidationFailed()) {
    // Business validation failed, but consent record created
    $consent = $result->getConsent();
    $notes = $consent->getValidationNotes();
} elseif ($result->isServerError() && $result->isRetryable()) {
    // Retry logic for server errors
    $this->scheduleRetry($transfer);
} elseif ($result->isClientError()) {
    // Configuration issue - investigate and fix
    $this->alertOperations($result->getErrorMessage());
}
```

## Configuration

```yaml
# config/packages/ipid.yaml
# All configuration values are passed directly to services that need them
parameters:
    env(IPID_API_ENDPOINT): 'https://api.ipid.live'
    env(IPID_API_KEY): 'your-api-key'
    env(IPID_CUSTOMER_ID): 'your-customer-id'
    env(IPID_CLIENT_PRIVATE_KEY): 'your-private-key'  # Required for OpenPGP encryption
    env(IPID_API_TIMEOUT): 30
    env(IPID_API_RETRY_ATTEMPTS): 3
```

**Note**: Configuration values are injected directly into services that need them rather than using a configuration wrapper class. This keeps the architecture simple and reduces unnecessary abstractions.

## Testing

The bundle includes test coverage for:
- **Node selection logic** for all supported countries
- **Corridor-specific field requirements** per node_api.txt section 11.6
- **API flow**: node selection → public key retrieval → encryption → validation
- **Response handling**: All API response codes (2xxx, 4xxx, 5xxx)
- **Response structure**: Proper parsing of response_code, response_message, and data fields
- **Match level determination**: Strong/Partial/Weak classification based on score
- **Caching logic**: Hash-based deduplication and TTL expiration
- **Database persistence**: Proper storage of response codes and messages
- **Error scenarios**: Network failures, invalid responses, expired cache
- **V1 vs V2 API version handling**
- **Mock responses** matching exact API specification format

### Sample Test Cases

```php
// Test response code handling
public function testResponseCodeHandling(): void
{
    $responseData = [
        'response_code' => '2000',
        'response_message' => 'ValidationSucceeded',
        'data' => [
            'match_score' => 0.95,
            'match_score_description' => 'Strong Match'
        ]
    ];
    
    $response = $this->denormalizer->denormalize($responseData);
    
    $this->assertTrue($response->isSuccessful());
    $this->assertEquals('Strong', $response->getMatchLevel());
    $this->assertFalse($response->getRequiresConsent());
}

// Test caching behavior
public function testCachingBehavior(): void
{
    $transfer = $this->createMockTransfer();
    
    // First call - hits API
    $result1 = $this->validationService->validateTransfer($transfer);
    
    // Second call - uses cache
    $result2 = $this->validationService->validateTransfer($transfer);
    
    $this->assertEquals($result1->getConsent()->getId(), $result2->getConsent()->getId());
    $this->assertSame(1, $this->apiClient->getCallCount()); // Only one API call
}
```

## Supported Countries

Based on section 5.4 and node_api.txt corridor specifications:
- **US**: Multiple node options with different validation methods
- **CN**: Individual vs business validation nodes
- **IBAN Countries**: BE, IT, FR, DE, NL, etc. (67 countries total)
- **Other Countries**: IN, ID, VN, NG, NP, PK, UK/GB, KR, BD, UG, BR, MX, AR, UY, PE, MY, TR, ZA, PL, CL, CO, EC, SA

Each country follows specific corridor requirements for optimal validation results. 

# iPiD Country Configuration Bundle

## Overview

This bundle provides a simple configuration system to enable/disable countries for iPiD bank account validation.

## Configuration

### Enable/Disable Countries via YAML

Edit `config/packages/evp_ipid.yaml`:

```yaml
evp_ipid:
  validation:
    # Specific countries to enable
    enabled_countries: []
    
    # Regions to enable (currently: EU only)
    enabled_regions:
      - 'EU'
```

### Examples

**Enable specific countries:**
```yaml
enabled_countries: ['US', 'GB', 'CH']
enabled_regions: ['EU']
```

**Enable entire regions:**
```yaml
enabled_countries: []
enabled_regions: ['EU', 'UK', 'NORTH_AMERICA']
```

### Available Regions

- `EU` - European Union (VOP scheme)
- `UK` - United Kingdom (COP scheme)
- `NORTH_AMERICA` - US, CA, MX
- `SOUTH_AMERICA` - BR, AR, CL, CO, PE, UY, EC
- `ASIA_PACIFIC` - CN, IN, ID, VN, MY, KR, BD, NP, PK
- `AFRICA` - NG, UG, ZA
- `MIDDLE_EAST` - SA, TR
- `IBAN_COUNTRIES` - All IBAN supporting countries

## Services

### `evp_ipid.country_checker`

Simple service to check if countries are enabled:

```php
// In your validation service
public function __construct(CountryValidationChecker $countryChecker) 
{
    $this->countryChecker = $countryChecker;
}

public function validateTransfer($transfer): bool 
{
    $country = $this->extractCountry($transfer);
    
    if (!$this->countryChecker->isCountryEnabled($country)) {
        return false; // Skip iPiD validation
    }
    
    // Continue with iPiD validation...
}
```

### Management Command

View current configuration:

```bash
php app/console evp:ipid:countries list
php app/console evp:ipid:countries check --country=DE
php app/console evp:ipid:countries regions --region=EU
```

## Current Setup

- **Default**: EU countries only (VOP validation)
- **To add US**: Add `'US'` to `enabled_countries`
- **To add UK**: Add `'UK'` to `enabled_regions`
- **To add all North America**: Add `'NORTH_AMERICA'` to `enabled_regions`

## Integration

The bundle is designed to work alongside existing iPiD validation services. Use the `CountryValidationChecker` service in your existing validation logic to check if a country should be validated.
