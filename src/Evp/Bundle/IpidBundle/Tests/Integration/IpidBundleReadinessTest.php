<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Integration;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use PHPUnit\Framework\TestCase;

/**
 * Integration test to verify the iPiD bundle is properly configured and ready for API calls
 */
final class IpidBundleReadinessTest extends TestCase
{
    public function testRequiredEnvironmentVariables(): void
    {
        $requiredEnvVars = [
            'IPID_API_ENDPOINT',
            'IPID_API_KEY',
            'IPID_CUSTOMER_ID'
        ];

        foreach ($requiredEnvVars as $envVar) {
            $value = $_ENV[$envVar] ?? getenv($envVar);

            // Check they're not empty and not placeholder values
            self::assertNotEmpty($value, "Environment variable {$envVar} must be set");
            self::assertNotEquals('your-' . strtolower(str_replace('IPID_', '', $envVar)), $value,
                "Environment variable {$envVar} appears to be a placeholder value");
        }
    }

    public function testApiEndpointFormat(): void
    {
        $endpoint = $_ENV['IPID_API_ENDPOINT'] ?? getenv('IPID_API_ENDPOINT');
        
        if ($endpoint) {
            self::assertTrue(
                filter_var($endpoint, FILTER_VALIDATE_URL) !== false,
                'IPID_API_ENDPOINT must be a valid URL'
            );
            
            self::assertTrue(
                str_starts_with($endpoint, 'https://'),
                'IPID_API_ENDPOINT must use HTTPS'
            );
        }
    }

    public function testServiceInstantiation(): void
    {
        // Test that we can create the main services without errors
        
        // Mock dependencies for testing
        $httpClient = $this->createMock(\GuzzleHttp\Client::class);
        $responseDenormalizer = $this->createMock(\Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer::class);
        $logger = $this->createMock(\Psr\Log\LoggerInterface::class);
        $encryptionService = $this->createMock(\Evp\Bundle\IpidBundle\Service\IpidEncryptionService::class);
        
        // Test IpidApiClient instantiation
        $apiClient = new IpidApiClient(
            $httpClient,
            $responseDenormalizer,
            'https://api.ipid.live',
            'test-api-key',
            'test-customer-id',
            $logger
        );
        
        self::assertInstanceOf(IpidApiClient::class, $apiClient);
        
        // Test IpidRequestBuilder instantiation
        $requestBuilder = new IpidRequestBuilder(
            $encryptionService,
            $apiClient
        );
        
        self::assertInstanceOf(IpidRequestBuilder::class, $requestBuilder);
    }

    public function testRequestBuilderBasicFunctionality(): void
    {
        // Create mocked dependencies
        $encryptionService = $this->createMock(\Evp\Bundle\IpidBundle\Service\IpidEncryptionService::class);
        $apiClient = $this->createMock(IpidApiClient::class);
        
        $requestBuilder = new IpidRequestBuilder($encryptionService, $apiClient);
        
        // Create a mock transfer
        $transfer = $this->createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);
        
        $beneficiary = $this->createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn('John Smith');
        
        $transfer->method('getBeneficiary')->willReturn($beneficiary);
        
        // Mock API client response
        $apiClient->method('getPublicKey')->willReturn([
            'node_public_key' => 'test-public-key'
        ]);
        
        // Mock encryption service
        $encryptionService->method('encrypt')->willReturn('encrypted-payload');
        
        // Test that buildRequest works without throwing exceptions
        $result = $requestBuilder->buildRequest($transfer);
        
        self::assertInstanceOf(\Evp\Bundle\IpidBundle\DTO\IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-payload', $result->getEncryptedPayload());
    }

    public function testCountryExtractionLogic(): void
    {
        $encryptionService = $this->createMock(\Evp\Bundle\IpidBundle\Service\IpidEncryptionService::class);
        $apiClient = $this->createMock(IpidApiClient::class);
        
        $requestBuilder = new IpidRequestBuilder($encryptionService, $apiClient);
        
        // Test IBAN country extraction
        $transfer = $this->createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);
        
        $beneficiary = $this->createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************'); // German IBAN
        $beneficiary->method('getName')->willReturn('Hans Mueller');
        
        $transfer->method('getBeneficiary')->willReturn($beneficiary);
        
        // Verify that the correct country (DE) is extracted and used for API call
        $apiClient->expects(self::once())
            ->method('getPublicKey')
            ->with('DE')
            ->willReturn(['node_public_key' => 'test-key']);
        
        $encryptionService->method('encrypt')->willReturn('encrypted');
        
        $requestBuilder->buildRequest($transfer);
    }

    public function testValidationErrorHandling(): void
    {
        $encryptionService = $this->createMock(\Evp\Bundle\IpidBundle\Service\IpidEncryptionService::class);
        $apiClient = $this->createMock(IpidApiClient::class);
        
        $requestBuilder = new IpidRequestBuilder($encryptionService, $apiClient);
        
        // Test missing beneficiary name
        $transfer = $this->createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);
        
        $beneficiary = $this->createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn(null); // Missing name
        $beneficiary->method('getDisplayName')->willReturn(null);
        
        $transfer->method('getBeneficiary')->willReturn($beneficiary);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary name is required for iPiD validation');
        
        $requestBuilder->buildRequest($transfer);
    }

    public function testApiClientConfiguration(): void
    {
        // Test that API client can be configured with real environment values
        $endpoint = $_ENV['IPID_API_ENDPOINT'] ?? getenv('IPID_API_ENDPOINT') ?? 'https://api.ipid.live';
        $apiKey = $_ENV['IPID_API_KEY'] ?? getenv('IPID_API_KEY') ?? 'test-key';
        $customerId = $_ENV['IPID_CUSTOMER_ID'] ?? getenv('IPID_CUSTOMER_ID') ?? 'test-customer';
        
        $httpClient = new \GuzzleHttp\Client();
        $responseDenormalizer = $this->createMock(\Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer::class);
        $logger = $this->createMock(\Psr\Log\LoggerInterface::class);
        
        $apiClient = new IpidApiClient(
            $httpClient,
            $responseDenormalizer,
            $endpoint,
            $apiKey,
            $customerId,
            $logger
        );
        
        self::assertInstanceOf(IpidApiClient::class, $apiClient);
        
        // Test that the client can make a request structure (without actually calling API)
        $reflection = new \ReflectionClass($apiClient);
        $buildDefaultHeadersMethod = $reflection->getMethod('buildDefaultHeaders');
        $buildDefaultHeadersMethod->setAccessible(true);
        
        $headers = $buildDefaultHeadersMethod->invoke($apiClient);
        
        self::assertArrayHasKey('x-api-key', $headers);
        self::assertArrayHasKey('x-customer-id', $headers);
        self::assertArrayHasKey('Content-Type', $headers);
        self::assertEquals('application/json', $headers['Content-Type']);
    }

    public function testBundleReadinessChecklist(): void
    {
        $readinessChecks = [
            'Environment variables configured' => function() {
                $required = ['IPID_API_ENDPOINT', 'IPID_API_KEY', 'IPID_CUSTOMER_ID'];
                foreach ($required as $var) {
                    if (empty($_ENV[$var] ?? getenv($var))) {
                        return false;
                    }
                }
                return true;
            },
            'API endpoint is valid URL' => function() {
                $endpoint = $_ENV['IPID_API_ENDPOINT'] ?? getenv('IPID_API_ENDPOINT');
                return $endpoint && filter_var($endpoint, FILTER_VALIDATE_URL) !== false;
            },
            'Services can be instantiated' => function() {
                try {
                    $httpClient = new \GuzzleHttp\Client();
                    $responseDenormalizer = $this->createMock(\Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer::class);
                    $logger = $this->createMock(\Psr\Log\LoggerInterface::class);
                    $encryptionService = $this->createMock(\Evp\Bundle\IpidBundle\Service\IpidEncryptionService::class);
                    
                    new IpidApiClient($httpClient, $responseDenormalizer, 'https://test.com', 'key', 'customer', $logger);
                    new IpidRequestBuilder($encryptionService, $this->createMock(IpidApiClient::class));
                    
                    return true;
                } catch (\Exception $e) {
                    return false;
                }
            }
        ];
        
        $results = [];
        foreach ($readinessChecks as $check => $callable) {
            $results[$check] = $callable();
        }
        
        // Output readiness status
        foreach ($results as $check => $passed) {
            if (!$passed) {
                self::fail("Readiness check failed: {$check}");
            }
        }
        
        self::assertTrue(true, 'All readiness checks passed - iPiD bundle is ready for API calls');
    }
}
