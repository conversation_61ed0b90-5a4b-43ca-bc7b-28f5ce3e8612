<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use PHPUnit\Framework\TestCase;

final class IpidTransferHelperTest extends TestCase
{
    private IpidTransferHelper $helper;

    protected function setUp(): void
    {
        $this->helper = new IpidTransferHelper();
    }

    public function testExtractCountryFromTransferWithValidIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary
            ->expects(self::once())
            ->method('getIban')
            ->willReturn('**********************')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->extractCountryFromTransfer($transfer);

        self::assertEquals('GB', $result);
    }

    public function testExtractCountryFromTransferWithShortIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary
            ->expects(self::once())
            ->method('getIban')
            ->willReturn('DE')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->extractCountryFromTransfer($transfer);

        self::assertEquals('DE', $result);
    }

    public function testExtractCountryFromTransferWithBankPartyAndValidBic(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBic')
            ->willReturn('DEUTDEFF500')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->extractCountryFromTransfer($transfer);

        self::assertEquals('DE', $result);
    }

    public function testExtractCountryFromTransferWithInvalidBic(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBic')
            ->willReturn('ABC')  // Too short to be a valid BIC
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine country from transfer beneficiary data.');

        $this->helper->extractCountryFromTransfer($transfer);
    }

    public function testExtractCountryFromTransferWithoutValidData(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBic')
            ->willReturn('')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine country from transfer beneficiary data.');

        $this->helper->extractCountryFromTransfer($transfer);
    }

    public function testGetBeneficiaryAccountWithPartyIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary
            ->expects(self::once())
            ->method('getIban')
            ->willReturn('**********************')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryAccount($transfer);

        self::assertEquals('**********************', $result);
    }

    public function testGetBeneficiaryAccountWithPartyBank(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getDisplayAccount')
            ->willReturn('********')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryAccount($transfer);

        self::assertEquals('********', $result);
    }

    public function testGetBeneficiaryAccountWithNoBeneficiary(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn(null)
        ;

        $result = $this->helper->getBeneficiaryAccount($transfer);

        self::assertNull($result);
    }

    /**
     * @dataProvider beneficiaryNameDataProvider
     */
    public function testGetBeneficiaryName(string $method, string $expectedName): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = $this->createBeneficiaryWithMethod($method, $expectedName);

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryName($transfer);

        self::assertEquals($expectedName, $result);
    }

    public function beneficiaryNameDataProvider(): array
    {
        return [
            'getName method' => ['getName', 'John Smith'],
            'getDisplayName method' => ['getDisplayName', 'Jane Doe Company'],
        ];
    }

    public function testGetBeneficiaryNameWithNoMethod(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = new \stdClass();

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryName($transfer);

        self::assertNull($result);
    }

    public function testGetBeneficiaryBankBic(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBic')
            ->willReturn('DEUTDEFF500')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryBankBic($transfer);

        self::assertEquals('DEUTDEFF500', $result);
    }

    public function testGetBeneficiaryBankBicWithNonBankParty(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryBankBic($transfer);

        self::assertNull($result);
    }

    public function testGetBeneficiarySortCode(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBankCode')
            ->willReturn('12-34-56')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiarySortCode($transfer);

        self::assertEquals('12-34-56', $result);
    }

    public function testGetBeneficiaryBankName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary
            ->expects(self::once())
            ->method('getBankName')
            ->willReturn('Test Bank Ltd')
        ;

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryBankName($transfer);

        self::assertEquals('Test Bank Ltd', $result);
    }

    /**
     * @dataProvider personCodeDataProvider
     */
    public function testExtractPersonCodeFromTransfer(
        array $mockSetup,
        ?string $expectedResult
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);

        foreach ($mockSetup as $method => $returnValue) {
            $beneficiary
                ->method($method)
                ->willReturn($returnValue)
            ;
        }

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->extractPersonCodeFromTransfer($transfer);

        self::assertEquals($expectedResult, $result);
    }

    public function personCodeDataProvider(): array
    {
        return [
            'INN code available' => [
                'mockSetup' => ['getInnCode' => '**********'],
                'expectedResult' => '**********',
            ],
            'KPP code available when INN is null' => [
                'mockSetup' => ['getInnCode' => null, 'getKppCode' => '*********'],
                'expectedResult' => '*********',
            ],
            'No codes available' => [
                'mockSetup' => ['getInnCode' => null, 'getKppCode' => null],
                'expectedResult' => null,
            ],
        ];
    }

    public function testExtractPersonCodeFromTransferWithCodeMethod(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = $this->createBeneficiaryWithMethod('getCode', 'PERSON123');

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->extractPersonCodeFromTransfer($transfer);

        self::assertEquals('PERSON123', $result);
    }

    public function testGetCurrencyFromAmountMoney(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $amountMoney = $this->createAmountMoneyMockWithMethod('getCurrency', 'EUR');

        $transfer
            ->expects(self::once())
            ->method('getAmountMoney')
            ->willReturn($amountMoney)
        ;

        $result = $this->helper->getCurrency($transfer);

        self::assertEquals('EUR', $result);
    }

    public function testGetCurrencyFromTransferMethod(): void
    {
        $transfer = $this->createMockWithMethods(TransferOut::class, [
            'getAmountMoney' => null,
            'getAmountCurrency' => 'USD'
        ]);

        $result = $this->helper->getCurrency($transfer);

        self::assertEquals('USD', $result);
    }

    public function testGetCurrencyWithNoAvailableMethod(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::once())
            ->method('getAmountMoney')
            ->willReturn(null)
        ;

        $result = $this->helper->getCurrency($transfer);

        self::assertNull($result);
    }

    /**
     * @dataProvider beneficiaryTypeDataProvider
     */
    public function testGetBeneficiaryType(
        array $mockSetup,
        string $expectedType
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);

        foreach ($mockSetup as $method => $returnValue) {
            $beneficiary
                ->method($method)
                ->willReturn($returnValue)
            ;
        }

        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn($beneficiary)
        ;

        $result = $this->helper->getBeneficiaryType($transfer);

        self::assertEquals($expectedType, $result);
    }

    public function beneficiaryTypeDataProvider(): array
    {
        return [
            'Business with INN code' => [
                'mockSetup' => ['getInnCode' => '**********', 'getKppCode' => null],
                'expectedType' => 'business',
            ],
            'Business with KPP code' => [
                'mockSetup' => ['getInnCode' => null, 'getKppCode' => '*********'],
                'expectedType' => 'business',
            ],
            'Individual without codes' => [
                'mockSetup' => ['getInnCode' => null, 'getKppCode' => null],
                'expectedType' => 'individual',
            ],
        ];
    }

    public function testGetBeneficiaryTypeWithNoBeneficiary(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer
            ->expects(self::once())
            ->method('getBeneficiary')
            ->willReturn(null)
        ;

        $result = $this->helper->getBeneficiaryType($transfer);

        self::assertNull($result);
    }

    public function testGenerateRequestHash(): void
    {
        $requestData = [
            'account' => '********',
            'name' => 'John Smith',
            'country' => 'GB',
        ];

        $result = $this->helper->generateRequestHash($requestData);

        self::assertIsString($result);
        self::assertEquals(64, strlen($result)); // SHA256 hash length
        
        // Test consistency
        $secondResult = $this->helper->generateRequestHash($requestData);
        self::assertEquals($result, $secondResult);
    }

    public function testGenerateRequestHashWithDifferentOrder(): void
    {
        $requestData1 = [
            'account' => '********',
            'name' => 'John Smith',
            'country' => 'GB',
        ];

        $requestData2 = [
            'country' => 'GB',
            'name' => 'John Smith',
            'account' => '********',
        ];

        $hash1 = $this->helper->generateRequestHash($requestData1);
        $hash2 = $this->helper->generateRequestHash($requestData2);

        self::assertEquals($hash1, $hash2, 'Hash should be consistent regardless of array key order');
    }

    public function testGenerateRequestHashWithDifferentData(): void
    {
        $requestData1 = ['account' => '********'];
        $requestData2 = ['account' => '********'];

        $hash1 = $this->helper->generateRequestHash($requestData1);
        $hash2 = $this->helper->generateRequestHash($requestData2);

        self::assertNotEquals($hash1, $hash2, 'Different data should produce different hashes');
    }

    private function createBeneficiaryWithMethod(string $methodName, $returnValue)
    {
        // Create actual test classes with the required methods
        switch ($methodName) {
            case 'getName':
                return new class($returnValue) {
                    private $name;
                    public function __construct($name) { $this->name = $name; }
                    public function getName() { return $this->name; }
                };
            case 'getDisplayName':
                return new class($returnValue) {
                    private $displayName;
                    public function __construct($displayName) { $this->displayName = $displayName; }
                    public function getDisplayName() { return $this->displayName; }
                };
            case 'getCode':
                return new class($returnValue) {
                    private $code;
                    public function __construct($code) { $this->code = $code; }
                    public function getCode() { return $this->code; }
                };
            default:
                throw new \InvalidArgumentException("Unknown method: $methodName");
        }
    }

    private function createMockWithMethod(string $methodName, $returnValue)
    {
        $mock = $this->createMock(\stdClass::class);
        $mock->method($methodName)->willReturn($returnValue);
        return $mock;
    }

    private function createBeneficiaryMockWithMethod(string $methodName, $returnValue)
    {
        // This method is no longer needed, but keeping for backward compatibility
        return $this->createBeneficiaryWithMethod($methodName, $returnValue);
    }

    private function createAmountMoneyMockWithMethod(string $methodName, $returnValue)
    {
        // Create an anonymous class that has the method
        $mockClass = new class {
            private $methods = [];
            
            public function addMethod(string $name, $value)
            {
                $this->methods[$name] = $value;
            }
            
            public function __call(string $name, array $arguments)
            {
                return $this->methods[$name] ?? null;
            }
        };
        
        $mockClass->addMethod($methodName, $returnValue);
        return $mockClass;
    }

    private function createMockWithMethods(string $className, array $methods)
    {
        $mock = $this->createMock($className);
        foreach ($methods as $method => $returnValue) {
            $mock->method($method)->willReturn($returnValue);
        }
        return $mock;
    }
} 