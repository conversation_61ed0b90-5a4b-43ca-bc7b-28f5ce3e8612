<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use PHPUnit\Framework\TestCase;

final class IpidRequestBuilderBasicTest extends TestCase
{
    private IpidRequestBuilder $requestBuilder;

    protected function setUp(): void
    {
        $this->requestBuilder = new IpidRequestBuilder();
    }

    public function testBuildRequestWithIbanAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        // Create IBAN beneficiary mock
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn('John Smith');

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);

        // Verify JSON payload contains expected data
        $jsonPayload = $result->getJsonPayload();
        $decodedPayload = json_decode($jsonPayload, true);

        self::assertEquals('GB', $decodedPayload['country_code']);
        self::assertEquals('John Smith', $decodedPayload['creditor']['name']);
        self::assertEquals('**********************', $decodedPayload['creditor_account']['iban']);

        // Node ID should be null initially (set by API client if needed)
        self::assertNull($result->getNodeId());
    }

    public function testBuildRequestWithBankAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(54321);

        // Create PartyBank beneficiary mock
        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary->method('getDisplayAccount')->willReturn('*********');
        $beneficiary->method('getName')->willReturn('Jane Doe');
        $beneficiary->method('getBic')->willReturn('CHASUS33'); // US BIC for country extraction
        $beneficiary->method('getBankCode')->willReturn('*********'); // Sort code

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);

        // Verify JSON payload contains expected data
        $jsonPayload = $result->getJsonPayload();
        $decodedPayload = json_decode($jsonPayload, true);

        self::assertEquals('US', $decodedPayload['country_code']);
        self::assertEquals('Jane Doe', $decodedPayload['creditor']['name']);
        self::assertEquals('*********', $decodedPayload['creditor_account']['account_id']);
        self::assertEquals('*********', $decodedPayload['creditor_agent']['clearing_system_id']);

        self::assertNull($result->getNodeId());
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(99999);

        // Create beneficiary that returns null for name
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn(null);

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary must have a name');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(88888);

        // Create beneficiary with name but no account
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn(null); // No IBAN
        $beneficiary->method('getName')->willReturn('John Smith');

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('IBAN beneficiary must have an IBAN');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testCountryExtractionFromIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn('Hans Mueller');

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);

        // Verify country extraction from IBAN
        $jsonPayload = $result->getJsonPayload();
        $decodedPayload = json_decode($jsonPayload, true);

        self::assertEquals('DE', $decodedPayload['country_code']);
        self::assertEquals('Hans Mueller', $decodedPayload['creditor']['name']);
        self::assertEquals('**********************', $decodedPayload['creditor_account']['iban']);
    }

    public function testCountryExtractionFromBic(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $beneficiary = self::createMock(PartyBank::class);
        $beneficiary->method('getDisplayAccount')->willReturn('*********');
        $beneficiary->method('getName')->willReturn('Test User');
        $beneficiary->method('getBic')->willReturn('CHASUS33'); // US BIC
        $beneficiary->method('getBankCode')->willReturn('*********');

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);

        // Verify country extraction from BIC
        $jsonPayload = $result->getJsonPayload();
        $decodedPayload = json_decode($jsonPayload, true);

        self::assertEquals('US', $decodedPayload['country_code']);
        self::assertEquals('Test User', $decodedPayload['creditor']['name']);
        self::assertEquals('*********', $decodedPayload['creditor_account']['account_id']);
    }

    public function testBuildRequestWithComplexScenarios(): void
    {
        // Test EU VOP scenario
        $euTransfer = self::createMock(TransferOut::class);
        $euBeneficiary = self::createMock(PartyIban::class);
        $euBeneficiary->method('getIban')->willReturn('***************************');
        $euBeneficiary->method('getName')->willReturn('Pierre Dupont');
        $euTransfer->method('getBeneficiary')->willReturn($euBeneficiary);

        $euResult = $this->requestBuilder->buildRequest($euTransfer);
        $euPayload = json_decode($euResult->getJsonPayload(), true);

        self::assertEquals('FR', $euPayload['country_code']);
        self::assertEquals('Pierre Dupont', $euPayload['creditor']['name']);
        self::assertEquals('***************************', $euPayload['creditor_account']['iban']);

        // Test UK COP scenario
        $ukTransfer = self::createMock(TransferOut::class);
        $ukBeneficiary = self::createMock(PartyBank::class);
        $ukBeneficiary->method('getDisplayAccount')->willReturn('********');
        $ukBeneficiary->method('getName')->willReturn('Sherlock Holmes');
        $ukBeneficiary->method('getBic')->willReturn('WESTGB2L');
        $ukBeneficiary->method('getBankCode')->willReturn('112233');
        $ukTransfer->method('getBeneficiary')->willReturn($ukBeneficiary);

        $ukResult = $this->requestBuilder->buildRequest($ukTransfer);
        $ukPayload = json_decode($ukResult->getJsonPayload(), true);

        self::assertEquals('GB', $ukPayload['country_code']);
        self::assertEquals('Sherlock Holmes', $ukPayload['creditor']['name']);
        self::assertEquals('********', $ukPayload['creditor_account']['account_id']);
        self::assertEquals('112233', $ukPayload['creditor_agent']['clearing_system_id']);
    }
}
