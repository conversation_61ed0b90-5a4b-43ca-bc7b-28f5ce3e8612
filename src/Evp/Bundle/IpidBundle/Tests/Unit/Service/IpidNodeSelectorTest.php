<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Service\IpidNodeSelector;
use PHPUnit\Framework\TestCase;

final class IpidNodeSelectorTest extends TestCase
{
    private IpidNodeSelector $nodeSelector;

    protected function setUp(): void
    {
        $this->nodeSelector = new IpidNodeSelector();
    }

    public function testConstants(): void
    {
        self::assertEquals('us-node-01', IpidNodeSelector::US_ANALYTICS_NODE);
        self::assertEquals('us-node-02', IpidNodeSelector::US_LOGIC_NODE);
        self::assertEquals('us-node-03', IpidNodeSelector::US_DIRECT_FI_NODE);
        self::assertEquals('cn-node-01', IpidNodeSelector::CN_INDIVIDUAL_NODE);
        self::assertEquals('cn-node-02', IpidNodeSelector::CN_BUSINESS_NODE);
        self::assertEquals('iban-node-01', IpidNodeSelector::IBAN_LOGIC_NODE);
    }

    /**
     * @dataProvider chinaNodeSelectionDataProvider
     */
    public function testChinaNodeSelection(
        ?string $identityType,
        string $expectedNode
    ): void {
        $result = $this->nodeSelector->getNodeIdForCountry('CN', $identityType);

        self::assertEquals($expectedNode, $result);
    }

    public function chinaNodeSelectionDataProvider(): array
    {
        return [
            'Business identity type' => ['business', IpidNodeSelector::CN_BUSINESS_NODE],
            'Corporate identity type' => ['corporate', IpidNodeSelector::CN_BUSINESS_NODE],
            'Individual identity type' => ['individual', IpidNodeSelector::CN_INDIVIDUAL_NODE],
            'No identity type specified' => [null, IpidNodeSelector::CN_INDIVIDUAL_NODE],
            'Unknown identity type defaults to individual' => ['unknown', IpidNodeSelector::CN_INDIVIDUAL_NODE],
        ];
    }

    /**
     * @dataProvider chinaCountryCodeCaseDataProvider
     */
    public function testChinaCountryCodeCaseInsensitive(string $countryCode): void
    {
        $businessResult = $this->nodeSelector->getNodeIdForCountry($countryCode, 'business');
        $individualResult = $this->nodeSelector->getNodeIdForCountry($countryCode, 'individual');

        self::assertEquals(IpidNodeSelector::CN_BUSINESS_NODE, $businessResult);
        self::assertEquals(IpidNodeSelector::CN_INDIVIDUAL_NODE, $individualResult);
    }

    public function chinaCountryCodeCaseDataProvider(): array
    {
        return [
            'Uppercase CN' => ['CN'],
            'Lowercase cn' => ['cn'],
            'Mixed case Cn' => ['Cn'],
            'Mixed case cN' => ['cN'],
        ];
    }

    /**
     * @dataProvider usNodeSelectionDataProvider
     */
    public function testUsNodeSelection(
        string $countryCode,
        ?string $identityType,
        string $expectedNode
    ): void {
        $result = $this->nodeSelector->getNodeIdForCountry($countryCode, $identityType);

        self::assertEquals($expectedNode, $result);
    }

    public function usNodeSelectionDataProvider(): array
    {
        return [
            'US with business identity' => ['US', 'business', IpidNodeSelector::US_LOGIC_NODE],
            'US with individual identity' => ['US', 'individual', IpidNodeSelector::US_LOGIC_NODE],
            'US with no identity type' => ['US', null, IpidNodeSelector::US_LOGIC_NODE],
            'us lowercase with business' => ['us', 'business', IpidNodeSelector::US_LOGIC_NODE],
            'Us mixed case with individual' => ['Us', 'individual', IpidNodeSelector::US_LOGIC_NODE],
        ];
    }

    /**
     * @dataProvider defaultNodeSelectionDataProvider
     */
    public function testDefaultNodeSelection(
        string $countryCode,
        ?string $identityType
    ): void {
        $result = $this->nodeSelector->getNodeIdForCountry($countryCode, $identityType);

        self::assertEquals(IpidNodeSelector::IBAN_LOGIC_NODE, $result);
    }

    public function defaultNodeSelectionDataProvider(): array
    {
        return [
            'GB with individual' => ['GB', 'individual'],
            'GB with business' => ['GB', 'business'],
            'GB with no identity' => ['GB', null],
            'DE with individual' => ['DE', 'individual'],
            'DE with business' => ['DE', 'business'],
            'FR with individual' => ['FR', 'individual'],
            'IT with business' => ['IT', 'business'],
            'ES with individual' => ['ES', 'individual'],
            'NL with business' => ['NL', 'business'],
            'BR with individual' => ['BR', 'individual'],
            'MX with business' => ['MX', 'business'],
            'AR with individual' => ['AR', 'individual'],
            'IN with business' => ['IN', 'business'],
            'Unknown country XX' => ['XX', 'individual'],
        ];
    }

    /**
     * @dataProvider comprehensiveNodeRoutingDataProvider
     */
    public function testComprehensiveNodeRouting(
        string $scenario,
        string $countryCode,
        ?string $identityType,
        string $expectedNode
    ): void {
        $result = $this->nodeSelector->getNodeIdForCountry($countryCode, $identityType);

        self::assertEquals($expectedNode, $result, "Failed for scenario: $scenario");
    }

    public function comprehensiveNodeRoutingDataProvider(): array
    {
        return [
            // China scenarios
            'China individual payment' => ['China individual payment', 'CN', 'individual', IpidNodeSelector::CN_INDIVIDUAL_NODE],
            'China business payment' => ['China business payment', 'CN', 'business', IpidNodeSelector::CN_BUSINESS_NODE],
            'China corporate payment' => ['China corporate payment', 'CN', 'corporate', IpidNodeSelector::CN_BUSINESS_NODE],
            'China payment without identity type' => ['China payment without identity type', 'CN', null, IpidNodeSelector::CN_INDIVIDUAL_NODE],

            // US scenarios
            'US individual ACH payment' => ['US individual ACH payment', 'US', 'individual', IpidNodeSelector::US_LOGIC_NODE],
            'US business wire transfer' => ['US business wire transfer', 'US', 'business', IpidNodeSelector::US_LOGIC_NODE],
            'US payment without identity type' => ['US payment without identity type', 'US', null, IpidNodeSelector::US_LOGIC_NODE],

            // IBAN countries scenarios
            'SEPA payment to Germany' => ['SEPA payment to Germany', 'DE', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],
            'UK Faster Payment' => ['UK Faster Payment', 'GB', 'business', IpidNodeSelector::IBAN_LOGIC_NODE],
            'French SEPA transfer' => ['French SEPA transfer', 'FR', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],
            'Italian business payment' => ['Italian business payment', 'IT', 'business', IpidNodeSelector::IBAN_LOGIC_NODE],

            // Latin America scenarios
            'Brazilian PIX payment' => ['Brazilian PIX payment', 'BR', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],
            'Mexican SPEI transfer' => ['Mexican SPEI transfer', 'MX', 'business', IpidNodeSelector::IBAN_LOGIC_NODE],
            'Argentine payment' => ['Argentine payment', 'AR', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],

            // Other regions
            'Indian UPI payment' => ['Indian UPI payment', 'IN', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],
            'Singapore payment' => ['Singapore payment', 'SG', 'business', IpidNodeSelector::IBAN_LOGIC_NODE],
            'Australian payment' => ['Australian payment', 'AU', 'individual', IpidNodeSelector::IBAN_LOGIC_NODE],
        ];
    }

    public function testAllCountryCodesCaseInsensitive(): void
    {
        $testCases = [
            ['cn', 'CN'],
            ['us', 'US'],
            ['gb', 'GB'],
            ['de', 'DE'],
        ];

        foreach ($testCases as [$lower, $upper]) {
            $lowerResult = $this->nodeSelector->getNodeIdForCountry($lower, 'individual');
            $upperResult = $this->nodeSelector->getNodeIdForCountry($upper, 'individual');

            self::assertEquals(
                $upperResult,
                $lowerResult,
                "Case insensitivity failed for country codes: $lower vs $upper"
            );
        }
    }

    public function testNodeSelectionConsistency(): void
    {
        // Test that the same inputs always return the same node
        $testCases = [
            ['CN', 'business'],
            ['CN', 'individual'],
            ['US', 'business'],
            ['US', null],
            ['GB', 'individual'],
            ['DE', 'business'],
        ];

        foreach ($testCases as [$country, $identityType]) {
            $firstCall = $this->nodeSelector->getNodeIdForCountry($country, $identityType);
            $secondCall = $this->nodeSelector->getNodeIdForCountry($country, $identityType);

            self::assertEquals(
                $firstCall,
                $secondCall,
                "Node selection inconsistent for country: $country, identity: $identityType"
            );
        }
    }

    public function testNodeSelectionWithEdgeCases(): void
    {
        // Test with empty string
        $result = $this->nodeSelector->getNodeIdForCountry('', null);
        self::assertEquals(IpidNodeSelector::IBAN_LOGIC_NODE, $result);

        // Test with whitespace
        $result = $this->nodeSelector->getNodeIdForCountry(' GB ', 'individual');
        self::assertEquals(IpidNodeSelector::IBAN_LOGIC_NODE, $result);

        // Test with numeric country code (should not match special cases)
        $result = $this->nodeSelector->getNodeIdForCountry('123', 'business');
        self::assertEquals(IpidNodeSelector::IBAN_LOGIC_NODE, $result);
    }

    public function testSpecialIdentityTypesForChina(): void
    {
        // Test various business-related identity types (case-sensitive)
        $businessTypes = ['business', 'corporate'];
        
        foreach ($businessTypes as $identityType) {
            $result = $this->nodeSelector->getNodeIdForCountry('CN', $identityType);
            self::assertEquals(
                IpidNodeSelector::CN_BUSINESS_NODE,
                $result,
                "Failed for business identity type: $identityType"
            );
        }

        // Test non-business identity types default to individual node
        $individualTypes = ['individual', 'personal', 'consumer', 'private', null, '', 'BUSINESS', 'Corporate', 'CORPORATE'];
        
        foreach ($individualTypes as $identityType) {
            $result = $this->nodeSelector->getNodeIdForCountry('CN', $identityType);
            self::assertEquals(
                IpidNodeSelector::CN_INDIVIDUAL_NODE,
                $result,
                "Failed for individual identity type: " . ($identityType ?? 'null')
            );
        }
    }

    public function testNodeConstantsAreStrings(): void
    {
        self::assertIsString(IpidNodeSelector::US_ANALYTICS_NODE);
        self::assertIsString(IpidNodeSelector::US_LOGIC_NODE);
        self::assertIsString(IpidNodeSelector::US_DIRECT_FI_NODE);
        self::assertIsString(IpidNodeSelector::CN_INDIVIDUAL_NODE);
        self::assertIsString(IpidNodeSelector::CN_BUSINESS_NODE);
        self::assertIsString(IpidNodeSelector::IBAN_LOGIC_NODE);

        // Verify they are not empty
        self::assertNotEmpty(IpidNodeSelector::US_ANALYTICS_NODE);
        self::assertNotEmpty(IpidNodeSelector::US_LOGIC_NODE);
        self::assertNotEmpty(IpidNodeSelector::US_DIRECT_FI_NODE);
        self::assertNotEmpty(IpidNodeSelector::CN_INDIVIDUAL_NODE);
        self::assertNotEmpty(IpidNodeSelector::CN_BUSINESS_NODE);
        self::assertNotEmpty(IpidNodeSelector::IBAN_LOGIC_NODE);
    }

    public function testAllNodesAreUnique(): void
    {
        $nodes = [
            IpidNodeSelector::US_ANALYTICS_NODE,
            IpidNodeSelector::US_LOGIC_NODE,
            IpidNodeSelector::US_DIRECT_FI_NODE,
            IpidNodeSelector::CN_INDIVIDUAL_NODE,
            IpidNodeSelector::CN_BUSINESS_NODE,
            IpidNodeSelector::IBAN_LOGIC_NODE,
        ];

        $uniqueNodes = array_unique($nodes);

        self::assertCount(
            count($nodes),
            $uniqueNodes,
            'All node constants should be unique'
        );
    }
} 