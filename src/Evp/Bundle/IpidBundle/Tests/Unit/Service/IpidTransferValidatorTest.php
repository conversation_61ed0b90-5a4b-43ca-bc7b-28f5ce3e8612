<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class IpidTransferValidatorTest extends TestCase
{
    private IpidTransferValidator $validator;
    private MockObject $countryRegistry;
    private MockObject $consentRepository;

    protected function setUp(): void
    {
        $this->countryRegistry = self::createMock(IpidCountryRegistry::class);
        $this->consentRepository = self::createMock(TransferIpidConsentRepository::class);

        $this->validator = new IpidTransferValidator(
            $this->countryRegistry,
            $this->consentRepository
        );
    }

    public function testIsCountrySupportedDelegatesToRegistry(): void
    {
        $this->countryRegistry
            ->expects(self::once())
            ->method('isCountryEnabled')
            ->with('GB')
            ->willReturn(true);

        $result = $this->validator->isCountrySupported('GB');

        self::assertTrue($result);
    }

    public function testIsCountrySupportedReturnsFalseForUnsupported(): void
    {
        $this->countryRegistry
            ->expects(self::once())
            ->method('isCountryEnabled')
            ->with('ZZ')
            ->willReturn(false);

        $result = $this->validator->isCountrySupported('ZZ');

        self::assertFalse($result);
    }

    public function testValidateTransferDataWithValidIbanTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getName')->willReturn('John Smith');
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        // Should not throw exception
        $this->validator->validateTransferData($transfer);
        
        // If we reach here, validation passed
        self::assertTrue(true);
    }

    public function testValidateTransferDataWithValidBankTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        
        $beneficiary->method('getName')->willReturn('John Smith');
        $beneficiary->method('getDisplayAccount')->willReturn('********');
        $beneficiary->method('getBankCode')->willReturn('112233');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        // Should not throw exception
        $this->validator->validateTransferData($transfer);
        
        self::assertTrue(true);
    }

    public function testValidateTransferDataThrowsExceptionForMissingBeneficiary(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getBeneficiary')->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Transfer must have a beneficiary');

        $this->validator->validateTransferData($transfer);
    }

    public function testValidateTransferDataThrowsExceptionForMissingBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getName')->willReturn(null);
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary must have a name');

        $this->validator->validateTransferData($transfer);
    }

    public function testValidateTransferDataThrowsExceptionForMissingIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getName')->willReturn('John Smith');
        $beneficiary->method('getIban')->willReturn(null);
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('IBAN beneficiary must have an IBAN');

        $this->validator->validateTransferData($transfer);
    }

    public function testValidateTransferDataThrowsExceptionForMissingAccountNumber(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        
        $beneficiary->method('getName')->willReturn('John Smith');
        $beneficiary->method('getDisplayAccount')->willReturn(null);
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Bank beneficiary must have an account number');

        $this->validator->validateTransferData($transfer);
    }

    public function testFindExistingConsentReturnsConsent(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $existingConsent = self::createMock(TransferIpidConsent::class);

        $this->consentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn($existingConsent);

        $result = $this->validator->findExistingConsent($transfer);

        self::assertSame($existingConsent, $result);
    }

    public function testFindExistingConsentReturnsNullWhenNotFound(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $this->consentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn(null);

        $result = $this->validator->findExistingConsent($transfer);

        self::assertNull($result);
    }

    public function testIsValidationRequiredReturnsTrueForSupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->countryRegistry
            ->expects(self::once())
            ->method('isCountryEnabled')
            ->with('GB')
            ->willReturn(true);

        $result = $this->validator->isValidationRequired($transfer);

        self::assertTrue($result);
    }

    public function testIsValidationRequiredReturnsFalseForUnsupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getIban')->willReturn('US********90********9');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->countryRegistry
            ->expects(self::once())
            ->method('isCountryEnabled')
            ->with('US')
            ->willReturn(false);

        $result = $this->validator->isValidationRequired($transfer);

        self::assertFalse($result);
    }

    public function testIsValidationRequiredReturnsFalseWhenConsentExists(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);
        
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $existingConsent = self::createMock(TransferIpidConsent::class);

        $this->countryRegistry
            ->method('isCountryEnabled')
            ->with('GB')
            ->willReturn(true);

        $this->consentRepository
            ->expects(self::once())
            ->method('findByTransferId')
            ->with(123)
            ->willReturn($existingConsent);

        $result = $this->validator->isValidationRequired($transfer);

        self::assertFalse($result);
    }

    public function testExtractCountryFromIbanTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->validator->extractCountryFromTransfer($transfer);

        self::assertEquals('GB', $result);
    }

    public function testExtractCountryFromBankTransferWithBic(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        
        $beneficiary->method('getIban')->willReturn(null);
        $beneficiary->method('getBic')->willReturn('WESTGB2L');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $result = $this->validator->extractCountryFromTransfer($transfer);

        self::assertEquals('GB', $result);
    }

    public function testExtractCountryThrowsExceptionWhenCannotDetermine(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyBank::class);
        
        $beneficiary->method('getIban')->willReturn(null);
        $beneficiary->method('getBic')->willReturn(null);
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine country from transfer');

        $this->validator->extractCountryFromTransfer($transfer);
    }

    public function testValidateTransferDataWithEmptyBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getName')->willReturn('');
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary must have a name');

        $this->validator->validateTransferData($transfer);
    }

    public function testValidateTransferDataWithWhitespaceBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $beneficiary = self::createMock(PartyIban::class);
        
        $beneficiary->method('getName')->willReturn('   ');
        $beneficiary->method('getIban')->willReturn('**********************');
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Beneficiary must have a name');

        $this->validator->validateTransferData($transfer);
    }
}
