<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use PHPUnit\Framework\TestCase;

final class IpidCorridorSpecificationTest extends TestCase
{
    private IpidCorridorSpecification $corridorSpec;

    protected function setUp(): void
    {
        $this->corridorSpec = new IpidCorridorSpecification();
    }

    /**
     * @dataProvider knownCountrySpecificationDataProvider
     */
    public function testGetRequiredFieldsForKnownCountries(
        string $countryCode,
        array $expectedSpec
    ): void {
        $result = $this->corridorSpec->getRequiredFields($countryCode);

        self::assertEquals($expectedSpec, $result);
    }

    public function knownCountrySpecificationDataProvider(): array
    {
        return [
            'US specification' => [
                'countryCode' => 'US',
                'expectedSpec' => [
                    'creditor' => ['given_name' => true, 'surname' => true],
                    'creditor_identification' => ['registration_id' => false],
                    'creditor_account' => ['account_id' => true],
                    'creditor_agent' => ['clearing_system_id' => false],
                    'account_type' => 'account_id',
                ],
            ],
            'CN specification' => [
                'countryCode' => 'CN',
                'expectedSpec' => [
                    'creditor' => ['name' => true],
                    'creditor_identification' => ['registration_id' => false],
                    'creditor_account' => ['account_id' => true],
                    'creditor_agent' => ['clearing_system_id' => false],
                    'account_type' => 'account_id',
                ],
            ],
            'BE specification' => [
                'countryCode' => 'BE',
                'expectedSpec' => [
                    'creditor' => ['name' => true],
                    'creditor_identification' => ['registration_id' => false],
                    'creditor_account' => ['iban' => true],
                    'creditor_agent' => ['bic' => false],
                    'account_type' => 'iban',
                ],
            ],
            'TR flexible specification' => [
                'countryCode' => 'TR',
                'expectedSpec' => [
                    'creditor' => ['name' => true],
                    'creditor_identification' => ['registration_id' => false],
                    'creditor_account' => ['account_id' => true, 'iban' => false],
                    'creditor_agent' => ['bic' => false],
                    'account_type' => 'flexible',
                ],
            ],
        ];
    }

    public function testGetRequiredFieldsForUnknownCountryReturnsDefault(): void
    {
        $result = $this->corridorSpec->getRequiredFields('ZZ');

        $expected = [
            'creditor' => ['name' => true],
            'creditor_identification' => ['registration_id' => false],
            'creditor_account' => ['account_id' => true],
            'creditor_agent' => ['bic' => false],
            'account_type' => 'flexible',
        ];

        self::assertEquals($expected, $result);
    }

    public function testGetRequiredFieldsIsCaseInsensitive(): void
    {
        $upperResult = $this->corridorSpec->getRequiredFields('US');
        $lowerResult = $this->corridorSpec->getRequiredFields('us');
        $mixedResult = $this->corridorSpec->getRequiredFields('Us');

        self::assertEquals($upperResult, $lowerResult);
        self::assertEquals($upperResult, $mixedResult);
    }

    /**
     * @dataProvider shouldSplitNameDataProvider
     */
    public function testShouldSplitName(string $countryCode, bool $expectedResult): void
    {
        $result = $this->corridorSpec->shouldSplitName($countryCode);

        self::assertEquals($expectedResult, $result);
    }

    public function shouldSplitNameDataProvider(): array
    {
        return [
            'US splits names' => ['US', true],
            'CN does not split names' => ['CN', false],
            'GB does not split names' => ['GB', false],
            'BR does not split names' => ['BR', false],
            'Unknown country does not split names' => ['ZZ', false],
        ];
    }

    /**
     * @dataProvider preferredAccountTypeDataProvider
     */
    public function testGetPreferredAccountType(string $countryCode, string $expectedType): void
    {
        $result = $this->corridorSpec->getPreferredAccountType($countryCode);

        self::assertEquals($expectedType, $result);
    }

    public function preferredAccountTypeDataProvider(): array
    {
        return [
            'US prefers account_id' => ['US', 'account_id'],
            'CN prefers account_id' => ['CN', 'account_id'],
            'BE prefers iban' => ['BE', 'iban'],
            'IT prefers iban' => ['IT', 'iban'],
            'TR uses flexible' => ['TR', 'flexible'],
            'Unknown country uses flexible' => ['ZZ', 'flexible'],
        ];
    }

    /**
     * @dataProvider registrationIdRequiredDataProvider
     */
    public function testIsRegistrationIdRequired(string $countryCode, bool $expectedResult): void
    {
        $result = $this->corridorSpec->isRegistrationIdRequired($countryCode);

        self::assertEquals($expectedResult, $result);
    }

    public function registrationIdRequiredDataProvider(): array
    {
        return [
            'US registration ID not required' => ['US', false],
            'CN registration ID not required' => ['CN', false],
            'BR registration ID not required' => ['BR', false],
            'Unknown country registration ID not required' => ['ZZ', false],
        ];
    }

    /**
     * @dataProvider bicRequiredDataProvider
     */
    public function testIsBicRequired(string $countryCode, bool $expectedResult): void
    {
        $result = $this->corridorSpec->isBicRequired($countryCode);

        self::assertEquals($expectedResult, $result);
    }

    public function bicRequiredDataProvider(): array
    {
        return [
            'US BIC not required' => ['US', false],
            'CN BIC not required' => ['CN', false],
            'BD BIC not required' => ['BD', false],
            'BE BIC not required' => ['BE', false],
            'IT BIC not required' => ['IT', false],
            'AR BIC not required' => ['AR', false],
            'UY BIC not required' => ['UY', false],
            'TR BIC not required' => ['TR', false],
            'KR BIC not required' => ['KR', false],
            'Unknown country BIC not required' => ['ZZ', false],
        ];
    }

    /**
     * @dataProvider clearingSystemIdRequiredDataProvider
     */
    public function testIsClearingSystemIdRequired(string $countryCode, bool $expectedResult): void
    {
        $result = $this->corridorSpec->isClearingSystemIdRequired($countryCode);

        self::assertEquals($expectedResult, $result);
    }

    public function clearingSystemIdRequiredDataProvider(): array
    {
        return [
            'US clearing system ID not required' => ['US', false],
            'CN clearing system ID not required' => ['CN', false],
            'BR clearing system ID not required' => ['BR', false],
            'MX clearing system ID not required' => ['MX', false],
            'PE clearing system ID not required' => ['PE', false],
            'MY clearing system ID not required' => ['MY', false],
            'BD clearing system ID not specified' => ['BD', false],
            'Unknown country clearing system ID not required' => ['ZZ', false],
        ];
    }

    /**
     * @dataProvider identificationTypeForCountryDataProvider
     */
    public function testGetIdentificationTypeForCountry(string $countryCode, string $expectedType): void
    {
        $result = $this->corridorSpec->getIdentificationTypeForCountry($countryCode);

        self::assertEquals($expectedType, $result);
    }

    public function identificationTypeForCountryDataProvider(): array
    {
        return [
            'CN uses REGISTRATION_ID' => ['CN', 'REGISTRATION_ID'],
            'US uses TAX_ID' => ['US', 'TAX_ID'],
            'BR uses REGISTRATION_ID' => ['BR', 'REGISTRATION_ID'],
            'BE uses REGISTRATION_ID' => ['BE', 'REGISTRATION_ID'],
            'IT uses REGISTRATION_ID' => ['IT', 'REGISTRATION_ID'],
            'MX uses REGISTRATION_ID' => ['MX', 'REGISTRATION_ID'],
            'AR uses REGISTRATION_ID' => ['AR', 'REGISTRATION_ID'],
            'Unknown country uses REGISTRATION_ID' => ['ZZ', 'REGISTRATION_ID'],
            'Case insensitive' => ['us', 'TAX_ID'],
        ];
    }

    /**
     * @dataProvider countrySupportDataProvider
     */
    public function testIsCountrySupported(string $countryCode, bool $expectedResult): void
    {
        $result = $this->corridorSpec->isCountrySupported($countryCode);

        self::assertEquals($expectedResult, $result);
    }

    public function countrySupportDataProvider(): array
    {
        return [
            // Supported countries
            'US supported' => ['US', true],
            'IN supported' => ['IN', true],
            'ID supported' => ['ID', true],
            'VN supported' => ['VN', true],
            'NG supported' => ['NG', true],
            'NP supported' => ['NP', true],
            'PK supported' => ['PK', true],
            'CN supported' => ['CN', true],
            'UK supported' => ['UK', true],
            'GB supported' => ['GB', true],
            'KR supported' => ['KR', true],
            'BD supported' => ['BD', true],
            'UG supported' => ['UG', true],
            'BR supported' => ['BR', true],
            'MX supported' => ['MX', true],
            'AR supported' => ['AR', true],
            'BE supported' => ['BE', true],
            'IT supported' => ['IT', true],
            'UY supported' => ['UY', true],
            'PE supported' => ['PE', true],
            'MY supported' => ['MY', true],
            'TR supported' => ['TR', true],
            'ZA supported' => ['ZA', true],
            'FR supported' => ['FR', true],
            'NL supported' => ['NL', true],
            'PL supported' => ['PL', true],
            'CL supported' => ['CL', true],
            'CO supported' => ['CO', true],
            'EC supported' => ['EC', true],
            'SA supported' => ['SA', true],
            
            // Unsupported countries
            'ZZ not supported' => ['ZZ', false],
            'XX not supported' => ['XX', false],
            'DE not supported' => ['DE', false],
            'CA not supported' => ['CA', false],
            'AU not supported' => ['AU', false],
            
            // Case insensitive
            'us lowercase supported' => ['us', true],
            'Cn mixed case supported' => ['Cn', true],
            'zz lowercase not supported' => ['zz', false],
        ];
    }

    public function testAllSpecifiedCountriesAreSupported(): void
    {
        $specifiedCountries = [
            'US', 'CN', 'BD', 'BR', 'MX', 'AR', 'BE', 'IT', 'UY', 'PE', 'MY', 'TR', 'KR'
        ];

        foreach ($specifiedCountries as $country) {
            self::assertTrue(
                $this->corridorSpec->isCountrySupported($country),
                "Country $country has specifications but is not marked as supported"
            );
        }
    }

    public function testCompleteWorkflowForTypicalCountries(): void
    {
        // Test complete workflow for US (splits names, uses account_id)
        self::assertTrue($this->corridorSpec->shouldSplitName('US'));
        self::assertEquals('account_id', $this->corridorSpec->getPreferredAccountType('US'));
        self::assertEquals('TAX_ID', $this->corridorSpec->getIdentificationTypeForCountry('US'));
        self::assertFalse($this->corridorSpec->isBicRequired('US'));
        self::assertFalse($this->corridorSpec->isClearingSystemIdRequired('US'));
        self::assertTrue($this->corridorSpec->isCountrySupported('US'));

        // Test complete workflow for BE (uses IBAN, full name)
        self::assertFalse($this->corridorSpec->shouldSplitName('BE'));
        self::assertEquals('iban', $this->corridorSpec->getPreferredAccountType('BE'));
        self::assertEquals('REGISTRATION_ID', $this->corridorSpec->getIdentificationTypeForCountry('BE'));
        self::assertFalse($this->corridorSpec->isBicRequired('BE'));
        self::assertTrue($this->corridorSpec->isCountrySupported('BE'));

        // Test complete workflow for TR (flexible account type)
        self::assertFalse($this->corridorSpec->shouldSplitName('TR'));
        self::assertEquals('flexible', $this->corridorSpec->getPreferredAccountType('TR'));
        self::assertEquals('REGISTRATION_ID', $this->corridorSpec->getIdentificationTypeForCountry('TR'));
        self::assertFalse($this->corridorSpec->isBicRequired('TR'));
        self::assertTrue($this->corridorSpec->isCountrySupported('TR'));
    }

    public function testConsistencyBetweenSupportedCountriesAndSpecs(): void
    {
        $supportedCountries = [
            'US', 'IN', 'ID', 'VN', 'NG', 'NP', 'PK', 'CN', 'UK', 'GB', 'KR', 'BD', 'UG',
            'BR', 'MX', 'AR', 'BE', 'IT', 'UY', 'PE', 'MY', 'TR', 'ZA', 'FR', 'NL', 'PL',
            'CL', 'CO', 'EC', 'SA'
        ];

        foreach ($supportedCountries as $country) {
            self::assertTrue($this->corridorSpec->isCountrySupported($country));

            // All methods should work without errors for supported countries
            $fields = $this->corridorSpec->getRequiredFields($country);
            self::assertIsArray($fields);
            self::assertArrayHasKey('account_type', $fields);

            $accountType = $this->corridorSpec->getPreferredAccountType($country);
            self::assertContains($accountType, ['iban', 'account_id', 'flexible']);

            $idType = $this->corridorSpec->getIdentificationTypeForCountry($country);
            self::assertContains($idType, ['REGISTRATION_ID', 'TAX_ID']);

            // Boolean methods should not throw exceptions
            self::assertIsBool($this->corridorSpec->shouldSplitName($country));
            self::assertIsBool($this->corridorSpec->isRegistrationIdRequired($country));
            self::assertIsBool($this->corridorSpec->isBicRequired($country));
            self::assertIsBool($this->corridorSpec->isClearingSystemIdRequired($country));
        }
    }
} 