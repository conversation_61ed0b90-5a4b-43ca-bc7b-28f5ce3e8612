<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Normalizer\IpidResponseDenormalizer;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\ServerException;
use PHPUnit\Framework\TestCase;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;
use Psr\Log\LoggerInterface;

final class IpidApiClientGuzzleTest extends TestCase
{
    private IpidApiClient $apiClient;
    private Client $httpClient;
    private IpidResponseDenormalizer $responseDenormalizer;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->httpClient = self::createMock(Client::class);
        $this->responseDenormalizer = self::createMock(IpidResponseDenormalizer::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->apiClient = new IpidApiClient(
            $this->httpClient,
            $this->responseDenormalizer,
            'https://api.ipid.live',
            'test-api-key',
            'test-customer-id',
            $this->logger
        );
    }

    public function testMakeRequestSuccessWithGuzzle(): void
    {
        $responseData = ['status' => 'success', 'data' => ['key' => 'value']];

        $stream = self::createMock(StreamInterface::class);
        $stream
            ->expects(self::once())
            ->method('getContents')
            ->willReturn(json_encode($responseData))
        ;

        $response = self::createMock(ResponseInterface::class);
        $response
            ->expects(self::once())
            ->method('getStatusCode')
            ->willReturn(200)
        ;
        $response
            ->expects(self::once())
            ->method('getBody')
            ->willReturn($stream)
        ;

        $this->httpClient
            ->expects(self::once())
            ->method('request')
            ->with(
                'GET',
                'https://api.ipid.live/test-path',
                self::callback(function (array $options) {
                    self::assertEquals(30, $options['timeout']);
                    self::assertEquals('test-api-key', $options['headers']['x-api-key']);
                    self::assertEquals('test-customer-id', $options['headers']['x-customer-id']);
                    self::assertEquals('application/json', $options['headers']['Content-Type']);
                    self::assertEquals('application/json', $options['headers']['Accept']);
                    return true;
                })
            )
            ->willReturn($response)
        ;

        $result = $this->apiClient->makeRequest('GET', '/test-path');

        self::assertEquals($responseData, $result);
    }

    public function testMakeRequestClientExceptionHandling(): void
    {
        $response = self::createMock(ResponseInterface::class);
        $response
            ->expects(self::once())
            ->method('getStatusCode')
            ->willReturn(401)
        ;

        $clientException = self::createMock(ClientException::class);
        $clientException
            ->expects(self::once())
            ->method('getResponse')
            ->willReturn($response)
        ;

        $this->httpClient
            ->expects(self::once())
            ->method('request')
            ->willThrowException($clientException)
        ;

        $this->expectException(IpidApiException::class);
        $this->expectExceptionMessage('iPiD API authentication failed');

        $this->apiClient->makeRequest('GET', '/test-path');
    }

    public function testMakeRequestConnectExceptionHandling(): void
    {
        $connectException = self::createMock(ConnectException::class);

        $this->httpClient
            ->expects(self::once())
            ->method('request')
            ->willThrowException($connectException)
        ;

        $this->expectException(IpidApiException::class);
        $this->expectExceptionMessage('iPiD API connection timeout');

        $this->apiClient->makeRequest('GET', '/test-path');
    }

    public function testMakeRequestServerExceptionHandling(): void
    {
        $serverException = self::createMock(ServerException::class);

        $this->httpClient
            ->expects(self::once())
            ->method('request')
            ->willThrowException($serverException)
        ;

        $this->expectException(IpidApiException::class);
        $this->expectExceptionMessage('iPiD API server error');

        $this->apiClient->makeRequest('GET', '/test-path');
    }

    public function testGetPublicKeyWithGuzzle(): void
    {
        $responseData = [
            'data' => [
                'node_public_key' => '-----BEGIN PGP PUBLIC KEY BLOCK-----',
                'node_id' => 'gb-node-01',
            ],
        ];

        $stream = self::createMock(StreamInterface::class);
        $stream
            ->expects(self::once())
            ->method('getContents')
            ->willReturn(json_encode($responseData))
        ;

        $response = self::createMock(ResponseInterface::class);
        $response
            ->expects(self::once())
            ->method('getStatusCode')
            ->willReturn(200)
        ;
        $response
            ->expects(self::once())
            ->method('getBody')
            ->willReturn($stream)
        ;

        $this->httpClient
            ->expects(self::once())
            ->method('request')
            ->with(
                'GET',
                'https://api.ipid.live/validation/api/v1/public-key',
                self::callback(function (array $options) {
                    self::assertEquals(['country_code' => 'GB'], $options['query']);
                    return true;
                })
            )
            ->willReturn($response)
        ;

        $result = $this->apiClient->getPublicKey('GB');

        self::assertEquals($responseData['data'], $result);
    }
}
