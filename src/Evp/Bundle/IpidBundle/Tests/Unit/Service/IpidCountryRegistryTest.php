<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use PHPUnit\Framework\TestCase;

final class IpidCountryRegistryTest extends TestCase
{
    public function testConstructorNormalizesCountryCodes(): void
    {
        $registry = new IpidCountryRegistry(['gb', 'us', 'fr'], ['eu', 'uk']);

        self::assertTrue($registry->isCountryEnabled('GB'));
        self::assertTrue($registry->isCountryEnabled('US'));
        self::assertTrue($registry->isCountryEnabled('FR'));
        self::assertFalse($registry->isCountryEnabled('DE'));
    }

    public function testIsCountryEnabledWithDirectCountries(): void
    {
        $registry = new IpidCountryRegistry(['GB', 'US', 'FR']);

        self::assertTrue($registry->isCountryEnabled('GB'));
        self::assertTrue($registry->isCountryEnabled('US'));
        self::assertTrue($registry->isCountryEnabled('FR'));
        self::assertFalse($registry->isCountryEnabled('DE'));
        self::assertFalse($registry->isCountryEnabled('JP'));
    }

    public function testIsCountryEnabledWithRegions(): void
    {
        $registry = new IpidCountryRegistry([], ['EU']);

        // EU countries should be enabled
        self::assertTrue($registry->isCountryEnabled('DE'));
        self::assertTrue($registry->isCountryEnabled('FR'));
        self::assertTrue($registry->isCountryEnabled('IT'));
        self::assertTrue($registry->isCountryEnabled('ES'));
        
        // Non-EU countries should not be enabled
        self::assertFalse($registry->isCountryEnabled('US'));
        self::assertFalse($registry->isCountryEnabled('GB'));
        self::assertFalse($registry->isCountryEnabled('JP'));
    }

    public function testIsCountryEnabledWithMixedConfiguration(): void
    {
        $registry = new IpidCountryRegistry(['GB', 'US'], ['EU']);

        // Direct countries
        self::assertTrue($registry->isCountryEnabled('GB'));
        self::assertTrue($registry->isCountryEnabled('US'));
        
        // EU region countries
        self::assertTrue($registry->isCountryEnabled('DE'));
        self::assertTrue($registry->isCountryEnabled('FR'));
        
        // Not enabled
        self::assertFalse($registry->isCountryEnabled('JP'));
        self::assertFalse($registry->isCountryEnabled('AU'));
    }

    public function testGetValidationSchemeForCountry(): void
    {
        $registry = new IpidCountryRegistry();

        self::assertEquals('COP', $registry->getValidationSchemeForCountry('GB'));
        self::assertEquals('VOP', $registry->getValidationSchemeForCountry('DE'));
        self::assertEquals('VOP', $registry->getValidationSchemeForCountry('FR'));
        self::assertEquals('COP', $registry->getValidationSchemeForCountry('US'));
        self::assertEquals('GLOBAL', $registry->getValidationSchemeForCountry('JP'));
    }

    public function testGetRegionForCountry(): void
    {
        $registry = new IpidCountryRegistry();

        self::assertEquals('UK', $registry->getRegionForCountry('GB'));
        self::assertEquals('EU', $registry->getRegionForCountry('DE'));
        self::assertEquals('EU', $registry->getRegionForCountry('FR'));
        self::assertEquals('EU', $registry->getRegionForCountry('IT'));
        self::assertEquals('US', $registry->getRegionForCountry('US'));
        self::assertNull($registry->getRegionForCountry('JP'));
        self::assertNull($registry->getRegionForCountry('AU'));
    }

    public function testGetEnabledCountries(): void
    {
        $registry = new IpidCountryRegistry(['GB', 'US', 'FR']);

        $enabledCountries = $registry->getEnabledCountries();
        
        self::assertIsArray($enabledCountries);
        self::assertContains('GB', $enabledCountries);
        self::assertContains('US', $enabledCountries);
        self::assertContains('FR', $enabledCountries);
        self::assertCount(3, $enabledCountries);
    }

    public function testGetEnabledRegions(): void
    {
        $registry = new IpidCountryRegistry([], ['EU', 'UK']);

        $enabledRegions = $registry->getEnabledRegions();
        
        self::assertIsArray($enabledRegions);
        self::assertContains('EU', $enabledRegions);
        self::assertContains('UK', $enabledRegions);
        self::assertCount(2, $enabledRegions);
    }

    public function testCaseInsensitiveCountryChecking(): void
    {
        $registry = new IpidCountryRegistry(['GB', 'US']);

        self::assertTrue($registry->isCountryEnabled('gb'));
        self::assertTrue($registry->isCountryEnabled('Gb'));
        self::assertTrue($registry->isCountryEnabled('GB'));
        self::assertTrue($registry->isCountryEnabled('us'));
        self::assertTrue($registry->isCountryEnabled('Us'));
        self::assertTrue($registry->isCountryEnabled('US'));
    }

    public function testEmptyConfiguration(): void
    {
        $registry = new IpidCountryRegistry();

        self::assertFalse($registry->isCountryEnabled('GB'));
        self::assertFalse($registry->isCountryEnabled('US'));
        self::assertFalse($registry->isCountryEnabled('DE'));
        
        self::assertEmpty($registry->getEnabledCountries());
        self::assertEmpty($registry->getEnabledRegions());
    }

    public function testRegionMappingConsistency(): void
    {
        $registry = new IpidCountryRegistry();

        // Test that all EU countries return EU region
        $euCountries = ['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'PT', 'FI', 'IE'];
        foreach ($euCountries as $country) {
            self::assertEquals('EU', $registry->getRegionForCountry($country), 
                "Country $country should be in EU region");
        }

        // Test specific regions
        self::assertEquals('UK', $registry->getRegionForCountry('GB'));
        self::assertEquals('US', $registry->getRegionForCountry('US'));
    }

    public function testValidationSchemeConsistency(): void
    {
        $registry = new IpidCountryRegistry();

        // COP countries
        $copCountries = ['GB', 'US'];
        foreach ($copCountries as $country) {
            self::assertEquals('COP', $registry->getValidationSchemeForCountry($country),
                "Country $country should use COP validation");
        }

        // VOP countries  
        $vopCountries = ['DE', 'FR', 'IT', 'ES'];
        foreach ($vopCountries as $country) {
            self::assertEquals('VOP', $registry->getValidationSchemeForCountry($country),
                "Country $country should use VOP validation");
        }

        // Global fallback
        $globalCountries = ['JP', 'AU', 'CA', 'BR'];
        foreach ($globalCountries as $country) {
            self::assertEquals('GLOBAL', $registry->getValidationSchemeForCountry($country),
                "Country $country should use GLOBAL validation");
        }
    }
}
