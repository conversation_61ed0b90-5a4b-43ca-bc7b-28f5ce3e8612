<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;

use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\IpidValidationService;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use Evp\Bundle\IpidBundle\Service\Validation\IpidTransferValidator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Unit tests for the refactored IpidValidationService.
 *
 * Tests the main validation flow scenarios:
 * - Unsupported country handling
 * - Already validated transfer detection
 * - API exception handling
 *
 * The service now directly coordinates all focused services instead of using an orchestrator.
 */
final class IpidValidationServiceTest extends TestCase
{
    private IpidValidationService $service;
    private MockObject $transferValidator;
    private MockObject $requestBuilder;
    private MockObject $cacheManager;
    private MockObject $apiClient;
    private MockObject $responseProcessor;

    private MockObject $countryRegistry;
    private MockObject $logger;

    protected function setUp(): void
    {
        $this->transferValidator = self::createMock(IpidTransferValidator::class);
        $this->requestBuilder = self::createMock(IpidRequestBuilder::class);
        $this->cacheManager = self::createMock(IpidCacheManager::class);
        $this->apiClient = self::createMock(IpidApiClient::class);
        $this->responseProcessor = self::createMock(IpidResponseProcessor::class);

        $this->countryRegistry = self::createMock(IpidCountryRegistry::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->service = new IpidValidationService(
            $this->transferValidator,
            $this->requestBuilder,
            $this->cacheManager,
            $this->apiClient,
            $this->responseProcessor,
            $this->countryRegistry,
            $this->logger
        );
    }

    public function testValidateTransferUnsupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        // Mock beneficiary for country extraction
        $beneficiary = self::createMock(\Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban::class);
        $beneficiary->method('getIban')->willReturn('*********************'); // US IBAN
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('US')
            ->willReturn(false); // Not supported for iPiD

        $this->responseProcessor
            ->expects(self::once())
            ->method('createAutomaticConsent')
            ->with($transfer, 'unsupported_country', 'Country not configured for iPiD validation')
            ->willReturn(self::createMock(TransferIpidConsent::class));

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('iPiD validation skipped - country not supported', self::isType('array'));

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('Creating automatic consent for unsupported country', self::isType('array'));

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('unsupported_country', $result->getStatus());
        self::assertNotNull($result->getConsent());
    }

    public function testValidateTransferAlreadyValidated(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        // Mock beneficiary for country extraction
        $beneficiary = self::createMock(\Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************'); // GB IBAN
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        // Mock supported country check
        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('GB')
            ->willReturn(true);

        $this->countryRegistry
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('GB')
            ->willReturn('COP');

        $this->countryRegistry
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('GB')
            ->willReturn('UK');

        $existingConsent = self::createMock(TransferIpidConsent::class);
        $existingConsent->expects(self::once())->method('getId')->willReturn(456);
        $existingConsent->expects(self::once())->method('getConsentStatus')->willReturn('granted');

        $this->transferValidator
            ->expects(self::once())
            ->method('findExistingConsent')
            ->with($transfer)
            ->willReturn($existingConsent);

        $this->logger
            ->expects(self::any())
            ->method('info');

        $this->logger
            ->expects(self::any())
            ->method('error');

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('already_validated', $result->getStatus());
    }

    public function testValidateTransferApiException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->expects(self::atLeastOnce())->method('getId')->willReturn(123);

        // Mock beneficiary for country extraction
        $beneficiary = self::createMock(\Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************'); // DE IBAN
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        // Mock supported country check
        $this->transferValidator
            ->expects(self::once())
            ->method('isCountrySupported')
            ->with('DE')
            ->willReturn(true);

        $this->countryRegistry
            ->expects(self::once())
            ->method('getValidationSchemeForCountry')
            ->with('DE')
            ->willReturn('COP');

        $this->countryRegistry
            ->expects(self::once())
            ->method('getRegionForCountry')
            ->with('DE')
            ->willReturn('EU');

        $this->logger
            ->expects(self::any())
            ->method('info');

        $this->logger
            ->expects(self::any())
            ->method('error');

        $this->transferValidator
            ->expects(self::once())
            ->method('findExistingConsent')
            ->with($transfer)
            ->willReturn(null);

        $request = self::createMock(IpidValidationRequest::class);
        $this->requestBuilder
            ->expects(self::once())
            ->method('buildRequest')
            ->with($transfer)
            ->willReturn($request);

        $this->cacheManager
            ->expects(self::once())
            ->method('calculateRequestHash')
            ->with($request)
            ->willReturn('test-hash');

        $this->cacheManager
            ->expects(self::once())
            ->method('findValidCachedResponse')
            ->with('test-hash', 'DE')
            ->willReturn(null);

        $apiException = new IpidApiException('API error', 'IPID_001');
        $this->apiClient
            ->expects(self::once())
            ->method('sendValidationRequest')
            ->with($request)
            ->willThrowException($apiException);

        // Mock automatic consent creation
        $automaticConsent = self::createMock(TransferIpidConsent::class);
        $this->responseProcessor
            ->expects(self::once())
            ->method('createAutomaticConsent')
            ->with($transfer, 'validation_error', 'API error')
            ->willReturn($automaticConsent);

        $this->logger
            ->expects(self::any())
            ->method('warning');

        $this->logger
            ->expects(self::any())
            ->method('info');

        $this->logger
            ->expects(self::any())
            ->method('error');

        $result = $this->service->validateTransfer($transfer);

        self::assertEquals('error', $result->getStatus());
        self::assertSame($automaticConsent, $result->getConsent());
    }
}