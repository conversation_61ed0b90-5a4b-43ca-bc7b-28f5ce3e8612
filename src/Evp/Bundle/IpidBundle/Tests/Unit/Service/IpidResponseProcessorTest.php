<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Entity\TransferIpidConsent;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Evp\Bundle\IpidBundle\Repository\TransferIpidConsentRepository;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use Evp\Bundle\IpidBundle\Service\Processing\IpidResponseProcessor;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class IpidResponseProcessorTest extends TestCase
{
    private IpidResponseProcessor $processor;
    private MockObject $apiResponseRepository;
    private MockObject $consentRepository;
    private MockObject $logger;

    protected function setUp(): void
    {
        $this->apiResponseRepository = self::createMock(IpidApiResponseRepository::class);
        $this->consentRepository = self::createMock(TransferIpidConsentRepository::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->processor = new IpidResponseProcessor(
            $this->apiResponseRepository,
            $this->consentRepository,
            $this->logger
        );
    }

    public function testSaveApiResponseSuccess(): void
    {
        $validationResponse = new IpidValidationResponse();
        $validationResponse->setStatus('success');
        $validationResponse->setResponseCode('2000');
        $validationResponse->setResponseMessage('Validation successful');
        $validationResponse->setMatchScore(0.95);
        $validationResponse->setMatchLevel('Strong');

        $requestHash = 'test-hash-123';
        $country = 'GB';

        $this->apiResponseRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (IpidApiResponse $response) use ($requestHash, $country) {
                return $response->getRequestHash() === $requestHash
                    && $response->getCountry() === $country
                    && $response->getResponseStatus() === 'success'
                    && $response->getResponseCode() === '2000'
                    && $response->getMatchScore() === 0.95
                    && $response->getMatchLevel() === 'Strong';
            }))
            ->willReturnCallback(function (IpidApiResponse $response) {
                // Simulate setting ID after save
                $reflection = new \ReflectionClass($response);
                $idProperty = $reflection->getProperty('id');
                $idProperty->setAccessible(true);
                $idProperty->setValue($response, 123);
                return $response;
            });

        $result = $this->processor->saveApiResponse($validationResponse, $requestHash, $country);

        self::assertInstanceOf(IpidApiResponse::class, $result);
        self::assertEquals($requestHash, $result->getRequestHash());
        self::assertEquals($country, $result->getCountry());
        self::assertEquals('success', $result->getResponseStatus());
    }

    public function testSaveApiResponseWithVopData(): void
    {
        $validationResponse = new IpidValidationResponse();
        $validationResponse->setStatus('success');
        $validationResponse->setResponseCode('2000');
        $validationResponse->setVopIdMatch('MATCH');
        $validationResponse->setVopNameMatch('MATCH');

        $this->apiResponseRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (IpidApiResponse $response) {
                return $response->getVopIdMatch() === 'MATCH'
                    && $response->getVopNameMatch() === 'MATCH';
            }));

        $this->processor->saveApiResponse($validationResponse, 'hash', 'DE');
    }

    public function testSaveApiResponseWithCopData(): void
    {
        $validationResponse = new IpidValidationResponse();
        $validationResponse->setStatus('success');
        $validationResponse->setResponseCode('2000');
        $validationResponse->setCopMatched(true);
        $validationResponse->setCopReason('Account verified');

        $this->apiResponseRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (IpidApiResponse $response) {
                return $response->getCopMatched() === true
                    && $response->getCopReason() === 'Account verified';
            }));

        $this->processor->saveApiResponse($validationResponse, 'hash', 'GB');
    }

    public function testProcessValidationResultWithStrongMatch(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $apiResponse = new IpidApiResponse();
        $apiResponse->setMatchLevel('Strong');
        $apiResponse->setMatchScore(0.95);
        $apiResponse->setResponseStatus('success');

        $this->consentRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (TransferIpidConsent $consent) {
                return $consent->getTransferId() === 123
                    && $consent->getConsentStatus() === TransferIpidConsent::CONSENT_STATUS_GRANTED
                    && $consent->getMatchLevel() === 'Strong'
                    && !$consent->isRequiresManualReview();
            }));

        $result = $this->processor->processValidationResult($transfer, $apiResponse);

        self::assertInstanceOf(IpidValidationResult::class, $result);
        self::assertEquals('validated', $result->getStatus());
        self::assertNotNull($result->getConsent());
    }

    public function testProcessValidationResultWithWeakMatch(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $apiResponse = new IpidApiResponse();
        $apiResponse->setMatchLevel('Weak');
        $apiResponse->setMatchScore(0.3);
        $apiResponse->setResponseStatus('success');

        $this->consentRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (TransferIpidConsent $consent) {
                return $consent->getConsentStatus() === TransferIpidConsent::CONSENT_STATUS_PENDING
                    && $consent->isRequiresManualReview();
            }));

        $result = $this->processor->processValidationResult($transfer, $apiResponse);

        self::assertEquals('pending_review', $result->getStatus());
    }

    public function testProcessValidationResultWithNoMatch(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $apiResponse = new IpidApiResponse();
        $apiResponse->setMatchLevel('no_match');
        $apiResponse->setMatchScore(0.1);
        $apiResponse->setResponseStatus('success');

        $this->consentRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (TransferIpidConsent $consent) {
                return $consent->getConsentStatus() === TransferIpidConsent::CONSENT_STATUS_DENIED
                    && $consent->isRequiresManualReview();
            }));

        $result = $this->processor->processValidationResult($transfer, $apiResponse);

        self::assertEquals('validation_failed', $result->getStatus());
    }

    public function testCreateAutomaticConsentForUnsupportedCountry(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $this->consentRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (TransferIpidConsent $consent) {
                return $consent->getTransferId() === 123
                    && $consent->getConsentStatus() === TransferIpidConsent::CONSENT_STATUS_GRANTED
                    && $consent->getValidationNotes() === 'Country not configured for iPiD validation'
                    && !$consent->isRequiresManualReview();
            }));

        $result = $this->processor->createAutomaticConsent(
            $transfer,
            'unsupported_country',
            'Country not configured for iPiD validation'
        );

        self::assertInstanceOf(TransferIpidConsent::class, $result);
        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_GRANTED, $result->getConsentStatus());
    }

    public function testCreateAutomaticConsentForValidationError(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $this->consentRepository
            ->expects(self::once())
            ->method('save')
            ->with(self::callback(function (TransferIpidConsent $consent) {
                return $consent->getConsentStatus() === TransferIpidConsent::CONSENT_STATUS_GRANTED
                    && $consent->getValidationNotes() === 'API validation failed'
                    && !$consent->isRequiresManualReview();
            }));

        $result = $this->processor->createAutomaticConsent(
            $transfer,
            'validation_error',
            'API validation failed'
        );

        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_GRANTED, $result->getConsentStatus());
    }

    public function testDetermineConsentStatusFromMatchLevel(): void
    {
        // Test Strong match
        $result = $this->processor->determineConsentStatus('Strong', 0.95);
        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_GRANTED, $result['status']);
        self::assertFalse($result['requires_review']);

        // Test Partial match
        $result = $this->processor->determineConsentStatus('Partial', 0.7);
        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_PENDING, $result['status']);
        self::assertTrue($result['requires_review']);

        // Test Weak match
        $result = $this->processor->determineConsentStatus('Weak', 0.3);
        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_PENDING, $result['status']);
        self::assertTrue($result['requires_review']);

        // Test No match
        $result = $this->processor->determineConsentStatus('no_match', 0.1);
        self::assertEquals(TransferIpidConsent::CONSENT_STATUS_DENIED, $result['status']);
        self::assertTrue($result['requires_review']);
    }

    public function testLoggingDuringProcessing(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(123);

        $apiResponse = new IpidApiResponse();
        $apiResponse->setMatchLevel('Strong');
        $apiResponse->setResponseStatus('success');

        $this->logger
            ->expects(self::atLeastOnce())
            ->method('info')
            ->with(
                self::stringContains('iPiD validation result processed'),
                self::arrayHasKey('transfer_id')
            );

        $this->consentRepository->method('save')->willReturnArgument(0);

        $this->processor->processValidationResult($transfer, $apiResponse);
    }
}
