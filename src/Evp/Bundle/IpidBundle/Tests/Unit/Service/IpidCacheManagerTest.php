<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Entity\IpidApiResponse;
use Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use PHPUnit\Framework\TestCase;

final class IpidCacheManagerTest extends TestCase
{
    private IpidCacheManager $cacheManager;
    private IpidApiResponseRepository $repository;

    protected function setUp(): void
    {
        $this->repository = self::createMock(IpidApiResponseRepository::class);
        $this->cacheManager = new IpidCacheManager($this->repository);
    }

    public function testCalculateRequestHashWithSamePayload(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"<PERSON>"},"creditor_account":{"iban":"**********************"}}';

        $request1 = new IpidValidationRequest($jsonPayload);
        $request1->setNodeId('node-gb-001');

        $request2 = new IpidValidationRequest($jsonPayload);
        $request2->setNodeId('node-gb-001');

        $hash1 = $this->cacheManager->calculateRequestHash($request1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2);

        self::assertEquals($hash1, $hash2, 'Same payload should generate same hash');
        self::assertEquals(64, strlen($hash1), 'Hash should be 64 characters (SHA256)');
    }

    public function testCalculateRequestHashWithDifferentPayload(): void
    {
        $jsonPayload1 = '{"country_code":"GB","creditor":{"name":"John Smith"},"creditor_account":{"iban":"**********************"}}';
        $jsonPayload2 = '{"country_code":"GB","creditor":{"name":"Jane Doe"},"creditor_account":{"iban":"**********************"}}';

        $request1 = new IpidValidationRequest($jsonPayload1);
        $request1->setNodeId('node-gb-001');

        $request2 = new IpidValidationRequest($jsonPayload2);
        $request2->setNodeId('node-gb-001');

        $hash1 = $this->cacheManager->calculateRequestHash($request1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2);

        self::assertNotEquals($hash1, $hash2, 'Different payload should generate different hash');
    }

    public function testCalculateRequestHashWithDifferentNodeId(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"John Smith"},"creditor_account":{"iban":"**********************"}}';

        $request1 = new IpidValidationRequest($jsonPayload);
        $request1->setNodeId('node-gb-001');

        $request2 = new IpidValidationRequest($jsonPayload);
        $request2->setNodeId('node-us-001');

        $hash1 = $this->cacheManager->calculateRequestHash($request1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2);

        self::assertNotEquals($hash1, $hash2, 'Different node ID should generate different hash');
    }

    public function testCalculateRequestHashWithNullNodeId(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"John Smith"},"creditor_account":{"iban":"**********************"}}';

        $request1 = new IpidValidationRequest($jsonPayload);
        $request1->setNodeId('node-gb-001');

        $request2 = new IpidValidationRequest($jsonPayload);
        // No node ID set (null)

        $hash1 = $this->cacheManager->calculateRequestHash($request1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2);

        self::assertNotEquals($hash1, $hash2, 'Null node ID should generate different hash');
    }

    public function testFindValidCachedResponseCallsRepository(): void
    {
        $requestHash = 'test-hash-123';
        $country = 'GB';
        $expectedResponse = self::createMock(IpidApiResponse::class);

        $this->repository
            ->expects(self::once())
            ->method('findValidByRequestHashAndCountry')
            ->with($requestHash, $country)
            ->willReturn($expectedResponse);

        $result = $this->cacheManager->findValidCachedResponse($requestHash, $country);

        self::assertSame($expectedResponse, $result);
    }

    public function testFindValidCachedResponseReturnsNull(): void
    {
        $requestHash = 'test-hash-456';
        $country = 'US';

        $this->repository
            ->expects(self::once())
            ->method('findValidByRequestHashAndCountry')
            ->with($requestHash, $country)
            ->willReturn(null);

        $result = $this->cacheManager->findValidCachedResponse($requestHash, $country);

        self::assertNull($result);
    }

    public function testHashConsistencyAcrossMultipleCalls(): void
    {
        $jsonPayload = '{"country_code":"DE","creditor":{"name":"Hans Mueller"},"creditor_account":{"iban":"**********************"}}';

        $request = new IpidValidationRequest($jsonPayload);
        $request->setNodeId('node-de-001');

        $hash1 = $this->cacheManager->calculateRequestHash($request);
        $hash2 = $this->cacheManager->calculateRequestHash($request);
        $hash3 = $this->cacheManager->calculateRequestHash($request);

        self::assertEquals($hash1, $hash2);
        self::assertEquals($hash2, $hash3);
        self::assertEquals($hash1, $hash3);
    }

    public function testHashIsValidSha256(): void
    {
        $jsonPayload = '{"country_code":"US","creditor":{"name":"John Doe"},"creditor_account":{"account_id":"*********"}}';

        $request = new IpidValidationRequest($jsonPayload);
        $request->setNodeId('node-test-001');

        $hash = $this->cacheManager->calculateRequestHash($request);

        // SHA256 hash should be 64 characters long and contain only hex characters
        self::assertEquals(64, strlen($hash));
        self::assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $hash);
    }

    public function testUniversalCachingScenario(): void
    {
        // Simulate same beneficiary data in different transfers
        // Both should generate the same hash for cache sharing

        $sameJsonPayload = '{"country_code":"GB","creditor":{"name":"John Smith"},"creditor_account":{"iban":"**********************"}}';

        $request1 = new IpidValidationRequest($sameJsonPayload);
        $request1->setNodeId('node-gb-001');

        $request2 = new IpidValidationRequest($sameJsonPayload);
        $request2->setNodeId('node-gb-001');

        $hash1 = $this->cacheManager->calculateRequestHash($request1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2);

        self::assertEquals($hash1, $hash2,
            'Same beneficiary data should generate same hash for universal caching');
    }
}
