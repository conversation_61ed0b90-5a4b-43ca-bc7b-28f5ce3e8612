<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidEncryptionService;
use Evp\Bundle\IpidBundle\Service\IpidNodeSelector;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use PHPUnit\Framework\TestCase;

final class IpidRequestBuilderTest extends TestCase
{
    private IpidRequestBuilder $requestBuilder;
    private IpidTransferHelper $transferHelper;
    private IpidEncryptionService $encryptionService;
    private IpidApiClient $apiClient;
    private IpidNodeSelector $nodeSelector;
    private IpidCorridorSpecification $corridorSpecification;

    protected function setUp(): void
    {
        $this->transferHelper = self::createMock(IpidTransferHelper::class);
        $this->encryptionService = self::createMock(IpidEncryptionService::class);
        $this->apiClient = self::createMock(IpidApiClient::class);
        $this->nodeSelector = self::createMock(IpidNodeSelector::class);
        $this->corridorSpecification = self::createMock(IpidCorridorSpecification::class);

        $this->requestBuilder = new IpidRequestBuilder(
            $this->transferHelper,
            $this->encryptionService,
            $this->apiClient,
            $this->nodeSelector,
            $this->corridorSpecification
        );
    }

    public function testBuildRequestWithGbIbanTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        // Setup mocks for GB IBAN scenario
        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');
        $this->transferHelper->method('getCurrency')->willReturn('GBP');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn(null);
        $this->transferHelper->method('getBeneficiaryBankBic')->willReturn('WESTGB2L');

        $this->nodeSelector->method('getNodeIdForCountry')->with('GB', 'individual')->willReturn('node-gb-001');
        $this->apiClient->method('getPublicKey')->with('GB')->willReturn([
            'node_public_key' => 'test-public-key-data'
        ]);

        $this->corridorSpecification->method('shouldSplitName')->with('GB')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->with('GB')->willReturn('iban');
        $this->corridorSpecification->method('isBicRequired')->with('GB')->willReturn(true);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->with('GB')->willReturn(false);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::stringContains('"iban":"**********************"'),
                'test-public-key-data'
            )
            ->willReturn('encrypted-payload-data')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-payload-data', $result->getEncryptedPayload());
        self::assertEquals('node-gb-001', $result->getNodeId());
        self::assertEquals('WESTGB2L', $result->getBic());
    }

    public function testBuildRequestWithUsNonIbanTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(54321);

        // Setup mocks for US non-IBAN scenario
        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('US');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Jane Doe');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('*********');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');
        $this->transferHelper->method('getCurrency')->willReturn('USD');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn('SSN*********');
        $this->transferHelper->method('getBeneficiarySortCode')->willReturn('*********');

        $this->nodeSelector->method('getNodeIdForCountry')->with('US', 'individual')->willReturn('node-us-001');
        $this->apiClient->method('getPublicKey')->with('US')->willReturn([
            'node_public_key' => 'test-us-public-key'
        ]);

        $this->corridorSpecification->method('shouldSplitName')->with('US')->willReturn(true);
        $this->corridorSpecification->method('getPreferredAccountType')->with('US')->willReturn('account_id');
        $this->corridorSpecification->method('getIdentificationTypeForCountry')->with('US')->willReturn('ssn');
        $this->corridorSpecification->method('isBicRequired')->with('US')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->with('US')->willReturn(true);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    self::stringContains('"given_name":"Jane"'),
                    self::stringContains('"surname":"Doe"'),
                    self::stringContains('"account_id":"*********"'),
                    self::stringContains('"identification":[{"value":"SSN*********","type":"ssn"}]')
                ),
                'test-us-public-key'
            )
            ->willReturn('encrypted-us-payload')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-us-payload', $result->getEncryptedPayload());
        self::assertEquals('node-us-001', $result->getNodeId());
        self::assertEquals('*********', $result->getClearingSystemId());
    }

    public function testBuildRequestWithCnBusinessTransfer(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(67890);

        // Setup mocks for CN business scenario
        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('CN');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Beijing Technology Co Ltd');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('6217000010012345678');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('business');
        $this->transferHelper->method('getCurrency')->willReturn('CNY');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn('91110000*********X');

        $this->nodeSelector->method('getNodeIdForCountry')->with('CN', 'business')->willReturn('node-cn-biz');
        $this->apiClient->method('getPublicKey')->with('CN')->willReturn([
            'node_public_key' => 'test-cn-public-key'
        ]);

        $this->corridorSpecification->method('shouldSplitName')->with('CN')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->with('CN')->willReturn('flexible');
        $this->corridorSpecification->method('getIdentificationTypeForCountry')->with('CN')->willReturn('business_license');
        $this->corridorSpecification->method('isBicRequired')->with('CN')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->with('CN')->willReturn(true);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    self::stringContains('"name":"Beijing Technology Co Ltd"'),
                    self::stringContains('"account_id":"6217000010012345678"'),
                    self::stringContains('"currency":"CNY"'),
                    self::stringContains('"identification":[{"value":"91110000*********X","type":"business_license"}]')
                ),
                'test-cn-public-key'
            )
            ->willReturn('encrypted-cn-payload')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-cn-payload', $result->getEncryptedPayload());
        self::assertEquals('node-cn-biz', $result->getNodeId());
    }

    public function testBuildRequestWithFlexibleAccountTypeChoosesIban(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(11111);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('DE');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Hans Mueller');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-de-001');
        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->corridorSpecification->method('shouldSplitName')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->with('DE')->willReturn('flexible');
        $this->corridorSpecification->method('isBicRequired')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->willReturn(false);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::stringContains('"iban":"**********************"'),
                'key'
            )
            ->willReturn('encrypted-payload')
        ;

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(99999);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid transfer data for iPiD validation: Beneficiary name is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(88888);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid transfer data for iPiD validation: Beneficiary account number is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesApiClientException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(77777);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-gb-001');
        
        $this->apiClient
            ->method('getPublicKey')
            ->willThrowException(new \RuntimeException('API connection failed'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to build iPiD validation request: API connection failed');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesEncryptionException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(66666);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-gb-001');
        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->corridorSpecification->method('shouldSplitName')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->willReturn('iban');
        $this->corridorSpecification->method('isBicRequired')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->willReturn(false);

        $this->encryptionService
            ->method('encrypt')
            ->willThrowException(new \RuntimeException('Encryption failed'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to build iPiD validation request: Encryption failed');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesUnexpectedException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(55555);

        $this->transferHelper
            ->method('extractCountryFromTransfer')
            ->willThrowException(new \Exception('Unexpected database error'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Unexpected error building iPiD request for transfer 55555: Unexpected database error');

        $this->requestBuilder->buildRequest($transfer);
    }

    /**
     * @dataProvider nameProcessingDataProvider
     */
    public function testNameProcessingForDifferentCountries(
        string $country,
        string $fullName,
        bool $shouldSplit,
        array $expectedFields
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn($country);
        $this->transferHelper->method('getBeneficiaryName')->willReturn($fullName);
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('*********');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-test');
        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->corridorSpecification->method('shouldSplitName')->with($country)->willReturn($shouldSplit);
        $this->corridorSpecification->method('getPreferredAccountType')->willReturn('account_id');
        $this->corridorSpecification->method('isBicRequired')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->willReturn(false);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    ...array_map(
                        fn($field) => self::stringContains($field),
                        $expectedFields
                    )
                ),
                'key'
            )
            ->willReturn('encrypted')
        ;

        $this->requestBuilder->buildRequest($transfer);
    }

    public function nameProcessingDataProvider(): array
    {
        return [
            'US splits names' => [
                'country' => 'US',
                'fullName' => 'John Smith',
                'shouldSplit' => true,
                'expectedFields' => ['"given_name":"John"', '"surname":"Smith"'],
            ],
            'GB keeps full name' => [
                'country' => 'GB',
                'fullName' => 'John Smith',
                'shouldSplit' => false,
                'expectedFields' => ['"name":"John Smith"'],
            ],
            'Single name split' => [
                'country' => 'US',
                'fullName' => 'Madonna',
                'shouldSplit' => true,
                'expectedFields' => ['"given_name":"Madonna"'],
            ],
            'Three part name split' => [
                'country' => 'US',
                'fullName' => 'Mary Jane Watson',
                'shouldSplit' => true,
                'expectedFields' => ['"given_name":"Mary"', '"surname":"Jane Watson"'],
            ],
        ];
    }

    /**
     * @dataProvider accountTypeHandlingDataProvider
     */
    public function testAccountTypeHandling(
        string $accountNumber,
        string $preferredType,
        string $expectedField
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('TEST');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Test Name');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn($accountNumber);
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-test');
        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->corridorSpecification->method('shouldSplitName')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->willReturn($preferredType);
        $this->corridorSpecification->method('isBicRequired')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->willReturn(false);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::stringContains($expectedField),
                'key'
            )
            ->willReturn('encrypted')
        ;

        $this->requestBuilder->buildRequest($transfer);
    }

    public function accountTypeHandlingDataProvider(): array
    {
        return [
            'IBAN with IBAN preference' => [
                'accountNumber' => '**********************',
                'preferredType' => 'iban',
                'expectedField' => '"iban":"**********************"',
            ],
            'Non-IBAN with account_id preference' => [
                'accountNumber' => '*********',
                'preferredType' => 'account_id',
                'expectedField' => '"account_id":"*********"',
            ],
            'IBAN with flexible preference uses IBAN' => [
                'accountNumber' => '**********************',
                'preferredType' => 'flexible',
                'expectedField' => '"iban":"**********************"',
            ],
            'Non-IBAN with flexible preference uses account_id' => [
                'accountNumber' => '*********',
                'preferredType' => 'flexible',
                'expectedField' => '"account_id":"*********"',
            ],
        ];
    }

    /**
     * @dataProvider optionalFieldInclusionDataProvider
     */
    public function testOptionalFieldInclusion(
        string $country,
        array $mockReturns,
        array $expectedRequestFields
    ): void {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn($country);
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Test Name');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('*********');
        $this->transferHelper->method('getBeneficiaryType')->willReturn('individual');

        foreach (['getCurrency', 'getBeneficiaryBankBic', 'getBeneficiarySortCode'] as $method) {
            $this->transferHelper->method($method)->willReturn($mockReturns[$method] ?? null);
        }

        $this->nodeSelector->method('getNodeIdForCountry')->willReturn('node-test');
        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->corridorSpecification->method('shouldSplitName')->willReturn(false);
        $this->corridorSpecification->method('getPreferredAccountType')->willReturn('account_id');
        $this->corridorSpecification->method('isBicRequired')->willReturn(false);
        $this->corridorSpecification->method('isClearingSystemIdRequired')->willReturn(false);

        $this->encryptionService->method('encrypt')->willReturn('encrypted');

        $result = $this->requestBuilder->buildRequest($transfer);

        foreach ($expectedRequestFields as $field => $expectedValue) {
            if ($expectedValue !== null) {
                self::assertEquals($expectedValue, $result->{"get$field"}(), "Field $field should be set");
            } else {
                self::assertNull($result->{"get$field"}(), "Field $field should be null");
            }
        }
    }

    public function optionalFieldInclusionDataProvider(): array
    {
        return [
            'BD includes BIC' => [
                'country' => 'BD',
                'mockReturns' => ['getBeneficiaryBankBic' => 'BKBDBDDH'],
                'expectedRequestFields' => ['Bic' => 'BKBDBDDH', 'ClearingSystemId' => null],
            ],
            'US includes clearing system ID' => [
                'country' => 'US',
                'mockReturns' => ['getBeneficiarySortCode' => '*********'],
                'expectedRequestFields' => ['Bic' => null, 'ClearingSystemId' => '*********'],
            ],
            'GB includes neither' => [
                'country' => 'GB',
                'mockReturns' => [
                    'getBeneficiaryBankBic' => 'WESTGB2L',
                    'getBeneficiarySortCode' => '20-20-15'
                ],
                'expectedRequestFields' => ['Bic' => null, 'ClearingSystemId' => null],
            ],
        ];
    }
} 