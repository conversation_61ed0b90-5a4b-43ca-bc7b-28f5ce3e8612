<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidEncryptionService;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use PHPUnit\Framework\TestCase;

final class IpidRequestBuilderSimplifiedTest extends TestCase
{
    private IpidRequestBuilder $requestBuilder;
    private IpidEncryptionService $encryptionService;
    private IpidApiClient $apiClient;

    protected function setUp(): void
    {
        $this->encryptionService = self::createMock(IpidEncryptionService::class);
        $this->apiClient = self::createMock(IpidApiClient::class);

        $this->requestBuilder = new IpidRequestBuilder(
            $this->encryptionService,
            $this->apiClient
        );
    }

    public function testBuildRequestWithIbanAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        // Create IBAN beneficiary mock
        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn('**********************');
        $beneficiary->method('getName')->willReturn('John Smith');

        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        $this->apiClient->method('getPublicKey')->with('GB')->willReturn([
            'node_public_key' => 'test-public-key-data',
            'node_id' => 'node-gb-001'
        ]);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    self::stringContains('"name":"John Smith"'),
                    self::stringContains('"iban":"**********************"')
                ),
                'test-public-key-data'
            )
            ->willReturn('encrypted-payload-data')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-payload-data', $result->getEncryptedPayload());
        self::assertEquals('node-gb-001', $result->getNodeId());
    }

    public function testBuildRequestWithNonIbanAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(54321);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('US');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Jane Doe');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('*********');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn('SSN*********');
        $this->transferHelper->method('getBeneficiaryBankBic')->willReturn(null);
        $this->transferHelper->method('getBeneficiarySortCode')->willReturn('*********');

        $this->apiClient->method('getPublicKey')->with('US')->willReturn([
            'node_public_key' => 'test-us-public-key'
        ]);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    self::stringContains('"name":"Jane Doe"'),
                    self::stringContains('"account_id":"*********"'),
                    self::stringContains('"identification":[{"value":"SSN*********","type":"registration_id"}]'),
                    self::stringContains('"clearing_system_id":"*********"')
                ),
                'test-us-public-key'
            )
            ->willReturn('encrypted-us-payload')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-us-payload', $result->getEncryptedPayload());
        self::assertNull($result->getNodeId()); // No node_id in response
    }

    public function testBuildRequestWithMinimalData(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(99999);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('DE');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Hans Mueller');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn(null);
        $this->transferHelper->method('getBeneficiaryBankBic')->willReturn(null);
        $this->transferHelper->method('getBeneficiarySortCode')->willReturn(null);

        $this->apiClient->method('getPublicKey')->with('DE')->willReturn([
            'node_public_key' => 'test-de-public-key'
        ]);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::logicalAnd(
                    self::stringContains('"name":"Hans Mueller"'),
                    self::stringContains('"iban":"**********************"')
                ),
                'test-de-public-key'
            )
            ->willReturn('encrypted-de-payload')
        ;

        $result = $this->requestBuilder->buildRequest($transfer);

        self::assertInstanceOf(IpidValidationRequest::class, $result);
        self::assertEquals('encrypted-de-payload', $result->getEncryptedPayload());
        self::assertNull($result->getNodeId());
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryName(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(99999);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid transfer data for iPiD validation: Beneficiary name is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestThrowsExceptionForMissingBeneficiaryAccount(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(88888);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn(null);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid transfer data for iPiD validation: Beneficiary account number is required for iPiD validation');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesApiClientException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(77777);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');

        $this->apiClient
            ->method('getPublicKey')
            ->willThrowException(new \RuntimeException('API connection failed'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to build iPiD validation request: API connection failed');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesEncryptionException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(66666);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('GB');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('John Smith');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn('**********************');
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn(null);
        $this->transferHelper->method('getBeneficiaryBankBic')->willReturn(null);
        $this->transferHelper->method('getBeneficiarySortCode')->willReturn(null);

        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->encryptionService
            ->method('encrypt')
            ->willThrowException(new \RuntimeException('Encryption failed'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Failed to build iPiD validation request: Encryption failed');

        $this->requestBuilder->buildRequest($transfer);
    }

    public function testBuildRequestHandlesUnexpectedException(): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(55555);

        $this->transferHelper
            ->method('extractCountryFromTransfer')
            ->willThrowException(new \Exception('Unexpected database error'))
        ;

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Unexpected error building iPiD request for transfer 55555: Unexpected database error');

        $this->requestBuilder->buildRequest($transfer);
    }

    /**
     * @dataProvider accountTypeDataProvider
     */
    public function testAccountTypeDetection(string $accountNumber, string $expectedField): void
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn(12345);

        $this->transferHelper->method('extractCountryFromTransfer')->willReturn('TEST');
        $this->transferHelper->method('getBeneficiaryName')->willReturn('Test Name');
        $this->transferHelper->method('getBeneficiaryAccount')->willReturn($accountNumber);
        $this->transferHelper->method('extractPersonCodeFromTransfer')->willReturn(null);
        $this->transferHelper->method('getBeneficiaryBankBic')->willReturn(null);
        $this->transferHelper->method('getBeneficiarySortCode')->willReturn(null);

        $this->apiClient->method('getPublicKey')->willReturn(['node_public_key' => 'key']);

        $this->encryptionService
            ->expects(self::once())
            ->method('encrypt')
            ->with(
                self::stringContains($expectedField),
                'key'
            )
            ->willReturn('encrypted')
        ;

        $this->requestBuilder->buildRequest($transfer);
    }

    public function accountTypeDataProvider(): array
    {
        return [
            'IBAN detected' => [
                'accountNumber' => '**********************',
                'expectedField' => '"iban":"**********************"',
            ],
            'Non-IBAN uses account_id' => [
                'accountNumber' => '*********',
                'expectedField' => '"account_id":"*********"',
            ],
            'German IBAN' => [
                'accountNumber' => '**********************',
                'expectedField' => '"iban":"**********************"',
            ],
        ];
    }
}
