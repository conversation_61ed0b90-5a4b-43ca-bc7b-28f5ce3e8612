<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\Service\OpenPgpEncryptionService;
use PHPUnit\Framework\TestCase;

final class OpenPgpEncryptionServiceTest extends TestCase
{
    private OpenPgpEncryptionService $encryptionService;

    protected function setUp(): void
    {
        $this->encryptionService = new OpenPgpEncryptionService();
    }

    public function testEncryptWithoutPublicKeyThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Public key is required for encryption');

        $this->encryptionService->encrypt('test payload', null);
    }

    public function testEncryptWithValidPublicKeyWhenGnuPGNotAvailable(): void
    {
        $publicKey = $this->createMockPublicKey();
        $payload = 'test payload for encryption';

        // When GnuPG extension is not available, expect appropriate exception
        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            // If GnuPG is available, test would require mocking gnupg functions
            $this->expectException(\RuntimeException::class);
        }

        $this->encryptionService->encrypt($payload, $publicKey);
    }

    public function testEncryptWithInvalidPublicKeyThrowsException(): void
    {
        $invalidPublicKey = 'invalid-public-key-data';
        $payload = 'test payload';

        // When GnuPG extension is not available, expect function call error
        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
            $this->expectExceptionMessage('Failed to import iPiD public key');
        }

        $this->encryptionService->encrypt($payload, $invalidPublicKey);
    }

    public function testDecryptWithoutPrivateKeyThrowsException(): void
    {
        $encryptedPayload = base64_encode('encrypted-data');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Private key not configured for decryption');

        $this->encryptionService->decrypt($encryptedPayload);
    }

    public function testDecryptWithValidPrivateKeyWhenGnuPGNotAvailable(): void
    {
        $privateKey = $this->createMockPrivateKey();
        $encryptionService = new OpenPgpEncryptionService($privateKey);
        $encryptedPayload = base64_encode('mock-encrypted-data');

        // When GnuPG extension is not available, expect appropriate exception
        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            // If GnuPG is available, test would require mocking gnupg functions
            $this->expectException(\RuntimeException::class);
        }

        $encryptionService->decrypt($encryptedPayload);
    }

    public function testDecryptWithInvalidBase64ThrowsException(): void
    {
        $privateKey = $this->createMockPrivateKey();
        $encryptionService = new OpenPgpEncryptionService($privateKey);
        $invalidBase64 = 'invalid-base64-data!@#';

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
            $this->expectExceptionMessage('Invalid base64 encoded payload');
        }

        $encryptionService->decrypt($invalidBase64);
    }

    public function testDecryptWithInvalidPrivateKeyThrowsException(): void
    {
        $invalidPrivateKey = 'invalid-private-key-data';
        $encryptionService = new OpenPgpEncryptionService($invalidPrivateKey);
        $encryptedPayload = base64_encode('encrypted-data');

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
            $this->expectExceptionMessage('Failed to import private key');
        }

        $encryptionService->decrypt($encryptedPayload);
    }

    public function testEncryptFailureThrowsException(): void
    {
        $publicKey = $this->createMockPublicKey();
        $payload = 'test payload';

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
            $this->expectExceptionMessage('Failed to encrypt payload');
        }

        $this->encryptionService->encrypt($payload, $publicKey);
    }

    public function testDecryptFailureThrowsException(): void
    {
        $privateKey = $this->createMockPrivateKey();
        $encryptionService = new OpenPgpEncryptionService($privateKey);
        $encryptedPayload = base64_encode('corrupted-encrypted-data');

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
            $this->expectExceptionMessage('Failed to decrypt payload');
        }

        $encryptionService->decrypt($encryptedPayload);
    }

    /**
     * @dataProvider encryptionPayloadDataProvider
     */
    public function testEncryptDifferentPayloadTypes(string $payload): void
    {
        $publicKey = $this->createMockPublicKey();

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            // If GnuPG is available, would need proper mocking
            $this->expectException(\RuntimeException::class);
        }

        $this->encryptionService->encrypt($payload, $publicKey);
    }

    public function encryptionPayloadDataProvider(): array
    {
        return [
            'Simple text' => ['Hello, world!'],
            'JSON payload' => ['{"creditor":{"name":"John Doe"},"account":{"id":"123456"}}'],
            'Empty string' => [''],
            'Special characters' => ['Åčëñťś & ñümßërs 123'],
            'Large payload' => [str_repeat('A', 10000)],
        ];
    }

    public function testCreateGnuPGResourceInitializationFailure(): void
    {
        $publicKey = $this->createMockPublicKey();

        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            // If GnuPG is available, test actual initialization behavior
            $this->expectException(\RuntimeException::class);
        }

        $this->encryptionService->encrypt('test', $publicKey);
    }

    public function testEncryptionServiceInstantiationWithoutGnuPG(): void
    {
        // Test that the service can be instantiated even without GnuPG
        $service = new OpenPgpEncryptionService();
        self::assertInstanceOf(OpenPgpEncryptionService::class, $service);
    }

    public function testEncryptionServiceInstantiationWithPrivateKey(): void
    {
        // Test that the service can be instantiated with a private key
        $privateKey = $this->createMockPrivateKey();
        $service = new OpenPgpEncryptionService($privateKey);
        self::assertInstanceOf(OpenPgpEncryptionService::class, $service);
    }

    public function testValidationOfRequiredParametersWithoutGnuPG(): void
    {
        // Test that parameter validation works regardless of GnuPG availability
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Public key is required for encryption');

        $this->encryptionService->encrypt('test', null);
    }

    public function testValidBase64DecodingCheck(): void
    {
        // This tests the base64 validation logic that doesn't require GnuPG
        $privateKey = $this->createMockPrivateKey();
        $encryptionService = new OpenPgpEncryptionService($privateKey);
        
        // Test with valid base64 but expect GnuPG function call error
        $validBase64 = base64_encode('test-data');
        
        if (!extension_loaded('gnupg')) {
            $this->expectException(\Error::class);
            $this->expectExceptionMessage('Call to undefined function');
        } else {
            $this->expectException(\RuntimeException::class);
        }

        $encryptionService->decrypt($validBase64);
    }

    private function createMockPublicKey(): string
    {
        return '-----BEGIN PGP PUBLIC KEY BLOCK-----
Version: GnuPG v1

mQENBFKd...mock...public...key...data...
-----END PGP PUBLIC KEY BLOCK-----';
    }

    private function createMockPrivateKey(): string
    {
        return '-----BEGIN PGP PRIVATE KEY BLOCK-----
Version: GnuPG v1

lQPGBFKd...mock...private...key...data...
-----END PGP PRIVATE KEY BLOCK-----';
    }
} 