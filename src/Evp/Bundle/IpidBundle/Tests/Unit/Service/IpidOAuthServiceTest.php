<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\Service;

use Evp\Bundle\IpidBundle\DTO\IpidOAuthRegistrationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidOAuthTokenResponse;
use Evp\Bundle\IpidBundle\Exception\IpidApiException;
use Evp\Bundle\IpidBundle\Exception\IpidOAuthException;
use Evp\Bundle\IpidBundle\Service\IpidApiClient;
use Evp\Bundle\IpidBundle\Service\IpidOAuthService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class IpidOAuthServiceTest extends TestCase
{
    private IpidOAuthService $oauthService;
    private IpidApiClient $apiClient;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->apiClient = self::createMock(IpidApiClient::class);
        $this->logger = self::createMock(LoggerInterface::class);

        $this->oauthService = new IpidOAuthService(
            $this->apiClient,
            $this->logger,
            'test-client-id',
            'test-client-secret'
        );
    }

    public function testRegisterClientSuccess(): void
    {
        $request = self::createMock(IpidOAuthRegistrationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getScopes')
            ->willReturn(['validation', 'data_hub'])
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getApplicationName')
            ->willReturn('Test Application')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getRedirectUri')
            ->willReturn('https://example.com/callback')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getDescription')
            ->willReturn('Test application description')
        ;

        $responseData = [
            'access_token' => 'test-access-token-12345',
            'expires_in' => 7200,
            'token_type' => 'Bearer',
            'scope' => 'validation data_hub',
        ];

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->with(
                'POST',
                '/oauth/api/v1/register',
                [
                    'json' => [
                        'client_id' => 'test-client-id',
                        'client_secret' => 'test-client-secret',
                        'grant_type' => 'client_credentials',
                        'scope' => 'validation data_hub',
                        'application_name' => 'Test Application',
                        'redirect_uri' => 'https://example.com/callback',
                        'description' => 'Test application description',
                    ],
                ],
                false
            )
            ->willReturn($responseData)
        ;

        $this->logger
            ->expects(self::exactly(2))
            ->method('info')
        ;

        $result = $this->oauthService->registerClient($request);

        self::assertInstanceOf(IpidOAuthTokenResponse::class, $result);
    }

    public function testRegisterClientApiException(): void
    {
        $request = self::createMock(IpidOAuthRegistrationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getScopes')
            ->willReturn(['validation'])
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getApplicationName')
            ->willReturn('Test Application')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getRedirectUri')
            ->willReturn('https://example.com/callback')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getDescription')
            ->willReturn('Test description')
        ;

        $apiException = new IpidApiException('Authentication failed', 'IPID_002');

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willThrowException($apiException)
        ;

        $this->logger
            ->expects(self::once())
            ->method('info')
            ->with('Registering OAuth client with iPiD API', self::isType('array'))
        ;

        $this->logger
            ->expects(self::once())
            ->method('error')
            ->with('OAuth registration failed', [
                'error' => 'Authentication failed',
                'client_id' => 'test-client-id',
            ])
        ;

        $this->expectException(IpidOAuthException::class);
        $this->expectExceptionMessage('OAuth registration failed: Authentication failed');

        $this->oauthService->registerClient($request);
    }

    public function testRegisterClientUnexpectedException(): void
    {
        $request = self::createMock(IpidOAuthRegistrationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getScopes')
            ->willReturn(['validation'])
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getApplicationName')
            ->willReturn('Test Application')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getRedirectUri')
            ->willReturn('https://example.com/callback')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getDescription')
            ->willReturn('Test description')
        ;

        $exception = new \Exception('Network error');

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willThrowException($exception)
        ;

        $this->logger
            ->expects(self::once())
            ->method('error')
            ->with('OAuth registration failed', [
                'error' => 'Network error',
                'client_id' => 'test-client-id',
            ])
        ;

        $this->expectException(IpidOAuthException::class);
        $this->expectExceptionMessage('OAuth registration failed: Network error');

        $this->oauthService->registerClient($request);
    }

    public function testRefreshTokenSuccess(): void
    {
        $refreshToken = 'test-refresh-token-12345';
        $responseData = [
            'access_token' => 'new-access-token-67890',
            'expires_in' => 7200,
            'token_type' => 'Bearer',
            'refresh_token' => 'new-refresh-token-67890',
        ];

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->with(
                'POST',
                '/oauth/api/v1/token',
                [
                    'json' => [
                        'grant_type' => 'refresh_token',
                        'refresh_token' => $refreshToken,
                        'client_id' => 'test-client-id',
                        'client_secret' => 'test-client-secret',
                    ],
                ],
                false
            )
            ->willReturn($responseData)
        ;

        $result = $this->oauthService->refreshToken($refreshToken);

        self::assertInstanceOf(IpidOAuthTokenResponse::class, $result);
    }

    public function testRefreshTokenApiException(): void
    {
        $refreshToken = 'invalid-refresh-token';
        $apiException = new IpidApiException('Invalid refresh token', 'IPID_003');

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willThrowException($apiException)
        ;

        $this->expectException(IpidOAuthException::class);
        $this->expectExceptionMessage('Token refresh failed: Invalid refresh token');

        $this->oauthService->refreshToken($refreshToken);
    }

    public function testRefreshTokenUnexpectedException(): void
    {
        $refreshToken = 'test-refresh-token';
        $exception = new \Exception('Database connection failed');

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willThrowException($exception)
        ;

        $this->expectException(IpidOAuthException::class);
        $this->expectExceptionMessage('Token refresh failed: Database connection failed');

        $this->oauthService->refreshToken($refreshToken);
    }

    /**
     * @dataProvider registrationRequestDataProvider
     */
    public function testRegisterClientWithDifferentRequests(
        array $scopes,
        string $applicationName,
        string $redirectUri,
        string $description
    ): void {
        $request = self::createMock(IpidOAuthRegistrationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getScopes')
            ->willReturn($scopes)
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getApplicationName')
            ->willReturn($applicationName)
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getRedirectUri')
            ->willReturn($redirectUri)
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getDescription')
            ->willReturn($description)
        ;

        $responseData = [
            'access_token' => 'test-token',
            'expires_in' => 7200,
            'token_type' => 'Bearer',
        ];

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->with(
                'POST',
                '/oauth/api/v1/register',
                [
                    'json' => [
                        'client_id' => 'test-client-id',
                        'client_secret' => 'test-client-secret',
                        'grant_type' => 'client_credentials',
                        'scope' => implode(' ', $scopes),
                        'application_name' => $applicationName,
                        'redirect_uri' => $redirectUri,
                        'description' => $description,
                    ],
                ],
                false
            )
            ->willReturn($responseData)
        ;

        $result = $this->oauthService->registerClient($request);

        self::assertInstanceOf(IpidOAuthTokenResponse::class, $result);
    }

    public function registrationRequestDataProvider(): array
    {
        return [
            'Single scope validation only' => [
                'scopes' => ['validation'],
                'applicationName' => 'Validation App',
                'redirectUri' => 'https://validation.example.com/callback',
                'description' => 'Application for bank account validation',
            ],
            'Multiple scopes' => [
                'scopes' => ['validation', 'data_hub', 'reporting'],
                'applicationName' => 'Full Feature App',
                'redirectUri' => 'https://app.example.com/oauth/callback',
                'description' => 'Full featured iPiD integration application',
            ],
            'Data hub only' => [
                'scopes' => ['data_hub'],
                'applicationName' => 'Data Hub Client',
                'redirectUri' => 'https://datahub.example.com/auth',
                'description' => 'Data hub synchronization service',
            ],
            'Empty description' => [
                'scopes' => ['validation'],
                'applicationName' => 'Minimal App',
                'redirectUri' => 'https://minimal.example.com/callback',
                'description' => '',
            ],
        ];
    }

    public function testRegisterClientLogsTokenLength(): void
    {
        $request = self::createMock(IpidOAuthRegistrationRequest::class);
        $request
            ->expects(self::atLeastOnce())
            ->method('getScopes')
            ->willReturn(['validation'])
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getApplicationName')
            ->willReturn('Test App')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getRedirectUri')
            ->willReturn('https://example.com/callback')
        ;
        $request
            ->expects(self::atLeastOnce())
            ->method('getDescription')
            ->willReturn('Test description')
        ;

        $longToken = str_repeat('a', 256);
        $responseData = [
            'access_token' => $longToken,
            'expires_in' => 3600,
            'token_type' => 'Bearer',
        ];

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willReturn($responseData)
        ;

        $this->logger
            ->expects(self::exactly(2))
            ->method('info')
        ;

        $this->oauthService->registerClient($request);
    }

    public function testRefreshTokenWithEmptyResponse(): void
    {
        $refreshToken = 'test-refresh-token';
        $responseData = [
            'access_token' => '',
            'expires_in' => 0,
            'token_type' => 'Bearer',
        ];

        $this->apiClient
            ->expects(self::once())
            ->method('makeRequest')
            ->willReturn($responseData)
        ;

        $result = $this->oauthService->refreshToken($refreshToken);

        self::assertInstanceOf(IpidOAuthTokenResponse::class, $result);
    }
} 