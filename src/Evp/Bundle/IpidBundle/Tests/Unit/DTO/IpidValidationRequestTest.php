<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use PHPUnit\Framework\TestCase;

final class IpidValidationRequestTest extends TestCase
{

    public function testConstructorSetsRequiredProperties(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"<PERSON>"}}';
        $request = new IpidValidationRequest($jsonPayload);

        self::assertEquals($jsonPayload, $request->getJsonPayload());
        self::assertNull($request->getNodeId());
    }

    public function testGetJsonPayload(): void
    {
        $jsonPayload = '{"country_code":"US","creditor":{"given_name":"<PERSON>","surname":"<PERSON><PERSON>"}}';
        $request = new IpidValidationRequest($jsonPayload);

        self::assertEquals($jsonPayload, $request->getJsonPayload());
    }

    public function testSetAndGetNodeId(): void
    {
        $request = new IpidValidationRequest('{"test":"payload"}');

        $result = $request->setNodeId('gb-node-01');

        self::assertSame($request, $result);
        self::assertEquals('gb-node-01', $request->getNodeId());
    }

    public function testSetNodeIdWithNull(): void
    {
        $request = new IpidValidationRequest('{"test":"payload"}');
        $request->setNodeId('test-node');

        $result = $request->setNodeId(null);

        self::assertSame($request, $result);
        self::assertNull($request->getNodeId());
    }

    public function testGetBodyWithoutNodeId(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"John Smith"}}';
        $request = new IpidValidationRequest($jsonPayload);

        $expectedBody = [
            'country_code' => 'GB',
            'creditor' => [
                'name' => 'John Smith'
            ]
        ];

        self::assertEquals($expectedBody, $request->getBody());
    }

    public function testGetBodyWithNodeId(): void
    {
        $jsonPayload = '{"country_code":"GB","creditor":{"name":"John Smith"}}';
        $request = new IpidValidationRequest($jsonPayload);
        $request->setNodeId('gb-node-01');

        $expectedBody = [
            'country_code' => 'GB',
            'creditor' => [
                'name' => 'John Smith'
            ],
            'node_id' => 'gb-node-01'
        ];

        self::assertEquals($expectedBody, $request->getBody());
    }

    public function testGetBodyWithComplexPayload(): void
    {
        $jsonPayload = '{"country_code":"DE","creditor":{"name":"Otto Larsen"},"creditor_account":{"iban":"**********************"},"creditor_agent":{"bic":"DEUTDEFF"}}';
        $request = new IpidValidationRequest($jsonPayload);
        $request->setNodeId('eu-node-02');

        $expectedBody = [
            'country_code' => 'DE',
            'creditor' => [
                'name' => 'Otto Larsen'
            ],
            'creditor_account' => [
                'iban' => '**********************'
            ],
            'creditor_agent' => [
                'bic' => 'DEUTDEFF'
            ],
            'node_id' => 'eu-node-02'
        ];

        self::assertEquals($expectedBody, $request->getBody());
    }

    public function testGetBodyWithInvalidJson(): void
    {
        $invalidJson = '{"invalid": json}';
        $request = new IpidValidationRequest($invalidJson);

        $this->expectException(\JsonException::class);

        $request->getBody();
    }

    public function testMethodChaining(): void
    {
        $request = new IpidValidationRequest('{"test":"payload"}');

        $result = $request->setNodeId('test-node');

        self::assertSame($request, $result);
        self::assertEquals('test-node', $request->getNodeId());
    }

    public function testRealWorldScenarios(): void
    {
        // UK COP scenario
        $ukPayload = '{"country_code":"GB","creditor":{"name":"Sherlock Holmes"},"creditor_account":{"account_id":"********"},"creditor_agent":{"clearing_system_id":"112233"}}';
        $ukRequest = new IpidValidationRequest($ukPayload);
        $ukRequest->setNodeId('uk-cop-node');

        $ukBody = $ukRequest->getBody();
        self::assertEquals('GB', $ukBody['country_code']);
        self::assertEquals('Sherlock Holmes', $ukBody['creditor']['name']);
        self::assertEquals('********', $ukBody['creditor_account']['account_id']);
        self::assertEquals('uk-cop-node', $ukBody['node_id']);

        // EU VOP scenario
        $euPayload = '{"country_code":"DE","creditor":{"name":"Otto Larsen"},"creditor_account":{"iban":"**********************"},"creditor_agent":{"bic":"DEUTDEFF"}}';
        $euRequest = new IpidValidationRequest($euPayload);
        $euRequest->setNodeId('eu-vop-node');

        $euBody = $euRequest->getBody();
        self::assertEquals('DE', $euBody['country_code']);
        self::assertEquals('Otto Larsen', $euBody['creditor']['name']);
        self::assertEquals('**********************', $euBody['creditor_account']['iban']);
        self::assertEquals('eu-vop-node', $euBody['node_id']);

        // US scenario with split names
        $usPayload = '{"country_code":"US","creditor":{"given_name":"John","surname":"Doe"},"creditor_account":{"account_id":"*********"},"creditor_agent":{"clearing_system_id":"*********"}}';
        $usRequest = new IpidValidationRequest($usPayload);

        $usBody = $usRequest->getBody();
        self::assertEquals('US', $usBody['country_code']);
        self::assertEquals('John', $usBody['creditor']['given_name']);
        self::assertEquals('Doe', $usBody['creditor']['surname']);
        self::assertArrayNotHasKey('node_id', $usBody); // No node ID set
    }
}