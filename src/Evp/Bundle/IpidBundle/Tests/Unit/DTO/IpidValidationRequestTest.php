<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use PHPUnit\Framework\TestCase;

final class IpidValidationRequestTest extends TestCase
{
    private IpidValidationRequest $request;

    protected function setUp(): void
    {
        $this->request = new IpidValidationRequest(
            'encrypted-payload-data',
            'gb-node-01'
        );
    }

    public function testConstructorSetsRequiredProperties(): void
    {
        $encryptedPayload = 'test-encrypted-payload';
        $nodeId = 'us-node-02';

        $request = new IpidValidationRequest($encryptedPayload, $nodeId);

        self::assertEquals($encryptedPayload, $request->getEncryptedPayload());
        self::assertEquals($nodeId, $request->getNodeId());
    }

    public function testGetEncryptedPayload(): void
    {
        self::assertEquals('encrypted-payload-data', $this->request->getEncryptedPayload());
    }

    public function testGetNodeId(): void
    {
        self::assertEquals('gb-node-01', $this->request->getNodeId());
    }

    public function testSetAndGetPublicKey(): void
    {
        $publicKey = '-----BEGIN PGP PUBLIC KEY BLOCK-----test-key-----END PGP PUBLIC KEY BLOCK-----';

        $result = $this->request->setPublicKey($publicKey);

        self::assertSame($this->request, $result);
        self::assertEquals($publicKey, $this->request->getPublicKey());
    }

    public function testSetPublicKeyWithNull(): void
    {
        $this->request->setPublicKey('test-key');
        $result = $this->request->setPublicKey(null);

        self::assertSame($this->request, $result);
        self::assertNull($this->request->getPublicKey());
    }

    public function testSetAndGetClearingSystemId(): void
    {
        $clearingSystemId = '112233';

        $result = $this->request->setClearingSystemId($clearingSystemId);

        self::assertSame($this->request, $result);
        self::assertEquals($clearingSystemId, $this->request->getClearingSystemId());
    }

    public function testSetClearingSystemIdWithNull(): void
    {
        $this->request->setClearingSystemId('123456');
        $result = $this->request->setClearingSystemId(null);

        self::assertSame($this->request, $result);
        self::assertNull($this->request->getClearingSystemId());
    }

    public function testSetAndGetBic(): void
    {
        $bic = 'ABCDGB2LXXX';

        $result = $this->request->setBic($bic);

        self::assertSame($this->request, $result);
        self::assertEquals($bic, $this->request->getBic());
    }

    public function testSetBicWithNull(): void
    {
        $this->request->setBic('TESTGB2LXXX');
        $result = $this->request->setBic(null);

        self::assertSame($this->request, $result);
        self::assertNull($this->request->getBic());
    }

    public function testSetAndGetPublicKeyHint(): void
    {
        $publicKeyHint = ['start' => 'xjMEZ', 'end' => '=Wloc'];

        $result = $this->request->setPublicKeyHint($publicKeyHint);

        self::assertSame($this->request, $result);
        self::assertEquals($publicKeyHint, $this->request->getPublicKeyHint());
    }

    public function testSetPublicKeyHintWithNull(): void
    {
        $this->request->setPublicKeyHint(['start' => 'abc', 'end' => 'xyz']);
        $result = $this->request->setPublicKeyHint(null);

        self::assertSame($this->request, $result);
        self::assertNull($this->request->getPublicKeyHint());
    }

    public function testGetBodyWithRequiredFieldsOnly(): void
    {
        $expectedBody = [
            'encrypted_payload' => 'encrypted-payload-data',
            'node_id' => 'gb-node-01',
        ];

        $body = $this->request->getBody();

        self::assertEquals($expectedBody, $body);
    }

    public function testGetBodyWithAllOptionalFields(): void
    {
        $publicKey = '-----BEGIN PGP PUBLIC KEY BLOCK-----test-----END PGP PUBLIC KEY BLOCK-----';
        $clearingSystemId = '112233';
        $bic = 'ABCDGB2LXXX';
        $publicKeyHint = ['start' => 'xjMEZ', 'end' => '=Wloc'];

        $this->request
            ->setPublicKey($publicKey)
            ->setClearingSystemId($clearingSystemId)
            ->setBic($bic)
            ->setPublicKeyHint($publicKeyHint)
        ;

        $expectedBody = [
            'encrypted_payload' => 'encrypted-payload-data',
            'node_id' => 'gb-node-01',
            'public_key' => $publicKey,
            'clearing_system_id' => $clearingSystemId,
            'bic' => $bic,
            'public_key_hint' => $publicKeyHint,
        ];

        $body = $this->request->getBody();

        self::assertEquals($expectedBody, $body);
    }

    public function testGetBodyWithSomeOptionalFields(): void
    {
        $clearingSystemId = '445566';
        $publicKeyHint = ['start' => 'abc12', 'end' => '=xyz9'];

        $this->request
            ->setClearingSystemId($clearingSystemId)
            ->setPublicKeyHint($publicKeyHint)
        ;

        $expectedBody = [
            'encrypted_payload' => 'encrypted-payload-data',
            'node_id' => 'gb-node-01',
            'clearing_system_id' => $clearingSystemId,
            'public_key_hint' => $publicKeyHint,
        ];

        $body = $this->request->getBody();

        self::assertEquals($expectedBody, $body);
    }

    public function testGetBodyExcludesNullValues(): void
    {
        $this->request
            ->setPublicKey(null)
            ->setClearingSystemId('112233')
            ->setBic(null)
            ->setPublicKeyHint(null)
        ;

        $expectedBody = [
            'encrypted_payload' => 'encrypted-payload-data',
            'node_id' => 'gb-node-01',
            'clearing_system_id' => '112233',
        ];

        $body = $this->request->getBody();

        self::assertEquals($expectedBody, $body);
    }

    /**
     * @dataProvider getBodyDataProvider
     */
    public function testGetBodyWithVariousConfigurations(
        ?string $publicKey,
        ?string $clearingSystemId,
        ?string $bic,
        ?array $publicKeyHint,
        array $expectedBodyFields
    ): void {
        $this->request
            ->setPublicKey($publicKey)
            ->setClearingSystemId($clearingSystemId)
            ->setBic($bic)
            ->setPublicKeyHint($publicKeyHint)
        ;

        $body = $this->request->getBody();

        // Always includes required fields
        $expectedBody = [
            'encrypted_payload' => 'encrypted-payload-data',
            'node_id' => 'gb-node-01',
        ];

        // Add expected optional fields
        foreach ($expectedBodyFields as $field => $value) {
            $expectedBody[$field] = $value;
        }

        self::assertEquals($expectedBody, $body);
    }

    public function getBodyDataProvider(): array
    {
        return [
            'Only public key set' => [
                'publicKey' => 'test-public-key',
                'clearingSystemId' => null,
                'bic' => null,
                'publicKeyHint' => null,
                'expectedBodyFields' => ['public_key' => 'test-public-key'],
            ],
            'Only BIC set' => [
                'publicKey' => null,
                'clearingSystemId' => null,
                'bic' => 'TESTGB2LXXX',
                'publicKeyHint' => null,
                'expectedBodyFields' => ['bic' => 'TESTGB2LXXX'],
            ],
            'BIC and clearing system ID set' => [
                'publicKey' => null,
                'clearingSystemId' => '778899',
                'bic' => 'BANKGB2LXXX',
                'publicKeyHint' => null,
                'expectedBodyFields' => [
                    'clearing_system_id' => '778899',
                    'bic' => 'BANKGB2LXXX',
                ],
            ],
            'Public key hint only' => [
                'publicKey' => null,
                'clearingSystemId' => null,
                'bic' => null,
                'publicKeyHint' => ['start' => 'start5', 'end' => '=end5'],
                'expectedBodyFields' => [
                    'public_key_hint' => ['start' => 'start5', 'end' => '=end5'],
                ],
            ],
            'All fields set' => [
                'publicKey' => 'full-public-key',
                'clearingSystemId' => '999888',
                'bic' => 'FULLGB2LXXX',
                'publicKeyHint' => ['start' => 'fullS', 'end' => '=fullE'],
                'expectedBodyFields' => [
                    'public_key' => 'full-public-key',
                    'clearing_system_id' => '999888',
                    'bic' => 'FULLGB2LXXX',
                    'public_key_hint' => ['start' => 'fullS', 'end' => '=fullE'],
                ],
            ],
        ];
    }

    public function testFluentInterface(): void
    {
        $publicKey = 'test-key';
        $clearingSystemId = '123456';
        $bic = 'TESTGB2LXXX';
        $publicKeyHint = ['start' => 'test', 'end' => '=test'];

        $result = $this->request
            ->setPublicKey($publicKey)
            ->setClearingSystemId($clearingSystemId)
            ->setBic($bic)
            ->setPublicKeyHint($publicKeyHint)
        ;

        self::assertSame($this->request, $result);
        self::assertEquals($publicKey, $this->request->getPublicKey());
        self::assertEquals($clearingSystemId, $this->request->getClearingSystemId());
        self::assertEquals($bic, $this->request->getBic());
        self::assertEquals($publicKeyHint, $this->request->getPublicKeyHint());
    }

    public function testInitialOptionalValuesAreNull(): void
    {
        $request = new IpidValidationRequest('test-payload', 'test-node');

        self::assertNull($request->getPublicKey());
        self::assertNull($request->getClearingSystemId());
        self::assertNull($request->getBic());
        self::assertNull($request->getPublicKeyHint());
    }
} 