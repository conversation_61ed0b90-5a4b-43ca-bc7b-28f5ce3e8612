<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use PHPUnit\Framework\TestCase;

final class IpidCreditorAgentTest extends TestCase
{
    private IpidCreditorAgent $creditorAgent;

    protected function setUp(): void
    {
        $this->creditorAgent = new IpidCreditorAgent();
    }

    public function testSetAndGetBic(): void
    {
        $bic = 'DEUTDEFF500';

        $result = $this->creditorAgent->setBic($bic);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($bic, $this->creditorAgent->getBic());
    }

    public function testSetBicWithNull(): void
    {
        $this->creditorAgent->setBic('DEUTDEFF500');
        $result = $this->creditorAgent->setBic(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getBic());
    }

    public function testSetAndGetClearingSystemId(): void
    {
        $clearingSystemId = '*********';

        $result = $this->creditorAgent->setClearingSystemId($clearingSystemId);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($clearingSystemId, $this->creditorAgent->getClearingSystemId());
    }

    public function testSetClearingSystemIdWithNull(): void
    {
        $this->creditorAgent->setClearingSystemId('*********');
        $result = $this->creditorAgent->setClearingSystemId(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getClearingSystemId());
    }

    public function testSetAndGetBankName(): void
    {
        $bankName = 'Deutsche Bank AG';

        $result = $this->creditorAgent->setBankName($bankName);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($bankName, $this->creditorAgent->getBankName());
    }

    public function testSetBankNameWithNull(): void
    {
        $this->creditorAgent->setBankName('Deutsche Bank AG');
        $result = $this->creditorAgent->setBankName(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getBankName());
    }

    public function testSetAndGetBankAddress(): void
    {
        $bankAddress = 'Taunusanlage 12, 60325 Frankfurt am Main, Germany';

        $result = $this->creditorAgent->setBankAddress($bankAddress);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($bankAddress, $this->creditorAgent->getBankAddress());
    }

    public function testSetBankAddressWithNull(): void
    {
        $this->creditorAgent->setBankAddress('Frankfurt, Germany');
        $result = $this->creditorAgent->setBankAddress(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getBankAddress());
    }

    public function testSetAndGetPaymentSchemeEligibility(): void
    {
        $eligibility = 'SEPA_CREDIT_TRANSFER';

        $result = $this->creditorAgent->setPaymentSchemeEligibility($eligibility);

        self::assertSame($this->creditorAgent, $result);
        self::assertEquals($eligibility, $this->creditorAgent->getPaymentSchemeEligibility());
    }

    public function testSetPaymentSchemeEligibilityWithNull(): void
    {
        $this->creditorAgent->setPaymentSchemeEligibility('SEPA_CREDIT_TRANSFER');
        $result = $this->creditorAgent->setPaymentSchemeEligibility(null);

        self::assertSame($this->creditorAgent, $result);
        self::assertNull($this->creditorAgent->getPaymentSchemeEligibility());
    }

    public function testToArrayWithBicOnly(): void
    {
        $this->creditorAgent->setBic('DEUTDEFF500');

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithClearingSystemIdOnly(): void
    {
        $this->creditorAgent->setClearingSystemId('*********');

        $result = $this->creditorAgent->toArray();

        $expected = [
            'clearing_system_id' => '*********',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAllFields(): void
    {
        $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId('*********')
            ->setBankName('Deutsche Bank AG')
            ->setBankAddress('Frankfurt, Germany')
            ->setPaymentSchemeEligibility('SEPA_CREDIT_TRANSFER')
        ;

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
            'clearing_system_id' => '*********',
            'bank_name' => 'Deutsche Bank AG',
            'bank_address' => 'Frankfurt, Germany',
            'payment_scheme_eligibility' => 'SEPA_CREDIT_TRANSFER',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayExcludesNullValues(): void
    {
        $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId(null)
            ->setBankName('Deutsche Bank AG')
            ->setBankAddress(null)
            ->setPaymentSchemeEligibility(null)
        ;

        $result = $this->creditorAgent->toArray();

        $expected = [
            'bic' => 'DEUTDEFF500',
            'bank_name' => 'Deutsche Bank AG',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('clearing_system_id', $result);
        self::assertArrayNotHasKey('bank_address', $result);
        self::assertArrayNotHasKey('payment_scheme_eligibility', $result);
    }

    public function testToArrayWithEmptyAgent(): void
    {
        $result = $this->creditorAgent->toArray();

        self::assertEquals([], $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditorAgent
            ->setBic('DEUTDEFF500')
            ->setClearingSystemId('*********')
            ->setBankName('Deutsche Bank AG')
            ->setBankAddress('Frankfurt, Germany')
            ->setPaymentSchemeEligibility('SEPA_CREDIT_TRANSFER')
        ;

        self::assertSame($this->creditorAgent, $result);
    }

    public function testInitialState(): void
    {
        $creditorAgent = new IpidCreditorAgent();

        self::assertNull($creditorAgent->getBic());
        self::assertNull($creditorAgent->getClearingSystemId());
        self::assertNull($creditorAgent->getBankName());
        self::assertNull($creditorAgent->getBankAddress());
        self::assertNull($creditorAgent->getPaymentSchemeEligibility());
        self::assertEquals([], $creditorAgent->toArray());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray
    ): void {
        foreach ($setterCalls as $method => $value) {
            $this->creditorAgent->$method($value);
        }

        self::assertEquals($expectedArray, $this->creditorAgent->toArray(), "Failed for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'SEPA payment with BIC' => [
                'scenario' => 'SEPA payment with BIC',
                'setterCalls' => [
                    'setBic' => 'DEUTDEFF500',
                    'setBankName' => 'Deutsche Bank AG',
                ],
                'expectedArray' => [
                    'bic' => 'DEUTDEFF500',
                    'bank_name' => 'Deutsche Bank AG',
                ],
            ],
            'US ACH payment with routing number' => [
                'scenario' => 'US ACH payment with routing number',
                'setterCalls' => [
                    'setClearingSystemId' => '*********',
                    'setBankName' => 'Bank of America',
                    'setBankAddress' => 'Charlotte, NC, USA',
                ],
                'expectedArray' => [
                    'clearing_system_id' => '*********',
                    'bank_name' => 'Bank of America',
                    'bank_address' => 'Charlotte, NC, USA',
                ],
            ],
            'UK Faster Payments with sort code' => [
                'scenario' => 'UK Faster Payments with sort code',
                'setterCalls' => [
                    'setClearingSystemId' => '20-20-15',
                    'setBic' => 'BARCGB22',
                    'setBankName' => 'Barclays Bank PLC',
                ],
                'expectedArray' => [
                    'bic' => 'BARCGB22',
                    'clearing_system_id' => '20-20-15',
                    'bank_name' => 'Barclays Bank PLC',
                ],
            ],
            'Brazilian bank with comprehensive info' => [
                'scenario' => 'Brazilian bank with comprehensive info',
                'setterCalls' => [
                    'setClearingSystemId' => '001',
                    'setBankName' => 'Banco do Brasil S.A.',
                    'setBankAddress' => 'SBS Quadra 1, Brasília, DF, Brazil',
                    'setPaymentSchemeEligibility' => 'PIX_INSTANT_PAYMENT',
                ],
                'expectedArray' => [
                    'clearing_system_id' => '001',
                    'bank_name' => 'Banco do Brasil S.A.',
                    'bank_address' => 'SBS Quadra 1, Brasília, DF, Brazil',
                    'payment_scheme_eligibility' => 'PIX_INSTANT_PAYMENT',
                ],
            ],
            'Chinese bank with minimal info' => [
                'scenario' => 'Chinese bank with minimal info',
                'setterCalls' => [
                    'setBankName' => 'Industrial and Commercial Bank of China',
                ],
                'expectedArray' => [
                    'bank_name' => 'Industrial and Commercial Bank of China',
                ],
            ],
            'Singapore bank with BIC and scheme eligibility' => [
                'scenario' => 'Singapore bank with BIC and scheme eligibility',
                'setterCalls' => [
                    'setBic' => 'DBSSSGSG',
                    'setBankName' => 'DBS Bank Ltd',
                    'setPaymentSchemeEligibility' => 'PAYNOW_FAST_PAYMENT',
                ],
                'expectedArray' => [
                    'bic' => 'DBSSSGSG',
                    'bank_name' => 'DBS Bank Ltd',
                    'payment_scheme_eligibility' => 'PAYNOW_FAST_PAYMENT',
                ],
            ],
        ];
    }

    /**
     * @dataProvider bicFormatDataProvider
     */
    public function testBicFormatHandling(string $bic, array $expectedArray): void
    {
        $this->creditorAgent->setBic($bic);

        $result = $this->creditorAgent->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function bicFormatDataProvider(): array
    {
        return [
            '8-character BIC' => [
                'bic' => 'DEUTDEFF',
                'expectedArray' => ['bic' => 'DEUTDEFF'],
            ],
            '11-character BIC' => [
                'bic' => 'DEUTDEFF500',
                'expectedArray' => ['bic' => 'DEUTDEFF500'],
            ],
            'US BIC' => [
                'bic' => 'CHASUS33',
                'expectedArray' => ['bic' => 'CHASUS33'],
            ],
            'UK BIC' => [
                'bic' => 'BARCGB22',
                'expectedArray' => ['bic' => 'BARCGB22'],
            ],
            'Singapore BIC' => [
                'bic' => 'DBSSSGSG',
                'expectedArray' => ['bic' => 'DBSSSGSG'],
            ],
        ];
    }

    /**
     * @dataProvider clearingSystemIdDataProvider
     */
    public function testClearingSystemIdHandling(string $clearingSystemId, array $expectedArray): void
    {
        $this->creditorAgent->setClearingSystemId($clearingSystemId);

        $result = $this->creditorAgent->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function clearingSystemIdDataProvider(): array
    {
        return [
            'US routing number' => [
                'clearingSystemId' => '*********',
                'expectedArray' => ['clearing_system_id' => '*********'],
            ],
            'UK sort code' => [
                'clearingSystemId' => '20-20-15',
                'expectedArray' => ['clearing_system_id' => '20-20-15'],
            ],
            'Brazilian bank code' => [
                'clearingSystemId' => '001',
                'expectedArray' => ['clearing_system_id' => '001'],
            ],
            'Canadian institution number' => [
                'clearingSystemId' => '006',
                'expectedArray' => ['clearing_system_id' => '006'],
            ],
            'Australian BSB' => [
                'clearingSystemId' => '032-001',
                'expectedArray' => ['clearing_system_id' => '032-001'],
            ],
        ];
    }

    public function testComplexCombinationsOfFields(): void
    {
        // Test that various combinations work correctly
        $testCases = [
            ['setBic' => 'DEUTDEFF500', 'setClearingSystemId' => '********'],
            ['setBankName' => 'Test Bank', 'setBankAddress' => 'Test City'],
            ['setPaymentSchemeEligibility' => 'SCHEME_A'],
            ['setBic' => 'TEST1234', 'setBankName' => 'Test', 'setPaymentSchemeEligibility' => 'SCHEME_B'],
        ];

        foreach ($testCases as $index => $setterCalls) {
            $agent = new IpidCreditorAgent();

            foreach ($setterCalls as $method => $value) {
                $agent->$method($value);
            }

            $result = $agent->toArray();

            // Verify that the array contains exactly the expected keys
            $expectedKeys = array_map(
                fn($method) => $this->getExpectedArrayKey($method),
                array_keys($setterCalls)
            );

            $resultKeys = array_keys($result);
            sort($expectedKeys);
            sort($resultKeys);

            self::assertEquals(
                $expectedKeys,
                $resultKeys,
                "Test case $index failed: keys mismatch"
            );
        }
    }

    private function getExpectedArrayKey(string $method): string
    {
        $mapping = [
            'setBic' => 'bic',
            'setClearingSystemId' => 'clearing_system_id',
            'setBankName' => 'bank_name',
            'setBankAddress' => 'bank_address',
            'setPaymentSchemeEligibility' => 'payment_scheme_eligibility',
        ];

        return $mapping[$method];
    }
}

