<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use PHPUnit\Framework\TestCase;

final class IpidCreditorTest extends TestCase
{
    private IpidCreditor $creditor;

    protected function setUp(): void
    {
        $this->creditor = new IpidCreditor();
    }

    public function testSetAndGetName(): void
    {
        $name = '<PERSON>';

        $result = $this->creditor->setName($name);

        self::assertSame($this->creditor, $result);
        self::assertEquals($name, $this->creditor->getName());
    }

    public function testSetNameWithNull(): void
    {
        $this->creditor->setName('<PERSON>');
        $result = $this->creditor->setName(null);

        self::assertSame($this->creditor, $result);
        self::assertNull($this->creditor->getName());
    }

    public function testSetAndGetGivenName(): void
    {
        $givenName = 'John';

        $result = $this->creditor->setGivenName($givenName);

        self::assertSame($this->creditor, $result);
        self::assertEquals($givenName, $this->creditor->getGivenName());
    }

    public function testSetAndGetSurname(): void
    {
        $surname = 'Smith';

        $result = $this->creditor->setSurname($surname);

        self::assertSame($this->creditor, $result);
        self::assertEquals($surname, $this->creditor->getSurname());
    }

    public function testSetAndGetNameTranslation(): void
    {
        $nameTranslation = 'Johan Schmidt';

        $result = $this->creditor->setNameTranslation($nameTranslation);

        self::assertSame($this->creditor, $result);
        self::assertEquals($nameTranslation, $this->creditor->getNameTranslation());
    }

    public function testSetAndGetMsisdn(): void
    {
        $msisdn = '+***********';

        $result = $this->creditor->setMsisdn($msisdn);

        self::assertSame($this->creditor, $result);
        self::assertEquals($msisdn, $this->creditor->getMsisdn());
    }

    public function testSetAndGetEmail(): void
    {
        $email = '<EMAIL>';

        $result = $this->creditor->setEmail($email);

        self::assertSame($this->creditor, $result);
        self::assertEquals($email, $this->creditor->getEmail());
    }

    public function testSetAndGetIdentification(): void
    {
        $identification = [
            ['value' => '*********', 'type' => 'passport'],
            ['value' => '*********', 'type' => 'national_id'],
        ];

        $result = $this->creditor->setIdentification($identification);

        self::assertSame($this->creditor, $result);
        self::assertEquals($identification, $this->creditor->getIdentification());
    }

    public function testAddIdentification(): void
    {
        $result = $this->creditor->addIdentification('*********', 'passport');

        self::assertSame($this->creditor, $result);

        $identification = $this->creditor->getIdentification();
        self::assertCount(1, $identification);
        self::assertEquals(['value' => '*********', 'type' => 'passport'], $identification[0]);
    }

    public function testAddMultipleIdentifications(): void
    {
        $this->creditor
            ->addIdentification('*********', 'passport')
            ->addIdentification('*********', 'national_id')
        ;

        $identification = $this->creditor->getIdentification();
        self::assertCount(2, $identification);
        self::assertEquals(['value' => '*********', 'type' => 'passport'], $identification[0]);
        self::assertEquals(['value' => '*********', 'type' => 'national_id'], $identification[1]);
    }

    /**
     * @dataProvider hasNameOrGivenNameDataProvider
     */
    public function testHasNameOrGivenName(
        ?string $name,
        ?string $givenName,
        bool $expectedResult
    ): void {
        $this->creditor->setName($name)->setGivenName($givenName);

        $result = $this->creditor->hasNameOrGivenName();

        self::assertEquals($expectedResult, $result);
    }

    public function hasNameOrGivenNameDataProvider(): array
    {
        return [
            'Has name only' => ['John Smith', null, true],
            'Has given name only' => [null, 'John', true],
            'Has both name and given name' => ['John Smith', 'John', true],
            'Has neither name nor given name' => [null, null, false],
        ];
    }

    public function testToArrayWithAllFields(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->setGivenName('John')
            ->setSurname('Smith')
            ->setNameTranslation('Johan Schmidt')
            ->setMsisdn('+***********')
            ->setEmail('<EMAIL>')
            ->addIdentification('*********', 'passport')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
            'given_name' => 'John',
            'surname' => 'Smith',
            'name_translation' => 'Johan Schmidt',
            'msisdn' => '+***********',
            'email' => '<EMAIL>',
            'identification' => [
                ['value' => '*********', 'type' => 'passport'],
            ],
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithMinimalFields(): void
    {
        $this->creditor->setName('John Smith');

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayExcludesNullValues(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->setGivenName(null)
            ->setSurname(null)
            ->setMsisdn(null)
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('given_name', $result);
        self::assertArrayNotHasKey('surname', $result);
        self::assertArrayNotHasKey('msisdn', $result);
    }

    public function testToArrayWithEmptyIdentification(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->setIdentification([])
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('identification', $result);
    }

    public function testToArrayWithGivenNameAndSurname(): void
    {
        $this->creditor
            ->setGivenName('John')
            ->setSurname('Smith')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'given_name' => 'John',
            'surname' => 'Smith',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithNameTranslationOnly(): void
    {
        $this->creditor->setNameTranslation('Johan Schmidt');

        $result = $this->creditor->toArray();

        $expected = [
            'name_translation' => 'Johan Schmidt',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithContactInformationOnly(): void
    {
        $this->creditor
            ->setMsisdn('+***********')
            ->setEmail('<EMAIL>')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'msisdn' => '+***********',
            'email' => '<EMAIL>',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithMultipleIdentifications(): void
    {
        $this->creditor
            ->setName('John Smith')
            ->addIdentification('*********', 'passport')
            ->addIdentification('*********', 'national_id')
            ->addIdentification('*********', 'driver_license')
        ;

        $result = $this->creditor->toArray();

        $expected = [
            'name' => 'John Smith',
            'identification' => [
                ['value' => '*********', 'type' => 'passport'],
                ['value' => '*********', 'type' => 'national_id'],
                ['value' => '*********', 'type' => 'driver_license'],
            ],
        ];

        self::assertEquals($expected, $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditor
            ->setName('John Smith')
            ->setGivenName('John')
            ->setSurname('Smith')
            ->setNameTranslation('Johan Schmidt')
            ->setMsisdn('+***********')
            ->setEmail('<EMAIL>')
            ->addIdentification('*********', 'passport')
            ->setIdentification([['value' => '*********', 'type' => 'national_id']])
        ;

        self::assertSame($this->creditor, $result);
    }

    public function testInitialState(): void
    {
        $creditor = new IpidCreditor();

        self::assertNull($creditor->getName());
        self::assertNull($creditor->getGivenName());
        self::assertNull($creditor->getSurname());
        self::assertNull($creditor->getNameTranslation());
        self::assertNull($creditor->getMsisdn());
        self::assertNull($creditor->getEmail());
        self::assertEquals([], $creditor->getIdentification());
        self::assertFalse($creditor->hasNameOrGivenName());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray,
        bool $expectedHasName
    ): void {
        foreach ($setterCalls as $method => $value) {
            if ($method === 'addIdentification') {
                foreach ($value as $identification) {
                    $this->creditor->addIdentification($identification['value'], $identification['type']);
                }
            } else {
                $this->creditor->$method($value);
            }
        }

        self::assertEquals($expectedArray, $this->creditor->toArray(), "Failed for scenario: $scenario");
        self::assertEquals($expectedHasName, $this->creditor->hasNameOrGivenName(), "Failed hasNameOrGivenName for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'Individual with full name' => [
                'scenario' => 'Individual with full name',
                'setterCalls' => [
                    'setName' => 'John Smith',
                    'setEmail' => '<EMAIL>',
                ],
                'expectedArray' => [
                    'name' => 'John Smith',
                    'email' => '<EMAIL>',
                ],
                'expectedHasName' => true,
            ],
            'Individual with split name' => [
                'scenario' => 'Individual with split name',
                'setterCalls' => [
                    'setGivenName' => 'John',
                    'setSurname' => 'Smith',
                    'setMsisdn' => '+***********',
                ],
                'expectedArray' => [
                    'given_name' => 'John',
                    'surname' => 'Smith',
                    'msisdn' => '+***********',
                ],
                'expectedHasName' => true,
            ],
            'Business entity with identification' => [
                'scenario' => 'Business entity with identification',
                'setterCalls' => [
                    'setName' => 'ACME Corporation Ltd',
                    'addIdentification' => [
                        ['value' => '***********', 'type' => 'tax_id'],
                        ['value' => 'REG123456', 'type' => 'registration_number'],
                    ],
                ],
                'expectedArray' => [
                    'name' => 'ACME Corporation Ltd',
                    'identification' => [
                        ['value' => '***********', 'type' => 'tax_id'],
                        ['value' => 'REG123456', 'type' => 'registration_number'],
                    ],
                ],
                'expectedHasName' => true,
            ],
            'International person with translation' => [
                'scenario' => 'International person with translation',
                'setterCalls' => [
                    'setGivenName' => 'Zhang',
                    'setSurname' => 'Wei',
                    'setNameTranslation' => 'David Zhang',
                    'setMsisdn' => '+8613812345678',
                ],
                'expectedArray' => [
                    'given_name' => 'Zhang',
                    'surname' => 'Wei',
                    'name_translation' => 'David Zhang',
                    'msisdn' => '+8613812345678',
                ],
                'expectedHasName' => true,
            ],
        ];
    }
} 