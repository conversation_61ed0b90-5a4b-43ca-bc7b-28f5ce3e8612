<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use PHPUnit\Framework\TestCase;

final class IpidValidationPayloadTest extends TestCase
{
    private IpidCreditor $creditor;
    private IpidCreditorAccount $creditorAccount;
    private IpidCreditorAgent $creditorAgent;

    protected function setUp(): void
    {
        $this->creditor = self::createMock(IpidCreditor::class);
        $this->creditorAccount = self::createMock(IpidCreditorAccount::class);
        $this->creditorAgent = self::createMock(IpidCreditorAgent::class);
    }

    public function testConstructorAndGetters(): void
    {
        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        self::assertSame($this->creditor, $payload->getCreditor());
        self::assertSame($this->creditorAccount, $payload->getCreditorAccount());
        self::assertSame($this->creditorAgent, $payload->getCreditorAgent());
    }

    public function testValidateWithValidPayload(): void
    {
        $this->creditor
            ->expects(self::once())
            ->method('hasNameOrGivenName')
            ->willReturn(true)
        ;

        $this->creditorAccount
            ->expects(self::once())
            ->method('hasIbanOrAccountId')
            ->willReturn(true)
        ;

        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        // Should not throw exception
        $payload->validate();

        // If we reach here, validation passed
        self::assertTrue(true);
    }

    public function testValidateThrowsExceptionForMissingCreditorName(): void
    {
        $this->creditor
            ->expects(self::once())
            ->method('hasNameOrGivenName')
            ->willReturn(false)
        ;

        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Creditor must have either name or given_name');

        $payload->validate();
    }

    public function testValidateThrowsExceptionForMissingCreditorAccount(): void
    {
        $this->creditor
            ->expects(self::once())
            ->method('hasNameOrGivenName')
            ->willReturn(true)
        ;

        $this->creditorAccount
            ->expects(self::once())
            ->method('hasIbanOrAccountId')
            ->willReturn(false)
        ;

        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Creditor account must have either IBAN or account_id');

        $payload->validate();
    }

    public function testToArray(): void
    {
        $creditorArray = ['name' => 'John Smith'];
        $accountArray = ['iban' => '**********************'];
        $agentArray = ['bic' => 'WESTGB2L'];

        $this->creditor
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($creditorArray)
        ;

        $this->creditorAccount
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($accountArray)
        ;

        $this->creditorAgent
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($agentArray)
        ;

        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        $result = $payload->toArray();

        $expected = [
            'creditor' => $creditorArray,
            'creditor_account' => $accountArray,
            'creditor_agent' => $agentArray,
        ];

        self::assertEquals($expected, $result);
    }

    public function testToJson(): void
    {
        $creditorArray = ['name' => 'John Smith'];
        $accountArray = ['iban' => '**********************'];
        $agentArray = ['bic' => 'WESTGB2L'];

        $this->creditor
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($creditorArray)
        ;

        $this->creditorAccount
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($accountArray)
        ;

        $this->creditorAgent
            ->expects(self::once())
            ->method('toArray')
            ->willReturn($agentArray)
        ;

        $payload = new IpidValidationPayload(
            $this->creditor,
            $this->creditorAccount,
            $this->creditorAgent
        );

        $result = $payload->toJson();

        $expectedArray = [
            'creditor' => $creditorArray,
            'creditor_account' => $accountArray,
            'creditor_agent' => $agentArray,
        ];
        $expectedJson = json_encode($expectedArray, JSON_THROW_ON_ERROR);

        self::assertEquals($expectedJson, $result);
        self::assertJson($result);
    }

    /**
     * @dataProvider realWorldPayloadDataProvider
     */
    public function testRealWorldPayloadScenarios(
        string $scenario,
        array $creditorData,
        array $accountData,
        array $agentData,
        bool $shouldValidate
    ): void {
        $creditor = $this->createCreditorFromData($creditorData);
        $account = $this->createAccountFromData($accountData);
        $agent = $this->createAgentFromData($agentData);

        $payload = new IpidValidationPayload($creditor, $account, $agent);

        if ($shouldValidate) {
            $payload->validate();
            self::assertTrue(true, "Validation passed for scenario: $scenario");
        } else {
            $this->expectException(\InvalidArgumentException::class);
            $payload->validate();
        }

        $result = $payload->toArray();
        self::assertEquals($creditorData, $result['creditor']);
        self::assertEquals($accountData, $result['creditor_account']);
        self::assertEquals($agentData, $result['creditor_agent']);
    }

    public function realWorldPayloadDataProvider(): array
    {
        return [
            'Valid SEPA payment' => [
                'scenario' => 'Valid SEPA payment',
                'creditorData' => ['name' => 'John Smith'],
                'accountData' => ['iban' => '**********************', 'currency' => 'EUR'],
                'agentData' => ['bic' => 'DEUTDEFF500'],
                'shouldValidate' => true,
            ],
            'Valid US ACH payment' => [
                'scenario' => 'Valid US ACH payment',
                'creditorData' => ['given_name' => 'Jane', 'surname' => 'Doe'],
                'accountData' => ['account_id' => '*********', 'currency' => 'USD'],
                'agentData' => ['clearing_system_id' => '*********'],
                'shouldValidate' => true,
            ],
            'Valid Chinese payment' => [
                'scenario' => 'Valid Chinese payment',
                'creditorData' => ['name' => 'Beijing Technology Co Ltd'],
                'accountData' => ['account_id' => '6217000010012345678', 'currency' => 'CNY'],
                'agentData' => ['bank_name' => 'ICBC'],
                'shouldValidate' => true,
            ],
            'Invalid - no creditor name' => [
                'scenario' => 'Invalid - no creditor name',
                'creditorData' => [],
                'accountData' => ['iban' => '**********************'],
                'agentData' => ['bic' => 'WESTGB2L'],
                'shouldValidate' => false,
            ],
            'Invalid - no account information' => [
                'scenario' => 'Invalid - no account information',
                'creditorData' => ['name' => 'John Smith'],
                'accountData' => [],
                'agentData' => ['bic' => 'WESTGB2L'],
                'shouldValidate' => false,
            ],
        ];
    }

    public function testComplexPayloadWithAllFields(): void
    {
        $creditorData = [
            'name' => 'ACME Corporation Ltd',
            'given_name' => 'John',
            'surname' => 'Smith',
            'email' => '<EMAIL>',
            'identification' => [
                ['value' => '*********', 'type' => 'tax_id']
            ]
        ];

        $accountData = [
            'iban' => '**********************',
            'currency' => 'GBP'
        ];

        $agentData = [
            'bic' => 'WESTGB2L',
            'bank_name' => 'West Bank Ltd',
            'bank_address' => 'London, UK',
            'clearing_system_id' => '20-20-15'
        ];

        $creditor = $this->createCreditorFromData($creditorData);
        $account = $this->createAccountFromData($accountData);
        $agent = $this->createAgentFromData($agentData);

        $payload = new IpidValidationPayload($creditor, $account, $agent);

        // Should validate successfully
        $payload->validate();

        $result = $payload->toArray();
        self::assertEquals($creditorData, $result['creditor']);
        self::assertEquals($accountData, $result['creditor_account']);
        self::assertEquals($agentData, $result['creditor_agent']);

        // Test JSON serialization
        $json = $payload->toJson();
        $decoded = json_decode($json, true);
        self::assertEquals($result, $decoded);
    }

    public function testJsonSerializationWithSpecialCharacters(): void
    {
        $creditorData = ['name' => 'José María García-López'];
        $accountData = ['iban' => '************************'];
        $agentData = ['bank_name' => 'Banco Santander, S.A.'];

        $creditor = $this->createCreditorFromData($creditorData);
        $account = $this->createAccountFromData($accountData);
        $agent = $this->createAgentFromData($agentData);

        $payload = new IpidValidationPayload($creditor, $account, $agent);

        $json = $payload->toJson();
        $decoded = json_decode($json, true);

        self::assertIsArray($decoded);
        self::assertEquals($creditorData['name'], $decoded['creditor']['name']);
        self::assertEquals($accountData['iban'], $decoded['creditor_account']['iban']);
        self::assertEquals($agentData['bank_name'], $decoded['creditor_agent']['bank_name']);
    }

    public function testEmptyComponentsStillIncludedInArray(): void
    {
        $creditor = $this->createCreditorFromData(['name' => 'Test']);
        $account = $this->createAccountFromData(['iban' => '**********************']);
        $agent = $this->createAgentFromData([]);

        $payload = new IpidValidationPayload($creditor, $account, $agent);

        $result = $payload->toArray();

        self::assertArrayHasKey('creditor', $result);
        self::assertArrayHasKey('creditor_account', $result);
        self::assertArrayHasKey('creditor_agent', $result);
        self::assertEquals([], $result['creditor_agent']);
    }

    private function createCreditorFromData(array $data): IpidCreditor
    {
        $creditor = new IpidCreditor();

        if (isset($data['name'])) {
            $creditor->setName($data['name']);
        }
        if (isset($data['given_name'])) {
            $creditor->setGivenName($data['given_name']);
        }
        if (isset($data['surname'])) {
            $creditor->setSurname($data['surname']);
        }
        if (isset($data['email'])) {
            $creditor->setEmail($data['email']);
        }
        if (isset($data['identification'])) {
            foreach ($data['identification'] as $id) {
                $creditor->addIdentification($id['value'], $id['type']);
            }
        }

        return $creditor;
    }

    private function createAccountFromData(array $data): IpidCreditorAccount
    {
        $account = new IpidCreditorAccount();

        if (isset($data['iban'])) {
            $account->setIban($data['iban']);
        }
        if (isset($data['account_id'])) {
            $account->setAccountId($data['account_id']);
        }
        if (isset($data['currency'])) {
            $account->setCurrency($data['currency']);
        }

        return $account;
    }

    private function createAgentFromData(array $data): IpidCreditorAgent
    {
        $agent = new IpidCreditorAgent();

        if (isset($data['bic'])) {
            $agent->setBic($data['bic']);
        }
        if (isset($data['clearing_system_id'])) {
            $agent->setClearingSystemId($data['clearing_system_id']);
        }
        if (isset($data['bank_name'])) {
            $agent->setBankName($data['bank_name']);
        }
        if (isset($data['bank_address'])) {
            $agent->setBankAddress($data['bank_address']);
        }

        return $agent;
    }
} 