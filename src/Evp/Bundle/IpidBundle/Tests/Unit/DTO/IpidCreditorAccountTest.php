<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use PHPUnit\Framework\TestCase;

final class IpidCreditorAccountTest extends TestCase
{
    private IpidCreditorAccount $creditorAccount;

    protected function setUp(): void
    {
        $this->creditorAccount = new IpidCreditorAccount();
    }

    public function testSetAndGetIban(): void
    {
        $iban = '**********************';

        $result = $this->creditorAccount->setIban($iban);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($iban, $this->creditorAccount->getIban());
    }

    public function testSetIbanWithNull(): void
    {
        $this->creditorAccount->setIban('**********************');
        $result = $this->creditorAccount->setIban(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getIban());
    }

    public function testSetAndGetAccountId(): void
    {
        $accountId = '*********';

        $result = $this->creditorAccount->setAccountId($accountId);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($accountId, $this->creditorAccount->getAccountId());
    }

    public function testSetAccountIdWithNull(): void
    {
        $this->creditorAccount->setAccountId('*********');
        $result = $this->creditorAccount->setAccountId(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getAccountId());
    }

    public function testSetAndGetCurrency(): void
    {
        $currency = 'USD';

        $result = $this->creditorAccount->setCurrency($currency);

        self::assertSame($this->creditorAccount, $result);
        self::assertEquals($currency, $this->creditorAccount->getCurrency());
    }

    public function testSetCurrencyWithNull(): void
    {
        $this->creditorAccount->setCurrency('USD');
        $result = $this->creditorAccount->setCurrency(null);

        self::assertSame($this->creditorAccount, $result);
        self::assertNull($this->creditorAccount->getCurrency());
    }

    /**
     * @dataProvider hasIbanOrAccountIdDataProvider
     */
    public function testHasIbanOrAccountId(
        ?string $iban,
        ?string $accountId,
        bool $expectedResult
    ): void {
        $this->creditorAccount->setIban($iban)->setAccountId($accountId);

        $result = $this->creditorAccount->hasIbanOrAccountId();

        self::assertEquals($expectedResult, $result);
    }

    public function hasIbanOrAccountIdDataProvider(): array
    {
        return [
            'Has IBAN only' => ['**********************', null, true],
            'Has account ID only' => [null, '*********', true],
            'Has both IBAN and account ID' => ['**********************', '*********', true],
            'Has neither IBAN nor account ID' => [null, null, false],
        ];
    }

    public function testToArrayWithIbanOnly(): void
    {
        $this->creditorAccount->setIban('**********************');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAccountIdOnly(): void
    {
        $this->creditorAccount->setAccountId('*********');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'account_id' => '*********',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayPrioritizesIbanOverAccountId(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('account_id', $result);
    }

    public function testToArrayWithIbanAndCurrency(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setCurrency('GBP')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
            'currency' => 'GBP',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAccountIdAndCurrency(): void
    {
        $this->creditorAccount
            ->setAccountId('*********')
            ->setCurrency('USD')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'account_id' => '*********',
            'currency' => 'USD',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithAllFieldsButIbanTakesPriority(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
            ->setCurrency('GBP')
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
            'currency' => 'GBP',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('account_id', $result);
    }

    public function testToArrayWithNoAccountInformation(): void
    {
        $this->creditorAccount->setCurrency('EUR');

        $result = $this->creditorAccount->toArray();

        $expected = [
            'currency' => 'EUR',
        ];

        self::assertEquals($expected, $result);
    }

    public function testToArrayWithNullValues(): void
    {
        $this->creditorAccount
            ->setIban(null)
            ->setAccountId(null)
            ->setCurrency(null)
        ;

        $result = $this->creditorAccount->toArray();

        self::assertEquals([], $result);
    }

    public function testToArrayExcludesNullCurrency(): void
    {
        $this->creditorAccount
            ->setIban('**********************')
            ->setCurrency(null)
        ;

        $result = $this->creditorAccount->toArray();

        $expected = [
            'iban' => '**********************',
        ];

        self::assertEquals($expected, $result);
        self::assertArrayNotHasKey('currency', $result);
    }

    public function testFluentInterface(): void
    {
        $result = $this->creditorAccount
            ->setIban('**********************')
            ->setAccountId('*********')
            ->setCurrency('GBP')
        ;

        self::assertSame($this->creditorAccount, $result);
    }

    public function testInitialState(): void
    {
        $creditorAccount = new IpidCreditorAccount();

        self::assertNull($creditorAccount->getIban());
        self::assertNull($creditorAccount->getAccountId());
        self::assertNull($creditorAccount->getCurrency());
        self::assertFalse($creditorAccount->hasIbanOrAccountId());
        self::assertEquals([], $creditorAccount->toArray());
    }

    /**
     * @dataProvider businessScenarioDataProvider
     */
    public function testBusinessScenarios(
        string $scenario,
        array $setterCalls,
        array $expectedArray,
        bool $expectedHasAccount
    ): void {
        foreach ($setterCalls as $method => $value) {
            $this->creditorAccount->$method($value);
        }

        self::assertEquals($expectedArray, $this->creditorAccount->toArray(), "Failed for scenario: $scenario");
        self::assertEquals($expectedHasAccount, $this->creditorAccount->hasIbanOrAccountId(), "Failed hasIbanOrAccountId for scenario: $scenario");
    }

    public function businessScenarioDataProvider(): array
    {
        return [
            'SEPA IBAN payment' => [
                'scenario' => 'SEPA IBAN payment',
                'setterCalls' => [
                    'setIban' => '**********************',
                    'setCurrency' => 'EUR',
                ],
                'expectedArray' => [
                    'iban' => '**********************',
                    'currency' => 'EUR',
                ],
                'expectedHasAccount' => true,
            ],
            'US ACH payment' => [
                'scenario' => 'US ACH payment',
                'setterCalls' => [
                    'setAccountId' => '*********',
                    'setCurrency' => 'USD',
                ],
                'expectedArray' => [
                    'account_id' => '*********',
                    'currency' => 'USD',
                ],
                'expectedHasAccount' => true,
            ],
            'Chinese domestic payment' => [
                'scenario' => 'Chinese domestic payment',
                'setterCalls' => [
                    'setAccountId' => '6217000010012345678',
                    'setCurrency' => 'CNY',
                ],
                'expectedArray' => [
                    'account_id' => '6217000010012345678',
                    'currency' => 'CNY',
                ],
                'expectedHasAccount' => true,
            ],
            'Brazilian PIX payment' => [
                'scenario' => 'Brazilian PIX payment',
                'setterCalls' => [
                    'setAccountId' => '12345-6',
                    'setCurrency' => 'BRL',
                ],
                'expectedArray' => [
                    'account_id' => '12345-6',
                    'currency' => 'BRL',
                ],
                'expectedHasAccount' => true,
            ],
            'Account without currency' => [
                'scenario' => 'Account without currency',
                'setterCalls' => [
                    'setIban' => '***************************',
                ],
                'expectedArray' => [
                    'iban' => '***************************',
                ],
                'expectedHasAccount' => true,
            ],
            'IBAN override account ID' => [
                'scenario' => 'IBAN override account ID',
                'setterCalls' => [
                    'setAccountId' => '*********',
                    'setIban' => '***************************',
                    'setCurrency' => 'EUR',
                ],
                'expectedArray' => [
                    'iban' => '***************************',
                    'currency' => 'EUR',
                ],
                'expectedHasAccount' => true,
            ],
            'Empty account' => [
                'scenario' => 'Empty account',
                'setterCalls' => [],
                'expectedArray' => [],
                'expectedHasAccount' => false,
            ],
        ];
    }

    /**
     * @dataProvider currencyCodeScenarioDataProvider
     */
    public function testCurrencyCodeHandling(string $currency, array $expectedArray): void
    {
        $this->creditorAccount
            ->setAccountId('*********')
            ->setCurrency($currency)
        ;

        $result = $this->creditorAccount->toArray();

        self::assertEquals($expectedArray, $result);
    }

    public function currencyCodeScenarioDataProvider(): array
    {
        return [
            'USD currency' => [
                'currency' => 'USD',
                'expectedArray' => ['account_id' => '*********', 'currency' => 'USD'],
            ],
            'EUR currency' => [
                'currency' => 'EUR',
                'expectedArray' => ['account_id' => '*********', 'currency' => 'EUR'],
            ],
            'GBP currency' => [
                'currency' => 'GBP',
                'expectedArray' => ['account_id' => '*********', 'currency' => 'GBP'],
            ],
            'CNY currency' => [
                'currency' => 'CNY',
                'expectedArray' => ['account_id' => '*********', 'currency' => 'CNY'],
            ],
            'BRL currency' => [
                'currency' => 'BRL',
                'expectedArray' => ['account_id' => '*********', 'currency' => 'BRL'],
            ],
        ];
    }
} 