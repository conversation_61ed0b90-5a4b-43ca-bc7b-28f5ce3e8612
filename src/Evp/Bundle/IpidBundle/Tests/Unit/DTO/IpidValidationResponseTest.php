<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Unit\DTO;

use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use PHPUnit\Framework\TestCase;

final class IpidValidationResponseTest extends TestCase
{
    public function testConstructorAndGetters(): void
    {
        $responseCode = '2000';
        $responseMessage = 'Success';

        $response = new IpidValidationResponse($responseCode, $responseMessage);

        self::assertEquals($responseCode, $response->getResponseCode());
        self::assertEquals($responseMessage, $response->getResponseMessage());
    }

    public function testResponseCodeConstants(): void
    {
        // Success codes
        self::assertEquals('2000', IpidValidationResponse::CODE_SUCCESS);
        self::assertEquals('2001', IpidValidationResponse::CODE_FORMAT_SUCCESS);
        self::assertEquals('2002', IpidValidationResponse::CODE_ACCOUNT_ACTIVE);

        // Validation error codes
        self::assertEquals('2100', IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND);
        self::assertEquals('2101', IpidValidationResponse::CODE_ACCOUNT_FLAGGED);
        self::assertEquals('2102', IpidValidationResponse::CODE_AGENT_INVALID);
        self::assertEquals('2103', IpidValidationResponse::CODE_ACCOUNT_INVALID);
        self::assertEquals('2104', IpidValidationResponse::CODE_NAME_MATCH_FAILED);
        self::assertEquals('2105', IpidValidationResponse::CODE_FORMAT_VALIDATION_FAILED);
        self::assertEquals('2106', IpidValidationResponse::CODE_INVALID_REGISTRATION_ID);

        // Client error codes
        self::assertEquals('4001', IpidValidationResponse::CODE_NOT_AUTHORISED);
        self::assertEquals('4002', IpidValidationResponse::CODE_DECRYPTION_ERROR);
        self::assertEquals('4003', IpidValidationResponse::CODE_REQUEST_ERROR);
        self::assertEquals('4004', IpidValidationResponse::CODE_INVALID_CORRIDOR);
        self::assertEquals('4005', IpidValidationResponse::CODE_INSUFFICIENT_INPUTS);
        self::assertEquals('4006', IpidValidationResponse::CODE_RATE_LIMIT);

        // Server error codes
        self::assertEquals('5000', IpidValidationResponse::CODE_GENERAL_EXCEPTION);
        self::assertEquals('5001', IpidValidationResponse::CODE_INTERNAL_ERROR);
        self::assertEquals('5002', IpidValidationResponse::CODE_PROVIDER_ERROR);
    }

    public function testMatchLevelConstants(): void
    {
        self::assertEquals('Strong', IpidValidationResponse::MATCH_LEVEL_STRONG);
        self::assertEquals('Partial', IpidValidationResponse::MATCH_LEVEL_PARTIAL);
        self::assertEquals('Weak', IpidValidationResponse::MATCH_LEVEL_WEAK);
    }

    public function testSetAndGetEncryptedPayload(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $payload = 'encrypted-data-123';

        $result = $response->setEncryptedPayload($payload);

        self::assertSame($response, $result);
        self::assertEquals($payload, $response->getEncryptedPayload());
    }

    public function testSetAndGetMatchScore(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $score = 0.85;

        $result = $response->setMatchScore($score);

        self::assertSame($response, $result);
        self::assertEquals($score, $response->getMatchScore());
    }

    public function testSetAndGetMatchScoreDescription(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $description = 'Strong match';

        $result = $response->setMatchScoreDescription($description);

        self::assertSame($response, $result);
        self::assertEquals($description, $response->getMatchScoreDescription());
    }

    public function testSetAndGetLogicScore(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $score = 95;

        $result = $response->setLogicScore($score);

        self::assertSame($response, $result);
        self::assertEquals($score, $response->getLogicScore());
    }

    public function testSetAndGetLogicScoreDescription(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $description = 'High confidence';

        $result = $response->setLogicScoreDescription($description);

        self::assertSame($response, $result);
        self::assertEquals($description, $response->getLogicScoreDescription());
    }

    public function testSetAndGetPublicKeyHint(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $hint = ['fingerprint' => 'abc123', 'algorithm' => 'RSA'];

        $result = $response->setPublicKeyHint($hint);

        self::assertSame($response, $result);
        self::assertEquals($hint, $response->getPublicKeyHint());
    }

    public function testSetAndGetVopIdMatch(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $match = 'MATCH';

        $result = $response->setVopIdMatch($match);

        self::assertSame($response, $result);
        self::assertEquals($match, $response->getVopIdMatch());
    }

    public function testSetAndGetVopNameMatch(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $match = 'PARTIAL_MATCH';

        $result = $response->setVopNameMatch($match);

        self::assertSame($response, $result);
        self::assertEquals($match, $response->getVopNameMatch());
    }

    public function testSetAndGetCopMatched(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');

        $result = $response->setCopMatched(true);

        self::assertSame($response, $result);
        self::assertTrue($response->getCopMatched());

        $response->setCopMatched(false);
        self::assertFalse($response->getCopMatched());
    }

    public function testSetAndGetCopReason(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $reason = 'Account verification successful';

        $result = $response->setCopReason($reason);

        self::assertSame($response, $result);
        self::assertEquals($reason, $response->getCopReason());
    }

    public function testSetAndGetReasonCode(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $reasonCode = 'MATCH_SUCCESS';

        $result = $response->setReasonCode($reasonCode);

        self::assertSame($response, $result);
        self::assertEquals($reasonCode, $response->getReasonCode());
    }

    /**
     * @dataProvider isSuccessfulDataProvider
     */
    public function testIsSuccessful(string $responseCode, bool $expectedResult): void
    {
        $response = new IpidValidationResponse($responseCode, 'Test message');

        self::assertEquals($expectedResult, $response->isSuccessful());
    }

    public function isSuccessfulDataProvider(): array
    {
        return [
            'Success code' => [IpidValidationResponse::CODE_SUCCESS, true],
            'Format success code' => [IpidValidationResponse::CODE_FORMAT_SUCCESS, true],
            'Account active code' => [IpidValidationResponse::CODE_ACCOUNT_ACTIVE, true],
            'Account not found' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, false],
            'Name match failed' => [IpidValidationResponse::CODE_NAME_MATCH_FAILED, false],
            'Client error' => [IpidValidationResponse::CODE_NOT_AUTHORISED, false],
            'Server error' => [IpidValidationResponse::CODE_INTERNAL_ERROR, false],
        ];
    }

    /**
     * @dataProvider isValidationErrorDataProvider
     */
    public function testIsValidationError(string $responseCode, bool $expectedResult): void
    {
        $response = new IpidValidationResponse($responseCode, 'Test message');

        self::assertEquals($expectedResult, $response->isValidationError());
    }

    public function isValidationErrorDataProvider(): array
    {
        return [
            'Account not found' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, true],
            'Account flagged' => [IpidValidationResponse::CODE_ACCOUNT_FLAGGED, true],
            'Agent invalid' => [IpidValidationResponse::CODE_AGENT_INVALID, true],
            'Account invalid' => [IpidValidationResponse::CODE_ACCOUNT_INVALID, true],
            'Name match failed' => [IpidValidationResponse::CODE_NAME_MATCH_FAILED, true],
            'Format validation failed' => [IpidValidationResponse::CODE_FORMAT_VALIDATION_FAILED, true],
            'Invalid registration ID' => [IpidValidationResponse::CODE_INVALID_REGISTRATION_ID, true],
            'Success code' => [IpidValidationResponse::CODE_SUCCESS, false],
            'Client error' => [IpidValidationResponse::CODE_NOT_AUTHORISED, false],
            'Server error' => [IpidValidationResponse::CODE_INTERNAL_ERROR, false],
        ];
    }

    /**
     * @dataProvider isClientErrorDataProvider
     */
    public function testIsClientError(string $responseCode, bool $expectedResult): void
    {
        $response = new IpidValidationResponse($responseCode, 'Test message');

        self::assertEquals($expectedResult, $response->isClientError());
    }

    public function isClientErrorDataProvider(): array
    {
        return [
            'Not authorised' => [IpidValidationResponse::CODE_NOT_AUTHORISED, true],
            'Decryption error' => [IpidValidationResponse::CODE_DECRYPTION_ERROR, true],
            'Request error' => [IpidValidationResponse::CODE_REQUEST_ERROR, true],
            'Invalid corridor' => [IpidValidationResponse::CODE_INVALID_CORRIDOR, true],
            'Insufficient inputs' => [IpidValidationResponse::CODE_INSUFFICIENT_INPUTS, true],
            'Rate limit' => [IpidValidationResponse::CODE_RATE_LIMIT, true],
            'Success code' => [IpidValidationResponse::CODE_SUCCESS, false],
            'Validation error' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, false],
            'Server error' => [IpidValidationResponse::CODE_INTERNAL_ERROR, false],
        ];
    }

    /**
     * @dataProvider isServerErrorDataProvider
     */
    public function testIsServerError(string $responseCode, bool $expectedResult): void
    {
        $response = new IpidValidationResponse($responseCode, 'Test message');

        self::assertEquals($expectedResult, $response->isServerError());
    }

    public function isServerErrorDataProvider(): array
    {
        return [
            'General exception' => [IpidValidationResponse::CODE_GENERAL_EXCEPTION, true],
            'Internal error' => [IpidValidationResponse::CODE_INTERNAL_ERROR, true],
            'Provider error' => [IpidValidationResponse::CODE_PROVIDER_ERROR, true],
            'Success code' => [IpidValidationResponse::CODE_SUCCESS, false],
            'Validation error' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, false],
            'Client error' => [IpidValidationResponse::CODE_NOT_AUTHORISED, false],
        ];
    }

    /**
     * @dataProvider hasHighMatchDataProvider
     */
    public function testHasHighMatch(?float $matchScore, bool $expectedResult): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $response->setMatchScore($matchScore);

        self::assertEquals($expectedResult, $response->hasHighMatch());
    }

    public function hasHighMatchDataProvider(): array
    {
        return [
            'High match score' => [0.85, true],
            'Exactly 0.8' => [0.8, true],
            'Below threshold' => [0.75, false],
            'Low match score' => [0.3, false],
            'Null score' => [null, false],
        ];
    }

    /**
     * @dataProvider getMatchLevelDataProvider
     */
    public function testGetMatchLevel(?float $matchScore, string $expectedLevel): void
    {
        $response = new IpidValidationResponse('2000', 'Success');
        $response->setMatchScore($matchScore);

        self::assertEquals($expectedLevel, $response->getMatchLevel());
    }

    public function getMatchLevelDataProvider(): array
    {
        return [
            'Strong match' => [0.9, IpidValidationResponse::MATCH_LEVEL_STRONG],
            'Exactly strong threshold' => [0.8, IpidValidationResponse::MATCH_LEVEL_STRONG],
            'Partial match' => [0.6, IpidValidationResponse::MATCH_LEVEL_PARTIAL],
            'Exactly partial threshold' => [0.5, IpidValidationResponse::MATCH_LEVEL_PARTIAL],
            'Weak match' => [0.3, IpidValidationResponse::MATCH_LEVEL_WEAK],
            'Very low match' => [0.1, IpidValidationResponse::MATCH_LEVEL_WEAK],
            'Null score defaults to weak' => [null, IpidValidationResponse::MATCH_LEVEL_WEAK],
        ];
    }

    /**
     * @dataProvider getStatusDataProvider
     */
    public function testGetStatus(string $responseCode, string $expectedStatus): void
    {
        $response = new IpidValidationResponse($responseCode, 'Test message');

        self::assertEquals($expectedStatus, $response->getStatus());
    }

    public function getStatusDataProvider(): array
    {
        return [
            'Success response' => [IpidValidationResponse::CODE_SUCCESS, 'success'],
            'Format success' => [IpidValidationResponse::CODE_FORMAT_SUCCESS, 'success'],
            'Account active' => [IpidValidationResponse::CODE_ACCOUNT_ACTIVE, 'success'],
            'Validation error' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, 'error'],
            'Client error' => [IpidValidationResponse::CODE_NOT_AUTHORISED, 'error'],
            'Server error' => [IpidValidationResponse::CODE_INTERNAL_ERROR, 'error'],
        ];
    }

    /**
     * @dataProvider getRequiresConsentDataProvider
     */
    public function testGetRequiresConsent(
        string $responseCode,
        ?float $matchScore,
        bool $expectedRequiresConsent
    ): void {
        $response = new IpidValidationResponse($responseCode, 'Test message');
        if ($matchScore !== null) {
            $response->setMatchScore($matchScore);
        }

        self::assertEquals($expectedRequiresConsent, $response->getRequiresConsent());
    }

    public function getRequiresConsentDataProvider(): array
    {
        return [
            'Name match failed requires consent' => [IpidValidationResponse::CODE_NAME_MATCH_FAILED, null, true],
            'Account flagged requires consent' => [IpidValidationResponse::CODE_ACCOUNT_FLAGGED, null, true],
            'Low match score requires consent' => [IpidValidationResponse::CODE_SUCCESS, 0.7, true],
            'High match score no consent needed' => [IpidValidationResponse::CODE_SUCCESS, 0.9, false],
            'Success with null score no consent' => [IpidValidationResponse::CODE_SUCCESS, null, false],
            'Account not found no consent' => [IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND, null, false],
        ];
    }

    public function testFluentInterface(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');

        $result = $response
            ->setEncryptedPayload('payload')
            ->setMatchScore(0.85)
            ->setMatchScoreDescription('Strong')
            ->setLogicScore(95)
            ->setLogicScoreDescription('High confidence')
            ->setPublicKeyHint(['key' => 'value'])
            ->setVopIdMatch('MATCH')
            ->setVopNameMatch('PARTIAL')
            ->setCopMatched(true)
            ->setCopReason('Verified')
            ->setReasonCode('SUCCESS')
        ;

        self::assertSame($response, $result);
    }

    public function testInitialStateWithNullValues(): void
    {
        $response = new IpidValidationResponse('2000', 'Success');

        self::assertNull($response->getEncryptedPayload());
        self::assertNull($response->getMatchScore());
        self::assertNull($response->getMatchScoreDescription());
        self::assertNull($response->getLogicScore());
        self::assertNull($response->getLogicScoreDescription());
        self::assertNull($response->getPublicKeyHint());
        self::assertNull($response->getVopIdMatch());
        self::assertNull($response->getVopNameMatch());
        self::assertNull($response->getCopMatched());
        self::assertNull($response->getCopReason());
        self::assertNull($response->getReasonCode());
    }

    /**
     * @dataProvider realWorldScenarioDataProvider
     */
    public function testRealWorldScenarios(
        string $scenario,
        string $responseCode,
        string $responseMessage,
        ?float $matchScore,
        array $expectedResults
    ): void {
        $response = new IpidValidationResponse($responseCode, $responseMessage);
        if ($matchScore !== null) {
            $response->setMatchScore($matchScore);
        }

        foreach ($expectedResults as $method => $expectedValue) {
            self::assertEquals(
                $expectedValue,
                $response->$method(),
                "Failed for method $method in scenario: $scenario"
            );
        }
    }

    public function realWorldScenarioDataProvider(): array
    {
        return [
            'Successful SEPA validation with high match' => [
                'scenario' => 'Successful SEPA validation with high match',
                'responseCode' => IpidValidationResponse::CODE_SUCCESS,
                'responseMessage' => 'Account verified successfully',
                'matchScore' => 0.95,
                'expectedResults' => [
                    'isSuccessful' => true,
                    'isValidationError' => false,
                    'isClientError' => false,
                    'isServerError' => false,
                    'hasHighMatch' => true,
                    'getMatchLevel' => IpidValidationResponse::MATCH_LEVEL_STRONG,
                    'getStatus' => 'success',
                    'getRequiresConsent' => false,
                ],
            ],
            'Name mismatch requiring manual review' => [
                'scenario' => 'Name mismatch requiring manual review',
                'responseCode' => IpidValidationResponse::CODE_NAME_MATCH_FAILED,
                'responseMessage' => 'Name does not match account holder',
                'matchScore' => 0.45,
                'expectedResults' => [
                    'isSuccessful' => false,
                    'isValidationError' => true,
                    'isClientError' => false,
                    'isServerError' => false,
                    'hasHighMatch' => false,
                    'getMatchLevel' => IpidValidationResponse::MATCH_LEVEL_WEAK,
                    'getStatus' => 'error',
                    'getRequiresConsent' => true,
                ],
            ],
            'Account not found error' => [
                'scenario' => 'Account not found error',
                'responseCode' => IpidValidationResponse::CODE_ACCOUNT_NOT_FOUND,
                'responseMessage' => 'The specified account could not be found',
                'matchScore' => null,
                'expectedResults' => [
                    'isSuccessful' => false,
                    'isValidationError' => true,
                    'isClientError' => false,
                    'isServerError' => false,
                    'hasHighMatch' => false,
                    'getMatchLevel' => IpidValidationResponse::MATCH_LEVEL_WEAK,
                    'getStatus' => 'error',
                    'getRequiresConsent' => false,
                ],
            ],
            'Client authentication error' => [
                'scenario' => 'Client authentication error',
                'responseCode' => IpidValidationResponse::CODE_NOT_AUTHORISED,
                'responseMessage' => 'Invalid API credentials',
                'matchScore' => null,
                'expectedResults' => [
                    'isSuccessful' => false,
                    'isValidationError' => false,
                    'isClientError' => true,
                    'isServerError' => false,
                    'hasHighMatch' => false,
                    'getMatchLevel' => IpidValidationResponse::MATCH_LEVEL_WEAK,
                    'getStatus' => 'error',
                    'getRequiresConsent' => false,
                ],
            ],
            'Server internal error' => [
                'scenario' => 'Server internal error',
                'responseCode' => IpidValidationResponse::CODE_INTERNAL_ERROR,
                'responseMessage' => 'Internal server error occurred',
                'matchScore' => null,
                'expectedResults' => [
                    'isSuccessful' => false,
                    'isValidationError' => false,
                    'isClientError' => false,
                    'isServerError' => true,
                    'hasHighMatch' => false,
                    'getMatchLevel' => IpidValidationResponse::MATCH_LEVEL_WEAK,
                    'getStatus' => 'error',
                    'getRequiresConsent' => false,
                ],
            ],
        ];
    }
} 