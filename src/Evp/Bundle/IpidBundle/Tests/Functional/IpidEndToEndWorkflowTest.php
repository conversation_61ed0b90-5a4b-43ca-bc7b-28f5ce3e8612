<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Functional;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\Service\Cache\IpidCacheManager;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use Evp\Bundle\IpidBundle\Service\IpidCountryChecker;
use Evp\Bundle\IpidBundle\Service\IpidCountryRegistry;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;

final class IpidEndToEndWorkflowTest extends TestCase
{
    private IpidTransferHelper $transferHelper;
    private IpidCountryChecker $countryChecker;
    private IpidCacheManager $cacheManager;

    protected function setUp(): void
    {
        $this->transferHelper = new IpidTransferHelper();
        $this->countryChecker = $this->createCountryChecker();

        // Create cache manager with mock repository
        $mockApiResponseRepo = self::createMock(\Evp\Bundle\IpidBundle\Repository\IpidApiResponseRepository::class);
        $this->cacheManager = new IpidCacheManager($mockApiResponseRepo, new NullLogger());
    }

    /**
     * @dataProvider endToEndWorkflowScenarios
     */
    public function testCompleteWorkflowDataFlow(
        string $scenario,
        array $transferData,
        array $expectedFlow
    ): void {
        $transfer = $this->createTransferFromData($transferData);

        // Step 1: Extract country from transfer
        $extractedCountry = $this->transferHelper->extractCountryFromTransfer($transfer);
        self::assertEquals($expectedFlow['country'], $extractedCountry, "Country extraction failed for: $scenario");

        // Step 2: Check country support
        $isSupported = $this->countryChecker->isCountryEnabled($extractedCountry);
        self::assertEquals($expectedFlow['is_supported'], $isSupported, "Country support check failed for: $scenario");

        // Step 3: Get validation scheme
        $validationScheme = $this->countryChecker->getValidationSchemeForCountry($extractedCountry);
        self::assertEquals($expectedFlow['validation_scheme'], $validationScheme, "Validation scheme detection failed for: $scenario");

        // Step 4: Test hash generation consistency
        $request = $this->createMockRequest($transfer);
        $hash1 = $this->cacheManager->calculateRequestHash($request, $transfer);
        $hash2 = $this->cacheManager->calculateRequestHash($request, $transfer);

        self::assertEquals($hash1, $hash2, "Hash generation should be deterministic for: $scenario");
        self::assertEquals(64, strlen($hash1), "Hash should be 64 characters (SHA256) for: $scenario");
        self::assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $hash1, "Hash should be valid hexadecimal for: $scenario");

        // Step 5: Verify workflow decision points
        if ($expectedFlow['is_supported']) {
            self::assertTrue($this->countryChecker->isCountryEnabled($extractedCountry), "Should support validation for: $scenario");

            if ($expectedFlow['validation_scheme'] === 'COP') {
                self::assertTrue($this->countryChecker->isCopCountry($extractedCountry), "Should be COP country for: $scenario");
                self::assertFalse($this->countryChecker->isVopCountry($extractedCountry), "Should not be VOP country for: $scenario");
            } elseif ($expectedFlow['validation_scheme'] === 'VOP') {
                self::assertTrue($this->countryChecker->isVopCountry($extractedCountry), "Should be VOP country for: $scenario");
                self::assertFalse($this->countryChecker->isCopCountry($extractedCountry), "Should not be COP country for: $scenario");
            }
        } else {
            self::assertFalse($this->countryChecker->isCountryEnabled($extractedCountry), "Should not support validation for: $scenario");
        }
    }

    public function endToEndWorkflowScenarios(): array
    {
        return [
            'UK IBAN - Complete COP workflow' => [
                'scenario' => 'UK IBAN - Complete COP workflow',
                'transferData' => [
                    'iban' => '**********************',
                    'beneficiary_name' => 'John Smith',
                    'transfer_id' => 12345,
                ],
                'expectedFlow' => [
                    'country' => 'GB',
                    'is_supported' => true,
                    'validation_scheme' => 'COP',
                    'region' => 'UK',
                ],
            ],
            'US account - Complete VOP workflow' => [
                'scenario' => 'US account - Complete VOP workflow',
                'transferData' => [
                    'iban' => '********************',
                    'beneficiary_name' => 'Jane Doe',
                    'transfer_id' => 67890,
                ],
                'expectedFlow' => [
                    'country' => 'US',
                    'is_supported' => true,
                    'validation_scheme' => 'VOP',
                    'region' => 'US',
                ],
            ],
            'French IBAN - EU COP workflow' => [
                'scenario' => 'French IBAN - EU COP workflow',
                'transferData' => [
                    'iban' => '***************************',
                    'beneficiary_name' => 'Pierre Dubois',
                    'transfer_id' => 11111,
                ],
                'expectedFlow' => [
                    'country' => 'FR',
                    'is_supported' => true,
                    'validation_scheme' => 'COP',
                    'region' => 'EU',
                ],
            ],
            'Unsupported country workflow' => [
                'scenario' => 'Unsupported country workflow',
                'transferData' => [
                    'iban' => '********************',
                    'beneficiary_name' => 'Tanaka San',
                    'transfer_id' => 88888,
                ],
                'expectedFlow' => [
                    'country' => 'JP',
                    'is_supported' => false,
                    'validation_scheme' => 'GLOBAL',
                    'region' => null,
                ],
            ],
        ];
    }

    public function testCacheHashConsistencyAcrossTransfers(): void
    {
        // Test that different transfers with same data produce same hash
        $transferData = [
            'iban' => '**********************',
            'beneficiary_name' => 'Hash Test User',
            'transfer_id' => 55555,
        ];

        $transfer1 = $this->createTransferFromData($transferData);
        $transfer2 = $this->createTransferFromData($transferData);

        $request1 = $this->createMockRequest($transfer1);
        $request2 = $this->createMockRequest($transfer2);

        $hash1 = $this->cacheManager->calculateRequestHash($request1, $transfer1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2, $transfer2);

        self::assertEquals($hash1, $hash2, 'Same transfer data should produce same hash');
    }

    public function testCacheHashUniquenessAcrossTransfers(): void
    {
        // Test that different transfers produce different hashes
        $transfer1 = $this->createTransferFromData([
            'iban' => '**********************',
            'beneficiary_name' => 'User One',
            'transfer_id' => 11111,
        ]);

        $transfer2 = $this->createTransferFromData([
            'iban' => '***************************',
            'beneficiary_name' => 'User Two',
            'transfer_id' => 22222,
        ]);

        $request1 = $this->createMockRequest($transfer1);
        $request2 = $this->createMockRequest($transfer2);

        $hash1 = $this->cacheManager->calculateRequestHash($request1, $transfer1);
        $hash2 = $this->cacheManager->calculateRequestHash($request2, $transfer2);

        self::assertNotEquals($hash1, $hash2, 'Different transfers should produce different hashes');
    }

    public function testServiceCoordinationWorkflow(): void
    {
        // Test complete service coordination for a realistic scenario
        $transfer = $this->createTransferFromData([
            'iban' => '**********************',
            'beneficiary_name' => 'Service Coordination Test',
            'transfer_id' => 77777,
        ]);

        // Extract and validate country
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        self::assertEquals('DE', $country);

        // Check support and get scheme
        $isSupported = $this->countryChecker->isCountryEnabled($country);
        $scheme = $this->countryChecker->getValidationSchemeForCountry($country);
        $region = $this->countryChecker->getRegionForCountry($country);

        self::assertTrue($isSupported);
        self::assertEquals('COP', $scheme);
        self::assertEquals('EU', $region);

        // Generate request hash
        $request = $this->createMockRequest($transfer);
        $hash = $this->cacheManager->calculateRequestHash($request, $transfer);

        self::assertNotEmpty($hash);
        self::assertEquals(64, strlen($hash));

        // Verify service coordination results
        self::assertTrue($this->countryChecker->isCopCountry($country));
        self::assertTrue($this->countryChecker->isCountryEnabled($country));
    }

    // Helper methods

    private function createTransferFromData(array $data): TransferOut
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn($data['transfer_id']);

        $beneficiary = self::createMock(PartyIban::class);
        $beneficiary->method('getIban')->willReturn($data['iban']);
        $beneficiary->method('getName')->willReturn($data['beneficiary_name']);

        $transfer->method('getBeneficiary')->willReturn($beneficiary);
        return $transfer;
    }

    private function createCountryChecker(): IpidCountryChecker
    {
        $countryRegistry = self::createMock(IpidCountryRegistry::class);

        $countryRegistry->method('isCountryEnabled')->willReturnCallback(function ($country) {
            return in_array($country, ['GB', 'US', 'FR', 'DE', 'BE'], true);
        });

        $countryRegistry->method('getValidationSchemeForCountry')->willReturnCallback(function ($country) {
            switch ($country) {
                case 'GB': return 'COP';
                case 'US': return 'VOP';
                case 'FR':
                case 'DE':
                case 'BE': return 'COP';
                default: return 'GLOBAL';
            }
        });

        $countryRegistry->method('getRegionForCountry')->willReturnCallback(function ($country) {
            switch ($country) {
                case 'GB': return 'UK';
                case 'US': return 'US';
                case 'FR':
                case 'DE':
                case 'BE': return 'EU';
                default: return null;
            }
        });

        return new IpidCountryChecker($countryRegistry);
    }

    private function createMockRequest(TransferOut $transfer): IpidValidationRequest
    {
        $request = self::createMock(IpidValidationRequest::class);
        $request->method('getEncryptedPayload')->willReturn('test-payload-' . $transfer->getId());
        $request->method('getNodeId')->willReturn('test-node-id');
        $request->method('getPublicKey')->willReturn('test-public-key');
        $request->method('getClearingSystemId')->willReturn('test-clearing-system');
        $request->method('getBic')->willReturn('TESTBIC1');
        return $request;
    }
}
