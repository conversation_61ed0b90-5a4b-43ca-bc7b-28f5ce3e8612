<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\Tests\Functional;

use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\IpidBundle\DTO\IpidCreditor;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAccount;
use Evp\Bundle\IpidBundle\DTO\IpidCreditorAgent;
use Evp\Bundle\IpidBundle\DTO\IpidValidationPayload;
use Evp\Bundle\IpidBundle\DTO\IpidValidationRequest;
use Evp\Bundle\IpidBundle\DTO\IpidValidationResponse;
use Evp\Bundle\IpidBundle\Service\IpidCorridorSpecification;
use Evp\Bundle\IpidBundle\Service\IpidEncryptionService;
use Evp\Bundle\IpidBundle\Service\IpidNodeSelector;
use Evp\Bundle\IpidBundle\Service\IpidRequestBuilder;
use Evp\Bundle\IpidBundle\Service\IpidTransferHelper;
use Evp\Bundle\IpidBundle\Service\IpidValidationResult;
use PHPUnit\Framework\TestCase;

final class IpidValidationFunctionalTest extends TestCase
{
    private IpidTransferHelper $transferHelper;
    private IpidNodeSelector $nodeSelector;
    private IpidCorridorSpecification $corridorSpec;

    protected function setUp(): void
    {
        // Create real service instances to test integration
        $this->transferHelper = new IpidTransferHelper();
        $this->nodeSelector = new IpidNodeSelector();
        $this->corridorSpec = new IpidCorridorSpecification();
    }

    /**
     * @dataProvider realWorldIntegrationDataProvider
     */
    public function testRealWorldIntegration(
        string $scenario,
        array $transferData,
        array $expectedResults
    ): void {
        $transfer = $this->createTransferFromData($transferData);

        // Test transfer helper integration
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        $beneficiaryName = $this->transferHelper->getBeneficiaryName($transfer);
        $beneficiaryAccount = $this->transferHelper->getBeneficiaryAccount($transfer);
        $beneficiaryType = $this->transferHelper->getBeneficiaryType($transfer);

        // Test node selector integration
        $nodeId = $this->nodeSelector->getNodeIdForCountry($country, $beneficiaryType);

        // Test corridor specification integration
        $isSupported = $this->corridorSpec->isCountrySupported($country);
        $shouldSplitName = $this->corridorSpec->shouldSplitName($country);
        $preferredAccountType = $this->corridorSpec->getPreferredAccountType($country);

        // Verify all results match expectations
        self::assertEquals($expectedResults['country'], $country, "Country extraction failed for: $scenario");
        self::assertEquals($expectedResults['beneficiary_name'], $beneficiaryName, "Name extraction failed for: $scenario");
        self::assertEquals($expectedResults['beneficiary_account'], $beneficiaryAccount, "Account extraction failed for: $scenario");
        self::assertEquals($expectedResults['beneficiary_type'], $beneficiaryType, "Type detection failed for: $scenario");
        self::assertEquals($expectedResults['node_id'], $nodeId, "Node selection failed for: $scenario");
        self::assertEquals($expectedResults['is_supported'], $isSupported, "Support check failed for: $scenario");
        self::assertEquals($expectedResults['should_split_name'], $shouldSplitName, "Name splitting rule failed for: $scenario");
        self::assertEquals($expectedResults['preferred_account_type'], $preferredAccountType, "Account type preference failed for: $scenario");
    }

    public function realWorldIntegrationDataProvider(): array
    {
        return [
            'US ACH payment integration' => [
                'scenario' => 'US ACH payment integration',
                'transferData' => [
                    'id' => 12345,
                    'beneficiary_type' => 'bank',
                    'account_number' => '*********',
                    'sort_code' => '*********',
                    'bic' => 'CHASUS33', // Valid US BIC for country extraction
                    'beneficiary_name' => 'John Smith',
                    'inn_code' => null,
                ],
                'expectedResults' => [
                    'country' => 'US',
                    'beneficiary_name' => 'John Smith',
                    'beneficiary_account' => '*********',
                    'beneficiary_type' => 'individual',
                    'node_id' => IpidNodeSelector::US_LOGIC_NODE,
                    'is_supported' => true,
                    'should_split_name' => true,
                    'preferred_account_type' => 'account_id',
                ],
            ],
            'German SEPA payment integration' => [
                'scenario' => 'German SEPA payment integration',
                'transferData' => [
                    'id' => 67890,
                    'beneficiary_type' => 'iban',
                    'iban' => '**********************',
                    'beneficiary_name' => 'Hans Mueller',
                ],
                'expectedResults' => [
                    'country' => 'DE',
                    'beneficiary_name' => 'Hans Mueller',
                    'beneficiary_account' => '**********************',
                    'beneficiary_type' => 'individual',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                    'is_supported' => false, // DE not in supported list
                    'should_split_name' => false,
                    'preferred_account_type' => 'flexible',
                ],
            ],
            'Chinese business payment integration' => [
                'scenario' => 'Chinese business payment integration',
                'transferData' => [
                    'id' => 54321,
                    'beneficiary_type' => 'bank',
                    'account_number' => '6217000010012345678',
                    'bic' => 'ICBKCNBJ', // Valid Chinese BIC for country extraction
                    'beneficiary_name' => 'Beijing Technology Co Ltd',
                    'inn_code' => '91110000*********X',
                ],
                'expectedResults' => [
                    'country' => 'CN',
                    'beneficiary_name' => 'Beijing Technology Co Ltd',
                    'beneficiary_account' => '6217000010012345678',
                    'beneficiary_type' => 'business',
                    'node_id' => IpidNodeSelector::CN_BUSINESS_NODE,
                    'is_supported' => true,
                    'should_split_name' => false,
                    'preferred_account_type' => 'account_id',
                ],
            ],
            'UK Faster Payment integration' => [
                'scenario' => 'UK Faster Payment integration',
                'transferData' => [
                    'id' => 98765,
                    'beneficiary_type' => 'iban',
                    'iban' => '**********************',
                    'beneficiary_name' => 'James Wilson',
                ],
                'expectedResults' => [
                    'country' => 'GB',
                    'beneficiary_name' => 'James Wilson',
                    'beneficiary_account' => '**********************',
                    'beneficiary_type' => 'individual',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                    'is_supported' => true,
                    'should_split_name' => false,
                    'preferred_account_type' => 'flexible',
                ],
            ],
            'Belgium SEPA business payment' => [
                'scenario' => 'Belgium SEPA business payment',
                'transferData' => [
                    'id' => 11111,
                    'beneficiary_type' => 'iban',
                    'iban' => '****************',
                    'beneficiary_name' => 'Brussels Consulting SPRL',
                ],
                'expectedResults' => [
                    'country' => 'BE',
                    'beneficiary_name' => 'Brussels Consulting SPRL',
                    'beneficiary_account' => '****************',
                    'beneficiary_type' => 'individual',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                    'is_supported' => true,
                    'should_split_name' => false,
                    'preferred_account_type' => 'iban',
                ],
            ],
            'French international transfer' => [
                'scenario' => 'French international transfer',
                'transferData' => [
                    'id' => 22222,
                    'beneficiary_type' => 'iban',
                    'iban' => '***************************',
                    'beneficiary_name' => 'Marie Dubois-Laurent',
                ],
                'expectedResults' => [
                    'country' => 'FR',
                    'beneficiary_name' => 'Marie Dubois-Laurent',
                    'beneficiary_account' => '***************************',
                    'beneficiary_type' => 'individual',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                    'is_supported' => true,
                    'should_split_name' => false,
                    'preferred_account_type' => 'flexible',
                ],
            ],
        ];
    }

    /**
     * @dataProvider complexBusinessScenarioDataProvider
     */
    public function testComplexBusinessScenarios(
        string $scenario,
        array $transferData,
        array $expectedBusinessLogic
    ): void {
        $transfer = $this->createTransferFromData($transferData);
        
        // Test complex business logic integration
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        $personCode = $this->transferHelper->extractPersonCodeFromTransfer($transfer);
        $beneficiaryType = $this->transferHelper->getBeneficiaryType($transfer);
        
        // Test corridor-specific business rules
        $identityType = $this->corridorSpec->getIdentificationTypeForCountry($country);
        $isSupported = $this->corridorSpec->isCountrySupported($country);
        
        // Test node selection with business logic
        $nodeId = $this->nodeSelector->getNodeIdForCountry($country, $beneficiaryType);
        
        // Generate request hash for caching logic
        $hashData = [
            'country' => $country,
            'account' => $this->transferHelper->getBeneficiaryAccount($transfer),
            'name' => $this->transferHelper->getBeneficiaryName($transfer),
            'person_code' => $personCode,
        ];
        $requestHash = $this->transferHelper->generateRequestHash($hashData);
        
        // Verify business logic
        self::assertEquals($expectedBusinessLogic['country'], $country, "Country failed for: $scenario");
        self::assertEquals($expectedBusinessLogic['person_code'], $personCode, "Person code failed for: $scenario");
        self::assertEquals($expectedBusinessLogic['beneficiary_type'], $beneficiaryType, "Type failed for: $scenario");
        self::assertEquals($expectedBusinessLogic['identity_type'], $identityType, "Identity type failed for: $scenario");
        self::assertEquals($expectedBusinessLogic['is_supported'], $isSupported, "Support failed for: $scenario");
        self::assertEquals($expectedBusinessLogic['node_id'], $nodeId, "Node selection failed for: $scenario");
        self::assertIsString($requestHash, "Hash generation failed for: $scenario");
        self::assertEquals(64, strlen($requestHash), "Hash length invalid for: $scenario");
    }

    public function complexBusinessScenarioDataProvider(): array
    {
        return [
            'US corporate entity with tax ID' => [
                'scenario' => 'US corporate entity with tax ID',
                'transferData' => [
                    'id' => 33333,
                    'beneficiary_type' => 'bank',
                    'account_number' => '*********',
                    'bic' => 'BOFAUS3N',
                    'beneficiary_name' => 'TechCorp LLC',
                    'inn_code' => '12-3456789', // US EIN format
                ],
                'expectedBusinessLogic' => [
                    'country' => 'US',
                    'person_code' => '12-3456789',
                    'beneficiary_type' => 'business',
                    'identity_type' => 'TAX_ID',
                    'is_supported' => true,
                    'node_id' => IpidNodeSelector::US_LOGIC_NODE,
                ],
            ],
            'Chinese state-owned enterprise' => [
                'scenario' => 'Chinese state-owned enterprise',
                'transferData' => [
                    'id' => 44444,
                    'beneficiary_type' => 'bank',
                    'account_number' => '6225*********012345',
                    'bic' => 'ICBKCNBJ',
                    'beneficiary_name' => 'China Construction Bank',
                    'inn_code' => '911100001000000001',
                    'kpp_code' => '*********',
                ],
                'expectedBusinessLogic' => [
                    'country' => 'CN',
                    'person_code' => '911100001000000001',
                    'beneficiary_type' => 'business',
                    'identity_type' => 'REGISTRATION_ID',
                    'is_supported' => true,
                    'node_id' => IpidNodeSelector::CN_BUSINESS_NODE,
                ],
            ],
            'Belgian individual with hyphenated name' => [
                'scenario' => 'Belgian individual with hyphenated name',
                'transferData' => [
                    'id' => 55555,
                    'beneficiary_type' => 'iban',
                    'iban' => '****************',
                    'beneficiary_name' => 'Jean-Baptiste Van Der Berg',
                ],
                'expectedBusinessLogic' => [
                    'country' => 'BE',
                    'person_code' => null,
                    'beneficiary_type' => 'individual',
                    'identity_type' => 'REGISTRATION_ID',
                    'is_supported' => true,
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                ],
            ],
        ];
    }

    /**
     * @dataProvider errorHandlingScenarioDataProvider
     */
    public function testErrorHandlingScenarios(
        string $scenario,
        array $transferData,
        string $expectedException,
        string $expectedMessage
    ): void {
        $transfer = $this->createTransferFromData($transferData);
        
        $this->expectException($expectedException);
        $this->expectExceptionMessage($expectedMessage);
        
        // This should trigger the expected exception
        $this->transferHelper->extractCountryFromTransfer($transfer);
    }

    public function errorHandlingScenarioDataProvider(): array
    {
        return [
            'Invalid BIC code too short' => [
                'scenario' => 'Invalid BIC code too short',
                'transferData' => [
                    'id' => 66666,
                    'beneficiary_type' => 'bank',
                    'account_number' => '*********',
                    'bic' => 'ABC', // Too short to be valid
                    'beneficiary_name' => 'Test User',
                ],
                'expectedException' => \InvalidArgumentException::class,
                'expectedMessage' => 'Cannot determine country from transfer beneficiary data.',
            ],
            'Missing IBAN for PartyIban' => [
                'scenario' => 'Missing IBAN for PartyIban',
                'transferData' => [
                    'id' => 77777,
                    'beneficiary_type' => 'iban',
                    'iban' => '', // Empty IBAN
                    'beneficiary_name' => 'Test User',
                ],
                'expectedException' => \InvalidArgumentException::class,
                'expectedMessage' => 'Cannot determine country from transfer beneficiary data.',
            ],
            'Invalid routing number format' => [
                'scenario' => 'Invalid routing number format',
                'transferData' => [
                    'id' => 88888,
                    'beneficiary_type' => 'bank',
                    'account_number' => '*********',
                    'bic' => '1234', // Too short to be valid (less than 6 characters)
                    'beneficiary_name' => 'Test User',
                ],
                'expectedException' => \InvalidArgumentException::class,
                'expectedMessage' => 'Cannot determine country from transfer beneficiary data.',
            ],
        ];
    }

    public function testCompleteWorkflowIntegration(): void
    {
        // Test complete workflow for a typical SEPA payment
        $transfer = $this->createTransferFromData([
            'id' => 99999,
            'beneficiary_type' => 'iban',
            'iban' => '***************************',
            'beneficiary_name' => 'Jean Baptiste',
        ]);

        // Extract transfer data
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        $beneficiaryName = $this->transferHelper->getBeneficiaryName($transfer);
        $beneficiaryAccount = $this->transferHelper->getBeneficiaryAccount($transfer);
        $beneficiaryType = $this->transferHelper->getBeneficiaryType($transfer);

        // Apply business rules
        $nodeId = $this->nodeSelector->getNodeIdForCountry($country, $beneficiaryType);
        $shouldSplitName = $this->corridorSpec->shouldSplitName($country);
        $preferredAccountType = $this->corridorSpec->getPreferredAccountType($country);
        $isSupported = $this->corridorSpec->isCountrySupported($country);

        // Generate request hash
        $requestData = [
            'country' => $country,
            'account' => $beneficiaryAccount,
            'name' => $beneficiaryName,
            'node_id' => $nodeId,
        ];
        $requestHash = $this->transferHelper->generateRequestHash($requestData);

        // Verify complete workflow
        self::assertEquals('FR', $country);
        self::assertEquals('Jean Baptiste', $beneficiaryName);
        self::assertEquals('***************************', $beneficiaryAccount);
        self::assertEquals('individual', $beneficiaryType);
        self::assertEquals(IpidNodeSelector::IBAN_LOGIC_NODE, $nodeId);
        self::assertFalse($shouldSplitName);
        self::assertEquals('flexible', $preferredAccountType);
        self::assertTrue($isSupported);
        self::assertIsString($requestHash);
        self::assertEquals(64, strlen($requestHash));

        // Verify this demonstrates end-to-end integration
        self::assertTrue(true, 'Complete workflow integration test passed');
    }

    public function testDTOIntegrationWorkflow(): void
    {
        // Test complete DTO integration workflow
        $transfer = $this->createTransferFromData([
            'id' => 11223,
            'beneficiary_type' => 'bank',
            'account_number' => '4000*********',
            'bic' => 'CHASUS33',
            'beneficiary_name' => 'Michael Johnson',
            'inn_code' => '12-3456789',
        ]);

        // Extract data for DTO creation
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        $beneficiaryName = $this->transferHelper->getBeneficiaryName($transfer);
        $beneficiaryAccount = $this->transferHelper->getBeneficiaryAccount($transfer);
        $beneficiaryBic = $this->transferHelper->getBeneficiaryBankBic($transfer);
        $sortCode = $this->transferHelper->getBeneficiarySortCode($transfer);
        $personCode = $this->transferHelper->extractPersonCodeFromTransfer($transfer);

        // Create and test DTO integration
        $creditor = new IpidCreditor();
        $creditor->setName($beneficiaryName);
        if ($personCode) {
            $creditor->addIdentification($personCode, 'TAX_ID');
        }

        $creditorAccount = new IpidCreditorAccount();
        $creditorAccount->setAccountId($beneficiaryAccount);
        $creditorAccount->setCurrency('USD');

        $creditorAgent = new IpidCreditorAgent();
        if ($beneficiaryBic) {
            $creditorAgent->setBic($beneficiaryBic);
        }
        if ($sortCode) {
            $creditorAgent->setClearingSystemId($sortCode);
        }

        $payload = new IpidValidationPayload($creditor, $creditorAccount, $creditorAgent);

        // Create validation request
        $nodeId = $this->nodeSelector->getNodeIdForCountry($country, 'business');
        $request = new IpidValidationRequest('mock-encrypted-payload', $nodeId);
        $request->setBic($beneficiaryBic);

        // Verify DTO integration
        self::assertEquals('US', $country);
        self::assertEquals('Michael Johnson', $creditor->getName());
        self::assertCount(1, $creditor->getIdentification());
        self::assertEquals('4000*********', $creditorAccount->getAccountId());
        self::assertEquals('USD', $creditorAccount->getCurrency());
        self::assertEquals('CHASUS33', $creditorAgent->getBic());
        $actualSortCode = $creditorAgent->getClearingSystemId();
        self::assertTrue($actualSortCode === null || $actualSortCode === '*********', 'Sort code should be null or the expected value');
        self::assertEquals(IpidNodeSelector::US_LOGIC_NODE, $request->getNodeId());
        self::assertEquals('CHASUS33', $request->getBic());

        // Test DTO array conversion
        $creditorArray = $creditor->toArray();
        $accountArray = $creditorAccount->toArray();
        $agentArray = $creditorAgent->toArray();

        self::assertArrayHasKey('name', $creditorArray);
        self::assertArrayHasKey('identification', $creditorArray);
        self::assertArrayHasKey('account_id', $accountArray);
        self::assertArrayHasKey('currency', $accountArray);
        self::assertArrayHasKey('bic', $agentArray);
        if ($creditorAgent->getClearingSystemId() !== null) {
            self::assertArrayHasKey('clearing_system_id', $agentArray);
        }
    }

    public function testValidationResponseIntegration(): void
    {
        // Test response processing workflow
        $responses = [
            new IpidValidationResponse('2000', 'Account verified successfully'),
            new IpidValidationResponse('2104', 'Name match below threshold'),
            new IpidValidationResponse('2100', 'Account not found'),
            new IpidValidationResponse('4001', 'Invalid request format'),
        ];

        // Test response 1: Success
        $response1 = $responses[0];
        $response1->setMatchScore(0.95);
        $response1->setVopNameMatch('EXACT_MATCH');
        $response1->setEncryptedPayload('success-response-data');

        self::assertTrue($response1->isSuccessful());
        self::assertFalse($response1->isValidationError());
        self::assertFalse($response1->getRequiresConsent());
        self::assertEquals('Strong', $response1->getMatchLevel());

        // Test response 2: Partial match requiring consent
        $response2 = $responses[1];
        $response2->setMatchScore(0.65);
        $response2->setVopNameMatch('PARTIAL_MATCH');

        self::assertFalse($response2->isSuccessful());
        self::assertTrue($response2->isValidationError());
        self::assertTrue($response2->getRequiresConsent());
        self::assertEquals('Partial', $response2->getMatchLevel());

        // Test response 3: Account not found
        $response3 = $responses[2];
        self::assertFalse($response3->isSuccessful());
        self::assertTrue($response3->isValidationError());
        self::assertFalse($response3->getRequiresConsent());

        // Test response 4: System error
        $response4 = $responses[3];
        self::assertFalse($response4->isSuccessful());
        self::assertFalse($response4->isValidationError());
        self::assertTrue($response4->isClientError());
    }

    public function testValidationResultFactoryMethods(): void
    {
        // Test IpidValidationResult factory methods and business logic
        
        // Test success result - create mock objects
        $mockConsent = self::createMock(\Evp\Bundle\IpidBundle\Entity\TransferIpidConsent::class);
        $mockApiResponse = self::createMock(\Evp\Bundle\IpidBundle\Entity\IpidApiResponse::class);
        
        $successResult = IpidValidationResult::createSuccess($mockConsent, $mockApiResponse);
        self::assertTrue($successResult->isSuccessful());
        self::assertFalse($successResult->isError());
        self::assertEquals('success', $successResult->getStatus());
        
        // Test error result
        $errorResult = IpidValidationResult::createError('Validation failed');
        self::assertFalse($errorResult->isSuccessful());
        self::assertTrue($errorResult->isError());
        self::assertEquals('error', $errorResult->getStatus());
        self::assertEquals('Validation failed', $errorResult->getErrorMessage());
        
        // Test unsupported country result
        $unsupportedResult = IpidValidationResult::createUnsupportedCountry('XX');
        self::assertFalse($unsupportedResult->isSuccessful());
        self::assertTrue($unsupportedResult->isUnsupportedCountry());
        self::assertEquals('unsupported_country', $unsupportedResult->getStatus());
        self::assertStringContainsString('XX', $unsupportedResult->getErrorMessage());
    }

    /**
     * @dataProvider multiCurrencyScenarioDataProvider
     */
    public function testMultiCurrencyValidationScenarios(
        string $currency,
        array $transferData,
        array $expectedCurrencyLogic
    ): void {
        $transferData['currency'] = $currency;
        $transfer = $this->createTransferFromData($transferData);
        
        // Test currency extraction
        $extractedCurrency = $this->transferHelper->getCurrency($transfer);
        $country = $this->transferHelper->extractCountryFromTransfer($transfer);
        
        // Test currency-specific business rules
        $preferredAccountType = $this->corridorSpec->getPreferredAccountType($country);
        $nodeId = $this->nodeSelector->getNodeIdForCountry($country, 'individual');
        
        // Verify currency handling
        self::assertEquals($expectedCurrencyLogic['currency'], $extractedCurrency, "Currency extraction failed");
        self::assertEquals($expectedCurrencyLogic['country'], $country, "Country extraction failed");
        self::assertEquals($expectedCurrencyLogic['preferred_account_type'], $preferredAccountType, "Account type failed");
        self::assertEquals($expectedCurrencyLogic['node_id'], $nodeId, "Node selection failed");
    }

    public function multiCurrencyScenarioDataProvider(): array
    {
        return [
            'EUR payment to Germany' => [
                'currency' => 'EUR',
                'transferData' => [
                    'id' => 1001,
                    'beneficiary_type' => 'iban',
                    'iban' => '**********************',
                    'beneficiary_name' => 'Klaus Weber',
                ],
                'expectedCurrencyLogic' => [
                    'currency' => 'EUR',
                    'country' => 'DE',
                    'preferred_account_type' => 'flexible',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                ],
            ],
            'GBP payment to UK' => [
                'currency' => 'GBP',
                'transferData' => [
                    'id' => 1002,
                    'beneficiary_type' => 'iban',
                    'iban' => '**********************',
                    'beneficiary_name' => 'David Thompson',
                ],
                'expectedCurrencyLogic' => [
                    'currency' => 'GBP',
                    'country' => 'GB',
                    'preferred_account_type' => 'flexible',
                    'node_id' => IpidNodeSelector::IBAN_LOGIC_NODE,
                ],
            ],
            'CNY payment to China' => [
                'currency' => 'CNY',
                'transferData' => [
                    'id' => 1003,
                    'beneficiary_type' => 'bank',
                    'account_number' => '6217000010012345678',
                    'bic' => 'ICBKCNBJ',
                    'beneficiary_name' => 'Li Wei',
                ],
                'expectedCurrencyLogic' => [
                    'currency' => 'CNY',
                    'country' => 'CN',
                    'preferred_account_type' => 'account_id',
                    'node_id' => IpidNodeSelector::CN_INDIVIDUAL_NODE,
                ],
            ],
            'USD payment to US' => [
                'currency' => 'USD',
                'transferData' => [
                    'id' => 1004,
                    'beneficiary_type' => 'bank',
                    'account_number' => '*********',
                    'bic' => 'CHASUS33',
                    'beneficiary_name' => 'Sarah Connor',
                ],
                'expectedCurrencyLogic' => [
                    'currency' => 'USD',
                    'country' => 'US',
                    'preferred_account_type' => 'account_id',
                    'node_id' => IpidNodeSelector::US_LOGIC_NODE,
                ],
            ],
        ];
    }

    public function testHashGenerationConsistencyAndSecurity(): void
    {
        // Test hash generation consistency across different data orders
        $data1 = [
            'account' => '*********',
            'name' => 'John Smith',
            'country' => 'US',
            'node_id' => 'us-node-02',
        ];
        $data2 = [
            'country' => 'US',
            'name' => 'John Smith',
            'account' => '*********',
            'node_id' => 'us-node-02',
        ];

        $hash1 = $this->transferHelper->generateRequestHash($data1);
        $hash2 = $this->transferHelper->generateRequestHash($data2);

        self::assertEquals($hash1, $hash2, 'Hash should be consistent regardless of data order');
        self::assertEquals(64, strlen($hash1), 'Hash should be 64 characters (SHA256)');

        // Test hash uniqueness
        $data3 = $data1;
        $data3['account'] = '*********'; // Change one field
        $hash3 = $this->transferHelper->generateRequestHash($data3);

        self::assertNotEquals($hash1, $hash3, 'Different data should produce different hashes');

        // Test special characters handling
        $data4 = [
            'account' => '***************************',
            'name' => 'Jean-Baptiste Müller',
            'country' => 'FR',
            'special_chars' => 'äöü@#$%^&*()',
        ];
        $hash4 = $this->transferHelper->generateRequestHash($data4);

        self::assertEquals(64, strlen($hash4), 'Hash should handle special characters correctly');
        self::assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $hash4, 'Hash should be valid hexadecimal');
    }

    public function testEdgeCasesAndBoundaryConditions(): void
    {
        // Test very long names
        $longNameTransfer = $this->createTransferFromData([
            'id' => 9001,
            'beneficiary_type' => 'iban',
            'iban' => '**********************',
            'beneficiary_name' => str_repeat('A', 200), // Very long name
        ]);

        $extractedName = $this->transferHelper->getBeneficiaryName($longNameTransfer);
        self::assertEquals(200, strlen($extractedName), 'Should handle very long names');

        // Test minimum valid IBAN
        $minIbanTransfer = $this->createTransferFromData([
            'id' => 9002,
            'beneficiary_type' => 'iban',
            'iban' => '************************', // Andorra IBAN (shortest valid IBAN)
            'beneficiary_name' => 'Test User',
        ]);

        $country = $this->transferHelper->extractCountryFromTransfer($minIbanTransfer);
        self::assertEquals('AD', $country, 'Should extract country from minimum valid IBAN');

        // Test maximum valid BIC
        $maxBicTransfer = $this->createTransferFromData([
            'id' => 9003,
            'beneficiary_type' => 'bank',
            'account_number' => '*********',
            'bic' => 'DEUTDEFFXXX', // 11-character BIC
            'beneficiary_name' => 'Test User',
        ]);

        $country = $this->transferHelper->extractCountryFromTransfer($maxBicTransfer);
        self::assertEquals('DE', $country, 'Should extract country from 11-character BIC');

        // Test empty string handling
        $emptyFieldsTransfer = $this->createTransferFromData([
            'id' => 9004,
            'beneficiary_type' => 'bank',
            'account_number' => '',
            'bic' => 'CHASUS33',
            'beneficiary_name' => '',
        ]);

        $name = $this->transferHelper->getBeneficiaryName($emptyFieldsTransfer);
        $account = $this->transferHelper->getBeneficiaryAccount($emptyFieldsTransfer);
        self::assertEquals('', $name, 'Should handle empty name gracefully');
        self::assertEquals('', $account, 'Should handle empty account gracefully');
    }

    private function createTransferFromData(array $data): TransferOut
    {
        $transfer = self::createMock(TransferOut::class);
        $transfer->method('getId')->willReturn($data['id']);

        if ($data['beneficiary_type'] === 'iban') {
            $beneficiary = self::createMock(PartyIban::class);
            $beneficiary->method('getIban')->willReturn($data['iban']);
            $beneficiary->method('getName')->willReturn($data['beneficiary_name'] ?? 'Test User');
        } else {
            $beneficiary = self::createMock(PartyBank::class);
            $beneficiary->method('getDisplayAccount')->willReturn($data['account_number'] ?? '*********');
            $beneficiary->method('getBankCode')->willReturn($data['sort_code'] ?? null);
            $beneficiary->method('getBic')->willReturn($data['bic'] ?? $data['sort_code'] ?? null);
            $beneficiary->method('getInnCode')->willReturn($data['inn_code'] ?? null);
            $beneficiary->method('getKppCode')->willReturn($data['kpp_code'] ?? null);
            $beneficiary->method('getDisplayName')->willReturn($data['beneficiary_name'] ?? 'Test User');
        }
        
        $transfer->method('getBeneficiary')->willReturn($beneficiary);

        // Setup currency if provided
        if (isset($data['currency'])) {
            $amountMoney = new class($data['currency']) {
                private $currency;
                public function __construct(string $currency) { $this->currency = $currency; }
                public function getCurrency(): string { return $this->currency; }
            };
            $transfer->method('getAmountMoney')->willReturn($amountMoney);
        }

        return $transfer;
    }
} 