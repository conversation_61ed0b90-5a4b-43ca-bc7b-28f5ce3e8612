<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidValidationResponse
{
    public const CODE_SUCCESS = '2000';
    public const CODE_FORMAT_SUCCESS = '2001';
    public const CODE_ACCOUNT_ACTIVE = '2002';
    public const CODE_ACCOUNT_NOT_FOUND = '2100';
    public const CODE_ACCOUNT_FLAGGED = '2101';
    public const CODE_AGENT_INVALID = '2102';
    public const CODE_ACCOUNT_INVALID = '2103';
    public const CODE_NAME_MATCH_FAILED = '2104';
    public const CODE_FORMAT_VALIDATION_FAILED = '2105';
    public const CODE_INVALID_REGISTRATION_ID = '2106';

    public const CODE_NOT_AUTHORISED = '4001';
    public const CODE_DECRYPTION_ERROR = '4002';
    public const CODE_REQUEST_ERROR = '4003';
    public const CODE_INVALID_CORRIDOR = '4004';
    public const CODE_INSUFFICIENT_INPUTS = '4005';
    public const CODE_RATE_LIMIT = '4006';

    public const CODE_GENERAL_EXCEPTION = '5000';
    public const CODE_INTERNAL_ERROR = '5001';
    public const CODE_PROVIDER_ERROR = '5002';

    public const MATCH_LEVEL_STRONG = 'Strong';
    public const MATCH_LEVEL_PARTIAL = 'Partial';
    public const MATCH_LEVEL_WEAK = 'Weak';

    private string $responseCode;
    private string $responseMessage;
    private ?string $encryptedPayload = null;
    private ?float $matchScore = null;
    private ?string $matchScoreDescription = null;
    private ?int $logicScore = null;
    private ?string $logicScoreDescription = null;
    private ?array $publicKeyHint = null;

    private ?string $vopIdMatch = null;
    private ?string $vopNameMatch = null;
    private ?bool $copMatched = null;
    private ?string $copReason = null;
    private ?string $reasonCode = null;

    public function __construct(string $responseCode, string $responseMessage)
    {
        $this->responseCode = $responseCode;
        $this->responseMessage = $responseMessage;
    }

    public function getResponseCode(): string
    {
        return $this->responseCode;
    }

    public function getResponseMessage(): string
    {
        return $this->responseMessage;
    }

    public function getEncryptedPayload(): ?string
    {
        return $this->encryptedPayload;
    }

    public function setEncryptedPayload(?string $encryptedPayload): self
    {
        $this->encryptedPayload = $encryptedPayload;
        return $this;
    }

    public function getMatchScore(): ?float
    {
        return $this->matchScore;
    }

    public function setMatchScore(?float $matchScore): self
    {
        $this->matchScore = $matchScore;
        return $this;
    }

    public function getMatchScoreDescription(): ?string
    {
        return $this->matchScoreDescription;
    }

    public function setMatchScoreDescription(?string $matchScoreDescription): self
    {
        $this->matchScoreDescription = $matchScoreDescription;
        return $this;
    }

    public function getLogicScore(): ?int
    {
        return $this->logicScore;
    }

    public function setLogicScore(?int $logicScore): self
    {
        $this->logicScore = $logicScore;
        return $this;
    }

    public function getLogicScoreDescription(): ?string
    {
        return $this->logicScoreDescription;
    }

    public function setLogicScoreDescription(?string $logicScoreDescription): self
    {
        $this->logicScoreDescription = $logicScoreDescription;
        return $this;
    }

    public function getPublicKeyHint(): ?array
    {
        return $this->publicKeyHint;
    }

    public function setPublicKeyHint(?array $publicKeyHint): self
    {
        $this->publicKeyHint = $publicKeyHint;
        return $this;
    }

    public function getVopIdMatch(): ?string
    {
        return $this->vopIdMatch;
    }

    public function setVopIdMatch(?string $vopIdMatch): self
    {
        $this->vopIdMatch = $vopIdMatch;
        return $this;
    }

    public function getVopNameMatch(): ?string
    {
        return $this->vopNameMatch;
    }

    public function setVopNameMatch(?string $vopNameMatch): self
    {
        $this->vopNameMatch = $vopNameMatch;
        return $this;
    }

    public function getCopMatched(): ?bool
    {
        return $this->copMatched;
    }

    public function setCopMatched(?bool $copMatched): self
    {
        $this->copMatched = $copMatched;
        return $this;
    }

    public function getCopReason(): ?string
    {
        return $this->copReason;
    }

    public function setCopReason(?string $copReason): self
    {
        $this->copReason = $copReason;
        return $this;
    }

    public function getReasonCode(): ?string
    {
        return $this->reasonCode;
    }

    public function setReasonCode(?string $reasonCode): self
    {
        $this->reasonCode = $reasonCode;
        return $this;
    }

    public function isSuccessful(): bool
    {
        return in_array($this->responseCode, [
            self::CODE_SUCCESS,
            self::CODE_FORMAT_SUCCESS,
            self::CODE_ACCOUNT_ACTIVE
        ], true);
    }

    public function isValidationError(): bool
    {
        return str_starts_with($this->responseCode, '21');
    }

    public function isClientError(): bool
    {
        return str_starts_with($this->responseCode, '40');
    }

    public function isServerError(): bool
    {
        return str_starts_with($this->responseCode, '50');
    }

    public function hasHighMatch(): bool
    {
        return $this->matchScore !== null && $this->matchScore >= 0.8;
    }

    public function getMatchLevel(): string
    {
        if ($this->matchScore === null) {
            return self::MATCH_LEVEL_WEAK;
        }

        if ($this->matchScore >= 0.8) {
            return self::MATCH_LEVEL_STRONG;
        }

        if ($this->matchScore >= 0.5) {
            return self::MATCH_LEVEL_PARTIAL;
        }

        return self::MATCH_LEVEL_WEAK;
    }

    public function getStatus(): string
    {
        return $this->isSuccessful() ? 'success' : 'error';
    }

    public function getRequiresConsent(): bool
    {
        return in_array($this->responseCode, [
            self::CODE_NAME_MATCH_FAILED,
            self::CODE_ACCOUNT_FLAGGED,
        ], true) || ($this->matchScore !== null && $this->matchScore < 0.8);
    }
} 
