<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditorAgent
{
    private ?string $bic = null;
    private ?string $clearingSystemId = null;
    private ?string $bankName = null;
    private ?string $bankAddress = null;
    private ?string $paymentSchemeEligibility = null;

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;
        return $this;
    }

    public function getClearingSystemId(): ?string
    {
        return $this->clearingSystemId;
    }

    public function setClearingSystemId(?string $clearingSystemId): self
    {
        $this->clearingSystemId = $clearingSystemId;
        return $this;
    }

    public function getBankName(): ?string
    {
        return $this->bankName;
    }

    public function setBankName(?string $bankName): self
    {
        $this->bankName = $bankName;
        return $this;
    }

    public function getBankAddress(): ?string
    {
        return $this->bankAddress;
    }

    public function setBankAddress(?string $bankAddress): self
    {
        $this->bankAddress = $bankAddress;
        return $this;
    }

    public function getPaymentSchemeEligibility(): ?string
    {
        return $this->paymentSchemeEligibility;
    }

    public function setPaymentSchemeEligibility(?string $paymentSchemeEligibility): self
    {
        $this->paymentSchemeEligibility = $paymentSchemeEligibility;
        return $this;
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->bic !== null) {
            $data['bic'] = $this->bic;
        }

        if ($this->clearingSystemId !== null) {
            $data['clearing_system_id'] = $this->clearingSystemId;
        }

        if ($this->bankName !== null) {
            $data['bank_name'] = $this->bankName;
        }

        if ($this->bankAddress !== null) {
            $data['bank_address'] = $this->bankAddress;
        }

        if ($this->paymentSchemeEligibility !== null) {
            $data['payment_scheme_eligibility'] = $this->paymentSchemeEligibility;
        }

        return $data;
    }
} 