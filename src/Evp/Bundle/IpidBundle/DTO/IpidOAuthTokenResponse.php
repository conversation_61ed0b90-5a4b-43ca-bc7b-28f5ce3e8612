<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidOAuthTokenResponse
{
    private string $accessToken;
    private string $tokenType;
    private int $expiresIn;
    private ?string $refreshToken;
    private ?array $scopes;
    private \DateTimeInterface $issuedAt;

    public function __construct(
        string $accessToken,
        string $tokenType,
        int $expiresIn,
        ?string $refreshToken = null,
        ?array $scopes = null
    ) {
        $this->accessToken = $accessToken;
        $this->tokenType = $tokenType;
        $this->expiresIn = $expiresIn;
        $this->refreshToken = $refreshToken;
        $this->scopes = $scopes;
        $this->issuedAt = new \DateTimeImmutable();
    }

    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    public function getTokenType(): string
    {
        return $this->tokenType;
    }

    public function getExpiresIn(): int
    {
        return $this->expiresIn;
    }

    public function getRefreshToken(): ?string
    {
        return $this->refreshToken;
    }

    public function getScopes(): ?array
    {
        return $this->scopes;
    }

    public function getIssuedAt(): \DateTimeInterface
    {
        return $this->issuedAt;
    }

    public function getExpiresAt(): \DateTimeInterface
    {
        return $this->issuedAt->add(new \DateInterval(sprintf('PT%dS', $this->expiresIn)));
    }

    public function isExpired(): bool
    {
        return new \DateTimeImmutable() >= $this->getExpiresAt();
    }

    public function isExpiringSoon(int $bufferSeconds = 300): bool
    {
        $bufferTime = new \DateTimeImmutable(sprintf('+%d seconds', $bufferSeconds));
        return $bufferTime >= $this->getExpiresAt();
    }

    public function getBearerToken(): string
    {
        return sprintf('%s %s', $this->tokenType, $this->accessToken);
    }

    public static function fromArray(array $data): self
    {
        return new self(
            $data['access_token'],
            $data['token_type'] ?? 'Bearer',
            (int) $data['expires_in'],
            $data['refresh_token'] ?? null,
            isset($data['scope']) ? explode(' ', $data['scope']) : null
        );
    }

    public function toArray(): array
    {
        return [
            'access_token' => $this->accessToken,
            'token_type' => $this->tokenType,
            'expires_in' => $this->expiresIn,
            'refresh_token' => $this->refreshToken,
            'scope' => $this->scopes ? implode(' ', $this->scopes) : null,
            'issued_at' => $this->issuedAt->format(\DateTimeInterface::ATOM),
            'expires_at' => $this->getExpiresAt()->format(\DateTimeInterface::ATOM),
        ];
    }
} 