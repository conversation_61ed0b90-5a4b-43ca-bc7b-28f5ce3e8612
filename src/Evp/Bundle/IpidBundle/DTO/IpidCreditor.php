<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidCreditor
{
    private ?string $name = null;
    private ?string $givenName = null;
    private ?string $surname = null;
    private ?string $nameTranslation = null;
    private ?string $msisdn = null;
    private ?string $email = null;
    private array $identification = [];

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getGivenName(): ?string
    {
        return $this->givenName;
    }

    public function setGivenName(?string $givenName): self
    {
        $this->givenName = $givenName;
        return $this;
    }

    public function getSurname(): ?string
    {
        return $this->surname;
    }

    public function setSurname(?string $surname): self
    {
        $this->surname = $surname;
        return $this;
    }

    public function getNameTranslation(): ?string
    {
        return $this->nameTranslation;
    }

    public function setNameTranslation(?string $nameTranslation): self
    {
        $this->nameTranslation = $nameTranslation;
        return $this;
    }

    public function getMsisdn(): ?string
    {
        return $this->msisdn;
    }

    public function setMsisdn(?string $msisdn): self
    {
        $this->msisdn = $msisdn;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;
        return $this;
    }

    public function getIdentification(): array
    {
        return $this->identification;
    }

    public function setIdentification(array $identification): self
    {
        $this->identification = $identification;
        return $this;
    }

    public function addIdentification(string $value, string $type): self
    {
        $this->identification[] = [
            'value' => $value,
            'type' => $type,
        ];
        return $this;
    }

    public function hasNameOrGivenName(): bool
    {
        return $this->name !== null || $this->givenName !== null;
    }

    public function toArray(): array
    {
        $data = [];

        if ($this->name !== null) {
            $data['name'] = $this->name;
        }

        if ($this->givenName !== null) {
            $data['given_name'] = $this->givenName;
        }

        if ($this->surname !== null) {
            $data['surname'] = $this->surname;
        }

        if ($this->nameTranslation !== null) {
            $data['name_translation'] = $this->nameTranslation;
        }

        if ($this->msisdn !== null) {
            $data['msisdn'] = $this->msisdn;
        }

        if ($this->email !== null) {
            $data['email'] = $this->email;
        }

        if (!empty($this->identification)) {
            $data['identification'] = $this->identification;
        }

        return $data;
    }
} 