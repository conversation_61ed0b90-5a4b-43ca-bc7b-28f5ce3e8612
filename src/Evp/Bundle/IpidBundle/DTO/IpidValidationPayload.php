<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidValidationPayload
{
    private string $countryCode;
    private IpidCreditor $creditor;
    private IpidCreditorAccount $creditorAccount;
    private IpidCreditorAgent $creditorAgent;

    public function __construct(
        string $countryCode,
        IpidCreditor $creditor,
        IpidCreditorAccount $creditorAccount,
        IpidCreditorAgent $creditorAgent
    ) {
        $this->countryCode = $countryCode;
        $this->creditor = $creditor;
        $this->creditorAccount = $creditorAccount;
        $this->creditorAgent = $creditorAgent;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getCreditor(): IpidCreditor
    {
        return $this->creditor;
    }

    public function getCreditorAccount(): IpidCreditorAccount
    {
        return $this->creditorAccount;
    }

    public function getCreditorAgent(): IpidCreditorAgent
    {
        return $this->creditorAgent;
    }

    public function validate(): void
    {
        if (!$this->creditor->hasNameOrGivenName()) {
            throw new \InvalidArgumentException('Creditor must have either name or given_name');
        }

        if (!$this->creditorAccount->hasIbanOrAccountId()) {
            throw new \InvalidArgumentException('Creditor account must have either IBAN or account_id');
        }
    }

    public function toArray(): array
    {
        return [
            'country_code' => $this->countryCode,
            'creditor' => $this->creditor->toArray(),
            'creditor_account' => $this->creditorAccount->toArray(),
            'creditor_agent' => $this->creditorAgent->toArray(),
        ];
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_THROW_ON_ERROR);
    }
}
