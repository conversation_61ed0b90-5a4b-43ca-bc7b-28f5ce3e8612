<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidOAuthRegistrationRequest
{
    private array $scopes;
    private string $applicationName;
    private string $redirectUri;
    private ?string $description;

    public function __construct(
        array $scopes,
        string $applicationName,
        string $redirectUri,
        ?string $description = null
    ) {
        $this->scopes = $scopes;
        $this->applicationName = $applicationName;
        $this->redirectUri = $redirectUri;
        $this->description = $description;
    }

    public function getScopes(): array
    {
        return $this->scopes;
    }

    public function getApplicationName(): string
    {
        return $this->applicationName;
    }

    public function getRedirectUri(): string
    {
        return $this->redirectUri;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public static function createDefault(string $applicationName, string $redirectUri): self
    {
        return new self(
            ['identity:validate', 'account:verify', 'profile:read', 'transfer:validate'],
            $applicationName,
            $redirectUri,
            'EVP Bank iPiD Integration'
        );
    }
} 