<?php

declare(strict_types=1);

namespace Evp\Bundle\IpidBundle\DTO;

class IpidValidationRequest
{
    private string $encryptedPayload;
    private ?string $nodeId;
    private ?string $publicKey = null;
    private ?string $clearingSystemId = null;
    private ?string $bic = null;
    private ?array $publicKeyHint = null;

    public function __construct(
        string $encryptedPayload,
        ?string $nodeId = null
    ) {
        $this->encryptedPayload = $encryptedPayload;
        $this->nodeId = $nodeId;
    }

    public function getEncryptedPayload(): string
    {
        return $this->encryptedPayload;
    }

    public function getNodeId(): ?string
    {
        return $this->nodeId;
    }

    public function setNodeId(?string $nodeId): self
    {
        $this->nodeId = $nodeId;
        return $this;
    }

    public function getPublicKey(): ?string
    {
        return $this->publicKey;
    }

    public function setPublicKey(?string $publicKey): self
    {
        $this->publicKey = $publicKey;
        return $this;
    }

    public function getClearingSystemId(): ?string
    {
        return $this->clearingSystemId;
    }

    public function setClearingSystemId(?string $clearingSystemId): self
    {
        $this->clearingSystemId = $clearingSystemId;
        return $this;
    }

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;
        return $this;
    }

    public function getPublicKeyHint(): ?array
    {
        return $this->publicKeyHint;
    }

    public function setPublicKeyHint(?array $publicKeyHint): self
    {
        $this->publicKeyHint = $publicKeyHint;
        return $this;
    }

    public function getBody(): array
    {
        $body = [
            'encrypted_payload' => $this->encryptedPayload,
        ];

        if ($this->nodeId !== null) {
            $body['node_id'] = $this->nodeId;
        }

        if ($this->publicKey !== null) {
            $body['public_key'] = $this->publicKey;
        }

        if ($this->clearingSystemId !== null) {
            $body['clearing_system_id'] = $this->clearingSystemId;
        }

        if ($this->bic !== null) {
            $body['bic'] = $this->bic;
        }

        if ($this->publicKeyHint !== null) {
            $body['public_key_hint'] = $this->publicKeyHint;
        }

        return $body;
    }
} 