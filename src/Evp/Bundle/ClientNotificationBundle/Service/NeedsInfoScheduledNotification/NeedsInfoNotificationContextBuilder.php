<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientNotificationBundle\Service\NeedsInfoScheduledNotification;

use ArrayObject;
use DateTimeImmutable;
use Evp\Bundle\ClientNotificationBundle\Entity\NotificationKey;
use Paysera\Bundle\TransferAmlInformationBundle\Entity\InformationRequest;

class NeedsInfoNotificationContextBuilder
{
    private const REQUESTED_INFO_KEY = 'requested_info_content';
    private const REQUESTED_INFO_COMMENT_KEY = 'comment';
    private const REQUESTED_INFO_QUESTIONS_KEY = 'questions';
    private const REQUESTED_INFO_REQUESTED_DOCUMENTS_KEY = 'requestedDocuments';
    private const REQUESTED_INFO_REQUESTED_DOCUMENTS_INSTRUCTIONS_KEY = 'instructions';
    private const REQUESTED_INFO_REQUESTED_DOCUMENTS_DOCUMENTS_KEY = 'documents';
    private const DATE_KEY = 'date';
    private const DATE_FORMAT = 'Y-m-d';

    private NeedsInfoNotificationConfigInterface $firstReminderConfig;

    public function __construct(NeedsInfoNotificationConfigInterface $firstReminderConfig)
    {
        $this->firstReminderConfig = $firstReminderConfig;
    }

    public function buildContext(
        NeedsInfoNotificationConfigInterface $config,
        InformationRequest $informationRequest
    ): ArrayObject {
        switch ($config->getNotificationKey()->getValue()) {
            case NotificationKey::NEEDS_INFO_INITIAL:
                return $this->buildInitialContext($informationRequest);
            default:
                return new ArrayObject([]);
        }
    }

    private function buildInitialContext(InformationRequest $informationRequest): ArrayObject {
        $data = [
            self::DATE_KEY => (new DateTimeImmutable())
                ->add($this->firstReminderConfig->getDateInterval())
                ->format(self::DATE_FORMAT)
            ,
            self::REQUESTED_INFO_KEY => [
                self::REQUESTED_INFO_QUESTIONS_KEY => $this->getQuestions($informationRequest),
                self::REQUESTED_INFO_REQUESTED_DOCUMENTS_KEY => $this->getRequestedDocuments($informationRequest),
            ],
        ];

        return new ArrayObject($data);
    }

    private function getQuestions(InformationRequest $informationRequest): ?array
    {
        $questionValues = [];
        foreach ($informationRequest->getQuestions() as $question) {
            $questionValues[] = $question->getQuestion();
        }

        return $questionValues;
    }

    private function getRequestedDocuments(InformationRequest $informationRequest): ?array
    {
        $requestedDocumentsEntity = $informationRequest->getRequestedDocuments();
        if ($requestedDocumentsEntity === null) {
            return null;
        }

        return [
            self::REQUESTED_INFO_REQUESTED_DOCUMENTS_INSTRUCTIONS_KEY => $requestedDocumentsEntity->getInstructions(),
            self::REQUESTED_INFO_REQUESTED_DOCUMENTS_DOCUMENTS_KEY => $requestedDocumentsEntity->getDocuments(),
        ];
    }
}
