<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientNotificationBundle\Tests\Service\NeedsInfoScheduledNotification;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientNotificationBundle\Entity\ScheduledNotification;
use Evp\Bundle\ClientNotificationBundle\Entity\ScheduleNotificationContext\NeedsInfoContext;
use Evp\Bundle\ClientNotificationBundle\Service\NeedsInfoScheduledNotification\NeedsInfoNotificationBuilder;
use Evp\Bundle\ClientNotificationBundle\Tests\DataFixtures\FixturesHelper;
use Paysera\Bundle\TransferAmlInformationBundle\Entity\Question;
use Paysera\Bundle\TransferAmlInformationBundle\Entity\RequestedDocuments;
use Paysera\Client\UserNotificationClient\Entity\Notification;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use RuntimeException;
use Traversable;

class NeedsInfoNotificationBuilderTest extends PersistableWebTestCase
{
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private NeedsInfoNotificationBuilder $builder;

    protected function setUp(): void
    {
        $this->entityManager = $this->getEntityManager();
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->builder = $this->getContainer()->get(
            'evp_client_notification.service.needs_info.notification_builder'
        );

    }

    /**
     * @dataProvider providerBuildNotification
     */
    public function testBuildNotification(string $type, bool $expectException, bool $expectContext): void
    {
        $this->createClientWithNewDatabase();
        $client = $this->fixturesHelper->createClientNatural();
        $account = $this->fixturesHelper->createAccount($client);
        $transfer = $this->fixturesHelper->createTransferOutBank($account);
        $informationRequest = $this->fixturesHelper->createInformationRequest($account, $transfer);

        $question1 = (new Question())
            ->setQuestion('Answer for question 1')
            ->setInputType('text')
            ->setRequired(true)
            ->setHash('question1')
        ;
        $question2 = (new Question())
            ->setQuestion('Answer for question 2')
            ->setInputType('text')
            ->setRequired(true)
            ->setHash('question2')
        ;
        $informationRequest->setQuestions([$question1, $question2]);

        $document1 = 'Document 1';
        $document2 = 'Document 2';
        $requestedDocuments = (new RequestedDocuments())
            ->setInstructions('Instructions instructions')
            ->setDocuments([$document1, $document2])
        ;
        $informationRequest->setRequestedDocuments($requestedDocuments);

        $this->entityManager->flush();

        if ($expectException) {
            $this->expectException(RuntimeException::class);
        }

        $scheduledNotification = $this->fixturesHelper->createScheduledNotification(
            $type,
            $transfer,
            null,
            new NeedsInfoContext($informationRequest->getHash(), $informationRequest->getRequestedFrom())
        );

        $result = $this->builder->buildNotification($scheduledNotification);
        $this->assertInstanceOf(Notification::class, $result);
        $this->assertSame($result->getUserId(), $client->getCovenanteeId());

        $context = iterator_to_array($result->getContext());
        if ($expectContext) {
            $this->assertArrayHasKey('date', $context);
            $this->assertArrayHasKey('requested_info_content', $context);

            $this->assertArrayHasKey('questions', $context['requested_info_content']);
            $this->assertContains($question1->getQuestion(), $context['requested_info_content']['questions']);
            $this->assertContains($question2->getQuestion(), $context['requested_info_content']['questions']);

            $this->assertArrayHasKey('requestedDocuments', $context['requested_info_content']);

            $this->assertArrayHasKey('instructions', $context['requested_info_content']['requestedDocuments']);
            $this->assertEquals($requestedDocuments->getInstructions(), $context['requested_info_content']['requestedDocuments']['instructions']);

            $this->assertArrayHasKey('documents', $context['requested_info_content']['requestedDocuments']);
            $this->assertContains($document1, $context['requested_info_content']['requestedDocuments']['documents']);
            $this->assertContains($document1, $context['requested_info_content']['requestedDocuments']['documents']);
        } else {
            $this->assertEmpty($context);
        }
    }

    public function providerBuildNotification(): Traversable
    {
        yield 'TYPE_NEEDS_INFO_INITIAL' => [
            'type' => ScheduledNotification::TYPE_NEEDS_INFO_INITIAL,
            'expectException' => false,
            'expectContext' => true,
        ];

        yield 'TYPE_NEEDS_INFO_FIRST_REMINDER' => [
            'type' => ScheduledNotification::TYPE_NEEDS_INFO_FIRST_REMINDER,
            'expectException' => false,
            'expectContext' => false,
        ];

        yield 'TYPE_NEEDS_INFO_SECOND_REMINDER' => [
            'type' => ScheduledNotification::TYPE_NEEDS_INFO_SECOND_REMINDER,
            'expectException' => false,
            'expectContext' => false,
        ];

        yield 'TYPE_NEEDS_INFO_THIRD_REMINDER' => [
            'type' => ScheduledNotification::TYPE_NEEDS_INFO_THIRD_REMINDER,
            'expectException' => false,
            'expectContext' => false,
        ];

        yield 'Unknown type' => [
            'type' => 'unknown_type',
            'expectException' => true,
            'expectContext' => false,
        ];
    }
}
