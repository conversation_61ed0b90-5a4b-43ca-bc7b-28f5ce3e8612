<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferRestBundle\Tests\Functional;

use DateTime;
use DateTimeInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankApiBundle\Controller\SoapController;
use Evp\Bundle\BankingHistoryClientBundle\Client\TransferHistoryClient;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManagerInterface;
use Evp\Bundle\BankTransferBundle\Entity\Transfer as LegacyTransfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPerson;
use Evp\Bundle\BankTransferBundle\Repository\TransferDeviceRepository;
use Evp\Bundle\BankTransferBundle\Repository\TransferInternalRepository;
use Evp\Bundle\BankTransferBundle\Service\BatchTransferProcessorInterface;
use Evp\Bundle\BankTransferBundle\Service\CommonTransfer\CommonTransferMapper;
use Evp\Bundle\BankTransferBundle\Service\IbanAliasAccountNumberManager;
use Evp\Bundle\BankTransferBundle\Tests\Mocks\MockSepaParticipantDetector;
use Evp\Bundle\BankTransferRestBundle\Controller\TransferApiV2Controller;
use Evp\Bundle\BankTransferRestBundle\Exception\TransferApiException;
use Evp\Bundle\BankTransferRestBundle\Tests\DataFixtures\LoadClient;
use Evp\Component\Date\DateTimeProvider;
use Evp\Component\Money\Money;
use Evp\Component\SensitiveValue\Entity\SensitiveValue;
use Mockery;
use Paysera\Bundle\RestBundle\Exception\ApiException;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\CommissionRule;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\ConvertCurrency;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\DetailsOptions;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\BankAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\PayseraAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\WebmoneyAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Address;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Beneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\FinalBeneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Identifiers;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Initiator;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Payer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Password;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\ProvidedPassword;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Purpose;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Transfer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransferAdditionalInformation;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransferRegistrationParameters;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransfersBatch;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CurrencyConversion\CurrencyConversionResult;
use Paysera\Bundle\TransferSurveillanceBundle\Entity\TransferInspection;
use Paysera\Bundle\TransferSurveillanceBundle\Listener\TransferInspectionListener;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferInspectionProvider;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Bundle\FrameworkBundle\Client;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Security\Core\Authentication\Token\AnonymousToken;
use Symfony\Component\Security\Core\Role\Role;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class TransferApiV2ControllerTest extends PersistableWebTestCase
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var TransferInspectionProvider
     */
    private $transferInspectionProvider;

    /**
     * @var TransferApiV2Controller
     */
    private $transferApiV2Controller;

    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    /**
     * @var TransferInternalRepository
     */
    private $transferInternalRepository;

    /**
     * @var BatchTransferProcessorInterface
     */
    private $transferBatchInProcessor;
    private TransferDeviceRepository $transferDeviceRepository;
    private DateTimeProvider $dateTimeProvider;
    public function setUp(): void
    {
        $this->dateTimeProvider = $this->getContainer()->get('evp.component.date.date_time_provider');
        $this->client = $this->createClientWithNewDatabase(__DIR__ . '/../DataFixtures');
        $container = $this->client->getContainer();

        $token = new AnonymousToken('key', 'user', [new Role('ROLE_SOAP')]);
        $token->setAuthenticated(true);
        $token->setAttribute('parameters', ['client_id' => 1]);

        $this->transferBatchInProcessor = $container->get('evp_bank_transfer.transfer_processor.batch.in');
        $this->tokenStorage = $container->get('security.token_storage');
        $this->tokenStorage->setToken($token);
        $converter = $container->get('evp_currency.evp_api_buy_currency_converter');
        $this->transferInternalRepository = $container->get('evp_bank_transfer.repository.transfer_internal');
        $this->transferDeviceRepository = $container->get('evp_bank_transfer.repository.transfer_device');

        $converter->setRate('USD', '0.5');

        $converter = $container->get('evp_currency.evp_api_sell_currency_converter');
        $converter->setRate('USD', '0.5');

        $converter = $container->get('evp_currency.currency_converter.market_value');
        $converter->setRate('USD', '0.5');

        $converter = $container->get('evp_currency.currency_converter.official');
        $converter->setRate('USD', '0.5');

        $this->transferApiV2Controller = $container->get('transfer_api_v2');
        $this->getContainer()->get('evp_banking_history_client.transfer_history_secondary_search_client')
            ->shouldReceive('findTransfers')
            ->andReturn(null)
        ;
        $this->getContainer()->get('evp_bundle_bank_transfer.service.iban_aliases_account_number_manager')
            ->shouldReceive('addIbanAliasForCommonBeneficiary')
        ;

        $permissionManagerMock = $this->createMock(PermissionManagerInterface::class);
        $permissionManagerMock->method('hasTransferCancelPermission')->willReturn(true);


        $transferInspectionListenerMock = $this->createMock(TransferInspectionListener::class);
        $transferInspectionListenerMock->expects($this->any())->method('onTransferMarkedAsCanceled');

        $this->getContainer()->set('paysera_bundle_surveillance.listener.transfer_inspection', $transferInspectionListenerMock);

        $this->transferInspectionProvider = $this->client->getContainer()->get(
            'paysera_transfer_surveillance.transfer_inspection_provider'
        );

        /** @uses CommonTransferMapper::__construct */
        $this->getContainer()->set(
            'evp_bank_transfer.mapper.transfer_to_common_transfer',
            $this->getMockBuilder(CommonTransferMapper::class)
                ->setConstructorArgs([
                    $this->getContainer()->getParameter('evp_bank_transfer.common_transfer_purpose_map'),
                    $this->getContainer()->get('paysera_transfer_callback.repository.transfer_callback'),
                    $this->getContainer()->get('evp_bank_transfer.service.evp_account_resolver'),
                    $this->getContainer()->get('evp_bank_transfer.mapper.beneficiary'),
                    $this->getContainer()->get('evp_protected_transfer.transfer_password_manager'),
                    $this->getContainer()->get('evp_bank_transfer.transfer_beneficiary_email_manager'),
                    $this->getContainer()->get('evp_bank.account_info_resolver'),
                    $this->getContainer()->get('evp_client.user_address_provider'),
                    $this->getContainer()->get('evp_bundle_bank_transfer.service.iban_aliases_account_number_manager'),
                    $this->getContainer()->get('evp_client.service.client_identifier_manager'),
                    $this->getContainer()->get('evp_bank_transfer.service.transfer_by_legal_special_country_client_detector'),
                    $this->getContainer()->get('evp_sepa.sepa_transfer_detector'),
                    $this->getContainer()->get('paysera_sepa_instant.sepa_instant_transfer_detector'),
                    $this->getContainer()->get('paysera_mobile_payments.repository.mobile_payment'),
                    $this->getContainer()->get('paysera_transfer_surveillance.transfer_inspection_provider'),
                    $this->getContainer()->get('logger'),
                    $this->getContainer()->get('debug.stopwatch'),
                ])
                ->setMethods(null)
                ->getMock()
        );
    }

    protected static function getOverwrittenServices(ContainerInterface $container)
    {
        return [
            'evp.component.date.date_time_provider' => Mockery::mock(DateTimeProvider::class),
            'evp_banking_history_client.transfer_history_secondary_search_client' => Mockery::mock(
                TransferHistoryClient::class
            ),
            'evp_sepa.sepa_participant_detector' => new MockSepaParticipantDetector(
                $container->get('evp_bank.swift_manager'),
                $container->get('evp_bank.swift_country_resolver'),
                $container->get('evp_sepa.repository.sepa_participant'),
                $container->get('doctrine_cache.providers.sepa_participant_cache'),
                $container->get('logger'),
                ['HABALT22XXX']
            ),
            'evp_bundle_bank_transfer.service.iban_aliases_account_number_manager' => Mockery::mock(
                IbanAliasAccountNumberManager::class
            ),
        ];
    }

    public function testCreateSimulatedTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createSimulatedTransfer($transfer);

        $this->makeNewTransferInternal($transfer, '');

        $this->checkTransferResult($transfer, $apiResult);
        $this->assertEmpty($this->transferInternalRepository->findAll());
    }

    public function testCreateTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->makeNewTransferInternal($transfer, $apiResult->getId());
        $this->checkTransferResult($transfer, $apiResult);
        $this->assertCount(1, $this->transferInternalRepository->findAll());
    }

    /**
     * @dataProvider transferTimeCommissionDataProvider
     */
    public function testCreateAndSignBankTransfer(DateTime $currentDate, Money $commission)
    {
        $this->mockTime($currentDate);
        $transfer = $this->createTransferBank($commission);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        /** @var BankAccount $bankAccount */
        $bankAccount = $apiResult->getBeneficiary()->getAccount();
        $this->makeNewTransferBank(
            $transfer,
            $apiResult->getId(),
            $bankAccount->getBic(),
            $bankAccount->getBankCode()
        );

        $transfer->getAdditionalInformation()->setEstimatedProcessingDate(
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );

        self::assertRegExp('#^.\s+details$|^details$#', $apiResult->getPurpose()->getDetails());
        $transfer->getPurpose()->setDetails($apiResult->getPurpose()->getDetails());

        $this->assertInstanceOf(DateTime::class, $apiResult->getMaxExecutionTime());
        $transfer->setMaxExecutionTime($apiResult->getMaxExecutionTime())
            ->setAllowedToEdit(false)
            ->setAllowedToSign(false);
        $transfer->getPayer()->setAccount($apiResult->getPayer()->getAccount());
        $this->checkTransferResult($transfer, $apiResult);

        $apiResult = $this->transferApiV2Controller->registerTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->assertGreaterThanOrEqual(
            new DateTime(),
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );

        $this->makeSignedTransferBank($transfer, $apiResult->getId());

        $transfer->getAdditionalInformation()->setEstimatedProcessingDate(
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );

        $this->assertInstanceOf(DateTime::class, $apiResult->getMaxExecutionTime());
        $transfer->setMaxExecutionTime($apiResult->getMaxExecutionTime());
        $this->checkTransferResult($transfer, $apiResult);
    }

    /**
     * @dataProvider transferTimeCommissionDataProvider
     */
    public function testReserveAndDeleteBankTransfer(DateTime $currentDate, Money $commission)
    {
        $this->mockTime($currentDate);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferBank($commission);

        $transfer
            ->setAllowedToEdit(false)
            ->setAllowedToSign(false)
        ;

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $transfer->getAdditionalInformation()->setEstimatedProcessingDate(
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );

        self::assertRegExp('#^.\s+details$|^details$#', $apiResult->getPurpose()->getDetails());
        $transfer->getPurpose()->setDetails($apiResult->getPurpose()->getDetails());

        /** @var BankAccount $bankAccount */
        $bankAccount = $apiResult->getBeneficiary()->getAccount();
        $this->makeNewTransferBank(
            $transfer,
            $apiResult->getId(),
            $bankAccount->getBic(),
            $bankAccount->getBankCode()
        );

        $this->assertInstanceOf(DateTime::class, $apiResult->getMaxExecutionTime());
        $transfer->setMaxExecutionTime($apiResult->getMaxExecutionTime());
        $transfer->getPayer()->setAccount($apiResult->getPayer()->getAccount());
        $this->checkTransferResult($transfer, $apiResult);

        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeReservedBankTransfer($transfer, $apiResult->getId());

        $this->assertGreaterThanOrEqual(
            new DateTime(),
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );
        $transfer->getAdditionalInformation()->setEstimatedProcessingDate(
            $apiResult->getAdditionalInformation()->getEstimatedProcessingDate()
        );

        $this->assertInstanceOf(DateTime::class, $apiResult->getMaxExecutionTime());
        $transfer->setMaxExecutionTime($apiResult->getMaxExecutionTime());
        $transfer->setOriginalStatus($apiResult->getOriginalStatus());
        $this->checkTransferResult($transfer, $apiResult);

        $balanceMain = new Money('70', 'EUR');
        $balanceReserved = new Money('30', 'EUR');
        $this->checkBalance($balanceMain->sub($commission));
        $this->checkReservedBalance($balanceReserved->add($commission));

        $apiResult = $this->transferApiV2Controller->deleteTransfer($apiResult->getId());

        $this->makeRejectedTransfer($transfer);
        $transfer->setMaxExecutionTime($apiResult->getMaxExecutionTime());

        $transfer->getAdditionalInformation()
            ->setEstimatedProcessingDate($apiResult->getAdditionalInformation()->getEstimatedProcessingDate())
        ;
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money('100', 'EUR'));
        $this->checkReservedBalance(new Money('0', 'EUR'));
    }

    public function testDeleteBankTransferOnBadStatusResponse()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));
        $transfer = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());
        $this->transferApiV2Controller->reserveTransfer($transfer->getId(), new TransferRegistrationParameters());
        $this->transferApiV2Controller->completeTransfer($transfer->getId());

        $this->client->request(
            'DELETE',
            $this->client->getContainer()->get('router')->generate(
                'evp_bank_transfer_rest.delete_transfer',
                ['id' => $transfer->getId()]
            )
        );
        $response = $this->client->getResponse();
        $content = json_decode($response->getContent(), true);

        $this->assertEquals(409, $response->getStatusCode());
        $this->assertEquals('invalid_state', $content['error']);
    }

    public function testCreateTransferWithNotExistingPayerAccountNumber()
    {
        $this->expectException(ApiException::class);
        $transfer = $this->createTransferInternal();
        $transfer->getPayer()->setAccountNumber('EVP123456789');

        $this->transferApiV2Controller->createTransfer($transfer);
    }

    public function testCreateTransferWithoutClientIdProvided()
    {
        $this->expectException(ApiException::class);
        /** @var AnonymousToken $token */
        $token = $this->tokenStorage->getToken();
        $token->setAttributes([]);

        $transfer = $this->createTransferInternal();

        $this->transferApiV2Controller->createTransfer($transfer);
    }

    public function testGetTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->getTransfer($apiResult->getId());

        $this->makeNewTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);
    }

    public function testGetTransferWithOtherClientThanCreated()
    {
        $this->expectException(ApiException::class);
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        /** @var AnonymousToken $token */
        $token = $this->tokenStorage->getToken();
        $token->setAttribute('parameters', ['client_id' => 2]);

        $this->transferApiV2Controller->getTransfer($apiResult->getId());
    }

    public function testDeleteTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->deleteTransfer($apiResult->getId());

        $this->makeRejectedTransfer($this->makeNewTransferInternal($transfer, $apiResult->getId()));

        $this->checkTransferResult($transfer, $apiResult);
    }

    public function testDeleteTransferWhenTransferIsDone()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), new TransferRegistrationParameters());
        $this->transferApiV2Controller->completeTransfer($apiResult->getId());

        $this->transferApiV2Controller->deleteTransfer($apiResult->getId());
    }

    public function testRegisterTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->registerTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeRegisteredTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);
    }

    public function testRegisterTransferWhenTransferIsNotNew()
    {
        $this->expectException(ApiException::class);
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->registerTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
        $this->transferApiV2Controller->registerTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
    }

    public function testSignTransfer()
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->signTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeSignedTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);
    }

    public function testSignTransferWhenTransferReserved()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->transferApiV2Controller->signTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->checkBalance(new Money(100, 'EUR'));
    }

    public function testSignDeviceId(): void
    {
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->signTransfer(
            $apiResult->getId(),
            (new TransferRegistrationParameters())
                ->setDeviceId(888)
        );

        $this->makeSignedTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);

        $transferDevice = $this->transferDeviceRepository->findOneByTransferId((int)$transfer->getId());

        $this->assertEquals(888, $transferDevice->getDeviceId());
    }

    public function testReserveTransfer()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeReservedTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(new Money(30, 'EUR'));
    }

    public function testReserveTransferDeviceId(): void
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            (new TransferRegistrationParameters())
                ->setDeviceId(888)
        );

        $this->makeReservedTransferInternal($transfer, $apiResult->getId());

        $transferDevice = $this->transferDeviceRepository->findOneByTransferId((int)$transfer->getId());
        $this->assertEquals(888, $transferDevice->getDeviceId());
    }

    public function testReserveTransferWhenNotEnoughFunds()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(10, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        try {
            $this->transferApiV2Controller->reserveTransfer(
                $apiResult->getId(),
                new TransferRegistrationParameters()
            );
        } catch (ApiException $exception) {
            $this->assertEquals(TransferApiException::NOT_ENOUGH_FUNDS, $exception->getErrorCode());

            $this->checkBalance(new Money(10, 'EUR'));
            $this->checkReservedBalance(new Money(0, 'EUR'));

            return;
        }
        $this->fail('Expected Exception has not been raised.');
    }

    public function testReserveTransferWhenReserveUntilNotProvided()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();
        $transfer->setReserveUntil(null);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeDoneTransferInternal($transfer, $apiResult->getId());

        $this->assertLessThanOrEqual(new DateTime(), $apiResult->getPerformedAt());
        $transfer->setPerformedAt($apiResult->getPerformedAt());

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkBalance(new Money(30, 'EUR'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(0, 'EUR'), 'EVP8010001000102');
    }

    public function testReserveTransferWhenTransferIsDone()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();
        $transfer->setReserveUntil(null);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
        $this->transferApiV2Controller->completeTransfer($apiResult->getId());

        $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
    }

    public function testFreezeTransfer()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
        $apiResult = $this->transferApiV2Controller->freezeTransfer($apiResult->getId());
        $this->makeFrozenTransferInternal($transfer, $apiResult->getId());

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkBalance(new Money(0, 'EUR'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(30, 'EUR'), 'EVP8010001000102');
    }

    public function testFreezeTransferWhenTransferIsNew()
    {
        $this->expectException(ApiException::class);
        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->freezeTransfer($apiResult->getId());
    }

    public function testCompleteTransfer()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );
        $apiResult = $this->transferApiV2Controller->completeTransfer($apiResult->getId());
        $this->makeDoneTransferInternal($transfer, $apiResult->getId());

        $this->assertLessThanOrEqual(new DateTime(), $apiResult->getPerformedAt());
        $transfer->setPerformedAt($apiResult->getPerformedAt());

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkBalance(new Money(30, 'EUR'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(0, 'EUR'), 'EVP8010001000102');
    }

    public function testCompleteTransferWhenTransferIsSigned()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $this->transferApiV2Controller->signTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->transferApiV2Controller->completeTransfer($apiResult->getId());
    }

    public function testProvidePassword()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $password = new Password();
        $password->setValue(new SensitiveValue('super_secret666'));

        $transfer->setPassword($password);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(new Money(30, 'EUR'));

        $this->makeWaitingPasswordTransferInternal($transfer, $apiResult->getId());
        $transfer->setPassword($password);

        $this->checkTransferResult($transfer, $apiResult);

        $apiResult = $this->transferApiV2Controller->providePassword(
            $apiResult->getId(),
            new ProvidedPassword(new SensitiveValue('super_secret666'))
        );

        $transfer = $this->createTransferInternal();
        $transfer = $this->makeDoneTransferInternal($transfer, $apiResult->getId());
        $transfer->setPerformedAt($apiResult->getPerformedAt());
        $transfer->setPassword($password->setStatus(Password::STATUS_UNLOCKED));

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(70, 'EUR'));
        $this->checkReservedBalance(Money::createZero('EUR'));
    }

    public function testProvidePasswordWhenTransferIsNew()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $password = new Password();
        $password->setValue(new SensitiveValue('super_secret666'));

        $transfer->setPassword($password);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $this->transferApiV2Controller->providePassword(
            $apiResult->getId(),
            new ProvidedPassword(new SensitiveValue('super_secret666'))
        );
    }

    public function testProvidePasswordWithInvalidPassword()
    {
        $this->mockTime();
        $this->expectException(ApiException::class);
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal();

        $password = new Password();
        $password->setValue(new SensitiveValue('super_secret666'));

        $transfer->setPassword($password);

        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);
        $apiResult = $this->transferApiV2Controller->reserveTransfer(
            $apiResult->getId(),
            new TransferRegistrationParameters()
        );

        $this->makeWaitingPasswordTransferInternal($transfer, $apiResult->getId());
        $transfer->setPassword($password);

        $this->checkTransferResult($transfer, $apiResult);

        $this->transferApiV2Controller->providePassword(
            $apiResult->getId(),
            new ProvidedPassword(new SensitiveValue('invalidPassword777'))
        );
    }

    public function testReserveTransfers()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer1 = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());
        $transfer2 = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());
        $transfer3 = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());
        $transfer4 = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());
        $transfer5 = $this->transferApiV2Controller->createTransfer($this->createTransferInternal());

        $transfersBatch = new TransfersBatch();
        $transfersBatch->setRevokeTransfers([$transfer1->getId(), $transfer3->getId()]);
        $transfersBatch->addConvertCurrency($this->createConvertCurrency(new Money('10', 'USD')));
        $transfersBatch->setReserveTransfers([$transfer2->getId(), $transfer4->getId(), $transfer5->getId()]);

        $transfersBatchResult = $this->transferApiV2Controller->reserveTransfers($transfersBatch);

        $revoked = $transfersBatchResult->getRevokedTransfers();
        $this->assertCount(2, $revoked);
        $reserved = $transfersBatchResult->getReservedTransfers();
        $this->assertCount(3, $reserved);

        $this->makeRejectedTransfer($transfer1);
        $this->makeRejectedTransfer($transfer3);
        $this->checkTransferResult($transfer1, $revoked[0]);
        $this->checkTransferResult($transfer3, $revoked[1]);

        $this->makeReservedTransferInternal($transfer2, $transfer2->getId());
        $this->makeReservedTransferInternal($transfer4, $transfer4->getId());
        $this->makeReservedTransferInternal($transfer5, $transfer5->getId());
        $this->checkTransferResult($transfer2, $reserved[0]);
        $this->checkTransferResult($transfer4, $reserved[1]);
        $this->checkTransferResult($transfer5, $reserved[2]);

        $this->checkBalance(new Money(5, 'EUR'));
        $this->checkBalance(new Money(10, 'USD'));
        $this->checkReservedBalance(new Money(90, 'EUR'));

        $transfersBatch2 = new TransfersBatch();
        $transfersBatch2->setRevokeTransfers([$transfer2->getId(), $transfer4->getId(), $transfer5->getId()]);

        $transfersBatchResult2 = $this->transferApiV2Controller->reserveTransfers($transfersBatch2);

        $revoked = $transfersBatchResult2->getRevokedTransfers();
        $this->assertCount(3, $revoked);
        $reserved = $transfersBatchResult2->getReservedTransfers();
        $this->assertCount(0, $reserved);

        $this->makeRejectedTransfer($transfer2);
        $this->makeRejectedTransfer($transfer4);
        $this->makeRejectedTransfer($transfer5);
        $this->checkTransferResult($transfer2, $revoked[0]);
        $this->checkTransferResult($transfer4, $revoked[1]);
        $this->checkTransferResult($transfer5, $revoked[2]);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(10, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
    }

    public function testTransferToDoneWithConvertCurrency()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        //register transfer
        $registrationParameters = new TransferRegistrationParameters();
        $convertCurrency = $this->createConvertCurrency(new Money('10', 'USD'));
        $registrationParameters->addConvertCurrency($convertCurrency);
        $apiResult = $this->transferApiV2Controller->registerTransfer($apiResult->getId(), $registrationParameters);
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_REGISTERED,
                new Money('5', 'EUR')
            )
        );
        $this->makeRegisteredTransferInternal($transfer, $apiResult->getId());
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(100, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));

        $relatedConvertCurrency = $transfer->getRelatedConvertCurrency();

        //sign transfer
        $emptyRegistrationParameters = new TransferRegistrationParameters();
        $apiResult = $this->transferApiV2Controller->signTransfer($apiResult->getId(), $emptyRegistrationParameters);
        $this->makeSignedTransferInternal($transfer, $apiResult->getId());
        $relatedConvertCurrency->setStatus(Transfer::STATUS_SIGNED);
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(100, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));

        //reserve transfer
        $apiResult = $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), $emptyRegistrationParameters);
        $this->makeReservedTransferInternal($transfer, $apiResult->getId());
        $relatedConvertCurrency->setStatus(Transfer::STATUS_DONE);
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(10, 'USD'));

        //complete transfer
        $apiResult = $this->transferApiV2Controller->completeTransfer($apiResult->getId());
        $this->makeDoneTransferInternal($transfer, $apiResult->getId());

        $this->assertLessThanOrEqual(new DateTime(), $apiResult->getPerformedAt());
        $transfer->setPerformedAt($apiResult->getPerformedAt());
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));
        $this->checkBalance(new Money(0, 'EUR'), 'EVP8010001000102');
        $this->checkBalance(new Money(10, 'USD'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(0, 'EUR'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(0, 'USD'), 'EVP8010001000102');

        $this->transferApiV2Controller->completeTransfer($apiResult->getId());
    }

    public function testReserveTransferWithConvertCurrency()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency($this->createConvertCurrency(new Money('10', 'USD')));
        $apiResult = $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), $registrationParameters);
        $this->makeReservedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_DONE,
                new Money('5', 'EUR')
            )
        );

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(10, 'USD'));
    }

    public function testReserveTransferWithConvertCurrencyWithMinAmount()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency(
            $this->createConvertCurrency(null, new Money('5', 'EUR'), new Money('10', 'USD'))
        );
        $apiResult = $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), $registrationParameters);
        $this->makeReservedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_DONE,
                new Money('5', 'EUR')
            )
        );
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(10, 'USD'));
    }

    public function testCompleteTransferWhenSignedAndReservedWithAnotherConvert()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency($this->createConvertCurrency(new Money('20', 'USD')));
        $apiResult = $this->transferApiV2Controller->signTransfer($apiResult->getId(), $registrationParameters);
        $this->makeSignedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('20', 'USD'),
                Transfer::STATUS_SIGNED,
                new Money('10', 'EUR')
            )
        );
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(100, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency($this->createConvertCurrency(new Money('10', 'USD')));
        $apiResult = $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), $registrationParameters);
        $this->makeReservedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_DONE,
                new Money('5', 'EUR')
            )
        );
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(10, 'USD'));

        $apiResult = $this->transferApiV2Controller->completeTransfer($apiResult->getId());
        $this->makeDoneTransferInternal($transfer, $apiResult->getId());

        $this->assertLessThanOrEqual(new DateTime(), $apiResult->getPerformedAt());
        $transfer->setPerformedAt($apiResult->getPerformedAt());
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(10, 'USD'), 'EVP8010001000102');
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));
    }

    public function testDeleteTransferWhenSignedWithConvertCurrency()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency($this->createConvertCurrency(new Money('10', 'USD')));
        $apiResult = $this->transferApiV2Controller->signTransfer($apiResult->getId(), $registrationParameters);
        $this->makeSignedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_SIGNED,
                new Money('5', 'EUR')
            )
        );
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(100, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));

        $apiResult = $this->transferApiV2Controller->deleteTransfer($apiResult->getId());
        $this->makeRejectedTransfer($transfer);
        $transfer->getRelatedConvertCurrency()->setStatus(Transfer::STATUS_REJECTED);
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(100, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(0, 'USD'));
    }

    public function testDeleteTransferWithConvertCurrency()
    {
        $this->mockTime();
        $this->makeTransferIn(new Money(100, 'EUR'));

        $transfer = $this->createTransferInternal(new Money(10, 'USD'));
        $apiResult = $this->transferApiV2Controller->createTransfer($transfer);

        $registrationParameters = new TransferRegistrationParameters();
        $registrationParameters->addConvertCurrency($this->createConvertCurrency(new Money('10', 'USD')));
        $apiResult = $this->transferApiV2Controller->reserveTransfer($apiResult->getId(), $registrationParameters);
        $this->makeReservedTransferInternal($transfer, $apiResult->getId());
        $transfer->setRelatedConvertCurrency(
            $this->createCurrencyConversionResult(
                $apiResult->getRelatedConvertCurrency()->getId(),
                $apiResult->getRelatedConvertCurrency()->getDate(),
                new Money('10', 'USD'),
                Transfer::STATUS_DONE,
                new Money('5', 'EUR')
            )
        );
        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(0, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(10, 'USD'));

        $apiResult = $this->transferApiV2Controller->deleteTransfer($apiResult->getId());
        $this->makeRejectedTransfer($transfer);
        $transfer->getRelatedConvertCurrency()->setStatus(Transfer::STATUS_DONE);

        $this->checkTransferResult($transfer, $apiResult);

        $this->checkBalance(new Money(95, 'EUR'));
        $this->checkBalance(new Money(10, 'USD'));
        $this->checkReservedBalance(new Money(0, 'EUR'));
        $this->checkReservedBalance(new Money(00, 'USD'));
    }

    private function checkTransferResult(Transfer $expected, Transfer $result)
    {
        $expected->setHash($result->getHash());
        $this->assertLessThanOrEqual(new DateTime(), $result->getCreatedAt());
        $this->assertLessThanOrEqual(new DateTime(), $result->getPerformAt());

        $expected->setCreatedAt($result->getCreatedAt());
        $expected->setPerformAt($result->getPerformAt());

        $this->assertEquals($expected, $result);
    }

    /**
     * @param Money $toAmount
     * @param Money $fromAmount
     * @param Money $minToAmount
     *
     * @return ConvertCurrency
     */
    private function createConvertCurrency(Money $toAmount = null, Money $fromAmount = null, Money $minToAmount = null)
    {
        $convertCurrency = new ConvertCurrency();
        $convertCurrency->setFromCurrency('EUR');
        $convertCurrency->setToCurrency('USD');
        $convertCurrency->setToAmount($toAmount);
        $convertCurrency->setFromAmount($fromAmount);
        $convertCurrency->setMinToAmount($minToAmount);
        $convertCurrency->setAccountNumber('EVP6410001000101');

        return $convertCurrency;
    }

    private function createCurrencyConversionResult(
        string $id,
        DateTimeInterface $date,
        Money $toAmount,
        string $status,
        Money $fromAmount = null
    ) {
        return (new CurrencyConversionResult())
            ->setId($id)
            ->setDate($date)
            ->setFromAmount($fromAmount)
            ->setFromCurrency('EUR')
            ->setToCurrency($toAmount->getCurrency())
            ->setToAmount($toAmount)
            ->setAccountNumber('EVP6410001000101')
            ->setUserId(123)
            ->setStatus($status)
        ;
    }

    private function createTransferBank(Money $commission)
    {
        $beneficiaryIdentifiers = new Identifiers();
        $beneficiaryIdentifiers->setGeneral('67890');

        $iban = '********************';
        $bankAccount = new BankAccount();
        $bankAccount->setIban($iban);
        $bankAccount->setAccountNumber('***********');
        $bankAccount->setCountryCode('LT');

        $beneficiary = new Beneficiary();
        $beneficiary
            ->setName('Beneficiary name')
            ->setType(BankAccount::TYPE_BANK)
            ->setIdentifiers($beneficiaryIdentifiers)
            ->setAccount($bankAccount)
            ->setIban($iban)
        ;

        $payer = new Payer();
        $payer
            ->setAccountNumber('EVP6410001000101')
            ->setUserId(123)
            ->setName(LoadClient::CLIENT_NAME)
        ;

        $purpose = new Purpose();
        $purpose
            ->setDetails('details')
            ->setCode('cash_out')
        ;

        $finalBeneficiaryIdentifiers = new Identifiers();
        $finalBeneficiaryIdentifiers->setGeneral('67890');

        $finalBeneficiary = new FinalBeneficiary();
        $finalBeneficiary
            ->setName('Someone Surname')
            ->setIdentifiers($finalBeneficiaryIdentifiers)
        ;

        $transfer = new Transfer();
        $transfer
            ->setAmount(new Money('30', 'EUR'))
            ->setBeneficiary($beneficiary)
            ->setPayer($payer)
            ->setPurpose($purpose)
            ->setCancelable(true)
            ->setAllowedToCancel(true)
            ->setOutCommission($commission)
            ->setFinalBeneficiary($finalBeneficiary)
            ->setOriginalStatus(LegacyTransfer::STATUS_NEW)
            ->setChargeType(TransferOutBank::CHARGE_TYPE_SHA)
            ->setIsSepa(true)
            ->setIsSepaByLegalSpecialCountryClient(true);
        ;

        $commissionRule = new CommissionRule();
        $commissionRule->setFix($commission);

        $additionalInformation = new TransferAdditionalInformation();
        $additionalInformation->setCorrespondentBankFeesMayApply(false);
        $additionalInformation->setOriginalOutCommission($commission);
        $additionalInformation->setOutCommissionRule($commissionRule);

        $transfer->setAdditionalInformation($additionalInformation);

        /** @var BankAccount $bankAccount */
        $bankAccount = $transfer->getBeneficiary()->getAccount();
        $bankAccount->setBankTitle('"Swedbank", AB');

        $transfer->getPurpose()->setDetailsOptions((new DetailsOptions())->setPreserved(false));

        return $transfer;
    }

    private function makeNewTransferBank(Transfer $transfer, $id = '1', $bic = null, $bankCode = null)
    {
        $initiator = new Initiator();
        $initiator->setUserId(123);

        $transfer->setInitiator($initiator);
        $transfer->setId($id);
        $transfer->setStatus(Transfer::STATUS_NEW);

        /** @var BankAccount $bankAccount */
        $bankAccount = $transfer->getBeneficiary()->getAccount();
        $bankAccount->setBankAddress(new Address('LT'));
        $bankAccount->setBic($bic);
        $bankAccount->setBankCode($bankCode);

        return $transfer;
    }

    private function makeSignedTransferBank(Transfer $transfer, $id = '1')
    {
        $transfer->setId($id);
        $transfer
            ->setStatus(Transfer::STATUS_REGISTERED)
            ->setOriginalStatus(LegacyTransfer::STATUS_PREPARED)
        ;

        return $transfer;
    }

    private function makeReservedBankTransfer(Transfer $transfer, $id = '1')
    {
        $this->makeSignedTransferBank($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_RESERVED)
            ->setOriginalStatus(LegacyTransfer::STATUS_RESERVED)
        ;

        return $transfer;
    }

    public function transferTimeCommissionDataProvider(): array
    {
        return [
            [
                new DateTime('2024-11-01 00:00'),
                new Money('0', 'EUR'),
            ],
        ];
    }

    private function createTransferWebmoney()
    {
        $beneficiary = new Beneficiary();
        $beneficiary
            ->setType(WebmoneyAccount::TYPE_WEBMONEY)
            ->setAccount(new WebmoneyAccount('z123456789012'))
        ;

        $payer = new Payer();
        $payer
            ->setAccountNumber('EVP6410001000101')
            ->setUserId(123)
        ;

        $purpose = new Purpose();
        $purpose
            ->setDetails('Blah blah')
            ->setCode('cash_out')
        ;

        $finalBeneficiaryIdentifiers = new Identifiers();
        $finalBeneficiaryIdentifiers->setGeneral('67890');

        $finalBeneficiary = new FinalBeneficiary();
        $finalBeneficiary
            ->setName('Someone Surname')
            ->setIdentifiers($finalBeneficiaryIdentifiers)
        ;

        $transfer = new Transfer();
        $transfer
            ->setAmount(new Money('30', 'EUR'))
            ->setBeneficiary($beneficiary)
            ->setPayer($payer)
            ->setPurpose($purpose)
            ->setCancelable(true)
            ->setAllowedToCancel(true)
            ->setFinalBeneficiary($finalBeneficiary)
        ;

        return $transfer;
    }

    private function makeNewTransferWebmoney(Transfer $transfer, $id = '1')
    {
        $initiator = new Initiator();
        $initiator->setUserId(123);

        $transfer->setInitiator($initiator);

        $transfer->setId($id);
        $transfer->setStatus(Transfer::STATUS_NEW);
        $transfer->getPurpose()->setDetailsOptions((new DetailsOptions())->setPreserved(false));

        $transfer->setOutCommission(Money::createFromCents(53, 'EUR'));

        $commissionRule = new CommissionRule();
        $commissionRule->setPercent(0.8);
        $commissionRule->setFix(new Money('0.29', 'EUR'));

        $additionalInformation = new TransferAdditionalInformation();
        $additionalInformation->setCorrespondentBankFeesMayApply(false);
        $additionalInformation->setOriginalOutCommission(Money::createFromCents(53, 'EUR'));
        $additionalInformation->setOutCommissionRule($commissionRule);

        $transfer->setAdditionalInformation($additionalInformation);

        return $transfer;
    }

    private function makeSignedTransferWebmoney(Transfer $transfer, $id = '1')
    {
        $this->makeNewTransferWebmoney($transfer, $id);

        $transfer->setStatus(Transfer::STATUS_REGISTERED);

        return $transfer;
    }

    private function createTransferInternal(Money $amount = null): Transfer
    {
        $beneficiaryIdentifiers = new Identifiers();
        $beneficiaryIdentifiers->setGeneral('67890');

        $payseraAccount = new PayseraAccount();
        $payseraAccount->setAccountNumber('EVP8010001000102');
        $payseraAccount->setClientId(2);

        $beneficiary = new Beneficiary();
        $beneficiary
            ->setType(PayseraAccount::TYPE_PAYSERA)
            ->setName('UAB "Second Legal Client"')
            ->setIdentifiers($beneficiaryIdentifiers)
            ->setAccount($payseraAccount)
            ->setIban('LT55102')
        ;

        $payer = new Payer();
        $payer
            ->setAccountNumber('EVP6410001000101')
            ->setUserId(123)
            ->setName(LoadClient::CLIENT_NAME)
        ;

        $purpose = new Purpose();
        $purpose
            ->setDetails('Blah blah')
            ->setCode('cash_out')
        ;

        $finalBeneficiaryIdentifiers = new Identifiers();
        $finalBeneficiaryIdentifiers->setGeneral('67890');

        $finalBeneficiary = new FinalBeneficiary();
        $finalBeneficiary
            ->setName('Someone Surname')
            ->setIdentifiers($finalBeneficiaryIdentifiers)
        ;

        if ($amount === null) {
            $amount = new Money('30', 'EUR');
        }

        $transfer = new Transfer();
        $transfer
            ->setAmount($amount)
            ->setBeneficiary($beneficiary)
            ->setPayer($payer)
            ->setPurpose($purpose)
            ->setCancelable(true)
            ->setAllowedToCancel(true)
            ->setFinalBeneficiary($finalBeneficiary)
            ->setReserveUntil(new DateTime('tomorrow'))
            ->setOriginalStatus(LegacyTransfer::STATUS_NEW)
            ->setOutCommission(Money::createZero($transfer->getAmount()->getCurrency()))
        ;

        return $transfer;
    }

    private function makeNewTransferInternal(Transfer $transfer, $id = '1')
    {
        $initiator = new Initiator();
        $initiator->setUserId(123);

        $transfer->setInitiator($initiator);

        $transfer->setId($id);
        $transfer->setStatus(Transfer::STATUS_NEW);

        return $transfer;
    }

    private function makeRejectedTransfer(Transfer $transfer)
    {
        $transfer
            ->setStatus(Transfer::STATUS_REJECTED)
            ->setOriginalStatus(LegacyTransfer::STATUS_CANCELED)
        ;

        return $transfer;
    }

    private function makeRegisteredTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeNewTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_REGISTERED)
            ->setOriginalStatus(LegacyTransfer::STATUS_PREPARED)
        ;

        return $transfer;
    }

    private function makeSignedTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeNewTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_SIGNED)
            ->setOriginalStatus(LegacyTransfer::STATUS_SIGNED)
        ;

        return $transfer;
    }

    private function makeReservedTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeRegisteredTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_RESERVED)
            ->setOriginalStatus(LegacyTransfer::STATUS_RESERVED)
        ;

        return $transfer;
    }

    private function makeFrozenTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeRegisteredTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_FROZEN)
            ->setOriginalStatus(LegacyTransfer::STATUS_FREEZED)
        ;


        return $transfer;
    }

    private function makeDoneTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeRegisteredTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_DONE)
            ->setOriginalStatus(LegacyTransfer::STATUS_DONE)
        ;

        return $transfer;
    }

    private function makeWaitingPasswordTransferInternal(Transfer $transfer, $id = '1')
    {
        $this->makeRegisteredTransferInternal($transfer, $id);

        $transfer
            ->setStatus(Transfer::STATUS_WAITING_PASSWORD)
            ->setOriginalStatus(LegacyTransfer::STATUS_RESERVED)
        ;

        return $transfer;
    }

    /**
     * Makes transfer in, checks for success status and starts processor
     *
     * @param Money $money
     * @param Money $commission
     *
     * @return int
     */
    private function makeTransferIn(Money $money, Money $commission = null)
    {
        $transferIn = new TransferIn();
        $transferIn->setAmountMoney($money);
        $partyAccount = new PartyAccount('EVP6410001000101', 'Company');
        $transferIn->setBeneficiary($partyAccount);
        $transferIn->setDate(new DateTime());
        $transferIn->setOperationDate(new DateTime());
        $transferIn->setDetails('Sąskaitos papildymas');
        $transferIn->setPayer(new PartyPerson('Testas Testauskas'));
        if ($commission !== null) {
            $transferIn->setDebitCommissionMoney($commission);
        }
        $this->createTransferInspectionWithActionNone($transferIn);
        $transferIn = $this->getBankApiController()->makeTransfer(1, $transferIn)->getReturnValue();
        $this->assertSame(LegacyTransfer::STATUS_PREPARED, $transferIn->getStatus());
        $this->transferBatchInProcessor->process();

        return $transferIn->getId();
    }

    private function getBankApiController(): SoapController
    {
        $soapController = new SoapController();
        $soapController->setContainer($this->client->getContainer());

        return $soapController;
    }

    private function getBalance(string $currency, string $accountNumber = null): Money
    {
        return $this->getAccount($accountNumber)
            ->getCurrencyAccount($currency)
            ->getInternalMainAccount()
            ->getBalanceMoney()
        ;
    }

    private function checkBalance(Money $money, string $accountNumber = null)
    {
        $this->assertEquals(
            $money,
            $this->getBalance($money->getCurrency(), $accountNumber)
        );
    }

    private function getReservedBalance(string $currency, string $accountNumber = null): Money
    {
        return $this->getAccount($accountNumber)
            ->getCurrencyAccount($currency)
            ->getInternalReservationAccount()
            ->getBalanceMoney()
        ;
    }

    private function checkReservedBalance(Money $money, string $accountNumber = null)
    {
        $this->assertEquals(
            $money,
            $this->getReservedBalance($money->getCurrency(), $accountNumber)
        );
    }

    private function getAccount(string $number = null): Account
    {
        if ($number === null) {
            $number = 'EVP6410001000101';
        }

        return $this->getBankApiController()->getAccountByNumber($number)->getReturnValue();
    }

    private function createTransferInspectionWithActionNone(LegacyTransfer $transfer)
    {
        $transferInspection = new TransferInspection();
        $transferInspection->setStatus(TransferInspection::STATUS_PROCESSED);
        $transferInspection->setAction(TransferInspection::ACTION_NONE);

        $this->transferInspectionProvider->setTransferInspection($transfer, $transferInspection);
    }

    private function mockTime(DateTime $currentTime = null) :void
    {
        $this->dateTimeProvider
            ->shouldReceive('getCurrent')
            ->andReturn($currentTime ?? new DateTime())
        ;
    }
}
