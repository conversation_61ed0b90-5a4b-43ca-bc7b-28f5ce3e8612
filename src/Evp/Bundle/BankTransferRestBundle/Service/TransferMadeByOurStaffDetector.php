<?php
declare(strict_types=1);

namespace Evp\Bundle\BankTransferRestBundle\Service;

use Paysera\Bundle\JWTAuthenticationBundle\Authentication\Token;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class TransferMadeByOurStaffDetector
{
    private TokenStorageInterface $tokenStorage;
    private ImpersonatorIdResolver $impersonatorIdResolver;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        ImpersonatorIdResolver $impersonatorIdResolver
    ) {
        $this->impersonatorIdResolver = $impersonatorIdResolver;
        $this->tokenStorage = $tokenStorage;
    }

    public function isDetectedByToken(): bool
    {
        $token = $this->tokenStorage->getToken();

        if (!$token instanceof Token) {
            return false;
        }

        $impersonatorId = $this->impersonatorIdResolver->resolve($token);
        if ($impersonatorId === null) {
            return false;
        }

        return true;
    }
}
