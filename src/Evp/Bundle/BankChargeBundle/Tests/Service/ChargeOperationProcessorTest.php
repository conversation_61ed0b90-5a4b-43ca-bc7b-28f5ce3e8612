<?php

declare(strict_types=1);

namespace Evp\Bundle\BankChargeBundle\Tests\Service;

use DateTime;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\ORMException;
use Evp\Bundle\AccountingBundle\Exception\OperationProcessorException;
use Evp\Bundle\AccountingBundle\Service\AccountingOperationBuilder;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\AccountingClient;
use Evp\Bundle\AccountingBundle\Service\OperationProcessor\ChargeOperationProcessor as PartnerChargeOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankChargeBundle\Entity\Charge;
use Evp\Bundle\BankChargeBundle\Entity\ChargeOperation;
use Evp\Bundle\BankChargeBundle\Service\ChargeOperationProcessor;
use Evp\Bundle\BankTransferBundle\Service\TemplateProvider;
use Evp\Bundle\BankTransferBundle\Tests\BaseTestCase;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\ClientUnverified;
use Evp\Component\Money\Money;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit_Framework_MockObject_MockObject;
use Psr\Log\LoggerInterface;

class ChargeOperationProcessorTest extends BaseTestCase
{
    /**
     * @var AccountingClient|MockObject
     */
    private $accountingClient;

    private ChargeOperationProcessor $processor;

    private PartnerChargeOperationProcessor $partnerChargeOperationProcessor;

    /**
     * Set up
     */
    public function setUp(): void
    {
        $this->accountingClient = $this->createMock(AccountingClient::class);
        $templateProvider = $this->createMock(TemplateProvider::class);
        $logger = $this->createMock(LoggerInterface::class);
        $builder = new AccountingOperationBuilder($logger);
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->partnerChargeOperationProcessor = $this->createMock(PartnerChargeOperationProcessor::class);
        $this->processor = new ChargeOperationProcessor(
            $templateProvider,
            $this->createMock(AccountingExceptionHandler::class),
            $this->partnerChargeOperationProcessor,
        );
        $this->processor->setBuilder($builder);
        $this->processor->setAccountingClient($this->accountingClient);
        $this->processor->setLogger($logger);
        $this->processor->setEntityManager($this->entityManager);
    }

    /**
     * @param ChargeOperation $chargeOperation
     *
     * @throws ORMException
     * @throws OperationProcessorException
     *
     * @dataProvider processProvider
     */
    public function testProcess(ChargeOperation $chargeOperation)
    {
        $this->accountingClient->expects($this->once())
            ->method('save')
            ->will($this->returnArgument(0));

        $this->partnerChargeOperationProcessor
            ->expects($this->once())
            ->method('process')
        ;

        $this->processor->process($chargeOperation);
    }

    public function processProvider(): iterable
    {
        $client = (new ClientNatural())
            ->setCovenanteeId(1)
        ;

        $account = (new Account())
            ->setClient($client)
        ;

        $charge = (new Charge())
            ->setAccount($account)
            ->setDetails('details')
        ;

        $operation = (new ChargeOperation($charge, $account, new Money(10, 'EUR')))
            ->setCreatedAt(new DateTime())
        ;

        return [
            'charge' => [
                'chargeOperation' => $operation,
            ],
        ];
    }

    /**
     * Test process() method with zero commission
     */
    public function testProcessWithZeroCommission()
    {
        $this->expectException(InvalidArgumentException::class);

        $client = new ClientUnverified();
        $client->setCovenanteeId(1);

        $account = new Account();
        $account->setClient($client);

        $charge = new Charge();
        $charge->setAccount($account);

        $operation = new ChargeOperation($charge, $account, new Money(0, 'EUR'));
        $operation->setCreatedAt(new \DateTime());

        $this->accountingClient->expects($this->never())
            ->method('save');

        $this->processor->process($operation);
    }

    /**
     * Test process() method with unsupported operation
     */
    public function testProcessWithUnsupportedOperation()
    {
        $this->expectException(InvalidArgumentException::class);

        /** * @var Operation|PHPUnit_Framework_MockObject_MockObject $operation */
        $operation = $this->createMock(Operation::class);

        $this->processor->process($operation);
    }
}
