<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/commands.xml"/>
        <import resource="services/processors.xml"/>
        <import resource="services/repositories.xml"/>
        <import resource="services/workers.xml"/>
        <import resource="services/listeners.xml"/>
    </imports>

    <parameters>
        <parameter key="charge_income_handler.priority_threshold">1000</parameter>
        <parameter key="charge_income_handler.batch_size">1000</parameter>
    </parameters>

    <services>
        <service id="evp_bank_charge.charge_operation_processor"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor" class="Evp\Bundle\BankChargeBundle\Entity\ChargeOperation" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_accounting.service.operation_processor.charge_operation_processor"/>
        </service>

        <service id="evp_bank_charge.charge_statement_assembler" class="Evp\Bundle\BankChargeBundle\Service\ChargeStatementAssembler">
            <tag name="evp_bank_account.statement.assembler" class="Evp\Bundle\BankChargeBundle\Entity\ChargeOperation" />

            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="paysera_partner.service.partner_statement_party_provider"/>
            <argument type="service" id="evp_bank_transfer.manager.transfer_related_to_charge_provider"/>
            <argument type="service" id="evp_bank_refund.refund_provider"/>
            <argument type="service" id="evp_bank_charge.statement_type_resolver"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_charge.statement_type_resolver"
                 class="Evp\Bundle\BankChargeBundle\Service\StatementTypeResolver"/>

        <service id="evp_bank_charge.charge_factory" class="Evp\Bundle\BankChargeBundle\Service\ChargeFactory">
            <argument type="service" id="evp_bank_account.commission_account_resolver"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_charge.charge_debt_provider"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeDebtProvider">
            <argument id="evp_bank_charge.repository.charge" type="service"/>
            <argument id="evp_rabbit_mq_extension.remote_high_load_job_publisher" type="service"/>
        </service>

        <service id="evp_bank_charge.charge_account_locker"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeAccountLocker">
            <argument id="evp_bank_account.account_lock" type="service"/>
            <argument id="evp_bank_charge.charge_account_checker" type="service"/>
        </service>

        <service id="evp_bank_charge.charge_account_checker"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeAccountChecker">
        </service>

        <service class="Evp\Bundle\BankChargeBundle\Service\ChargeIncomeHandler"
                 id="evp_bank_charge.service.charge_income_handler">
            <tag name="evp_bank_account.account_income_handler" priority="40" skipOnContext="true"/>

            <argument type="service" id="evp_bank_charge.repository.charge"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_charge.processor.batch_charge"/>
            <argument type="service" id="logger"/>
            <argument>%charge_income_handler.priority_threshold%</argument>
            <argument>%charge_income_handler.batch_size%</argument>
        </service>

        <!-- Admin -->
        <service id="evp_bank_charge.admin.charge" class="Evp\Bundle\BankChargeBundle\Admin\ChargeAdmin">
            <tag name="sonata.admin" manager_type="orm" group="Charge" label="Charge" />

            <argument />
            <argument>Evp\Bundle\BankChargeBundle\Entity\Charge</argument>
            <argument />
            <argument type="service" id="evp_audit.auditor" />
            <argument type="service" id="evp_bank_charge.processor.charge" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.commission_account_resolver" />
            <argument type="service" id="evp_bank_charge.charge_helper"/>
            <argument type="service" id="evp_bank_charge.permission_checker" />

            <call method="setTranslationDomain">
                <argument>EvpBankChargeBundleAdmin</argument>
            </call>
            <call method="setModelManager">
                <argument type="service" id="evp_admin.sonata.model_manager" />
            </call>
        </service>

        <service id="evp_bank_charge.charge_status_manager"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeStatusManager">

            <argument type="service" id="event_dispatcher"/>
        </service>

        <service id="evp_bank_charge.charge_helper"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeHelper">
            <argument type="service" id="evp_bank_charge.repository.charge"/>
        </service>

        <service id="evp_bank_charge.service.charge_sorter"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeSorter"/>

        <service id="evp_bank_charge.charge_processing_progress_registry"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeProcessingProgressRegistry"/>

        <service id="evp_bank_charge.charge_scope_resolver"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeScopeResolver"/>

        <service id="evp_bank_charge.charge_finalizer"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeFinalizer">

            <argument type="service" id="evp_bank_charge.repository.charge"/>
            <argument type="service" id="evp_bank_charge.charge_currency_renewer"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_charge.charge_currency_renewer"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeCurrencyRenewer">

            <argument type="service" id="evp_currency.currency_renewer"/>
            <argument type="service" id="evp_bank_charge.charge_factory"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_charge.permission_checker" class="Evp\Bundle\BankChargeBundle\Service\ChargePermissionChecker">
            <argument id="security.token_storage" type="service"/>
            <argument id="security.role_hierarchy" type="service"/>
        </service>

        <service id="evp_bank_charge.callback_provider"
                 class="Evp\Bundle\BankChargeBundle\Service\ChargeCallbackProvider">
            <tag name="evp_callback.provider" />

            <argument type="collection">
                <argument>PT5S</argument>
                <argument>PT15S</argument>
                <argument>PT30S</argument>
                <argument>PT1M</argument>
                <argument>PT5M</argument>
                <argument>PT30M</argument>
                <argument>PT1H</argument>
            </argument>
            <argument type="service" id="evp_bank_charge.repository.charge_callback"/>
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
