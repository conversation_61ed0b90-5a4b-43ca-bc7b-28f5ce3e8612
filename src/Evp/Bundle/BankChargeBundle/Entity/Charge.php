<?php

declare(strict_types=1);

namespace Evp\Bundle\BankChargeBundle\Entity;

use BeSimple\SoapBundle\ServiceDefinition\Annotation as Soap;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Component\Money\Money;

/**
 * @uses Soap
 */
class Charge
{
    public const ROLE_BANK_CHARGE_MASTER = 'CREATE_MASTER';
    public const ROLE_BANK_CHARGE_SIMPLE = 'ROLE_EVP_BANK_CHARGE_ADMIN_CHARGE_CREATE';

    public const STATUS_NEW = 'new';
    public const STATUS_QUEUED = 'queued';
    public const STATUS_WAITING_FUNDS = 'waiting_funds';
    public const STATUS_CANT_PROCESS = 'cant_process';
    public const STATUS_DONE = 'done';
    public const STATUS_PREPARED = 'prepared';

    public const PURPOSE_MACRO = 'macro';
    public const PURPOSE_WALLET = 'wallet';
    public const PURPOSE_CARD_ORDER = 'card_order';
    public const PURPOSE_CARD_ENABLE = 'card_enable';
    public const PURPOSE_CARD_CANCEL = 'card_cancel';
    public const PURPOSE_CARD_MONTHLY_FEE = 'card_monthly_fee';
    public const PURPOSE_CARD_CHANGE_DELIVERY_ADDRESS = 'card_change_delivery_address';
    public const PURPOSE_SEPA_TRANSFER_CANCELLATION_REQUEST = 'sepa_transfer_cancellation_request';
    public const PURPOSE_TARGET2RTGS_TRANSFER_CANCELLATION_REQUEST = 'target2rtgs_transfer_cancellation_request';
    public const PURPOSE_TARGET2_INVALID_ACCOUNT_TOP_UP = 'target2_invalid_account_top_up';
    public const PURPOSE_TARGET2_RTGS_INVALID_ACCOUNT_TOP_UP = 'target2_rtgs_invalid_account_top_up';
    public const PURPOSE_CLIENT_MONTHLY_CHARGE = 'client_monthly_charge';
    public const PURPOSE_CLIENT_CREATION_CHARGE = 'client_creation_charge';
    public const PURPOSE_USER_INACTIVITY = 'user_inactivity';
    public const PURPOSE_PLAIS = 'plais';
    public const PURPOSE_DEBT_COLLECTION_COMMISSION = 'debt_collection_commission';
    public const PURPOSE_INTEREST_FEE = 'interest_fee';
    public const PURPOSE_CLIENT_MONTHLY_FEE = 'client_monthly_fee';
    public const PURPOSE_CHECKOUT_DELAYED_COMMISSION = 'checkout_delayed_commission';
    public const PURPOSE_SMS_CHARGE = 'sms_charge';
    public const PURPOSE_IBAN_ACTIVATION_FEE = 'iban_activation_fee';
    public const PURPOSE_IBAN_MONTHLY_FEE = 'iban_monthly_fee';
    public const PURPOSE_EPAY_CASH_OUT_FEE = 'epay_cash_out_fee';
    public const PURPOSE_CLEARING = 'clearing';

    /**
     * @var string[]
     */
    private static array $statuses = [
        self::STATUS_NEW,
        self::STATUS_WAITING_FUNDS,
        self::STATUS_DONE,
        self::STATUS_QUEUED,
        self::STATUS_PREPARED,
        self::STATUS_CANT_PROCESS,
    ];

    /**
     * @var int;
     * @Soap\ComplexType("int", nillable=true)
     */
    protected $id;

    /**
     * @var DateTime;
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $createdAt;

    /**
     * @var DateTime;
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $updatedAt;

    /**
     * @var string
     * @Soap\ComplexType("string")
     */
    protected $details;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $macroService;

    /**
     * @var string
     */
    protected $purpose;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $conversionAllowed;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $installmentPaymentsAllowed;

    /**
     * @var string
     */
    protected $amount;

    /**
     * @var string
     */
    protected $amountCurrency;

    /**
     * @var Money
     * @Soap\ComplexType("Evp\Component\Money\Money")
     */
    protected $amountMoney;

    /**
     * @var string
     */
    protected $chargedAmount;

    /**
     * @var string
     */
    protected $chargedAmountCurrency;

    /**
     * @var Money
     * @Soap\ComplexType("Evp\Component\Money\Money", nillable=true)
     */
    protected $chargedAmountMoney;

    /**
     * @var int
     */
    protected $version;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $status;

    /**
     * @var ChargeAccountingTransaction[]
     */
    protected $chargeAccountingTransactions;

    /**
     * @var Account
     * @Soap\ComplexType("Evp\Bundle\BankAccountBundle\Entity\Account", nillable=true)
     */
    protected $account;

    protected ?Client $client;
    protected ?ChargeCallback $chargeCallback;

    /**
     * @var bool
     */
    protected $fallbackToOtherAccountsAllowed;

    /**
     * @var ChargeOperation[]
     */
    protected $operations;

    /**
     * Class constructor
     */
    public function __construct()
    {
        $this->chargeAccountingTransactions = new ArrayCollection();
        $this->operations = new ArrayCollection();
        $this->conversionAllowed = false;
        $this->fallbackToOtherAccountsAllowed = true;
        $this->installmentPaymentsAllowed = false;
        $this->status = self::STATUS_NEW;
        $this->macroService = false;
        $this->client = null;
        $this->chargeCallback = null;
    }

    /**
     * @return string[]
     */
    public static function getStatuses()
    {
        return self::$statuses;
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param DateTime $createdAt
     *
     * @return $this
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * @return DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param DateTime $updatedAt
     *
     * @return $this
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @param string $details
     *
     * @return $this
     */
    public function setDetails($details)
    {
        $this->details = $details;

        return $this;
    }

    /**
     * @return string
     */
    public function getDetails()
    {
        return $this->details;
    }

    /**
     * @return string|null
     */
    public function getAmount()
    {
        return $this->amountMoney === null ? null : $this->amountMoney->getAmount();
    }

    /**
     * @return string|null
     */
    public function getAmountCurrency()
    {
        return $this->amountMoney === null ? null : $this->amountMoney->getCurrency();
    }

    /**
     * @param Money $amount
     * @return $this
     */
    public function setAmountMoney(Money $amount)
    {
        $this->amountMoney = $amount;
        $this->amount = $amount->getAmount();
        $this->amountCurrency = $amount->getCurrency();

        return $this;
    }

    public function setChargedAmountMoney(Money $amount): self
    {
        $this->chargedAmountMoney = $amount;
        $this->chargedAmount = $amount->getAmount();
        $this->chargedAmountCurrency = $amount->getCurrency();

        return $this;
    }

    /**
     * @return Money
     */
    public function getAmountMoney()
    {
        return $this->amountMoney;
    }

    /**
     * @return string|null
     */
    public function getChargedAmount()
    {
        return $this->chargedAmountMoney === null ? null : $this->chargedAmountMoney->getAmount();
    }

    /**
     * @return string|null
     */
    public function getChargedAmountCurrency()
    {
        return $this->chargedAmountMoney === null ? null : $this->chargedAmountMoney->getCurrency();
    }

    /**
     * @param Money $amount
     * @return $this;
     */
    public function addChargedAmountMoney(Money $amount)
    {
        $this->chargedAmountMoney = $this->chargedAmountMoney !== null
            ? $this->chargedAmountMoney->add($amount)
            : $amount;

        $this->chargedAmount = $this->getChargedAmount();
        $this->chargedAmountCurrency = $this->getChargedAmountCurrency();

        return $this;
    }

    /**
     * @return Money
     */
    public function getChargedAmountMoney()
    {
        return $this->chargedAmountMoney;
    }

    /**
     * @return Money
     */
    public function getNeededAmountMoney()
    {
        return ($charged = $this->getChargedAmountMoney())
            ? $this->getAmountMoney()->sub($charged)
            : $this->getAmountMoney();
    }

    /**
     * @return int
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param string $status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param bool $macroService
     *
     * @return $this
     */
    public function setMacroService($macroService)
    {
        $this->macroService = $macroService;
        if ($macroService) {
            $this->purpose = self::PURPOSE_MACRO;
        } elseif ($this->purpose === self::PURPOSE_MACRO) {
            $this->purpose = null;
        }

        return $this;
    }

    /**
     * @return bool
     */
    public function isMacroService()
    {
        return $this->macroService;
    }

    /**
     * @param string $purpose
     *
     * @return $this
     */
    public function setPurpose($purpose)
    {
        if ($purpose === self::PURPOSE_MACRO) {
            $this->macroService = true;
        } elseif ($purpose === null && $this->macroService) {
            $purpose = self::PURPOSE_MACRO;
        } else {
            $this->macroService = false;
        }
        $this->purpose = $purpose;

        return $this;
    }

    /**
     * @return string
     */
    public function getPurpose()
    {
        return $this->purpose;
    }

    /**
     * @param bool $conversionAllowed
     *
     * @return $this
     */
    public function setConversionAllowed($conversionAllowed)
    {
        $this->conversionAllowed = $conversionAllowed;

        return $this;
    }

    /**
     * @return bool
     */
    public function isConversionAllowed()
    {
        return $this->conversionAllowed;
    }

    /**
     * @param bool $installmentPaymentsAllowed
     *
     * @return $this
     */
    public function setInstallmentPaymentsAllowed($installmentPaymentsAllowed)
    {
        $this->installmentPaymentsAllowed = $installmentPaymentsAllowed;

        return $this;
    }

    /**
     * @return bool
     */
    public function getInstallmentPaymentsAllowed()
    {
        return $this->installmentPaymentsAllowed;
    }

    /**
     * @param AccountingTransaction $transaction
     *
     * @return $this
     */
    public function addAccountingTransaction(AccountingTransaction $transaction)
    {
        $this->addChargeAccountingTransaction(
            new ChargeAccountingTransaction($transaction)
        );

        return $this;
    }

    /**
     * @param ChargeAccountingTransaction $transaction
     *
     * @return $this
     */
    public function addChargeAccountingTransaction($transaction)
    {
        $this->chargeAccountingTransactions[] = $transaction;
        $transaction->setCharge($this);

        return $this;
    }

    /**
     * @return ChargeAccountingTransaction[]
     */
    public function getChargeAccountingTransactions()
    {
        return $this->chargeAccountingTransactions;
    }

    /**
     * @param Account $account
     *
     * @return $this
     */
    public function setAccount(Account $account)
    {
        $this->account = $account;

        return $this;
    }

    /**
     * @return Account
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * @return bool
     */
    public function isFallbackToOtherAccountsAllowed()
    {
        return $this->fallbackToOtherAccountsAllowed;
    }

    /**
     * @param bool $fallbackToOtherAccountsAllowed
     *
     * @return $this
     */
    public function setFallbackToOtherAccountsAllowed($fallbackToOtherAccountsAllowed)
    {
        $this->fallbackToOtherAccountsAllowed = $fallbackToOtherAccountsAllowed;

        return $this;
    }

    /**
     * @param ChargeOperation $operation
     *
     * @return $this
     */
    public function addOperation(ChargeOperation $operation)
    {
        $this->operations[] = $operation;
        $operation->setCharge($this);

        return $this;
    }

    /**
     * @return ChargeOperation[]
     */
    public function getOperations()
    {
        return $this->operations;
    }

    /**
     * On pre persist and on pre update
     */
    public function onPreSave()
    {
        if ($this->amountMoney !== null) {
            $this->amount = $this->amountMoney->getAmount();
            $this->amountCurrency = $this->amountMoney->getCurrency();
        } else {
            $this->amount = null;
            $this->amountCurrency = null;
        }
        if ($this->chargedAmountMoney !== null) {
            $this->chargedAmount = $this->chargedAmountMoney->getAmount();
            $this->chargedAmountCurrency = $this->chargedAmountMoney->getCurrency();
        } else {
            $this->chargedAmount = null;
            $this->chargedAmountCurrency = null;
        }
    }

    /**
     * On post load
     */
    public function onPostLoad()
    {
        if ($this->amount !== null && $this->amountCurrency !== null) {
            $this->amountMoney = new Money($this->amount, $this->amountCurrency);
        }
        if ($this->chargedAmount !== null && $this->chargedAmountCurrency !== null) {
            $this->chargedAmountMoney = new Money($this->chargedAmount, $this->chargedAmountCurrency);
        }
    }

    public static function create()
    {
        return new static();
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function setChargeCallback(ChargeCallback $chargeCallback): self
    {
        $this->chargeCallback = $chargeCallback;

        return $this;
    }

    /**
     * @return ChargeCallback|null
     */
    public function getChargeCallback(): ?ChargeCallback
    {
        return $this->chargeCallback;
    }
}
