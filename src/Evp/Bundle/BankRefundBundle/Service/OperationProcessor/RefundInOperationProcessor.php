<?php

namespace Evp\Bundle\BankRefundBundle\Service\OperationProcessor;

use DateTime;
use Doctrine\Common\Persistence\ObjectManager;
use Evp\Bundle\AccountingBundle\Entity\AccountingOperation;
use Evp\Bundle\AccountingBundle\Exception\AlreadyExistsException;
use Evp\Bundle\AccountingBundle\Repository\AccountingOperationRepository;
use Evp\Bundle\AccountingBundle\Service\AccountingClient;
use Evp\Bundle\AccountingBundle\Service\AccountingGroupKeyResolver;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\AccountingBundle\Service\AccountingSoapClient;
use Evp\Bundle\AccountingBundle\Service\PartnerAccountingProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Bundle\BankRefundBundle\Entity\RefundInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp_Api_Accounting_Operation;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

class RefundInOperationProcessor extends BaseOperationProcessor
{
    private AccountingExceptionHandler $accountingExceptionHandler;
    protected $objectManager;
    protected $internalOperationHandler;
    protected $accountingSoapClient;
    protected $accountingOperationRepository;
    protected $accountingGroupKeyResolver;
    protected $accountingRestClient;
    protected $accountTypeAtDateProvider;
    private PartnerAccountingProcessor $partnerAccountingProcessor;
    protected string $templateKeyFundsOnWayIn;
    protected $useMapkeyPairForNegatingContisOperation;
    private $skipAccountList;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        ObjectManager $objectManager,
        InternalOperationHandler $internalOperationHandler,
        AccountingSoapClient $accountingSoapClient,
        AccountingOperationRepository $accountingOperationRepository,
        AccountingGroupKeyResolver $accountingGroupKeyResolver,
        AccountingClient $accountingRestClient,
        AccountTypeAtDateProvider $accountTypeAtDateProvider,
        PartnerAccountingProcessor $partnerAccountingProcessor,
        string $templateKeyFundsOnWayIn,
        array $useMapkeyPairForNegatingContisOperation,
        array $skipAccountList
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->objectManager = $objectManager;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->accountingSoapClient = $accountingSoapClient;
        $this->accountingOperationRepository = $accountingOperationRepository;
        $this->accountingGroupKeyResolver = $accountingGroupKeyResolver;
        $this->accountingRestClient = $accountingRestClient;
        $this->accountTypeAtDateProvider = $accountTypeAtDateProvider;
        $this->partnerAccountingProcessor = $partnerAccountingProcessor;
        $this->templateKeyFundsOnWayIn = $templateKeyFundsOnWayIn;
        $this->useMapkeyPairForNegatingContisOperation = $useMapkeyPairForNegatingContisOperation;
        $this->skipAccountList = $skipAccountList;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     * @throws \Evp\Bundle\AccountingBundle\Exception\OperationProcessorException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundInOperation)) {
            throw new \InvalidArgumentException(sprintf(
                'Unsupported operation class (operation_class=%s) expected Operation\TransferInOperation',
                get_class($operation)
            ));
        }

        $transfer = $operation->getRefund()->getTransfer();
        if ($transfer->getAmountMoney()->isZero()) {
            throw new \InvalidArgumentException('Unsupported zero amount');
        }

        if ($transfer instanceof TransferIn) {
            $this->processTransferIn($operation, $transfer);
        } else {
            if ($transfer instanceof TransferInternal) {
                $this->processTransferInternal($operation, $transfer);
            } else {
                throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
            }
        }
    }

    /**
     * Process TransferIn
     *
     * @param \Evp\Bundle\BankRefundBundle\Entity\RefundInOperation $operation
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferIn      $transfer
     */
    protected function processTransferIn(RefundInOperation $operation, TransferIn $transfer)
    {
        if ($transfer->getStatus() !== Transfer::STATUS_DONE) {
            $this->saveAsSkipped($operation); // There are no operations in accounting, so nothing to revert
            return;
        }

        if (in_array($transfer->getDebitAccount()->getNumber(), $this->skipAccountList, true)) {
            $this->saveAsSkipped($operation); // Operations were not created, negate operations does not needed
            return;
        }

        if ($transfer->getId() === 6283784) {
            // Will be removed straight away afterwards after this transfer create_operation message will be handled
            // It is very old transfer and all operations were already created long ago manually
            // We do not need to do anything with it anymore
            $this->saveAsSkipped($operation);
            return;
        }

        $groupToNegate = $this->accountingGroupKeyResolver->resolveGroupKeyForIn($operation, true);

        // Must be called before negateRelatedOperations() because operations created in that
        // method must have the same MapKeys.
        if ($operation->getRefund()->isFailedInBank()) {
            $this->accountingRestClient->markOperationsAsFailedInBankForIncoming($groupToNegate);
        }

        $accountingOperations = $this->accountingSoapClient->negateRelatedOperations(
            $groupToNegate,
            $operation->getRefundedAmountMoney(),
            $operation->getCreatedAt(),
            'Negate:' . $operation->getId()
        );

        $negatedContisOperation = $this->negateContisOperationIfNeeded($operation, $transfer, $accountingOperations);
        if ($negatedContisOperation !== null) {
            $accountingOperations[] = $negatedContisOperation;
        }

        // Associate the new accounting operations with the refund operation
        if (count($accountingOperations) > 0) {
            $this->saveAccountingOperations($accountingOperations, $operation);
        }

        try {
            $this->partnerAccountingProcessor->processOperation($operation);
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    /**
     * @param Evp_Api_Accounting_Operation[] $newAccountingOperations
     * @param Operation $operation
     */
    protected function saveAccountingOperations($newAccountingOperations, Operation $operation)
    {
        foreach ($newAccountingOperations as $newAccountingOperation) {
            $accountingOperation = new AccountingOperation();
            $accountingOperation
                ->setOperation($operation)
                ->setAccountingId($newAccountingOperation->getId())
                ->setCreatedAt(new \DateTime());
            // If persist through manager, update other places like this to use the manager as well
            $this->objectManager->persist($accountingOperation);
        }
    }

    /**
     * Process TransferInternal
     *
     * @param \Evp\Bundle\BankRefundBundle\Entity\RefundInOperation  $operation
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferInternal $transfer
     *
     * @throws \Evp\Bundle\AccountingBundle\Exception\OperationProcessorException
     */
    protected function processTransferInternal(RefundInOperation $operation, TransferInternal $transfer)
    {
        $this->internalOperationHandler->handleIn(
            $operation,
            $transfer->getCreditAccount(),
            $transfer->getDebitAccount(),
            $operation->getRefundedAmountMoney()->negate(),
            $transfer->getDetails()
        );

        try {
            $this->partnerAccountingProcessor->processOperation($operation);
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    /**
     * A contis operation D44403 K44409 is created for operations D44402 K44403. We negate D44402 K44403 in
     * processTransferIn and operations D44403 K44402 are created. This method finds the latter operation so that it
     * can be used as basis for building a negating operation D44409 K44403.
     *
     * Contis intermediary D44403 K44409 operations cannot be put in a group because it causes problems elsewhere, so
     * we need to negate them manually.
     *
     * @param RefundInOperation $operation
     * @param TransferIn $transfer
     * @param $accountingOperations
     *
     * @return Evp_Api_Accounting_Operation|null
     *
     * @throws \Evp\Bundle\AccountingBundle\Exception\AccountingException
     */
    private function negateContisOperationIfNeeded(RefundInOperation $operation, TransferIn $transfer, $accountingOperations)
    {
        if (count($accountingOperations) === 0) {
            return null;
        }

        $createReversedContisOperationForOperation = $this->findOperationToBaseNegatingContisOperation(
            $accountingOperations
        );

        if ($createReversedContisOperationForOperation === null) {
            return null;
        }

        $operationDate = DateTime::createFromFormat(
            'Y-m-d',
            $createReversedContisOperationForOperation->getDate()
        );

        $accountTypeAtDate = $this->accountTypeAtDateProvider
            ->getAccountTypeAtDate($operation->getAccount(), $operationDate)
        ;
        if (
            $operationDate === false
            || !in_array($accountTypeAtDate, [Account::TYPE_CONTIS, Account::TYPE_CARD_V2], true)
        ) {
            return null;
        }

        try {
            return $this->accountingSoapClient->addOperationByTemplateKey(
                $this->builder->build(
                    $transfer->getDebitAccount()->getClient(),
                    $createReversedContisOperationForOperation->getAmount()->toMoney()->abs()->negate(),
                    $createReversedContisOperationForOperation->getTitle(),
                    $operationDate
                ),
                $this->templateKeyFundsOnWayIn,
                \Evp_Api_Accounting_Company::getEvpCompany(),
                'Negate:' . $operation->getId() . ':FUNDS_ON_WAY'
            );
        } catch (AlreadyExistsException $exception) {
            // Ignore
        }

        return null;
    }

    /**
     * @param Evp_Api_Accounting_Operation[] $accountingOperations
     *
     * @return Evp_Api_Accounting_Operation|null
     */
    private function findOperationToBaseNegatingContisOperation($accountingOperations)
    {
        foreach ($accountingOperations as $accountingOperation) {
            if (
                count(
                    array_filter(
                        $accountingOperation->getOperationItems(),
                        function (\Evp_Api_Accounting_OperationItem $item) {
                            return
                                $item->getTemplate()->getAccount()->isDebit()
                                && $item->getTemplate()->getAccount()->getKey()
                                    === $this->useMapkeyPairForNegatingContisOperation['debit']
                            ;
                        }
                    )
                ) > 0
                && count(
                    array_filter(
                        $accountingOperation->getOperationItems(),
                        function (\Evp_Api_Accounting_OperationItem $item) {
                            return
                                $item->getTemplate()->getAccount()->isCredit()
                                && $item->getTemplate()->getAccount()->getKey()
                                    === $this->useMapkeyPairForNegatingContisOperation['credit']
                            ;
                        }
                    )
                ) > 0
            ) {
                return $accountingOperation;
            }
        }
        return null;
    }
}
