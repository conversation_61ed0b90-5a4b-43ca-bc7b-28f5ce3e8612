<?php

namespace Evp\Bundle\BankRefundBundle\Service\OperationProcessor;

use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\AccountingBankResolver;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * RefundOutOperationProcessor
 */
class RefundOutOperationProcessor extends BaseOperationProcessor
{
    protected $internalOperationHandler;
    protected $externalOperationHandler;
    protected $accountingBankResolver;
    private ExternalPartnerOperationHandler $externalPartnerOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        ExternalOperationHandler $externalOperationHandler,
        ExternalPartnerOperationHandler $externalPartnerOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler,
        AccountingBankResolver $accountingBankResolver
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->externalOperationHandler = $externalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
        $this->externalPartnerOperationHandler = $externalPartnerOperationHandler;
        $this->accountingBankResolver = $accountingBankResolver;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundOutOperation)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected RefundOutOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if ($transfer->getAmountMoney()->isZero()) {
            throw new \InvalidArgumentException('Unsupported zero amount');
        }

        if ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } else {
            if ($transfer instanceof TransferOut) {
                $this->processTransferOut($operation, $transfer);
            } else {
                throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
            }
        }
    }

    /**
     * Process TransferInternal
     *
     * @param \Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation $operation
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferInternal $transfer
     *
     * @throws \Evp\Bundle\AccountingBundle\Exception\OperationProcessorException
     */
    protected function processTransferInternal(RefundOutOperation $operation, TransferInternal $transfer)
    {
        $this->internalOperationHandler->handleOut(
            $operation,
            $operation->getAccount(),
            $transfer->getDebitAccount(),
            $operation->getRefundedAmountMoney()->negate(),
            $transfer->getDetails()
        );

        try {
            $this->internalPartnerOperationHandler->handleOut(
                $operation,
                $transfer,
                $operation->getRefundedAmountMoney()->negate()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    /**
     * @param RefundOutOperation $operation
     * @param TransferOut $transfer
     */
    private function processTransferOut(RefundOutOperation $operation, TransferOut $transfer)
    {
        $this->externalOperationHandler->handleOut(
            $operation,
            $operation->getAccount(),
            $transfer->getCreditAccountCovenanteeId(),
            $transfer->getBeneficiary(),
            $this->accountingBankResolver->resolveBank($transfer, $operation),
            $operation->getRefundedAmountMoney()->negate(),
            $this->getDetails($transfer),
            $operation->getRefund()->getOperationDate()
        );

         try {
             $this->externalPartnerOperationHandler->handleOut(
                 $transfer,
                 $operation->getAccount(),
                 $operation,
                 $this->accountingBankResolver->resolveBank($transfer, $operation),
                 $operation->getRefundedAmountMoney()->negate()
             );
         } catch (Exception $exception) {
             $this->accountingExceptionHandler->handle($exception);
         }
    }

    private function getDetails(Transfer $transfer)
    {
        return $transfer->getDetails() !== null && $transfer->getDetails() !== ''
            ? $transfer->getDetails()
            : $transfer->getReferenceNumber()
        ;
    }
}
