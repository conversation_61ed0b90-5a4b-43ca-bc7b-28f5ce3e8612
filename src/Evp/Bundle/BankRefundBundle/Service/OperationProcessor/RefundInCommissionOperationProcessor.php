<?php

namespace Evp\Bundle\BankRefundBundle\Service\OperationProcessor;

use Evp\Bundle\BankRefundBundle\Entity\RefundInCommissionOperation;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * RefundInCommissionOperationProcessor
 */
class RefundInCommissionOperationProcessor extends BaseOperationProcessor
{
    /**
     * @var InternalOperationHandler
     */
    protected $internalOperationHandler;

    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    )
    {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundInCommissionOperation)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if (!($transfer instanceof TransferInInterface)) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $this->internalOperationHandler->handleInCommission(
            $operation,
            $operation->getAccount(),
            $operation->getRefundedAmountMoney()->negate(),
            $transfer->getPurpose()
        );

        try {
            $this->internalPartnerOperationHandler->handleInCommission(
                $operation,
                $operation->getAccount(),
                $operation->getRefundedAmountMoney()->negate(),
                $transfer->getPurpose()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
