<?php

namespace Evp\Bundle\BankRefundBundle\Service\OperationProcessor;

use Evp\Bundle\BankRefundBundle\Entity\RefundOutCommissionOperation;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * RefundOutCommissionOperationProcessor
 */
class RefundOutCommissionOperationProcessor extends BaseOperationProcessor
{
    /**
     * @var InternalOperationHandler
     */
    protected $internalOperationHandler;

    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    )
    {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof RefundOutCommissionOperation)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected TransferOutCommissionOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getRefund()->getTransfer();
        if (!($transfer instanceof TransferOutInterface)) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $this->internalOperationHandler->handleOutCommission(
            $operation,
            $operation->getAccount(),
            $operation->getRefundedAmountMoney()->negate()
        );

        try {
            $this->internalPartnerOperationHandler->handleOutCommission(
                $operation,
                $operation->getAccount(),
                $operation->getRefundedAmountMoney()->negate()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
