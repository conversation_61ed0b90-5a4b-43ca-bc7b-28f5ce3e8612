<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <parameters>
        <parameter key="evp_accounting.operation_processor.vmi_covenantee_id">4126</parameter>
        <parameter key="evp_bank_refund.use_mapkey_pair_for_negating_contis_operation" type="collection">
            <parameter key="debit">44403</parameter>
            <parameter key="credit">44402</parameter>
        </parameter>
    </parameters>

    <services>
        <service id="evp_bank_refund.operation_processor.refund_in"
                class="Evp\Bundle\BankRefundBundle\Service\OperationProcessor\RefundInOperationProcessor"
                parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor" class="Evp\Bundle\BankRefundBundle\Entity\RefundInOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument id="evp_accounting.accounting_soap_client" type="service"/>
            <argument id="evp_accounting.repository.accounting_operation" type="service"/>
            <argument id="evp_accounting.accounting_group_key_resolver" type="service"/>
            <argument id="evp_accounting.client.accounting_client" type="service"/>
            <argument id="evp_bank_account.service.account_type_at_date_provider" type="service"/>
            <argument type="service" id="evp_accounting.partner_accounting_processor"/>
            <argument>W2P_TRANSFER_FUNDS_ON_WAY_IN</argument>
            <argument>%evp_bank_refund.use_mapkey_pair_for_negating_contis_operation%</argument>
            <argument>%evp_accounting.operation_processor.paysera_macro_accounts%</argument>
        </service>

        <service id="evp_bank_refund.operation_processor.refund_in_commission"
                class="Evp\Bundle\BankRefundBundle\Service\OperationProcessor\RefundInCommissionOperationProcessor"
                parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                    class="Evp\Bundle\BankRefundBundle\Entity\RefundInCommissionOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_bank_refund.operation_processor.refund_out"
                class="Evp\Bundle\BankRefundBundle\Service\OperationProcessor\RefundOutOperationProcessor"
                parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                    class="Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.bank_resolver"/>
        </service>

        <service id="evp_bank_refund.operation_processor.refund_out_commission"
                class="Evp\Bundle\BankRefundBundle\Service\OperationProcessor\RefundOutCommissionOperationProcessor"
                parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                    class="Evp\Bundle\BankRefundBundle\Entity\RefundOutCommissionOperation" />

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>
    </services>
</container>
