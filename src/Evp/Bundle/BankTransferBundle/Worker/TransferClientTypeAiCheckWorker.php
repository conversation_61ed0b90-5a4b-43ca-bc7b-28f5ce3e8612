<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Worker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankTransferBundle\Entity\ClientTypePrediction;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Repository\ClientTypePredictionRepository;
use Evp\Bundle\BankTransferBundle\Repository\TransferRepository;
use Evp\Bundle\RabbitMqExtensionBundle\Service\JobWorkerInterface;
use Exception;
use Paysera\Client\UserTypeAi\Entity\Prediction;
use Paysera\Client\UserTypeAi\UserTypeAi;
use Psr\Log\LoggerInterface;

class TransferClientTypeAiCheckWorker implements JobWorkerInterface
{
    const JOB_CHECK_TRANSFER_OUT = 'gateway.transfer_client_type_ai_checker_worker_transfer_out';
    const JOB_CHECK_TRANSFER_IN = 'gateway.transfer_client_type_ai_checker_worker_transfer_in';

    private EntityManagerInterface $entityManager;
    private TransferRepository $transferRepository;
    private UserTypeAi $userTypeAiClient;
    private ClientTypePredictionRepository $clientTypePredictionRepository;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        TransferRepository $transferRepository,
        UserTypeAi $userTypeAiClient,
        ClientTypePredictionRepository $clientTypePredictionRepository,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->transferRepository = $transferRepository;
        $this->userTypeAiClient = $userTypeAiClient;
        $this->clientTypePredictionRepository = $clientTypePredictionRepository;
        $this->logger = $logger;
    }

    /**
     * @throws Exception
     */
    public function work($data)
    {
        if (!isset($data['transfer_id'])) {
            $this->logger->error('transfer_id was not provided', [$data]);
            return;
        }

        $transfer = $this->transferRepository->findOneById($data['transfer_id']);
        if ($transfer === null) {
            $this->logger->warning('Transfer not found for client_type resolving', [$data['transfer_id']]);
            return;
        }

        if ($transfer instanceof TransferInternal) {
            $this->logger->warning('Internal transfer provided', [$transfer->getId()]);
            return;
        }

        $existingPrediction = $this->clientTypePredictionRepository->findOneByTransfer($transfer);
        if ($existingPrediction !== null) {
            $this->logger->info('Prediction already exists for transfer.', [$transfer->getId(), $existingPrediction->getId()]);
            return;
        }

        $predictableName = null;
        if ($transfer instanceof TransferOut) {
            $name = $transfer->getBeneficiary()->getDisplayName();
            if ($name === null || $name === '') {
                $this->logger->info('TransferOut beneficiary has empty name.', [$transfer->getId(), $transfer->getBeneficiary()->getId()]);
                return;
            }

            $predictableName = (new Prediction())->setName($name);
        } else if ($transfer instanceof TransferIn) {
            $name = $transfer->getPayer()->getDisplayName();
            if ($name === null || $name === '') {
                $this->logger->info('TransferIn payer has empty name.', [$transfer->getId(), $transfer->getPayer()->getId()]);
                return;
            }

            $predictableName = (new Prediction())->setName($name);
        }

        if ($predictableName === null) {
            $this->logger->warning('Prediction is null. Transfer might be unsupported type', [$transfer->getId()]);
            return;
        }

        try {
            $prediction = $this->userTypeAiClient->createPrediction($predictableName);
        } catch (Exception $exception) {
            $this->logger->info('Client_type resolving could not get prediction', [$transfer->getId()]);
            throw $exception;
        }

        if ($prediction === null) {
            return;
        }

        $newPrediction = (new ClientTypePrediction())
            ->setClientName($prediction->getName())
            ->setClientType($prediction->getType())
            ->setConfidence((float)$prediction->getConfidence())
            ->setTransfer($transfer)
        ;

        $this->entityManager->persist($newPrediction);
        $this->entityManager->flush();
    }
}
