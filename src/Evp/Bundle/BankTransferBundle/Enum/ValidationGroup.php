<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Enum;

class ValidationGroup
{
    public const DEFAULT = 'Default';

    public const TRANSFER = 'Transfer';

    public const TRANSFER_NUMBER = 'number';
    public const BANK_NAME = 'bank_name';
    public const AMOUNT = 'amount';
    public const PURPOSE = 'purpose';
    public const DETAILS = 'details';
    public const OPERATION_DATE = 'operation_date';
    public const CHARGE_TYPE = 'charge_type';
    public const URGENCY = 'urgency';

    public const BENEFICIARY = 'Beneficiary';
    public const BENEFICIARY_IBAN = 'beneficiary_iban';
    public const BENEFICIARY_NAME = 'beneficiary_name';
    public const BENEFICIARY_EMAIL = 'beneficiary_email';
    public const BENEFICIARY_ACCOUNT_TYPE = 'beneficiary_account_type';
    public const BENEFICIARY_COUNTRY = 'beneficiary_country';
    public const BENEFICIARY_ADDRESS = 'beneficiary_address';
    public const BENEFICIARY_PHONE_NUMBER = 'beneficiary_phone_number';
    public const BENEFICIARY_RESIDENCE = 'beneficiary_residence';
    public const BENEFICIARY_STATE = 'beneficiary_state';
    public const BENEFICIARY_CITY = 'beneficiary_city';
    public const BENEFICIARY_STREET = 'beneficiary_street';
    public const BENEFICIARY_HOUSE = 'beneficiary_house';
    public const BENEFICIARY_APARTMENT = 'beneficiary_apartment';
    public const BENEFICIARY_POST_CODE = 'beneficiary_post_code';
    public const BENEFICIARY_BIRTH_DATE = 'beneficiary_birth_date';
    public const BENEFICIARY_IDENTIFIER_PERSONAL_NUMBER = 'beneficiary_identifier_personal_number';
    public const BENEFICIARY_ACCOUNT_NUMBER = 'beneficiary_account_number';
    public const BENEFICIARY_BANK_NAME = 'beneficiary_bank_name';
    public const BENEFICIARY_BANK_CODE = 'beneficiary_bank_code';
    public const BENEFICIARY_BANK_ADDRESS = 'beneficiary_bank_address';
    public const BENEFICIARY_BANK_ACCOUNT_TYPE = 'beneficiary_bank_account_type';
    public const BENEFICIARY_ADDITIONAL_INFORMATION = 'beneficiary_additional_information';
    public const BENEFICIARY_CORRESPONDENT_BANK_NAME = 'beneficiary_correspondent_bank_name';
    public const BENEFICIARY_CORRESPONDENT_BANK_ACCOUNT = 'beneficiary_correspondent_bank_account';
    public const BENEFICIARY_CORRESPONDENT_BANK_SWIFT = 'beneficiary_correspondent_bank_swift';
    public const REFERENCE_TO_BENEFICIARY = 'reference_to_beneficiary';
    public const BENEFICIARY_DOCUMENT_TYPE = 'beneficiary_document_type';
    public const BENEFICIARY_DOCUMENT_SERIES = 'beneficiary_document_series';
    public const BENEFICIARY_DOCUMENT_NUMBER = 'beneficiary_document_number';
    public const BENEFICIARY_DOCUMENT_ISSUER = 'beneficiary_document_issuer';
    public const BENEFICIARY_DOCUMENT_ISSUED_DATE = 'beneficiary_document_issued_date';
    public const BENEFICIARY_TAX_NUMBER = 'beneficiary_tax_number';
    public const BENEFICIARY_INN_CODE = 'beneficiary_inn_code';
    public const BENEFICIARY_TYPE = 'beneficiary_type';

    public const PAYER = 'Payer';
    public const PAYER_ACCOUNT_NUMBER = 'payer_account_number';
    public const PAYER_ADDITIONAL_INFORMATION = 'payer_additional_information';
    public const PAYER_NAME = 'payer_name';
    public const PAYER_BIRTH_DATE = 'payer_birth_date';
    public const PAYER_BIRTH_PLACE = 'payer_birth_place';
    public const PAYER_RESIDENCE = 'payer_residence';
    public const PAYER_ADDRESS = 'payer_address';
    public const PAYER_POST_CODE = 'payer_post_code';
    public const PAYER_COUNTRY = 'payer_country';
    public const PAYER_STATE = 'payer_state';
    public const PAYER_CITY = 'payer_city';
    public const PAYER_STREET = 'payer_street';
    public const PAYER_HOUSE = 'payer_house';
    public const PAYER_APARTMENT = 'payer_apartment';
    public const PAYER_DOCUMENT_TYPE = 'payer_document_type';
    public const PAYER_DOCUMENT_SERIES = 'payer_document_series';
    public const PAYER_DOCUMENT_NUMBER = 'payer_document_number';
    public const PAYER_DOCUMENT_ISSUER = 'payer_document_issuer';
    public const PAYER_TAX_IDENTIFIER = 'payer_tax_identifier';
    public const PAYER_ROIK = 'payer_roik';
    public const PAYER_IDENTIFICATION_TYPE = 'payer_identification_type';
    public const PAYER_IDENTIFICATION_NUMBER = 'payer_identification_number';
    public const PAYER_ACCOUNT_TYPE = 'payer_account_type';
    public const PAYER_CODE = 'payer_code';
    public const PAYER_DATE_OF_BIRTH = 'payer_date_of_birth';

    public const REFERENCE_TO_PAYER = 'reference_to_payer';

    public const REFERENCE_NUMBER = 'reference_number';
    public const SYSTEM_ID = 'system_id';
    public const VO_CODE = 'vo_code';
}
