<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

class InvalidBeneficiaryName extends Constraint
{
    public string $message = 'Provided beneficiary name is not valid';

    public function validatedBy(): string
    {
        return 'evp_invalid_beneficiary_name_validator';
    }

    public function getTargets(): string
    {
        return self::CLASS_CONSTRAINT;
    }
}
