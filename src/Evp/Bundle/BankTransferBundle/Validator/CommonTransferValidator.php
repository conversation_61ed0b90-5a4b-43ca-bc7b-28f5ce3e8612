<?php

namespace Evp\Bundle\BankTransferBundle\Validator;

use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\Validator\OcrCodeValidator as BaseOcrCodeValidator;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\CommonTransfer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\BankAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\PayseraAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\TaxAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Transfer;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\ConstraintValidator;

class CommonTransferValidator extends ConstraintValidator
{
    private $ocrCodeValidator;

    /**
     * @param BaseOcrCodeValidator $ocrCodeValidator
     */
    public function __construct(BaseOcrCodeValidator $ocrCodeValidator)
    {
        $this->ocrCodeValidator = $ocrCodeValidator;
    }

    /**
     * @param Transfer $transfer
     * @param Constraint|CommonTransfer $constraint
     */
    public function validate($transfer, Constraint $constraint)
    {
        if ($transfer->getBeneficiary() !== null) {
            if ($transfer->getBeneficiary()->getAccountByType(BankAccount::TYPE_BANK) === null) {
                if ($transfer->getUrgency() !== null) {
                    $this->context
                        ->buildViolation($constraint->unecessaryUrgency)
                        ->addViolation()
                    ;
                }
                if ($transfer->getChargeType() !== null) {
                    $this->context
                        ->buildViolation($constraint->unecessaryChargeType)
                        ->addViolation()
                    ;
                }
            }

            if ($transfer->getBeneficiary()->getAccountByType(PayseraAccount::TYPE_PAYSERA) === null) {
                if ($transfer->getPassword() !== null) {
                    $this->context
                        ->buildViolation($constraint->unsupportedPassword)
                        ->addViolation()
                    ;
                }

                if ($transfer->getReserveUntil() !== null) {
                    $this->context
                        ->buildViolation($constraint->unecessaryReserveUntil)
                        ->addViolation()
                    ;
                }
            }
        }

        if (
            $transfer->getBeneficiary() !== null &&
            $transfer->getBeneficiary()->getAccount() !== null
            && $transfer->getBeneficiary()->getAccount()->getType() !== TaxAccount::TYPE_TAX
        ) {
            $validationContext = $transfer->getValidationContext();

            $this->context
                ->getValidator()
                ->inContext($this->context)
                ->atPath('purpose')
                ->validate(
                    $transfer->getPurpose(),
                    new NotBlank(
                        [
                            'groups' => [ValidationGroup::DEFAULT, ValidationGroup::PURPOSE],
                        ]
                    ),
                    $validationContext ? $validationContext->getValidationGroups() : null
                )
            ;
        }

        if ($transfer->getPurpose() !== null && $transfer->getPurpose()->getOcrCode() !== null) {
            /** @var BankAccount $bankAccount */
            $bankAccount = $transfer->getBeneficiary()->getAccountByType(BankAccount::TYPE_BANK);
            if ($bankAccount === null) {
                $this->context
                    ->buildViolation($constraint->ocrCodeOnlyWithBankAccount)
                    ->atPath('purpose.ocrCode')
                    ->addViolation()
                ;

                return;
            }

            $country = null;
            if ($bankAccount->getIban() !== null) {
                $ibanInfo = new \IbanLib_IbanInfo($bankAccount->getIban());
                $country = $ibanInfo->Country() !== null ? strtoupper($ibanInfo->Country()) : null; // TODO: remove strtoupper
            } elseif ($bankAccount->getCountryCode() !== null) {
                $country = $bankAccount->getCountryCode() !== null ? strtoupper($bankAccount->getCountryCode()) : null; // TODO: remove strtoupper
            } elseif (
                $bankAccount->getBankAddress() !== null
                && $bankAccount->getBankAddress()->getCountryCode() !== null
            ) {
                $country = $bankAccount->getBankAddress()->getCountryCode() !== null
                    ? strtoupper($bankAccount->getBankAddress()->getCountryCode())  // TODO: remove strtoupper
                    : null
                ;
            }

            if (!$this->ocrCodeValidator->isValid($transfer->getPurpose()->getOcrCode(), $country)) {
                $this->context
                    ->buildViolation($constraint->ocrCodeInvalid)
                    ->atPath('purpose.ocrCode')
                    ->addViolation()
                ;
            }
        }
    }
}
