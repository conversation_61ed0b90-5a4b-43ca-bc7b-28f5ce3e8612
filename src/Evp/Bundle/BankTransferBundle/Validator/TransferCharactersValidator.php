<?php

namespace Evp\Bundle\BankTransferBundle\Validator;

use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\ForbiddenCharacter;
use Symfony\Component\Validator\Constraint;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Class TransferCharactersValidator
 *
 * Checks if transfer properties has forbidden characters
 */
class TransferCharactersValidator extends ConstraintValidator
{
    protected ValidatorInterface $validator;

    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    /**
     * Checks if the passed value is valid.
     *
     * @param Transfer   $transfer
     * @param Constraint $constraint
     *
     * @return bool
     */
    public function validate($transfer, Constraint $constraint): bool
    {
        if (!($transfer instanceof TransferOutBank)) {
            $this->context
                ->buildViolation('Given transfer is not TransferOutBank')
                ->addViolation()
            ;
            return false;
        }

        $beneficiary = $transfer->getResolvedBeneficiary();

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_NAME])
            && $beneficiary !== null
            && $beneficiary->getDisplayName()
        ) {
            $violations = $this->validator->validate(
                $beneficiary->getDisplayName(),
                new ForbiddenCharacter()
            );
            if (count($violations) > 0) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->addViolation()
                ;
                return false;
            }
        }

        if (ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])) {
            $violations = $this->validator->validate($transfer->getDetails(), new ForbiddenCharacter());
            if (count($violations) > 0) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->addViolation()
                ;
                return false;
            }
        }

        if (ValidationHelper::isProcessable($transfer, [ValidationGroup::BANK_NAME])) {
            $violations = $this->validator->validate($beneficiary->getBankName(), new ForbiddenCharacter());
            if (count($violations) > 0) {
                $this->context
                    ->buildViolation($constraint->message)
                    ->addViolation()
                ;
                return false;
            }
        }

        return true;
    }
}
