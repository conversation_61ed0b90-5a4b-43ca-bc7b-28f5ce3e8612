<?php
declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Validator;

use Evp\Bundle\BankBundle\Exception\IbanBankResolverException;
use Evp\Bundle\BankBundle\Service\IbanBankResolverInterface;
use Evp\Bundle\BankTransferBundle\Entity\BlockedIban;
use Evp\Bundle\BankTransferBundle\Entity\ForbiddenBicRule;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvert;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferMatcher\TransferMatcher;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class ForbiddenBeneficiaryValidator extends ConstraintValidator
{
    /**
     * @var BlockedIban[]
     */
    private $ibanList;

    /**
     * @var array
     */
    private $accountList;

    /**
     * @var array
     */
    private $bankList;

    /**
     * @var [][]
     */
    private $evpAccountList;

    /**
     * @var array
     */
    private $countryList;

    /**
     * @var ForbiddenBicRule[]
     */
    private $forbiddenBicRules;

    /**
     * @var ForbiddenBicRule[]
     */
    private $forbiddenCorrespondentBicRules;

    /**
     * @var string[]
     */
    private $ibanRegularExpressions;

    private $ibanBankResolver;
    private $transferMatcher;
    private $logger;

    public function __construct(
        IbanBankResolverInterface $ibanBankResolver,
        TransferMatcher $transferMatcher,
        LoggerInterface $logger
    ) {
        $this->ibanBankResolver = $ibanBankResolver;
        $this->transferMatcher = $transferMatcher;
        $this->logger = $logger;

        $this->ibanList = [];
        $this->accountList = [];
        $this->bankList = [];
        $this->evpAccountList = [];
        $this->countryList = [];
        $this->forbiddenBicRules = [];
        $this->ibanRegularExpressions = [];
    }

    public function addIban(
        $iban,
        $blockUrgent = true,
        $blockNonUrgent = true,
        $blockInternal = true,
        $blockExternal = true
    ) {
        foreach ($this->ibanList as $value) {
            if ($value->getIban() === $iban) {
                throw new \Exception('IBAN already set, only single occurence of IBAN supported');
            }
        }

        $blockedIban = new BlockedIban(
            $iban,
            $blockUrgent,
            $blockNonUrgent,
            $blockInternal,
            $blockExternal
        );

        $this->ibanList[] = $blockedIban;
    }

    /**
     * @param string $ibanRegularExpression
     *
     * @return $this
     */
    public function addIbanRegularExpression($ibanRegularExpression)
    {
        $this->ibanRegularExpressions[] = $ibanRegularExpression;

        return $this;
    }

    /**
     * Adds account to forbiden list
     *
     * @param string $country
     * @param string $account
     *
     * @return void
     */
    public function addAccount($country, $account)
    {
        $this->accountList[] = [$country, $account];
    }

    /**
     * Adds EVP account number to forbidden list
     *
     * @param string $evpAccountNumber
     * @param string[] $allowedPurposes
     * @param bool $blockInternal
     * @param bool $blockExternal
     */
    public function addEvpAccount(
        string $evpAccountNumber,
        array $allowedPurposes = [],
        bool $blockInternal = true,
        bool $blockExternal = true
    ) {
        if (isset($this->evpAccountList[$evpAccountNumber])) {
            throw new \Exception('EVP account number already set, only single occurence supported');
        }

        $this->evpAccountList[$evpAccountNumber] = [
            'allowed_purposes' => $allowedPurposes,
            'block_internal' => $blockInternal,
            'block_external' => $blockExternal,
        ];
    }

    /**
     * Adds bank to forbiden list
     *
     * @param string $bankKey
     */
    public function addBank($bankKey)
    {
        $this->bankList[] = $bankKey;
    }

    /**
     * Adds country to forbiden list
     *
     * @param string $countryCode
     */
    public function addCountry($countryCode)
    {
        $this->countryList[] = $countryCode;
    }

    public function addForbiddenBicRule(ForbiddenBicRule $rule): self
    {
        $this->forbiddenBicRules[] = $rule;

        return $this;
    }

    public function addForbiddenCorrespondentBicRule(ForbiddenBicRule $rule): self
    {
        $this->forbiddenCorrespondentBicRules[] = $rule;

        return $this;
    }

    /**
     * Validates provided beneficiary
     *
     * @param Transfer                                $transfer
     * @param \Symfony\Component\Validator\Constraint $constraint
     *
     * @throws \InvalidArgumentException
     * @return bool
     */
    public function validate($transfer, Constraint $constraint)
    {
        if (!$transfer instanceof Transfer) {
            throw new \InvalidArgumentException('This validator only validates transfers');
        }

        $value = $transfer->getBeneficiary();
        if ($transfer instanceof TransferOutBank) {
            $value = $transfer->getResolvedBeneficiary();
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_IBAN])
            && $value instanceof PartyIban
        ) {
            if (
                $transfer instanceof TransferOutBank
                && $this->isTransferToBlockedIban($value->getIban(), $transfer)
            ) {
                return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
            }

            if (
                $transfer instanceof TransferOutBank
                && $value->getIban() === '********************'
            ) {
                return $this->markInvalid($constraint, TransferFailureStatus::CODE_RETURN_IS_TEMPORARY_UNAVAILABLE);
            }

            foreach ($this->ibanRegularExpressions as $regularExpression) {
                if (preg_match($regularExpression, $value->getIban()) === 1) {
                    return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
                }
            }

            try {
                $bank = $this->ibanBankResolver->getBank($value->getIban());
                if ($bank && in_array($bank->getKey(), $this->bankList)) {
                    return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
                }
            } catch (IbanBankResolverException $exception) {
                // Ignore
            }
        } elseif (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY])
            && $value instanceof PartyAccountCountry
        ) {
            $countryCode = $value->getCountry() !== null ? strtoupper($value->getCountry()) : null;
            foreach ($this->accountList as $data) {
                [$country, $account] = $data;
                if (($country !== null && $countryCode === strtoupper($country)) && $value->getAccount() === $account) {
                    return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
                }
            }

            if (in_array($countryCode, $this->countryList, true)) {
                return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
            }
        } elseif (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_ACCOUNT_NUMBER])
            && $value instanceof PartyAccount && $this->isTransferToBlockedEvpAccount($value, $transfer)
        ) {
            return $this->markInvalid($constraint, TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN);
        }

        if ($value instanceof PartyBank) {
            if (ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])) {
                $bic = $value->getBic();
                if ($bic !== null) {
                    foreach ($this->forbiddenBicRules as $rule) {
                        if ($this->transferMatcher->matches($transfer, $rule->getTransferMatchData())) {
                            foreach ($rule->getBicRegexList() as $pattern) {
                                if (preg_match($pattern, $bic) === 1) {
                                    return $this->markInvalid($constraint, $rule->getFailureStatus());
                                }
                            }
                        }
                    }
                }
            }

            if (ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])) {
                $correspondentBankBic = $value->getCorrespondentBankSwift();
                if ($correspondentBankBic !== null) {
                    foreach ($this->forbiddenCorrespondentBicRules as $rule) {
                        if ($this->transferMatcher->matches($transfer, $rule->getTransferMatchData())) {
                            foreach ($rule->getBicRegexList() as $pattern) {
                                if (preg_match($pattern, $correspondentBankBic) === 1) {
                                    return $this->markInvalid($constraint, $rule->getFailureStatus());
                                }
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * Sets invalid message and returns false
     *
     * @param Constraint $constraint
     * @param string $failureCode
     *
     * @return bool
     */
    protected function markInvalid(Constraint $constraint, $failureCode = null)
    {
        $this->context
            ->buildViolation($constraint->message)
            ->setCode($failureCode)
            ->addViolation()
        ;
        return false;
    }

    private function isTransferToBlockedIban($value, TransferOutBank $transfer): bool
    {
        foreach ($this->ibanList as $iban) {
            if ($iban->getIban() === $value) {
                $transferTypeBlocked = $this->isTransferTypeBlocked(
                    $transfer,
                    $iban->isBlockInternal(),
                    $iban->isBlockExternal()
                );

                if (!$transferTypeBlocked) {
                    return false;
                }

                if ($transfer->getRoutingParameter() === null) {
                    return true;
                } elseif (
                    $transfer->getRoutingParameter() === TransferOutBank::ROUTING_PARAMETER_URGENT
                    && $iban->isBlockUrgent()
                ) {
                    return true;
                } elseif (
                    $transfer->getRoutingParameter() === TransferOutBank::ROUTING_PARAMETER_NOT_URGENT
                    && $iban->isBlockNonUrgent()
                ) {
                    return true;
                }
            }
        }

        return false;
    }

    private function isTransferToBlockedEvpAccount(PartyAccount $value, Transfer $transfer)
    {
        if (array_key_exists($value->getNumber(), $this->evpAccountList)) {
            $transferTypeBlocked = $this->isTransferTypeBlocked(
                $transfer,
                $this->evpAccountList[$value->getNumber()]['block_internal'],
                $this->evpAccountList[$value->getNumber()]['block_external']
            );

            if (!$transferTypeBlocked) {
                return false;
            }

            if (!in_array(
                $transfer->getPurpose(),
                $this->evpAccountList[$value->getNumber()]['allowed_purposes']
            )) {
                $this->logger->warning(
                    'Invalid beneficiary, failing transfer',
                    [$value->getNumber(), $transfer->getPurpose()]
                );

                return true;
            }
        }

        return false;
    }

    private function isTransferTypeBlocked(Transfer $transfer, bool $blockInternal, bool $blockExternal): bool
    {
        return
            ($blockInternal && $this->isInternalTransfer($transfer))
            || ($blockExternal && $this->isExternalTransfer($transfer))
        ;
    }

    private function isInternalTransfer(Transfer $transfer): bool
    {
        return $transfer instanceof TransferInternal || $transfer instanceof TransferCurrencyConvert;
    }

    private function isExternalTransfer(Transfer $transfer): bool
    {
        return !$this->isInternalTransfer($transfer);
    }
}
