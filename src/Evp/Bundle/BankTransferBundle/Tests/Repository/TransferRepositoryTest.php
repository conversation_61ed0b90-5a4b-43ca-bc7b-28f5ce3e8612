<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Repository;

use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Mapping\ClassMetadata;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\LocalAccountProvider;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutPaypal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutSingleWindow;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutTax;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPaypal;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartySingleWindow;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyTaxReference;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequest;
use Evp\Bundle\BankTransferBundle\Entity\TransferSignature;
use Evp\Bundle\BankTransferBundle\Entity\TransferStatus;
use Evp\Bundle\BankTransferBundle\Filter\FindTotalsByCurrencyFilter;
use Evp\Bundle\BankTransferBundle\Repository\TransferRepository;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Tests\BaseTestCase;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Component\Money\Money;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransfersFilter;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Traversable;

class TransferRepositoryTest extends BaseTestCase
{
    /**
     * @var TransferRepository
     */
    private $repository;

    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * @var TransferStatusManager
     */
    private $transferStatusManager;

    /**
     * @var FixturesHelper
     */
    private $fixturesHelper;

    public function setUp(): void
    {
        $this->entityManager = $this->getMockEntityManager();

        $this->repository = new TransferRepository(
            $this->entityManager,
            new ClassMetadata(Transfer::class)
        );
        $this->repository->setLogger($this->getContainer()->get('logger'));

        /** @var EventDispatcherInterface $eventDispatcher */
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);

        /** @var LoggerInterface $logger */
        $logger = $this->createMock(LoggerInterface::class);

        $this->transferStatusManager = new TransferStatusManager($eventDispatcher, $logger);
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testFindSignPermissionTotalsByCurrencyWithSeveralCurrencies()
    {
        $signPermission = $this->createSignPermission();
        $this->createTransfer($signPermission, new Money(200, 'USD'), new TransferOutBank());
        $this->createTransfer($signPermission, new Money(100, 'USD'), new TransferOutBank());
        $this->createTransfer($signPermission, new Money(20.12, 'EUR'), new TransferOutBank());

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('yesterday')
        );
        sort($result);
        $expectedResult = [new Money(300, 'USD'), new Money(20.12, 'EUR')];
        sort($expectedResult);
        $this->assertEquals($expectedResult, $result);
    }

    public function testFindSignPermissionTotalsByCurrencyWithSeveralDates()
    {
        $date = new DateTime('-3 DAYS');
        $signPermission = $this->createSignPermission();
        $this->createTransfer(
            $signPermission,
            new Money(100, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) use ($date) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt($date);
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
            }
        );
        $this->createTransfer(
            $signPermission,
            new Money(10, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_SIGNED);
                $status->setCreatedAt(new DateTime('-20 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('+3 DAYS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
            }
        );
        $this->createTransfer(
            $signPermission,
            new Money(1, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-6 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-2 DAYS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
            }
        );

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency($signPermission->getId(), $date);
        $this->assertEquals([new Money(110, 'EUR')], $result);
    }

    public function testFindSignPermissionTotalsByCurrencyWithTaxTransfer()
    {
        $date = new DateTime('-3 DAYS');
        $signPermission = $this->createSignPermission();
        $this->createTransfer(
            $signPermission,
            new Money(100, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) use ($date) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt($date);
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
            }
        );
        $this->createTransfer(
            $signPermission,
            new Money(10, 'EUR'),
            new TransferOutTax(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_SIGNED);
                $status->setCreatedAt(new DateTime('-20 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('+3 DAYS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
                /** @var TransferOutBank $transfer */
                $transfer->setBank('tax_custom');
            }
        );
        $this->createTransfer(
            $signPermission,
            new Money(1, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-6 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-2 DAYS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_DONE);
            }
        );

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency($signPermission->getId(), $date);
        $this->assertEquals([new Money(100, 'EUR')], $result);
    }

    public function testFindSignPermissionTotalsByCurrencyWithSeveralPermissions()
    {
        $signPermission1 = $this->createSignPermission();
        $signPermission2 = $this->createSignPermission();
        $this->createTransfer($signPermission1, new Money(100, 'EUR'), new TransferOutBank());
        $this->createTransfer($signPermission2, new Money(10, 'EUR'), new TransferOutBank());
        $this->createTransfer($signPermission1, new Money(1, 'EUR'), new TransferOutBank());

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission1->getId(),
            new DateTime('-1 DAY')
        );
        $this->assertEquals([new Money(101, 'EUR')], $result);
    }

    public function testFindSignPermissionTotalsByCurrencyWithSeveralTransferStatuses()
    {
        $signPermission = $this->createSignPermission();
        $this->createTransfer(
            $signPermission,
            new Money(100, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_DONE);
                $status->setCreatedAt(new DateTime());
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_PREPARED);
            }
        );
        $this->createTransfer($signPermission, new Money(10, 'EUR'), new TransferOutBank());
        $this->createTransfer($signPermission, new Money(1, 'EUR'), new TransferOutBank());

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('-1 DAY')
        );
        $this->assertEquals([new Money(111, 'EUR')], $result);
    }

    public function testFindSignPermissionTotalsByCurrencyWithInternalTransfer()
    {
        $client1 = $this->createClientNatural();
        $client2 = $this->createClientNatural();

        $account1 = $this->createAccount($client1);
        $account2 = $this->createAccount($client2);

        $signPermission = $this->createSignPermission();

        $transfer = new TransferInternal();
        $transfer->setClient($client1);
        $transfer->setCreditAccount($account1);
        $transfer->setDebitAccount($account2);
        $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer);

        $transfer = new TransferInternal();
        $transfer->setClient($client2);
        $transfer->setCreditAccount($account1);
        $transfer->setDebitAccount($account2);
        $this->createTransfer($signPermission, new Money(10, 'EUR'), $transfer);

        $transfer = new TransferInternal();
        $transfer->setClient($client1);
        $transfer->setCreditAccount($account1);
        $transfer->setDebitAccount($account2);
        $transfer->setPurpose(Transfer::PURPOSE_PLAIS);

        $this->createTransfer($signPermission, new Money(5, 'EUR'), $transfer);

        $transfer = new TransferInternal();
        $transfer->setClient($client1);
        $transfer->setCreditAccount($account2);
        $transfer->setDebitAccount($account1);
        $this->createTransfer($signPermission, new Money(1, 'EUR'), $transfer);

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('-1 DAY')
        );
        $this->assertEquals([new Money(111, 'EUR')], $result);
    }

    /**
     * @dataProvider excludesSignedByAdminInternalTransfersProvider
     * @param int $expectedAmount
     * @param bool $excludeInPerson
     */
    public function testExcludesSignedByAdminInternalTransfers(int $expectedAmount, bool $excludeInPerson)
    {
        $client1 = $this->createClientNatural();
        $client2 = $this->createClientNatural();

        $account1 = $this->createAccount($client1);
        $account2 = $this->createAccount($client2);

        $signPermission = $this->createSignPermission();

        $transfer = new TransferInternal();
        $transfer->setClient($client1);
        $transfer->setCreditAccount($account1);
        $transfer->setDebitAccount($account2);
        $transfer = $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer);
        $signature = new TransferSignature($client1);
        $signature->setAuthenticationType(TransferSignature::AUTH_TYPE_IN_PERSON_ADMIN);
        $transfer->addTransferSignature($signature);

        $this->createTransfer($signPermission, new Money(10, 'EUR'), new TransferOutBank());

        $transfer = new TransferInternal();
        $transfer->setClient($client1);
        $transfer->setCreditAccount($account2);
        $transfer->setDebitAccount($account1);
        $transfer = $this->createTransfer($signPermission, new Money(1, 'EUR'), $transfer);
        $signature = new TransferSignature($client1);
        $signature->setAuthenticationType(TransferSignature::AUTH_TYPE_IN_PERSON);
        $transfer->addTransferSignature($signature);

        $this->entityManager->flush();

        $filter = FindTotalsByCurrencyFilter::create()->notForMacro();
        if (!$excludeInPerson) {
            $filter->includeInPerson();
        }
        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('-1 DAY'),
            $filter
        );
        $this->assertEquals([new Money($expectedAmount, 'EUR')], $result);
    }

    public function excludesSignedByAdminInternalTransfersProvider()
    {
        return [
            [111, false],
            [11, true],
        ];
    }

    /**
     * @dataProvider excludesSignedByAdminOutgoingTransfersProvider
     * @param int $expectedAmount
     * @param bool $excludeInPerson
     */
    public function testExcludesSignedByAdminOutgoingTransfers(int $expectedAmount, bool $excludeInPerson)
    {
        $client1 = $this->createClientNatural();
        $client2 = $this->createClientNatural();

        $account1 = $this->createAccount($client1);
        $account2 = $this->createAccount($client2);

        $signPermission = $this->createSignPermission();

        $transfer = new TransferOutBank();
        $transfer->setCreditAccount($account1);
        $transfer = $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer);
        $signature = new TransferSignature($client1);
        $signature->setAuthenticationType(TransferSignature::AUTH_TYPE_IN_PERSON_ADMIN);
        $transfer->addTransferSignature($signature);

        $this->createTransfer($signPermission, new Money(10, 'EUR'), new TransferOutBank());

        $transfer = new TransferOutBank();
        $transfer->setCreditAccount($account2);
        $transfer = $this->createTransfer($signPermission, new Money(1, 'EUR'), $transfer);
        $signature = new TransferSignature($client1);
        $signature->setAuthenticationType(TransferSignature::AUTH_TYPE_IN_PERSON);
        $transfer->addTransferSignature($signature);

        $this->entityManager->flush();

        $filter = FindTotalsByCurrencyFilter::create()->notForMacro();
        if (!$excludeInPerson) {
            $filter->includeInPerson();
        }
        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('-1 DAY'),
            $filter
        );
        $this->assertEquals([new Money($expectedAmount, 'EUR')], $result);
    }

    public function excludesSignedByAdminOutgoingTransfersProvider()
    {
        return [
            [111, false],
            [11, true],
        ];
    }


    public function testFindSignPermissionTotalsByCurrencyWithInternalTransferAndSameClient()
    {
        $client1 = $this->createClientNatural();
        $client2 = $this->createClientNatural();

        $account11 = $this->createAccount($client1);
        $account12 = $this->createAccount($client1);
        $account2 = $this->createAccount($client2);

        $signPermission = $this->createSignPermission();

        $transfer = new TransferInternal();
        $transfer->setCreditAccount($account11);    // should not be included - same client
        $transfer->setDebitAccount($account12);
        $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer);

        $transfer = new TransferInternal();
        $transfer->setCreditAccount($account11);
        $transfer->setDebitAccount($account2);
        $this->createTransfer($signPermission, new Money(10, 'EUR'), $transfer);

        $transfer = new TransferInternal();
        $transfer->setCreditAccount($account12);
        $transfer->setDebitAccount($account2);
        $this->createTransfer($signPermission, new Money(1, 'EUR'), $transfer);

        $this->createTransfer($signPermission, new Money(1000, 'EUR'), new TransferOutPaypal());

        $this->entityManager->flush();

        $result = $this->repository->findSignPermissionTotalsByCurrency(
            $signPermission->getId(),
            new DateTime('-1 DAY')
        );
        $this->assertEquals([new Money(1011, 'EUR')], $result);
    }

    public function testFindWaitingForTooLong()
    {
        $transfer1 = $this->createTransfer(
            $this->createSignPermission(),
            new Money(1, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-6 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-5 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_SIGNED);
                $status->setCreatedAt(new DateTime('-4 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-3 DAYS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_WAITING_FUNDS);
            }
        );
        $this->createTransfer(
            $this->createSignPermission(),
            new Money(2, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-6 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_SIGNED);
                $status->setCreatedAt(new DateTime('-5 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-2 HOURS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-1 HOUR'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_WAITING_FUNDS);
            }
        );
        $transfer3 = $this->createTransfer(
            $this->createSignPermission(),
            new Money(3, 'EUR'),
            new TransferOutBank(),
            function (Transfer $transfer) {
                $status = new TransferStatus(Transfer::STATUS_READY);
                $status->setCreatedAt(new DateTime('-6 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-3 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_SIGNED);
                $status->setCreatedAt(new DateTime('-2 DAYS'));
                $transfer->addTransferStatus($status);
                $status = new TransferStatus(Transfer::STATUS_WAITING_FUNDS);
                $status->setCreatedAt(new DateTime('-3 HOURS'));
                $transfer->addTransferStatus($status);
                $transfer->setStatus(Transfer::STATUS_WAITING_FUNDS);
            }
        );

        $this->entityManager->flush();

        $result = $this->repository->findWaitingForTooLong(new DateTime('-1 DAY'));
        $this->assertEquals([$transfer1, $transfer3], $result);
    }

    public function testFindAccountTotalsByCurrencyWithSeveralCurrencies()
    {
        $client1 = $this->createClientNatural();

        $account1 = $this->createAccount($client1);

        $transfer1 = new TransferOutBank();
        $transfer1->setCreditAccount($account1);
        $transfer2 = new TransferOutBank();
        $transfer2->setCreditAccount($account1);
        $transfer3 = new TransferOutSingleWindow();
        $transfer3->setCreditAccount($account1);

        $signPermission = $this->createSignPermission();
        $this->createTransfer($signPermission, new Money(200, 'USD'), $transfer1);
        $this->createTransfer($signPermission, new Money(100, 'USD'), $transfer2);
        $this->createTransfer($signPermission, new Money(20.12, 'EUR'), $transfer3);

        $this->entityManager->flush();

        $result = $this->repository->findAccountTotalsByCurrency($account1, new DateTime('yesterday'));
        sort($result);
        $expectedResult = [new Money(300, 'USD'), new Money(20.12, 'EUR')];
        sort($expectedResult);
        $this->assertEquals($expectedResult, $result);
    }

    public function testFindAccountTotalsByCurrencyWithSeveralAccounts()
    {
        $client1 = $this->createClientNatural();
        $client2 = $this->createClientNatural();

        $account1 = $this->createAccount($client1);
        $account2 = $this->createAccount($client2);

        $transfer1 = new TransferOutBank();
        $transfer1->setCreditAccount($account1);
        $transfer2 = new TransferOutBank();
        $transfer2->setCreditAccount($account2);
        $transfer3 = new TransferOutBank();
        $transfer3->setCreditAccount($account1);

        $signPermission = $this->createSignPermission();
        $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer1);
        $this->createTransfer($signPermission, new Money(10, 'EUR'), $transfer2);
        $this->createTransfer($signPermission, new Money(1, 'EUR'), $transfer3);

        $this->entityManager->flush();

        $result = $this->repository->findAccountTotalsByCurrency($account1, new DateTime('-1 DAY'));
        $this->assertEquals([new Money(101, 'EUR')], $result);
    }

    public function test_findAccountTotalsByCurrency_severalSignaturesForSameTransfer()
    {
        $signingClient1 = $this->createClientNatural();
        $signingClient2 = $this->createClientNatural();

        $creditAccount = $this->createAccount($signingClient1);

        $signPermission = $this->createSignPermission();

        for ($i = 0; $i < 2; $i++) {
            $transfer = new TransferOutBank();
            $transfer->setCreditAccount($creditAccount);
            $transfer = $this->createTransfer($signPermission, new Money(100, 'EUR'), $transfer);
            $transfer->addTransferSignature(
                (new TransferSignature($signingClient1))
                    ->setAuthenticationType(TransferSignature::AUTH_TYPE_NORMAL)
            );
            $transfer->addTransferSignature(
                (new TransferSignature($signingClient2))
                    ->setAuthenticationType(TransferSignature::AUTH_TYPE_NORMAL)
            );
            $this->entityManager->flush();
        }

        $totals = $this->repository->findAccountTotalsByCurrency(
            $creditAccount,
            new DateTime('-1 DAY'),
            new FindTotalsByCurrencyFilter()
        );
        $this->assertEquals([Money::create(200, 'EUR')], $totals);
    }

    public function testV2Filter()
    {
        [$creditAccount, $debitAccount] = $this->fixturesHelper->createAccounts(2);

        $transfer = $this->fixturesHelper->createTransferInternal($creditAccount, $debitAccount)
            ->setCreatedAt(new DateTime('2000-01-01 00:00:00'))
            ->setAmountMoney(new Money('1000', 'USD'))
            ->setStatus(Transfer::STATUS_NEW)
            ->setNumber('abc-123')
            ->setPurpose('cashback')
        ;
        $this->entityManager->flush();

        $filter = (new TransfersFilter())
            ->setCreatedDateFrom(new DateTime('2000-01-01 00:00:00'))
            ->setCreatedDateTo(new DateTime('2000-01-01 00:00:01'))
            ->setCreditAccountNumber($creditAccount->getNumber())
            ->setDebitAccountNumber($debitAccount->getNumber())
            ->setCurrency($transfer->getAmountCurrency())
            ->setStatuses([$transfer->getStatus()])
        ;
        self::assertNotCount(0, $this->repository->findByV2Filter($filter));
        self::assertSame(1, $this->repository->findCountByV2Filter($filter));

        $filter2 = clone $filter;
        $filter2->setCreatedDateFrom(new DateTime('2000-01-01 00:00:01'));
        self::assertCount(0, $this->repository->findByV2Filter($filter2));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter2));

        $filter3 = clone $filter;
        $filter3->setCreatedDateTo(new DateTime('2000-01-01 00:00:00'));
        self::assertCount(0, $this->repository->findByV2Filter($filter3));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter3));

        $filter4 = clone $filter;
        $filter4->setCreditAccountNumber($creditAccount->getNumber() . 'A');
        self::assertCount(0, $this->repository->findByV2Filter($filter4));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter4));

        $filter5 = clone $filter;
        $filter5->setDebitAccountNumber($debitAccount->getNumber() . 'A');
        self::assertCount(0, $this->repository->findByV2Filter($filter5));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter5));

        $filter6 = clone $filter;
        $filter6->setCurrency('EUR');
        self::assertCount(0, $this->repository->findByV2Filter($filter6));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter6));

        $filter7 = clone $filter;
        $filter7->setStatuses([Transfer::STATUS_PREPARED]);
        self::assertCount(0, $this->repository->findByV2Filter($filter7));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter7));

        $filter8 = clone $filter;
        $filter8->setDeniedPurposes(['cashback']);
        self::assertCount(0, $this->repository->findByV2Filter($filter8));
        self::assertSame(0, $this->repository->findCountByV2Filter($filter8));

        // The repository does not filter by $number, I am not adding it at the moment.
        // https://jira.paysera.net/browse/SUPPORT-26113
        //$filter8 = clone $filter;
        //$filter8->setNumber('A');
        //self::assertCount(0, $this->repository->findByV2Filter($filter8));

        $emptyFilter = new TransfersFilter();
        self::assertCount(1, $this->repository->findByV2Filter($emptyFilter));
        self::assertSame(1, $this->repository->findCountByV2Filter($emptyFilter));
    }

    public function testV2FilterByAccountNumber()
    {
        [$creditAccount, $debitAccount, $creditAccount2] = $this->fixturesHelper->createAccounts(3);

        $transfer1 = $this->fixturesHelper->createTransferInternal($creditAccount, $debitAccount)
            ->setCreatedAt(new DateTime('2000-01-01 00:00:00'))
            ->setAmountMoney(new Money('1000', 'USD'))
            ->setStatus(Transfer::STATUS_NEW)
            ->setNumber('abc-123')
            ->setPurpose('cashback')
        ;

        $transfer2 = $this->fixturesHelper->createTransferInternal($debitAccount, $creditAccount)
            ->setCreatedAt(new DateTime('2000-01-01 00:00:00'))
            ->setAmountMoney(new Money('1000', 'USD'))
            ->setStatus(Transfer::STATUS_NEW)
            ->setNumber('abc-1234')
            ->setPurpose('cashback')
        ;

        $transfer3 = $this->fixturesHelper->createTransferInternal($debitAccount, $creditAccount)
            ->setCreatedAt(new DateTime('2000-01-01 00:00:00'))
            ->setAmountMoney(new Money('1000', 'USD'))
            ->setStatus(Transfer::STATUS_NEW)
            ->setNumber('abc-12345')
            ->setPurpose('cashback')
        ;

        $transfer4 = $this->fixturesHelper->createTransferInternal($creditAccount2, $debitAccount)
            ->setCreatedAt(new DateTime('2000-01-01 00:00:00'))
            ->setAmountMoney(new Money('1000', 'USD'))
            ->setStatus(Transfer::STATUS_NEW)
            ->setNumber('abc-123456')
            ->setPurpose('cashback')
        ;

        $this->entityManager->flush();

        $filter = (new TransfersFilter())
            ->setCreatedDateFrom(new DateTime('2000-01-01 00:00:00'))
            ->setCreatedDateTo(new DateTime('2000-01-01 00:00:01'))
            ->setAccountId($creditAccount->getId())
            ->setStatuses([$transfer1->getStatus()])
        ;

        $transfers = $this->repository->findByV2Filter($filter);
        self::assertCount(3, $transfers);
        self::assertSame(3, $this->repository->findCountByV2Filter($filter));
        /** @var TransferInternal $transfer */
        foreach ($transfers as $transfer) {
            self::assertContains($transfer, [$transfer1, $transfer2, $transfer3]);
            self::assertContains($creditAccount->getId(), [$transfer->getCreditAccount()->getId(), $transfer->getDebitAccount()->getId()]);
        }

        $filter = clone $filter
            ->setAccountId($debitAccount->getId());

        $transfers = $this->repository->findByV2Filter($filter);
        self::assertCount(4, $transfers);
        self::assertSame(4, $this->repository->findCountByV2Filter($filter));
        /** @var TransferInternal $transfer */
        foreach ($transfers as $transfer) {
            self::assertContains($transfer, [$transfer1, $transfer2, $transfer3, $transfer4]);
            self::assertContains($debitAccount->getId(), [$transfer->getCreditAccount()->getId(), $transfer->getDebitAccount()->getId()]);
        }

        $filter = clone $filter;
        $filter->setAccountId($creditAccount2->getId());

        $transfers = $this->repository->findByV2Filter($filter);
        self::assertCount(1, $transfers);
        self::assertSame(1, $this->repository->findCountByV2Filter($filter));
        self::assertSame($transfer4, $transfers[0]);
        self::assertSame($creditAccount2->getId(), $transfers[0]->getCreditAccount()->getId());
    }

    protected function createClientNatural(): ClientNatural
    {
        $client = new ClientNatural();
        $client->setCreatedAt(new DateTime());
        $client->setUpdatedAt(new DateTime());
        $client->setFirstName(rand());
        $client->setLocale('lt');
        $this->entityManager->persist($client);

        return $client;
    }

    protected function createAccount(Client $client): Account
    {
        $account = new Account();
        $account->setCreatedAt(new DateTime());
        $account->setUpdatedAt(new DateTime());
        $account->setActive(true);
        $account->setClient($client);
        $account->setType(LocalAccountProvider::PROVIDER_KEY);
        $this->entityManager->persist($account);

        return $account;
    }

    protected function createSignPermission(): SignPermission
    {
        $signPermission = new SignPermission();
        $signPermission->setCreatedAt(new DateTime());
        $signPermission->setUpdatedAt(new DateTime());
        $signPermission->setValidFrom(new DateTime('yesterday'));
        $this->entityManager->persist($signPermission);

        return $signPermission;
    }

    protected function createTransfer(
        SignPermission $signPermission,
        Money $amount,
        Transfer $newTransfer,
        callable $callableStatusChanger = null
    ): Transfer {
        $request = new TransferRequest();
        $request->setCreatedAt(new DateTime());
        $this->entityManager->persist($request);

        $transfer = $newTransfer;
        $transfer->setTransferRequest($request);
        $transfer->setCreatedAt(new DateTime());
        $transfer->setUpdatedAt(new DateTime());
        $transfer->setDate(new DateTime());
        $transfer->setOperationDate(new DateTime());
        $transfer->setPriority(Transfer::PRIORITY_NORMAL);
        $transfer->setAmountMoney($amount);
        $transfer->setDetails('Some details of transfer');
        $transfer->setSignPermission($signPermission);
        $transfer->setPayer(new PartyAccount('EVP********9'));

        if ($transfer instanceof TransferOutPaypal) {
            $transfer->setBeneficiary(new PartyPaypal('<EMAIL>'));
        } elseif ($transfer instanceof TransferOutTax) {
            $transfer->setBeneficiary(new PartyTaxReference('vmi'));
        } elseif ($transfer instanceof TransferOutSingleWindow) {
            $transfer->setBeneficiary(
                (new PartySingleWindow())
                    ->setEmail('<EMAIL>')
                    ->setName('SingleWindow')
                    ->setBankAccountNumber('123456')
            );
        } else {
            $transfer->setBeneficiary(new PartyIban('LT********90'));
        }


        if ($callableStatusChanger !== null) {
            $callableStatusChanger($transfer);
        } else {
            $transfer->setStatus(Transfer::STATUS_DONE);
        }

        $this->entityManager->persist($signPermission);
        $this->entityManager->persist($transfer);

        return $transfer;
    }

    /**
     * @dataProvider providerGetCashInTransfersCount
     */
    public function testGetCashInTransfersCount(
        int $expectedCount,
        string $status,
        string $purpose,
        DateTime $createdAt,
        DateTimeImmutable $from,
        DateTimeImmutable $to
    ): void {
        $covenanteeId = ********;
        $client = $this->createClientNatural();
        $client->setCreatedAt(new DateTime('- 5days'));
        $client->setCovenanteeId($covenanteeId);
        $account = $this->createAccount($client);

        $transfer = new TransferIn();
        $transfer->setDebitAccount($account);
        $transfer->setAmountMoney(new Money(100, 'EUR'));
        $transfer->setPayer(new PartyAccount('EVP********9'));
        $transfer->setBeneficiary(new PartyIban('<EMAIL>'));
        $transfer->setCreatedAt($createdAt);
        $transfer->setStatus($status);
        $transfer->setPurpose($purpose);

        $this->entityManager->persist($transfer);
        $this->entityManager->flush();

        $result = $this->repository->getCashInTransfersCount($covenanteeId, $from, $to);

        $this->assertSame($expectedCount, $result);
    }

    public function providerGetCashInTransfersCount(): Traversable
    {
        $format = 'Y-m-d';

        yield 'Positive STATUS_DONE PURPOSE_CASH_IN from limit' => [
            'expectedCount' => 1,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-01'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Positive STATUS_DONE PURPOSE_CASH_IN to limit' => [
            'expectedCount' => 1,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Positive STATUS_DONE PURPOSE_FILL_CASH_IN' => [
            'expectedCount' => 1,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_FILL_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Negative STATUS_CANCELED PURPOSE_FILL_CASH_IN' => [
            'expectedCount' => 0,
            'status' => Transfer::STATUS_CANCELED,
            'purpose' => Transfer::PURPOSE_FILL_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Negative STATUS_FAILED PURPOSE_FILL_CASH_IN' => [
            'expectedCount' => 0,
            'status' => Transfer::STATUS_FAILED,
            'purpose' => Transfer::PURPOSE_FILL_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Negative STATUS_FAILED unexpected purpose' => [
            'expectedCount' => 0,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_CASHBACK,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-01'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
        ];

        yield 'Negative below from' => [
            'expectedCount' => 0,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_FILL_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-03'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-04'),
        ];

        yield 'Negative above/equal to' => [
            'expectedCount' => 0,
            'status' => Transfer::STATUS_DONE,
            'purpose' => Transfer::PURPOSE_FILL_CASH_IN,
            'createdAt' => DateTime::createFromFormat($format, '2024-05-02'),
            'from' => DateTimeImmutable::createFromFormat($format, '2024-05-04'),
            'to' => DateTimeImmutable::createFromFormat($format, '2024-05-04'),
        ];
    }
}
