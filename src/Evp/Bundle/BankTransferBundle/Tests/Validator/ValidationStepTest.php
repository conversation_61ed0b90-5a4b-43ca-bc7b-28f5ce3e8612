<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Validator;

use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\DTO\ValidationContextDTO;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\ValidationStep;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class ValidationStepTest extends PersistableWebTestCase
{
    /**
     * @var ValidationStep
     */
    private $validationStep;

    protected function setUp(): void
    {
        $this->validationStep = $this->getContainer()->get('evp_bank_transfer.transfer_processor.step.validation');
    }

    /**
     * @dataProvider vmiTransferVariationsProvider
     * @param string $referenceToPayer
     * @param string $paymentCode
     * @param boolean $expectedResult
     */
    public function testVmiTransferValidate($referenceToPayer, $paymentCode, $expectedResult)
    {
        $transfer = new TransferOutBank();
        $transfer
            ->setAmountMoney(new Money(1, 'EUR'))
            ->setPayer(new PartyAccount('EVP2810002802189'))
            ->setReferenceNumber($paymentCode)
        ;
        $transfer->setBeneficiary(new PartyIban('********************', 'VMI'));
        $transfer->setReferenceToPayer($referenceToPayer);
        $account = (new Account());
        $client = (new ClientNatural());
        $client->setCountryCode('LT');
        $account->setClient($client);
        $transfer->setCreditAccount($account);
        $transfer->setClient($client);
        $this->assertEquals($expectedResult, $this->validationStep->apply($transfer));
    }

    public function vmiTransferVariationsProvider()
    {
        return [
            'Empty RoikCode provided' => [
                'natural:roik:',
                '1001',
                true,
            ],
            'Bad Roik Code' => [
                'natural:roik:BAD_ROIK',
                '1001',
                false,
            ],
            'Good Roik Code' => [
                'natural:roik:***********',
                '1001',
                true,
            ],
            'Bad PersonCode ' => [
                'natural:cust:bad_code',
                '1411',
                false,
            ],
            'Good PersonCode ' => [
                'natural:cust:*********',
                '1411',
                true,
            ],
            'Empty PersonCode' => [
                'natural:cust:',
                '1411',
                false,
            ],
        ];
    }

    /**
     * @dataProvider invalidBeneficiaryName
     * @param string $beneficiaryName
     * @param ?array $validationGroups
     * @param bool $expectedResult
     */
    public function testInvalidBeneficiaryName(string $beneficiaryName, ?array $validationGroups, bool $expectedResult)
    {
        $client = new ClientNatural();
        $client->setCountryCode('LT');

        $account = new Account();
        $account->setClient($client);

        $transfer = (new TransferOutBank())
            ->setAmountMoney(new Money(1, 'EUR'))
            ->setReferenceToPayer('natural:cust:*********')
            ->setReferenceNumber(1411)
            ->setPayer(new PartyAccount('EVP2810002802189'))
            ->setBeneficiary(new PartyIban('********************', $beneficiaryName))
            ->setCreditAccount($account)
            ->setClient($client)
        ;

        if ($validationGroups !== null) {
            $validationContext = new ValidationContextDTO();
            $validationContext->setValidationGroups($validationGroups);

            $transfer->setValidationContext($validationContext);
        }

        $result = $this->validationStep->apply($transfer);

        $this->assertEquals($expectedResult, $result);
    }

    public function invalidBeneficiaryName(): array
    {
        return [
            'Valid with no validation groups' => [
                '0123456789abcdefghijklmnopqrstuvwxyz',
                null,
                true,
            ],
            'Valid with matched validation groups' => [
                '0123456789abcdefghijklmnopqrstuvwxyz',
                [
                    ValidationGroup::BENEFICIARY_NAME,
                ],
                true,
            ],
            'Not valid with no validation groups' => [
                '',
                null,
                false,
            ],
            'Not valid with matched validation groups' => [
                '',
                [
                    ValidationGroup::BENEFICIARY_NAME,
                ],
                false,
            ],
            'Not valid with not matched validation group' => [
                '',
                [
                    'OtherThenBeneficiaryName',
                ],
                true,
            ],
        ];
    }
}
