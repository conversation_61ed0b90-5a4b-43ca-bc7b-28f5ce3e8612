<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Service;

use Evp\Bundle\BankTransferBundle\DTO\ValidationContextDTO;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutTax;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\TransferValidator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class TransferValidatorTest extends TestCase
{
    /**
     * @var TransferStatusManager&MockObject
     */
    private TransferStatusManager $transferStatusManager;

    /**
     * @var ValidatorInterface&MockObject
     */
    private ValidatorInterface $validator;
    private TransferValidator $transferValidator;

    protected function setUp(): void
    {
        $this->transferStatusManager = self::createMock(TransferStatusManager::class);
        $this->validator = self::createMock(ValidatorInterface::class);

        $this->transferValidator = new TransferValidator($this->transferStatusManager, $this->validator);
    }

    /**
     * @param string[] $violationMessages
     * @param string[] $violationMessageTemplates
     * @param string $transferClass
     * @param string[] $expectedValidationGroups
     * @param string $transferFailureStatusCode
     * @param string|null $transferFailureStatusMessage
     * @param bool $valid
     * @param array|null $validationGroups
     * @dataProvider validateDataProvider
     */
    public function testValidate(
        array $violationMessages,
        array $violationMessageTemplates,
        string $transferClass,
        array $expectedValidationGroups,
        string $transferFailureStatusCode,
        ?string $transferFailureStatusMessage,
        bool $valid,
        ?array $validationGroups = null
    ): void {
        $transfer = new $transferClass();

        if($validationGroups) {
            $validationContext = new ValidationContextDTO();
            $validationContext->setValidationGroups($validationGroups);
            $transfer->setValidationContext($validationContext);
        }

        $violations = $this->getConstraintViolationsMock($violationMessages, $violationMessageTemplates);
        $this->validator
            ->expects(self::once())
            ->method('validate')
            ->with($transfer, null, $expectedValidationGroups)
            ->willReturn($violations)
        ;

        $this->transferStatusManager
            ->expects(count($violations) > 0 ? self::once() : self::never())
            ->method('markAsFailed')
            ->with($transfer, new TransferFailureStatus($transferFailureStatusCode, $transferFailureStatusMessage))
        ;

        self::assertEquals($valid, $this->transferValidator->validate($transfer));
    }

    public function validateDataProvider(): array
    {
        return [
            'TransferOut - Valid' => [
                'violation messages' => [],
                'violation message templates' => [],
                'transfer class' => TransferOut::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'Default'],
                'transfer failure status code' => '',
                'transfer failure status message' => null,
                'expected validation result' => true,
            ],
            'TransferOut - Valid with validation_groups' => [
                'violation messages' => [],
                'violation message templates' => [],
                'transfer class' => TransferOut::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'AnyValidationGroup'],
                'transfer failure status code' => '',
                'transfer failure status message' => null,
                'expected validation result' => true,
                'validationGroups' => ['AnyValidationGroup'],
            ],
            'Transfer out tax - Valid' => [
                'violation messages' => [],
                'violation message templates' => [],
                'transfer class' => TransferOutTax::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'Default'],
                'transfer failure status code' => '',
                'transfer failure status message' => null,
                'expected validation result' => true,
            ],
            'Transfer out tax - Valid with validation_groups' => [
                'violation messages' => [],
                'violation message templates' => [],
                'transfer class' => TransferOutTax::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'AnyValidationGroup'],
                'transfer failure status code' => '',
                'transfer failure status message' => null,
                'expected validation result' => true,
                'validationGroups' => ['AnyValidationGroup'],
            ],
            'Transfer - details too long' => [
                'violation messages' => [TransferFailureStatus::CODE_DETAILS_TOO_LONG],
                'violation message templates' => ['template_1'],
                'transfer class' => TransferOut::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'Default'],
                'transfer failure status code' => TransferFailureStatus::CODE_DETAILS_TOO_LONG,
                'transfer failure status message' => null,
                'expected validation result' => false,
            ],
            'Transfer IN - details invalid characters' => [
                'violation messages' => [
                    TransferFailureStatus::CODE_DETAILS_INVALID_CHARACTERS,
                    TransferFailureStatus::CODE_DETAILS_TOO_LONG
                ],
                'violation message templates' => ['template_1', 'template_2'],
                'transfer class' => TransferIn::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'Default'],
                'transfer failure status code' => TransferFailureStatus::CODE_DETAILS_INVALID_CHARACTERS,
                'transfer failure status message' => null,
                'expected validation result' => false,
            ],
            'Transfer Internal - payment to same account' => [
                'violation messages' => [
                    TransferFailureStatus::CODE_PAYMENT_TO_SAME_ACCOUNT,
                    TransferFailureStatus::CODE_DETAILS_INVALID_CHARACTERS,
                    TransferFailureStatus::CODE_DETAILS_TOO_LONG
                ],
                'violation message templates' => ['template_1', 'template_2', 'template_3'],
                'transfer class' => TransferInternal::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'Default'],
                'transfer failure status code' => TransferFailureStatus::CODE_PAYMENT_TO_SAME_ACCOUNT,
                'transfer failure status message' => null,
                'expected validation result' => false,
            ],
            'Transfer Out - default case' => [
                'violation messages' => ['message_1', 'message_2'],
                'violation message templates' => ['template_1', 'template_2'],
                'transfer class' => TransferOut::class,
                'expectedValidationGroups' => ['managementApi', 'insert', 'money', 'transferOut', 'Default'],
                'transfer failure status code' => TransferFailureStatus::CODE_VALIDATION,
                'transfer failure status message' => '["message_1","message_2"]',
                'expected validation result' => false,
            ],
        ];
    }

    /**
     * @param string[] $violationMessages
     * @param string[] $violationMessageTemplates
     *
     * @return ConstraintViolationList
     */
    private function getConstraintViolationsMock(
        array $violationMessages,
        array $violationMessageTemplates
    ): ConstraintViolationList {
        $constraintViolations = [];
        for ($i = 0; $i < count($violationMessages); $i++) {
            $constraintViolation = self::createMock(ConstraintViolationInterface::class);
            $constraintViolation
                ->method('getMessage')
                ->willReturn($violationMessages[$i])
            ;
            $constraintViolation
                ->method('getMessageTemplate')
                ->willReturn($violationMessageTemplates[$i])
            ;
            $constraintViolations[] = $constraintViolation;
        }

        return new ConstraintViolationList($constraintViolations);
    }
}
