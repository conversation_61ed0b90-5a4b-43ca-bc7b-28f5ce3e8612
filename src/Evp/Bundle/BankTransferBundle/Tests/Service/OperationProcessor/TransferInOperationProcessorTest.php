<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\PartnerAccountingProcessor;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferInOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Component\Money\Money;

/**
 * TransferInOperationProcessorTest
 *
 * <AUTHOR> <<EMAIL>>
 */
class TransferInOperationProcessorTest extends OperationProcessorTestCase
{
    private TransferInOperationProcessor $processor;

    public function setUp(): void
    {
        parent::setUp();
        $this->processor = new TransferInOperationProcessor(
            $this->internalOperationHandler,
            $this->externalOperationHandler,
            $this->getContainer()->get('evp_accounting.service.accounting_exception_handler'),
            $this->createMock(PartnerAccountingProcessor::class)
        );
    }

    public function testProcessWithTransferIn()
    {
        $client = new ClientLegal();
        $client->setCovenanteeId(1);

        $account = new Account();
        $account->setClient($client);

        $transfer = new TransferIn();
        $transfer->setDebitAccount($account);
        $transfer->setAmountMoney(new Money(10, 'EUR'));
        $transfer->setDetails('Details');

        $operation = new TransferInOperation($transfer);
        $operation->setCreatedAt(new \DateTime());

        $this->internalOperationHandler->expects($this->never())->method($this->anything());
        $this->externalOperationHandler->expects($this->once())->method('handleIn')
            ->with($operation, $account, new Money(10, 'EUR'), 'Details');
        $this->processor->process($operation);
    }

    public function testProcessWithTransferInternal()
    {
        $operation = new TransferInOperation($this->createInternalTransfer(1, 2, 10, 'EVP0000001000002'));

        $this->externalOperationHandler->expects($this->never())->method($this->anything());
        $this->internalOperationHandler->expects($this->once())->method('handleIn')
            ->with(
                $operation,
                $this->anything(),
                $this->anything(),
                new Money(10, 'EUR'),
                'Details'
            );

        $this->processor->process($operation);
    }
}
