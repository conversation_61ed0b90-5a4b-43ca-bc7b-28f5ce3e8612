<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Service\OperationProcessor;

use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferInCommissionOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Component\Money\Money;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;

class TransferInCommissionOperationProcessorTest extends OperationProcessorTestCase
{
    protected TransferInCommissionOperationProcessor $processor;

    /**
     * @var InternalPartnerOperationHandler|MockObject
     */
    protected $internalPartnerOperationHandler;

    public function setUp(): void
    {
        parent::setUp();
        $this->internalPartnerOperationHandler = $this->getMockBuilder(InternalPartnerOperationHandler::class)
            ->disableOriginalConstructor()
            ->getMock();
        $this->processor = new TransferInCommissionOperationProcessor(
            $this->getContainer()->get('evp_accounting.service.accounting_exception_handler'),
            $this->internalOperationHandler,
            $this->internalPartnerOperationHandler
        );
    }

    public function testProcess()
    {
        $clientPayer = new ClientLegal();
        $clientPayer->setCovenanteeId(3);

        $clientBeneficiary = new ClientNatural();
        $clientBeneficiary->setCovenanteeId(1);

        $account = new Account();
        $account->setClient($clientBeneficiary);

        $transfer = new TransferInternal();
        $transfer->setClient($clientPayer);
        $transfer->setDebitAccount($account);
        $transfer->setDebitCommissionMoney(new Money(10, 'EUR'));

        $operation = new TransferInCommissionOperation($transfer, $transfer->getDebitAccount());

        $this->externalOperationHandler->expects($this->never())->method($this->anything());
        $this->internalOperationHandler->expects($this->once())
            ->method('handleInCommission')
            ->with($operation, $account, new Money(10, 'EUR'), null);

        $this->processor->process($operation);
    }

    public function testProcessWithUnsupportedOperation()
    {
        $this->expectException(InvalidArgumentException::class);
        $operation = $this->createMock(Operation::class);
        $this->processor->process($operation);
    }

    public function testProcessWithUnsupportedTransfer()
    {
        $this->expectException(InvalidArgumentException::class);
        $transfer = $this->createMock(Transfer::class);
        $account = $this->createMock(Account::class);
        $operation = new TransferInCommissionOperation($transfer, $account);
        $this->processor->process($operation);
    }
}
