<?php

namespace Evp\Bundle\BankTransferBundle\Tests\Service\CommonTransfer;

use DateTime;
use DateTimeImmutable;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankCommissionBundle\Entity\CommissionRule;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvertSell;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAdditionalInformation;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyTaxReference;
use Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PersonalNumberClientIdentifier;
use Evp\Bundle\BankTransferBundle\Entity\TransferStatus;
use Evp\Bundle\BankTransferBundle\Service\CommonTransfer\CommonTransferMapper;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Service\ClientIdentifier\ClientIdentifierManager;
use Evp\Component\Money\Money;
use Paysera\Bundle\BgBudgetPaymentBundle\Entity\BgBudgetPayment;
use Paysera\Bundle\BgBudgetPaymentBundle\Entity\BgBudgetPaymentTransfer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\CommissionRule as CommonCommissionRule;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\DetailsOptions;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\FailureStatus;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\BankAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\PayseraAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\AdditionalInformation;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Address;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Beneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\BgBudgetPrimaryPayer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\ClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\PassportNumberClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\PersonalNumberClientIdentifier as CommonPersonalNumberClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\TaxPayerCodeClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\CorrespondentBank;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\FinalBeneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Identifiers;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Initiator;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Payer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Purpose;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Transfer as CommonTransfer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransferAdditionalInformation;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CurrencyConversion\CurrencyConversionResult;
use Paysera\Bundle\TransferSurveillanceBundle\Entity\TransferInspection;
use Paysera\Bundle\TransferSurveillanceBundle\Tests\Mocks\Service\MockTransferInspectionProvider;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class CommonTransferMapperTest extends PersistableWebTestCase
{
    /**
     * @var CommonTransferMapper
     */
    private $commonTransferMapper;

    /**
     * @var ClientIdentifierManager
     */
    private $clientIdentifierManager;

    private MockTransferInspectionProvider $transferInspectionProvider;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->clientIdentifierManager = $this->createMock(ClientIdentifierManager::class);
        $this->transferInspectionProvider = new MockTransferInspectionProvider();

        $this->commonTransferMapper = new CommonTransferMapper(
            $this->getContainer()->getParameter('evp_bank_transfer.common_transfer_purpose_map'),
            $this->getContainer()->get('paysera_transfer_callback.repository.transfer_callback'),
            $this->getContainer()->get('evp_bank_transfer.service.evp_account_resolver'),
            $this->getContainer()->get('evp_bank_transfer.mapper.beneficiary'),
            $this->getContainer()->get('evp_protected_transfer.transfer_password_manager'),
            $this->getContainer()->get('evp_bank_transfer.transfer_beneficiary_email_manager'),
            $this->getContainer()->get('evp_bank.account_info_resolver'),
            $this->getContainer()->get('evp_client.user_address_provider'),
            $this->getContainer()->get('evp_bundle_bank_transfer.service.iban_aliases_account_number_manager'),
            $this->clientIdentifierManager,
            $this->getContainer()->get('evp_bank_transfer.service.transfer_by_legal_special_country_client_detector'),
            $this->getContainer()->get('evp_sepa.sepa_transfer_detector'),
            $this->getContainer()->get('paysera_sepa_instant.sepa_instant_transfer_detector'),
            $this->getContainer()->get('paysera_mobile_payments.repository.mobile_payment'),
            $this->transferInspectionProvider,
            $this->getContainer()->get('logger'),
            $this->getContainer()->get('debug.stopwatch'),
        );
    }

    /**
     * @param Transfer $transfer
     * @param CommonTransfer $expectedTransfer
     * @param ClientIdentifier|null $clientIdentifier
     * @param TransferInspection|null $transferInspection
     * @dataProvider transferDataProvider
     */
    public function testToCommonTransfer(
        Transfer $transfer,
        CommonTransfer $expectedTransfer,
        ?ClientIdentifier $clientIdentifier = null,
        ?TransferInspection $transferInspection = null
    ) {
        $this->assertInstanceOf(DateTime::class, $transfer->getCreatedAt());
        $expectedTransfer->setCreatedAt($transfer->getCreatedAt());
        $expectedTransfer->setPerformAt($transfer->getOperationDate());

        if ($clientIdentifier !== null) {
            $this->clientIdentifierManager
                ->expects($this->once())
                ->method('getIdentifier')
                ->willReturn($clientIdentifier)
            ;
        }
        if ($transferInspection !== null) {
            $this->transferInspectionProvider->setTransferInspection($transfer, $transferInspection);
        }

        $this->assertEquals($expectedTransfer, $this->commonTransferMapper->toCommonTransfer($transfer));
    }

    public function testToCommonTransferBeneficiaryPartyIban()
    {
        $additionalInformationManager = $this->getContainer()->get('evp_bank_transfer.additional_information_manager');
        $clientIdentifierManager = $this->getContainer()->get('evp_bank_transfer.client_identifier_manager');
        $transferOutBank = new TransferOutBank();
        $commonTransferOutBank = (new CommonTransfer())
            ->setBeneficiary(
                (new Beneficiary())
                    ->setName('test')
                    ->setIdentifiers((new Identifiers())->setGeneral('123'))
                    ->setType(BankAccount::TYPE_BANK)
                    ->setAccount(
                        (new BankAccount())
                            ->setIban('IBAN')
                            ->setAccountNumber('')
                            ->setCountryCode('')
                            ->setBic('BIC')
                            ->setBankCode('BIC')
                            ->setBankAddress((new Address())->setCountryCode('')->setAddressLine('address'))
                            ->setBankTitle('bank_name')
                            ->setCorrespondentBank(
                                (new CorrespondentBank())
                                    ->setBankTitle('correspondent_bank_name')
                                    ->setAccountNumber('correspondent_bank_account')
                                    ->setBankCode('CORRESPONDENT_BANK_SWIFT')
                            )
                    )
                    ->setClientIdentifier((new CommonPersonalNumberClientIdentifier())->setValue('321'))
                    ->setAdditionalInformation(
                        (new AdditionalInformation())
                            ->setType(PartyAdditionalInformation::TYPE_NATURAL)
                            ->setCity('city')
                            ->setCountry('LT')
                            ->setBankBranchCode('bank_branch_code')
                            ->setPostalCode('postal_code')
                            ->setState('state')
                    )
            )
        ;
        $this->mapDefaults($transferOutBank, $commonTransferOutBank);
        $commonTransferOutBank->setPurpose((new Purpose())->setDetailsOptions(new DetailsOptions()));

        $beneficiary = (new PartyIban())
            ->setName('test')
            ->setCode('123')
            ->setIdentifier('string')
            ->setIban('iban')
            ->setBic('bic')
            ->setBankCode('bank_code')
            ->setBankName('bank_name')
            ->setBankAddress('address')
            ->setCorrespondentBankAccount('correspondent_bank_account')
            ->setCorrespondentBankName('correspondent_bank_name')
            ->setCorrespondentBankSwift('correspondent_bank_swift')
        ;
        $additionalInformation = (new PartyAdditionalInformation())
            ->setType(PartyAdditionalInformation::TYPE_NATURAL)
            ->setCity('city')
            ->setCountry('LT')
            ->setBankBranchCode('bank_branch_code')
            ->setParty($beneficiary)
            ->setPostalCode('postal_code')
            ->setState('state')
        ;
        $clientIdentifier = (new PersonalNumberClientIdentifier())
            ->setValue('321')
            ->setParty($beneficiary)
        ;
        $additionalInformationManager->persistAdditionalInformation($additionalInformation);
        $clientIdentifierManager->persistClientIdentifier($clientIdentifier);

        $this->assertInstanceOf(DateTime::class, $transferOutBank->getCreatedAt());
        $commonTransferOutBank->setCreatedAt($transferOutBank->getCreatedAt());
        $commonTransferOutBank->setPerformAt($transferOutBank->getOperationDate());

        $this->assertEquals($commonTransferOutBank, $this->commonTransferMapper->toCommonTransfer($transferOutBank));
    }

    private function mapDefaults(Transfer $transfer, CommonTransfer $commonTransfer)
    {
        $client = (new ClientNatural())->setCovenanteeId(1);
        $transfer
            ->setStatus(Transfer::STATUS_READY)
            ->setAmountMoney(new Money(10, 'EUR'))
            ->setBeneficiary(new PartyIban())
            ->setClient($client)
        ;

        $commonTransfer
            ->setId('')
            ->setStatus(Transfer::STATUS_RESERVED)
            ->setOriginalStatus(Transfer::STATUS_READY)
            ->setAmount(new Money(10, 'EUR'))
            ->setBeneficiary(
                (new Beneficiary())
                    ->setType(BankAccount::TYPE_BANK)
                    ->setAccount(
                        (new BankAccount())
                            ->setIban('')
                            ->setAccountNumber('')
                            ->setCountryCode('')
                            ->setBankAddress(new Address())
                    )
            )
            ->setPayer((new Payer()))
            ->setInitiator((new Initiator())->setUserId(1))
            ->setPurpose((new Purpose()))
        ;
        if ($transfer instanceof TransferOutBank) {
            $transfer->setCreditAccount((new Account())->setClient($client));
            $commonTransfer
                ->setAllowedToEdit(false)
                ->setAllowedToSign(false)
                ->setIsSepaByLegalSpecialCountryClient(false)
                ->setIsSepa(false);
            $commonTransfer->getPayer()->setUserId($client->getCovenanteeId());
        }
    }

    public function transferDataProvider()
    {
        $newDateTime = new DateTime();
        $transferOutBank = new TransferOutBank();
        $commonTransferOutBank = (new CommonTransfer());
        $this->mapDefaults($transferOutBank, $commonTransferOutBank);
        $commonTransferOutBank->setPurpose((new Purpose())->setDetailsOptions(new DetailsOptions()));

        $transferInternal = new TransferInternal();
        $commonTransferInternal = (new CommonTransfer());
        $this->mapDefaults($transferInternal, $commonTransferInternal);

        $bgBudgetTransferOutBank = new BgBudgetPaymentTransfer();
        $commonBgBudgetTransfer = (new CommonTransfer());
        $this->mapDefaults($bgBudgetTransferOutBank, $commonBgBudgetTransfer);
        $commonBgBudgetTransfer->setPurpose((new Purpose())->setDetailsOptions(new DetailsOptions()));

        return [
            'Default TransferOutBank' => [
                $transferOutBank,
                $commonTransferOutBank,
            ],
            'Default TransferInternal' => [
                $transferInternal,
                $commonTransferInternal->setBeneficiary(
                    (new Beneficiary())
                        ->setAccount(new PayseraAccount())
                        ->setType(PayseraAccount::TYPE_PAYSERA)
                ),
            ],
            'Basic TransferOutBank checks' => [
                $transferOutBank
                    ->setCancelable(true)
                    ->setHasCancelPermission(true)
                    ->setOperationDate($newDateTime->add(new \DateInterval('P1D')))
                    ->setAutoCurrencyConvert(true)
                    ->setCreditCommissionMoney(new Money('0.99', 'EUR'))
                    ->setChargeType(TransferOutBank::CHARGE_TYPE_OUR)
                    ->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_URGENT)
                    ->addTransferStatus(
                        (new TransferStatus())
                            ->setStatus(Transfer::STATUS_DONE)
                            ->setCreatedAt($newDateTime->add(new \DateInterval('P2D')))
                    )
                ,
                $commonTransferOutBank
                    ->setAllowedToCancel(true)
                    ->setCancelable(true)
                    ->setPerformAt($newDateTime->add(new \DateInterval('P1D')))
                    ->setAutoCurrencyConvert(true)
                    ->setOutCommission(new Money('0.99', 'EUR'))
                    ->setChargeType(CommonTransfer::CHARGE_TYPE_OUR)
                    ->setUrgency(CommonTransfer::URGENCY_URGENT)
                    ->setPerformedAt($newDateTime->add(new \DateInterval('P2D')))
                ,
            ],
            'TransferOutBank Final beneficiary' => [
                (clone $transferOutBank)
                    ->setFinalBeneficiary((new PartyTaxReference())->setName('name'))
                ,
                (clone $commonTransferOutBank)
                    ->setFinalBeneficiary(
                        (new FinalBeneficiary())
                            ->setName('name')
                            ->setPersonType(FinalBeneficiary::TYPE_LEGAL)
                    )
                ,
            ],
            'TransferOutBank Payer with Reference' => [
                (clone $transferOutBank)
                    ->setCreditAccount(
                        (new Account())
                            ->setNumber('account_number')
                            ->setClient(
                                (new ClientNatural())
                                    ->setCovenanteeId(2)
                                    ->setFirstName('first_name')
                                    ->setLastName('last_name')
                            )
                    )
                    ->setPrimaryPayer((new PartyTaxReference())->setIdentifier('reference'))
                    ->setReferenceToPayer(sprintf(
                            '%s:%s:%s',
                            Transfer::REFERENCE_TYPE_NATURAL,
                            Transfer::REFERENCE_IDENTIFIER_NIDN,
                            'value which is restricted by type')
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setReferenceToPayer('value which is restricted by type')
                    ->setPayer(
                        (new Payer())
                            ->setName('first_name last_name')
                            ->setAccountNumber('account_number')
                            ->setReference('reference')
                            ->setClientIdentifier((new CommonPersonalNumberClientIdentifier()))
                            ->setUserId(2)
                    )
                ,
            ],
            'TransferOutBank Payer Natural' => [
                (clone $transferOutBank)
                    ->setCreditAccount(
                        (new Account())
                            ->setNumber('account_number')
                            ->setClient(
                                (new ClientNatural())
                                    ->setCovenanteeId(2)
                                    ->setFirstName('first_name')
                                    ->setLastName('last_name')
                                    ->setResidenceCountry('lt')
                            )
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setPayer(
                        (new Payer())
                            ->setName('first_name last_name')
                            ->setAccountNumber('account_number')
                            ->setUserId(2)
                    )
                ,
            ],
            'TransferOutBank Georgia Payer Natural' => [
                (clone $transferOutBank)
                    ->setCreditAccount(
                        (new Account())
                            ->setNumber('account_number')
                            ->setClient(
                                (new ClientNatural())
                                    ->setCovenanteeId(2)
                                    ->setFirstName('first_name')
                                    ->setLastName('last_name')
                                    ->setResidenceCountry('ge')
                            )
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setPayer(
                        (new Payer())
                            ->setName('first_name last_name')
                            ->setAccountNumber('account_number')
                            ->setClientIdentifier(
                                (new PassportNumberClientIdentifier())
                                    ->setValue('**********')
                            )
                            ->setUserId(2)
                    )
                ,
                (new PassportNumberClientIdentifier())
                    ->setValue('**********')
            ],
            'TransferOutBank Georgia Payer Legal' => [
                (clone $transferOutBank)
                    ->setCreditAccount(
                        (new Account())
                            ->setNumber('account_number')
                            ->setClient(
                                (new ClientLegal())
                                    ->setCovenanteeId(2)
                                    ->setName('first_name last_name')
                                    ->setCode('12345')
                            )
                    )
                    ->setPrimaryPayer(
                        (new PartyTaxReference())
                            ->setIdentifier('reference')
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setPayer(
                        (new Payer())
                            ->setName('first_name last_name')
                            ->setAccountNumber('account_number')
                            ->setReference('reference')
                            ->setClientIdentifier(
                                (new TaxPayerCodeClientIdentifier())
                                    ->setValue('***********')
                            )
                            ->setUserId(2)
                    )
                ,
                (new TaxPayerCodeClientIdentifier())
                    ->setValue('***********')
            ],
            'TransferOutBank Georgia Payer Legal Tax type Natural' => [
                (clone $transferOutBank)
                    ->setCreditAccount(
                        (new Account())
                            ->setNumber('account_number')
                            ->setClient(
                                (new ClientLegal())
                                    ->setCovenanteeId(2)
                                    ->setName('first_name last_name')
                                    ->setCode('12345')
                            )
                    )
                    ->setPrimaryPayer(
                        (new PartyTaxReference())
                            ->setIdentifier('reference')
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setPayer(
                        (new Payer())
                            ->setName('first_name last_name')
                            ->setAccountNumber('account_number')
                            ->setReference('reference')
                            ->setClientIdentifier(
                                (new TaxPayerCodeClientIdentifier())
                                    ->setValue('***********')
                            )
                            ->setUserId(2)
                    )
                ,
                (new TaxPayerCodeClientIdentifier())
                    ->setValue('***********')
            ],
            'TransferOutBank Initiator' => [
                (clone $transferOutBank)
                    ->setClient((new ClientNatural())->setId(9))
                ,
                (clone $commonTransferOutBank)
                    ->setInitiator((new Initiator())->setClientId(9))
                ,
            ],
            'TransferOutBank Failure status' => [
                (clone $transferOutBank)
                    ->setFailureStatus(new TransferFailureStatus(TransferFailureStatus::CODE_OTHER, 'message'))
                ,
                (clone $commonTransferOutBank)
                    ->setFailureStatus(new FailureStatus(TransferFailureStatus::CODE_OTHER, 'message'))
                ,
            ],
            'TransferOutBank Purpose' => [
                (clone $transferOutBank)
                    ->setPurpose(Transfer::PURPOSE_CASH_IN)
                    ->setVoCode('vo_code')
                    ->setOcrCode('ocr_code')
                    ->setDetails('details')
                    ->setReferenceNumber('reference_number')
                    ->setPreserveDetails(false)
                ,
                (clone $commonTransferOutBank)
                    ->setPurpose(
                        (new Purpose())
                            ->setDetails('details')
                            ->setReference('reference_number')
                            ->setDetailsOptions((new DetailsOptions())->setPreserved(false))
                            ->setOcrCode('ocr_code')
                            ->setVoCode('vo_code')
                            ->setCode(Transfer::PURPOSE_CASH_IN)
                    )
                ,
            ],
            'TransferOutBank Additional information' => [
                (clone $transferOutBank)
                    ->setMaxExecutionTime($newDateTime->add(new \DateInterval('P3D')))
                    ->setOriginalCreditCommission(new Money('0.1', 'EUR'))
                    ->setCorrespondentBankFeesMayApply(true)
                    ->setCreditCommissionRule(
                        (new CommissionRule())
                            ->setPercentage('0.75')
                            ->setMin(new Money('0.25', 'EUR'))
                            ->setMax(new Money('0.75', 'EUR'))
                            ->setFix(new Money('0.50', 'EUR'))
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setMaxExecutionTime($newDateTime->add(new \DateInterval('P3D')))
                    ->setAdditionalInformation(
                        (new TransferAdditionalInformation())
                            ->setEstimatedProcessingDate($newDateTime->add(new \DateInterval('P2D')))
                            ->setOriginalOutCommission(new Money('0.1', 'EUR'))
                            ->setCorrespondentBankFeesMayApply(true)
                            ->setOutCommissionRule(
                                (new CommonCommissionRule())
                                    ->setPercent('0.75')
                                    ->setMin(new Money('0.25', 'EUR'))
                                    ->setMax(new Money('0.75', 'EUR'))
                                    ->setFix(new Money('0.50', 'EUR'))
                            )
                    )
                ,
            ],
            'TransferOut RelatedCurrencyConversion' => [
                (clone $transferOutBank)
                    ->setRelatedCurrencyConvertTransfer(
                        (new TransferCurrencyConvertSell())
                            ->setHash('Ta123')
                            ->setCreditAccount(
                                (new Account())
                                    ->setNumber('EVP123')
                            )
                            ->setAmountMoney(
                                (new Money())
                                    ->setAmount(10)
                                    ->setCurrency('EUR')
                            )
                            ->setToAmountMoney(
                                (new Money())
                                    ->setAmount(11)
                                    ->setCurrency('USD')
                            )
                            ->setClient(
                                (new ClientNatural())
                                    ->setCovenanteeId(15)
                            )
                            ->setCreatedAt(
                                new DateTime('2019-09-07 12:12:05')
                            )
                            ->setStatus(Transfer::STATUS_RESERVED)
                    )
                ,
                (clone $commonTransferOutBank)
                    ->setRelatedConvertCurrency(
                        (new CurrencyConversionResult())
                            ->setId('Ta123')
                            ->setAccountNumber('EVP123')
                            ->setUserId(15)
                            ->setStatus(CommonTransfer::STATUS_RESERVED)
                            ->setFromAmount(
                                (new Money())
                                    ->setAmount(10)
                                    ->setCurrency('EUR')
                            )
                            ->setToAmount(
                                (new Money())
                                    ->setAmount(11)
                                    ->setCurrency('USD')
                            )
                            ->setDate(new DateTimeImmutable('2019-09-07 12:12:05'))
                    )
                ,
            ],
            'TransferOut bg budget payment' => [
                $bgBudgetTransferOutBank
                    ->setPrimaryPayer(
                        (new PartyAccount())
                        ->setName('test test')
                        ->setCode('**********')
                    )->setBgBudgetPayment(
                        (new BgBudgetPayment())
                            ->setResidenceType('legal')
                            ->setPaymentType('995588')
                    )
                ,
                $commonBgBudgetTransfer
                    ->setBgBudgetPaymentType('995588')
                    ->setBgBudgetPrimaryPayer(
                        (new BgBudgetPrimaryPayer())
                            ->setName('test test')
                            ->setAccountNumber('**********')
                            ->setResidenceType('legal')
                    )
            ],
            'Transfer inspection accepted' => [
                (clone $transferOutBank),
                (clone $commonTransferOutBank)
                    ->setInspectionStatus(TransferInspection::REVIEW_ACTION_ACCEPTED)
                ,
                null,
                (new TransferInspection())
                    ->setTransfer($transferOutBank)
                    ->setStatus(TransferInspection::STATUS_PROCESSED)
                    ->setReviewAction(TransferInspection::REVIEW_ACTION_ACCEPTED)
            ],
            'Transfer inspection no action' => [
                (clone $transferOutBank),
                (clone $commonTransferOutBank)
                    ->setInspectionStatus(TransferInspection::STATUS_PROCESSED)
                ,
                null,
                (new TransferInspection())
                    ->setTransfer($transferOutBank)
                    ->setStatus(TransferInspection::STATUS_PROCESSED)
                    ->setAction(TransferInspection::ACTION_NONE)
            ],
            'Transfer inspection need info' => [
                (clone $transferOutBank),
                (clone $commonTransferOutBank)
                    ->setInspectionStatus(TransferInspection::ACTION_NEEDS_INFO)
                ,
                null,
                (new TransferInspection())
                    ->setTransfer($transferOutBank)
                    ->setStatus(TransferInspection::STATUS_PROCESSED)
                    ->setAction(TransferInspection::ACTION_NEEDS_INFO)
            ],
        ];
    }
}
