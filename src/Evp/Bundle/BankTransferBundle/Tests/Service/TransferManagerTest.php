<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManagerInterface;
use Evp\Bundle\BankTransferBundle\Entity\Event\TransferEvent;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAdditionalInformation;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\CompanyCodeClientIdentifier;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequest;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequestConvertCurrency;
use Evp\Bundle\BankTransferBundle\Event\TransferRequestEvent;
use Evp\Bundle\BankTransferBundle\Repository\TransferInRepository;
use Evp\Bundle\BankTransferBundle\Repository\TransferOutRepository;
use Evp\Bundle\BankTransferBundle\Service\FundsSourceManager;
use Evp\Bundle\BankTransferBundle\Service\SlaveConnectionTransferCountriesProvider;
use Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryEmailManager;
use Evp\Bundle\BankTransferBundle\Service\TransferManager;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferInternalProcessor;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferOutProcessor;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessorInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\TransferEvents;
use Evp\Bundle\BankTransferBundle\TransferRequestEvents;
use Evp\Bundle\ProtectedTransferBundle\Service\TransferPasswordManager;
use Paysera\Bundle\RandomHashBundle\Service\HashGeneratorInterface;
use PHPUnit_Framework_MockObject_MockObject as MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class TransferManagerTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @var EntityManagerInterface|MockObject
     */
    private $entityManager;

    /**
     * @var EventDispatcherInterface|MockObject
     */
    private $dispatcher;

    /**
     * @var TransferManager
     */
    private $transferManager;

    /**
     * @var TransferOutProcessor|MockObject
     */
    private $transferOutProcessor;

    /**
     * @var TransferInternalProcessor|MockObject
     */
    private $transferInternalProcessor;

    /**
     * @var HashGeneratorInterface|MockObject
     */
    private $hashGenerator;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->dispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->transferInternalProcessor = $this->createMock(TransferProcessorInterface::class);
        $this->transferOutProcessor = $this->createMock(TransferProcessorInterface::class);
        $this->hashGenerator = $this->createMock(HashGeneratorInterface::class);

        /** @var LoggerInterface|MockObject $logger */
        $logger = $this->createMock(LoggerInterface::class);

        /** @var TransferProcessorInterface|MockObject $transferInProcessor */
        $transferInProcessor = $this->createMock(TransferProcessorInterface::class);

        /** @var TransferProcessorInterface|MockObject $transferCurrencyConvertProcessor */
        $transferCurrencyConvertProcessor = $this->createMock(TransferProcessorInterface::class);

        /** @var TransferOutRepository|MockObject $transferOutRepository */
        $transferOutRepository = $this->createMock(TransferOutRepository::class);

        /** @var TransferInRepository|MockObject $transferInRepository */
        $transferInRepository = $this->createMock(TransferInRepository::class);

        /** @var TransferPasswordManager|MockObject $transferPasswordManager */
        $transferPasswordManager = $this->createMock(TransferPasswordManager::class);

        /** @var FundsSourceManager|MockObject $fundsSourceManager */
        $fundsSourceManager = $this->createMock(FundsSourceManager::class);

       /** @var PermissionManagerInterface|MockObject $permissionManager */
        $permissionManager = $this->createMock(PermissionManagerInterface::class);

        /** @var TransferBeneficiaryEmailManager|MockObject $transferBeneficiaryEmailManager */
        $transferBeneficiaryEmailManager = $this->createMock(TransferBeneficiaryEmailManager::class);

        $transferStatusManager = new TransferStatusManager($this->dispatcher, $logger);

        /** @var SlaveConnectionTransferCountriesProvider|MockObject $transferCountriesProvider */
        $transferCountriesProvider = $this->createMock(SlaveConnectionTransferCountriesProvider::class);

        $this->transferManager = new TransferManager(
            $this->entityManager,
            $transferStatusManager,
            $transferInProcessor,
            $this->transferInternalProcessor,
            $this->transferOutProcessor,
            $transferCurrencyConvertProcessor,
            $transferOutRepository,
            $transferInRepository,
            $transferPasswordManager,
            $fundsSourceManager,
            $transferBeneficiaryEmailManager,
            $this->hashGenerator,
            $this->dispatcher,
            $permissionManager,
            $transferCountriesProvider,
            $logger
        );
    }

    public function testRegisterTransferRequest()
    {
        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->willReturnOnConsecutiveCalls([
                PartyAdditionalInformation::class,
                CompanyCodeClientIdentifier::class,
                TransferRequest::class,
            ])
        ;

        $transferRequest = new TransferRequest();
        $transferRequest->addTransfer((new TransferOutBank())->setBeneficiary(new PartyIban()));
        $transferRequest->addTransfer((new TransferOutBank())->setBeneficiary(new PartyIban()));
        $transferRequest->addTransfer((new TransferOutBank())->setBeneficiary(new PartyIban()));

        $this->hashGenerator->expects($this->atLeastOnce())->method('generateHash')->willReturn('AB123');

        foreach ($transferRequest->getTransfers() as $index => $transfer) {
            $transferEvent = new TransferEvent($transfer);
            $this->mockTransferEventDate($transferEvent);
            $this->dispatcher
                ->expects($this->at($index))
                ->method('dispatch')
                ->with($this->equalTo(TransferEvents::ON_STATUS_PREPARED), $this->equalTo($transferEvent))
            ;
        }

        $this->dispatcher
            ->expects($this->at(3))
            ->method('dispatch')
            ->with(
                $this->equalTo(TransferRequestEvents::ON_REGISTER),
                $this->equalTo(new TransferRequestEvent($transferRequest))
            )
        ;

        $this->transferManager->registerTransferRequest($transferRequest);

        foreach ($transferRequest->getTransfers() as $transfer) {
            $this->assertEquals(TransferOut::STATUS_PREPARED, $transfer->getStatus());
        }
    }

    public function testSkipExistingTransfersDuringRequestRegistration(): void
    {
        $transferRequest = new TransferRequest();
        $transferRequest->addTransfer((new TransferOutBank())->setBeneficiary(new PartyIban()));
        $transferRequest->addTransfer(
            (new TransferOutBank())
                ->setBeneficiary(new PartyIban())
                ->setStatus(Transfer::STATUS_NEW)
        );
        $transferRequest->addTransfer(
            (new TransferOutBank())
                ->setBeneficiary(new PartyIban())
                ->setStatus(Transfer::STATUS_FAILED)
        );

        $this->hashGenerator->expects($this->atLeastOnce())->method('generateHash')->willReturn('AB123');

        $this->transferManager->registerTransferRequest($transferRequest);

        $this->assertEquals(TransferOut::STATUS_PREPARED, $transferRequest->getTransfers()->get(0)->getStatus());
        $this->assertEquals(TransferOut::STATUS_PREPARED, $transferRequest->getTransfers()->get(1)->getStatus());
        $this->assertEquals(TransferOut::STATUS_FAILED, $transferRequest->getTransfers()->get(2)->getStatus());
    }

    /**
     * @param Transfer $transfer
     * @param array $processFilterResults
     * @param string $expectedStatus
     * @param string $expectInstance
     * @param int $hashCount
     *
     * @dataProvider transferDataProvider
     */
    public function testRegisterTransfer(
        Transfer $transfer,
        array $processFilterResults,
        string $expectedStatus,
        string $expectInstance,
        int $hashCount
    ) {
        $this->mockProcessTransfer($this->transferOutProcessor, $processFilterResults[0]);

        if (isset($processFilterResults[1])) {
            $this->mockProcessTransfer($this->transferInternalProcessor, $processFilterResults[1]);
        }
        $this->hashGenerator
            ->expects($this->exactly($hashCount))
            ->method('generateHash')
            ->willReturn('TA123')
        ;
        $returnedTransfer = $this->transferManager->registerTransfer($transfer);
        $this->assertEquals($expectedStatus, $returnedTransfer->getStatus());
        $this->assertInstanceOf($expectInstance, $returnedTransfer);
    }

    public function transferDataProvider(): array
    {
        return [
            [new TransferOutBank(), [true], Transfer::STATUS_NEW, TransferOutBank::class, 1],
            [
                new TransferOutBank(),
                [true],
                Transfer::STATUS_NEW,
                TransferOutBank::class,
                1,
            ],
            [new TransferOutBank(), [false], Transfer::STATUS_FAILED, TransferOutBank::class, 1],
            [
                (new TransferOutBank())->setTransferThatReplacedThisTransfer(new TransferInternal()),
                [false, true],
                Transfer::STATUS_NEW,
                TransferInternal::class,
                1,
            ],
            [
                (new TransferOutBank())->setTransferThatReplacedThisTransfer(
                    (new TransferInternal())
                        ->setStatus(Transfer::STATUS_FAILED)
                        ->setFailureStatus(new TransferFailureStatus())
                ),
                [false, false],
                Transfer::STATUS_FAILED,
                TransferOutBank::class,
                2,
            ],
        ];
    }

    public function testSetTransferRequestConvertCurrency(): void
    {
        $transferRequest = new TransferRequest();
        $currency = 'USD';

        $this->transferManager->setTransferRequestConvertCurrency($transferRequest, $currency);

        $transferRequestConvertCurrency = $transferRequest->getConvertToCurrency();

        $this->assertInstanceOf(TransferRequestConvertCurrency::class, $transferRequestConvertCurrency);
    }

    /**
     * @param TransferProcessorInterface|MockObject $processor
     * @param bool $processFilterResults
     */
    private function mockProcessTransfer(TransferProcessorInterface $processor, bool $processFilterResults)
    {
        $processor
            ->expects($this->once())
            ->method('processTransfer')
            ->willReturnCallback(function (Transfer $transfer) use ($processFilterResults) {
                if ($processFilterResults) {
                    return true;
                }
                $transfer->setStatus(Transfer::STATUS_FAILED);
                $transfer->setFailureStatus(new TransferFailureStatus(TransferFailureStatus::CODE_VALIDATION));
                return false;
            })
        ;
    }

    private function mockTransferEventDate(TransferEvent $expectedResult)
    {
        $this->dispatcher
            ->expects($this->any())
            ->method('dispatch')
            ->willReturnCallback(function ($status, $transferEvent) use ($expectedResult) {
                if ($transferEvent instanceof TransferEvent) {
                    $transferEvent->setCreatedAt($expectedResult->getCreatedAt());
                }
            })
        ;
    }
}
