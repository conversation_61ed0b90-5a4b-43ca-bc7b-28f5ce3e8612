<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Psr\Log\LoggerInterface;

class SepaTransferValidationStep implements StepInterface
{
    const PARTY_NAME_MAX_LENGTH = 70;

    private $transferStatusManager;
    private $sepaBankKeys;
    private $maxDetailsLength;
    private $logger;

    public function __construct(
        TransferStatusManager $transferStatusManager,
        array $sepaBankKeys,
        $maxDetailsLength,
        LoggerInterface $logger
    ) {
        $this->transferStatusManager = $transferStatusManager;
        $this->sepaBankKeys = $sepaBankKeys;
        $this->maxDetailsLength = $maxDetailsLength;
        $this->logger = $logger;
    }

    /**
     * Applies step to Transfer
     *
     * @param Transfer $transfer
     *
     * @return bool
     */
    public function apply(Transfer $transfer)
    {
        if (!$transfer instanceof TransferOutBank) {
            return true;
        }

        if (!in_array($transfer->getBank(), $this->sepaBankKeys, true)) {
            return true;
        }

        $failureStatus = $this->validate($transfer);
        if ($failureStatus !== null) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus($failureStatus, 'SEPA transfer validation step error')
            );

            $this->logger->info(
                'SEPA transfer marked as failed due invalid data',
                [$transfer, $failureStatus]
            );
            return false;
        }
        return true;
    }

    private function validate(TransferOutBank $transfer): ?string
    {
        $resolvedBeneficiary = $transfer->getResolvedBeneficiary();

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])
            && !($resolvedBeneficiary instanceof PartyIban)
        ) {
            return TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_INVALID;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_IBAN])
            && $resolvedBeneficiary->getIban() === null
        ) {
            return TransferFailureStatus::CODE_BENEFICIARY_IBAN_NOT_SET;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_NAME])
            && $resolvedBeneficiary->getName() === null
        ) {
            return TransferFailureStatus::CODE_BENEFICIARY_NAME_NOT_SET;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_NAME])
            && mb_strlen($resolvedBeneficiary->getDisplayName()) > self::PARTY_NAME_MAX_LENGTH
        ) {
            return TransferFailureStatus::CODE_BENEFICIARY_NAME_TO_LONG;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])
            && empty($transfer->getReferenceNumber()) && empty($transfer->getDetails())
        ) {
            return TransferFailureStatus::CODE_DETAILS_NOT_SET;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])
            && ValidationHelper::isProcessable($transfer, [ValidationGroup::REFERENCE_NUMBER])
            && $transfer->getDetails() && $transfer->getReferenceNumber()
        ) {
            return TransferFailureStatus::CODE_DETAILS_AND_REFERENCE_SET_IN_SEPA_TRANSFER;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])
            && mb_strlen($transfer->getDetails()) > $this->maxDetailsLength
        ) {
            return TransferFailureStatus::CODE_DETAILS_TOO_LONG;
        }

        if (
            ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])
            && mb_strlen($transfer->getReferenceToPayerValue()) > $this->maxDetailsLength
        ) {
            return TransferFailureStatus::CODE_REFERENCE_TO_PAYER_TOO_LONG;
        }

        return null;
    }
}
