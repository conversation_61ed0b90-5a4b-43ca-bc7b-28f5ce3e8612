<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\CurrencyBundle\Service\CurrencyManager;
use Psr\Log\LoggerInterface;

class ValidateCurrencyStep implements StepInterface
{
    private $transferStatusManager;
    private $currencyManager;
    private $accountOwnerResolver;
    private $logger;

    public function __construct(
        TransferStatusManager $transferStatusManager,
        CurrencyManager $currencyManager,
        AccountOwnerResolver $accountOwnerResolver,
        LoggerInterface $logger
    ) {
        $this->transferStatusManager = $transferStatusManager;
        $this->currencyManager = $currencyManager;
        $this->accountOwnerResolver = $accountOwnerResolver;
        $this->logger = $logger;
    }

    public function apply(Transfer $transfer)
    {
        if (!ValidationHelper::isProcessable($transfer, [ValidationGroup::AMOUNT])) {
            return true;
        }

        /** @var TransferInternal $transfer */
        $currency = $this->currencyManager->findActiveCurrencyByCode(
            $this->accountOwnerResolver->getRealAccountOwner($transfer->getCreditAccount()),
            $transfer->getAmountCurrency()
        );

        if ($currency === null || !$currency->externalTransfersEnabled()) {
            $this->logger->info('Active currency not found', ['transfer_id' => $transfer->getId()]);
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_TRANSFER_CURRENCY_DISABLED)
            );

            return false;
        }

        return true;
    }
}
