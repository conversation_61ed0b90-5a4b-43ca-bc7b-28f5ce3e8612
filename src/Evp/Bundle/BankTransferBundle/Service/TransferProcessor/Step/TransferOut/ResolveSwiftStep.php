<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankBundle\Exception\SwiftNotResolvedException;
use Evp\Bundle\BankBundle\Service\BankBicChangeResolver;
use Evp\Bundle\BankBundle\Service\SwiftManager;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\Swift;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ResolveSwiftStep implements StepInterface
{
    private SwiftManager $swiftManager;
    private TransferStatusManager $transferStatusManager;
    private ValidatorInterface $validator;
    private BankBicChangeResolver $bankBicChangeResolver;
    private LoggerInterface $logger;

    public function __construct(
        SwiftManager $swiftManager,
        TransferStatusManager $transferStatusManager,
        ValidatorInterface $validator,
        BankBicChangeResolver $bankBicChangeResolver,
        LoggerInterface $logger
    ) {
        $this->swiftManager = $swiftManager;
        $this->transferStatusManager = $transferStatusManager;
        $this->validator = $validator;
        $this->bankBicChangeResolver = $bankBicChangeResolver;
        $this->logger = $logger;
    }

    public function apply(Transfer $transfer)
    {
        if (!$transfer instanceof TransferOutBank) {
            return true;
        }

        $beneficiary = $transfer->getResolvedBeneficiary();

        if ($beneficiary->getCorrespondentBankSwift() !== null) {
            $beneficiary->setCorrespondentBankSwift(
                $this->swiftManager->prepareSwift($beneficiary->getCorrespondentBankSwift())
            );
        }

        if (!$beneficiary instanceof PartyIban) {
            return $this->processNonIbanTransfer($transfer);
        }

        if ($beneficiary->getBic() === null || $beneficiary->getBic() === '') {
            return $this->processTransferWithoutSwiftCode($transfer);
        } else {
            return $this->processTransferWithSwiftCode($transfer);
        }
    }

    /**
     * @param TransferOutBank $transfer
     * @return bool
     */
    private function processNonIbanTransfer(TransferOutBank $transfer)
    {
        $beneficiary = $transfer->getResolvedBeneficiary();

        if ($beneficiary->getBic() !== null) {
            $beneficiary->setBic(
                $this->swiftManager->prepareSwift($beneficiary->getBic())
            );
            $this->resolveBicChange($beneficiary);

            if (
                ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])
                && !$this->swiftManager->isValidSwiftCountry($beneficiary->getBic())
            ) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_BANK_SWIFT_INVALID)
                );

                return false;
            }
        }

        return true;
    }

    private function processTransferWithoutSwiftCode(TransferOutBank $transfer): bool
    {
        $beneficiary = $transfer->getResolvedBeneficiary();

        try {
            $swift = $this->swiftManager->getSwift($beneficiary->getIban(), $transfer->getAmountCurrency());

            if ($swift !== null) {
                $beneficiary->setBic($swift->getSwift());

                if ($beneficiary->getBankName() === null || $beneficiary->getBankName() === '') {
                    $beneficiary->setBankName($swift->getBankName());
                }

                return $this->processTransferWithSwiftCode($transfer);
            } elseif (ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_BANK_SWIFT_NOT_SET)
                );
            }
        } catch (SwiftNotResolvedException $exception) {
            $this->logger->info('Swift cannot be resolved', [$exception, $beneficiary->getIban()]);

            if (ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_BANK_CODE])) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_BANK_SWIFT_NOT_SET)
                );
            }
        }

        return false;
    }

    private function processTransferWithSwiftCode(TransferOutBank $transfer): bool
    {
        $transfer->getResolvedBeneficiary()->setBic(
            $this->swiftManager->prepareSwift($transfer->getResolvedBeneficiary()->getBic())
        );
        $this->resolveBicChange($transfer->getResolvedBeneficiary());

        $constraint = new Swift([
            'groups' => [ValidationGroup::DEFAULT, ValidationGroup::BENEFICIARY_BANK_CODE],
        ]);
        $violations = $this->validator->validate($transfer, $constraint, ValidationHelper::getValidationGroups($transfer));
        if (count($violations)) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_BANK_SWIFT_INVALID)
            );
            return false;
        }

        return true;
    }

    private function resolveBicChange(PartyBank $beneficiary)
    {
        $bicChanged = $this->bankBicChangeResolver->resolveBicChange($beneficiary->getBic());
        if ($bicChanged !== null) {
            $beneficiary->setBic($bicChanged);
        }
    }
}
