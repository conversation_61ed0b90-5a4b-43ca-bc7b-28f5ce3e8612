<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPerson;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyTaxReference;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\SodraManager;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\AccountResolver;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;

class ResolvePrimaryPayerStep implements StepInterface
{
    private $transferStatusManager;
    private $sodraManager;
    private $accountResolver;

    /**
     * @param TransferStatusManager $transferStatusManager
     * @param SodraManager $sodraManager
     * @param AccountResolver $accountResolver
     */
    public function __construct(
        TransferStatusManager $transferStatusManager,
        SodraManager $sodraManager,
        AccountResolver $accountResolver
    ) {
        $this->transferStatusManager = $transferStatusManager;
        $this->sodraManager = $sodraManager;
        $this->accountResolver = $accountResolver;
    }

    /**
     * Applies step to Transfer
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\Transfer $transfer
     *
     * @return bool
     *
     * @throws \InvalidArgumentException
     */
    public function apply(Transfer $transfer)
    {
        if (!$transfer instanceof TransferOutInterface) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported transfer class (transfer_class=%s). Expected instance of TransferOutInterface',
                    get_class($transfer)
                )
            );
        }

        if ($this->sodraManager->isSodraParty($transfer)) {
            if (!$this->isValidSodraPrimaryPayer($transfer)) {
                $account = $this->accountResolver->resolveAccount($transfer->getPayer());
                if ($account !== null) {
                    $client = $account->getClient();
                    if ($client instanceof ClientLegal || $client instanceof ClientNatural) {
                        $transfer->setPrimaryPayer(new PartyPerson(
                                $client->getDisplayName(),
                                $client->getCode()
                            )
                        );
                    }
                }

            }
            if (ValidationHelper::isProcessable($transfer, [ValidationGroup::REFERENCE_NUMBER])
                && !$transfer->getReferenceNumber()
            ) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_NUMBER_NOT_SET)
                );
            }

            if ($transfer->isFailed()) {
                return false;
            }
            if (
                $transfer->getCreditAccount()->getClient() instanceof ClientNatural
                && $transfer->getPurpose() === Transfer::PURPOSE_PLAIS
            ) {
                $transfer->setReferenceToPayerItems(
                    'natural',
                    'nidn',
                    $transfer->getCreditAccount()->getClient()->getCode()
                );
            }
        }

        return true;
    }

    /**
     * Check's if is valid Sodra Primary payer party
     *
     * @param Transfer $transfer
     *
     * @return bool
     */
    protected function isValidSodraPrimaryPayer(Transfer $transfer)
    {
        if ($transfer->getPrimaryPayer()
            && (($transfer->getPrimaryPayer() instanceof PartyIban)
                || ($transfer->getPrimaryPayer() instanceof PartyPerson)
                || ($transfer->getPrimaryPayer() instanceof PartyTaxReference))
        ) {
            return true;
        }

        return false;
    }
}
