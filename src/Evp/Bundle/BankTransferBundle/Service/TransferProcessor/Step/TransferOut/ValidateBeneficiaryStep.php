<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Psr\Log\LoggerInterface;

class ValidateBeneficiaryStep implements StepInterface
{
    /**
     * @var TransferStatusManager
     */
    private $transferStatusManager;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * ValidateBeneficiaryStep constructor.
     * @param TransferStatusManager $transferStatusManager
     * @param LoggerInterface $logger
     */
    public function __construct(TransferStatusManager $transferStatusManager, LoggerInterface $logger)
    {
        $this->transferStatusManager = $transferStatusManager;
        $this->logger = $logger;
    }

    public function apply(Transfer $transfer)
    {
        if (!$transfer instanceof TransferOut) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported transfer class (transfer_class=%s). Expected instance of TransferOutInterface',
                    get_class($transfer)
                )
            );
        }

        if (!ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY])
            && !ValidationHelper::isProcessable($transfer, [ValidationGroup::PAYER])
        ) {
            return true;
        }

        $mainPayer = $transfer->getMainPayer();
        $beneficiary = $transfer->getBeneficiary();
        if (
            $mainPayer instanceof PartyIban && $beneficiary instanceof PartyIban
            && $mainPayer->getIban() === $beneficiary->getIban()
        ) {
            return $this->failTransfer($transfer);
        }
        return true;
    }

    /**
     * @param Transfer $transfer
     * @return bool
     */
    private function failTransfer(Transfer $transfer)
    {
        $this->transferStatusManager->markAsFailed(
            $transfer,
            new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_INVALID)
        );
        $this->logger->info(
            'Transfer marked as failed due to invalid beneficiary',
            ['transfer_id' => $transfer->getId()]
        );
        return false;
    }
}
