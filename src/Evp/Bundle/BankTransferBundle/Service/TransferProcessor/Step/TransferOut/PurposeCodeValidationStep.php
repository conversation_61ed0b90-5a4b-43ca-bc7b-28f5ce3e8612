<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Exception\TransferProcessorException;
use Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Psr\Log\LoggerInterface;

class PurposeCodeValidationStep implements StepInterface
{
    private $purposeCodeResolver;
    private $transferStatusManager;
    private $logger;

    public function __construct(
        PurposeCodeResolver $purposeCodeResolver,
        TransferStatusManager $transferStatusManager,
        LoggerInterface $logger
    ) {
        $this->purposeCodeResolver = $purposeCodeResolver;
        $this->transferStatusManager = $transferStatusManager;
        $this->logger = $logger;
    }

    public function apply(Transfer $transfer)
    {
        if (!$transfer instanceof TransferOutInterface) {
            throw new TransferProcessorException(sprintf('Unsupported transfer of class %s', get_class($transfer)));
        }

        if (
            !ValidationHelper::isProcessable($transfer, [ValidationGroup::PURPOSE])
            || $transfer->getPurposeCode() === null
            || in_array($transfer->getPurposeCode(), $this->purposeCodeResolver->getInternalPurposeCodes(), true)
        ) {
            return true;
        }

        $this->logger->info(
            'Transfer marked as failed due to invalid purpose code',
            [$transfer, $transfer->getPurposeCode()]
        );

        $this->transferStatusManager->markAsFailed(
            $transfer,
            new TransferFailureStatus(TransferFailureStatus::CODE_PURPOSE_CODE_INVALID)
        );

        return false;
    }
}
