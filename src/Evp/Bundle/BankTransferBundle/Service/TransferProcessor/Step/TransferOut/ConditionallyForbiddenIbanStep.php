<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;

class ConditionallyForbiddenIbanStep implements StepInterface
{
    private $transferStatusManager;
    private $skipBankCodes;
    private $ibans;

    public function __construct(TransferStatusManager $transferStatusManager, array $skipBankCodes, array $ibans)
    {
        $this->transferStatusManager = $transferStatusManager;
        $this->skipBankCodes = $skipBankCodes;
        $this->ibans = $ibans;
    }

    public function apply(Transfer $transfer)
    {
        if (!ValidationHelper::isProcessable($transfer, [ValidationGroup::BENEFICIARY_IBAN])) {
            return true;
        }

        if (!$transfer instanceof TransferOutBank) {
            return true;
        }

        $beneficiary = $transfer->getResolvedBeneficiary();

        if (!$beneficiary instanceof PartyIban) {
            return true;
        }

        if (in_array($transfer->getBank(), $this->skipBankCodes, true)) {
            return true;
        }

        if (in_array($beneficiary->getIban(), $this->ibans, true)) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_FORBIDEN)
            );

            return false;
        }

        return true;
    }
}
