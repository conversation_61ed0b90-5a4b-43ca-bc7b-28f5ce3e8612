<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\TransferOut;

use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\TransitAccountClientResolver;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\DetailsRequirementsManager;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\Translation\TranslatorInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ReformatDetailsStep implements StepInterface
{
    protected $detailsRequirementsManager;
    protected $transferStatusManager;
    protected $translator;
    protected $validator;
    protected $transitAccountClientResolver;
    protected $logger;

    public function __construct(
        DetailsRequirementsManager $detailsRequirementsManager,
        TransferStatusManager $transferStatusManager,
        TranslatorInterface $translator,
        ValidatorInterface $validator,
        TransitAccountClientResolver $transitAccountClientResolver,
        LoggerInterface $logger
    ) {
        $this->detailsRequirementsManager = $detailsRequirementsManager;
        $this->transferStatusManager = $transferStatusManager;
        $this->translator = $translator;
        $this->validator = $validator;
        $this->transitAccountClientResolver = $transitAccountClientResolver;
        $this->logger = $logger;
    }

    /**
     * Applies step to Transfer
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\Transfer $transfer
     *
     * @return bool
     *
     * @throws \InvalidArgumentException
     */
    public function apply(Transfer $transfer)
    {
        $this->logger->debug(__METHOD__, func_get_args());

        if (!$transfer->getDetails()) {
            return true;
        }

        $prepend = null;
        if ($transfer instanceof TransferOutBank && $this->detailsRequirementsManager->canChangeDetails($transfer)) {
            $prepend = $this->getStandardDetailsPrefix($transfer);
        } elseif ($transfer instanceof TransferInternal && $transfer->getPurpose() === Transfer::PURPOSE_CASHBACK) {
            $prepend = $this->translator->trans(
                'transfer.details.cashback.prefix',
                [],
                'EvpBankTransferBundle',
                $transfer->getDebitAccount()->getClient()->getLocale()
            );
        }
        if (
            $prepend !== null
            && strpos(mb_strtolower($transfer->getDetails(), 'UTF-8'), mb_strtolower($prepend, 'UTF-8')) !== 0
        ) {
            $transfer->setDetails($prepend . $transfer->getDetails());
        }

        if (ValidationHelper::isProcessable($transfer, [ValidationGroup::DETAILS])) {
            $errors = $this->validator->validate($transfer, null, [ValidationGroup::DETAILS]);

            if (count($errors) > 0) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_DETAILS_TOO_LONG)
                );

                return false;
            }
        }

        return true;
    }

    private function getStandardDetailsPrefix(TransferOutBank $transfer): string
    {
        $displayName = $transfer->getCreditAccount()->getType() === Account::TYPE_TRANSIT
            ? $this->transitAccountClientResolver->getRelatedClient($transfer->getCreditAccount())->getDisplayName()
            : $transfer->getPayer()->getDisplayName()
        ;

        return $displayName . '. ';
    }
}
