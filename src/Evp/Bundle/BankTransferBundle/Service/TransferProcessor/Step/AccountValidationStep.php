<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step;

use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Exception\TransferProcessorException;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\AccountNumber;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\ActiveAccounts;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;

class AccountValidationStep implements StepInterface
{
    private ValidatorInterface $validator;

    private TransferStatusManager $transferStatusManager;

    private LoggerInterface $logger;

    public function __construct(
        ValidatorInterface $validator,
        TransferStatusManager $transferStatusManager,
        LoggerInterface $logger
    ) {
        $this->validator = $validator;
        $this->transferStatusManager = $transferStatusManager;
        $this->logger = $logger;
    }

    /**
     * Applies step to Transfer
     *
     * @param Transfer $transfer
     *
     * @return bool
     *
     * @throws TransferProcessorException
     */
    public function apply(Transfer $transfer)
    {
        $this->logger->debug(__METHOD__, ['transfer_id' => $transfer->getId()]);

        $this->logger->info('Validating account', [
            'transfer_id' => $transfer->getId(),
        ]);

        $validationGroups = ValidationHelper::getValidationGroups($transfer);

        // Skip validation for internal transfers
        if (
               ($transfer instanceof TransferInInterface && $transfer->getDebitAccount() !== null)
            || !$transfer instanceof TransferInInterface
        ) {
            $constraint = new ActiveAccounts([
                'validateBeneficiaryAccount' => true,
                'validatePayerAccount' => false,
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::BENEFICIARY]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);
            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_INACTIVE)
                );
                $this->logger->info(
                    'Transfer marked as failed due to inactive beneficiary account',
                    [$transfer, $violations]
                );
                return false;
            }
        }

        $constraint = new ActiveAccounts([
            'validateBeneficiaryAccount' => false,
            'validatePayerAccount' => true,
            'groups' => [ValidationGroup::DEFAULT, ValidationGroup::PAYER]
        ]);
        $violations = $this->validator->validate($transfer, $constraint, $validationGroups);
        if (count($violations)) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_PAYER_ACCOUNT_INACTIVE)
            );
            $this->logger->info(
                'Transfer marked as failed due to inactive payer account',
                [$transfer, $violations]
            );
            return false;
        }

        $constraint = new AccountNumber(['groups' => [ValidationGroup::DEFAULT, ValidationGroup::BENEFICIARY_ACCOUNT_NUMBER]]);
        $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

        if (count($violations)) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_NUMBER_INVALID)
            );
            $this->logger->info(
                'Transfer marked as failed due to invalid beneficiary account number',
                [$transfer, $violations]
            );
            return false;
        }

        return true;
    }
}
