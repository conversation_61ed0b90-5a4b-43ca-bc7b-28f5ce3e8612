<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvert;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutTax;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Enum\ValidationGroup;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\StepInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferStatusManager;
use Evp\Bundle\BankTransferBundle\Service\TransferToVmiDetector;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\ContisAccountSupportedCurrency;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\ForbiddenBeneficiary;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\InvalidBeneficiaryName;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\OnlyInternalTransfer;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\SodraReferenceNumber;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\TaxLines;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\TransferCharacters;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\TransferCountry;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\UniqueTransfer;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\VmiPayer;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\VmiReferenceNumber;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\VmiReferenceToBeneficiary;
use Evp\Bundle\BankTransferBundle\Validator\Constraints\VmiReferenceToPayer;
use Psr\Log\LoggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ValidationStep implements StepInterface
{
    private TransferStatusManager $transferStatusManager;
    private ValidatorInterface $validator;
    private TransferToVmiDetector $vmiTransferDetector;
    private LoggerInterface $logger;

    public function __construct(
        TransferStatusManager $transferStatusManager,
        ValidatorInterface $validator,
        TransferToVmiDetector $vmiTransferDetector,
        LoggerInterface $logger
    ) {
        $this->transferStatusManager = $transferStatusManager;
        $this->validator = $validator;
        $this->vmiTransferDetector = $vmiTransferDetector;
        $this->logger = $logger;
    }

    public function apply(Transfer $transfer): bool
    {
        $this->logger->debug(__METHOD__, ['transfer_id' => $transfer->getId()]);

        $validationGroups = ValidationHelper::getValidationGroups($transfer);

        $constraint = new UniqueTransfer([
            'groups' => [ValidationGroup::DEFAULT, ValidationGroup::TRANSFER_NUMBER],
        ]);
        $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

        if (count($violations)) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus(TransferFailureStatus::CODE_NUMBER_NOT_UNIQUE)
            );
            $this->logger->info(
                'Transfer marked as failed due to transfer number duplicate',
                [$transfer, $violations]
            );

            return false;
        }

        $constraint = new ForbiddenBeneficiary();
        $violations = $this->validator->validate($transfer, $constraint);

        if (count($violations) > 0) {
            $this->transferStatusManager->markAsFailed(
                $transfer,
                new TransferFailureStatus($violations[0]->getCode())
            );
            $this->logger->info(
                'Transfer marked as failed due to forbidden beneficiary',
                [$transfer, $violations]
            );

            return false;
        }

        if ($transfer instanceof TransferOutBank) {
            $constraint = new VmiPayer([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::REFERENCE_TO_BENEFICIARY],
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_TO_BENEFICIARY_INVALID)
                );
                $this->logger->info(
                    'Transfer marked as failed due invalid reference to beneficiary or empty payer code',
                    [$transfer, $violations]
                );

                return false;
            }

            if ($transfer->getBeneficiary() instanceof PartyIban && $this->vmiTransferDetector->isVmiTransferParty($transfer)) {
                $constraint = new VmiReferenceToBeneficiary([
                    'groups' => [ValidationGroup::DEFAULT, ValidationGroup::REFERENCE_TO_BENEFICIARY],
                ]);
                $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

                if (count($violations)) {
                    $this->transferStatusManager->markAsFailed(
                        $transfer,
                        new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_TO_BENEFICIARY_INVALID)
                    );
                    $this->logger->info(
                        'Transfer marked as failed due invalid reference to beneficiary code',
                        [$transfer, $violations]
                    );

                    return false;
                }
            }

            $constraint = new VmiReferenceToPayer();
            $violations = $this->validator->validate($transfer, $constraint);

            if (count($violations)) {
                foreach ($violations as $violation) {
                    if (ValidationHelper::isProcessable($transfer, [ValidationGroup::PAYER_ROIK])
                        && $violation->getMessage() === VmiReferenceToPayer::MESSAGE_ROIK_CODE_INVALID
                    ) {
                        $this->transferStatusManager->markAsFailed(
                            $transfer,
                            new TransferFailureStatus(TransferFailureStatus::CODE_TAX_ROIK_CODE_INVALID)
                        );

                        $this->logger->info('Transfer marked as failed due to invalid roik code in VMI transfer');

                        return false;
                    } elseif (ValidationHelper::isProcessable($transfer, [ValidationGroup::REFERENCE_TO_PAYER])
                        && $violation->getMessage() === VmiReferenceToPayer::MESSAGE_REFERENCE_TO_PAYER_INVALID
                    ) {
                        $this->transferStatusManager->markAsFailed(
                            $transfer,
                            new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_TO_PAYER_INVALID)
                        );
                        $this->logger->info('Transfer marked as failed due invalid reference to payer in VMI transfer');

                        return false;
                    }
                }
            }

            $constraint = new SodraReferenceNumber([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::REFERENCE_NUMBER]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_NUMBER_INVALID)
                );
                $this->logger->info(
                    'Transfer marked as failed due invalid sodra reference number',
                    [$transfer, $violations]
                );

                return false;
            }

            $constraint = new VmiReferenceNumber([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::REFERENCE_NUMBER]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_REFERENCE_NUMBER_INVALID)
                );
                $this->logger->info(
                    'Transfer marked as failed due invalid VMI reference number',
                    [$transfer, $violations]
                );

                return false;
            }

            $constraint = new TransferCountry([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::BENEFICIARY_BANK_CODE]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_COUNTRY_AND_SWIFT_DOES_NOT_MATCH)
                );
                $this->logger->info(
                    'Transfer marked as failed due invalid country/swift code',
                    [$transfer, $violations]
                );

                return false;
            }

            $constraint = new TransferCharacters();
            $violations = $this->validator->validate($transfer, $constraint);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_FORBIDDEN_CHARACTERS)
                );
                $this->logger->info(
                    'Transfer marked as failed due non printable characters',
                    [$transfer, $violations]
                );

                return false;
            }

            $constraint = new InvalidBeneficiaryName([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::BENEFICIARY_NAME]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations)) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_BENEFICIARY_INVALID_NAME)
                );
                $this->logger->info(
                    'Transfer marked as failed due to invalid beneficiary name',
                    [$transfer, $violations]
                );

                return false;
            }

            if ($transfer->getBeneficiary() instanceof PartyIban) {
                $constraint = new OnlyInternalTransfer();
                $violations = $this->validator->validate($transfer, $constraint);

                if (count($violations)) {
                    $this->transferStatusManager->markAsFailed(
                        $transfer,
                        new TransferFailureStatus(TransferFailureStatus::CODE_ONLY_INTERNAL_TRANSFER)
                    );
                    $this->logger->info(
                        'Transfer marked as failed due incorrect transfer type',
                        [$transfer, $violations]
                    );

                    return false;
                }
            }
        }

        if ($transfer instanceof TransferOutInterface && !$transfer instanceof TransferCurrencyConvert) {
            $constraint = new ContisAccountSupportedCurrency([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::AMOUNT]
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations) > 0) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus(TransferFailureStatus::CODE_CONTIS_ACCOUNT_TRANSFER_CURRENCY_UNSUPPORTED)
                );
                $this->logger->info(
                    'Transfer marked as failed due to unsupported currency',
                    [$transfer, $violations]
                );

                return false;
            }
        }

        if ($transfer instanceof TransferOutTax && $transfer->getTaxLines() !== null) {
            $constraint = new TaxLines([
                'groups' => [ValidationGroup::DEFAULT, ValidationGroup::REFERENCE_TO_PAYER],
            ]);
            $violations = $this->validator->validate($transfer, $constraint, $validationGroups);

            if (count($violations) > 0) {
                $this->transferStatusManager->markAsFailed(
                    $transfer,
                    new TransferFailureStatus($violations[0]->getMessage())
                );

                return false;
            }
        }

        return true;
    }
}
