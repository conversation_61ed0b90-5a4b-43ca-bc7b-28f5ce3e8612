<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use InvalidArgumentException;
use Paysera\Bundle\BpbBundle\Service\XlsxWriter;

class BpbXkMapper implements TransferExportInterface
{
    private $xlsxWriter;
    private $maxDetailsLength;

    public function __construct(XlsxWriter $xlsxWriter, int $maxDetailsLength) {
        $this->xlsxWriter = $xlsxWriter;
        $this->maxDetailsLength = $maxDetailsLength;
    }

    public function mapFromTransferList(array $transferList)
    {
        return $this->xlsxWriter->getContents($transferList);
    }

    public function getFileExtension()
    {
        return '.xlsx';
    }

    public function getTransferDetails(TransferOut $transfer)
    {
        return $transfer->getDetails();
    }

    public function validate(TransferOut $transfer): TransferMapperValidationStatus
    {
        if (!($transfer instanceof TransferOutBank)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $resolvedBeneficiary = $transfer->getResolvedBeneficiary();

        if ($resolvedBeneficiary->getDisplayName() === null) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_BENEFICIARY_NAME_NOT_SET);
        }

        if (
            $resolvedBeneficiary instanceof PartyAccountCountry
            && (
                $resolvedBeneficiary->getAccount() === null
                || strlen($resolvedBeneficiary->getAccount()) !== 16
            )
        ) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_INVALID);
        }

        if (mb_strlen($transfer->getDetails()) > $this->maxDetailsLength) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_DETAILS_TOO_LONG);
        }

        if ($transfer->getDetails() === '' || $transfer->getDetails() === null) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_DETAILS_NOT_SET);
        }


        return new TransferMapperValidationStatus();
    }
}
