<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Service\MoreUrlBuilder;
use Evp\Bundle\BankTransferBundle\Service\TransferDetailsFormatter;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use Evp\Component\Date\DateTimeProvider;
use Evp\Component\TextFilter\TextFilter;
use Evp\Component\Transliteration\TransliteratorInterface;
use Exception;
use InvalidArgumentException;
use Paysera\Bundle\LibraBankBundle\Service\BudgetPaymentChecker;

class TechventuresBankMapper extends ManualBaseMapper
{
    const TECHVENTURES_IDENTIFIER = 'BFER';

    private BudgetPaymentChecker $budgetPaymentChecker;
    private TransliteratorInterface $transliterator;
    private TextFilter $textFilter;
    private DateTimeProvider $dateTimeProvider;
    private string $ourAccountNumber;
    private int $maxAllowedTransferDetailsLength;
    private int $maxAllowedBeneficiaryNameLength;
    private int $currentRowNumber;

    public function __construct(
        AccountInfoResolver $accountInfoResolver,
        MoreUrlBuilder $moreUrlBuilder,
        string $countryCode,
        string $currency,
        TransferDetailsFormatter $transferDetailsFormatter,
        BudgetPaymentChecker $budgetPaymentChecker,
        TransliteratorInterface $transliterator,
        TextFilter $textFilter,
        DateTimeProvider $dateTimeProvider,
        string $ourAccountNumber,
        int $maxAllowedTransferDetailsLength,
        int $maxAllowedBeneficiaryNameLength
    ) {
        parent::__construct(
            $accountInfoResolver,
            $moreUrlBuilder,
            $countryCode,
            $currency,
            $transferDetailsFormatter
        );

        $this->budgetPaymentChecker = $budgetPaymentChecker;
        $this->transliterator = $transliterator;
        $this->textFilter = $textFilter;
        $this->dateTimeProvider = $dateTimeProvider;
        $this->ourAccountNumber = $ourAccountNumber;
        $this->maxAllowedTransferDetailsLength = $maxAllowedTransferDetailsLength;
        $this->maxAllowedBeneficiaryNameLength = $maxAllowedBeneficiaryNameLength;
        $this->currentRowNumber = 0;
    }

    public function getFileExtension(): string
    {
        return '.txt';
    }

    /**
     * @param TransferOutBank[] $transferList
     *
     * @return string
     */
    public function mapFromTransferList(array $transferList): string
    {
        $buffer = [];

        foreach ($transferList as $transfer) {
            $this->currentRowNumber++;
            $buffer[] = $this->mapTransferToRow($transfer);
        }

        return implode("\r\n", $buffer);
    }

    /**
     * @param TransferOut $transfer
     *
     * @return string
     * @throws InvalidArgumentException
     */
    protected function mapTransferToRow(TransferOut $transfer): string
    {
        if (!$transfer instanceof TransferOutBank) {
            throw new InvalidArgumentException('Transfer is not TransferOutBank.');
        }

        $beneficiary = $transfer->getResolvedBeneficiary();

        if (!($beneficiary instanceof PartyIban)) {
            throw new InvalidArgumentException(
                sprintf('Unsupported transfer beneficiary party %s', get_class($beneficiary))
            );
        }

        $currentRow[] = (string) $transfer->getId();
        $currentRow[] = $this->ourAccountNumber;
        $currentRow[] = $transfer->getAmount();
        $currentRow[] = '';
        $currentRow[] = (string) $this->currentRowNumber;
        $currentRow[] = $this->dateTimeProvider->getCurrent()->format('d.m.Y');
        $currentRow[] = $this->trim(
            $this->sanitizeData($transfer->getDetails()),
            $this->maxAllowedTransferDetailsLength
        );

        $currentRow[] = $this->trim(
            $this->sanitizeData($beneficiary->getDisplayName()),
            $this->maxAllowedBeneficiaryNameLength
        );

        $currentRow[] = $beneficiary->getIban();
        $currentRow[] = $beneficiary->getBic();
        $currentRow[] = $transfer->getReferenceToPayerValue();
        $currentRow[] = '';
        $currentRow[] = '';
        $currentRow[] = $this->resolvePaymentType($transfer);

        return implode("\t", $currentRow);
    }

    public function validate(TransferOut $transfer): TransferMapperValidationStatus
    {
        /** @var TransferOutBank $transfer */
        if ($transfer->getAmountMoney()->getCurrency() !== $this->currency) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_COUNTRY_SPECIFIC_BAD_DATA
            );
        }

        $beneficiaryInfo = $this->accountInfoResolver->getAccountInfoForTransferParty(
            $transfer->getResolvedBeneficiary(),
            $transfer
        );

        try {
            $beneficiaryInfo->checkBankSwift();
            $beneficiaryInfo->checkIban();
        } catch (Exception $exception) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_BENEFICIARY_IBAN_NOT_SET);
        }

        if ($transfer->getDetails() === null) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_DETAILS_NOT_SET);
        }

        $transferDetails = $this->sanitizeData($transfer->getDetails());

        if (mb_strlen($transferDetails) === 0) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_DETAILS_INVALID_CHARACTERS);
        }

        if (mb_strlen($transferDetails) > $this->maxAllowedTransferDetailsLength) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_DETAILS_TOO_LONG);
        }

        $beneficiaryName = $this->sanitizeData($transfer->getResolvedBeneficiary()->getDisplayName());

        if (mb_strlen($beneficiaryName) > $this->maxAllowedBeneficiaryNameLength) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_BENEFICIARY_NAME_TO_LONG);
        }

        if ($transfer->getAmountMoney()->abs()->getAmount() !== $transfer->getAmount()) {
            return new TransferMapperValidationStatus(TransferFailureStatus::CODE_AMOUNT_NOT_NATURAL);
        }

        if ($this->budgetPaymentChecker->isBudgetPayment($transfer)) {
            if ($transfer->getReferenceToPayerValue() === null) {
                return new TransferMapperValidationStatus(TransferFailureStatus::CODE_REFERENCE_TO_PAYER_NOT_SET);
            }
        }

        return new TransferMapperValidationStatus();
    }

    private function resolvePaymentType(TransferOutBank $transfer): string
    {
        if ($this->isIntrabankPayment($transfer)) {
            return 'I';
        } elseif ($this->budgetPaymentChecker->isBudgetPayment($transfer)) {
            return 'T';
        } else {
            return 'E';
        }
    }

    private function sanitizeData(string $text): string
    {
        return $this->textFilter
            ->filter($this->transliterator->transliterate($text))
        ;
    }

    private function trim(string $text, int $maxLength): string
    {
        return substr($text, 0, $maxLength);
    }

    private function isIntrabankPayment(TransferOutBank $transfer): bool
    {
        return strpos($transfer->getResolvedBeneficiary()->getIban(), self::TECHVENTURES_IDENTIFIER) !== false;
    }
}
