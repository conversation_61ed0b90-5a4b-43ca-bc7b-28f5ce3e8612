<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankBundle\Exception\SwiftBankResolverException;
use Evp\Bundle\BankBundle\Service\IbanAccountResolverInterface;
use Evp\Bundle\BankBundle\Service\SwiftBankResolverInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Exception\MapperException;
use Evp\Bundle\BankTransferBundle\Service\TransferDetailsFormatter;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use Evp\Component\Money\Money;
use Psr\Log\LoggerInterface;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryAccountAndSwift;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryGiroNumber;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryName;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_CurrencyAndAmount;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_Details;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_EntryDate;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_OcrReference;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_PayerAccount;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_PaymentMethod;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_PaymentReason;
use SebCIOnline_Tag_Transaction_Domestic_Sweden_RemitterReference;

/**
 * SEB C&I Online Sweden format export to bank
 */
class SebCIOnlineSeMapper implements TransferExportInterface
{
    /**
     * Prefix used for remitter reference
     */
    const MAX_DETAILS_LENGTH = 35;

    /**
     * Prefix used for remitter reference
     */
    const NUMBER_PREFIX = 'G';

    /**
     * Default method of payment. Could be DIRECT CREDIT
     */
    const DEFAULT_PAYMENT_METHOD = 'MASS TRANSFER, OTHER';

    const PLUSGIRO_PAYMENT_METHOD = 'PG';

    /**
     * Exported file extension
     */
    const FILE_EXTENSION = '.seb';

    const PLUSGIRO_PATTERN = '/SE\d{8}9960\d{2}(\d{8})/';

    /**
     * @var \Evp\Bundle\BankBundle\Service\IbanAccountResolverInterface
     */
    protected $ibanAccountResolver;

    /**
     * @var SwiftBankResolverInterface
     */
    protected $swiftBankResolver;

    /**
     * @var TransferDetailsFormatter
     */
    protected $textFormater;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    public function __construct(
        IbanAccountResolverInterface $ibanAccountResolver,
        SwiftBankResolverInterface $swiftBankResolver,
        TransferDetailsFormatter $textFormater,
        LoggerInterface $logger
    ) {
        $this->ibanAccountResolver = $ibanAccountResolver;
        $this->swiftBankResolver = $swiftBankResolver;
        $this->textFormater = $textFormater;
        $this->logger = $logger;
    }

    /**
     * Map list of Transfer to string content
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferOut[] $transferList
     *
     * @return string
     * @throws \Exception
     */
    public function mapFromTransferList(array $transferList)
    {
        $this->logger->debug(__METHOD__, [$transferList]);

        $file = new \SebCIOnline_File();
        $file->setHeader(new \SebCIOnline_Part());

        $transactionCount = 0;
        $total = new Money(0, 'SEK');
        foreach ($transferList as $transfer) {
            $file->addTransaction($this->mapTransferToTransaction($transfer));

            $transactionCount++;
            $total = $total->add($transfer->getAmountMoney());
        }

        $trailer = new \SebCIOnline_Part();
        $trailer->addTags([
            new \SebCIOnline_Tag_Trailer_TransactionCount($transactionCount),
            new \SebCIOnline_Tag_Trailer_Total($total->formatAmount(2, ',')),
        ]);
        $file->setTrailer($trailer);

        return (string) $file;
    }

    /**
     * Maps given transfer to a transaction
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferOut $transfer
     *
     * @return \SebCIOnline_Part
     *
     * @throws \Evp\Bundle\BankTransferBundle\Exception\MapperException
     */
    private function mapTransferToTransaction(TransferOut $transfer)
    {
        if (!$transfer instanceof TransferOutBank) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        if ($transfer->getMainPayer() === null) {
            throw new MapperException('No main payer set');
        }
        if ($transfer->getBeneficiary() === null) {
            throw new MapperException('No main payer set');
        }
        if ($transfer->getDetails() === null) {
            throw new MapperException('No details set');
        }
        if ($transfer->getAmountMoney() === null) {
            throw new MapperException('No amount set');
        }

        $transaction = new \SebCIOnline_Part();

        $tags = $this->prepareTags($transfer);

        $transaction->addTags($tags);

        return $transaction;
    }

    /**
     * Gets transfer details formatted string
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferOut $transfer
     *
     * @return string
     */
    public function getTransferDetails(TransferOut $transfer)
    {
        $details = $transfer->getDetails();

        return $this->textFormater->formatText($details, '', self::MAX_DETAILS_LENGTH);
    }

    /**
     * Get extension of exported file
     *
     * @return string
     */
    public function getFileExtension()
    {
        return self::FILE_EXTENSION;
    }

    /**
     * Gets beneficiary domestic account from transfer
     *
     * @param TransferOutBank $transfer
     *
     * @return string
     */
    protected function getBeneficiaryAccount(TransferOutBank $transfer)
    {
        $beneficiary = $transfer->getResolvedBeneficiary();

        $beneficiaryAccount = ($beneficiary instanceof PartyIban)
            ? $this->ibanAccountResolver->getAccount($beneficiary->getIban())
            : $beneficiary->getAccount();

        try {
            $bank = $this->swiftBankResolver->getBank($beneficiary->getBic());
            if ($bank && ($bank->getKey() == 'se_seb' || $bank->getKey() == 'se_nordea')) {
                $beneficiaryAccount = substr($beneficiaryAccount, -11);
            }
        } catch (SwiftBankResolverException $exception) {
            if ($beneficiary->getBic() == 'NDEASESS') {
                $beneficiaryAccount = substr($beneficiaryAccount, -11);
            }
        }
        return $beneficiaryAccount;
    }

    /**
     * Validates transfer
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferOut $transfer
     *
     * @return \Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
     */
    public function validate(TransferOut $transfer)
    {
        if (!$transfer instanceof TransferOutBank) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $beneficiary = $transfer->getResolvedBeneficiary();

        if ($beneficiary instanceof PartyIban) {
            if (!$beneficiary->getIban()) {
                return new TransferMapperValidationStatus(
                    TransferFailureStatus::CODE_BENEFICIARY_IBAN_NOT_SET
                );
            }
        } else {
            if (!$beneficiary->getAccount()) {
                return new TransferMapperValidationStatus(
                    TransferFailureStatus::CODE_BENEFICIARY_ACCOUNT_NOT_SET
                );
            }
        }

        if (!$beneficiary->getName()
            || ctype_space($beneficiary->getName())
        ) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_BENEFICIARY_NAME_NOT_SET
            );
        }

        if (!$transfer->getDetails()) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_DETAILS_NOT_SET
            );
        }

        if (!$beneficiary->getBic()) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_BENEFICIARY_BANK_SWIFT_NOT_SET
            );
        }

        return new TransferMapperValidationStatus();
    }

    private function isPlusGiro(TransferOutBank $transfer)
    {
        if ($transfer->getResolvedBeneficiary() instanceof PartyIban) {
            return preg_match(self::PLUSGIRO_PATTERN, $transfer->getResolvedBeneficiary()->getIban()) === 1;
        }

        return false;
    }

    private function getPlusGiroNumber(TransferOut $transfer)
    {
        preg_match(self::PLUSGIRO_PATTERN, $transfer->getResolvedBeneficiary()->getIban(), $matches);

        return $matches[1];
    }

    private function prepareTags(TransferOutBank $transfer)
    {
        $beneficiaryBankSwift = $transfer->getResolvedBeneficiary()->getBic();

        if (strlen($beneficiaryBankSwift) === 8) {
            $beneficiaryBankSwift .= 'XXX';
        }

        $date = strtotime('today 17:00:00') < time()
            ? new \DateTime('tomorrow')
            : new \DateTime('now');

        $mandatoryTags = [
            new SebCIOnline_Tag_Transaction_Domestic_Sweden_RemitterReference(
                sprintf('%s%s', self::NUMBER_PREFIX, $transfer->getId())
            ),
            new SebCIOnline_Tag_Transaction_Domestic_Sweden_EntryDate($date),
            new SebCIOnline_Tag_Transaction_Domestic_Sweden_CurrencyAndAmount(
                $transfer->getAmountMoney()->getCurrency(),
                $transfer->getAmountMoney()->formatAmount(2, ',')
            ),
            new SebCIOnline_Tag_Transaction_Domestic_Sweden_PayerAccount(
                $this->ibanAccountResolver->getAccount($transfer->getMainPayer()->getIban())
            ),
        ];

        if ($this->isPlusGiro($transfer)) {
            $optionalTags = [
                new SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryGiroNumber(
                    $this->getPlusGiroNumber($transfer)
                ),
                new SebCIOnline_Tag_Transaction_Domestic_Sweden_PaymentMethod(self::PLUSGIRO_PAYMENT_METHOD),
            ];
        } else {
            $optionalTags = [
                new SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryAccountAndSwift(
                    $this->getBeneficiaryAccount($transfer),
                    $beneficiaryBankSwift
                ),
                new SebCIOnline_Tag_Transaction_Domestic_Sweden_BeneficiaryName(
                    mb_substr($transfer->getResolvedBeneficiary()->getName(), 0, 35)
                ),
                new SebCIOnline_Tag_Transaction_Domestic_Sweden_PaymentMethod(self::DEFAULT_PAYMENT_METHOD),
            ];
        }

        $optionalTags[] = new SebCIOnline_Tag_Transaction_Domestic_Sweden_PaymentReason(
            mb_substr($transfer->getDetails(), 0, 12)
        );

        $optionalTags[] = new SebCIOnline_Tag_Transaction_Domestic_Sweden_Details(
            mb_substr($transfer->getDetails(), 0, 12)
        );

        if ($transfer->getOcrCode() !== null) {
            $optionalTags[] = new SebCIOnline_Tag_Transaction_Domestic_Sweden_OcrReference(
                $transfer->getOcrCode()
            );
        }

        return array_merge($mandatoryTags, $optionalTags);
    }
}
