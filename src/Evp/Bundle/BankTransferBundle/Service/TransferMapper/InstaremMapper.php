<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Service\AdditionalInformationManager;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use Evp\Bundle\BankTransferBundle\Service\TransferRoutingInformationRetriever;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\AddressProvider;
use Evp\Bundle\ClientBundle\Service\UserInformationProviderInterface;
use Evp\Bundle\UtilBundle\Service\TransliteratorInterface;
use Evp\Component\TextFilter\TextFilter;
use InvalidArgumentException;
use Paysera\Bundle\InstaremBundle\Entity\MatcherData;
use Paysera\Bundle\InstaremBundle\Service\TransactionMapper;
use Paysera\Bundle\TransferValidatorBundle\Service\ValidatorRegistry;
use Paysera\Bundle\InstaremBundle\Service\XlsxWriter;

class InstaremMapper implements TransferExportInterface
{
    const FILE_EXTENSION = '.xlsx';

    public static $currencies = [
        'client_birth_date' => ['USD', 'GBP', 'JPY', 'CZK', 'PLN', 'SEK', 'DKK', 'NOK', 'CAD', 'JPY'],
        'client_address' => ['USD', 'CAD', 'GBP', 'JPY', 'CZK', 'PLN', 'SEK', 'DKK', 'NOK', 'NZD', 'SGD', 'HKD', 'INR', 'TRY', 'CNY', 'EGP', 'MAD'],
        'beneficiary_address' => ['USD', 'CAD', 'GBP', 'AUD', 'JPY', 'NZD', 'CZK', 'NOK', 'SEK', 'DKK', 'HKD', 'PLN', 'SGD', 'EUR', 'INR', 'TRY', 'CNY', 'EGP', 'DZD', 'MAD'],
        'beneficiary_city' => ['USD', 'CAD', 'GBP', 'AUD', 'JPY', 'CZK', 'PLN', 'SEK', 'DKK', 'NOK', 'NZD', 'CNY', 'SGD', 'HKD', 'EGP', 'MAD'],
        'beneficiary_state' => ['USD', 'CAD', 'AUD', 'CNY', 'NOK', 'DKK', 'SEK'],
        'beneficiary_postal_code' => ['USD', 'CAD', 'AUD', 'JPY', 'CZK', 'PLN', 'INR', 'CNY', 'MAD', 'NOK', 'DKK', 'SEK'],
        'beneficiary_bank_name' => ['USD', 'CAD', 'GBP', 'JPY', 'NZD', 'EGP', 'MAD'],
        'beneficiary_name' => ['NOK', 'DKK', 'SEK', 'CZK', 'TRY'],
        'statement_narrative' => ['NOK', 'DKK', 'SEK', 'CZK', 'JPY', 'PLN'],
        'client_country_code' => ['NOK', 'DKK', 'SEK', 'CZK'],
        'client_identification_number' => ['NOK', 'DKK', 'SEK', 'CZK'],
        'beneficiary_country_code' => ['NOK', 'DKK', 'SEK', 'CZK', 'JPY', 'PLN'],
        'beneficiary_bank_code' => ['HKD', 'INR', 'CAD'],
        'beneficiary_bic' => ['SGD', 'JPY', 'NZD', 'EUR', 'CAD', 'CZK', 'PLN', 'SEK', 'DKK', 'NOK'],
    ];

    public static $currenciesFieldConditional = [
        'beneficiary_bank_code' => ['SEK' => 15, 'DKK' => 10, 'NOK' => 7],
    ];

    private $xlsxWriter;
    private $transactionMapper;
    private $transferRoutingInformationRetriever;
    private $additionalInformationManager;
    private $userInformationProvider;
    private $addressProvider;
    private $transliterator;
    private $textFilter;

    private ValidatorRegistry $validatorRegistry;

    public function __construct(
        XlsxWriter $xlsxWriter,
        TransactionMapper $transactionMapper,
        TransferRoutingInformationRetriever $transferRoutingInformationRetriever,
        AdditionalInformationManager $additionalInformationManager,
        UserInformationProviderInterface $userInformationProvider,
        AddressProvider $addressProvider,
        TransliteratorInterface $transliterator,
        TextFilter $textFilter,
        ValidatorRegistry $validatorRegistry
    ) {
        $this->xlsxWriter = $xlsxWriter;
        $this->transactionMapper = $transactionMapper;
        $this->transferRoutingInformationRetriever = $transferRoutingInformationRetriever;
        $this->additionalInformationManager = $additionalInformationManager;
        $this->userInformationProvider = $userInformationProvider;
        $this->addressProvider = $addressProvider;
        $this->transliterator = $transliterator;
        $this->textFilter = $textFilter;
        $this->validatorRegistry = $validatorRegistry;
    }

    /**
     * @param TransferOut[] $transferList
     *
     * @return string
     */
    public function mapFromTransferList(array $transferList): string
    {
        foreach ($transferList as $transferOut) {
            $transaction = $this->transactionMapper->mapTransferToTransaction($transferOut);
            $this->xlsxWriter->addTransaction($transaction);
        }

        return $this->xlsxWriter->getContents();
    }

    public function getFileExtension(): string
    {
        return self::FILE_EXTENSION;
    }

    public function getTransferDetails(TransferOut $transfer): string
    {
        return $transfer->getDetails();
    }

    /**
     * @param TransferOut $transfer
     *
     * @return TransferMapperValidationStatus
     *
     * @throws InvalidArgumentException
     */
    public function validate(TransferOut $transfer): TransferMapperValidationStatus
    {
        if (!($transfer instanceof TransferOutBank)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $client = $transfer->getCreditAccount()->getClient();
        if (!($client instanceof ClientNatural) && !($client instanceof ClientLegal)) {
            throw new InvalidArgumentException(sprintf('Unsupported Client type %s', get_class($client)));
        }

        $currency = $transfer->getAmountCurrency();
        $beneficiary = $transfer->getResolvedBeneficiary();
        $additionalInformation = $this->additionalInformationManager->getAdditionalInformation($beneficiary);
        $country = $this->transferRoutingInformationRetriever->getCountry($transfer);

        if ($additionalInformation === null) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_BENEFICIARY_ADDITIONAL_INFORMATION_NOT_SET
            );
        }

        $matcherData = (
        new MatcherData(
            $currency,
            $country,
            $beneficiary
        ))
            ->setTransfer($transfer)
            ->setAdditionalInformation($additionalInformation)
            ->setFilteredDetails(
                $this->textFilter
                    ->filter(
                        $this->transliterator
                            ->transliterate($transfer->getDetails())
                    )
            )
            ->setUserAddress($this->addressProvider->getAddressByClient($client))
            ->setClient($client)
            ->setUserAddressLine(
                $this->addressProvider
                    ->buildAddressLine(
                        $this->addressProvider->getAddressByClient($client)
                    )
            )
            ->setUserInformation(
                $this->userInformationProvider
                    ->getUserInformation($client->getCovenanteeId())
            )
        ;
        try {
            $matcherData->setTransaction(
                $this->transactionMapper
                    ->mapTransferToTransaction($transfer)
            );
        } catch (InvalidArgumentException $exception) {
            return new TransferMapperValidationStatus($exception->getMessage());
        }

        $registryValidatedResult = $this->validatorRegistry->validate($matcherData);

        if ($registryValidatedResult !== null) {
            return $registryValidatedResult;
        }

        return new TransferMapperValidationStatus();
    }
}
