<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Service\MoreUrlBuilder;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use Evp\Component\Date\DateTimeImmutableProvider;
use Exception;
use InvalidArgumentException;

class LibertyGeMapper implements TransferExportInterface
{
    private const FIELDS_BEGIN = 0;
    private const FIELDS_END = 31;
    private const FIELDS_TO_SKIP = [5, 24, 25, 26];

    private const FIELD_PREFIX = ':';
    private const FIELD_SUFFIX = '::';
    private const FIELD_SEPARATOR = "\r\n";

    private const PAYMENT_TYPE_DOMESTIC = '**M3';
    private const PAYMENT_TYPE_INTERNATIONAL = '**M4';

    private const PAYMENT_PRIORITY_ECONOMIC = 'N';
    private const PAYMENT_PRIORITY_STANDARD = 'U';
    private const PAYMENT_PRIORITY_EXPRESS = 'X';
    private const COMMISSION_TYPE_PAYER = 'OUR';
    private const COMMISSION_TYPE_RECEIVER = 'BEN';

    private AccountInfoResolver $accountInfoResolver;
    private MoreUrlBuilder $moreUrlBuilder;
    private DateTimeImmutableProvider $dateTimeProvider;
    private string $fileExtension;
    private string $countryCode;
    private string $country;

    private string $currency;
    /**
     * @var array
     */
    protected array $transferDetailsCache;

    public function __construct(
        AccountInfoResolver $accountInfoResolver,
        MoreUrlBuilder $moreUrlBuilder,
        DateTimeImmutableProvider $dateTimeProvider,
        $country,
        $countryCode,
        $currency,
        $fileExtension
    ) {
        $this->moreUrlBuilder = $moreUrlBuilder;
        $this->accountInfoResolver = $accountInfoResolver;
        $this->dateTimeProvider = $dateTimeProvider;
        $this->country = $country;
        $this->countryCode = $countryCode;
        $this->currency = $currency;
        $this->fileExtension = $fileExtension;
    }

    public function getFileExtension(): string
    {
        return $this->fileExtension;
    }

    /**
     * @param TransferOut[] $transferList
     * @return string
     */
    public function mapFromTransferList(array $transferList): string
    {
        $buffer = [];
        foreach ($transferList as $transfer) {
            $buffer[] = $this->mapTransferToRow($transfer);
        }

        $content = implode('', $buffer);

        return $content;
    }

    public function getTransferDetails(TransferOut $transfer): string
    {
        $cacheKey = $transfer->getId();
        if (!isset($this->transferDetailsCache[$cacheKey])) {
            $this->transferDetailsCache[$cacheKey] = mb_substr($this->moreUrlBuilder->getUrl($transfer), 0, 140);
        }
        return $this->transferDetailsCache[$cacheKey];
    }

    public function validate(TransferOut $transfer): TransferMapperValidationStatus
    {
        if ($transfer->getAmountMoney()->getCurrency() !== $this->currency) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_COUNTRY_SPECIFIC_BAD_DATA
            );
        }

        $beneficiaryInfo = $this->accountInfoResolver->getAccountInfoForTransferParty($transfer->getBeneficiary());
        if (strtoupper($beneficiaryInfo->getCountryCode()) !== strtoupper($this->countryCode)) { // TODO: remove strtoupper
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_COUNTRY_SPECIFIC_BAD_DATA
            );
        }

        try {
            $beneficiaryInfo->checkBankSwift();
            $beneficiaryInfo->checkIban();
        } catch (Exception $e) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_BENEFICIARY_IBAN_NOT_SET
            );
        }

        if (!$transfer->getDetails()) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_DETAILS_NOT_SET
            );
        }

        return new TransferMapperValidationStatus();
    }

    private function mapTransferToRow(TransferOut $transfer): string
    {
        if (!($transfer instanceof TransferOutBank)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }
        $mainPayerInfo = $this->accountInfoResolver->getAccountInfoForTransferParty($transfer->getMainPayer());
        $beneficiaryInfo = $this->accountInfoResolver->getAccountInfoForTransferParty($transfer->getBeneficiary(), $transfer);

        $data = [
            0 => self::PAYMENT_TYPE_DOMESTIC,
            1 => (string)$transfer->getId(),
            2 => $transfer->getAmountMoney()->formatAmount(2, '.'),
            3 => $transfer->getAmountMoney()->getCurrency(),
            4 => $transfer->getDetails(),
            6 => $mainPayerInfo->getIban() . '_' . $transfer->getAmountMoney()->getCurrency(),
            7 => $beneficiaryInfo->getIban(),
            8 => $transfer->getResolvedBeneficiary()->getName(),
            9 => $this->country,
            17 => self::PAYMENT_PRIORITY_STANDARD,
            18 => self::COMMISSION_TYPE_PAYER,
            19 => $transfer->getDetails(),
            20 => $this->countryCode,
            27 => $this->dateTimeProvider->getCurrent()->format('Ymd'),
        ];

        $text = '';
        for ($fieldNumber = self::FIELDS_BEGIN; $fieldNumber <= self::FIELDS_END; $fieldNumber++) {
            if (in_array($fieldNumber, self::FIELDS_TO_SKIP)) {
                continue;
            }

            $fieldValue = (isset($data[$fieldNumber]) ? $this->escape($data[$fieldNumber]) : '');
            $text .= self::FIELD_PREFIX . $fieldNumber . self::FIELD_SUFFIX . $fieldValue . self::FIELD_SEPARATOR;
        }

        return $text;
    }

    private function escape(string $string): string
    {
        return str_replace([
            "\r\n", "\r", "\n", '::',
        ], ' ', $string);
    }
}
