<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAdditionalInformation;
use Evp\Bundle\BankTransferBundle\Exception\MapperInvalidContentException;
use Evp\Bundle\BankTransferBundle\Service\AdditionalInformationManager;
use Evp\Bundle\BankTransferBundle\Service\EvpAccountResolver;
use InvalidArgumentException;
use DateTime;
use Exception;
use LitasEsis_Mokesis_Row_Transaction;
use LitasEsis_Mokesis_Writer;
use LitasEsis_Mokesis_Reader;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankBundle\Exception\BicBankResolverException;
use Evp\Bundle\BankBundle\Service\BicBankResolverInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountAlias;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequest;
use Evp\Bundle\BankTransferBundle\Exception\TransferRequestMapException;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferImportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferRequestFactoryInterface;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Component\Money\Money;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Psr\Log\LoggerInterface;

class LitasEsisMokesisMapper implements TransferImportInterface, TransferExportInterface
{
    const FILE_EXTENSION = '.mokesis';

    private $transferRequestFactory;
    private $bicBankResolver;
    private $logger;
    private EvpAccountResolver $evpAccountResolver;

    private AdditionalInformationManager $additionalInformationManager;

    public function __construct(
        TransferRequestFactoryInterface $transferRequestFactory,
        BicBankResolverInterface $bicBankResolver,
        LoggerInterface $logger,
        EvpAccountResolver $evpAccountResolver,
        AdditionalInformationManager $additionalInformationManager
    ) {
        $this->transferRequestFactory = $transferRequestFactory;
        $this->bicBankResolver = $bicBankResolver;
        $this->logger = $logger;
        $this->evpAccountResolver = $evpAccountResolver;
        $this->additionalInformationManager = $additionalInformationManager;
    }

    /**
     * @param string $content
     * @param Client|null $client
     * @return TransferRequest
     * @throws \Exception
     * @throws TransferRequestMapException
     */
    public function mapToTransferRequest($content, Client $client = null)
    {
        $this->logger->debug(__METHOD__, [$client, $content]);
        $rowCount = 0;
        $contentEncoding = mb_detect_encoding($content) === 'UTF-8' ? 'UTF-8' : null;
        $reader = new LitasEsis_Mokesis_Reader(null, 'UTF-8//TRANSLIT//IGNORE', $contentEncoding);
        $reader->setContent($content);

        if (!$reader->isValid()) {
            throw new MapperInvalidContentException();
        }

        $request = $this->transferRequestFactory->getTransferRequest();
        foreach ($reader as $row) {
            $rowCount++;

            if (empty(trim($row->__toString()))) {
                continue;
            }

            try {
                $transfer = $this->mapRowToTransfer($row);
                if ($client !== null) {
                    $transfer->setClient($client);
                }
                $request->addTransfer($transfer);
            } catch (Exception $exception) {
                throw new TransferRequestMapException(
                    $exception->getMessage(),
                    $rowCount,
                    $exception->getCode(),
                    $exception
                );
            }
        }

        return $request;
    }

    /**
     * @param TransferOut[] $transferList
     * @return string
     * @throws \Exception
     */
    public function mapFromTransferList(array $transferList)
    {
        $this->logger->debug(__METHOD__, [$transferList]);

        $writer = new LitasEsis_Mokesis_Writer();
        $rowList = [];
        foreach ($transferList as $transfer) {
            $row = new LitasEsis_Mokesis_Row_Transaction();
            $this->mapTransferToRow($transfer, $row);
            $rowList[] = $row;
        }
        $writer->load($rowList);
        return $writer->getContent();
    }

    /**
     * @param LitasEsis_Mokesis_Row_Transaction $transaction
     * @return Transfer
     */
    protected function mapRowToTransfer(LitasEsis_Mokesis_Row_Transaction $transaction)
    {
        // Beneficiary
        $beneficiaryIban = trim(preg_replace("/\W/", "", $transaction->beneficiaryIban));
        if ($this->isEvpAccount($beneficiaryIban)) {
            // Transfer to EVP account
            $transfer = new TransferInternal();

            // Set beneficiary
            $transfer->setBeneficiary(
                new PartyAccount(
                    $beneficiaryIban,
                    $transaction->beneficiaryName,
                    $transaction->beneficiaryCode ?: null
                )
            );
        } elseif ($this->isAlias($beneficiaryIban)) {
            $transfer = new TransferInternal();
            $transfer->setBeneficiary(new PartyAccountAlias($beneficiaryIban));
        } else {
            // Transfer to a real bank
            $transfer = new TransferOutBank();

            $transferBeneficiary = new PartyIban(
                $beneficiaryIban,
                $transaction->beneficiaryName,
                $transaction->beneficiaryCode ?: null
            );

            $transferBeneficiary->setBeneficiaryAddress($transaction->beneficiaryAddress);

            $transfer
                ->setBeneficiary($transferBeneficiary)
                ->setPurposeCode($transaction->purposeCode)
            ;

            $partyAdditionalInformation = new PartyAdditionalInformation();
            $partyAdditionalInformation
                ->setCity($transaction->beneficiaryCity)
                ->setParty($transferBeneficiary)
                ->setCountry($transaction->beneficiaryCountry)
                ->setPostalCode($transaction->beneficiaryPostalCode)
                ->setType($transaction->clientType)
            ;
            $this->additionalInformationManager->persistAdditionalInformation($partyAdditionalInformation);
        }

        // Payer
        if ($transaction->payerIban) {
            $transfer->setPayer($this->getPayerParty($transaction));
        }

        // Final beneficiary
        if ($transaction->finalBeneficiaryIban && $transaction->finalBeneficiaryName) {
            if ($this->isEvpAccount($transaction->finalBeneficiaryIban)) {
                $transfer->setFinalBeneficiary(
                    new PartyAccount(
                        $transaction->finalBeneficiaryIban,
                        $transaction->finalBeneficiaryName,
                        $transaction->finalBeneficiaryCode ?: null
                    )
                );
            } else {
                $transfer->setFinalBeneficiary(
                    new PartyIban(
                        $transaction->finalBeneficiaryIban,
                        $transaction->finalBeneficiaryName,
                        $transaction->finalBeneficiaryCode ?: null
                    )
                );
            }
        }

        $transfer->setNumber($transaction->number);
        if ($transaction->date) {
            $transfer->setDate(DateTime::createFromFormat('Ymd', $transaction->date));
        }

        $operationDate = $transaction->operationDate
            ? DateTime::createFromFormat('Ymd', $transaction->operationDate)
            : false;
        if (!$operationDate) {
            $operationDate = new DateTime();
        }
        $transfer->setOperationDate($operationDate->setTime(0, 0, 0));

        if ($transaction->priority == 2) {
            $transfer->setPriority(TransferOutBank::PRIORITY_URGENT);
            if ($transfer instanceof TransferOutBank) {
                $transfer->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_URGENT);
            }
        } else {
            $transfer->setPriority(TransferOutBank::PRIORITY_NORMAL);
            if ($transfer instanceof TransferOutBank) {
                $transfer->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_NOT_URGENT);
            }
        }

        if (!empty($transaction->beneficiaryBic) && $transfer instanceof TransferOutBank) {
            if (is_numeric($transaction->beneficiaryBic)) {
                try {
                    $bank = $this->bicBankResolver->getBank($transaction->beneficiaryBic);
                    // Check if it's possible to get swift, otherwise - use bic
                    if ($bank->getSwift() !== null) {
                        $beneficiaryBankSwift = $bank->getSwift();
                    } else {
                        $beneficiaryBankSwift = $bank->getCode();
                    }
                } catch (BicBankResolverException $exception) {
                    // Provided code is numeric, but we have no bank with this code, throw warning
                    $beneficiaryBankSwift = null;

                    $this->logger->debug($transaction->beneficiaryBic);
                }
            } else {
                // Probably swift
                $beneficiaryBankSwift = $transaction->beneficiaryBic;
            }

            $beneficiary = $transfer->getResolvedBeneficiary();
            if ($beneficiary instanceof PartyBank) {
                $beneficiary->setBic($beneficiaryBankSwift);
            }
        }

        if ($transaction->referenceToBeneficiary) {
            $transfer->setReferenceToBeneficiary($transaction->referenceToBeneficiary);
        }

        if ($transaction->referenceToPayer) {
            $transfer->setReferenceToPayer($transaction->referenceToPayer);
        }

        $amount = Money::createFromNoDelimiterAmount($transaction->amount, $transaction->amountCurrency, 2);
        $transfer->setAmountMoney($amount);
        if ($transaction->referenceNumber) {
            $transfer->setReferenceNumber($transaction->referenceNumber);
        }
        $transfer->setDetails($transaction->details);
        if ($transaction->archiveNumber) {
            $transfer->setArchiveNumber($transaction->archiveNumber);
        }

        return $transfer;
    }

    /**
     * @param TransferOut $transfer
     * @param LitasEsis_Mokesis_Row_Transaction $transaction
     * @throws InvalidArgumentException
     */
    protected function mapTransferToRow(TransferOut $transfer, LitasEsis_Mokesis_Row_Transaction $transaction)
    {
        if (!$transfer instanceof TransferOutBank) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $this->setParties($transfer, $transaction);
        $transaction->number = $transfer->getNumber();
        if ($transfer->getDate()) {
            $transaction->date = $transfer->getDate()->format('Ymd');
        }
        if ($transfer->getOperationDate()) {
            $transaction->operationDate = $transfer->getOperationDate()->format('Ymd');
        }
        $transaction->priority = $transfer->getPriority() == TransferOut::PRIORITY_URGENT ? 2 : 1;

        $transaction->referenceToBeneficiary = $transfer->getReferenceToBeneficiary();
        $transaction->beneficiaryBic = $transfer->getResolvedBeneficiary()->getBic();
        $transaction->referenceToPayer = $transfer->getReferenceToPayer();

        $amount = $transfer->getAmountMoney();
        $transaction->amount = $amount->formatAmount(2, '');
        $transaction->amountCurrency = $amount->getCurrency();

        $transaction->referenceNumber = $transfer->getReferenceNumber();
        $transaction->details = $transfer->getDetails();
        $transaction->archiveNumber = $transfer->getArchiveNumber();
    }

    /**
     * Validates and sets parties from transfer to transaction row
     *
     * @param TransferOut $transfer
     * @param LitasEsis_Mokesis_Row_Transaction $transaction
     * @throws \Exception
     */
    private function setParties(TransferOut $transfer, LitasEsis_Mokesis_Row_Transaction $transaction)
    {
        if (
            $transfer->getPayer() === null
            || $transfer->getBeneficiary() === null
            || !$transfer->getPayer() instanceof PartyIban
            || !$transfer->getBeneficiary() instanceof PartyIban
        ) {
            throw new Exception('Transfer must have payer and beneficiary as IBAN parties');
        }

        $transaction->payerIban = $transfer->getPayer()->getIban();
        $transaction->payerName = $transfer->getPayer()->getName();
        $transaction->payerCode = $transfer->getPayer()->getCode();

        $transaction->beneficiaryIban = $transfer->getBeneficiary()->getIban();
        $transaction->beneficiaryName = $transfer->getBeneficiary()->getName();
        $transaction->beneficiaryCode = $transfer->getBeneficiary()->getCode();

        if ($transfer->getFinalBeneficiary() && $transfer->getFinalBeneficiary() instanceof PartyIban) {
            $transaction->finalBeneficiaryIban = $transfer->getFinalBeneficiary()->getIban();
            $transaction->finalBeneficiaryName = $transfer->getFinalBeneficiary()->getName();
            $transaction->finalBeneficiaryCode = $transfer->getFinalBeneficiary()->getCode();
        }
    }

    public function getTransferDetails(TransferOut $transfer): string
    {
        return $transfer->getDetails();
    }

    public function getFileExtension(): string
    {
        return self::FILE_EXTENSION;
    }

    public function validate(TransferOut $transfer): TransferMapperValidationStatus
    {
        if (!$transfer->getDetails()) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_DETAILS_NOT_SET
            );
        }

        return new TransferMapperValidationStatus();
    }

    private function isEvpAccount($account): bool
    {
        return stripos($account, 'EVP') === 0;
    }

    /**
     * Checks if string can be alias (Email Or International Phone Number)
     *
     * @param string $account
     * @return bool
     */
    private function isAlias($account): bool
    {
        return
            filter_var($account, FILTER_VALIDATE_EMAIL)
            || preg_match(
                '#^\+(9[976]\d|8[987530]\d|6[987]\d|5[90]\d|42\d|3[875]\d|2[********]\d|9[8543210]|8[6421]|6[6543210]|5[********]|4[*********]|3[9643210]|2[70]|7|1)\d{1,14}$#',
                $account
            )
        ;
    }

    private function getPayerParty(LitasEsis_Mokesis_Row_Transaction $transaction): Party
    {
        if ($this->evpAccountResolver->isEvpAccount($transaction->payerIban)) {
            try {
                $account = $this->evpAccountResolver->resolveFromEvpNumber($transaction->payerIban);
                return new PartyAccount(
                    $account->getNumber(),
                    $account->getClient()->getDisplayName(),
                    $transaction->payerCode ?? $account->getClient()->getCode()
                );
            } catch (InvalidArgumentException $exception) {
                return new PartyAccount(
                    $transaction->payerIban,
                    $transaction->payerName,
                    $transaction->payerCode ?? null
                );
            }
        }

        try {
            $account = $this->evpAccountResolver->resolveFromIban($transaction->payerIban);
            return new PartyIban(
                $account->getIban(),
                $account->getClient()->getDisplayName(),
                $transaction->payerCode ?? $account->getClient()->getCode()
            );
        } catch (InvalidArgumentException $exception) {
            return new PartyIban(
                $transaction->payerIban,
                $transaction->payerName,
                $transaction->payerCode ?? null
            );
        }
    }
}
