<?php

namespace Evp\Bundle\BankTransferBundle\Service\TransferMapper;

use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Service\MoreUrlBuilder;
use Evp\Bundle\BankTransferBundle\Service\TransferDetailsFormatter;
use Evp\Bundle\BankTransferBundle\Service\TransferExportInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
use <PERSON><PERSON><PERSON>\DrupalTransliteratorBundle\Transliterator;

class ManualBaseMapper implements TransferExportInterface
{
    protected $mustValidateSwiftAndIban = true;
    protected $mustValidateBankCode = false;

    /**
     * @var \Evp\Bundle\BankTransferBundle\Service\TransferDetailsFormatter
     */
    protected $transferDetailsFormatter;

    /**
     * @var AccountInfoResolver
     */
    protected $accountInfoResolver;

    /**
     * @var MoreUrlBuilder
     */
    protected $moreUrlBuilder;

    /**
     * @var string
     */
    protected $countryCode;

    /**
     * @var string
     */
    protected $currency;

    /**
     * @param AccountInfoResolver $accountInfoResolver
     * @param MoreUrlBuilder $moreUrlBuilder
     * @param string $countryCode
     * @param string $currency
     * @param TransferDetailsFormatter $transferDetailsFormatter
     */
    public function __construct(
        AccountInfoResolver $accountInfoResolver,
        MoreUrlBuilder $moreUrlBuilder,
        $countryCode,
        $currency,
        TransferDetailsFormatter $transferDetailsFormatter
    ) {
        $this->accountInfoResolver = $accountInfoResolver;
        $this->moreUrlBuilder = $moreUrlBuilder;
        $this->countryCode = $countryCode;
        $this->currency = $currency;
        $this->transferDetailsFormatter = $transferDetailsFormatter;
    }

    /**
     * Map list of Transfer to string content
     *
     * @param TransferOut[] $transferList
     * @return string
     */
    public function mapFromTransferList(array $transferList)
    {
        $buffer = [];
        foreach ($transferList as $transfer) {
            $buffer[] = $this->mapTransferToRow($transfer);
        }

        return implode('', $buffer);
    }

    /**
     * Gets transfer details formatted string
     *
     * @param TransferOut $transfer
     * @return string
     */
    public function getTransferDetails(TransferOut $transfer)
    {
        return $this->transferDetailsFormatter->formatText(
            Transliterator::transliterate($transfer->getDetails()),
            '',
            70
        );
    }

    /**
     * @return string Extension of generated file
     */
    public function getFileExtension()
    {
        return '.html';
    }

    /**
     * Validates transfer
     *
     * @param \Evp\Bundle\BankTransferBundle\Entity\TransferOut $transfer
     * @return \Evp\Bundle\BankTransferBundle\Service\TransferMapperValidationStatus;
     */
    public function validate(TransferOut $transfer)
    {
        if (!in_array($transfer->getAmountMoney()->getCurrency(), [$this->currency, 'USD'])) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_COUNTRY_SPECIFIC_BAD_DATA
            );
        }

        $beneficiaryInfo = $this->accountInfoResolver->getAccountInfoForTransferParty(
            $transfer->getResolvedBeneficiary(),
            $transfer
        );

        if (!in_array(strtoupper($beneficiaryInfo->getCountryCode()), [strtoupper($this->countryCode), 'US'])) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_COUNTRY_SPECIFIC_BAD_DATA
            );
        }

        if ($this->mustValidateSwiftAndIban && $transfer->getAmountMoney()->getCurrency() !== 'USD') {
            try {
                $beneficiaryInfo->checkBankSwift();
                $beneficiaryInfo->checkIban();
            } catch (\Exception $e) {
                return new TransferMapperValidationStatus(
                    TransferFailureStatus::CODE_BENEFICIARY_IBAN_NOT_SET
                );
            }
        }

        if ($this->mustValidateBankCode) {
            try {
                $beneficiaryInfo->checkBankCode();
            } catch (\Exception $e) {
                return new TransferMapperValidationStatus(
                    TransferFailureStatus::CODE_BENEFICIARY_BANK_CODE_NOT_SET
                );
            }
        }

        if (!$transfer->getDetails()) {
            return new TransferMapperValidationStatus(
                TransferFailureStatus::CODE_DETAILS_NOT_SET
            );
        }

        return new TransferMapperValidationStatus();
    }

    /**
     * Returns beneficiary account number
     *
     * @param TransferOutBank $transfer
     * @return string
     */
    protected function getBeneficiaryAccountNumber(TransferOutBank $transfer)
    {
        $accountInfo = $this->accountInfoResolver->getAccountInfoForTransferParty(
            $transfer->getResolvedBeneficiary(),
            $transfer
        );

        return $accountInfo->getBban();
    }

    /**
     * Escapes the given string
     *
     * @param string $param
     * @return string
     */
    protected function escape($param)
    {
        return htmlentities($param, ENT_QUOTES, 'UTF-8');
    }
}
