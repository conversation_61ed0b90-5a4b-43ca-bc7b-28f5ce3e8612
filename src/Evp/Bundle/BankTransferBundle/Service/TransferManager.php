<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManagerInterface;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvert;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInBase;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequest;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequestConvertCurrency;
use Evp\Bundle\BankTransferBundle\Event\TransferRequestEvent;
use Evp\Bundle\BankTransferBundle\Repository\TransferInRepository;
use Evp\Bundle\BankTransferBundle\Repository\TransferOutRepository;
use Evp\Bundle\BankTransferBundle\TransferRequestEvents;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ProtectedTransferBundle\Service\TransferPasswordManager;
use Evp\Component\GatewayCommon\BankTransfer\Entity\TransfersFilter;
use Paysera\Bundle\RandomHashBundle\Service\HashGeneratorInterface;
use Psr\Log\LoggerInterface;
use Exception;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class TransferManager implements TransferManagerInterface
{
    private $entityManager;
    private $transferStatusManager;
    private $transferInProcessor;
    private $transferInternalProcessor;
    private $transferOutProcessor;
    private $currencyConvertProcessor;
    private $transferOutRepository;
    private $transferInRepository;
    private $transferPasswordManager;
    private $fundsSourceManager;
    private $transferBeneficiaryEmailManager;
    private $hashGenerator;
    private $dispatcher;
    private $logger;
    private $permissionManager;
    private SlaveConnectionTransferCountriesProvider $transferCountriesProvider;

    public function __construct(
        EntityManagerInterface $entityManager,
        TransferStatusManager $transferStatusManager,
        TransferProcessorInterface $transferInProcessor,
        TransferProcessorInterface $transferInternalProcessor,
        TransferProcessorInterface $transferOutProcessor,
        TransferProcessorInterface $currencyConvertProcessor,
        TransferOutRepository $transferOutRepository,
        TransferInRepository $transferInRepository,
        TransferPasswordManager $transferPasswordManager,
        FundsSourceManager $fundsSourceManager,
        TransferBeneficiaryEmailManager $transferBeneficiaryEmailManager,
        HashGeneratorInterface $hashGenerator,
        EventDispatcherInterface $dispatcher,
        PermissionManagerInterface $permissionManager,
        SlaveConnectionTransferCountriesProvider $transferCountriesProvider,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->transferStatusManager = $transferStatusManager;
        $this->transferInProcessor = $transferInProcessor;
        $this->transferInternalProcessor = $transferInternalProcessor;
        $this->transferOutProcessor = $transferOutProcessor;
        $this->currencyConvertProcessor = $currencyConvertProcessor;
        $this->transferOutRepository = $transferOutRepository;
        $this->transferInRepository = $transferInRepository;
        $this->transferPasswordManager = $transferPasswordManager;
        $this->fundsSourceManager = $fundsSourceManager;
        $this->transferBeneficiaryEmailManager = $transferBeneficiaryEmailManager;
        $this->hashGenerator = $hashGenerator;
        $this->dispatcher = $dispatcher;
        $this->permissionManager = $permissionManager;
        $this->transferCountriesProvider = $transferCountriesProvider;
        $this->logger = $logger;
    }

    public function registerTransferRequest(TransferRequest $request)
    {
        $this->logger->debug(__METHOD__, [$request]);

        foreach ($request->getTransfers() as $index => $transfer) {
            if (!$transfer->isFailed()) {
                $request->getTransfers()->set($index, $this->prepareTransfer($transfer));
            }
        }

        $this->dispatcher->dispatch(TransferRequestEvents::ON_REGISTER, new TransferRequestEvent($request));

        $this->entityManager->persist($request);
    }

    public function setTransferRequestConvertCurrency(TransferRequest $transferRequest, string $currency): void
    {
        $transferRequestConvertCurrency = $transferRequest->getConvertToCurrency();

        if ($transferRequestConvertCurrency === null) {
            $transferRequestConvertCurrency = new TransferRequestConvertCurrency($transferRequest, $currency);
            $this->entityManager->persist($transferRequestConvertCurrency);
            $transferRequest->setConvertToCurrency($transferRequestConvertCurrency);
        } else {
            $transferRequestConvertCurrency->setCurrency($currency);
        }
    }

    /**
     * @param Transfer $transfer
     * @return Transfer
     * @throws \Exception
     */
    public function registerTransfer(Transfer $transfer)
    {
        $this->logger->debug(
            __METHOD__,
            [
                'transfer_id' => $transfer->getId(),
                'trace' => json_encode(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)),
            ]
        );

        $transfer->clearTransferStatuses();
        $this->transferStatusManager->markAsNew($transfer);

        $allFiltersPassed = false;
        $returnTransfer = $transfer;

        $this->ensurePropertiesPersistedInCaseSetBySoap($transfer);

        if ($transfer instanceof TransferCurrencyConvert) {
            $allFiltersPassed = $this->currencyConvertProcessor->processTransfer($transfer);
        } elseif ($transfer instanceof TransferIn) {
            $allFiltersPassed = $this->transferInProcessor->processTransfer($transfer);
        } elseif ($transfer instanceof TransferInternal) {
            $allFiltersPassed = $this->transferInternalProcessor->processTransfer($transfer);
        } elseif ($transfer instanceof TransferOut) {
            $allFiltersPassed = $this->transferOutProcessor->processTransfer($transfer);
        }

        if ($transfer->getTransferThatReplacedThisTransfer() !== null) {
            $newTransfer = $this->registerTransfer($transfer->getTransferThatReplacedThisTransfer());
            if ($newTransfer->isFailed()) {
                $this->transferStatusManager->markAsFailed($transfer, $newTransfer->getFailureStatus());
            } else {
                $returnTransfer = $newTransfer;
                $this->logger->debug(sprintf('Transfer is now %s.', get_class($transfer)));
            }
            $this->removeOrphanProperties($transfer);
        } elseif ($allFiltersPassed === false && !$returnTransfer->isFailed()) {
            throw new Exception(sprintf('Transfer (%s) is not failed after filter steps failure', $transfer->getId()));
        }

        if ($returnTransfer->getHash() === null) {
            $returnTransfer->setHash($this->hashGenerator->generateHash());
        }

        return $returnTransfer;
    }

    /**
     * @param Transfer $transfer
     * @return Transfer
     */
    public function prepareTransfer(Transfer $transfer)
    {
        $this->logger->debug(__METHOD__, ['transfer_id' => $transfer->getId()]);

        $returnTransfer = $this->registerTransfer($transfer);
        if ($returnTransfer->isNew()) {
            $this->transferStatusManager->markAsPrepared($returnTransfer);
        }
        return $returnTransfer;
    }

    /**
     * @param Transfer $parent
     * @param Transfer $child
     * @return Transfer
     */
    public function saveParentsChild(Transfer $parent, Transfer $child)
    {
        $parent->getTransferRequest()->addTransfer($child);
        $child = $this->prepareTransfer($child);
        return $child;
    }

    public function getClientAccountsTransfersCountries(Client $client, TransfersFilter $filter): array
    {
        $countries = [];

        $accounts = $client->getAccounts();
        foreach ($accounts as $account) {
            $countries = array_merge(
                $countries,
                $this->transferCountriesProvider->findClientCountriesListByAccount($account, $filter)
            );

            if (
                $filter->getDirection() === TransfersFilter::DIRECTION_IN
                || $filter->getDirection() === TransfersFilter::DIRECTION_ANY
            ) {
                $countries = array_merge(
                    $countries,
                    $this->transferInRepository->findPartyAccountCountriesListByAccount($account, $filter),
                    $this->transferInRepository->findPartyIbanCountriesListByAccount($account, $filter)
                );
            }

            if (
                $filter->getDirection() === TransfersFilter::DIRECTION_OUT
                || $filter->getDirection() === TransfersFilter::DIRECTION_ANY
            ) {
                $countries = array_merge(
                    $countries,
                    $this->transferOutRepository->findPartyAccountCountriesListByAccount($account, $filter),
                    $this->transferOutRepository->findPartyIbanCountriesListByAccount($account, $filter)
                );
            }
        }

        return array_unique($countries);
    }

    /**
     * @see TransferInternal::$transferPassword
     *
     * @param Transfer $transfer
     */
    private function ensurePropertiesPersistedInCaseSetBySoap(Transfer $transfer)
    {
        if ($transfer instanceof TransferInternal) {
            $transferPassword = $transfer->getTransferPassword();
            if ($transferPassword !== null) {
                $transferPassword->setTransfer($transfer);
                $this->transferPasswordManager->persistTransferPassword($transferPassword);
            }
        }

        if ($transfer instanceof TransferInBase) {
            $fundsSource = $transfer->getFundsSource();
            if ($fundsSource !== null) {
                $fundsSource->setTransfer($transfer);
                $this->fundsSourceManager->persistFundsSource($fundsSource);
            }
        }

        $beneficiaryEmail = $transfer->getBeneficiaryEmail();
        if ($beneficiaryEmail !== null) {
            $beneficiaryEmail->setTransfer($transfer);
            $this->transferBeneficiaryEmailManager->persistTransferBeneficiaryEmail($beneficiaryEmail);
        }
    }

    private function removeOrphanProperties(TransferOut $transfer)
    {
        $beneficiaryEmail = $this->transferBeneficiaryEmailManager->findTransferBeneficiaryEmail($transfer);
        if ($beneficiaryEmail !== null) {
            $this->transferBeneficiaryEmailManager->removeTransferBeneficiaryEmail($beneficiaryEmail);
        }
    }

    public function setTransferPermissions(Transfer $transfer, Client $client = null)
    {
        $transfer->setHasEditPermission(
            $client !== null
            && $this->permissionManager->hasTransferEditPermission($client, $transfer)
        );
        $transfer->setHasSignPermission(
            $client !== null
            && $this->permissionManager->hasTransferSignPermission($client, $transfer)
        );
        $transfer->setHasCancelPermission(
            $client !== null
            && $this->permissionManager->hasTransferCancelPermission($client, $transfer)
        );
    }
}
