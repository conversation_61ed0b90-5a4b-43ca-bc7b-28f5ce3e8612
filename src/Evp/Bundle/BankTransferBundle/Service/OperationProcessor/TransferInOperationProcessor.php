<?php

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Exception\OperationProcessorException;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\AccountingBundle\Service\PartnerAccountingProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Exception;
use InvalidArgumentException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * Transfer operation processor
 */
class TransferInOperationProcessor extends BaseOperationProcessor
{
    private InternalOperationHandler $internalOperationHandler;
    private ExternalOperationHandler $externalOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;
    private PartnerAccountingProcessor $partnerAccountingProcessor;

    public function __construct(
        InternalOperationHandler $internalOperationHandler,
        ExternalOperationHandler $externalOperationHandler,
        AccountingExceptionHandler $accountingExceptionHandler,
        PartnerAccountingProcessor $partnerAccountingProcessor
    ) {
        $this->internalOperationHandler = $internalOperationHandler;
        $this->externalOperationHandler = $externalOperationHandler;
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->partnerAccountingProcessor = $partnerAccountingProcessor;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param Operation $operation
     *
     * @throws InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof TransferInOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    TransferInOperation::class
                )
            );
        }

        $transfer = $operation->getTransfer();
        if ($transfer->getAmountMoney()->isZero()) {
            $this->saveAsSkipped($operation);
        } elseif ($transfer instanceof TransferIn) {
            $this->processTransferIn($operation, $transfer);
        } elseif ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        try {
            $this->partnerAccountingProcessor->processOperation($operation);
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    /**
     * Process TransferIn
     *
     * @param TransferInOperation $operation
     * @param TransferIn $transfer
     * @throws OperationProcessorException
     */
    protected function processTransferIn(TransferInOperation $operation, TransferIn $transfer)
    {
        $details = $transfer->getDetails();
        $debitAccount = $transfer->getDebitAccount();

        if ($details === null || $details === '') {
            $details = $transfer->getReferenceNumber();
        }

        $this->externalOperationHandler->handleIn(
            $operation,
            $debitAccount,
            $transfer->getAmountMoney(),
            $details
        );
    }

    /**
     * Process TransferInternal
     *
     * @param TransferInOperation $operation
     * @param TransferInternal $transfer
     */
    protected function processTransferInternal(TransferInOperation $operation, TransferInternal $transfer)
    {
        $this->internalOperationHandler->handleIn(
            $operation,
            $transfer->getCreditAccount(),
            $transfer->getDebitAccount(),
            $transfer->getAmountMoney(),
            $transfer->getDetails()
        );
    }
}
