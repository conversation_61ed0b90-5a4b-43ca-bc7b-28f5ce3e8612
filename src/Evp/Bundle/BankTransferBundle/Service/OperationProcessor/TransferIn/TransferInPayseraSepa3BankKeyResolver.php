<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferIn;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\HasTransactionInTransferEntryMatcher;
use DateTime;

class TransferInPayseraSepa3BankKeyResolver implements TransferInBankKeyResolverInterface
{
    private HasTransactionInTransferEntryMatcher $transactionInTransferEntryMatcher;
    private string $accountNumber;
    private string $bankKey;
    private string $sepa3AccountShutdownDate;
    private string $sepa3AccountStartDate;

    public function __construct(
        HasTransactionInTransferEntryMatcher $transactionInTransferEntryMatcher,
        string $accountNumber,
        string $bankKey,
        string $sepa3AccountShutdownDate,
        string $sepa3AccountStartDate
    ) {
        $this->transactionInTransferEntryMatcher = $transactionInTransferEntryMatcher;
        $this->accountNumber = $accountNumber;
        $this->bankKey = $bankKey;
        $this->sepa3AccountShutdownDate = $sepa3AccountShutdownDate;
        $this->sepa3AccountStartDate = $sepa3AccountStartDate;
    }

    public function isApplicable(Transfer $transfer): bool
    {
        return $transfer instanceof TransferIn;
    }

    public function resolve(TransferIn $transfer): ?string
    {
        if (
            $transfer->getDebitAccount() !== null
            && $transfer->getDebitAccount()->getNumber() === $this->accountNumber
            && $this->transactionInTransferEntryMatcher->matches($transfer)
            && $transfer->getOperationDate() < DateTime::createFromFormat('Y-m-d H:i:s', $this->sepa3AccountShutdownDate)
            && $transfer->getOperationDate() >= DateTime::createFromFormat('Y-m-d H:i:s', $this->sepa3AccountStartDate)
        ) {
            return $this->bankKey;
        }

        return null;
    }
}
