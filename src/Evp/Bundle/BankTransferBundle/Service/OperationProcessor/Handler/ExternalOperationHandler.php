<?php

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler;

use DateTimeInterface;
use Evp\Bundle\AccountingBundle\Entity\AccountingOperation;
use Evp\Bundle\AccountingBundle\Exception\OperationProcessorException;
use Evp\Bundle\AccountingBundle\Service\AccountingClient as AccountingRestClient;
use Evp\Bundle\AccountingBundle\Service\AccountingGroupKeyManager;
use Evp\Bundle\AccountingBundle\Service\AccountingGroupKeyResolver;
use Evp\Bundle\AccountingBundle\Service\AccountingOperationBuilderInterface;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\AccountingClientInterface;
use Evp\Bundle\AccountingBundle\Service\Template\VmiTemplateResolver;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankRefundBundle\Entity\RefundOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationDetailsService;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationProvider;
use Evp\Bundle\BankTransferBundle\Service\TransferToVmiDetector;
use Evp\Bundle\CardBundle\Repository\DepositRepository;
use Evp\Bundle\CardBundle\Service\CardManager;
use Evp\Bundle\SepaBundle\Entity\SepaTransactionInTransfer;
use Evp\Bundle\SepaBundle\Entity\SepaUnresolvedRefund;
use Evp\Bundle\TransferProviderBundle\Service\TransferFieldProcessor;
use Evp\Component\Money\Money;
use Paysera\Bundle\SepaInstantBundle\Entity\TransactionIn as SepaInstantTransactionIn;
use Paysera\Bundle\Target2Bundle\Entity\TransactionIn as Target2TransactionIn;
use Paysera\Bundle\Target2RTGSBundle\Entity\TransactionIn as Target2RTGSTransactionIn;
use Paysera\Bundle\VersobankBundle\Entity\TransactionIn as VersobankTransactionIn;
use Paysera\Bundle\GeorgiaRTGSBundle\Entity\TransactionIn as GeorgiaTransactionIn;
use Symfony\Component\Translation\TranslatorInterface;
use DateTime;

//dm old accounting
class ExternalOperationHandler
{
    private $accountOwnerResolver;
    private $accountingClient;
    private VmiTemplateResolver $vmiTemplateResolver;
    private $builder;
    private $templateKeyFill;
    private $templateKeyMicroFill;
    private $templateKeyOutBank;
    private $templateKeyOutBankSpecific;
    private $templateKeyFillCardDepositLpb;
    private $templateContisTopUpToIntermediary;
    private $templateContisTopUpToIntermediary2;
    private $templateKeyLbFill;
    private $templateKeyLbSepaInstFill;
    private $templateKeyTarget2Fill;
    private $templateKeyVersobankFill;
    private $contisFillAccountIbans;
    private $transferToVmiDetector;
    private $templateKeyOutVpo;
    private $vpoCovenanteeId;
    private $translator;
    private $depositRepository;
    private $groupKeyResolver;
    private $groupKeyManager;
    private $skipAccountList;
    private $transferFieldProcessor;
    private $sepaBankKeys;
    private $sepaInstantBankKeys;
    private $target2BankKeys;
    private string $georgiaRTGSBankKey;
    private $accountingRestClient;

    /**
     * @var string[]
     */
    private $bankKeyAliases = [];
    private string $templateKeyGeorgiaRTGSFill;
    private string $templateKeyGatewayReceivedLtLbPayseraSepaEur;
    private string $disabledPayseraSepa3Date;
    private string $evpMainAccount;
    private array $inbankLeasingTemplateAccountMap;
    private string $enabledPayseraSepa3Date;
    private OperationProvider $operationProvider;
    private OperationDetailsService $operationDetailsService;

    /**
     * @param AccountOwnerResolver $accountOwnerResolver
     * @param OperationDetailsService $operationDetailsService
     * @param VmiTemplateResolver $vmiTemplateResolver
     * @param string $templateKeyFill
     * @param string $templateKeyMicroFill
     * @param string $templateKeyOutBankSpecific
     * @param string $templateKeyOutBank
     * @param string $templateKeyOutVpo
     * @param string $templateKeyFillCardDepositLpb
     * @param string $templateContisTopUpToIntermediary
     * @param string $templateContisTopUpToIntermediary2
     * @param string $templateKeyLbFill
     * @param string $templateKeyLbSepaInstFill
     * @param string $templateKeyTarget2Fill
     * @param string $templateKeyVersobankFill
     * @param string $templateKeyGeorgiaRTGSFill
     * @param string[] $contisFillAccountIbans
     * @param TransferToVmiDetector $transferToVmiDetector
     * @param int $vpoCovenanteeId
     * @param DepositRepository $depositRepository
     * @param TranslatorInterface $translator
     * @param AccountingGroupKeyResolver $groupKeyResolver
     * @param AccountingGroupKeyManager $groupKeyManager
     * @param array $skipAccountList
     * @param AccountingClientInterface $accountingClient
     * @param AccountingOperationBuilderInterface $builder
     * @param TransferFieldProcessor $transferFieldProcessor
     * @param AccountingRestClient $accountingRestClient
     * @param array $sepaBankKeys
     * @param array $sepaInstantBankKeys
     * @param array $target2BankKeys
     * @param string $georgiaRTGSBankKey
     * @param string[] $bankKeyAliases
     * @param string $templateKeyGatewayReceivedLtLbPayseraSepaEur
     * @param string $disabledPayseraSepa3Date
     * @param string $enabledPayseraSepa3Date
     * @param string $evpMainAccount
     * @param array $inbankLeasingTemplateAccountMap
     * @param OperationProvider $operationProvider
     */
    public function __construct(
        AccountOwnerResolver $accountOwnerResolver,
        OperationDetailsService $operationDetailsService,
        VmiTemplateResolver $vmiTemplateResolver,
        $templateKeyFill,
        $templateKeyMicroFill,
        $templateKeyOutBankSpecific,
        $templateKeyOutBank,
        $templateKeyOutVpo,
        $templateKeyFillCardDepositLpb,
        $templateContisTopUpToIntermediary,
        $templateContisTopUpToIntermediary2,
        $templateKeyLbFill,
        $templateKeyLbSepaInstFill,
        $templateKeyTarget2Fill,
        $templateKeyVersobankFill,
        $templateKeyGeorgiaRTGSFill,
        array $contisFillAccountIbans,
        TransferToVmiDetector $transferToVmiDetector,
        $vpoCovenanteeId,
        DepositRepository $depositRepository,
        TranslatorInterface $translator,
        AccountingGroupKeyResolver $groupKeyResolver,
        AccountingGroupKeyManager $groupKeyManager,
        array $skipAccountList,
        AccountingClientInterface $accountingClient,
        AccountingOperationBuilderInterface $builder,
        TransferFieldProcessor $transferFieldProcessor,
        AccountingRestClient $accountingRestClient,
        array $sepaBankKeys,
        array $sepaInstantBankKeys,
        array $target2BankKeys,
        string $georgiaRTGSBankKey,
        array $bankKeyAliases,
        string $templateKeyGatewayReceivedLtLbPayseraSepaEur,
        string $disabledPayseraSepa3Date,
        string $enabledPayseraSepa3Date,
        string $evpMainAccount,
        array $inbankLeasingTemplateAccountMap,
        OperationProvider $operationProvider
    ) {
        $this->accountOwnerResolver = $accountOwnerResolver;
        $this->templateKeyFill = $templateKeyFill;
        $this->vmiTemplateResolver = $vmiTemplateResolver;
        $this->templateKeyMicroFill = $templateKeyMicroFill;
        $this->templateKeyOutBankSpecific = $templateKeyOutBankSpecific;
        $this->templateKeyOutVpo = $templateKeyOutVpo;
        $this->templateKeyOutBank = $templateKeyOutBank;
        $this->templateKeyFillCardDepositLpb = $templateKeyFillCardDepositLpb;
        $this->templateKeyLbFill = $templateKeyLbFill;
        $this->templateKeyLbSepaInstFill = $templateKeyLbSepaInstFill;
        $this->templateKeyTarget2Fill = $templateKeyTarget2Fill;
        $this->templateKeyVersobankFill = $templateKeyVersobankFill;
        $this->templateContisTopUpToIntermediary = $templateContisTopUpToIntermediary;
        $this->templateContisTopUpToIntermediary2 = $templateContisTopUpToIntermediary2;
        $this->contisFillAccountIbans = $contisFillAccountIbans;
        $this->transferToVmiDetector = $transferToVmiDetector;
        $this->vpoCovenanteeId = $vpoCovenanteeId;
        $this->depositRepository = $depositRepository;
        $this->translator = $translator;
        $this->groupKeyResolver = $groupKeyResolver;
        $this->groupKeyManager = $groupKeyManager;
        $this->skipAccountList = $skipAccountList;
        $this->accountingClient = $accountingClient;
        $this->builder = $builder;
        $this->transferFieldProcessor = $transferFieldProcessor;
        $this->accountingRestClient = $accountingRestClient;
        $this->sepaBankKeys = $sepaBankKeys;
        $this->sepaInstantBankKeys = $sepaInstantBankKeys;
        $this->target2BankKeys = $target2BankKeys;
        $this->bankKeyAliases = $bankKeyAliases;
        $this->operationDetailsService = $operationDetailsService;
        $this->georgiaRTGSBankKey = $georgiaRTGSBankKey;
        $this->templateKeyGeorgiaRTGSFill = $templateKeyGeorgiaRTGSFill;
        $this->templateKeyGatewayReceivedLtLbPayseraSepaEur = $templateKeyGatewayReceivedLtLbPayseraSepaEur;
        $this->disabledPayseraSepa3Date = $disabledPayseraSepa3Date;
        $this->enabledPayseraSepa3Date = $enabledPayseraSepa3Date;
        $this->evpMainAccount = $evpMainAccount;
        $this->inbankLeasingTemplateAccountMap = $inbankLeasingTemplateAccountMap;
        $this->operationProvider = $operationProvider;
    }

    /**
     * Handles external IN operation
     *
     * @param Operation $operation
     * @param Account $debitAccount
     * @param Money $amount
     * @param string $details
     * @param \DateTime|null $date
     *
     * @throws OperationProcessorException
     * @throws \InvalidArgumentException
     */
    public function handleIn(
        Operation $operation,
        Account $debitAccount,
        Money $amount,
        $details,
        \DateTime $date = null
    ) {
        if (!$operation instanceof TransferInOperation) {
            return;
        }

        if (in_array($debitAccount->getNumber(), $this->skipAccountList)) {
            $this->accountingClient->saveAsSkipped($operation);
            return;
        }
        $client = $this->accountOwnerResolver->getRealAccountOwner($debitAccount);

        $transactionInDto = $this->operationProvider->getTransactionIn($operation->getTransfer());

        $details = $this->operationDetailsService->modifyDetailsForSepaTransactionsIfNeeded(
            $details,
            $transactionInDto
        );

        $accOperation = $this->builder->build(
            $client,
            $amount,
            sprintf('%s (W2P sąskaitos papildymas)', $details),
            $date === null ? $operation->getCreatedAt() : $date
        );

        $accOperation->setGroupKey($this->groupKeyResolver->resolveGroupKeyForIn($operation));

        $transfer = $operation->getTransfer();

        if (
            $transfer->getPurpose() === Transfer::PURPOSE_MICRO
            && strpos($operation->getTransfer()->getNumber(), 'SMSPayout:') === 0
        ) {
            $templateKey = $this->templateKeyMicroFill;
        }  elseif (
            in_array($debitAccount->getNumber(), array_column($this->inbankLeasingTemplateAccountMap, 'account'))
        ) {
            foreach ($this->inbankLeasingTemplateAccountMap as $item) {
                if ($item['account'] === $debitAccount->getNumber()) {
                    $templateKey = $item['template'];
                    break;
                }
            }
        } else {
            $templateKey = $this->templateKeyFill;
        }

        $this->accountingClient->save($operation, $accOperation, $templateKey, $debitAccount);

        if ($transfer->getPurpose() === Transfer::PURPOSE_DEPOSIT_FROM_CARD) {
            $deposit = $this->depositRepository->findOneBy(['transferIn' => $operation->getTransfer()]);
            $accOperation = $this->builder->build(
                $client,
                $amount,
                $this->translator->trans(
                    CardManager::PAYMENT_DEPOSIT_DESCRIPTION,
                    ['%account_number%' => $deposit->getAccount()->getNumber()],
                    'EvpCardBundle',
                    $deposit->getCard()->getClient()->getLocale()
                ),
                $date === null ? $operation->getCreatedAt() : $date
            );
            $accOperation->setGroupKey($this->groupKeyResolver->resolveGroupKeyForIn($operation));
            $this->accountingClient->save(
                $operation,
                $accOperation,
                sprintf($this->templateKeyFillCardDepositLpb, $operation->getTransfer()->getAmountCurrency()),
                $debitAccount,
                Transfer::PURPOSE_DEPOSIT_FROM_CARD
            );
        } else {
            if ($transactionInDto === null) {
                return;
            }

            $transactionIn = $transactionInDto->getTransaction();
            if ($transactionIn instanceof SepaTransactionInTransfer) {
                $accOperation->setTitle($details);
                $templateKey = ($debitAccount->getNumber() === $this->evpMainAccount
                    && $transfer->getOperationDate() >= DateTime::createFromFormat('Y-m-d H:i:s', $this->enabledPayseraSepa3Date)
                    && $transfer->getOperationDate() < DateTime::createFromFormat('Y-m-d H:i:s', $this->disabledPayseraSepa3Date)
                ) ? $this->templateKeyGatewayReceivedLtLbPayseraSepaEur
                    : sprintf($this->templateKeyLbFill, $operation->getTransfer()->getAmountCurrency());
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    $templateKey,
                    $debitAccount,
                    'Transfer:CRDT' . $operation->getTransfer()->getId()
                );
            } elseif ($transactionIn instanceof SepaUnresolvedRefund) {
                $accOperation->setTitle($details);
                $accOperation->setDate($transactionIn->getCreatedAt()->format('Y-m-d'));
                $templateKey = ($debitAccount->getNumber() === $this->evpMainAccount
                    && $transfer->getOperationDate() > DateTime::createFromFormat('Y-m-d H:i:s', $this->enabledPayseraSepa3Date)
                    && $transfer->getOperationDate() < DateTime::createFromFormat('Y-m-d H:i:s', $this->disabledPayseraSepa3Date)
                ) ? $this->templateKeyGatewayReceivedLtLbPayseraSepaEur
                    : sprintf($this->templateKeyLbFill, $operation->getTransfer()->getAmountCurrency());
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    $templateKey,
                    $debitAccount,
                    'Transfer:CRDT' . $operation->getTransfer()->getId()
                );
            } elseif ($transactionIn instanceof SepaInstantTransactionIn) {
                $accOperation->setTitle($details);
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    sprintf($this->templateKeyLbSepaInstFill, $operation->getTransfer()->getAmountCurrency()),
                    $debitAccount,
                    'Transfer:SEPA_INST_CRDT' . $operation->getTransfer()->getId()
                );
            } elseif ($transactionIn instanceof Target2TransactionIn) {
                $accOperation->setTitle($details);
                if ($transactionIn->getInterbankSettlementDate() !== null) {
                    $accOperation->setDate($transactionIn->getInterbankSettlementDate()->format('Y-m-d'));
                }
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    sprintf($this->templateKeyTarget2Fill, $operation->getTransfer()->getAmountCurrency()),
                    $debitAccount,
                    'Transfer:TARGET2_CRDT' . $operation->getTransfer()->getId()
                );

            } elseif ($transactionIn instanceof Target2RTGSTransactionIn) {
                $accOperation->setTitle($details);
                if ($transactionIn->getSettlementDate() !== null) {
                    $accOperation->setDate($transactionIn->getSettlementDate()->format('Y-m-d'));
                }
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    sprintf($this->templateKeyTarget2Fill, $operation->getTransfer()->getAmountCurrency()),
                    $debitAccount,
                    'Transfer:TARGET2_CRDT' . $operation->getTransfer()->getId()
                );
            } elseif ($transactionIn instanceof VersobankTransactionIn) {
                $accOperation->setTitle($details);
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    sprintf($this->templateKeyVersobankFill, $operation->getTransfer()->getAmountCurrency()),
                    $debitAccount,
                    'Transfer:CRDT' . $operation->getTransfer()->getId()
                );
            } elseif ($transactionIn instanceof GeorgiaTransactionIn) {
                $accOperation->setTitle($details);
                $this->accountingClient->save(
                    $operation,
                    $accOperation,
                    strtoupper(
                        strtr(
                            $this->templateKeyGeorgiaRTGSFill,
                            [
                                '%bank%' => mb_strtoupper($this->georgiaRTGSBankKey),
                                '%currency%' => $operation->getTransfer()->getAmountCurrency(),
                            ]
                        )
                    ),
                    $debitAccount,
                    'Transfer:GEORGIA_RTGS_CRDT' . $operation->getTransfer()->getId()
                );
            }
        }
    }

    /**
     * Handles external OUT operation
     *
     * @param Operation $operation
     * @param Account $creditAccount
     * @param int $covenanteeId
     * @param Party $beneficiary
     * @param string $bank
     * @param Money $amount
     * @param string $details
     * @param \DateTimeInterface $bankSpecificOperationDate
     *
     * @throws OperationProcessorException
     */
    public function handleOut(
        Operation $operation,
        Account $creditAccount,
        $covenanteeId,
        Party $beneficiary,
        $bank,
        Money $amount,
        $details,
        DateTimeInterface $bankSpecificOperationDate = null
    ) {
        $client = $this->accountOwnerResolver->getRealAccountOwner($creditAccount);

        if (empty($covenanteeId)) {
            throw new OperationProcessorException(sprintf('Empty covenanteeId (client_id=%s)', $client->getId()));
        }

        $bank = $this->solveBankKey($bank);

        $accOperation = $this->builder->build($client, $amount, $details, $operation->getCreatedAt());
        $accOperation->setCovenantee(new \Evp_Api_Accounting_Covenantee((int) $covenanteeId));

        if ($operation instanceof TransferOutOperation && $this->isTransferToContisFillAccount($beneficiary)) {
            $accOperation->setGroupKey($this->groupKeyResolver->resolveGroupKeyForContisTopUp($operation));
            $this->accountingClient->save(
                $operation,
                $accOperation,
                $this->resolveContisTopUpTemplateKey($bank),
                $creditAccount
            );
            return;
        }

        if (in_array($creditAccount->getNumber(), $this->skipAccountList)) {
            $this->accountingClient->saveAsSkipped($operation);
            return;
        }

        if (
            $operation instanceof TransferOutOperation
            && $beneficiary instanceof PartyIban
            && $this->transferToVmiDetector->isVmiTransferParty($operation->getTransfer())
        ) {
            $accOperation->setGroupKey($this->groupKeyResolver->resolveGroupKeyForVmi($operation));
            $template = $this->vmiTemplateResolver->getTemplateOutVMI($amount->getCurrency(), $bank);

            $this->accountingClient->save(
                $operation,
                $accOperation,
                $template,
                $creditAccount,
                'VMI'
            );

            $externalTemplate = $this->vmiTemplateResolver->getExternalTemplateOutVMI(
                $amount->getCurrency(),
                $bank,
                $client->getCovenanteeId()
            );

            $operationForBankAccount = $this->accountingClient->save(
                $operation,
                $accOperation,
                $externalTemplate,
                $creditAccount,
                $this->resolveExtraCorrelationKey($operation, $bank)
            );

            $this->markAsFailedIfApplicable($operationForBankAccount, $operation);

            return;
        } elseif ($this->vpoCovenanteeId == $covenanteeId && $this->isVpoTaxOperation($operation)) {
            /** @var $operation TransferOutOperation */
            $groupKey = $this->groupKeyResolver->resolveGroupKeyForVpo($operation);
            if (!$this->groupKeyManager->updateVpoGroupKey($operation, $groupKey)) {
                return;
            }
            $accOperation->setGroupKey($groupKey);
            $this->accountingClient->save(
                $operation,
                $accOperation,
                sprintf($this->templateKeyOutVpo, $amount->getCurrency()),
                $creditAccount,
                'VPO'
            );
        } else {
            $accOperation->setGroupKey($this->groupKeyResolver->resolveGroupKeyForOut($operation, $bank));
            $this->accountingClient->save($operation, $accOperation, $this->templateKeyOutBank, $creditAccount, 'BANK');
        }

        if ($bankSpecificOperationDate !== null) {
            $accOperation->setDate($bankSpecificOperationDate->format('Y-m-d'));
        }

        if ($operation instanceof TransferOutOperation) {
            $transfer = $operation->getTransfer();

            if ($transfer instanceof TransferOutBank) {
                $title = $this->transferFieldProcessor->processField($transfer, $bank);

                $accOperation->setTitle($title);
            }
        }

        $templateKey = strtr(
            $this->templateKeyOutBankSpecific,
            [
                '%bank%' => $bank,
                '%currency%' => $amount->getCurrency(),
            ]
        );

        // Operation between intermediary and bank accounts
        $operationForBankAccount = $this->accountingClient->save(
            $operation,
            $accOperation,
            strtoupper($templateKey),
            $creditAccount,
            $this->resolveExtraCorrelationKey($operation, $bank)
        );

        $this->markAsFailedIfApplicable($operationForBankAccount, $operation);
    }

    /**
     * @param string $bank
     *
     * @return string
     */
    private function solveBankKey($bank)
    {
        if (isset($this->bankKeyAliases[$bank])) {
            return $this->bankKeyAliases[$bank];
        }
        return $bank;
    }

    private function isTransferToContisFillAccount(Party $beneficiary)
    {
        return
            $beneficiary instanceof PartyIban
            && in_array($beneficiary->getIban(), $this->contisFillAccountIbans, true)
        ;
    }

    private function isVpoTaxOperation(Operation $operation)
    {
        if ($operation instanceof TransferOutOperation) {
            $transfer = $operation->getTransfer();
            $client = $this->accountOwnerResolver->getRealAccountOwner($transfer->getCreditAccount());
            if (
                $transfer instanceof TransferOut
                && $client->getTransferServiceAgreement()->getUseRtpAsCovenanteeId()
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param Operation $operation
     * @param string $bank
     * @return null|string
     */
    private function resolveExtraCorrelationKey(Operation $operation, $bank)
    {
        if (!$operation instanceof TransferOutOperation) {
            return null;
        }
        if (in_array($bank, $this->sepaBankKeys, true)) {
            return 'Transfer:DBIT' . $operation->getTransfer()->getId();
        }
        if (in_array($bank, $this->sepaInstantBankKeys, true)) {
            return 'Transfer:SEPA_INST_DBIT' . $operation->getTransfer()->getId();
        }
        if (in_array($bank, $this->target2BankKeys, true)) {
            return 'Transfer:TARGET2_DBIT' . $operation->getTransfer()->getId();
        }

        return null;
    }

    /**
     * @param string $bank
     *
     * @return string
     */
    private function resolveContisTopUpTemplateKey($bank)
    {
        if (in_array($bank, $this->sepaInstantBankKeys, true)) {
            return $this->templateContisTopUpToIntermediary2;
        }

        return $this->templateContisTopUpToIntermediary;
    }

    public function markAsFailedIfApplicable(?AccountingOperation $operationForBankAccount, $operation): void
    {
        if (
            $operationForBankAccount !== null
            && $operation instanceof RefundOutOperation
            && $operation->getRefund()->isFailedInBank()
        ) {
            // Mark transfer operation as failed in bank
            $this->accountingRestClient->markOperationsAsFailedInBankForOutgoing(
                $this->groupKeyResolver->resolveGroupKeyForTransferOut($operation->getRefund()->getTransfer())
            );

            // Mark refund operation as failed in bank
            $this->accountingRestClient
                ->markOperationAsFailedInBankForOutgoingTransferRefund($operationForBankAccount->getAccountingId())
            ;
        }
    }

    private function isSepaUnresolvedRefund(Transfer $transfer): bool
    {
        if ($transfer instanceof TransferIn) {
            $sepaUnresolvedRefund = $this->sepaUnresolvedRefundRepository->findOneByTransfer($transfer);

            return $sepaUnresolvedRefund !== null;
        }

        return false;
    }
}
