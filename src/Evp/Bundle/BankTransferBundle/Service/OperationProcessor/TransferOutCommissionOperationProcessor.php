<?php

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor;

use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutCommissionOperation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * TransferOutCommissionOperationProcessor
 */
class TransferOutCommissionOperationProcessor extends BaseOperationProcessor
{
    /**
     * @var InternalOperationHandler
     */
    protected $internalOperationHandler;
    private $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    /**
     * @param Handler\InternalOperationHandler $internalOperationHandler
     */
    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Evp\Bundle\AccountingBundle\Exception\OperationProcessorException
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof TransferOutCommissionOperation)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutCommissionOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getTransfer();
        if (!($transfer instanceof TransferOutInterface)) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $this->internalOperationHandler->handleOutCommission(
            $operation,
            $transfer->getCreditCommissionAccount(),
            $transfer->getCreditCommissionMoney()
        );

        try {
            $this->internalPartnerOperationHandler->handleOutCommission(
                $operation,
                $transfer->getCreditCommissionAccount(),
                $transfer->getCreditCommissionMoney()
            );
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
