<?php

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor;

use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutCommissionOperation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferInOperation;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Entity\Operation\BaseTransferOperation;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;

/**
 * Processes operations for revoked transfers
 */
class RevokeTransferOperationProcessor extends BaseOperationProcessor
{
    /**
     * @var Handler\InternalOperationHandler
     */
    protected $internalOperationHandler;

    protected InternalPartnerOperationHandler $internalPartnerOperationHandler;
    protected AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        InternalOperationHandler $internalOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler,
        AccountingExceptionHandler $accountingExceptionHandler
    )
    {
        $this->internalOperationHandler = $internalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
        $this->accountingExceptionHandler = $accountingExceptionHandler;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param \Evp\Bundle\BankAccountBundle\Entity\Operation\Operation $operation
     *
     * @throws \InvalidArgumentException
     */
    public function process(Operation $operation)
    {
        if (!$operation instanceof BaseTransferOperation) {
            throw new \InvalidArgumentException(
                sprintf('Unsupported operation class (operation_class=%s)', get_class($operation))
            );
        }

        $transfer = $operation->getTransfer();
        if (!($transfer instanceof TransferOutInterface)) {
            throw new \InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $this->processTransfer($operation, $transfer);
    }

    protected function processTransfer(Operation $operation, TransferOutInterface $transfer)
    {
        if ($transfer instanceof TransferInternal && $operation instanceof RevokeTransferInOperation) {
            $this->internalOperationHandler->handleIn(
                $operation,
                $transfer->getCreditAccount(),
                $transfer->getDebitAccount(),
                $transfer->getAmountMoney()->negate(),
                $transfer->getDetails()
            );
        } elseif ($transfer instanceof TransferInternal && $operation instanceof RevokeTransferOutOperation) {
            $this->internalOperationHandler->handleOut(
                $operation,
                $transfer->getCreditAccount(),
                $transfer->getDebitAccount(),
                $transfer->getAmountMoney()->negate(),
                $transfer->getDetails()
            );
        } elseif ($operation instanceof RevokeTransferOutCommissionOperation) {
            $this->internalOperationHandler->handleOutCommission(
                $operation,
                $transfer->getCreditAccount(),
                $transfer->getCreditCommissionMoney()->negate()
            );
        } else {
            throw new \InvalidArgumentException(
                sprintf('Unsupported operation class (operation_class=%s)', get_class($operation))
            );
        }

        try {
            if ($transfer instanceof TransferInternal && $operation instanceof RevokeTransferInOperation) {
                $this->internalPartnerOperationHandler->handleIn(
                    $operation,
                    $transfer,
                    $transfer->getAmountMoney()->negate()
                );
            } elseif ($transfer instanceof TransferInternal && $operation instanceof RevokeTransferOutOperation) {
                $this->internalPartnerOperationHandler->handleOut(
                    $operation,
                    $transfer,
                    $transfer->getAmountMoney()->negate(),
                );
            } elseif ($operation instanceof RevokeTransferOutCommissionOperation) {
                $this->internalPartnerOperationHandler->handleOutCommission(
                    $operation,
                    $transfer->getCreditAccount(),
                    $transfer->getCreditCommissionMoney()->negate()
                );
            }
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }
}
