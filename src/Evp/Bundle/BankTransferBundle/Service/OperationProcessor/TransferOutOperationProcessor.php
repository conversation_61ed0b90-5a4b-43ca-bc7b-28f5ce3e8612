<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Exception\OperationProcessorException;
use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\AccountingBundle\Service\PartnerAccountingProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Exception;
use InvalidArgumentException;
use Paysera\Bundle\PartnerBundle\Exception\PartnerAccountingRequestException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;
use Throwable;

class TransferOutOperationProcessor extends BaseOperationProcessor
{
    private AccountingExceptionHandler $accountingExceptionHandler;
    private InternalOperationHandler $internalOperationHandler;
    private ExternalOperationHandler $externalOperationHandler;
    private AccountingBankResolver $accountingBankResolver;
    private AccountOwnerResolver $accountOwnerResolver;
    private OperationDateService $operationDateService;
    private PartnerAccountingProcessor $partnerAccountingProcessor;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        ExternalOperationHandler $externalOperationHandler,
        AccountingBankResolver $accountingBankResolver,
        AccountOwnerResolver $accountOwnerResolver,
        OperationDateService $operationDateService,
        PartnerAccountingProcessor $partnerAccountingProcessor
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->externalOperationHandler = $externalOperationHandler;
        $this->accountingBankResolver = $accountingBankResolver;
        $this->accountOwnerResolver = $accountOwnerResolver;
        $this->operationDateService = $operationDateService;
        $this->partnerAccountingProcessor = $partnerAccountingProcessor;
    }

    /**
     * @param Operation $operation
     *
     * @throws PartnerAccountingRequestException
     * @throws Throwable
     */
    public function process(Operation $operation)
    {
        if (!$operation instanceof TransferOutOperation) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected %s',
                    get_class($operation),
                    TransferOutOperation::class
                )
            );
        }

        $transfer = $operation->getTransfer();
        if ($transfer->getAmountMoney()->isZero()) {
            $this->saveAsSkipped($operation);
        } elseif ($transfer instanceof TransferInternal) {
            $this->processTransferInternal($operation, $transfer);
        } elseif ($transfer instanceof TransferOut) {
            $this->processTransferOut($operation, $transfer);
        } else {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        try {
            $this->partnerAccountingProcessor->processOperation($operation);
        } catch (Exception $exception) {
            $this->accountingExceptionHandler->handle($exception);
        }
    }

    /**
     * @param TransferOutOperation $operation
     * @param TransferInternal $transfer
     * @throws OperationProcessorException
     */
    protected function processTransferInternal(TransferOutOperation $operation, TransferInternal $transfer)
    {
        $this->internalOperationHandler->handleOut(
            $operation,
            $transfer->getCreditAccount(),
            $transfer->getDebitAccount(),
            $transfer->getAmountMoney(),
            $transfer->getDetails()
        );
    }


    /**
     * @param TransferOutOperation $operation
     * @param TransferOut $transfer
     * @throws OperationProcessorException
     */
    private function processTransferOut(TransferOutOperation $operation, TransferOut $transfer)
    {
        $details = $transfer->getDetails();

        if ($details === null || $details === '') {
            $details = $transfer->getReferenceNumber();
        }

        $client = $this->accountOwnerResolver->getRealAccountOwner($transfer->getCreditAccount());
        $specificOperationDate = $this->operationDateService->getTransferOutOperationDatetime($transfer, $operation);
        $bank = $this->accountingBankResolver->resolveBank($transfer, $operation);

        $this->externalOperationHandler->handleOut(
            $operation,
            $transfer->getCreditAccount(),
            $client->getCovenanteeId(),
            $transfer->getBeneficiary(),
            $bank,
            $transfer->getAmountMoney(),
            $details,
            $specificOperationDate
        );
    }
}
