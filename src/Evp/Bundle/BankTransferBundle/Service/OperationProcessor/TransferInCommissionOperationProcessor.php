<?php

namespace Evp\Bundle\BankTransferBundle\Service\OperationProcessor;

use Evp\Bundle\AccountingBundle\Service\AccountingProcessor\BaseOperationProcessor;
use Evp\Bundle\BankAccountBundle\Entity\Operation\Operation;
use Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler;
use Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler;
use Exception;
use InvalidArgumentException;
use Paysera\Bundle\PartnerBundle\Exception\PartnerAccountingRequestException;
use Evp\Bundle\AccountingBundle\Service\AccountingExceptionHandler;
use Throwable;

class TransferInCommissionOperationProcessor extends BaseOperationProcessor
{
    protected InternalOperationHandler $internalOperationHandler;
    private InternalPartnerOperationHandler $internalPartnerOperationHandler;
    private AccountingExceptionHandler $accountingExceptionHandler;

    public function __construct(
        AccountingExceptionHandler $accountingExceptionHandler,
        InternalOperationHandler $internalOperationHandler,
        InternalPartnerOperationHandler $internalPartnerOperationHandler
    ) {
        $this->accountingExceptionHandler = $accountingExceptionHandler;
        $this->internalOperationHandler = $internalOperationHandler;
        $this->internalPartnerOperationHandler = $internalPartnerOperationHandler;
    }

    /**
     * Process transfer operation for evp accounting
     *
     * @param Operation $operation
     *
     * @throws PartnerAccountingRequestException
     * @throws Throwable
     */
    public function process(Operation $operation)
    {
        if (!($operation instanceof TransferInCommissionOperation)) {
            throw new InvalidArgumentException(
                sprintf(
                    'Unsupported operation class (operation_class=%s) expected TransferInCommissionOperation',
                    get_class($operation)
                )
            );
        }

        $transfer = $operation->getTransfer();
        if (!($transfer instanceof TransferInInterface)) {
            throw new InvalidArgumentException(sprintf('Unsupported transfer class %s', get_class($transfer)));
        }

        $amount = $transfer->getDebitCommissionMoney();
        if ($transfer->getDebitCommissionConvertedMoney() && !$transfer->getDebitCommissionConvertedMoney()->isZero()) {
            $amount = $transfer->getDebitCommissionConvertedMoney();
        }
        if ($operation->getAccount() !== null) {
            $this->internalOperationHandler->handleInCommission(
                $operation,
                $operation->getAccount(),
                $amount,
                $transfer->getPurpose()
            );

            try {
                $this->internalPartnerOperationHandler->handleInCommission(
                    $operation,
                    $operation->getAccount(),
                    $amount,
                    $transfer->getPurpose()
                );
            } catch (Exception $exception) {
                $this->accountingExceptionHandler->handle($exception);
            }
        } else {
            $this->logger->error('TransferInCommissionOperation with account NULL. Not processing.', [
                'operation_id' => $operation->getId(),
            ]);
        }
    }
}
