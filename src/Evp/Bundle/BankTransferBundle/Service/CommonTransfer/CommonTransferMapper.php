<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\CommonTransfer;

use Evp\Bundle\BankBundle\Service\AccountInfoResolver;
use Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvert;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutSingleWindow;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Service\CommonTransfer\BeneficiaryMapper\BeneficiaryMapper;
use Evp\Bundle\BankTransferBundle\Service\IbanAliasAccountNumberManager;
use Evp\Bundle\BankTransferBundle\Service\SepaByLegalSpecialCountryClientDetector;
use Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryEmailManager;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Service\AddressProvider;
use Evp\Bundle\ClientBundle\Service\ClientIdentifier\ClientIdentifierManager;
use Evp\Bundle\ProtectedTransferBundle\Service\TransferPasswordManager;
use Evp\Bundle\SepaBundle\Service\SepaTransferDetector;
use Paysera\Bundle\SepaInstantBundle\Service\SepaInstantTransferDetector;
use Paysera\Bundle\BgBudgetPaymentBundle\Entity\BgBudgetPaymentTransfer;
use Paysera\Bundle\MobilePaymentsBundle\Repository\MobilePaymentRepository;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Account\BankAccount;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Address;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\BgBudgetPrimaryPayer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CurrencyConversion\CurrencyConversionResult;
use Paysera\Bundle\TransferSurveillanceBundle\Entity\TransferInspection;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferInspectionProvider;
use Paysera\Client\UserAddressClient\Entity\UserAddress;
use Paysera\Component\Serializer\Exception\InvalidDataException;
use RuntimeException;
use Exception;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutInterface;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyTaxReference;
use Evp\Bundle\BankTransferBundle\Service\EvpAccountResolver;
use Evp\Bundle\ProtectedTransferBundle\Entity\TransferPassword;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\CommissionRule;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\DetailsOptions;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\FailureStatus;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Notification;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\CompanyCodeClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\CustomerCodeClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\ClientIdentifier\PersonalNumberClientIdentifier;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Initiator;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Payer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Password;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Purpose;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Transfer as CommonTransfer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransferAdditionalInformation;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\TransferCallback as CommonTransferCallback;
use Evp\Component\SensitiveValue\Entity\SensitiveValue;
use Paysera\Bundle\TransferCallbackBundle\Repository\TransferCallbackRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Stopwatch\Stopwatch;
use Symfony\Component\Stopwatch\StopwatchPeriod;

class CommonTransferMapper
{
    private array $purposeMap;
    private TransferCallbackRepository $transferCallbackRepository;
    private EvpAccountResolver $evpAccountResolver;
    private BeneficiaryMapper $beneficiaryMapper;
    private TransferPasswordManager $transferPasswordManager;
    private TransferBeneficiaryEmailManager $transferBeneficiaryEmailManager;
    private AccountInfoResolver $accountInfoResolver;
    private AddressProvider $addressProvider;
    private IbanAliasAccountNumberManager $transferBeneficiaryAccountNumberManager;
    private ClientIdentifierManager $clientIdentifierManager;
    private SepaByLegalSpecialCountryClientDetector $sepaByLegalSpecialCountryClientDetector;
    private SepaTransferDetector $sepaTransferDetector;
    private SepaInstantTransferDetector $sepaInstantTransferDetector;
    private MobilePaymentRepository $mobilePaymentRepository;
    private TransferInspectionProvider $transferInspectionProvider;
    private LoggerInterface $logger;
    private Stopwatch $stopwatch;

    public function __construct(
        array $purposeMap,
        TransferCallbackRepository $transferCallbackRepository,
        EvpAccountResolver $evpAccountResolver,
        BeneficiaryMapper $beneficiaryMapper,
        TransferPasswordManager $transferPasswordManager,
        TransferBeneficiaryEmailManager $transferBeneficiaryEmailManager,
        AccountInfoResolver $accountInfoResolver,
        AddressProvider $addressProvider,
        IbanAliasAccountNumberManager $transferBeneficiaryAccountNumberManager,
        ClientIdentifierManager $clientIdentifierManager,
        SepaByLegalSpecialCountryClientDetector $sepaByLegalSpecialCountryClientDetector,
        SepaTransferDetector $sepaTransferDetector,
        SepaInstantTransferDetector $sepaInstantTransferDetector,
        MobilePaymentRepository $mobilePaymentRepository,
        TransferInspectionProvider $transferInspectionProvider,
        LoggerInterface $logger,
        Stopwatch $stopwatch
    ) {
        $this->purposeMap = $purposeMap;
        $this->transferCallbackRepository = $transferCallbackRepository;
        $this->evpAccountResolver = $evpAccountResolver;
        $this->beneficiaryMapper = $beneficiaryMapper;
        $this->transferPasswordManager = $transferPasswordManager;
        $this->transferBeneficiaryEmailManager = $transferBeneficiaryEmailManager;
        $this->accountInfoResolver = $accountInfoResolver;
        $this->addressProvider = $addressProvider;
        $this->transferBeneficiaryAccountNumberManager = $transferBeneficiaryAccountNumberManager;
        $this->clientIdentifierManager = $clientIdentifierManager;
        $this->sepaByLegalSpecialCountryClientDetector = $sepaByLegalSpecialCountryClientDetector;
        $this->sepaTransferDetector = $sepaTransferDetector;
        $this->sepaInstantTransferDetector = $sepaInstantTransferDetector;
        $this->mobilePaymentRepository = $mobilePaymentRepository;
        $this->transferInspectionProvider = $transferInspectionProvider;
        $this->logger = $logger;
        $this->stopwatch = $stopwatch;
    }

    /**
     * @param Transfer $transfer
     * @return CommonTransfer
     */
    public function toCommonTransfer(Transfer $transfer)
    {
        $this->logger->info('Mapping to common transfer', ['transfer' => $transfer]);
        $this->stopwatch->start(__METHOD__);

        try {
            $commonBeneficiary = $this->beneficiaryMapper->mapToCommonBeneficiary($transfer->getBeneficiary(), $transfer);

            $this->stopwatch->lap(__METHOD__);

            if ($transfer instanceof TransferInternal) {
                $this->transferBeneficiaryAccountNumberManager
                    ->addIbanAliasForCommonBeneficiary($commonBeneficiary, $transfer);
            }

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer = new CommonTransfer();
            $commonTransfer
                ->setId((string) $transfer->getId())
                ->setHash($transfer->getHash())
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setCallback($this->mapToCommonTransferCallback($transfer))
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setStatus($this->mapToCommonStatus($transfer))
                ->setOriginalStatus($transfer->getStatus())
                ->setAmount($transfer->getAmountMoney())
                ->setBeneficiary($commonBeneficiary)
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setFinalBeneficiary($this->beneficiaryMapper->mapToFinalBeneficiary($transfer->getFinalBeneficiary()))
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setPayer($this->mapToCommonPayer($transfer))
                ->setCreatedAt($transfer->getCreatedAt())
                ->setPerformAt($transfer->getOperationDate())
                ->setFailureStatus($this->mapToCommonFailureStatus($transfer))
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setNotifications($this->mapToCommonNotifications($transfer))
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setPurpose($this->mapToCommonPurpose($transfer))
                ->setCancelable($transfer->getCancelable())
                ->setFormedAutomatically($transfer->isFormedAutomatically())
                ->setAdditionalInformation($this->mapToCommonAdditionalInformation($transfer))
                ->setAllowedToCancel($transfer->getHasCancelPermission())
                ->setAllowedToEdit($transfer->getHasEditPermission() ?? false)
                ->setAllowedToSign($transfer->getHasSignPermission() ?? false)
            ;

            $this->stopwatch->lap(__METHOD__);

            $commonTransfer
                ->setRelatedConvertCurrency($this->mapToCommonRelatedCurrencyConversion($transfer))
            ;

            $this->stopwatch->lap(__METHOD__);

            try {
                $commonTransfer->setPerformedAt($transfer->getDoneFromDate());
            } catch (Exception $exception) {
            }

            if ($transfer->getReferenceToPayerValue() !== null) {
                $commonTransfer->setReferenceToPayer($transfer->getReferenceToPayerValue());
            }

            $this->stopwatch->lap(__METHOD__);

            if ($transfer instanceof TransferOutInterface) {
                $commonTransfer
                    ->setInitiator($this->mapToCommonInitiator($transfer))
                    ->setAutoCurrencyConvert($transfer->getAutoCurrencyConvert())
                    ->setAutoChargeRelatedCard($transfer->getPaymentCardDebitAllowed())
                    ->setOutCommission($transfer->getCreditCommissionMoney())
                ;
            }

            $this->stopwatch->lap(__METHOD__);

            if ($transfer instanceof TransferInternal) {
                $commonTransfer
                    ->setReserveUntil($transfer->getReserveUntil())
                    ->setAutoProcessToDone($transfer->isAutoProcessToDone())
                ;
                $transferPassword = $this->transferPasswordManager->findTransferPassword($transfer);
                if ($transferPassword !== null) {
                    $commonTransfer->setPassword($this->mapToCommonPassword($transferPassword));
                }

                $mobilePayment = $this->mobilePaymentRepository->findOneByInternalTransfer($transfer);
                if ($mobilePayment !== null) {
                    $commonTransfer->setIsInternalMobilePayment(true)
                        ->setInternalMobilePaymentStatus($mobilePayment->getStatus())
                    ;
                }
            }

            $this->stopwatch->lap(__METHOD__);

            if ($transfer instanceof TransferOut) {
                $commonTransfer->setMaxExecutionTime($transfer->getMaxExecutionTime());
            }

            $this->stopwatch->lap(__METHOD__);

            if ($transfer instanceof TransferOutBank) {
                $commonTransfer
                    ->setChargeType($transfer->getChargeType())
                    ->setIsSepaByLegalSpecialCountryClient(
                        $this->sepaByLegalSpecialCountryClientDetector
                            ->isSepaByLegalSpecialCountryClient($transfer)
                    )
                    ->setIsSepa(
                        $this->sepaTransferDetector->isRegularSepaBankTransfer($transfer)
                        || $this->sepaInstantTransferDetector->isSepaInstantBankTransfer($transfer)
                    )
                ;
                if ($transfer->getRoutingParameter() === TransferOutBank::ROUTING_PARAMETER_NOT_URGENT) {
                    $commonTransfer->setUrgency(CommonTransfer::URGENCY_STANDARD);
                } elseif ($transfer->getRoutingParameter() === TransferOutBank::ROUTING_PARAMETER_URGENT) {
                    $commonTransfer->setUrgency(CommonTransfer::URGENCY_URGENT);
                } elseif ($transfer->getRoutingParameter() === TransferOutBank::ROUTING_PARAMETER_VERY_URGENT) {
                    $commonTransfer->setUrgency(CommonTransfer::URGENCY_VERY_URGENT);
                }
            }

            $this->stopwatch->lap(__METHOD__);

            if (
                $transfer instanceof TransferOutSingleWindow
                && $transfer->getTransferInformation() !== null
            ) {
                $commonTransfer->setEstimatedPayoutAmountMoney(
                    $transfer->getTransferInformation()->getEstimatedPayoutAmountMoney()
                );
            }

            if (
                $transfer instanceof BgBudgetPaymentTransfer
            ) {
                $commonTransfer->setBgBudgetPaymentType($transfer->getBgBudgetPayment()->getPaymentType());
                $primaryPayer = $transfer->getPrimaryPayer();
                if ($primaryPayer !== null) {
                    $commonTransfer->setBgBudgetPrimaryPayer(
                        (new BgBudgetPrimaryPayer())
                            ->setName($primaryPayer->getDisplayName())
                            ->setAccountNumber($primaryPayer->getCode())
                            ->setResidenceType($transfer->getBgBudgetPayment()->getResidenceType())
                    );
                }
            }

            $transferInspection = $this->transferInspectionProvider->findTransferInspection($transfer);
            if ($transferInspection !== null) {
                $inspectionAction = $transferInspection->getAction();
                $commonTransfer->setInspectionStatus(
                    $transferInspection->getReviewAction()
                    ?? ($inspectionAction !== TransferInspection::ACTION_NONE
                        ? $inspectionAction : $transferInspection->getStatus())
                );
            }

            $this->logger->info('Mapped to common transfer', ['common_transfer' => $commonTransfer]);

            return $commonTransfer;
        } finally {
            $this->logStopWatch(__METHOD__, $transfer->getId());
        }
    }

    /**
     * @param Transfer $transfer
     * @return string
     * @throws RuntimeException
     */
    public function mapToCommonStatus(Transfer $transfer)
    {
        $status = $transfer->getStatus();
        if ($status === Transfer::STATUS_NEW) {
            return CommonTransfer::STATUS_NEW;
        } elseif ($status === Transfer::STATUS_PREPARED) {
            return CommonTransfer::STATUS_REGISTERED;
        } elseif ($status === Transfer::STATUS_SIGNED) {
            return CommonTransfer::STATUS_SIGNED;
        } elseif ($status === Transfer::STATUS_RESERVED) {
            if ($transfer instanceof TransferInternal) {
                $transferPassword = $this->transferPasswordManager->findTransferPassword($transfer);
                if (
                    $transferPassword !== null
                    && $transferPassword->getStatus() !== TransferPassword::STATUS_OK
                ) {
                    return CommonTransfer::STATUS_WAITING_PASSWORD;
                } elseif ($transfer->getDebitAccount() === null) {
                    return CommonTransfer::STATUS_WAITING_REGISTRATION;
                }
            }
            return CommonTransfer::STATUS_RESERVED;
        } elseif ($status === Transfer::STATUS_READY) {
            return CommonTransfer::STATUS_RESERVED;
        } elseif ($status === Transfer::STATUS_FREEZED) {
            return CommonTransfer::STATUS_FROZEN;
        } elseif ($status === Transfer::STATUS_FAILED) {
            if (
                $transfer->getFailureStatus() !== null
                && $transfer->getFailureStatus()->getCode() === TransferFailureStatus::CODE_REVOKED
            ) {
                return CommonTransfer::STATUS_REVOKED;
            }

            return CommonTransfer::STATUS_FAILED;
        } elseif ($status === Transfer::STATUS_WAITING_FUNDS) {
            return CommonTransfer::STATUS_WAITING_FUNDS;
        } elseif ($status === Transfer::STATUS_CANCELED) {
            return CommonTransfer::STATUS_REJECTED;
        } elseif ($status === Transfer::STATUS_DONE) {
            return CommonTransfer::STATUS_DONE;
        } elseif ($status === Transfer::STATUS_PROCESSING || $status === Transfer::STATUS_SUCCESS) {
            return CommonTransfer::STATUS_PROCESSING;
        }
        throw new RuntimeException(sprintf('Unknown transfer status: %s', $status));
    }

    /**
     * @param Transfer $transfer
     * @return CommonTransferCallback|null
     */
    private function mapToCommonTransferCallback(Transfer $transfer)
    {
        $transferCallback = $this->transferCallbackRepository->findOneByTransfer($transfer);
        if ($transferCallback === null) {
            return null;
        }

        return
            (new CommonTransferCallback())
                ->setStatuses($transferCallback->getStatuses())
                ->setUrl($transferCallback->getUrl())
            ;
    }

    /**
     * @param Transfer $transfer
     * @return Initiator
     */
    private function mapToCommonInitiator(Transfer $transfer)
    {
        $initiator = new Initiator();

        $client = $transfer->getClient();
        if ($client->getCovenanteeId() !== null) {
            $initiator->setUserId($client->getCovenanteeId());
        } else {
            $initiator->setClientId($client->getId());
        }

        return $initiator;
    }

    /**
     * @param Transfer $transfer
     * @return Payer
     */
    private function mapToCommonPayer(Transfer $transfer)
    {
        $commonPayer = new Payer();
        $payer = $transfer->getPayer();
        if ($transfer instanceof TransferOutInterface && $transfer->getCreditAccount() !== null) {
            $commonPayer->setAccountNumber($transfer->getCreditAccount()->getNumber());
            $creditAccountClient = $transfer->getCreditAccount()->getClient();
            $commonPayer->setName($creditAccountClient->getDisplayName());
            if ($creditAccountClient->getCovenanteeId() !== null) {
                try {
                    $userAddress = $this->addressProvider->getAddressByType(
                        $creditAccountClient,
                        $creditAccountClient instanceof ClientNatural
                            ? UserAddress::TYPE_LIVING_ADDRESS
                            : UserAddress::TYPE_BUSINESS_ADDRESS
                    );
                } catch (Exception $exception) {
                    $userAddress = null;
                }

                if ($userAddress !== null) {
                    $commonPayer
                        ->setHouse($userAddress->getHouseNumber())
                        ->setApartment($userAddress->getApartmentNumber())
                        ->setStreet($userAddress->getStreetName())
                        ->setCity($userAddress->getCityName())
                        ->setCountryCode($userAddress->getCountry());
                }
                $commonPayer->setUserId($creditAccountClient->getCovenanteeId());

                $clientIdentifier = $this->clientIdentifierManager->getIdentifier($creditAccountClient);
                if ($clientIdentifier !== null) {
                    $commonPayer->setClientIdentifier($clientIdentifier);
                }
            } else {
                $commonPayer->setClientId($creditAccountClient->getId());
            }
        } elseif ($transfer instanceof TransferInInterface && $payer !== null) {
            $commonPayer->setName($payer->getDisplayName());
            if ($this->evpAccountResolver->isEvpAccount($payer->getDisplayAccount())) {
                $commonPayer->setAccountNumber($payer->getDisplayAccount());
            }
        }

        $primaryPayer = $transfer->getPrimaryPayer();
        if ($transfer->getReferenceToBeneficiary() !== null) {
            $commonPayer->setReference($transfer->getReferenceToBeneficiary());
        } elseif ($primaryPayer !== null && $primaryPayer instanceof PartyTaxReference) {
            $commonPayer->setReference($primaryPayer->getIdentifier());
        }

        if ($transfer->getReferenceToPayer() !== null) {
            switch ($transfer->getReferenceToPayerIdentifier()) {
                case Transfer::REFERENCE_IDENTIFIER_NIDN:
                    $commonPayer->setClientIdentifier(new PersonalNumberClientIdentifier());
                    break;
                case Transfer::REFERENCE_IDENTIFIER_COID:
                    $commonPayer->setClientIdentifier(
                        (new CompanyCodeClientIdentifier())->setValue($transfer->getReferenceToPayerValue())
                    );
                    break;
                case Transfer::REFERENCE_IDENTIFIER_CUST:
                    $commonPayer->setClientIdentifier(
                        (new CustomerCodeClientIdentifier())->setValue($transfer->getReferenceToPayerValue())
                    );
                    break;
            }
        }

        if ($transfer instanceof TransferOut && $transfer->getMainPayer() instanceof PartyBank) {
            $mainPayer = $transfer->getMainPayer();

            $accountInfo = $this->accountInfoResolver->getAccountInfoForTransferParty($mainPayer);

            $accountNumber = $accountInfo->getAccountNumber() !== null
                ? $accountInfo->getAccountNumber()
                : $accountInfo->getUnformattedAccountNumber();

            $bankAccount = (new BankAccount())
                ->setIban($accountInfo->getIban())
                ->setAccountNumber($accountNumber)
                ->setCountryCode($accountInfo->getCountryCode())
                ->setBic($accountInfo->getBankSwift())
                ->setBankCode($accountInfo->getBankBic())
                ->setSortCode($accountInfo->getSortCode())
                ->setBankTitle($mainPayer->getDisplayName())
            ;

            if ($accountInfo->getCountryCode() !== null || $accountInfo->getBankAddress() !== null) {
                $bankAccount->setBankAddress(
                    new Address($accountInfo->getCountryCode(), $accountInfo->getBankAddress())
                );
            }

            $commonPayer->setAccount($bankAccount);
        }

        return $commonPayer;
    }

    /**
     * @param Transfer $transfer
     * @return FailureStatus
     */
    private function mapToCommonFailureStatus(Transfer $transfer)
    {
        $failureStatus = $transfer->getFailureStatus();
        if ($failureStatus === null) {
            return null;
        }

        return new FailureStatus($failureStatus->getCode(), $failureStatus->getMessage());
    }

    private function mapToCommonPurpose(Transfer $transfer)
    {
        $purpose = new Purpose();
        $purpose->setDetails($transfer->getDetails());
        $purpose->setReference($transfer->getReferenceNumber());

        if ($transfer->getPurposeCode() !== null) {
            $purpose->setPurposeCode($transfer->getPurposeCode());
        }

        if ($transfer instanceof TransferOut) {
            $detailsOptions = new DetailsOptions();
            $detailsOptions->setPreserved($transfer->isPreserveDetails());
            $purpose->setDetailsOptions($detailsOptions);
            if ($transfer instanceof TransferOutBank) {
                $purpose->setOcrCode($transfer->getOcrCode());
                $purpose->setVoCode($transfer->getVoCode());
            }
        }

        if ($transfer->getPurpose() !== null && in_array($transfer->getPurpose(), $this->purposeMap, true)) {
            $purpose->setCode(array_search($transfer->getPurpose(), $this->purposeMap, true));
        }

        return $purpose;
    }

    /**
     * @param Transfer $transfer
     *
     * @return Notification[]
     */
    private function mapToCommonNotifications(Transfer $transfer)
    {
        $notifications = [];
        $transferBeneficiaryEmail = $this->transferBeneficiaryEmailManager->findTransferBeneficiaryEmail($transfer);
        if ($transferBeneficiaryEmail !== null) {
            $notifications[] = new Notification(
                Notification::TYPE_DONE,
                $transferBeneficiaryEmail->getEmail(),
                $transferBeneficiaryEmail->getLanguage()
            );
        }

        return $notifications;
    }

    private function mapToCommonPassword(TransferPassword $transferPassword): Password
    {
        $password = new Password();
        $password->setValue(new SensitiveValue($transferPassword->getPassword()));

        if ($transferPassword->isOk()) {
            $password->setStatus(Password::STATUS_UNLOCKED);
        } else {
            $password->setStatus(Password::STATUS_PENDING);
        }

        return $password;
    }

    /**
     * @param Transfer $transfer
     * @return TransferAdditionalInformation|null
     */
    private function mapToCommonAdditionalInformation(Transfer $transfer)
    {
        if ($transfer instanceof TransferOut && $transfer->getOriginalCreditCommission() !== null) {
            $additionalInformation = new TransferAdditionalInformation();
            $additionalInformation->setEstimatedProcessingDate($transfer->getMaxExecutionTime());
            $additionalInformation->setOriginalOutCommission($transfer->getOriginalCreditCommission());
            $additionalInformation->setCorrespondentBankFeesMayApply($transfer->mayCorrespondentBankFeesApply());

            $creditCommissionRule = $transfer->getCreditCommissionRule();
            if ($creditCommissionRule !== null) {
                $outCommissionRule = new CommissionRule();
                $outCommissionRule->setPercent($creditCommissionRule->getPercentage());
                $outCommissionRule->setMin($creditCommissionRule->getMin());
                $outCommissionRule->setMax($creditCommissionRule->getMax());
                $outCommissionRule->setFix($creditCommissionRule->getFix());

                $additionalInformation->setOutCommissionRule($outCommissionRule);

                return $additionalInformation;
            }
        }

        return null;
    }

    /**
     * @param Transfer $transfer
     * @return CurrencyConversionResult|null
     * @throws InvalidDataException
     */
    private function mapToCommonRelatedCurrencyConversion(Transfer $transfer)
    {
        $relatedTransferCurrencyConvert = $transfer->getRelatedCurrencyConvertTransfer();
        if ($relatedTransferCurrencyConvert === null) {
            return null;
        }

        if (!$relatedTransferCurrencyConvert instanceof TransferCurrencyConvert) {
            throw new InvalidDataException('Expected Related Currency Conversion is wrong');
        }

        $currencyConversionResult = (new CurrencyConversionResult())
            ->setId($relatedTransferCurrencyConvert->getHash())
            ->setFromAmount($relatedTransferCurrencyConvert->getAmountMoney())
            ->setToAmount($relatedTransferCurrencyConvert->getToAmountMoney())
            ->setFromCurrency($relatedTransferCurrencyConvert->getAmountCurrency())
            ->setToCurrency($relatedTransferCurrencyConvert->getToAmountCurrency())
            ->setDate($relatedTransferCurrencyConvert->getCreatedAt())
            ->setStatus($this->mapToCommonStatus($relatedTransferCurrencyConvert))
         ;

        if ($relatedTransferCurrencyConvert->getCreditAccount() !== null) {
            $currencyConversionResult->setAccountNumber(
                $relatedTransferCurrencyConvert->getCreditAccount()->getNumber()
            );
        }

        if ($relatedTransferCurrencyConvert->getClient() !== null) {
            $currencyConversionResult->setUserId($relatedTransferCurrencyConvert->getClient()->getCovenanteeId());
        }

        return $currencyConversionResult;
    }

    private function logStopWatch(string $method, ?int $transferId): void
    {
        $this->stopwatch->stop($method);
        $stopWatchEvent = $this->stopwatch->getEvent($method);

        $this->logger->info($method, [
            'transfer_id' => $transferId,
            'millis' => $stopWatchEvent->getDuration(),
            'periods' => array_map(
                function (StopwatchPeriod $period) {
                    return [
                        'millis' => $period->getDuration(),
                    ];
                },
                $stopWatchEvent->getPeriods()
            ),
        ]);
    }
}
