<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\CommonTransfer;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferBeneficiaryEmail;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOut;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyPerson;
use Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\AccountMapperInterface;
use Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\PayerMapperInterface;
use Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryEmailManager;
use Evp\Bundle\ProtectedTransferBundle\Entity\TransferPassword;
use Evp\Bundle\ProtectedTransferBundle\Service\TransferPasswordManager;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Notification;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Beneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\FinalBeneficiary;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Party\Payer;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Purpose;
use Paysera\Bundle\TransferNormalizationRestBundle\Entity\CommonTransfer\Transfer as CommonTransfer;
use Psr\Log\LoggerInterface;

class TransferMapper
{
    private array $purposeMap;
    private IdentifiersMapper $identifiersMapper;
    private TransferPasswordManager $transferPasswordManager;
    private TransferBeneficiaryEmailManager $transferBeneficiaryEmailManager;
    private LoggerInterface $logger;

    /**
     * @var AccountMapperInterface[]
     */
    private array $accountMappers;

    /**
     * @var PayerMapperInterface[]
     */
    private array $payerMappers;

    public function __construct(
        array $purposeMap,
        IdentifiersMapper $identifiersMapper,
        TransferPasswordManager $transferPasswordManager,
        TransferBeneficiaryEmailManager $transferBeneficiaryEmailManager,
        LoggerInterface $logger
    ) {
        $this->purposeMap = $purposeMap;
        $this->identifiersMapper = $identifiersMapper;
        $this->transferPasswordManager = $transferPasswordManager;
        $this->transferBeneficiaryEmailManager = $transferBeneficiaryEmailManager;
        $this->logger = $logger;
    }

    public function addAccountMapper(AccountMapperInterface $accountMapper)
    {
        $this->accountMappers[$accountMapper->getType()] = $accountMapper;
    }

    public function addPayerMapper(PayerMapperInterface $payerMapper): void
    {
        $this->payerMappers[] = $payerMapper;
    }

    public function fromCommonTransfer(CommonTransfer $commonTransfer): Transfer
    {
        $this->logger->info('Mapping from common transfer', ['common_transfer' => $commonTransfer]);

        $transfer = $this->createTransferFromCommonBeneficiary($commonTransfer->getBeneficiary());

        $transfer->setAmountMoney($commonTransfer->getAmount());
        $this->mapFromCommonPayer($transfer, $commonTransfer->getPayer());
        if ($commonTransfer->getFinalBeneficiary() !== null) {
            $transfer->setFinalBeneficiary(
                $this->mapFromCommonFinalBeneficiary($commonTransfer->getFinalBeneficiary())
            );
        }
        if ($commonTransfer->getPerformAt() !== null) {
            $transfer->setOperationDate($commonTransfer->getPerformAt());
        } else {
            $transfer->setOperationDate(new \DateTime());
        }
        $this->mapFromCommonNotifications($transfer, $commonTransfer);
        if ($commonTransfer->getPurpose() !== null) {
            $this->mapFromCommonPurpose($transfer, $commonTransfer->getPurpose());
        }

        $transfer->setCancelable($commonTransfer->isCancelable());
        if ($transfer instanceof TransferOut || $transfer instanceof TransferInternal) {
            $transfer->setAutoCurrencyConvert($commonTransfer->shouldAutoConvertCurrency());
        }
        if ($transfer instanceof TransferInternal) {
            $transfer->setPaymentCardDebitAllowed($commonTransfer->shouldAutoChargeRelatedCard());
            $transfer->setAutoProcessToDone($commonTransfer->isAutoProcessToDone());
        }

        if ($transfer instanceof TransferInternal) {
            $this->mapFromCommonPassword($transfer, $commonTransfer);
            if ($commonTransfer->getReserveUntil() !== null) {
                $transfer->setReserveUntil($commonTransfer->getReserveUntil());
            }
        } elseif ($transfer instanceof TransferOutBank) {
            $transfer->setChargeType($commonTransfer->getChargeType());
            $transfer->setRequestedBankKey($commonTransfer->getBankKey());
            if ($commonTransfer->getUrgency() === CommonTransfer::URGENCY_STANDARD) {
                $transfer->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_NOT_URGENT);
            } elseif ($commonTransfer->getUrgency() === CommonTransfer::URGENCY_URGENT) {
                $transfer->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_URGENT);
            } elseif ($commonTransfer->getUrgency() === CommonTransfer::URGENCY_VERY_URGENT) {
                $transfer->setRoutingParameter(TransferOutBank::ROUTING_PARAMETER_VERY_URGENT);
            }
        }

        $transfer->setValidationContext($commonTransfer->getValidationContext());

        $this->logger->info('Mapped from common transfer', ['transfer' => $transfer]);

        return $transfer;
    }

    private function createTransferFromCommonBeneficiary(Beneficiary $beneficiary): Transfer
    {
        if (count($beneficiary->getAccounts()) !== 1) {
            throw new \RuntimeException('Only one Account has to be provided');
        }

        if (!isset($this->accountMappers[$beneficiary->getType()])) {
            throw new \RuntimeException('Unknown account type');
        }

        return $this->accountMappers[$beneficiary->getType()]->mapFromCommonAccount($beneficiary);
    }

    private function mapFromCommonFinalBeneficiary(FinalBeneficiary $commonFinalBeneficiary): PartyPerson
    {
        $code = null;
        if ($commonFinalBeneficiary->getIdentifiers() !== null) {
            $code = $this->identifiersMapper->getCodeFromCommonIdentifiers($commonFinalBeneficiary->getIdentifiers());
        }

        return new PartyPerson($commonFinalBeneficiary->getName(), $code);
    }

    private function mapFromCommonPayer(Transfer $transfer, Payer $payer): void
    {
        foreach ($this->payerMappers as $payerMapper) {
            if ($payerMapper->supports($transfer, $payer)) {
                $payerMapper->mapFromCommonPayer($transfer, $payer);
            }
        }
    }

    /**
     * @param Transfer $transfer
     * @param Purpose $purpose
     *
     * @return Purpose
     */
    private function mapFromCommonPurpose(Transfer $transfer, Purpose $purpose)
    {
        $transfer->setDetails($purpose->getDetails());
        $transfer->setReferenceNumber($purpose->getReference());

        if ($purpose->getPurposeCode() !== null) {
            $transfer->setPurposeCode($purpose->getPurposeCode());
        }

        if ($transfer instanceof TransferOut) {
            if ($purpose->getDetailsOptions() !== null) {
                $transfer->setPreserveDetails($purpose->getDetailsOptions()->isPreserved());
            }
            if ($transfer instanceof TransferOutBank) {
                $transfer->setOcrCode($purpose->getOcrCode());
                $transfer->setVoCode($purpose->getVoCode());

            }
        }

        if ($purpose->getCode() !== null) {
            if (isset($this->purposeMap[$purpose->getCode()])) {
                $transfer->setPurpose($this->purposeMap[$purpose->getCode()]);
            } else {
                throw new \RuntimeException('Unknown purpose code');
            }
        }

        return $purpose;
    }

    /**
     * @param Transfer $transfer
     * @param CommonTransfer $commonTransfer
     *
     * @throws \RuntimeException
     */
    private function mapFromCommonNotifications(Transfer $transfer, CommonTransfer $commonTransfer)
    {
        foreach ($commonTransfer->getNotifications() as $notification) {
            if ($notification->getType() === Notification::TYPE_DONE) {
                $beneficiaryEmail = new TransferBeneficiaryEmail();
                $beneficiaryEmail->setTransfer($transfer);
                $beneficiaryEmail->setEmail($notification->getEmail());
                $beneficiaryEmail->setLanguage($notification->getLocale());
                $this->transferBeneficiaryEmailManager->persistTransferBeneficiaryEmail($beneficiaryEmail);
            } else {
                throw new \RuntimeException('Unsupported notification type');
            }
        }
    }

    /**
     * @param TransferInternal $transfer
     * @param CommonTransfer $commonTransfer
     */
    private function mapFromCommonPassword(TransferInternal $transfer, CommonTransfer $commonTransfer)
    {
        $commonTransferPassword = $commonTransfer->getPassword();
        if ($commonTransferPassword !== null) {
            $transferPassword = (new TransferPassword())
                ->setPassword($commonTransferPassword->getValue()->get())
                ->setTransfer($transfer)
            ;
            $this->transferPasswordManager->persistTransferPassword($transferPassword);
        }
    }
}
