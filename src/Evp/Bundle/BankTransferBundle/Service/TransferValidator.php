<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus;
use Evp\Bundle\BankTransferBundle\Entity\TransferInInterface;
use Evp\Bundle\BankTransferBundle\Service\Validation\ValidationHelper;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class TransferValidator
{
    private const SINGLE_VIOLATION_BY_MESSAGE_GROUP = [
        TransferFailureStatus::CODE_DETAILS_TOO_LONG,
        TransferFailureStatus::CODE_DETAILS_INVALID_CHARACTERS,
        TransferFailureStatus::CODE_PAYMENT_TO_SAME_ACCOUNT,
        TransferFailureStatus::CODE_TRANSFER_AMOUNT_NOT_POSITIVE,
        TransferFailureStatus::CODE_TRANSACTION_LIMIT_EXCEEDED,
        TransferFailureStatus::CODE_CLIENT_NOT_IDENTIFIED,
    ];

    private TransferStatusManager $transferStatusManager;
    private ValidatorInterface $validator;

    public function __construct(
        TransferStatusManager $transferStatusManager,
        ValidatorInterface $validator
    ) {
        $this->transferStatusManager = $transferStatusManager;
        $this->validator = $validator;
    }

    public function validate(Transfer $transfer): bool
    {
        $validationGroups = $this->getValidationGroups($transfer);

        /** @var ConstraintViolation[] $violations */
        $violations = $this->validator->validate($transfer, null, $validationGroups);
        if (count($violations) > 0) {
            $failureStatus = $this->resolveTransferFailureStatus($violations);

            $this->transferStatusManager->markAsFailed($transfer, $failureStatus);

            return false;
        }

        return true;
    }

    /**
     * @param Transfer $transfer
     *
     * @return string[]
     */
    private function getValidationGroups(Transfer $transfer): array
    {
        $validationGroups = [];
        $defaultValidationGroups = [
            'managementApi',
            'insert',
            'money',
        ];

        if (!$transfer instanceof TransferInInterface) {
            $validationGroups[] = 'transferOut';
        }

        $transferContextGroups = ValidationHelper::getValidationGroups($transfer);
        if ($transferContextGroups !== null) {
            $validationGroups = array_merge($validationGroups, $transferContextGroups);
        } else {
            $validationGroups[] = 'Default';
        }

        return array_unique(array_merge($defaultValidationGroups, $validationGroups));
    }

    private function doesViolationMessageMatchAgainstGroup(string $violationMessage, array $group): bool
    {
        return in_array($violationMessage, $group, true);
    }

    /**
     * @param ConstraintViolationList $violations
     *
     * @return TransferFailureStatus|null
     */
    public function resolveTransferFailureStatus(ConstraintViolationList $violations): ?TransferFailureStatus
    {
        $firstViolationMessage = $violations[0]->getMessage();

        if (
            $this->doesViolationMessageMatchAgainstGroup(
                $firstViolationMessage,
                self::SINGLE_VIOLATION_BY_MESSAGE_GROUP,
            )
        ) {
            return new TransferFailureStatus($firstViolationMessage);
        }

        $validationViolations = [];
        foreach ($violations as $violation) {
            $validationViolations[] = $violation->getMessage();
        }

        return new TransferFailureStatus(
            TransferFailureStatus::CODE_VALIDATION,
            json_encode($validationViolations)
        );
    }
}
