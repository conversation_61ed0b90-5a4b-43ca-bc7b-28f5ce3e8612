<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Service\Validation;

use Evp\Bundle\BankTransferBundle\Entity\Transfer;

class ValidationHelper
{
    public static function getValidationGroups(Transfer $transfer): ?array
    {
        $validationContext = $transfer->getValidationContext();

        return $validationContext ? $validationContext->getValidationGroups() : null;
    }

    /**
     * Checks if the transfer is processable based on validation groups
     *
     * @param Transfer $transfer
     * @param array|null $validationGroups
     * @return bool
     */
    public static function isProcessable(Transfer $transfer, ?array $validationGroups): bool
    {
        $transferValidationGroups = self::getValidationGroups($transfer);

        if ($transferValidationGroups === null || $validationGroups === null || count($validationGroups) === 0) {
            return true;
        }

        return count(array_intersect($validationGroups, $transferValidationGroups)) > 0;
    }
}
