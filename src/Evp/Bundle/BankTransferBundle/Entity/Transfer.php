<?php

namespace Evp\Bundle\BankTransferBundle\Entity;

use BeSimple\SoapBundle\ServiceDefinition\Annotation as Soap;
use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission;
use Evp\Bundle\BankTransferBundle\DTO\ValidationContextDTO;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party;
use Evp\Bundle\BankTransferBundle\Exception\TransferStatusNotFound;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Component\Money\Money;

abstract class Transfer
{
    const REFERENCE_REGEXP = '/^([a-z]+)\:([a-z]{4})\:(.+)$/i';
    const MAX_SUPPORTED_AMOUNT = '**********.999999';

    /**
     * Available priorities
     */
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Available transfer statuses
     */
    const STATUS_NEW = 'new';
    const STATUS_PREPARED = 'prepared';
    const STATUS_CANCELED = 'canceled';
    const STATUS_SIGNED = 'signed';
    const STATUS_WAITING_FUNDS = 'waitingFunds';
    const STATUS_RESERVED = 'reserved';
    const STATUS_FREEZED = 'freezed';
    const STATUS_READY = 'ready';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_DONE = 'done';
    const STATUS_FAILED = 'failed';

    const REFERENCE_TYPE_NATURAL = 'natural';
    const REFERENCE_TYPE_JURIDICAL = 'juridical';

    const REFERENCE_IDENTIFIER_CUST = 'CUST';
    const REFERENCE_IDENTIFIER_COID = 'COID';
    const REFERENCE_IDENTIFIER_NIDN = 'NIDN';
    const REFERENCE_IDENTIFIER_ROIK = 'ROIK';

    public static $statuses = [
        self::STATUS_NEW => self::STATUS_NEW,
        self::STATUS_PREPARED => self::STATUS_PREPARED,
        self::STATUS_CANCELED => self::STATUS_CANCELED,
        self::STATUS_SIGNED => self::STATUS_SIGNED,
        self::STATUS_WAITING_FUNDS => self::STATUS_WAITING_FUNDS,
        self::STATUS_RESERVED => self::STATUS_RESERVED,
        self::STATUS_FREEZED => self::STATUS_FREEZED,
        self::STATUS_READY => self::STATUS_READY,
        self::STATUS_PROCESSING => self::STATUS_PROCESSING,
        self::STATUS_SUCCESS => self::STATUS_SUCCESS,
        self::STATUS_DONE => self::STATUS_DONE,
        self::STATUS_FAILED => self::STATUS_FAILED,
    ];

    /**
     * @var array
     */
    protected static $pendingStatuses = [
        // Prepared, no reservation
        Transfer::STATUS_NEW,
        Transfer::STATUS_PREPARED,
        Transfer::STATUS_SIGNED,
        Transfer::STATUS_WAITING_FUNDS,
        // Reservation, bank internal account entries are made
        Transfer::STATUS_READY,
        Transfer::STATUS_RESERVED,
        Transfer::STATUS_FREEZED,
    ];

    protected $referenceTypes = [
        self::REFERENCE_TYPE_NATURAL,
        self::REFERENCE_TYPE_JURIDICAL,
    ];

    /**
     * Statuses to be included when counting transfer limits
     *
     * @var array
     */
    public static $statusListForLimits = self::STATUS_FOR_LIMITS;
    public const STATUS_FOR_LIMITS = [
        self::STATUS_READY,
        self::STATUS_PROCESSING,
        self::STATUS_SUCCESS,
        self::STATUS_DONE,
        self::STATUS_RESERVED,
        self::STATUS_FREEZED,
        self::STATUS_NEW,
        self::STATUS_PREPARED,
        self::STATUS_SIGNED,
        self::STATUS_WAITING_FUNDS,
    ];

    /**
     * Available risk levels
     */
    const RISK_LEVEL_LOW = 'low';
    const RISK_LEVEL_MEDIUM = 'medium';
    const RISK_LEVEL_HIGH = 'high';

    /**
     * Available transfer purposes
     */
    const PURPOSE_MACRO = 'macro';          // from mokejimai.lt + wallet_beneficiary when project is mokejimai.lt
    const PURPOSE_MICRO = 'micro';          // from mokejimai.lt
    const PURPOSE_PARTNER_BONUS = 'partner_bonus'; // from mokejimai.lt
    const PURPOSE_FILL = 'fill';            // when uploaded funds by creating request (client herself)
    const PURPOSE_CASHBACK = 'cashback';    // returning money for some payment
    const PURPOSE_CASH_IN = 'cash_in';     // cash in - operation with cash
    const PURPOSE_FILL_CASH_IN = 'fill_cash_in';     // fill cash in - operation with cash in fills
    const PURPOSE_CASH_OUT = 'cash_out';   // cash out - operation with cash
    const PURPOSE_WALLET = 'wallet';        // from wallet, when funds are directly to project
    const PURPOSE_WALLET_BENEFICIARY = 'wallet_beneficiary';    // from wallet, when funds are to custom beneficiary
    const PURPOSE_DEPOSIT_FROM_CARD = 'deposit_from_card'; //deposit from payment card
    const PURPOSE_PLAIS = 'plais'; //PLAIS transfers
    const PURPOSE_MOBILE_PAYMENT = 'mobile_payment'; //MainIBAN transfers
    const PURPOSE_AML_REFUND = 'aml_refund';
    const PURPOSE_DEBT_AUTOMATIC = 'debt_automatic'; // Any automatic debt transfer initiated by government authority

    public const PURPOSES_CASH_IN = [
        Transfer::PURPOSE_CASH_IN,
        Transfer::PURPOSE_FILL_CASH_IN,
    ];
    public const PURPOSES_CASH_OUT = [
        Transfer::PURPOSE_CASH_OUT,
    ];

    /**
     * @var int
     */
    private $version;

    /**
     * @var int
     * @Soap\ComplexType("int", nillable=true)
     */
    protected $parentId;

    /**
     * @var \DateTime
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $createdAt;

    /**
     * @var \DateTime
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $updatedAt;

    /**
     * @var \DateTime
     * @Soap\ComplexType("date", nillable=true)
     */
    protected $date;

    /**
     * @var \DateTime
     * @Soap\ComplexType("date")
     */
    protected $operationDate;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $number;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $numberBackup;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $priority;

    /**
     * @var string
     */
    protected $amount;

    /**
     * @var string
     */
    protected $amountCurrency;

    /**
     * @var Money
     * @Soap\ComplexType("Evp\Component\Money\Money", nillable=true)
     */
    protected $amountMoney;

    /**
     * @var string|null
     * @Soap\ComplexType("string")
     */
    protected $details;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $referenceNumber;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $referenceToPayer;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $referenceToBeneficiary;

    /**
     * @var string
     */
    protected $riskLevel;

    /**
     * @var string
     */
    protected $riskLevelComment;

    /**
     * @var int
     * @Soap\ComplexType("int", nillable=true)
     */
    protected $id;

    /**
     * @var Party
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party")
     */
    protected $payer;

    /**
     * @var Party
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party", nillable=true)
     */
    protected $primaryPayer;

    /**
     * @var Party
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party")
     */
    protected $beneficiary;

    /**
     * @var Party
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferParty\Party", nillable=true)
     */
    protected $finalBeneficiary;

    /**
     * @var Client
     */
    protected $client;

    /**
     * Getters and Setters in classes which uses this field
     * @var Account
     * @Soap\ComplexType("Evp\Bundle\BankAccountBundle\Entity\Account", nillable=true, readonly=true)
     */
    protected $creditAccount;

    /**
     * Getters and Setters in classes which uses this field
     * @var Account
     */
    protected $debitAccount;

    /**
     * @var Account
     */
    protected $creditCommissionAccount;

    /**
     * @var TransferRequest|null
     */
    protected $transferRequest;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $transferAmlDetailsHash;

    /**
     * @var string
     */
    protected $archiveNumber;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $status;

    /**
     * @var TransferFailureStatus
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus", nillable=true)
     */
    protected $failureStatus;

    /**
     * @var TransferAccountingTransaction
     */
    protected $transferAccountingTransactions;

    /**
     * @var TransferStatus[]
     */
    protected $transferStatuses;

    /**
     * @var ArrayCollection|TransferSignature[]
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferSignature[]", nillable=true)
     */
    protected $transferSignatures;

    /**
     * @var SignPermission
     */
    protected $signPermission;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $hasCancelPermission;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $hasSignPermission;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $hasEditPermission;

    /**
     * @var int
     * @Soap\ComplexType("int", nillable=true)
     */
    protected $slaTime;

    /**
     * Automatically formed transfers can be signed with sign permission which has forAutomaticTransfers set to true
     * @var bool
     * @Soap\ComplexType("boolean")
     */
    protected $formedAutomatically;

    /**
     * Transfer purpose like macro payment
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $purpose;

    /**
     * Date and time when the transfer is done
     * @var \DateTime
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $statusDoneDate;

    /**
     * @var \DateTime
     * @Soap\ComplexType("dateTime", nillable=true)
     */
    protected $lastStatusDate;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $comment;

    /**
     * @var Transfer|null
     */
    protected $transferThatThisTransferReplaced;

    /**
     * @var Transfer|null
     */
    protected $transferThatReplacedThisTransfer;

    protected $inspection;

    /**
     * @var bool
     * @Soap\ComplexType("boolean")
     */
    protected $cancelable;

    /**
     * @internal
     *  Instead of accessors in this class, use TransferBeneficiaryEmailManager.
     *
     *  The Doctrine relation between Transfer and TransferBeneficiaryEmail is no longer bi-directional,
     *  only TransferBeneficiaryEmail points to Transfer. We only have this property now for BC purposes,
     *  in order to have TransferBeneficiaryEmail in Transfer object when receiving a Transfer via SOAP. This
     *  property is no longer populated by this project.
     *
     * @var TransferBeneficiaryEmail
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferBeneficiaryEmail", nillable=true)
     */
    protected $beneficiaryEmail;

    /**
     * @var bool
     */
    protected $instant;

    /**
     * @var TransferCurrencyConvert
     * @Soap\ComplexType("Evp\Bundle\BankTransferBundle\Entity\TransferCurrencyConvert", nillable=true)
     */
    protected $relatedCurrencyConvertTransfer;

    /**
     * @var bool
     * @Soap\ComplexType("boolean", nillable=true)
     */
    protected $amlFormRequested;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $purposeCode;

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    protected $hash;

    /**
     * @var ArrayCollection|BankTransferData[]
     */
    protected $bankTransferData;

    /**
     * @var TransferDevice[]
     */
    protected $transferDevices;

    /**
     * @var ?ValidationContextDTO
     */
    protected $validationContext;

    /**
     * @return string
     */
    abstract public function getType();

    private static $finalStatuses = [
        self::STATUS_CANCELED => self::STATUS_CANCELED,
        self::STATUS_DONE => self::STATUS_DONE,
        self::STATUS_FAILED => self::STATUS_FAILED,
    ];

    /**
     * Transfer constructor
     */
    public function __construct()
    {
        $this->transferAccountingTransactions = new ArrayCollection();
        $this->transferSignatures = new ArrayCollection();
        $this->transferStatuses = new ArrayCollection();
        $this->transferDevices = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->operationDate = new \DateTime();
        $this->priority = self::PRIORITY_NORMAL;
        $this->hasCancelPermission = false;
        $this->hasSignPermission = false;
        $this->hasEditPermission = false;
        $this->formedAutomatically = false;
        $this->cancelable = true;
        $this->instant = false;
        $this->amlFormRequested = false;
        $this->bankTransferData = new ArrayCollection();
        $this->validationContext = null;
    }

    /**
     * Get version
     *
     * @return int
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param int $id
     *
     * @return $this
     */
    public function setParentId($id)
    {
        $this->parentId = $id;

        return $this;
    }

    /**
     * Get parent id
     *
     * @return int
     */
    public function getParentId()
    {
        return $this->parentId;
    }

    /**
     * @param \DateTime $createdAt
     *
     * @return $this
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @return int
     */
    public function getCreatedAtInMilliseconds()
    {
        return $this->createdAt->getTimestamp() * 1000;
    }

    /**
     * @param \DateTime $statusDoneDate
     * @return $this
     */
    public function setStatusDoneDate($statusDoneDate)
    {
        $this->statusDoneDate = $statusDoneDate;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStatusDoneDate()
    {
        return $this->statusDoneDate;
    }

    /**
     * @param \DateTime $lastStatusDate
     * @return $this
     */
    public function setLastStatusDate($lastStatusDate)
    {
        $this->lastStatusDate = $lastStatusDate;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getLastStatusDate()
    {
        return $this->lastStatusDate;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set date
     *
     * @param \DateTime $date
     * @return $this
     */
    public function setDate($date)
    {
        $this->date = $date;
        return $this;
    }

    /**
     * Get date
     *
     * @return \DateTime
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * Set number
     *
     * @param string $number
     * @return $this
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return string
     */
    public function getNumber()
    {
        return $this->number;
    }


    /**
     * @param string $number
     * @return $this
     */
    public function setNumberBackup($number)
    {
        $this->numberBackup = $number;

        return $this;
    }

    /**
     * Get number backup
     *
     * @return string
     */
    public function getNumberBackup()
    {
        return $this->numberBackup;
    }

    /**
     * Set priority
     *
     * @param string $priority
     * @return $this
     */
    public function setPriority($priority)
    {
        $this->priority = $priority;

        return $this;
    }

    /**
     * Get priority
     *
     * @return string
     */
    public function getPriority()
    {
        return $this->priority;
    }

    /**
     * Get amount
     *
     * @return string
     */
    public function getAmount()
    {
        return $this->amountMoney === null ? null : $this->amountMoney->getAmount();
    }

    /**
     * Get amountCurrency
     *
     * @return string
     */
    public function getAmountCurrency()
    {
        return $this->amountMoney === null ? null : $this->amountMoney->getCurrency();
    }

    /**
     * Set amountMoney
     *
     * @param Money $amount
     *
     * @return $this
     */
    public function setAmountMoney(Money $amount)
    {
        $this->amountMoney = $amount;
        $this->amount = $this->getAmount();
        $this->amountCurrency = $this->getAmountCurrency();

        return $this;
    }

    /**
     * Get amountMoney
     *
     * @return Money
     */
    public function getAmountMoney()
    {
        return $this->amountMoney;
    }

    /**
     * Set details
     *
     * @param string|null $details
     *
     * @return $this
     */
    public function setDetails($details)
    {
        $this->details = $details;

        return $this;
    }

    /**
     * Get details
     *
     * @return string|null
     */
    public function getDetails()
    {
        return $this->details;
    }

    /**
     * Set referenceNumber
     *
     * @param string $referenceNumber
     *
     * @return $this
     */
    public function setReferenceNumber($referenceNumber)
    {
        $this->referenceNumber = $referenceNumber;

        return $this;
    }

    /**
     * Get referenceNumber
     *
     * @return string
     */
    public function getReferenceNumber()
    {
        return $this->referenceNumber;
    }

    /**
     * Set referenceToPayer
     *
     * @param string $referenceToPayer
     *
     * @return $this
     */
    public function setReferenceToPayer($referenceToPayer)
    {
        $this->referenceToPayer = $referenceToPayer;

        return $this;
    }

    /**
     * @param string $type
     * @param string $referenceIdentifier
     * @param string $code
     *
     * @return $this
     */
    public function setReferenceToPayerItems($type, $referenceIdentifier, $code)
    {
        $this->referenceToPayer = $type . ':' . $referenceIdentifier . ':' . $code;

        return $this;
    }

    /**
     * Get referenceToPayer
     *
     * @return string
     */
    public function getReferenceToPayer()
    {
        return $this->referenceToPayer;
    }

    public function getReferenceToPayerType()
    {
        return $this->getReferenceType($this->referenceToPayer);
    }

    public function getReferenceToPayerIdentifier()
    {
        return $this->getReferenceIdentifier($this->referenceToPayer);
    }

    public function getReferenceToPayerValue()
    {
        return $this->getReferenceValue($this->referenceToPayer);
    }

    /**
     * Set referenceToBeneficiary
     *
     * @param string $referenceToBeneficiary
     *
     * @return $this
     */
    public function setReferenceToBeneficiary($referenceToBeneficiary)
    {
        $this->referenceToBeneficiary = $referenceToBeneficiary;

        return $this;
    }

    /**
     * Get referenceToBeneficiary
     *
     * @return string
     */
    public function getReferenceToBeneficiary()
    {
        return $this->referenceToBeneficiary;
    }

    public function getReferenceToBeneficiaryType()
    {
        return $this->getReferenceType($this->referenceToBeneficiary);
    }

    public function getReferenceToBeneficiaryIdentifier()
    {
        return $this->getReferenceIdentifier($this->referenceToBeneficiary);
    }

    public function getReferenceToBeneficiaryValue()
    {
        return $this->getReferenceValue($this->referenceToBeneficiary);
    }

    private function getReferenceType($reference)
    {
        if (preg_match(self::REFERENCE_REGEXP, $reference, $matches)) {
            if (in_array($matches[1], $this->referenceTypes)) {
                return mb_strtolower($matches[1]);
            }
        }

        return null;
    }

    private function getReferenceIdentifier($reference)
    {
        if (preg_match(self::REFERENCE_REGEXP, $reference, $matches)) {
            return mb_strtoupper($matches[2]);
        }

        return null;
    }

    private function getReferenceValue($reference)
    {
        if (preg_match(self::REFERENCE_REGEXP, $reference, $matches)) {
            return $matches[3];
        }

        return null;
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return $this
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Set payer
     *
     * @param Party $payer
     *
     * @return $this
     */
    public function setPayer(Party $payer)
    {
        $this->payer = $payer;

        return $this;
    }

    /**
     * Get payer
     *
     * @return Party
     */
    public function getPayer()
    {
        return $this->payer;
    }

    /**
     * Set primaryPayer
     *
     * @param Party $primaryPayer
     * @return $this
     */
    public function setPrimaryPayer(Party $primaryPayer = null)
    {
        $this->primaryPayer = $primaryPayer;

        return $this;
    }

    /**
     * Get primaryPayer
     *
     * @return Party
     */
    public function getPrimaryPayer()
    {
        return $this->primaryPayer;
    }

    /**
     * Set beneficiary
     *
     * @param Party $beneficiary
     *
     * @return $this
     */
    public function setBeneficiary(Party $beneficiary)
    {
        $this->beneficiary = $beneficiary;

        return $this;
    }

    /**
     * Get beneficiary
     *
     * @return Party
     */
    public function getBeneficiary()
    {
        return $this->beneficiary;
    }

    /**
     * @param Party $finalBeneficiary
     * @return $this
     */
    public function setFinalBeneficiary(Party $finalBeneficiary)
    {
        $this->finalBeneficiary = $finalBeneficiary;

        return $this;
    }

    /**
     * Get finalBeneficiary
     *
     * @return Party
     */
    public function getFinalBeneficiary()
    {
        return $this->finalBeneficiary;
    }

    /**
     * @param Client $client
     * @return $this
     */
    public function setClient(Client $client)
    {
        $this->client = $client;

        return $this;
    }

    /**
     * @return Client
     */
    public function getClient()
    {
        return $this->client;
    }

    /**
     * @param TransferRequest $transferRequest
     *
     * @return $this
     */
    public function setTransferRequest(TransferRequest $transferRequest)
    {
        $this->transferRequest = $transferRequest;

        return $this;
    }

    /**
     * Get transferRequest
     *
     * @return TransferRequest|null
     */
    public function getTransferRequest()
    {
        return $this->transferRequest ?? null;
    }

    /**
     * Set transferAmlDetailsHash
     *
     * @param string
     */
    public function setTransferAmlDetailsHash($hash)
    {
        $this->transferAmlDetailsHash = $hash;
    }

    /**
     * Get transferAmlDetailsHash
     *
     * @return string
     */
    public function getTransferAmlDetailsHash()
    {
        return $this->transferAmlDetailsHash;
    }

    /**
     * @param \DateTime $operationDate
     *
     * @return $this
     */
    public function setOperationDate($operationDate)
    {
        $this->operationDate = $operationDate;

        return $this;
    }

    /**
     * Get operationDate
     *
     * @return \DateTime
     */
    public function getOperationDate()
    {
        return $this->operationDate;
    }

    /**
     * Set archiveNumber
     *
     * @param string $archiveNumber
     *
     * @return $this
     */
    public function setArchiveNumber($archiveNumber)
    {
        $this->archiveNumber = $archiveNumber;

        return $this;
    }

    /**
     * Get archiveNumber
     *
     * @return string
     */
    public function getArchiveNumber()
    {
        return $this->archiveNumber;
    }

    /**
     * @param string $riskLevel
     * @return $this
     */
    public function setRiskLevel($riskLevel)
    {
        $this->riskLevel = $riskLevel;
        return $this;
    }

    /**
     * Get riskLevel
     *
     * @return string
     */
    public function getRiskLevel()
    {
        return $this->riskLevel;
    }

    /**
     * Set riskLevelComment
     *
     * @param string $riskLevelComment
     */
    public function setRiskLevelComment($riskLevelComment)
    {
        $this->riskLevelComment = $riskLevelComment;
    }

    /**
     * Get riskLevelComment
     *
     * @return string
     */
    public function getRiskLevelComment()
    {
        return $this->riskLevelComment;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;
        $sendNotification = (
            $this->getTransferRequest() !== null
            && $this->getTransferRequest()->isNotificationForStatusNeeded($status)
        );
        $this->addTransferStatus(new TransferStatus($status, !$sendNotification));

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Checks if transfer status is STATUS_NEW
     *
     * @return bool
     */
    public function isNew()
    {
        return $this->getStatus() == self::STATUS_NEW;
    }

    /**
     * Checks if transfer status is STATUS_PREPARED
     *
     * @return bool
     */
    public function isPrepared()
    {
        return $this->getStatus() == self::STATUS_PREPARED;
    }

    /**
     * Checks if transfer status is STATUS_CANCELED
     *
     * @return bool
     */
    public function isCanceled()
    {
        return $this->getStatus() == self::STATUS_CANCELED;
    }

    /**
     * Checks if transfer status is STATUS_SIGNED
     *
     * @return bool
     */
    public function isSigned()
    {
        return $this->getStatus() == self::STATUS_SIGNED;
    }

    /**
     * Checks if transfer status is STATUS_READY
     *
     * @return bool
     */
    public function isReady()
    {
        return $this->getStatus() == self::STATUS_READY;
    }

    /**
     * Checks if transfer status is STATUS_PROCESSING
     *
     * @return bool
     */
    public function isProcessing()
    {
        return $this->getStatus() == self::STATUS_PROCESSING;
    }

    /**
     * Checks if transfer status is STATUS_SUCCESS
     *
     * @return bool
     */
    public function isSuccessful()
    {
        return $this->getStatus() == self::STATUS_SUCCESS;
    }

    /**
     * Checks if transfer status is STATUS_WAITING_FUNDS
     *
     * @return bool
     */
    public function isWaitingFunds()
    {
        return $this->getStatus() == self::STATUS_WAITING_FUNDS;
    }

    /**
     * Checks if transfer status is STATUS_RESERVED
     *
     * @return bool
     */
    public function isReserved()
    {
        return $this->getStatus() == self::STATUS_RESERVED;
    }

    /**
     * Checks if transfer status is STATUS_FREEZED
     *
     * @return bool
     */
    public function isFreezed()
    {
        return $this->getStatus() == self::STATUS_FREEZED;
    }

    /**
     * Checks if transfer status is STATUS_DONE
     *
     * @return bool
     */
    public function isDone()
    {
        return $this->getStatus() == self::STATUS_DONE;
    }

    /**
     * Backup transfer number
     */
    public function backupNumber()
    {
        if ($this->getNumber() === null) {
            return;
        }
        $this->setNumberBackup($this->getNumber());
        $this->setNumber(null);
    }

    /**
     * Checks if transfer status is STATUS_FAILED
     *
     * @return bool
     */
    public function isFailed()
    {
        return $this->getStatus() == self::STATUS_FAILED;
    }

    /**
     * Set failureStatus
     *
     * @param TransferFailureStatus $failureStatus
     * @return $this
     */
    public function setFailureStatus(TransferFailureStatus $failureStatus)
    {
        $this->failureStatus = $failureStatus;
        return $this;
    }

    /**
     * Get failureStatus
     *
     * @return TransferFailureStatus
     */
    public function getFailureStatus()
    {
        return $this->failureStatus;
    }

    /**
     * Add transferAccountingTransactions
     *
     * @param TransferAccountingTransaction $transferAccountingTransaction
     */
    public function addTransferAccountingTransaction(TransferAccountingTransaction $transferAccountingTransaction)
    {
        $this->transferAccountingTransactions[] = $transferAccountingTransaction;
        $transferAccountingTransaction->setTransfer($this);
    }

    /**
     * @param TransferAccountingTransaction $transferAccountingTransaction
     */
    public function removeTransferAccountingTransaction(TransferAccountingTransaction $transferAccountingTransaction)
    {
        $this->transferAccountingTransactions->removeElement($transferAccountingTransaction);
    }

    /**
     * Get transferAccountingTransactions
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getTransferAccountingTransactions()
    {
        return $this->transferAccountingTransactions;
    }

    /**
     * @param TransferStatus $transferStatus
     *
     * @return $this
     */
    public function addTransferStatus(TransferStatus $transferStatus)
    {
        $this->transferStatuses[] = $transferStatus;
        $transferStatus->setTransfer($this);

        return $this;
    }

    /**
     * Get transferStatuses
     *
     * @return TransferStatus[]
     */
    public function getTransferStatuses()
    {
        return $this->transferStatuses;
    }

    /**
     * Clears transfer statuses
     */
    public function clearTransferStatuses()
    {
        $this->transferStatuses->clear();
    }

    /**
     * Get transferStatus by status
     * Returns with greatest id
     *
     * @param string $status
     *
     * @return TransferStatus|null
     */
    public function getTransferStatus($status)
    {
        $result = null;
        foreach ($this->getTransferStatuses() as $transferStatus) {
            if (
                $transferStatus->getStatus() === $status &&
                ($result === null || $transferStatus->getId() > $result->getId())
            ) {
                $result = $transferStatus;
            }
        }
        return $result;
    }

    /**
     * Add AccountingTransaction
     *
     * @param AccountingTransaction $transaction
     */
    public function addAccountingTransaction(AccountingTransaction $transaction)
    {
        $this->addTransferAccountingTransaction(
            new TransferAccountingTransaction($transaction)
        );
    }

    /**
     * Get date when transfer was marked as ready
     *
     * @return \DateTime
     *
     * @throws \Exception
     */
    public function getReadyFromDate()
    {
        foreach ($this->transferStatuses as $status) {
            if ($status->isReady()) {
                return $status->getCreatedAt();
            }
        }
        throw new \Exception('Transfer status with STATUS_READY not found');
    }

    /**
     * Get date when transfer was marked as done
     *
     * @return \DateTime
     *
     * @throws \Exception
     */
    public function getDoneFromDate()
    {
        foreach ($this->transferStatuses as $status) {
            if ($status->isDone()) {
                return $status->getCreatedAt();
            }
        }
        throw new \Exception('Transfer status with STATUS_DONE not found');
    }

    /**
     * Get date when transfer was marked as specified status
     *
     * @param $status
     *
     * @return \DateTime
     *
     * @throws TransferStatusNotFound
     */
    public function getStatusFromDate($status)
    {
        foreach ($this->transferStatuses as $statusEntity) {
            if ($statusEntity->getStatus() === $status) {
                return $statusEntity->getCreatedAt();
            }
        }

        throw new TransferStatusNotFound('Transfer status with status ' . $status . ' not found');
    }

    /**
     * Get transfer waiting time in minutes
     *
     * @return number
     */
    public function getWaitingTime()
    {
        $diff = time() - $this->getReadyFromDate()->getTimestamp();
        return floor($diff / 60);
    }

    /**
     * @param TransferSignature $transferSignature
     * @return $this
     */
    public function addTransferSignature(TransferSignature $transferSignature)
    {
        $this->transferSignatures[] = $transferSignature;
        $transferSignature->addTransfer($this);

        return $this;
    }

    public function removeTransferSignature(TransferSignature $transferSignature)
    {
        $this->transferSignatures->removeElement($transferSignature);
        $transferSignature->removeTransfer($this);
        return $this;
    }

    /**
     * Get transferSignatures
     *
     * @return TransferSignature[]|ArrayCollection
     */
    public function getTransferSignatures()
    {
        return $this->transferSignatures;
    }

    /**
     * Clears transferSignatures
     */
    public function clearTransferSignatures()
    {
        return $this->transferSignatures->clear();
    }

    /**
     * Set sign permission
     *
     * @param SignPermission $permission
     *
     * @return $this
     */
    public function setSignPermission(SignPermission $permission)
    {
        $this->signPermission = $permission;

        return $this;
    }

    /**
     * Get sign permission
     *
     * @return SignPermission
     */
    public function getSignPermission()
    {
        return $this->signPermission;
    }

    /**
     * @param bool $hasCancelPermission
     * @return $this
     */
    public function setHasCancelPermission($hasCancelPermission)
    {
        $this->hasCancelPermission = $hasCancelPermission;

        return $this;
    }

    /**
     * @param bool $hasSignPermission
     *
     * @return $this
     */
    public function setHasSignPermission($hasSignPermission)
    {
        $this->hasSignPermission = $hasSignPermission;

        return $this;
    }


    /**
     * @param bool $hasEditPermission
     * @return $this
     */
    public function setHasEditPermission($hasEditPermission)
    {
        $this->hasEditPermission = $hasEditPermission;

        return $this;
    }

    /**
     * Gets hasCancelPermission
     *
     * @return bool
     */
    public function getHasCancelPermission()
    {
        return $this->hasCancelPermission;
    }

    /**
     * Gets hasEditPermission
     *
     * @return bool
     */
    public function getHasEditPermission()
    {
        return $this->hasEditPermission;
    }

    /**
     * Gets hasSignPermission
     *
     * @return bool
     */
    public function getHasSignPermission()
    {
        return $this->hasSignPermission;
    }

    /**
     * Set service-level agreement time
     *
     * @param int $slaTime
     *
     * @return $this
     */
    public function setSlaTime($slaTime)
    {
        $this->slaTime = $slaTime;

        return $this;
    }

    /**
     * Get service-level agreement time
     *
     * @return int
     */
    public function getSlaTime()
    {
        return $this->slaTime;
    }

    /**
     * @param bool $formedAutomatically
     *
     * @return $this
     */
    public function setFormedAutomatically($formedAutomatically)
    {
        $this->formedAutomatically = $formedAutomatically;

        return $this;
    }

    /**
     * Gets formedAutomatically
     *
     * @return bool
     */
    public function isFormedAutomatically()
    {
        return $this->formedAutomatically;
    }

    /**
     * @param string $purpose
     *
     * @return $this
     */
    public function setPurpose($purpose)
    {
        $this->purpose = $purpose;

        return $this;
    }

    /**
     * @param string $comment
     *
     * @return $this
     */
    public function setComment($comment)
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * Get comment
     *
     * @return string
     */
    public function getComment()
    {
        return $this->comment;
    }

    /**
     * Getter of Purpose
     *
     * @return string
     */
    public function getPurpose()
    {
        return $this->purpose;
    }

    /**
     * Setter of transferThatThisTransferReplaced
     *
     * @param Transfer $transferThatThisTransferReplaced
     */
    public function setTransferThatThisTransferReplaced($transferThatThisTransferReplaced)
    {
        $this->transferThatThisTransferReplaced = $transferThatThisTransferReplaced;
    }

    /**
     * Getter of transferThatThisTransferReplaced
     *
     * @return Transfer
     */
    public function getTransferThatThisTransferReplaced()
    {
        return $this->transferThatThisTransferReplaced;
    }

    /**
     * Setter of transferThatReplacedThisTransfer
     *
     * @param Transfer
     * @return $this
     */
    public function setTransferThatReplacedThisTransfer($replacedTransfer)
    {
        $this->transferThatReplacedThisTransfer = $replacedTransfer;
        return $this;
    }

    /**
     * Getter of transferThatReplacedThisTransfer
     *
     * @return Transfer
     */
    public function getTransferThatReplacedThisTransfer()
    {
        return $this->transferThatReplacedThisTransfer;
    }

    /**
     * @param mixed $cancelable
     *
     * @return $this
     */
    public function setCancelable($cancelable)
    {
        $this->cancelable = $cancelable;

        return $this;
    }

    /**
     * Get cancelable
     *
     * @return mixed
     */
    public function getCancelable()
    {
        return $this->cancelable;
    }

    /**
     * @deprecated see the note on the property.
     * @see $beneficiaryEmail
     *
     * @param TransferBeneficiaryEmail
     * @return Transfer
     */
    public function setBeneficiaryEmail($beneficiaryEmail)
    {
        $this->beneficiaryEmail = $beneficiaryEmail;
        return $this;
    }

    /**
     * @deprecated see the note on the property
     *
     * @return TransferBeneficiaryEmail
     */
    public function getBeneficiaryEmail()
    {
        return $this->beneficiaryEmail;
    }

    /**
     * On pre persist and on pre update
     */
    public function onPreSave()
    {
        if ($this->amountMoney !== null) {
            $this->amount = $this->amountMoney->formatAmount(6);
            $this->amountCurrency = $this->amountMoney->getCurrency();
        } else {
            throw new \Exception('Amount money cannot be null');
//            $this->amountCurrency = null;
        }
    }

    /**
     * On post load
     */
    public function onPostLoad()
    {
        if ($this->amount !== null && $this->amountCurrency !== null) {
            $this->amountMoney = new Money($this->amount, $this->amountCurrency);
        }
    }

    public function markAsInstant()
    {
        $this->instant = true;
        return $this;
    }

    public function isInstant()
    {
        return $this->instant;
    }

    /**
     * To string
     *
     * @return string
     */
    public function __toString()
    {
        return (string) implode(' | ', [
            'ID:' . $this->getId(),
            'B:' . (string) $this->getBeneficiary(),
            'P:' . (string) $this->getPayer(),
            'A:' . (string) $this->getAmountMoney(),
            'OD:' . ($this->getOperationDate() ? $this->getOperationDate()->format('Y-m-d') : ''),
        ]);
    }

    /**
     * @param TransferCurrencyConvert $relatedCurrencyConvertTransfer
     * @return $this
     */
    public function setRelatedCurrencyConvertTransfer(TransferCurrencyConvert $relatedCurrencyConvertTransfer)
    {
        $this->relatedCurrencyConvertTransfer = $relatedCurrencyConvertTransfer;

        return $this;
    }

    /**
     * Getter of RelatedCurrencyConvertTransfer
     *
     * @return TransferCurrencyConvert|null
     */
    public function getRelatedCurrencyConvertTransfer()
    {
        return $this->relatedCurrencyConvertTransfer;
    }

    /**
     * @return array
     */
    public static function getPendingStatuses()
    {
        return self::$pendingStatuses;
    }

    /**
     * @return array
     */
    public static function getStatuses()
    {
        return self::$statuses;
    }

    /**
     * @param bool $amlFormRequested
     *
     * @return $this
     */
    public function setAmlFormRequested($amlFormRequested)
    {
        $this->amlFormRequested = $amlFormRequested;

        return $this;
    }

    /**
     * @return bool
     */
    public function getAmlFormRequested()
    {
        return $this->amlFormRequested;
    }

    /**
     * @return string
     */
    public function getPurposeCode()
    {
        return $this->purposeCode;
    }

    /**
     * @param string $purposeCode
     *
     * @return $this
     */
    public function setPurposeCode($purposeCode)
    {
        $this->purposeCode = $purposeCode;

        return $this;
    }

    /**
     * @return array
     */
    public static function getFinalStatuses()
    {
        return self::$finalStatuses;
    }

    /**
     * @return string|null
     */
    public function getHash()
    {
        return $this->hash;
    }

    /**
     * @param string $hash
     * @return $this
     */
    public function setHash(string $hash)
    {
        $this->hash = $hash;
        return $this;
    }


    /**
     * @param BankTransferData $bankTransferData
     */
    public function removeBankTransferData(BankTransferData $bankTransferData)
    {
        $this->bankTransferData->removeElement($bankTransferData);
    }

    /**
     * Get bankTransferData
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getBankTransferData()
    {
        return $this->bankTransferData;
    }

    /**
     * Get transferDevice
     *
     * @return array
     */
    public function getTransferDevices()
    {
        return $this->transferDevices;
    }

    public function addTransferDevice(TransferDevice $transferDevice)
    {
        $this->transferDevices[] = $transferDevice;

        return $this;
    }

    /**
     * Clears transfer devices
     */
    public function clearTransferDevices()
    {
        $this->transferDevices->clear();
    }

    public function getValidationContext(): ?ValidationContextDTO
    {
        return $this->validationContext;
    }

    public function setValidationContext(?ValidationContextDTO $validationContext): void
    {
        $this->validationContext = $validationContext;
    }
}
