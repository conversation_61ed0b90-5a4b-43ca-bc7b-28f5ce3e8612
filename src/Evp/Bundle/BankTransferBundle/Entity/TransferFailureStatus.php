<?php
declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\Entity;

use BeSimple\SoapBundle\ServiceDefinition\Annotation as Soap;

/**
 * Transfer failure status
 */
class TransferFailureStatus
{
    /**
     * Available code values
     */
    const CODE_OTHER = 'other';
    const CODE_EXCEPTION = 'exception';
    const CODE_VALIDATION = 'validation';
    const CODE_PERMISSION = 'permission';
    const CODE_NOT_ENOUGH_FUNDS = 'not_enough_funds';
    const CODE_INTERNAL_ACCOUNT_NOT_FOUND = 'internal_account_not_found';
    const CODE_ACCOUNT_AND_CODE_DOES_NOT_MATCH = 'account_and_code_does_not_match';
    const CODE_TRANSACTION_LIMIT_EXCEEDED = 'transaction_limit_exceeded';
    const CODE_TRANSACTION_QUANTITY_EXCEEDED = 'transaction_quantity_exceeded';
    const CODE_TRANSACTION_MONTHLY_LIMIT_EXCEEDED = 'transaction_monthly_limit_exceeded';
    const CODE_TRANSACTION_MONTHLY_QUANTITY_EXCEEDED = 'transaction_monthly_quantity_exceeded';
    const CODE_TRANSACTION_LIMIT_EXCEEDED_AIRWALLEX_USD = 'transaction_limit_exceeded_airwallex_usd';
    const CODE_TRANSACTION_LIMIT_EXCEEDED_AIRWALLEX_EUR = 'transaction_limit_exceeded_airwallex_eur';
    const CODE_DAILY_LIMIT_EXCEEDED = 'daily_limit_exceeded';
    const CODE_WEEKLY_LIMIT_EXCEEDED = 'weekly_limit_exceeded';
    const CODE_MONTHLY_LIMIT_EXCEEDED = 'monthly_limit_exceeded';
    const CODE_YEARLY_LIMIT_EXCEEDED = 'yearly_limit_exceeded';
    const CODE_CASH_IN_LIMIT_EXCEEDED = 'cash_in_limit_exceeded';
    const CODE_CASH_OUT_LIMIT_EXCEEDED = 'cash_out_limit_exceeded';
    const CODE_RESTRICTION_LIMIT_EXCEEDED = 'restriction_limit_exceeded';
    const CODE_CLIENT_RESTRICTED = 'client_restricted';
    const CODE_UNSUPPORTED_ROUTING = 'unsupported_routing';
    const CODE_UNSUPPORTED_OUR_ROUTING = 'unsupported_our_routing';
    const CODE_REGION_RESTRICTED_INTERNAL_TRANSFER = 'region_restricted_internal_transfer';
    const CODE_BENEFICIARY_ACCOUNT_INACTIVE = 'beneficiary_account_inactive';
    const CODE_PAYER_ACCOUNT_INACTIVE = 'payer_account_inactive';
    const CODE_PAYER_ACCOUNT_NOT_SET = 'payer_account_not_set';
    const CODE_PAYER_ACCOUNT_NUMBER_INVALID = 'payer_account_number_invalid';
    const CODE_PAYER_CLIENT_TYPE_FORBIDDEN = 'payer_client_type_forbidden';
    const CODE_PAYER_ADDITIONAL_INFORMATION_NOT_SET = 'mapper_payer_additional_information_not_set';
    const CODE_PAYER_NAME_NOT_SET = 'mapper_payer_name_empty';
    const CODE_PAYER_NAME_INVALID = 'mapper_payer_name_invalid';
    const CODE_PAYER_SURNAME_NOT_SET = 'mapper_payer_surname_empty';
    const CODE_PAYER_SURNAME_INVALID = 'mapper_payer_surname_invalid';
    const CODE_PAYER_PHONE_NUMBER_INVALID = 'mapper_payer_phone_number_invalid';
    const CODE_PAYER_BIRTH_DATE_INVALID = 'mapper_payer_birth_date_invalid';
    const CODE_PAYER_BIRTH_PLACE_INVALID_CHARACTERS = 'mapper_payer_birth_place_invalid_characters';
    const CODE_PAYER_RESIDENCE_INVALID = 'mapper_payer_residence_invalid';
    const CODE_PAYER_COUNTRY_INVALID_CHARACTERS = 'mapper_payer_country_invalid_characters';
    const CODE_PAYER_CITY_INVALID_CHARACTERS = 'mapper_payer_city_invalid_characters';
    const CODE_PAYER_STREET_INVALID_CHARACTERS = 'mapper_payer_street_invalid_characters';
    const CODE_PAYER_HOUSE_INVALID_CHARACTERS = 'mapper_payer_house_invalid_characters';
    const CODE_PAYER_APARTMENT_INVALID_CHARACTERS = 'mapper_payer_apartment_invalid_characters';
    const CODE_PAYER_DOCUMENT_TYPE_INVALID_CHARACTERS = 'mapper_payer_document_type_invalid_characters';
    const CODE_PAYER_DOCUMENT_SERIES_INVALID_CHARACTERS = 'mapper_payer_document_series_invalid_characters';
    const CODE_PAYER_DOCUMENT_NUMBER_INVALID_CHARACTERS = 'mapper_payer_document_number_invalid_characters';
    const CODE_PAYER_DOCUMENT_ISSUER_INVALID_CHARACTERS = 'mapper_payer_document_issuer_invalid_characters';
    const CODE_PAYER_DOCUMENT_ISSUED_DATE_INVALID = 'mapper_payer_document_issued_date_invalid';
    const CODE_DEBIT_ACCOUNT_FORBIDDEN = 'debit_account_forbidden';
    const CODE_BENEFICIARY_ACCOUNT_NUMBER_INVALID = 'beneficiary_account_number_invalid';
    const CODE_PRIMARY_PAYER_INVALID = 'primary_payer_invalid';
    const CODE_PRIMARY_PAYER_INSUFFICIENT_DATA = 'primary_payer_insufficient_data';
    const CODE_PAYER_HAS_NO_CODE = 'payer_has_no_code';
    const CODE_NUMBER_NOT_UNIQUE = 'transfer_number_not_unique';
    const CODE_INFORMATION_NOT_SET = 'transfer_information_not_set';
    const CODE_TIMEOUT = 'timeout';
    const CODE_NEW_TIMEOUT = 'timed_out_new';
    const CODE_REVOKED = 'revoked';
    const CODE_FORBIDDEN_CURRENCY_CONVERT = 'forbidden_currency_convert';
    const CODE_BENEFICIARY_FORBIDEN = 'beneficiary_forbiden';
    const CODE_BENEFICIARY_FORBIDDEN_BIC = 'beneficiary_forbidden_bic';
    const CODE_COUNTRY_SPECIFIC_BAD_DATA = 'country_specific_bad_data';
    const CODE_REFERENCE_NUMBER_INVALID = 'transfer_reference_number_invalid';
    const CODE_REFERENCE_NUMBER_INVALID_CHARACTERS = 'transfer_reference_number_invalid_characters';
    const CODE_REAL_BENEFICIARY_NOT_SET = 'transfer_real_beneficiary_is_not_set';
    const CODE_BENEFICIARY_AGE_LIMIT_EXCEEDED = 'transfer_beneficiary_age_limit_exceeded';
    const CODE_SYSTEM_ID_NOT_SET = 'transfer_system_id_not_set';
    const CODE_SYSTEM_ID_INVALID = 'transfer_system_id_invalid';
    const CODE_REFERENCE_TO_BENEFICIARY_INVALID = 'transfer_reference_to_beneficiary_invalid';
    const CODE_REFERENCE_TO_PAYER_INVALID = 'transfer_reference_to_payer_invalid';
    const CODE_ONLY_INTERNAL_TRANSFER = 'only_internal_transfer';
    const CODE_BENEFICIARY_BANK_SWIFT_INVALID = 'beneficiary_bank_swift_invalid';
    const CODE_COUNTRY_AND_SWIFT_DOES_NOT_MATCH = 'country_and_swift_does_not_match';
    const CODE_OCR_CODE_INVALID = 'beneficiary_ocr_code_invalid';
    const CODE_RISK_LEVEL_EXCEEDED = 'risk_level_exceeded';
    const CODE_CLIENT_NOT_VERIFIED = 'client_not_verified';
    const CODE_CLIENT_NOT_IDENTIFIED = 'client_not_identified';
    const CODE_CURRENCY_FINALIZATION = 'currency_finalization';
    const CODE_DETAILS_TOO_LONG = 'details_too_long';
    const CODE_GB_DETAILS_TOO_LONG = 'gb_details_too_long';
    const CODE_TRANSFER_TYPE_FORBIDDEN = 'transfer_type_forbidden';
    const CODE_QUESTIONNAIRE_NOT_FILLED = 'questionnaire_not_filled';
    const CODE_FORBIDDEN_CHARACTERS = 'forbidden_characters';
    const CODE_ADDRESS_IS_REQUIRED = 'address_is_required';
    const CODE_DOB_IS_REQUIRED = 'dob_is_required';
    const CODE_AMOUNT_NOT_NATURAL = 'amount_not_natural';
    const CODE_TRANSFER_AMOUNT_NOT_POSITIVE = 'transfer_amount_not_positive';
    const CODE_UNSUPPORTED_CHARGE_TYPE = 'unsupported_charge_type';
    const CODE_PAYMENT_TO_SAME_ACCOUNT = 'payment_to_same_account';
    const CODE_REPLACED_BY_ANOTHER_TRANSFER = 'replaced_by_another_transfer';
    const CODE_MOBILE_PAYMENT_REPLACED_BY_ANOTHER_TRANSFER = 'mobile_payment_replaced_by_another_transfer';
    const CODE_TAX_TRANSFER_REPLACED_BY_ANOTHER_TRANSFER = 'tax_transfer_replaced_by_another_transfer';
    const CODE_TRANSFER_SURVEILLANCE_REVIEW_REJECTED = 'transfer_surveillance_review_rejected';
    const CODE_BENEFICIARY_CORRESPONDENT_BANK_SWIFT_FORBIDDEN = 'beneficiary_correspondent_bank_swift_forbidden';
    const CODE_BENEFICIARY_FORBIDDEN_COUNTRY_CURRENCY = 'beneficiary_forbidden_country_currency';
    const CODE_BENEFICIARY_FORBIDDEN_COUNTRY_CURRENCY_RUB = 'beneficiary_forbidden_country_currency_rub';
    const CODE_BENEFICIARY_FORBIDDEN_DISTRICT = 'beneficiary_forbidden_district';
    const CODE_SURVEILLANCE = 'surveillance';
    const CODE_CONTIS_ACCOUNT_TRANSFER_CURRENCY_UNSUPPORTED = 'contis_account_transfer_currency_unsupported';
    const CODE_IN_DEBT = 'in_debt';
    const CODE_SEPA_INST_AB06 = 'sepa_inst_ab06';
    const CODE_SEPA_INST_AB07 = 'sepa_inst_ab07';
    const CODE_SEPA_INST_BENEFICIARY_BANK_NOT_ACCEPTING = 'sepa_inst_beneficiary_bank_not_accepting';
    const CODE_SEPA_INST_AM23 = 'sepa_inst_am23';
    const CODE_SEPA_INST_AG01 = 'sepa_inst_ag01';
    const CODE_SEPA_INST_RR04 = 'sepa_inst_rr04';
    const CODE_SEPA_INST_TIMEOUT = 'sepa_inst_timeout';
    const CODE_REASON_HAS_NOT_BEEN_SPECIFIED_BY_AGENT = 'reason_has_not_been_specified_by_agent';
    const CODE_ONLY_CURRENCY_EUR_ALLOWED = 'only_currency_eur_allowed';
    const CODE_FORBIDDEN_EUR_OUR_TRANSFER = 'forbidden_eur_our_transfer';
    const CODE_FORBIDDEN_USD_OUR_TRANSFER = 'forbidden_usd_our_transfer';
    const CODE_SEPA_INST_UNAVAILABLE = 'sepa_inst_unavailable';
    const CODE_SEPA_INST_BANK_UNAVAILABLE = 'sepa_inst_bank_unavailable';
    const CODE_CANCELLED_BY_NEW_TRANSFER_REGISTER = 'cancelled_by_new_transfer_register';
    const CODE_INVALID_BENEFICIARY = 'beneficiary_invalid';
    const CODE_INVALID_TAX_BENEFICIARY = 'tax_beneficiary_invalid';
    const CODE_TRANSFER_CURRENCY_DISABLED = 'transfer_currency_disabled';
    const CODE_SMS_NOTIFICATION_UNAVAILABLE = 'sms_notification_unavailable';
    const CODE_RETURN_IS_TEMPORARY_UNAVAILABLE = 'return_is_temporary_unavailable';
    const CODE_UNSUPPORTED_URGENCY = 'unsupported_urgency';
    const CODE_PARTICIPANT_NOT_FOUND = 'participant_not_found';
    const CODE_BENEFICIARY_NAME_SHOULD_BE_NOT_LESS_THAN_TWO_WORDS = 'beneficiary_name_should_be_not_less_than_two_words';
    const CODE_ACCOUNT_BLOCKED = 'account_blocked';
    const CODE_TREASURY_CODE_INVALID = 'treasury_code_invalid';
    public const CODE_ACCOUNT_TYPE_RESTRICTED = 'account_type_restricted';
    const CODE_PHONE_NUMBER_INVALID = 'phone_number_invalid';

    /**
     * Tax codes
     */
    const CODE_BAD_PRICE = 'bad_price';
    const CODE_BAD_PRICE_MIN = 'bad_min_price';
    const CODE_BAD_PRICE_MAX = 'bad_max_price';
    const CODE_TAX_PAYER_IDENTIFIER = 'bad_tax_payer_identifier';
    const CODE_TAX_ROWS_INCORRECT = 'bad_tax_rows';
    const CODE_TAX_UNAVAILABLE = 'tax_unavailable';
    const CODE_TAX_REMOTE_ERROR = 'tax_remote_error';
    const CODE_TAX_ROIK_CODE_INVALID = 'tax_roik_code_invalid';

    /**
     * Mapper validation codes
     */
    const CODE_BANK_CODE_AND_CORESPONDENT_BANK_ACCOUNT_MISMATCH = 'mapper_bank_code_and_corespondent_bank_account_mismatch';
    const CODE_BENEFICIARY_IBAN_NOT_SET = 'mapper_empty_beneficiary_payer_iban';
    const CODE_BENEFICIARY_ACCOUNT_NOT_SET = 'mapper_empty_beneficiary_account';
    const CODE_BENEFICIARY_IBAN_NOT_SUPPORTED = 'mapper_beneficiary_iban_not_supported';
    const CODE_BENEFICIARY_ACCOUNT_INVALID = 'mapper_invalid_beneficiary_account';
    const CODE_BENEFICIARY_ACCOUNT_INVALID_ONLY_DIGITS_ALLOWED = 'mapper_invalid_beneficiary_account_only_digits_allowed';
    const CODE_BENEFICIARY_INVALID_CHARACTERS = 'mapper_invalid_beneficiary_characters';
    const CODE_BENEFICIARY_INVALID_NAME = 'mapper_invalid_beneficiary_name';
    const CODE_BENEFICIARY_INVALID_SURNAME = 'mapper_invalid_beneficiary_surname';
    const CODE_BENEFICIARY_NAME_NOT_SET = 'mapper_empty_beneficiary_name';
    const CODE_BENEFICIARY_SURNAME_NOT_SET = 'mapper_empty_beneficiary_surname';
    const CODE_BENEFICIARY_SORT_CODE_NOT_SET = 'mapper_empty_beneficiary_sort_code';
    const CODE_BENEFICIARY_SORT_CODE_INVALID = 'mapper_invalid_beneficiary_sort_code';
    const CODE_BENEFICIARY_INN_CODE_NOT_SET = 'mapper_empty_beneficiary_inn_code';
    const CODE_BENEFICIARY_KPP_CODE_NOT_SET = 'mapper_empty_beneficiary_kpp_code';
    const CODE_BENEFICIARY_BANK_NAME_NOT_SET = 'mapper_empty_beneficiary_bank_name';
    const CODE_BENEFICIARY_BANK_NAME_INVALID_CHARACTERS = 'mapper_beneficiary_bank_name_invalid_characters';
    const CODE_BENEFICIARY_BANK_NAME_TOO_LONG = 'mapper_beneficiary_bank_name_too_long';
    const CODE_BENEFICIARY_BANK_SWIFT_NOT_SET = 'mapper_empty_beneficiary_bank_swift';
    const CODE_BENEFICIARY_BANK_SWIFT_NOT_RESOLVED = 'mapper_beneficiary_bank_swift_not_resolved';
    const CODE_BENEFICIARY_BANK_NAME_AND_SWIFT_NOT_SET = 'mapper_empty_beneficiary_bank_name_and_swift';
    const CODE_BENEFICIARY_BANK_BIC_NOT_SET = 'mapper_empty_beneficiary_bank_bic';
    const CODE_BENEFICIARY_BANK_CODE_NOT_SET = 'mapper_empty_beneficiary_bank_code';
    const CODE_BENEFICIARY_BANK_CODE_INVALID = 'mapper_beneficiary_bank_code_invalid';
    const CODE_BENEFICIARY_ROUTING_CODE_INVALID = 'mapper_beneficiary_routing_code_invalid';
    const CODE_BENEFICIARY_CLEARING_NUMBER_INVALID = 'mapper_beneficiary_clearing_number_invalid';
    const CODE_BENEFICIARY_BANK_CODE_OR_SWIFT_NOT_SET = 'mapper_empty_beneficiary_bank_code_or_swift';
    const CODE_BENEFICIARY_EMAIL_NOT_SET = 'mapper_empty_beneficiary_email';
    const CODE_BENEFICIARY_CORRESPONDENT_BANK_ACCOUNT_NOT_SET = 'mapper_empty_beneficiary_correspondent_bank_account';
    const CODE_BENEFICIARY_CORRESPONDEND_BANK_ACCOUNT_INVALID = 'mapper_invalid_beneficiary_correspondent_bank_account';
    const CODE_BENEFICIARY_CORRESPONDENT_BANK_NAME_NOT_SET = 'mapper_empty_beneficiary_correspondent_bank_name';
    const CODE_BENEFICIARY_CORRESPONDENT_BANK_SWIFT_NOT_SET = 'mapper_empty_beneficiary_correspondent_bank_swift';
    const CODE_DETAILS_NOT_SET = 'mapper_empty_details';
    const CODE_DETAILS_TOO_SHORT = 'mapper_details_too_short';
    const CODE_REFERENCE_TO_BENEFICIARY_NOT_SET = 'mapper_empty_reference_to_beneficiary';
    const CODE_REFERENCE_NUMBER_NOT_SET = 'mapper_empty_reference_number';
    const CODE_AMOUNT_TOO_SMALL = 'mapper_amount_too_small';
    const CODE_DETAILS_AND_REFERENCE_SET_IN_SEPA_TRANSFER = 'mapper_details_and_reference_set_in_sepa_transfer';
    const CODE_BENEFICIARY_NAME_TO_LONG = 'mapper_beneficiary_name_to_long';
    const CODE_BENEFICIARY_NAME_LONGER_THAN_70_CHARACTERS = 'mapper_beneficiary_name_longer_than_70_characters';
    const CODE_PAYER_NAME_TO_LONG = 'mapper_payer_name_to_long';
    const CODE_BENEFICIARY_ADDRESS_NOT_SET = 'mapper_empty_beneficiary_address';
    const CODE_NON_TRANSLITERATABLE_SYMBOLS_OCCURRED = 'mapper_non_transliteratable_symbols_occurred';
    const CODE_REFERENCE_TO_PAYER_TOO_LONG = 'mapper_reference_to_payer_too_long';
    const CODE_REFERENCE_TO_PAYER_NOT_SET = 'mapper_empty_reference_to_payer';
    const CODE_TRANSFER_ADDITIONAL_INFO_REQUIRED = 'transfer_additional_info_required'; // For AML purposes
    const CODE_TRANSFER_IMPORT_ADDITIONAL_INFO_REQUIRED = 'transfer_import_additional_info_required';
    const CODE_BENEFICIARY_ADDRESS_INVALID = 'mapper_beneficiary_address_invalid';
    const CODE_BENEFICIARY_ADDRESS_INVALID_CHARACTERS = 'mapper_beneficiary_address_invalid_characters';
    const CODE_BENEFICIARY_ADDRESS_SHOULD_NOT_BE_POST_OFFICE = 'mapper_beneficiary_address_should_not_be_post_office';
    const CODE_BENEFICIARY_CITY_NOT_SET = 'mapper_beneficiary_city_not_set';
    const CODE_BENEFICIARY_ADDITIONAL_INFORMATION_NOT_SET = 'mapper_beneficiary_additional_information_not_set';
    const CODE_BENEFICIARY_STATE_NOT_SET = 'mapper_beneficiary_state_not_set';
    const CODE_BENEFICIARY_STATE_INVALID_CHARACTERS = 'mapper_beneficiary_state_invalid_characters';
    const CODE_BENEFICIARY_COUNTRY_NOT_SET = 'mapper_beneficiary_country_not_set';
    const CODE_BENEFICIARY_COUNTRY_INVALID_CHARACTERS = 'mapper_beneficiary_country_invalid_characters';
    const CODE_BENEFICIARY_PHONE_NUMBER_NOT_SET = 'mapper_beneficiary_phone_number_not_set';
    const CODE_BENEFICIARY_POST_CODE_NOT_SET = 'mapper_beneficiary_post_code_not_set';
    const CODE_BENEFICIARY_POST_CODE_INVALID_US = 'mapper_beneficiary_post_code_invalid_us';
    const CODE_BENEFICIARY_POST_CODE_INVALID = 'mapper_beneficiary_post_code_invalid';
    const CODE_BENEFICIARY_POST_CODE_INVALID_CHARACTERS = 'mapper_beneficiary_post_code_invalid_characters';
    const CODE_BENEFICIARY_BIRTH_DATE_NOT_SET = 'mapper_beneficiary_birth_date_not_set';
    const CODE_BENEFICIARY_BIRTH_DATE_INVALID = 'mapper_beneficiary_birth_date_invalid';
    const CODE_BENEFICIARY_BIRTH_PLACE_INVALID_CHARACTERS = 'mapper_beneficiary_birth_place_invalid_characters';
    const CODE_BENEFICIARY_RESIDENCE_INVALID = 'mapper_beneficiary_residence_invalid';
    const CODE_CHARGE_TYPE_OUR_UNSUPPORTED = 'mapper_charge_type_our_unsupported';
    const CODE_PAYMENT_PURPOSE_CODE_NOT_SET = 'mapper_payment_purpose_code_not_set';
    const CODE_BENEFICIARY_TYPE_UNSUPPORTED = 'mapper_beneficiary_type_unsupported';
    const CODE_BENEFICIARY_LEGAL_TYPE_UNSUPPORTED = 'mapper_beneficiary_legal_type_unsupported';
    const CODE_BENEFICIARY_BIC_SIAULIU_BANKAS = 'mapper_beneficiary_bic_siauliu_bankas';
    const CODE_PURPOSE_CODE_NOT_SET = 'mapper_empty_purpose_code';
    const CODE_PURPOSE_CODE_INVALID = 'mapper_purpose_code_invalid';
    const CODE_PURPOSE_CODE_NOT_SUPPORTED = 'mapper_purpose_code_not_supported';
    const CODE_CLIENT_NATURAL_CODE_NOT_SET = 'mapper_empty_client_natural_code';
    const CODE_CLIENT_LEGAL_CODE_NOT_SET = 'mapper_empty_client_legal_code';
    const CODE_CLIENT_DATE_OF_BIRTH_NOT_SET = 'mapper_empty_date_of_birth';
    const CODE_BENEFICIARY_TYPE_NOT_SET = 'mapper_empty_beneficiary_type';
    const CODE_BENEFICIARY_IDENTIFIER_NOT_SET = 'mapper_beneficiary_identifier_not_set';
    const CODE_BENEFICIARY_IDENTIFIER_UNSUPPORTED = 'mapper_beneficiary_identifier_unsupported';
    const CODE_BENEFICIARY_IDENTIFIER_PERSONAL_NUMBER_NOT_SET = 'mapper_beneficiary_identifier_personal_number_not_set';
    const CODE_BENEFICIARY_IDENTIFIER_PERSONAL_NUMBER_INVALID = 'mapper_beneficiary_identifier_personal_number_invalid';
    const CODE_BENEFICIARY_IDENTIFIER_PERSONAL_NUMBER_INVALID_CHARACTERS = 'mapper_beneficiary_identifier_personal_number_invalid_characters';
    const CODE_BENEFICIARY_BANK_AND_BRANCH_CODES_MISMATCH = 'mapper_mismatch_beneficiary_bank_and_branch_codes';
    const CODE_BENEFICIARY_BSB_CODE_EMPTY = 'mapper_bsb_code_empty';
    const CODE_BENEFICIARY_BSB_CODE_INVALID = 'mapper_bsb_code_invalid';
    const CODE_SEPA_INST_MS02 = 'sepa_inst_ms02';
    const CODE_BENEFICIARY_COMPANY_CODE_TOO_LONG = 'mapper_beneficiary_company_code_too_long';
    const CODE_QUESTIONNAIRE_NOT_ACCEPTED = 'questionnaire_not_accepted';
    const CODE_TRANSFER_NUMBER_NOT_UNIQUE = 'transfer_number_not_unique';
    const CODE_CITY_NAME_TOO_LONG = 'city_name_too_long';
    const CODE_BENEFICIARY_ADDRESS_TOO_LONG = 'mapper_beneficiary_address_too_long';
    const CODE_BENEFICIARY_BANK_ADDRESS_TOO_LONG = 'mapper_beneficiary_bank_address_too_long';
    const CODE_DETAILS_INVALID_CHARACTERS = 'mapper_details_invalid_characters';
    const CODE_BENEFICIARY_CITY_INVALID_CHARACTERS = 'mapper_beneficiary_city_invalid_characters';
    const CODE_BENEFICIARY_STREET_INVALID_CHARACTERS = 'mapper_beneficiary_street_invalid_characters';
    const CODE_BENEFICIARY_HOUSE_INVALID_CHARACTERS = 'mapper_beneficiary_house_invalid_characters';
    const CODE_BENEFICIARY_APARTMENT_INVALID_CHARACTERS = 'mapper_beneficiary_apartment_invalid_characters';
    const CODE_BENEFICIARY_DOCUMENT_TYPE_INVALID_CHARACTERS = 'mapper_beneficiary_document_type_invalid_characters';
    const CODE_BENEFICIARY_DOCUMENT_SERIES_INVALID_CHARACTERS = 'mapper_beneficiary_document_series_invalid_characters';
    const CODE_BENEFICIARY_DOCUMENT_NUMBER_INVALID_CHARACTERS = 'mapper_beneficiary_document_number_invalid_characters';
    const CODE_BENEFICIARY_DOCUMENT_ISSUER_INVALID_CHARACTERS = 'mapper_beneficiary_document_issuer_invalid_characters';
    const CODE_BENEFICIARY_DOCUMENT_ISSUED_DATE_INVALID = 'mapper_beneficiary_document_issued_date_invalid';
    const CODE_ALLEGRO_ACCOUNT_ACTIVATION_NOT_SUPPORTED = 'mapper_allegro_account_activation_not_supported';
    const CODE_INVALID_GB_IBAN = 'mapper_gb_iban_invalid';
    const CODE_ACCOUNT_NUMBER_ONLY_ALPHANUMERIC = 'mapper_account_number_only_alphanumeric';
    const CODE_ACCOUNT_NUMBER_TOO_LONG = 'mapper_account_number_too_long';
    const CODE_REGULATORY_REASON = 'regulatory_reason';
    const CODE_CASH_PICKUP_PAYOUT_CURRENCY_NOT_SET = 'mapper_cash_pickup_payout_currency_not_set';
    const CODE_BENEFICIARY_FIRST_NAME_NOT_SET = 'mapper_beneficiary_first_name_not_set';
    const CODE_BENEFICIARY_FIRST_NAME_TOO_LONG = 'mapper_beneficiary_first_name_too_long';
    const CODE_BENEFICIARY_LAST_NAME_NOT_SET = 'mapper_beneficiary_last_name_not_set';
    const CODE_BENEFICIARY_LAST_NAME_TOO_LONG = 'mapper_beneficiary_last_name_too_long';
    const CODE_BENEFICIARY_AGE_TOO_YOUNG = 'mapper_beneficiary_age_too_young';
    const CODE_BENEFICIARY_AGE_TOO_OLD = 'mapper_beneficiary_age_too_old';
    const CODE_BENEFICIARY_PHONE_NUMBER_TOO_LONG = 'mapper_beneficiary_phone_number_too_long';
    const CODE_BENEFICIARY_PHONE_NUMBER_TOO_SHORT = 'mapper_beneficiary_phone_number_too_short';
    const CODE_BENEFICIARY_MOBILE_PHONE_NUMBER_TOO_LONG = 'mapper_beneficiary_mobile_phone_number_too_long';
    const CODE_BENEFICIARY_MOBILE_PHONE_NUMBER_INVALID = 'mapper_beneficiary_mobile_phone_number_invalid';
    const CODE_BENEFICIARY_RELATIONSHIP_NOT_SUPPORTED = 'mapper_beneficiary_relationship_not_supported';
    const CODE_BENEFICIARY_REGION_TOO_LONG = 'mapper_beneficiary_region_too_long';
    const CODE_BENEFICIARY_REGION_INVALID = 'mapper_beneficiary_region_invalid';
    const CODE_BENEFICIARY_CITY_TOO_LONG = 'mapper_beneficiary_city_too_long';
    const CODE_BENEFICIARY_DOCUMENT_NUMBER_TOO_LONG = 'mapper_beneficiary_document_number_too_long';
    const CODE_BENEFICIARY_DOCUMENT_TYPE_NOT_SUPPORTED = 'mapper_beneficiary_document_type_not_supported';
    const CODE_BENEFICIARY_DOCUMENT_TYPE_NOT_SET = 'mapper_beneficiary_document_type_not_set';
    const CODE_BENEFICIARY_DOCUMENT_NUMBER_NOT_SET = 'mapper_beneficiary_document_number_not_set';
    const CODE_PAYER_FIRST_NAME_NOT_SET = 'mapper_payer_first_name_not_set';
    const CODE_PAYER_FIRST_NAME_TOO_LONG = 'mapper_payer_first_name_too_long';
    const CODE_PAYER_LAST_NAME_NOT_SET = 'mapper_payer_last_name_not_set';
    const CODE_PAYER_LAST_NAME_TOO_LONG = 'mapper_payer_last_name_too_long';
    const CODE_PAYER_BIRTH_DATE_NOT_SET = 'mapper_payer_birth_date_not_set';
    const CODE_PAYER_BIRTH_DATE_IN_FUTURE = 'mapper_payer_birth_date_in_future';
    const CODE_PAYER_COUNTRY_NOT_SET = 'mapper_payer_country_not_set';
    const CODE_PAYER_COUNTRY_INVALID = 'mapper_payer_country_invalid';
    const CODE_PAYER_CITY_NOT_SET = 'mapper_payer_city_not_set';
    const CODE_PAYER_CITY_TOO_LONG = 'mapper_payer_city_too_long';
    const CODE_PAYER_STREET_NOT_SET = 'mapper_payer_street_not_set';
    const CODE_PAYER_HOUSE_NOT_SET = 'mapper_payer_house_not_set';
    const CODE_PAYER_BIRTH_COUNTRY_INVALID = 'mapper_payer_birth_country_invalid';
    const CODE_PAYER_RESIDENCE_COUNTRY_INVALID = 'mapper_payer_residence_country_invalid';
    const CODE_PAYER_CITIZENSHIP_COUNTRY_INVALID = 'mapper_payer_citizenship_country_invalid';
    const CODE_PAYER_REGION_TOO_LONG = 'mapper_payer_region_too_long';
    const CODE_PAYER_REGION_INVALID = 'mapper_payer_region_invalid';
    const CODE_PAYER_PHONE_NUMBER_TOO_LONG = 'mapper_payer_phone_number_too_long';
    const CODE_PAYER_PHONE_NUMBER_TOO_SHORT = 'mapper_payer_phone_number_too_short';
    const CODE_PAYER_MOBILE_PHONE_NUMBER_MISSED = 'mapper_payer_mobile_phone_number_missed';
    const CODE_PAYER_MOBILE_PHONE_NUMBER_TOO_LONG = 'mapper_payer_mobile_phone_number_too_long';
    const CODE_PAYER_MOBILE_PHONE_NUMBER_INVALID = 'mapper_payer_mobile_phone_number_invalid';
    const CODE_PAYER_ZIP_CODE_TOO_LONG  = 'mapper_payer_zip_code_too_long';
    const CODE_PAYER_OCCUPATION_TOO_LONG = 'mapper_payer_occupation_too_long';
    const CODE_PAYER_DOCUMENT_NUMBER_TOO_LONG = 'mapper_payer_document_number_too_long';
    const CODE_PAYER_DOCUMENT_TYPE_INVALID = 'mapper_payer_document_type_invalid';
    const CODE_PAYER_DOCUMENT_ISSUER_INVALID_COUNTRY_CODE = 'mapper_payer_document_issuer_invalid_country_code';
    const CODE_PAYER_DOCUMENT_ISSUED_DATE_IN_FUTURE = 'mapper_payer_document_issued_date_in_future';
    const CODE_PAYER_DOCUMENT_ISSUED_DATE_BEFORE_BIRTH_DATE = 'mapper_payer_document_issued_date_before_birth_date';
    const CODE_PAYER_DOCUMENT_NUMBER_NOT_SET = 'mapper_payer_document_number_not_set';
    const CODE_PAYER_DOCUMENT_TYPE_NOT_SET = 'mapper_payer_document_type_not_set';
    const CODE_PAYER_COUNTRY_NOT_SUPPORTED = 'mapper_payer_country_not_supported';
    const CODE_PAYER_FIRST_NAME_INVALID = 'mapper_payer_first_name_invalid';
    const CODE_PAYER_LAST_NAME_INVALID = 'mapper_payer_last_name_invalid';
    const CODE_PAYER_PHONE_NUMBER_NOT_SET = 'mapper_payer_phone_number_not_set';
    const CODE_PAYER_BIRTH_COUNTRY_NOT_SET = 'mapper_payer_birth_country_not_set';
    const CODE_PAYER_STREET_TOO_LONG = 'mapper_payer_street_too_long';
    const CODE_PAYER_ZIP_CODE_INVALID = 'mapper_payer_zip_code_invalid';
    const CODE_PAYER_HOUSE_NUMBER_TOO_LONG = 'mapper_payer_house_number_too_long';
    const CODE_PAYER_APARTMENT_NUMBER_TOO_LONG = 'mapper_payer_apartment_number_too_long';
    const CODE_PAYER_DOCUMENT_NUMBER_INVALID = 'mapper_payer_document_number_invalid';
    const CODE_PAYER_DOCUMENT_ISSUE_DATE_INVALID = 'mapper_payer_document_issue_date_invalid';
    const CODE_PAYER_OCCUPATION_INVALID = 'mapper_payer_occupation_invalid';
    const CODE_BENEFICIARY_LAST_NAME_INVALID = 'mapper_validation_beneficiary_last_name_invalid';
    const CODE_BENEFICIARY_FIRST_NAME_INVALID = 'mapper_validation_beneficiary_first_name_invalid';
    const CODE_BENEFICIARY_PHONE_NUMBER_INVALID = 'mapper_validation_beneficiary_phone_number_invalid';
    const CODE_BENEFICIARY_STREET_TOO_LONG = 'mapper_validation_beneficiary_street_too_long';
    const CODE_BENEFICIARY_ZIP_CODE_TOO_LONG = 'mapper_validation_beneficiary_zip_code_too_long';
    const CODE_BENEFICIARY_ZIP_CODE_INVALID = 'mapper_validation_beneficiary_zip_code_invalid';
    const CODE_BENEFICIARY_HOUSE_NUMBER_TOO_LONG = 'mapper_validation_beneficiary_house_number_too_long';
    const CODE_BENEFICIARY_APARTMENT_NUMBER_TOO_LONG = 'mapper_validation_beneficiary_apartment_number_too_long';
    const CODE_BENEFICIARY_DOCUMENT_NUMBER_AND_TYPE_MUST_BE_SET_TOGETHER = 'mapper_validation_beneficiary_document_number_and_type_must_be_set_together';
    const CODE_PAYMENT_CURRENCY_NOT_SET = 'mapper_validation_payment_currency_not_set';
    const CODE_PAYMENT_AMOUNT_NOT_SET = 'mapper_validation_payment_amount_not_set';
    const CODE_PAYOUT_TYPE_NOT_SET = 'mapper_validation_payout_type_not_set';
    const CODE_PAYMENT_DESTINATION_COUNTRY_NOT_SET = 'mapper_validation_payment_destination_country_not_set';
    const CODE_PAYOUT_TYPE_INVALID = 'mapper_validation_payout_type_invalid';
    const CODE_PAYMENT_AMOUNT_EXCEEDS_MAX_ALLOWED_FOR_SELECTED_DIRECTION = 'mapper_validation_payment_amount_exceeds_max_allowed_for_selected_direction';
    const CODE_PAYMENT_AMOUNT_LESS_THAN_MIN_ALLOWED_FOR_SELECTED_DIRECTION = 'mapper_validation_payment_amount_less_than_min_allowed_for_selected_direction';
    const CODE_RIA_MONEY_TRANSFER_SERVICE_IS_TEMPORARY_UNAVAILABLE = 'mapper_validation_ria_money_transfer_service_is_temporary_unavailable';
    const CODE_PAYMENT_RISKY_CURRENCY_RATE_NOT_CONFIRMED = 'mapper_validation_payment_risky_currency_rate_not_confirmed';

    const CODE_REMITTER_NAME_INVALID = 'mapper_validation_remitter_name_invalid';
    const CODE_REMITTER_CITY_LENGTH_INVALID = 'mapper_validation_remitter_city_length_invalid';
    const CODE_REMITTER_IDENTIFICATION_NUMBER_INVALID = 'mapper_validation_remitter_identification_number_invalid';
    const CODE_REMITTER_IDENTIFICATION_NUMBER_INVALID_CHARACTERS = 'mapper_validation_remitter_identification_number_invalid_characters';
    const CODE_REMITTER_NAME_LENGTH_INVALID = 'mapper_validation_remitter_name_length_invalid';
    const CODE_REMITTER_ADDRESS_INVALID_CHARACTERS = 'mapper_validation_remitter_address_invalid_characters';
    const CODE_REMITTER_ADDRESS_LENGTH_INVALID = 'mapper_validation_remitter_address_length_invalid';
    const CODE_REMITTER_POST_CODE_INVALID = 'mapper_validation_remitter_post_code_invalid';
    const CODE_REMITTER_POST_CODE_LENGTH_INVALID = 'mapper_validation_remitter_post_code_length_invalid';
    const CODE_REMITTER_STATE_LENGTH_INVALID = 'mapper_validation_remitter_state_length_invalid';
    const CODE_REMITTER_STATE_INVALID_CHARACTERS = 'mapper_remitter_state_invalid_characters';
    const CODE_REMITTER_IDENTIFICATION_TYPE_INVALID = 'mapper_validation_remitter_identification_type_invalid';
    const CODE_REMITTER_ACCOUNT_TYPE_INVALID = 'mapper_validation_remitter_account_type_invalid';
    const CODE_REMITTER_STATE_NOT_SET = 'mapper_remitter_state_not_set';
    const CODE_ROUTING_CODE_TYPE1_INVALID = 'mapper_validation_routing_code_type1_invalid';
    const CODE_BENEFICIARY_BANK_ACCOUNT_TYPE_INVALID = 'mapper_validation_beneficiary_bank_account_type_invalid';
    const CODE_BENEFICIARY_STATE_TOO_LONG = 'mapper_validation_beneficiary_state_too_long';
    const CODE_BENEFICIARY_POST_CODE_TOO_LONG = 'mapper_validation_beneficiary_post_code_too_long';
    const CODE_TRANSIT_NUMBER_INVALID = 'mapper_validation_transit_number_invalid';
    const CODE_BRANCH_CODE_INVALID = 'mapper_validation_branch_code_invalid';

    const CODE_GLOBUSBANK_PARTY_UNSUPPORTED_PHONE_NUMBER = 'mapper_globusbank_unsupported_phone_number';
    const CODE_AIRWALLEX_EXTERNAL_FAIL = 'mapper_airwallex_external_fail';
    const CODE_BENEFICIARY_ACCOUNT_LIMITATION = 'mapper_beneficiary_account_limit';

    /**
     * @var string
     * @Soap\ComplexType("string", nillable=true)
     */
    private $message;

    /**
     * @var string
     * @Soap\ComplexType("string")
     */
    private $code;

    /**
     * @var array
     */
    public static $codeList = [
        self::CODE_VALIDATION,
        self::CODE_EXCEPTION,
        self::CODE_OTHER,
        self::CODE_NOT_ENOUGH_FUNDS,
        self::CODE_INTERNAL_ACCOUNT_NOT_FOUND,
        self::CODE_DAILY_LIMIT_EXCEEDED,
        self::CODE_WEEKLY_LIMIT_EXCEEDED,
        self::CODE_MONTHLY_LIMIT_EXCEEDED,
        self::CODE_UNSUPPORTED_ROUTING,
    ];

    /**
     * @var array
     */
    public static $codeMap = [
        self::CODE_OTHER => 'evp.transferFailureStatus.code.other',
    ];

    /**
     * Class construct
     *
     * @param string $code
     * @param string $message
     */
    public function __construct($code = null, $message = null)
    {
        if ($code) {
            $this->setCode($code);
        }
        if ($message) {
            $this->setMessage($message);
        }
    }

    /**
     * Set code
     *
     * @param string $code
     */
    public function setCode($code)
    {
        $this->code = $code;
    }

    /**
     * Get code
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Set message
     *
     * @param string $message
     */
    public function setMessage($message)
    {
        $this->message = $message;
    }

    /**
     * Get message
     *
     * @return string
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Get code list
     *
     * @return array
     */
    public static function getCodeList()
    {
        return self::$codeList;
    }

    public function areLimitsExceeded()
    {
        return in_array($this->code, [
            self::CODE_TRANSACTION_LIMIT_EXCEEDED,
            self::CODE_DAILY_LIMIT_EXCEEDED,
            self::CODE_WEEKLY_LIMIT_EXCEEDED,
            self::CODE_MONTHLY_LIMIT_EXCEEDED,
            self::CODE_YEARLY_LIMIT_EXCEEDED,
            self::CODE_TRANSACTION_LIMIT_EXCEEDED_AIRWALLEX_USD,
            self::CODE_TRANSACTION_LIMIT_EXCEEDED_AIRWALLEX_EUR,
        ], true);
    }
}
