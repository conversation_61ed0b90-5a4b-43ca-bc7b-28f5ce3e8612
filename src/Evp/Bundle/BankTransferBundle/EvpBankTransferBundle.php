<?php

namespace Evp\Bundle\BankTransferBundle;

use Evp\Bundle\BankTransferBundle\DependencyInjection\RabbitProducerCompilerPass;
use Evp\Component\DependencyInjection\AddTaggedCompilerPass;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\Bundle;

class EvpBankTransferBundle extends Bundle
{
    public function build(ContainerBuilder $container)
    {
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.mapper.common_transfer_to_transfer',
                'evp_bank_transfer.mapper.account',
                'addAccountMapper'
            )
        );
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.mapper.common_transfer_to_transfer',
                'evp_bank_transfer.mapper.payer',
                'addPayerMapper'
            )
        );
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.transfer_validator_registry',
                'evp_bank_transfer.transfer_provider_validator',
                'registerValidator',
                ['key']
            )
        );
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.manager.transfer_related_to_charge_provider',
                'evp_bank_transfer.transfer_related_to_charge_provider',
                'addTransferRelatedChargeProvider'
            )
        );
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.mapper.client_identifier',
                'evp_bank_transfer.mapper.client_identifier_type',
                'addCustomMapper'
            )
        );
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.transfer_notice_manager',
                'evp_bank_transfer.transfer_notice_matcher',
                'addTransferNoticeMatcher'
            )
        );
        $container->addCompilerPass(new AddTaggedCompilerPass(
            'evp_bank_transfer.manager.convert_transfer_type_to_internal_voter',
            'evp_bank_transfer.convert_transfer_type_to_internal_voter',
            'addVoter'
        ));
        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.document_number_provider',
                'evp_bank_transfer.document_resolver',
                'addResolver'
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.party.formatter_resolver',
                'evp_bank_transfer.party_formatter',
                'addFormatter'
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.operation_processor.external_partner_operation_handler',
                'evp_bank_transfer.external_partner_operation_creator',
                'addCreator'
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.operation_processor.external_partner_operation_handler',
                'evp_bank_transfer.unique_external_partner_operation_creator',
                'addUniqueCreator'
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.operation_processor.internal_partner_operation_handler',
                'evp_bank_transfer.internal_partner_operation_creator',
                'addCreator',
                ['type', 'priority']
            )
        );

        $container->addCompilerPass(new AddTaggedCompilerPass(
            'evp_bundle_bank_transfer.service.operation_date_resolver',
            'evp_bank_transfer.operation_date_resolver',
            'addResolver'
        ));

        $container->addCompilerPass(new RabbitProducerCompilerPass());

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_accounting.operation_processing_verifier',
                'evp_bank_transfer.operation_processing_verifier_rule',
                'addRule',
                ['priority']
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bundle_bank_transfer.service_operation_processor_transfer_in.transfer_in_accounting_bank_resolver',
                'evp_bank_transfer.transfer_bank_key_resolver',
                'addResolver',
                ['priority']
            )
        );

        $container->addCompilerPass(
            new AddTaggedCompilerPass(
                'evp_bank_transfer.transfer_processor.currency_to_convert.currency_to_convert_resolver',
                'evp_bank_transfer.currency_to_convert_strategy',
                'addStrategy',
            )
        );
    }
}
