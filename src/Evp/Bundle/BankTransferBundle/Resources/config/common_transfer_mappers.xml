<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="evp_bank_transfer.common_transfer_purpose_map" type="collection">
            <parameter key="cash_in" type="constant">\Evp\Bundle\BankTransferBundle\Entity\Transfer::PURPOSE_CASH_IN</parameter>
            <parameter key="cash_out" type="constant">\Evp\Bundle\BankTransferBundle\Entity\Transfer::PURPOSE_CASH_OUT</parameter>
        </parameter>
    </parameters>

    <services>
        <service id="evp_bank_transfer.mapper.transfer_to_common_transfer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\CommonTransferMapper">
            <argument>%evp_bank_transfer.common_transfer_purpose_map%</argument>
            <argument type="service" id="paysera_transfer_callback.repository.transfer_callback"/>
            <argument type="service" id="evp_bank_transfer.service.evp_account_resolver"/>
            <argument type="service" id="evp_bank_transfer.mapper.beneficiary"/>
            <argument type="service" id="evp_protected_transfer.transfer_password_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_email_manager" />
            <argument type="service" id="evp_bank.account_info_resolver" />
            <argument type="service" id="evp_client.user_address_provider"/>
            <argument type="service" id="evp_bundle_bank_transfer.service.iban_aliases_account_number_manager"/>
            <argument type="service" id="evp_client.service.client_identifier_manager"/>
            <argument type="service" id="evp_bank_transfer.service.transfer_by_legal_special_country_client_detector"/>
            <argument type="service" id="evp_sepa.sepa_transfer_detector"/>
            <argument type="service" id="paysera_sepa_instant.sepa_instant_transfer_detector"/>
            <argument type="service" id="paysera_mobile_payments.repository.mobile_payment"/>
            <argument type="service" id="paysera_transfer_surveillance.transfer_inspection_provider"/>
            <argument type="service" id="logger" />
            <argument type="service" id="debug.stopwatch"/>
        </service>

        <service id="evp_bank_transfer.mapper.common_transfer_to_transfer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\TransferMapper">
            <argument>%evp_bank_transfer.common_transfer_purpose_map%</argument>
            <argument type="service" id="evp_bank_transfer.mapper.identifiers" />
            <argument type="service" id="evp_protected_transfer.transfer_password_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_email_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.mapper.identifiers"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\IdentifiersMapper">
        </service>

        <service id="evp_bank_transfer.mapper.additional_information"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AdditionalInformationMapper">
            <argument type="service" id="evp_bank_transfer.additional_information_manager"/>
        </service>

        <service id="evp_bank_transfer.mapper.client_identifier"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\ClientIdentifierMapper">
            <argument type="service" id="evp_bank_transfer.mapper.default_client_identifier" />
            <argument type="service" id="evp_bank_transfer.client_identifier_manager" />
        </service>

        <service id="evp_bank_transfer.mapper.default_client_identifier"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\ClientIdentifierMapper\DefaultClientIdentifierMapper">
            <argument type="service" id="evp_bank_transfer.party_client_identifier_factory" />
            <argument type="service" id="evp_bank_transfer.common_client_identifier_factory" />
        </service>

        <service id="evp_bank_transfer.mapper.date_and_place_of_birth_client_identifier"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\ClientIdentifierMapper\DateAndPlaceOfBirthClientIdentifierMapper">
            <argument type="service" id="evp_bank_transfer.party_client_identifier_factory" />
            <argument type="service" id="evp_bank_transfer.common_client_identifier_factory" />

            <tag name="evp_bank_transfer.mapper.client_identifier_type" />
        </service>

        <service id="evp_bank_transfer.mapper.other_client_identifier"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\ClientIdentifierMapper\OtherClientIdentifierMapper">
            <argument type="service" id="evp_bank_transfer.party_client_identifier_factory" />
            <argument type="service" id="evp_bank_transfer.common_client_identifier_factory" />

            <tag name="evp_bank_transfer.mapper.client_identifier_type" />
        </service>

        <service id="evp_bank_transfer.mapper.bank_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\BankAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />

            <argument type="service" id="evp_bank_transfer.mapper.identifiers" />
            <argument type="service" id="evp_bank_transfer.mapper.additional_information" />
            <argument type="service" id="evp_bank_transfer.mapper.client_identifier" />
            <argument type="service" id="evp_bank.swift_manager" />
        </service>

        <service id="evp_bank_transfer.mapper.paysera_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\PayseraAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
            <argument type="service" id="evp_bank_transfer.mapper.identifiers" />
            <argument type="service" id="evp_bank_transfer.service.evp_account_resolver" />
        </service>

        <service id="evp_bank_transfer.mapper.payza_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\PayzaAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
        </service>

        <service id="evp_bank_transfer.mapper.tax_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\TaxAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
            <argument type="service" id="evp_bank_transfer.mapper.identifiers" />
        </service>

        <service id="evp_bank_transfer.mapper.webmoney_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\WebmoneyAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
        </service>

        <service id="evp_bank_transfer.mapper.single_window_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\SingleWindowAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
        </service>

        <service id="evp_bank_transfer.mapper.prvatbank_account"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\AccountMapper\PrivatbankAccountMapper">
            <tag name="evp_bank_transfer.mapper.account" />
        </service>

        <service id="evp_bank_transfer.mapper.beneficiary"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\BeneficiaryMapper\BeneficiaryMapper">
            <argument type="service" id="evp_bank.account_info_resolver" />
            <argument type="service" id="validator" />
            <argument type="service" id="evp_bank_transfer.additional_information_manager"/>
            <argument type="service" id="evp_bank_transfer.client_identifier_manager"/>
            <argument type="service" id="evp_bank_transfer.mapper.additional_information"/>
            <argument type="service" id="evp_bank_transfer.mapper.client_identifier"/>
            <argument type="service" id="evp_client.email_validator"/>
        </service>

        <service id="evp_bank_transfer.mapper.account_number_payer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\AccountNumberPayerMapper">
            <tag name="evp_bank_transfer.mapper.payer" />
            <argument type="service" id="evp_bank_account.repository.account" />
        </service>

        <service id="evp_bank_transfer.mapper.tax_payer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\TaxPayerMapper">
            <tag name="evp_bank_transfer.mapper.payer" />
        </service>

        <service id="evp_bank_transfer.mapper.prvatbank_payer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\PrivatbankPayerMapper">
            <tag name="evp_bank_transfer.mapper.payer" />
            <argument type="service" id="evp_bank_account.repository.account" />
            <argument type="service" id="evp_vmi_account_report.identification_client" />
            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
        </service>

        <service id="evp_bank_transfer.mapper.single_window_payer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\SingleWindowPayerMapper">
            <tag name="evp_bank_transfer.mapper.payer" />

            <argument type="service" id="evp_bank_account.repository.account" />
            <argument type="service" id="evp_vmi_account_report.identification_client" />
            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
        </service>

        <service id="evp_bank_transfer.mapper.reference_to_payer"
                 class="Evp\Bundle\BankTransferBundle\Service\CommonTransfer\PayerMapper\ReferenceToPayerMapper">
            <tag name="evp_bank_transfer.mapper.payer" />
            <argument type="service" id="evp_bank_account.repository.account" />
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
        </service>
    </services>
</container>
