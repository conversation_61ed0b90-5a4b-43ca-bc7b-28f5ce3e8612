<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="repositories.xml"/>
        <import resource="normalizers/normalizers.xml"/>
        <import resource="normalizers/remote_event_normalizers.xml"/>
        <import resource="limits.xml"/>
        <import resource="services/admins.xml"/>
        <import resource="services/checkers.xml"/>
        <import resource="services/commands.xml"/>
        <import resource="services/validators.xml"/>
        <import resource="services/listeners.xml"/>
        <import resource="services/helpers.xml"/>
        <import resource="services/workers.xml"/>
        <import resource="services/transfer_status_managers.xml"/>
        <import resource="services/clients.xml"/>
        <import resource="services/transfer_notice_matchers.xml"/>
        <import resource="services/transfer_charge_type_resolver.xml"/>
        <import resource="services/hash_generators.xml"/>
        <import resource="services/party.xml"/>
        <import resource="services/resolvers.xml"/>
        <import resource="services/ai.xml"/>
        <import resource="services/transfer_sign_async.xml"/>
    </imports>

    <parameters>

        <parameter key="evp_bank_transfer.max_supported_transfer_amount" type="constant">\Evp\Bundle\BankTransferBundle\Entity\Transfer::MAX_SUPPORTED_AMOUNT</parameter>
        <parameter key="evp_bank_transfer.max_transfer_details_length">300</parameter>
        <parameter key="evp_bank_transfer.max_transfer_reference_number_length">35</parameter>
        <parameter key="evp_bank_transfer.vmi_payment_key">vmi_automatic</parameter>
        <parameter key="evp_bank_transfer.paysera_client_id">2</parameter>
        <parameter key="evp_bank_transfer.paysera_al_client_id">8912114</parameter>
        <parameter key="evp_bank_transfer.paysera_xk_client_id">3772624</parameter>
        <parameter key="evp_bank_transfer.paysera_ge_client_id">********</parameter>

        <parameter key="evp_bank_transfer.transfer_parties.all_key" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::ALL_KEY</parameter>
        <parameter key="evp_bank_transfer.transfer_parties.main_payer_parties" type="collection">

            <parameter key="hr_raiffeisenbank" type="collection">
                <parameter key="HRK">evp_bank_transfer.party.hr.raiffeisenbank.hrk</parameter>
            </parameter>

            <parameter key="webmoney" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.webmoney.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.webmoney.usd</parameter>
                <parameter key="RUB">evp_bank_transfer.party.webmoney.rub</parameter>
            </parameter>
            <parameter key="webmoney_automatic" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.webmoney.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.webmoney.usd</parameter>
                <parameter key="RUB">evp_bank_transfer.party.webmoney.rub</parameter>
            </parameter>
            <parameter key="payza_automatic" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.payza</parameter>
            </parameter>
            <parameter key="instarem" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <parameter key="ifx" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <parameter key="currency_one" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <parameter key="%paysera_airwallex.bank_key%" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <parameter key="xk_pribank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.xk.pribank.eur</parameter>
            </parameter>
            <parameter key="bpb_xk" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <parameter key="credinsbank_xk" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%" type="constant">Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver::PARTY_ID_NONE</parameter>
            </parameter>
            <!-- GBP -->
            <parameter key="gb_danske" type="collection">
                <parameter key="GBP">evp_bank_transfer.party.gb.danske.gbp</parameter>
            </parameter>
            <parameter key="gb_seb" type="collection">
                <parameter key="GBP">evp_bank_transfer.party.gb.seb.gbp</parameter>
            </parameter>
            <parameter key="gb_metro" type="collection">
                <parameter key="GBP">evp_bank_transfer.party.gb.metro.gbp</parameter>
            </parameter>
            <parameter key="lt_lb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.lb</parameter>
            </parameter>
            <parameter key="lt_lb_paysera" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.lb_paysera</parameter>
            </parameter>
            <parameter key="lt_lb_sepa_inst" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.lb</parameter>
            </parameter>
            <parameter key="lt_lb_target2_rtgs" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.lb</parameter>
            </parameter>
            <parameter key="lt_swedbank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.swedbank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.lt.swedbank.usd</parameter>
            </parameter>
            <parameter key="lt_swedbank_international" type="collection">
                <parameter key="USD">evp_bank_transfer.party.lt.swedbank.usd</parameter>
            </parameter>

            <parameter key="lt_seb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.seb.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.lt.seb.usd</parameter>
            </parameter>
            <parameter key="lt_seb_international" type="collection">
                <parameter key="CNY">evp_bank_transfer.party.lt.seb.cny</parameter>
                <parameter key="ILS">evp_bank_transfer.party.lt.seb.ils</parameter>
                <parameter key="INR">evp_bank_transfer.party.lt.seb.inr</parameter>
                <parameter key="RUB">evp_bank_transfer.party.lt.seb.rub</parameter>
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.lt.seb.eur</parameter>
            </parameter>
            <parameter key="lt_dnb" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.lt.dnb.eur</parameter>
            </parameter>
            <parameter key="lt_danske" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.danske.eur</parameter>
            </parameter>
            <parameter key="lt_citadele" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.citadele.eur</parameter>
                <!--<parameter key="USD">evp_bank_transfer.party.lt.citadele.usd</parameter>-->
            </parameter>
            <parameter key="lt_citadele_international" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.lt.citadele.eur</parameter>
            </parameter>
            <parameter key="lt_ub" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.ub.eur</parameter>
            </parameter>
            <parameter key="lt_sb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.sb.eur</parameter>
                <!--<parameter key="USD">evp_bank_transfer.party.lt.sb.usd</parameter>-->
            </parameter>
            <parameter key="lt_mb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.mb.eur</parameter>
            </parameter>
            <parameter key="lt_nordea" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.nordea.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.lt.nordea.usd</parameter>
                <parameter key="RUB">evp_bank_transfer.party.lt.nordea.rub</parameter>
                <parameter key="PLN">evp_bank_transfer.party.lt.nordea.pln</parameter>
                <parameter key="GBP">evp_bank_transfer.party.lt.nordea.gbp</parameter>
                <parameter key="TRY">evp_bank_transfer.party.lt.nordea.try</parameter>
                <parameter key="NOK">evp_bank_transfer.party.lt.nordea.nok</parameter>
                <parameter key="SEK">evp_bank_transfer.party.lt.nordea.sek</parameter>
                <parameter key="DKK">evp_bank_transfer.party.lt.nordea.dkk</parameter>
                <parameter key="AUD">evp_bank_transfer.party.lt.nordea.aud</parameter>
                <parameter key="BGN">evp_bank_transfer.party.lt.nordea.bgn</parameter>
                <parameter key="CZK">evp_bank_transfer.party.lt.nordea.czk</parameter>
                <parameter key="HKD">evp_bank_transfer.party.lt.nordea.hkd</parameter>
                <parameter key="INR">evp_bank_transfer.party.lt.nordea.inr</parameter>
                <parameter key="JPY">evp_bank_transfer.party.lt.nordea.jpy</parameter>
                <parameter key="CAD">evp_bank_transfer.party.lt.nordea.cad</parameter>
                <parameter key="HRK">evp_bank_transfer.party.lt.nordea.hrk</parameter>
                <parameter key="NZD">evp_bank_transfer.party.lt.nordea.nzd</parameter>
                <parameter key="RON">evp_bank_transfer.party.lt.nordea.ron</parameter>
                <parameter key="CHF">evp_bank_transfer.party.lt.nordea.chf</parameter>
                <parameter key="THB">evp_bank_transfer.party.lt.nordea.thb</parameter>
                <parameter key="ILS">evp_bank_transfer.party.lt.nordea.ils</parameter>
                <parameter key="HUF">evp_bank_transfer.party.lt.nordea.huf</parameter>
                <parameter key="KZT">evp_bank_transfer.party.lt.nordea.kzt</parameter>
                <parameter key="MXN">evp_bank_transfer.party.lt.nordea.mxn</parameter>
                <parameter key="ZAR">evp_bank_transfer.party.lt.nordea.zar</parameter>
                <parameter key="SGD">evp_bank_transfer.party.lt.nordea.sgd</parameter>
            </parameter>

            <!--*-->
            <parameter key="lt_nordea_urgent" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lt.nordea.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.lt.nordea.usd</parameter>
                <parameter key="RUB">evp_bank_transfer.party.lt.nordea.rub</parameter>
            </parameter>

            <!--*-->
            <parameter key="lt_unicredit" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.lt.unicredit</parameter>
            </parameter>
            <!--*-->
            <parameter key="lv_citadele" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.lv.citadele</parameter>
            </parameter>
            <parameter key="lv_swedbank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lv.swedbank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.lv.swedbank.usd</parameter>
                <parameter key="RUB">evp_bank_transfer.party.lv.swedbank.rub</parameter>
                <parameter key="GBP">evp_bank_transfer.party.lv.swedbank.gbp</parameter>
                <parameter key="HKD">evp_bank_transfer.party.lv.swedbank.hkd</parameter>
            </parameter>
            <parameter key="lv_nordea" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lv.nordea.eur</parameter>
            </parameter>
            <parameter key="lv_seb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.lv.seb.eur</parameter>
            </parameter>
            <parameter key="ee_swedbank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ee.swedbank</parameter>
            </parameter>
            <parameter key="ee_swedbank_international" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.ee.swedbank</parameter>
            </parameter>
            <parameter key="ee_seb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ee.seb.eur</parameter>
            </parameter>
            <parameter key="ee_seb_international" type="collection">
                <parameter key="%evp_bank_transfer.transfer_parties.all_key%">evp_bank_transfer.party.ee.seb.eur</parameter>
            </parameter>
            <parameter key="ee_nordea" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ee.nordea.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.ee.nordea.usd</parameter>
            </parameter>
            <parameter key="ee_danske" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ee.danske.eur</parameter>
            </parameter>
            <parameter key="es_sabadell" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.es.sabadell.eur</parameter>
            </parameter>
            <parameter key="bg_bcr" type="collection">
                <parameter key="RON">evp_bank_transfer.party.bg.bcr.ron</parameter>
                <parameter key="EUR">evp_bank_transfer.party.bg.bcr.eur</parameter>
                <parameter key="CHF">evp_bank_transfer.party.bg.bcr.chf</parameter>
            </parameter>
            <parameter key="no_seb" type="collection">
                <parameter key="NOK">evp_bank_transfer.party.no.seb.nok</parameter>
                <parameter key="EUR">evp_bank_transfer.party.no.seb.eur</parameter>
            </parameter>
            <parameter key="no_dnb" type="collection">
                <parameter key="NOK">evp_bank_transfer.party.no.dnb.nok</parameter>
            </parameter>
            <parameter key="dk_seb" type="collection">
                <parameter key="DKK">evp_bank_transfer.party.dk.seb.dkk</parameter>
                <parameter key="EUR">evp_bank_transfer.party.dk.seb.eur</parameter>
            </parameter>
            <parameter key="dk_nordea" type="collection">
                <parameter key="DKK">evp_bank_transfer.party.dk.nordea.dkk</parameter>
            </parameter>
            <parameter key="de_seb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.de.seb.eur</parameter>
            </parameter>

            <parameter key="de_dnb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.de.dnb.eur</parameter>
            </parameter>

            <parameter key="fi_seb" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.fi.seb.eur</parameter>
            </parameter>

            <!-- GEL -->
            <parameter key="ge_liberty" type="collection">
                <parameter key="GEL">evp_bank_transfer.party.ge.liberty.gel</parameter>
            </parameter>

            <parameter key="ge_basisbank" type="collection">
                <parameter key="GEL">evp_bank_transfer.party.ge.basisbank.gel</parameter>
            </parameter>

            <parameter key="ge_nbg_rtgs" type="collection">
                <parameter key="GEL">evp_bank_transfer.party.ge.rtgs.gel</parameter>
            </parameter>

            <parameter key="sk_unicredit" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.sk.unicredit.eur</parameter>
            </parameter>

            <parameter key="pl_getinbank" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.getinbank.pln</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.getinbank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.getinbank.usd</parameter>
            </parameter>
            <parameter key="pl_ingbank" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.ingbank.pln</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.ingbank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.ingbank.usd</parameter>
            </parameter>
            <parameter key="pl_pko" type="collection">
                <parameter key="USD">evp_bank_transfer.party.pl.pko.usd</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.pko.eur</parameter>
                <parameter key="PLN">evp_bank_transfer.party.pl.pko.pln</parameter>
            </parameter>

            <parameter key="pl_pko_international" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.pko.pln</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.pko.usd</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.pko.eur</parameter>
                <parameter key="GBP">evp_bank_transfer.party.pl.pko.gbp</parameter>
                <parameter key="RUB">evp_bank_transfer.party.pl.pko.rub</parameter>
                <parameter key="BGN">evp_bank_transfer.party.pl.pko.bgn</parameter>
                <parameter key="HUF">evp_bank_transfer.party.pl.pko.huf</parameter>
                <parameter key="HRK">evp_bank_transfer.party.pl.pko.hrk</parameter>
                <parameter key="CZK">evp_bank_transfer.party.pl.pko.czk</parameter>
                <parameter key="CNY">evp_bank_transfer.party.pl.pko.cny</parameter>
                <parameter key="TRY">evp_bank_transfer.party.pl.pko.try</parameter>
                <parameter key="ZAR">evp_bank_transfer.party.pl.pko.zar</parameter>
                <parameter key="AUD">evp_bank_transfer.party.pl.pko.aud</parameter>
                <parameter key="JPY">evp_bank_transfer.party.pl.pko.jpy</parameter>
                <parameter key="CAD">evp_bank_transfer.party.pl.pko.cad</parameter>
                <parameter key="DKK">evp_bank_transfer.party.pl.pko.dkk</parameter>
                <parameter key="NOK">evp_bank_transfer.party.pl.pko.nok</parameter>
                <parameter key="CHF">evp_bank_transfer.party.pl.pko.chf</parameter>
                <parameter key="SEK">evp_bank_transfer.party.pl.pko.sek</parameter>
                <parameter key="RON">evp_bank_transfer.party.pl.pko.ron</parameter>
            </parameter>

            <parameter key="pl_ing_international" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.ing.pln</parameter>
            </parameter>
            <parameter key="pl_mbank" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.mbank.pln</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.mbank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.mbank.usd</parameter>
            </parameter>
            <parameter key="pl_alior" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.alior.pln</parameter>
            </parameter>
            <parameter key="pl_alior_international" type="collection">
                <parameter key="USD">evp_bank_transfer.party.pl.alior.usd</parameter>
                <parameter key="JPY">evp_bank_transfer.party.pl.alior.jpy</parameter>
                <parameter key="RUB">evp_bank_transfer.party.pl.alior.rub</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.alior.eur</parameter>
                <parameter key="CZK">evp_bank_transfer.party.pl.alior.czk</parameter>
                <parameter key="CHF">evp_bank_transfer.party.pl.alior.chf</parameter>
                <parameter key="AUD">evp_bank_transfer.party.pl.alior.aud</parameter>
                <parameter key="SEK">evp_bank_transfer.party.pl.alior.sek</parameter>
                <parameter key="HUF">evp_bank_transfer.party.pl.alior.huf</parameter>
                <parameter key="CAD">evp_bank_transfer.party.pl.alior.cad</parameter>
                <parameter key="GBP">evp_bank_transfer.party.pl.alior.gbp</parameter>
                <parameter key="NOK">evp_bank_transfer.party.pl.alior.nok</parameter>
                <parameter key="DKK">evp_bank_transfer.party.pl.alior.dkk</parameter>
            </parameter>

            <parameter key="pl_bnpparibas" type="collection">
                <parameter key="AUD">evp_bank_transfer.party.pl.bnpparibas.aud</parameter>
                <parameter key="CAD">evp_bank_transfer.party.pl.bnpparibas.cad</parameter>
                <parameter key="CHF">evp_bank_transfer.party.pl.bnpparibas.chf</parameter>
                <parameter key="DKK">evp_bank_transfer.party.pl.bnpparibas.dkk</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.bnpparibas.eur</parameter>
                <parameter key="GBP">evp_bank_transfer.party.pl.bnpparibas.gbp</parameter>
                <parameter key="JPY">evp_bank_transfer.party.pl.bnpparibas.jpy</parameter>
                <parameter key="NOK">evp_bank_transfer.party.pl.bnpparibas.nok</parameter>
                <parameter key="NZD">evp_bank_transfer.party.pl.bnpparibas.nzd</parameter>
                <parameter key="PLN">evp_bank_transfer.party.pl.bnpparibas.pln</parameter>
                <parameter key="SEK">evp_bank_transfer.party.pl.bnpparibas.sek</parameter>
                <parameter key="TRY">evp_bank_transfer.party.pl.bnpparibas.try</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.bnpparibas.usd</parameter>
                <parameter key="BGN">evp_bank_transfer.party.pl.bnpparibas.bgn</parameter>
                <parameter key="CZK">evp_bank_transfer.party.pl.bnpparibas.czk</parameter>
                <parameter key="HRK">evp_bank_transfer.party.pl.bnpparibas.hrk</parameter>
                <parameter key="HUF">evp_bank_transfer.party.pl.bnpparibas.huf</parameter>
                <parameter key="RON">evp_bank_transfer.party.pl.bnpparibas.ron</parameter>
                <parameter key="RUB">evp_bank_transfer.party.pl.bnpparibas.rub</parameter>
            </parameter>

            <parameter key="pl_millennium" type="collection">
                <parameter key="PLN">evp_bank_transfer.party.pl.millennium.pln</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.millennium.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.millennium.usd</parameter>
            </parameter>

            <parameter key="pl_millennium_international" type="collection">
                <parameter key="CNY">evp_bank_transfer.party.pl.millennium.cny</parameter>
                <parameter key="RON">evp_bank_transfer.party.pl.millennium.ron</parameter>
                <parameter key="HUF">evp_bank_transfer.party.pl.millennium.huf</parameter>
                <parameter key="CZK">evp_bank_transfer.party.pl.millennium.czk</parameter>
                <parameter key="SEK">evp_bank_transfer.party.pl.millennium.sek</parameter>
                <parameter key="NOK">evp_bank_transfer.party.pl.millennium.nok</parameter>
                <parameter key="AUD">evp_bank_transfer.party.pl.millennium.aud</parameter>
                <parameter key="CAD">evp_bank_transfer.party.pl.millennium.cad</parameter>
                <parameter key="DKK">evp_bank_transfer.party.pl.millennium.dkk</parameter>
                <parameter key="JPY">evp_bank_transfer.party.pl.millennium.jpy</parameter>
                <parameter key="CHF">evp_bank_transfer.party.pl.millennium.chf</parameter>
                <parameter key="GBP">evp_bank_transfer.party.pl.millennium.gbp</parameter>
                <parameter key="PLN">evp_bank_transfer.party.pl.millennium.pln</parameter>
                <parameter key="EUR">evp_bank_transfer.party.pl.millennium.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.pl.millennium.usd</parameter>
            </parameter>

            <parameter key="ru_telecomerc" type="collection">
                <parameter key="RUB">evp_bank_transfer.party.ru.telecomerc.rub</parameter>
            </parameter>

            <parameter key="se_seb" type="collection">
                <!--<parameter key="EUR">evp_bank_transfer.party.se.seb.eur</parameter>-->
                <parameter key="SEK">evp_bank_transfer.party.se.seb.sek</parameter>
            </parameter>
            <parameter key="cz_unicredit" type="collection">
                <parameter key="CZK">evp_bank_transfer.party.cz.unicredit.czk</parameter>
            </parameter>

            <parameter key="%paysera_libra_bank.bank_key%" type="collection">
                <parameter key="RON">evp_bank_transfer.party.ro.libra.ron</parameter>
            </parameter>

            <parameter key="ro_bcr" type="collection">
                <parameter key="RON">evp_bank_transfer.party.ro.bcr.ron</parameter>
            </parameter>

            <parameter key="ro_bcr_international" type="collection">
                <parameter key="CZK">evp_bank_transfer.party.ro.bcr.czk</parameter>
            </parameter>

            <parameter key="ro_techventures" type="collection">
                <parameter key="RON">evp_bank_transfer.party.ro.techventures.ron</parameter>
            </parameter>

            <!-- BGN -->
            <parameter key="bg_allianz" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.allianz.bgn</parameter>
                <parameter key="EUR">evp_bank_transfer.party.bg.allianz.eur</parameter>
            </parameter>

            <parameter key="bg_unicreditbulbank" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.unicredit.bgn</parameter>
            </parameter>

            <parameter key="bg_bacb" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.bacb.bgn</parameter>
            </parameter>

            <parameter key="bg_postbank" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.postbank.bgn</parameter>
                <parameter key="USD">evp_bank_transfer.party.bg.postbank.usd</parameter>
                <parameter key="EUR">evp_bank_transfer.party.bg.postbank.eur</parameter>
            </parameter>

            <parameter key="bg_budget_postbank" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.postbank.bgn</parameter>
            </parameter>

            <parameter key="bg_ubb" type="collection">
                <parameter key="BGN">evp_bank_transfer.party.bg.ubb.bgn</parameter>
                <parameter key="EUR">evp_bank_transfer.party.bg.ubb.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.bg.ubb.usd</parameter>
            </parameter>

            <parameter key="hr_zagrebacka" type="collection">
                <parameter key="HRK">evp_bank_transfer.party.hr.zagrebacka.hrk</parameter>
            </parameter>

            <parameter key="al_intesa_sanpaolo" type="collection">
                <parameter key="ALL">evp_bank_transfer.party.al.intesa_sanpaolo.all</parameter>
                <parameter key="EUR">evp_bank_transfer.party.al.intesa_sanpaolo.eur</parameter>
            </parameter>

            <parameter key="al_tirana_bank" type="collection">
                <parameter key="ALL">evp_bank_transfer.party.al.tirana_bank.all</parameter>
                <parameter key="EUR">evp_bank_transfer.party.al.tirana_bank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.al.tirana_bank.usd</parameter>
            </parameter>

            <parameter key="al_credins_bank" type="collection">
                <parameter key="ALL">evp_bank_transfer.party.al.credins_bank.all</parameter>
                <parameter key="EUR">evp_bank_transfer.party.al.credins_bank.eur</parameter>
            </parameter>

            <parameter key="ua_globus_bank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ua.globus.eur</parameter>
            </parameter>

            <parameter key="lv_lpb" type="collection">
                <parameter key="RUB">evp_bank_transfer.party.lv.lpb.rub</parameter>
            </parameter>

            <parameter key="us_ria" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.us.ria_bank.eur</parameter>
                <parameter key="USD">evp_bank_transfer.party.us.ria_bank.usd</parameter>
                <parameter key="GBP">evp_bank_transfer.party.us.ria_bank.gbp</parameter>
            </parameter>

            <parameter key="ua_privatbank" type="collection">
                <parameter key="EUR">evp_bank_transfer.party.ua.privatbank.eur</parameter>
            </parameter>
        </parameter>

        <parameter key="evp_bank_transfer.government_accounts.lv" type="collection">
            <parameter>*********************</parameter>
        </parameter>
        <parameter key="evp_bank_transfer.government_accounts.ee" type="collection">
            <!-- Tax and Customs Board -->
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <!-- Rahandusministeerium (EN - the Ministry of Finance) -->
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.government_accounts.excluded_bg" type="collection">
            <parameter>**********************</parameter>
            <parameter>**********************</parameter>
            <parameter>**********************</parameter>
            <parameter>**********************</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.government_accounts.vmi" type="collection">
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
            <parameter>********************</parameter>
        </parameter>
        <parameter key="evp_bank_transfer.government_accounts.sodra" type="collection">
            <parameter>********************</parameter><!-- SODRA AB DNB bankas -->
            <parameter>********************</parameter><!-- SODRA AB SEB bankas -->
            <parameter>********************</parameter><!-- SODRA „Swedbank“, AB -->
            <parameter>********************</parameter><!-- SODRA UAB Medicinos bankas -->
            <parameter>********************</parameter><!-- SODRA AB „Citadele“ bankas -->
            <parameter>********************</parameter><!-- SODRA AB Šiaulių bankas -->
            <parameter>********************</parameter><!-- SODRA Nordea Bank Finland Plc Lietuvos skyriuje -->
        </parameter>
        <parameter key="evp_bank_transfer.government_accounts.customs.luminor">********************</parameter>
        <parameter key="evp_bank_transfer.government_accounts.customs.swedbank">********************</parameter>
        <parameter key="evp_bank_transfer.government_accounts.customs.medbank">********************</parameter>
        <parameter key="evp_bank_transfer.government_accounts.customs" type="collection">
            <parameter>%evp_bank_transfer.government_accounts.customs.luminor%</parameter>
            <parameter>%evp_bank_transfer.government_accounts.customs.swedbank%</parameter>
            <parameter>%evp_bank_transfer.government_accounts.customs.medbank%</parameter>
        </parameter>
        <parameter key="evp_bank_transfer.charities_accounts" type="collection">
            <parameter>************************</parameter><!-- Magic Association -->
            <parameter>************************</parameter><!-- Agent green -->
            <parameter>************************</parameter> <!-- Daruiesteviata	Ingrijire medicala (EUR) -->
            <parameter>************************</parameter> <!-- Plantam fapte bune in Romania -->
            <parameter>************************</parameter> <!-- Hospice -->
            <parameter>**********************</parameter> <!-- SOS Children's Villages Association Bulgaria -->
            <parameter>**********************</parameter> <!-- BULGARIAN RED CROSS -->
            <parameter>********************</parameter> <!-- Laste fond -->
            <parameter>********************</parameter> <!-- Eesti Vähiliit -->
            <parameter>********************</parameter> <!-- Laikykites ten medikai -->
            <parameter>********************</parameter> <!-- LR finansų mnisterija COVID19 -->
            <parameter>*********************</parameter> <!-- Bērnu slimnīcas fonds -->
            <parameter>*********************</parameter> <!-- Biedrība Dzīvnieku pansija Ulubele -->
        </parameter>

        <parameter type="collection" key="evp_bank_transfer.government_accounts">
            <parameter type="string" key="vmi">%evp_bank_transfer.government_accounts.vmi%</parameter>
            <parameter type="string" key="sodra">%evp_bank_transfer.government_accounts.sodra%</parameter>
            <parameter type="string" key="customs">%evp_bank_transfer.government_accounts.customs%</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.operator_window_time" type="string">5</parameter>
        <parameter key="evp_bank_transfer.internal_cash_account_purposes" type="collection">
            <parameter>cash_out</parameter>
            <parameter>commission</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.vo_codes" type="collection">
            <parameter type="string">01010</parameter>
            <parameter type="string">01030</parameter>
            <parameter type="string">01040</parameter>
            <parameter type="string">02010</parameter>
            <parameter type="string">02020</parameter>
            <parameter type="string">10100</parameter>
            <parameter type="string">10200</parameter>
            <parameter type="string">10800</parameter>
            <parameter type="string">11100</parameter>
            <parameter type="string">11200</parameter>
            <parameter type="string">11900</parameter>
            <parameter type="string">12050</parameter>
            <parameter type="string">12060</parameter>
            <parameter type="string">12800</parameter>
            <parameter type="string">12900</parameter>
            <parameter type="string">13010</parameter>
            <parameter type="string">13020</parameter>
            <parameter type="string">13800</parameter>
            <parameter type="string">13900</parameter>
            <parameter type="string">20100</parameter>
            <parameter type="string">20200</parameter>
            <parameter type="string">20300</parameter>
            <parameter type="string">20400</parameter>
            <parameter type="string">20500</parameter>
            <parameter type="string">20800</parameter>
            <parameter type="string">21100</parameter>
            <parameter type="string">21200</parameter>
            <parameter type="string">21300</parameter>
            <parameter type="string">21400</parameter>
            <parameter type="string">21500</parameter>
            <parameter type="string">21900</parameter>
            <parameter type="string">22100</parameter>
            <parameter type="string">22110</parameter>
            <parameter type="string">22200</parameter>
            <parameter type="string">22210</parameter>
            <parameter type="string">22300</parameter>
            <parameter type="string">22800</parameter>
            <parameter type="string">23100</parameter>
            <parameter type="string">23110</parameter>
            <parameter type="string">23200</parameter>
            <parameter type="string">23210</parameter>
            <parameter type="string">23300</parameter>
            <parameter type="string">23900</parameter>
            <parameter type="string">30010</parameter>
            <parameter type="string">30020</parameter>
            <parameter type="string">30030</parameter>
            <parameter type="string">30040</parameter>
            <parameter type="string">30800</parameter>
            <parameter type="string">30900</parameter>
            <parameter type="string">32010</parameter>
            <parameter type="string">32015</parameter>
            <parameter type="string">32020</parameter>
            <parameter type="string">32025</parameter>
            <parameter type="string">35030</parameter>
            <parameter type="string">35040</parameter>
            <parameter type="string">40030</parameter>
            <parameter type="string">40900</parameter>
            <parameter type="string">41030</parameter>
            <parameter type="string">41800</parameter>
            <parameter type="string">42015</parameter>
            <parameter type="string">42035</parameter>
            <parameter type="string">42050</parameter>
            <parameter type="string">42900</parameter>
            <parameter type="string">42950</parameter>
            <parameter type="string">43015</parameter>
            <parameter type="string">43035</parameter>
            <parameter type="string">43050</parameter>
            <parameter type="string">43800</parameter>
            <parameter type="string">43850</parameter>
            <parameter type="string">50100</parameter>
            <parameter type="string">50110</parameter>
            <parameter type="string">50200</parameter>
            <parameter type="string">50210</parameter>
            <parameter type="string">50800</parameter>
            <parameter type="string">50900</parameter>
            <parameter type="string">51210</parameter>
            <parameter type="string">51215</parameter>
            <parameter type="string">51230</parameter>
            <parameter type="string">51235</parameter>
            <parameter type="string">51250</parameter>
            <parameter type="string">51255</parameter>
            <parameter type="string">51800</parameter>
            <parameter type="string">52210</parameter>
            <parameter type="string">52215</parameter>
            <parameter type="string">52230</parameter>
            <parameter type="string">52235</parameter>
            <parameter type="string">52250</parameter>
            <parameter type="string">52255</parameter>
            <parameter type="string">52900</parameter>
            <parameter type="string">55210</parameter>
            <parameter type="string">55230</parameter>
            <parameter type="string">55250</parameter>
            <parameter type="string">55310</parameter>
            <parameter type="string">55330</parameter>
            <parameter type="string">55350</parameter>
            <parameter type="string">55800</parameter>
            <parameter type="string">55900</parameter>
            <parameter type="string">56010</parameter>
            <parameter type="string">56060</parameter>
            <parameter type="string">56800</parameter>
            <parameter type="string">56900</parameter>
            <parameter type="string">57010</parameter>
            <parameter type="string">57015</parameter>
            <parameter type="string">57020</parameter>
            <parameter type="string">57025</parameter>
            <parameter type="string">57030</parameter>
            <parameter type="string">57035</parameter>
            <parameter type="string">57800</parameter>
            <parameter type="string">57900</parameter>
            <parameter type="string">58010</parameter>
            <parameter type="string">58015</parameter>
            <parameter type="string">58020</parameter>
            <parameter type="string">58025</parameter>
            <parameter type="string">58030</parameter>
            <parameter type="string">58800</parameter>
            <parameter type="string">58900</parameter>
            <parameter type="string">60070</parameter>
            <parameter type="string">60071</parameter>
            <parameter type="string">60075</parameter>
            <parameter type="string">60076</parameter>
            <parameter type="string">60080</parameter>
            <parameter type="string">60081</parameter>
            <parameter type="string">60085</parameter>
            <parameter type="string">60086</parameter>
            <parameter type="string">60090</parameter>
            <parameter type="string">60095</parameter>
            <parameter type="string">60200</parameter>
            <parameter type="string">61070</parameter>
            <parameter type="string">61100</parameter>
            <parameter type="string">61115</parameter>
            <parameter type="string">61130</parameter>
            <parameter type="string">61135</parameter>
            <parameter type="string">61140</parameter>
            <parameter type="string">61145</parameter>
            <parameter type="string">61150</parameter>
            <parameter type="string">61155</parameter>
            <parameter type="string">61160</parameter>
            <parameter type="string">61161</parameter>
            <parameter type="string">61162</parameter>
            <parameter type="string">61163</parameter>
            <parameter type="string">61164</parameter>
            <parameter type="string">61165</parameter>
            <parameter type="string">61170</parameter>
            <parameter type="string">61175</parameter>
            <parameter type="string">61200</parameter>
            <parameter type="string">70010</parameter>
            <parameter type="string">70020</parameter>
            <parameter type="string">70030</parameter>
            <parameter type="string">70040</parameter>
            <parameter type="string">70050</parameter>
            <parameter type="string">70060</parameter>
            <parameter type="string">70090</parameter>
            <parameter type="string">70095</parameter>
            <parameter type="string">70100</parameter>
            <parameter type="string">70105</parameter>
            <parameter type="string">70110</parameter>
            <parameter type="string">70115</parameter>
            <parameter type="string">70120</parameter>
            <parameter type="string">70125</parameter>
            <parameter type="string">70200</parameter>
            <parameter type="string">70205</parameter>
            <parameter type="string">70800</parameter>
            <parameter type="string">70900</parameter>
            <parameter type="string">80010</parameter>
            <parameter type="string">80020</parameter>
            <parameter type="string">80021</parameter>
            <parameter type="string">80050</parameter>
            <parameter type="string">80110</parameter>
            <parameter type="string">80120</parameter>
            <parameter type="string">80121</parameter>
            <parameter type="string">80150</parameter>
            <parameter type="string">99010</parameter>
            <parameter type="string">99020</parameter>
            <parameter type="string">99090</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.company_types" type="collection">
            <parameter key="private_limited_liability">UAB</parameter>
            <parameter key="sole_proprietorship">IĮ</parameter>
            <parameter key="joint_stock">AB</parameter>
            <parameter key="association_apartment_house_owners">DNSB</parameter>
            <parameter key="residential_construction_association">GNSB</parameter>
            <parameter key="state_enterprise">VĮ</parameter>
            <parameter key="public_institution">VšĮ</parameter>
            <parameter key="agricultural_enterprise">ŽŪB</parameter>
            <parameter key="little_community">MB</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.country_code_applicable_for_legal_name_acronym">LT</parameter>

        <parameter key="evp_bank_transfer.skip_resolving_account_countries" type="collection">
            <parameter>LT</parameter>
            <parameter>LV</parameter>
            <parameter>EE</parameter>
            <parameter>MA</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.forbidden_bics.usd" type="collection">
            <parameter>/^BKTR/</parameter>
            <parameter>/^BKTIGB2LSWP$/</parameter>
            <parameter>/^DBCCUS31XXX$/</parameter>
            <parameter>/^DBCMGB21XXX$/</parameter>
            <parameter>/^DBMLMUMUXXX$/</parameter>
            <parameter>/^DBSCESMMXXX$/</parameter>
            <parameter>/^DEAMCHZZXXX$/</parameter>
            <parameter>/^DECRESM1XXX$/</parameter>
            <parameter>/^DEFBCWC1XXX$/</parameter>
            <parameter>/^DEGEESM1XXX$/</parameter>
            <parameter>/^DEMUITM1XXX$/</parameter>
            <parameter>/^DESMITM1XXX$/</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.external_to_internal_purpose_code_map" type="collection">
            <parameter key="%evp_bank_transfer.purpose_code_type.sepa%" type="collection">
                <parameter key="%evp_bank_transfer.purpose_code.transfer_to_own_account%" type="collection">
                    <parameter>CDCB</parameter>
                    <parameter>CDCD</parameter>
                    <parameter>CDCS</parameter>
                    <parameter>CDOC</parameter>
                    <parameter>ACCT</parameter>
                    <parameter>CASH</parameter>
                    <parameter>CSDB</parameter>
                    <parameter>INTC</parameter>
                    <parameter>LIMA</parameter>
                    <parameter>NETT</parameter>
                    <parameter>POPE</parameter>
                    <parameter>CBFF</parameter>
                    <parameter>TREA</parameter>
                </parameter>
                <parameter key="%evp_bank_transfer.purpose_code.family_maintenance%" type="collection">
                    <parameter>CDQC</parameter>
                    <parameter>GDDS</parameter>
                    <parameter>GDSV</parameter>
                    <parameter>GSCB</parameter>
                    <parameter>SUBS</parameter>
                    <parameter>INTE</parameter>
                    <parameter>CCRD</parameter>
                    <parameter>CDBL</parameter>
                    <parameter>RCPT</parameter>
                    <parameter>ALMY</parameter>
                    <parameter>BECH</parameter>
                    <parameter>PAYR</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.education_expenses%">
                    <parameter>STDY</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.medical_treatment%">
                    <parameter>ANTS</parameter>
                    <parameter>CVCF</parameter>
                    <parameter>DMEQ</parameter>
                    <parameter>DNTS</parameter>
                    <parameter>HLTC</parameter>
                    <parameter>HLTI</parameter>
                    <parameter>HSPC</parameter>
                    <parameter>ICRF</parameter>
                    <parameter>LTCF</parameter>
                    <parameter>MDCS</parameter>
                    <parameter>VIEW</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.hotel_accommodation%">
                    <parameter>NOWS</parameter>
                    <parameter>REFU</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.travel%">
                    <parameter>OTHR</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.utility_bills%">
                    <parameter>DEPT</parameter>
                    <parameter>BLDM</parameter>
                    <parameter>DCRD</parameter>
                    <parameter>RCKE</parameter>
                    <parameter>TELI</parameter>
                    <parameter>WEBI</parameter>
                    <parameter>CBTV</parameter>
                    <parameter>ELEC</parameter>
                    <parameter>ENRG</parameter>
                    <parameter>GASB</parameter>
                    <parameter>NWCH</parameter>
                    <parameter>NWCM</parameter>
                    <parameter>OTLC</parameter>
                    <parameter>PHON</parameter>
                    <parameter>WTER</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.repayment_of_loans%">
                    <parameter>CLPR</parameter>
                    <parameter>HLRP</parameter>
                    <parameter>LOAN</parameter>
                    <parameter>LOAR</parameter>
                    <parameter>PADD</parameter>
                    <parameter>CSLP</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.tax_payment%">
                    <parameter>BOCE</parameter>
                    <parameter>GOVT</parameter>
                    <parameter>ICCP</parameter>
                    <parameter>IDCP</parameter>
                    <parameter>GFRP</parameter>
                    <parameter>GVEA</parameter>
                    <parameter>GVEB</parameter>
                    <parameter>GVEC</parameter>
                    <parameter>GVED</parameter>
                    <parameter>ESTX</parameter>
                    <parameter>HSTX</parameter>
                    <parameter>INTX</parameter>
                    <parameter>NITX</parameter>
                    <parameter>TAXS</parameter>
                    <parameter>VATX</parameter>
                    <parameter>WHLD</parameter>
                    <parameter>TAXR</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.property_purchase%">
                    <parameter>ADVA</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.property_rental%">
                    <parameter>RENT</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.insurance_premium%">
                    <parameter>INPC</parameter>
                    <parameter>INSU</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.product_indemnity_insurance%">
                    <parameter>PENO</parameter>
                    <parameter>BENE</parameter>
                    <parameter>BONU</parameter>
                    <parameter>GWLT</parameter>
                    <parameter>PENS</parameter>
                    <parameter>RHBS</parameter>
                    <parameter>SSBE</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.insurance_claims_payment%">
                    <parameter>GOVI</parameter>
                    <parameter>LBRI</parameter>
                    <parameter>LIFI</parameter>
                    <parameter>PPTI</parameter>
                    <parameter>ANNI</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.mutual_fund_investment%">
                    <parameter>CMDT</parameter>
                    <parameter>DERI</parameter>
                    <parameter>FREX</parameter>
                    <parameter>HEDG</parameter>
                    <parameter>PRME</parameter>
                    <parameter>SAVG</parameter>
                    <parameter>SECU</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.investment_in_shares%">
                    <parameter>DIVD</parameter>
                    <parameter>COMM</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.donations%">
                    <parameter>COLL</parameter>
                    <parameter>CHAR</parameter>
                    <parameter>DBTC</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.information_service_charges%">
                    <parameter>ETUP</parameter>
                    <parameter>MTUP</parameter>
                    <parameter>RINP</parameter>
                    <parameter>MSVC</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.advertising_expenses%">
                    <parameter>AGRT</parameter>
                    <parameter>AREN</parameter>
                    <parameter>BEXP</parameter>
                    <parameter>SCVE</parameter>
                    <parameter>AIRB</parameter>
                    <parameter>BUSB</parameter>
                    <parameter>FERB</parameter>
                    <parameter>RLWY</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.copyright_fees%">
                    <parameter>CPYR</parameter>
                    <parameter>ROYA</parameter>
                    <parameter>COST</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.guarantee_fees%">
                    <parameter>LICF</parameter>
                    <parameter>CFEE</parameter>
                    <parameter>OFEE</parameter>
                    <parameter>PTSP</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.assistance_expenses%">
                    <parameter>COMC</parameter>
                    <parameter>COMT</parameter>
                    <parameter>TRFD</parameter>
                    <parameter>ADMG</parameter>
                    <parameter>IHRP</parameter>
                    <parameter>INSM</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.representative_office_expenses%">
                    <parameter>AEMP</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.construction_costs%">
                    <parameter>FAND</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.transportation_fees%">
                    <parameter>SUPP</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.payment_for_exported_goods%">
                    <parameter>PRCP</parameter>
                    <parameter>SALA</parameter>
                </parameter>
                <parameter type="collection" key="%evp_bank_transfer.purpose_code.delivery_fees%">
                    <parameter>TRAD</parameter>
                </parameter>
            </parameter>
        </parameter>
        <parameter type="collection" key="evp_bank_transfer.internal_to_external_purpose_code_map">
            <parameter type="collection" key="%evp_bank_transfer.purpose_code_type.instarem%">
                <parameter type="string" key="%evp_bank_transfer.purpose_code.delivery_fees%">IR01810</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.payment_for_exported_goods%">IR01809</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.transportation_fees%">IR01808</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.construction_costs%">IR01807</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.representative_office_expenses%">IR01806</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.assistance_expenses%">IR01805</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.guarantee_fees%">IR01804</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.copyright_fees%">IR01803</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.advertising_expenses%">IR01802</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.information_service_charges%">IR01801</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.donations%">IR017</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.investment_in_shares%">IR016</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.mutual_fund_investment%">IR015</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.insurance_claims_payment%">IR014</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.product_indemnity_insurance%">IR013</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.insurance_premium%">IR012</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.property_rental%">IR011</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.property_purchase%">IR010</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.tax_payment%">IR009</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.repayment_of_loans%">IR008</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.utility_bills%">IR007</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.travel%">IR006</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.hotel_accommodation%">IR005</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.medical_treatment%">IR004</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.education_expenses%">IR003</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.family_maintenance%">IR002</parameter>
                <parameter type="string" key="%evp_bank_transfer.purpose_code.transfer_to_own_account%">IR001</parameter>
            </parameter>
            <parameter type="collection" key="%evp_bank_transfer.purpose_code_type.sepa%">
                <parameter type="string" key="%evp_bank_transfer.purpose_code.utility_bills%">UBIL</parameter>
            </parameter>
        </parameter>

        <parameter key="evp_bank_transfer.postbank.our_account.bic">BPBIBGSF</parameter>
        <parameter key="evp_bank_transfer.postbank.our_account.iban.bgn">**********************</parameter>
        <parameter key="evp_bank_transfer.postbank.our_account.iban.usd">**********************</parameter>
        <parameter key="evp_bank_transfer.postbank.our_account.iban.eur">**********************</parameter>

        <parameter key="evp_bank_transfer.allianz_bg.our_account.bic">BUINBGSF</parameter>
        <parameter key="evp_bank_transfer.allianz_bg.our_account.iban.bgn">**********************</parameter>
        <parameter key="evp_bank_transfer.allianz_bg.our_account.iban.eur">**********************</parameter>

        <parameter key="evp_bank_transfer.client_charge_type.offshore" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferAdditionalCommissionManager::CLIENT_CHARGE_TYPE_OFFSHORE</parameter>
        <parameter key="evp_bank_transfer.client_charge_type.psp" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferAdditionalCommissionManager::CLIENT_CHARGE_TYPE_PSP</parameter>
        <parameter key="evp_bank_transfer.client_charge_type.non_sepa" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferAdditionalCommissionManager::CLIENT_CHARGE_TYPE_NON_SEPA</parameter>

        <parameter key="evp_bank_transfer.transfer_charge.amount" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferAdditionalCommissionManager::CHARGE_AMOUNT</parameter>

        <parameter key="evp_bank_transfer.transfer_charge_type.in" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_IN</parameter>
        <parameter key="evp_bank_transfer.transfer_charge_type.sepa_out" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_SEPA_OUT</parameter>
        <parameter key="evp_bank_transfer.transfer_charge_type.target2_out" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_TARGET2_OUT</parameter>
        <parameter key="evp_bank_transfer.transfer_charge_type.very_urgent" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_USD_VERY_URGENT</parameter>
        <parameter key="evp_bank_transfer.transfer_charge_type.internal" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_INTERNAL</parameter>
        <parameter key="evp_bank_transfer.transfer_charge_type.international" type="constant">Evp\Bundle\BankTransferBundle\Service\TransferChargeTypeResolver::TYPE_INTERNATIONAL</parameter>

        <parameter key="evp_bank_transfer.purpose_code.transfer_to_own_account" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_TRANSFER_TO_OWN_ACCOUNT</parameter>
        <parameter key="evp_bank_transfer.purpose_code.family_maintenance" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_FAMILY_MAINTENANCE</parameter>
        <parameter key="evp_bank_transfer.purpose_code.education_expenses" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_EDUCATION_EXPENSES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.medical_treatment" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_MEDICAL_TREATMENT</parameter>
        <parameter key="evp_bank_transfer.purpose_code.hotel_accommodation" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_HOTEL_ACCOMMODATION</parameter>
        <parameter key="evp_bank_transfer.purpose_code.travel" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_TRAVEL</parameter>
        <parameter key="evp_bank_transfer.purpose_code.utility_bills" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_UTILITY_BILLS</parameter>
        <parameter key="evp_bank_transfer.purpose_code.repayment_of_loans" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_REPAYMENT_OF_LOANS</parameter>
        <parameter key="evp_bank_transfer.purpose_code.tax_payment" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_TAX_PAYMENT</parameter>
        <parameter key="evp_bank_transfer.purpose_code.property_purchase" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_PROPERTY_PURCHASE</parameter>
        <parameter key="evp_bank_transfer.purpose_code.property_rental" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_PROPERTY_RENTAL</parameter>
        <parameter key="evp_bank_transfer.purpose_code.insurance_premium" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_INSURANCE_PREMIUM</parameter>
        <parameter key="evp_bank_transfer.purpose_code.product_indemnity_insurance" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_PRODUCT_INDEMNITY_INSURANCE</parameter>
        <parameter key="evp_bank_transfer.purpose_code.insurance_claims_payment" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_INSURANCE_CLAIMS_PAYMENT</parameter>
        <parameter key="evp_bank_transfer.purpose_code.mutual_fund_investment" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_MUTUAL_FUND_INVESTMENT</parameter>
        <parameter key="evp_bank_transfer.purpose_code.investment_in_shares" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_INVESTMENT_IN_SHARES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.donations" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_DONATIONS</parameter>
        <parameter key="evp_bank_transfer.purpose_code.information_service_charges" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_INFORMATION_SERVICE_CHARGES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.advertising_expenses" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_ADVERTISING_EXPENSES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.copyright_fees" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_COPYRIGHT_FEES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.guarantee_fees" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_GUARANTEE_FEES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.assistance_expenses" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_ASSISTANCE_EXPENSES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.representative_office_expenses" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_REPRESENTATIVE_OFFICE_EXPENSES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.construction_costs" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_CONSTRUCTION_COSTS</parameter>
        <parameter key="evp_bank_transfer.purpose_code.transportation_fees" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_TRANSPORTATION_FEES</parameter>
        <parameter key="evp_bank_transfer.purpose_code.payment_for_exported_goods" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_PAYMENT_FOR_EXPORTED_GOODS</parameter>
        <parameter key="evp_bank_transfer.purpose_code.delivery_fees" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::PURPOSE_CODE_DELIVERY_FEES</parameter>

        <parameter key="evp_bank_transfer.purpose_code_type.sepa" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::TYPE_SEPA</parameter>
        <parameter key="evp_bank_transfer.purpose_code_type.instarem" type="constant">Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver::TYPE_INSTAREM</parameter>

        <parameter type="collection" key="evp_bank_transfer.support_contacts">
            <parameter type="collection" key="LT">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+***********</parameter>
            </parameter>
            <parameter type="collection" key="LV">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+***********</parameter>
            </parameter>
            <parameter type="collection" key="BG">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+***********</parameter>
            </parameter>
            <parameter type="collection" key="EE">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+**********</parameter>
            </parameter>
            <parameter type="collection" key="PL">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+48222062146</parameter>
            </parameter>
            <parameter type="collection" key="ES">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+34937370166</parameter>
            </parameter>
            <parameter type="collection" key="GB">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+442080996963</parameter>
            </parameter>
            <parameter type="collection" key="DE">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+493056796555</parameter>
            </parameter>
            <parameter type="collection" key="FR">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+33186653416</parameter>
            </parameter>
            <parameter type="collection" key="RO">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string"> +***********</parameter>
            </parameter>
            <parameter type="collection" key="XK">
                <parameter key="support_email" type="string"><EMAIL></parameter>
                <parameter key="support_phone_number" type="string">+383 38 607607</parameter>
            </parameter>
        </parameter>

        <parameter type="collection" key="evp_bank_transfer.transfer_failure_status_parameters">
            <parameter type="collection" key="country_specific">
                <parameter key="transfer_failure_status.mapper_empty_client_natural_code">%evp_bank_transfer.support_contacts%</parameter>
                <parameter key="transfer_failure_status.mapper_empty_client_legal_code">%evp_bank_transfer.support_contacts%</parameter>
                <parameter key="transfer_failure_status.mapper_empty_date_of_birth">%evp_bank_transfer.support_contacts%</parameter>
            </parameter>
            <parameter type="collection" key="external_routes">
                <parameter type="collection" key="transfer_failure_status.beneficiary_forbiden">
                    <parameter key="redemption_limitations_page" type="constant">Paysera\Bundle\ExternalRoutingBundle\ExternalRoutes::EXTERNAL_ROUTE_PAYSERA_MONEY_REDEMPTION_LIMITATIONS</parameter>
                </parameter>
                <parameter type="collection" key="transfer_failure_status.address_is_required">
                    <parameter key="link_to_questionnaire_form" type="constant">Paysera\Bundle\ExternalRoutingBundle\ExternalRoutes::EXTERNAL_ROUTE_PAYSERA_QUESTIONNAIRE_FORM</parameter>
                </parameter>
                <parameter type="collection" key="transfer_failure_status.sepa_inst_unavailable">
                    <parameter key="link_to_fees_for_transfer_in_euro" type="constant">Paysera\Bundle\ExternalRoutingBundle\ExternalRoutes::EXTERNAL_ROUTE_PAYSERA_FRONTEND_EURO_TRANSFER_FEES</parameter>
                </parameter>
                <parameter type="collection" key="transfer_failure_status.sepa_inst_bank_unavailable">
                    <parameter key="link_to_fees_for_transfer_in_euro" type="constant">Paysera\Bundle\ExternalRoutingBundle\ExternalRoutes::EXTERNAL_ROUTE_PAYSERA_FRONTEND_EURO_TRANSFER_FEES</parameter>
                </parameter>
            </parameter>
        </parameter>

        <parameter key="evp_bank_transfer.ignitis.payment_code_electricity_public_supply">103500</parameter>
        <parameter key="evp_bank_transfer.ignitis.payment_code_electricity_independent_supply">203000</parameter>
        <parameter key="evp_bank_transfer.ignitis.payment_code_gas">10001</parameter>

        <parameter key="evp_bank_transfer.ignitis.condition.tax_line_regex_key" type="constant">Evp\Bundle\BankTransferBundle\Validator\TaxLines\BaseTaxLineRegexValidator::TAX_LINE_REGEX_KEY</parameter>
        <parameter key="evp_bank_transfer.ignitis.condition.validate_tax_line_key" type="constant">Evp\Bundle\BankTransferBundle\Validator\TaxLines\BaseTaxLineRegexValidator::VALIDATE_TAX_LINE_KEY</parameter>
        <parameter key="evp_bank_transfer.ignitis.condition.first_digit_multiplier_key" type="constant">Evp\Bundle\BankTransferBundle\Validator\TaxLines\BaseTaxLineRegexValidator::FIRST_DIGIT_MULTIPLIER_KEY</parameter>
        <parameter key="evp_bank_transfer.ignitis.condition.checksum_digit_multiplier_key" type="constant">Evp\Bundle\BankTransferBundle\Validator\TaxLines\BaseTaxLineRegexValidator::CHECKSUM_DIGIT_MULTIPLIER_KEY</parameter>
        <parameter key="evp_bank_transfer.ignitis.condition.remainder_divider_key" type="constant">Evp\Bundle\BankTransferBundle\Validator\TaxLines\BaseTaxLineRegexValidator::REMAINDER_DIVIDER_KEY</parameter>

        <parameter type="collection" key="evp_bank_transfer.route.special_client_transfer_out_commission">
            <parameter type="collection" key="714773">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1304711">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1195529">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1308050">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1341326">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1608539">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="918394">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="918376">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1778150">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5110560">
                <parameter key="amount" type="string">0.15</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1900929">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5285898">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="840215">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1105877">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="212996">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="918544">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5500101">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5659143">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1565357">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5737608">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5640003">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5462685">
                <parameter key="amount" type="string">0.15</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5619273">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5396822">
                <parameter key="amount" type="string">0.1</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1965060">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1105877">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5709744">
                <parameter key="amount" type="string">0.15</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1395110">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5811090">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5403750">
                <parameter key="amount" type="string">0.15</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5795019">
                <parameter key="amount" type="string">0.25</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5761716">
                <parameter key="amount" type="string">0.20</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1304711">
                <parameter key="amount" type="string">0.30</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5800362">
                <parameter key="amount" type="string">0.20</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="859508">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5728701">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="1909167">
                <parameter key="amount" type="string">0.20</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6010317">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6055719">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5305692">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6270891">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5858388">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5780973">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6288033">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6321264">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5425929">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="217046">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5659635">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6238449">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5308191">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="5270733">
                <parameter key="amount" type="string">0.15</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="7014930">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="6887790">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="7087486">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="8090427">
                <parameter key="amount" type="string">0.5</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="8008335">
                <parameter key="amount" type="string">0.2</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="8989646">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="58374">
                <parameter key="amount" type="string">0.05</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="10170758">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="10763438">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="11069426">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="121455">
                <parameter key="amount" type="string">0.03</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
            <parameter type="collection" key="8552430">
                <parameter key="amount" type="string">0.10</parameter>
                <parameter key="currency" type="string">EUR</parameter>
            </parameter>
        </parameter>

        <parameter type="collection" key="evp_bank_transfer.route.internal_transfer_count_threshold">
            <parameter type="collection">
                <parameter key="date" type="string">2024-11-01</parameter>
                <parameter key="count" type="string">30</parameter>
            </parameter>
        </parameter>
    </parameters>

    <services>
        <service id="evp_bank_transfer.static_cache"
                 class="Evp\Component\Cache\StaticTimedCache">
            <argument type="service" id="evp.component.time.clock"/>
        </service>

        <!-- MainPayerTransferPartyResolver -->
        <service id="evp_bank_transfer.main_payer_transfer_party_resolver" class="Evp\Bundle\BankTransferBundle\Service\MainPayerTransferPartyResolver">
            <argument>%evp_bank_transfer.transfer_parties.main_payer_parties%</argument>
            <argument type="service" id="service_container"/>
            <argument type="service" id="logger"/>
        </service>

        <!-- TransferExecutionTimeCalculator -->
        <service id="evp_bank_transfer.transfer_execution_time_calculator"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferExecutionTimeCalculator">
            <argument type="service" id="evp_bank_transfer.transfer_configuration"/>
            <argument type="service" id="evp_bank_transfer.transfer_routing_information_retriever"/>
            <argument>900</argument> <!-- 15 minutes default sla time -->
            <argument>%evp_bank_transfer.operator_window_time%</argument> <!-- 5 minutes operator window time -->
            <argument type="service" id="evp_commons.clock"/>
            <argument type="service" id="evp_calendar.service.holidays"/>
            <argument type="service" id="evp_bank_transfer.working_hours_calculator"/>
            <argument type="service" id="logger"/>
        </service>

        <!-- TransferRoutingInformationRetriever -->
        <service id="evp_bank_transfer.transfer_routing_information_retriever"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRoutingInformationRetriever" lazy="true">

            <argument type="service" id="evp_bank.iban_bank_resolver" />
            <argument type="service" id="evp_bank.iban_country_resolver" />
            <argument type="service" id="evp_bank.swift_bank_resolver" />
            <argument type="service" id="evp_bank.swift_country_resolver" />
            <argument type="service" id="evp_bank.bic_bank_resolver" />
            <argument>webmoney</argument>
            <argument>payza_automatic</argument>
            <argument>bg_budget_postbank</argument>
            <argument>%paysera_privatbank.bank_key%</argument>
            <argument type="service" id="logger" />
        </service>

        <!-- TransferConverter -->
        <service id="evp_bank_transfer.transfer_converter"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferConverter">
            <argument type="service" id="evp_currency.currency_renewer"/>
        </service>

        <!-- StalledTransfersReporter -->
        <service id="evp_bank_transfer.stalled_transfers_reporter"
                 class="Evp\Bundle\BankTransferBundle\Service\StalledTransfersReporter">
            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_currency_convert"/>
            <argument type="service" id="logger"/>
        </service>

        <!-- TransferListener Listener -->
        <service id="evp_bank_transfer.transfer_listener"
                 class="Evp\Bundle\BankTransferBundle\Listener\TransferListener">

            <!-- onBeneficiaryAliasNotFound -->
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.beneficiary_alias_not_found"
                 method="onBeneficiaryAliasNotFound" priority="0" />

            <!-- onBecameNotReadyToComplete -->
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.became_not_ready_to_complete"
                 method="onBecameNotReadyToComplete" priority="0" />

            <!-- onBeneficiaryAliasNotFound | Persisted -->
            <!--<tag name="kernel.event_listener" event="evp_bank_transfer.transfer.beneficiary_alias_not_found.persisted"-->
                 <!--method="onBeneficiaryAliasNotFoundPersisted" priority="0" />-->

            <!-- onStatusReserved | Persisted -->
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.status_reserved.persisted"
                 method="onStatusReservedPersisted" priority="0" />

            <!-- onDebitAccountAvailable | Persisted -->
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.debit_account_available.persisted"
                 method="onDebitAccountAvailablePersisted" priority="0" />

            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.status_done.persisted"
                 method="onTransferDonePersisted" priority="0" />

            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.status_signed"
                 method="onTransferSigned" priority="10" />

            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.password_entered"
                 method="onPasswordEntered" priority="0" />
            <!-- onDebitAccountAvailable | Persisted -->
            <tag name="kernel.event_listener" event="remote.mailer_email_bounce" method="onEmailBounced" />

            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="evp_client_notification.notification_sender"/>
            <argument type="service" id="evp_bank_transfer.transfer_processor.internal"/>
            <argument type="service" id="evp_bank_account.account_alias_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_ready_to_complete_checker"/>
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_notifier"/>
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher" />
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="paysera_mobile_payments.manager.mobile_payment"/>
            <argument type="service" id="evp_bank_transfer.profit_from_conversion_manager"/>
            <argument type="service" id="paysera_external_routing.service.external_routing_manager"/>
            <argument type="service" id="evp_bank_transfer.validator.transfer_internal_validator"/>
            <argument type="service" id="evp_bank_transfer.validator.phone"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.listener.client_restriction"
                 class="Evp\Bundle\BankTransferBundle\Listener\ClientRestrictionListener">

            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher" />
            <argument type="service" id="logger" />
        </service>

        <!-- AccountListener Listener -->
        <service id="evp_bank_transfer.account_new_alias_listener"
                 class="Evp\Bundle\BankTransferBundle\Listener\AccountListener">

            <!-- onNewAlias -->
            <tag name="kernel.event_listener" event="evp_bank_account.account.new_alias.persisted"
                 method="onNewAlias" priority="0" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="logger" />
        </service>

        <!-- AccountIncome Listener -->
        <service id="evp_bank_transfer.account_income_listener"
        		class="Evp\Bundle\BankTransferBundle\Listener\AccountIncomeListener">

            <tag name="kernel.event_listener" event="evp_bank_account.account.balance_decrease.persisted"
                 method="onAccountDecrease" priority="0" />

            <argument type="service" id="evp_bank_transfer.repository.transfer" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_event_publisher" />
            <argument type="service" id="evp_bank_account.account_balance_manager" />
            <argument type="service" id="logger" />
        </service>

        <!-- Refund listener: releases holds, related to transfer, before refund is made -->
        <service id="evp_bank_transfer.refund_listener.before_refund"
                 class="Evp\Bundle\BankTransferBundle\Listener\RefundListener">
            <tag name="kernel.event_listener" event="evp_bank_refund.before_refund" method="onRefundEvent"/>
            <tag name="monolog.logger" channel="evp_bank_transfer.refund_listener.before_refund" />

            <argument type="service" id="evp_bank_hold.hold_release_processor" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_transfer_tax.provider_manager" />
        </service>

        <service id="evp_bank_transfer.funds_source_listener"
                 class="Evp\Bundle\BankTransferBundle\Listener\FundsSourceListener">
            <tag name="kernel.event_listener" event="soap.resolved" method="onSoapResolvedEvent"/>
            <argument type="service" id="evp_client.repository.client" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.listener.related_currency_convert_transfer"
                 class="Evp\Bundle\BankTransferBundle\Listener\RelatedCurrencyConvertTransferListener">
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.status_done" method="onTransferDone" />
            <tag name="kernel.event_listener" event="evp_bank_transfer.transfer.status_failed" method="onTransferFail" />

            <argument type="service" id="evp_bank_transfer.transfer_processor.currency_convert" />
            <argument type="service" id="evp_bank_transfer.transfer_status_manager" />
        </service>

        <service id="evp_bank_transfer.transfer_manager" class="Evp\Bundle\BankTransferBundle\Service\TransferManager">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_manager" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_status_manager"/>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.in.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.internal.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.out.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.currency_convert.prepare</argument>
                </service>
            </argument>
            <argument type="service" id="evp_bank_transfer.repository.transfer_out"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in"/>
            <argument type="service" id="evp_protected_transfer.transfer_password_manager"/>
            <argument type="service" id="evp_bank_transfer.funds_source_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_email_manager"/>
            <argument type="service" id="evp_bank_transfer.hash_generator.prefix_hash_generator"/>
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_permission.permission_manager" />
            <argument type="service" id="evp_bundle_bank_transfer.service.slave_connection_transfer_countries_provider"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_status_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferStatusManager">
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.transfer_manager.currency_finalization"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferManager">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_manager" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_status_manager"/>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.in.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.internal.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.out.prepare</argument>
                </service>
            </argument>
            <argument type="service">
                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\LazyLoadedTransferProcessor">
                    <argument type="service" id="service_container"/>
                    <argument>evp_bank_transfer.transfer_processor.currency_convert.prepare.currency_finalization</argument>
                </service>
            </argument>
            <argument type="service" id="evp_bank_transfer.repository.transfer_out"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in"/>
            <argument type="service" id="evp_protected_transfer.transfer_password_manager"/>
            <argument type="service" id="evp_bank_transfer.funds_source_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_email_manager"/>
            <argument type="service" id="evp_bank_transfer.hash_generator.prefix_hash_generator"/>
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_permission.permission_manager" />
            <argument type="service" id="evp_bundle_bank_transfer.service.slave_connection_transfer_countries_provider"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_processor_proxy"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferProcessorProxy">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_processor_proxy" />

            <argument type="service" id="evp_bank_transfer.transfer_processor.in" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.internal" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.out" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.currency_convert" />
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>transfer_processing_time</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_sign_manager"
                class="Evp\Bundle\BankTransferBundle\Service\TransferSignManager">
            <argument type="service" id="logger" />
            <argument type="service" id="evp_bank_transfer.turnover_calculator" />
            <argument type="service" id="evp_client.client_restriction_manager" />
            <argument type="service" id="evp_bank_permission.permission_manager" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_transfer.transfer_status_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_internal_to_self_checker"/>
            <argument type="service" id="evp_bank_transfer.transfer_manager"/>
            <argument type="service" id="evp_bank_transfer.checker.transfer_party_account_checker"/>
        </service>

        <service id="evp_bank_transfer.turnover_calculator"
                class="Evp\Bundle\BankTransferBundle\Service\TurnoverCalculator" lazy="true">
            <tag name="monolog.logger" channel="evp_bank_transfer.turnover_calculator" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_currency.currency_converter.official.cached" />
            <argument type="service" id="evp_bank_permission.signing_limits_manager" />
            <argument type="service" id="evp_bank_permission.service.cash_limits_manager" />
            <argument type="collection">
                <argument type="service" id="evp_bank_transfer.limits.unexplained_funds_source_cash_out_limit"/>
                <argument type="service" id="evp_bank_transfer.limits.unexplained_funds_source_cash_out_limit_per_day"/>
            </argument>
            <argument type="collection">
                <argument type="service" id="evp_bank_transfer.limits.unexplained_funds_source_cash_in_limit"/>
                <argument type="service" id="evp_bank_transfer.limits.unexplained_funds_source_cash_in_limit_per_day"/>
            </argument>
            <argument type="service">
                <service class="Evp\Component\Cache\DoctrineRelatedCache">
                    <argument type="service" id="doctrine.orm.default_entity_manager"/>
                </service>
            </argument>
            <argument type="service" id="evp_bank_account.account_balance_manager.internal" />
            <argument type="service" id="evp_bank_account.repository.account" />
            <argument type="service" id="evp_bank_transfer.repository.transfer" />
            <argument type="service" id="evp_client.repository.restriction_limit" />
            <argument type="service" id="evp_bank_permission.repository.sign_permission" />
            <argument type="service" id="evp_bank_transfer.helper.limits_time_helper" />
            <argument type="service" id="evp_bank_permission.permission_ancestors_provider" />
            <argument type="service" id="evp_bank_transfer.cash_turnover_calculator_with_cache" />
            <argument type="service" id="evp_bank_transfer.service.special_cash_limit_resolver"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.cash_turnover_calculator_with_cache"
                 class="Evp\Bundle\BankTransferBundle\Service\CashTurnoverCalculatorWithCache">
            <argument id="evp_currency.currency_converter.market_value" type="service"/>
            <argument type="service">
                <service class="Evp\Component\Cache\DoctrineRelatedCache">
                    <argument type="service" id="doctrine.orm.default_entity_manager"/>
                </service>
            </argument>
            <argument id="evp_bank_transfer.repository.transfer" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_request_factory_api"
                class="Evp\Bundle\BankTransferBundle\Service\TransferRequestFactoryApi" public="false">
                <tag name="monolog.logger" channel="evp_bank_transfer.transfer_request_factory_api"/>
        </service>

        <service id="evp_bank_transfer.transfer_request_factory_file_import"
                class="Evp\Bundle\BankTransferBundle\Service\TransferRequestFactoryFileImport" public="false">
                <tag name="monolog.logger" channel="evp_bank_transfer.transfer_request_factory_file_import"/>
        </service>

        <service id="evp_bank_transfer.transfer_router" class="Evp\Bundle\BankTransferBundle\Service\TransferRouter">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_router" />

            <argument type="service" id="evp_bank_transfer.transfer_configuration" />
            <argument type="service" id="evp_bank_transfer.main_payer_transfer_party_resolver" />
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
            <argument type="service" id="evp_transfer_tax.provider_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_routing_information_retriever"/>
            <argument type="service" id="evp_bank_transfer.transfer_bank_changes_manager"/>
            <argument type="service" id="logger" />
            <argument>%evp_bank_transfer.vmi_payment_key%</argument>
            <argument type="service" id="evp_questionnaire.service.questionnaire_cached_result_provider"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_bank_transfer.router_exclude_checker"/>
        </service>

        <service id="evp_bank_transfer.router_exclude_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRouterExcludeChecker">
            <argument>%paysera_target2_rtgs.target2_bank_key%</argument>
            <argument>%paysera_georgia_rtgs.paysera_deposit_account%</argument>
            <argument>%paysera_georgia_rtgs.georgia_rtgs_bank_key%</argument>
            <argument>%paysera_georgia_rtgs.paysera_transaction_return_iban%</argument>
        </service>

        <service id="evp_bank_transfer.commission_exclude_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferCommissionExcludeChecker">
            <argument type="service" id="evp_bank_transfer.router_exclude_checker"/>
        </service>

        <service id="evp_bank_transfer.code_generator"
            class="Evp\Bundle\BankTransferBundle\Service\CodeGenerator">
            <argument type="string">AaBbCcDdEeFfGgHhiJjKkLMmNnoPpQqRrSsTtUuVvWwXxYyZz</argument>
            <argument type="string">********</argument>
        </service>

        <service id="evp_bank_transfer.payer_party_updater"
            class="Evp\Bundle\BankTransferBundle\Service\PayerPartyUpdater">

            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_transfer.statement_assembler.transfer_in" />
            <argument type="service" id="validator" />
            <argument type="service" id="evp_bank.swift_manager" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_rabbit_mq_extension.remote_high_load_event_publisher"/>
            <argument type="service" id="event_dispatcher" />
        </service>

        <!-- TransferToVmiDetector -->
        <service id="evp_bank_transfer.transfer_to_vmi_detector"
                  class="Evp\Bundle\BankTransferBundle\Service\TransferToVmiDetector">
            <argument>%evp_bank_transfer.government_accounts.vmi%</argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_to_latvian_budget_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToIbanDetector">
            <argument>%evp_bank_transfer.government_accounts.lv%</argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_to_estonian_budget_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToIbanDetector">
            <argument>%evp_bank_transfer.government_accounts.ee%</argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_to_bulgarian_budget_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToBulgariaBudgetDetector">
            <argument>%evp_bank_transfer.government_accounts.excluded_bg%</argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_to_romanian_budget_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToRomanianBudgetDetector">
        </service>

        <!-- Validators -->
        <service id="evp_bank_transfer.validator.max_amount_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\MaxAmountValidator">
            <tag name="validator.constraint_validator" alias="evp_max_amount_validator" />

            <argument>%evp_bank_transfer.max_supported_transfer_amount%</argument>
        </service>

        <service id="evp_bank_transfer.validator.iban_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\IbanValidator">
            <argument type="service" id="evp_iban.iban"/>

            <tag name="validator.constraint_validator" alias="evp_iban_validator" />
        </service>

        <service id="evp_bank_transfer.validator.iban_match_transfer_type_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\IbanTransferTypeMatchValidator">
            <call method="addRule">
                <argument type="string">/^BG.{10}[38]/</argument>
                <argument type="string">Evp\Bundle\BankTransferBundle\Entity\TransferOutBank</argument>
            </call>
            <call method="addEqualsRule">
                <argument>%evp_bank_transfer.government_accounts.excluded_bg%</argument>
                <argument type="string">Paysera\Bundle\BgBudgetPaymentBundle\Entity\BgBudgetPaymentTransfer</argument>
            </call>
            <tag name="validator.constraint_validator" alias="evp_iban_transfer_type_match_validator" />
        </service>

        <service id="evp_bank_transfer.validator.phone_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\PhoneValidator">
            <tag name="validator.constraint_validator" alias="evp_phone_validator" />

            <argument type="service" id="evp_bank_transfer.validator.phone"/>
        </service>

        <service id="evp_bank_transfer.validator.vmi_reference_number_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\VmiReferenceNumberValidator">
            <tag name="validator.constraint_validator" alias="evp_vmi_reference_number_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
            <argument type="service" id="evp_bank_transfer.vmi_reference_number_manager" />
            <argument type="service" id="evp_vmi_payments.consolidated_payment_builder" />
        </service>

        <service id="evp_bank_transfer.validator.vmi_currency_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\VmiCurrencyValidator">
            <tag name="validator.constraint_validator" alias="evp_vmi_currency_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
            <argument type="service" id="evp_currency.lithuanian_currency" />
        </service>

        <service id="evp_bank_transfer.validator.vmi_reference_to_payer"
                 class="Evp\Bundle\BankTransferBundle\Validator\VmiReferenceToPayerValidator">
            <tag name="validator.constraint_validator" alias="evp_vmi_reference_to_payer_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
            <argument type="service" id="evp_vmi_payments.service.roik_code_validator"/>
        </service>

        <service id="evp_bank_transfer.validator.ignitis_gas_payer_code_tax_line"
                 class="Evp\Bundle\BankTransferBundle\Validator\TaxLines\IgnitisPayerCodeTaxLineValidator">
            <argument>payer_identifier</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="%evp_bank_transfer.ignitis.condition.tax_line_regex_key%">/\A(\d)(\d{6})(\d)\z/</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.validate_tax_line_key%">true</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.first_digit_multiplier_key%">8</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.checksum_digit_multiplier_key%">7</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.remainder_divider_key%">11</argument>
                </argument>
            </argument>
            <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_REFERENCE_TO_PAYER_INVALID</argument>
            <argument type="service" id="evp_bank_transfer.service.transfer_to_ignitis_detector"/>
            <argument>%evp_bank_transfer.ignitis.payment_code_gas%</argument>
        </service>

        <service id="evp_bank_transfer.validator.ignitis_electricity_public_supply_payer_code_tax_line"
                 class="Evp\Bundle\BankTransferBundle\Validator\TaxLines\IgnitisPayerCodeTaxLineValidator">
            <argument>payer_identifier</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="%evp_bank_transfer.ignitis.condition.tax_line_regex_key%">/\A([1-7])(\d{6})(\d)\z/</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.validate_tax_line_key%">true</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.first_digit_multiplier_key%">2</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.checksum_digit_multiplier_key%">7</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.remainder_divider_key%">11</argument>
                </argument>
                <argument type="collection">
                    <argument key="%evp_bank_transfer.ignitis.condition.tax_line_regex_key%">/\A(\d{6})\z/</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.validate_tax_line_key%">false</argument>
                </argument>
            </argument>
            <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_REFERENCE_TO_PAYER_INVALID</argument>
            <argument type="service" id="evp_bank_transfer.service.transfer_to_ignitis_detector"/>
            <argument>%evp_bank_transfer.ignitis.payment_code_electricity_public_supply%</argument>
        </service>

        <service id="evp_bank_transfer.validator.ignitis_electricity_independent_supply_payer_code_tax_line"
                 class="Evp\Bundle\BankTransferBundle\Validator\TaxLines\IgnitisPayerCodeTaxLineValidator">
            <argument>payer_identifier</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="%evp_bank_transfer.ignitis.condition.tax_line_regex_key%">/\A([1-9])(\d{6})(\d)\z/</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.validate_tax_line_key%">true</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.first_digit_multiplier_key%">2</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.checksum_digit_multiplier_key%">7</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.remainder_divider_key%">11</argument>
                </argument>
                <argument type="collection">
                    <argument key="%evp_bank_transfer.ignitis.condition.tax_line_regex_key%">/\A(\d{6})\z/</argument>
                    <argument key="%evp_bank_transfer.ignitis.condition.validate_tax_line_key%">false</argument>
                </argument>
            </argument>
            <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_REFERENCE_TO_PAYER_INVALID</argument>
            <argument type="service" id="evp_bank_transfer.service.transfer_to_ignitis_detector"/>
            <argument>%evp_bank_transfer.ignitis.payment_code_electricity_independent_supply%</argument>
        </service>

        <service id="evp_bank_transfer.validator.tax_lines"
                 class="Evp\Bundle\BankTransferBundle\Validator\TaxLinesValidator">
            <tag name="validator.constraint_validator" alias="evp_tax_payment_tax_lines_validator" />

            <call method="addTaxLineValidator">
                <argument type="service" id="evp_bank_transfer.validator.ignitis_gas_payer_code_tax_line"/>
            </call>
            <call method="addTaxLineValidator">
                <argument type="service" id="evp_bank_transfer.validator.ignitis_electricity_public_supply_payer_code_tax_line"/>
            </call>
            <call method="addTaxLineValidator">
                <argument type="service" id="evp_bank_transfer.validator.ignitis_electricity_independent_supply_payer_code_tax_line"/>
            </call>
        </service>

        <service id="evp_bank_transfer.validator.tax_currency_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TaxCurrencyValidator">
            <tag name="validator.constraint_validator" alias="evp_tax_currency_validator" />

            <argument type="service" id="evp_currency.lithuanian_currency" />
        </service>

        <service id="evp_bank_transfer.validator.vmi_payer_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\VmiPayerValidator">
            <tag name="validator.constraint_validator" alias="evp_vmi_payer_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
        </service>

        <service id="evp_bank_transfer.validator.vmi_reference_to_beneficiary_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\VmiReferenceToBeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_vmi_reference_to_beneficiary_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector" />
            <argument type="service" id="evp_bank_transfer.validator.reference_to_beneficiary_validator" />
            <argument type="service" id="validator" />
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument>%evp_vmi_payments.payment_manager.vmi_internal_technical_account%</argument>
        </service>

        <service id="evp_bank_transfer.validator.swift_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\SwiftValidator">
            <tag name="validator.constraint_validator" alias="evp_swift_validator" />

            <argument type="service" id="evp_bank.swift_manager" />
            <call method="setSwiftFormatPattern">
                <argument type="string">/^[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}$/i</argument>
            </call>
        </service>

        <service id="evp_bank_transfer.validator.swift_string_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\SwiftStringValidator">
            <tag name="validator.constraint_validator" alias="evp_swift_string_validator" />

            <argument type="service" id="evp_bank_transfer.validator.swift_validator" />
            <argument type="service" id="evp_bank.swift_manager" />
        </service>

        <service id="evp_bank_transfer.validator.transfer_details_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TransferLengthValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_details_validator" />

            <argument>%evp_bank_transfer.max_transfer_details_length%</argument>
        </service>

        <service id="evp_bank_transfer.validator.transfer_reference_number_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TransferLengthValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_reference_number_validator" />

            <argument>%evp_bank_transfer.max_transfer_reference_number_length%</argument>
        </service>

        <service id="evp_bank_transfer.validator.transfer_country_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TransferCountryValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_country_validator" />

            <argument type="service" id="evp_bank.swift_country_resolver" />
            <argument type="service" id="evp_bank.iban_country_resolver" />
        </service>

        <service id="evp_bank_transfer.validator.sodra_reference_number_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\SodraReferenceNumberValidator">
            <tag name="validator.constraint_validator" alias="evp_sodra_reference_number_validator" />

            <argument type="service" id="evp_bank_transfer.sodra_manager" />
        </service>

        <service id="evp_bank_transfer.validator.sodra_reference_to_beneficiary_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\SodraReferenceToBeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_sodra_reference_to_beneficiary_validator" />

            <argument type="service" id="evp_bank_transfer.sodra_manager" />
        </service>

        <service id="evp_bank_transfer.validator.reference_to_beneficiary_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\ReferenceToBeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_reference_to_beneficiary_validator" />
            <argument type="service" id="validator" />
        </service>

        <service id="evp_bank_transfer.validator.forbidden_character_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\ForbiddenCharacterValidator">
            <tag name="validator.constraint_validator" alias="evp_forbidden_character_validator" />
        </service>

        <service id="evp_bank_transfer.validator.transfer_characters_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TransferCharactersValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_characters_validator" />

            <argument type="service" id="validator" />
        </service>

        <service id="evp_bank_transfer.validator.invalid_beneficiary_name_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\InvalidBeneficiaryNameValidator">
            <tag name="validator.constraint_validator" alias="evp_invalid_beneficiary_name_validator" />

            <argument type="service" id="validator" />
        </service>

        <service id="evp_bank_transfer.validator.internal_account_number_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\InternalAccountNumberValidator">
            <tag name="validator.constraint_validator" alias="evp_internal_account_number_validator" />

            <argument type="service" id="evp_bank_account.account_number_generator" />
        </service>

        <service id="evp_bank_transfer.validator.party_account_country.gb"
                 class="Evp\Bundle\BankTransferBundle\Validator\CountrySpecific\PartyAccountCountryGbValidator">
            <tag name="validator.constraint_validator" alias="evp_party_account_country.gb" />

            <argument type="service" id="evp_bank_transfer.validator.sort_code"/>
        </service>

        <service id="evp_bank_transfer.validator.future_date_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\FutureDateValidator">
            <tag name="validator.constraint_validator" alias="evp_future_date_validator" />
        </service>

        <service id="evp_bank_transfer.validator.ocr_code_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\OcrCodeValidator">
            <tag name="validator.constraint_validator" alias="evp_ocr_code_validator" />

            <argument type="service" id="evp_bank_transfer.validator.ocr_code" />
        </service>

        <service id="evp_bank_transfer.validator.inn_code_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\InnCodeValidator">
            <tag name="validator.constraint_validator" alias="evp_inn_code_validator" />
            <argument type="service" id="evp_bank_transfer.additional_information_manager"/>
        </service>

        <service id="evp_bank_transfer.validator.number_length_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\NumberLengthValidator">
            <tag name="validator.constraint_validator" alias="evp_number_length_validator" />
        </service>

        <service id="evp_bank_transfer.validator.unique_transfer_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\UniqueTransferValidator">
            <tag name="validator.constraint_validator" alias="evp_unique_transfer_validator" />

            <argument type="service" id="evp_bank_transfer.repository.transfer" />
        </service>

        <service id="evp_bank_transfer.validator.unique_named_api_transfer_request_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\UniqueNamedApiTransferRequestValidator">
            <tag name="validator.constraint_validator" alias="evp_unique_named_api_transfer_request_validator" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_request_named_api" />
        </service>

        <service id="evp_bank_transfer.validator.unique_successful_named_api_transfer_request_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\UniqueSuccessfulNamedApiTransferRequestValidator">
            <tag name="validator.constraint_validator" alias="evp_unique_successful_named_api_transfer_request_validator" />

            <argument type="service" id="evp_bank_transfer.repository.transfer_request_named_api" />
        </service>

        <service id="evp_bank_transfer.validator.only_internal_transfer_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\OnlyInternalTransferValidator">
            <tag name="validator.constraint_validator" alias="evp_only_internal_transfer_validator" />

            <argument type="service" id="evp_bank_transfer.transfer_internal_converter" />
        </service>

        <service id="evp_bank_transfer.validator.is_instance_of"
                 class="Evp\Bundle\BankTransferBundle\Validator\IsInstanceOfValidator">
            <tag name="validator.constraint_validator" alias="evp_is_instance_of_validator" />
        </service>

        <service id="evp_bank_transfer.validator.verified_client"
                 class="Evp\Bundle\BankTransferBundle\Validator\VerifiedClientValidator">
            <tag name="validator.constraint_validator" alias="evp_verified_client_validator" />
            <argument type="service" id="evp_bank_permission.permission_checker"/>
            <argument type="service" id="evp_bank_permission.manager_permission_checker"/>
        </service>

        <service id="evp_bank_transfer.validator.payment_to_same_account"
                 class="Evp\Bundle\BankTransferBundle\Validator\PaymentToSameAccountValidator">
            <tag name="validator.constraint_validator" alias="evp_payment_to_same_account_validator" />
            <argument type="service" id="evp_bank_transfer.internal_account_resolver" />
            <argument type="service" id="paysera_mobile_payments.manager.mobile_payment" />
        </service>

        <service id="evp_bank_transfer.validator.account_number_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\AccountNumberValidator">
            <tag name="validator.constraint_validator" alias="evp_account_number_validator" />
            <tag name="monolog.logger" channel="evp_bank_transfer.validator.account_number_validator" />

            <argument type="service" id="evp_bank.account_info_resolver"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.validator.transfer_beneficiary"
                 class="Evp\Bundle\BankTransferBundle\Validator\BeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_beneficiary_validator" />
        </service>

        <service id="evp_bank_transfer.validator.transfer_final_beneficiary"
                 class="Evp\Bundle\BankTransferBundle\Validator\FinalBeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_final_beneficiary_validator" />
        </service>

        <service id="evp_bank_transfer.validator.transfer_paysera_account"
                 class="Evp\Bundle\BankTransferBundle\Validator\PayseraAccountValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_paysera_account_validator" />
        </service>

        <service id="evp_bank_transfer.validator.vo_code"
                 class="Evp\Bundle\BankTransferBundle\Validator\VoCodeValidator">
            <tag name="validator.constraint_validator" alias="evp_vo_code_validator" />

            <argument>%evp_bank_transfer.vo_codes%</argument>
        </service>

        <service id="evp_bank_transfer.validator.language"
                 class="Evp\Component\Validation\Symfony\LanguageValidator">
            <tag name="validator.constraint_validator" alias="evp_language_validator" />
            <argument>%available_locales%</argument>
        </service>

        <service id="evp_bank_transfer.validator.common_transfer"
                 class="Evp\Bundle\BankTransferBundle\Validator\CommonTransferValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_common_transfer_validator" />

            <argument type="service" id="evp_bank_transfer.validator.ocr_code" />
        </service>

        <service id="evp_bank_transfer.validator.bank_account"
                 class="Evp\Bundle\BankTransferBundle\Validator\BankAccountValidator">
            <tag name="validator.constraint_validator" alias="evp_bank_account_validator" />
        </service>

        <service id="evp_bank_transfer.validator.additional_information_type"
                 class="Evp\Bundle\BankTransferBundle\Validator\AdditionalInformationTypeValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_additional_information_type_validator" />
        </service>

        <service id="evp_bank_transfer.validator.identifiers"
                 class="Evp\Bundle\BankTransferBundle\Validator\IdentifiersValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_identifiers_validator" />
        </service>

        <service id="evp_bank_transfer.validator.client_identifier_type"
                 class="Evp\Bundle\BankTransferBundle\Validator\ClientIdentifierTypeValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_paysera_client_identifier_type_validator"/>

            <argument type="service" id="evp_bank_transfer.additional_information_manager"/>
            <argument type="service" id="evp_bank_transfer.client_identifier_manager"/>
            <argument type="collection">
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_COMPANY_CODE</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_CUSTOMER_CODE</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_TAX_PAYER_CODE</argument>
            </argument>
            <argument type="collection">
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_PERSONAL_NUMBER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_CUSTOMER_CODE</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_TAX_PAYER_CODE</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_WORKER_IDENTIFICATION_NUMBER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_PASSPORT_NUMBER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_OTHER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_SOCIAL_SECURITY_NUMBER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_DATE_AND_PLACE_OF_BIRTH</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_DRIVER_LICENSE_NUMBER</argument>
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferPartyClientIdentifier\PartyClientIdentifier::TYPE_NONRESIDENT_REGISTRATION_NUMBER</argument>
            </argument>
        </service>

        <service id="evp_bank_transfer.validator.contis_account_supported_currency"
                class="Evp\Bundle\BankTransferBundle\Validator\ContisAccountSupportedCurrencyValidator">
            <tag name="validator.constraint_validator" alias="evp_transfer_contis_account_supported_currency_validator"/>
        </service>

        <service id="evp_bank_transfer.validator.forbidden_emoji_validator"
                class="Evp\Bundle\BankTransferBundle\Validator\ForbiddenEmojiValidator">
            <tag name="validator.constraint_validator" alias="evp_forbidden_emoji_validator"/>
        </service>

        <service id="evp_bank_transfer.transfer_to_self_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToSelfDetector">
            <argument type="service" id="paysera_transfer_surveillance.matcher.transfer_in_client_and_payer_matcher"/>
        </service>

        <service id="evp_bank_transfer.transfer_reservation_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferReservationProvider">
            <tag name="evp_bank_account.reservation_statement_provider" />

            <argument type="service" id="evp_bank_transfer.repository.transfer" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal" />
            <argument type="service" id="evp_bank_account.service.reservation_statement_mapper" />
        </service>

        <service id="evp_bank_transfer.transfer_commission_reservation_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferCommissionReservationProvider">
            <tag name="evp_bank_account.reservation_statement_provider" />

            <argument type="service" id="paysera_client_monthly_charge_fee.repository.client_monthly_transfer_commission" />
            <argument type="service" id="translator"/>
        </service>

        <service id="evp_bank_transfer.provider.transfer_request_file_import"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRequestFileImportProvider">

            <argument type="service" id="evp_bank_transfer.repository.transfer_request_file_import"/>
            <argument type="service" id="evp_bank_permission.repository.read_permission"/>
        </service>

        <service id="evp_bank_transfer.resolver.transfer_request_file_import"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRequestFileImportFailureStatusResolver">
            <argument type="collection">
                <argument key="transfer_additional_info_required" type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_TRANSFER_IMPORT_ADDITIONAL_INFO_REQUIRED</argument>
            </argument>
        </service>

        <service id="evp_bank_transfer.transliterate_filter_lt_to_en"
                 class="Evp\Component\TextFilter\TransliterateFilter">
            <argument type="service" id="evp_bank_transfer.transliterator.lt_danske.eur_simple"/>
        </service>

        <service id="evp_bank_transfer.sodra_manager" class="Evp\Bundle\BankTransferBundle\Service\SodraManager">
            <tag name="monolog.logger" channel="evp_bank_transfer.sodra_manager" />
            <argument type="service" id="evp_bank_transfer.commission_rule.eur_0" />
            <argument type="service" id="evp_transfer_tax.repository.tax_beneficiary" />
            <argument>%evp_bank_transfer.sodra_reference_numbers%</argument>
            <argument>%evp_bank_transfer.government_accounts.sodra%</argument>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.charities_manager" class="Evp\Bundle\BankTransferBundle\Service\CharitiesManager">
            <argument>%evp_bank_transfer.charities_accounts%</argument>
        </service>

        <service id="evp_bank_transfer.validator.forbidden_beneficiary_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\ForbiddenBeneficiaryValidator">
            <tag name="validator.constraint_validator" alias="evp_forbidden_beneficiary_validator" />
            <tag name="monolog.logger" channel="evp_bank_transfer.validator.forbidden_beneficiary_validator" />

            <argument type="service" id="evp_bank.iban_bank_resolver" />
            <argument type="service" id="evp_bank_transfer.transfer_matcher.transfer_matcher" />
            <argument type="service" id="logger" />

            <call method="addForbiddenBicRule">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Entity\ForbiddenBicRule">
                        <argument>%evp_bank_transfer.forbidden_bics.usd%</argument>
                        <argument type="service" id="evp_bank_transfer.transfer_match_data.equals_to_usd"/>
                        <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_BENEFICIARY_FORBIDDEN_BIC</argument>
                    </service>
                </argument>
            </call>
            <call method="addForbiddenCorrespondentBicRule">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Entity\ForbiddenBicRule">
                        <argument>%evp_bank_transfer.forbidden_bics.usd%</argument>
                        <argument type="service" id="evp_bank_transfer.transfer_match_data.equals_to_usd"/>
                        <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferFailureStatus::CODE_BENEFICIARY_CORRESPONDENT_BANK_SWIFT_FORBIDDEN</argument>
                    </service>
                </argument>
            </call>

            <!-- Lithuania -->
            <call method="addIban">
                <argument>********************</argument><!-- Moneybookers Ltd -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO UAB -->
            </call>
            <call method="addIban">
                <argument>********************</argument><!-- MISTERTANGO: IBLIS ENTREPRISE LIMITED -->
            </call>
            <call method="addBank">
                <argument>lt_unicredit</argument><!-- Unicredit LT -->
            </call>
            <call method="addBank">
                <argument>lt_ub</argument><!-- Ūkio bankas LT -->
            </call>
            <call method="addBank">
                <argument>lt_ku_vilniaus_kreditas</argument><!-- Kredito unija „Vilniaus kreditas“ LT -->
            </call>
            <call method="addBank">
                <argument>lt_pajurio_kredito_unija</argument><!-- Pajūrio kredito unija LT -->
            </call>

            <!-- Poland -->
            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>63101010100165932222200000</argument><!-- PL VMI -->
            </call>

            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>68101010100165932222100000</argument><!-- PL VMI -->
            </call>

            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>17101010100165932227000000</argument><!-- PL VMI -->
            </call>

            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>11101010100165932231000000</argument><!-- PL VMI -->
            </call>

            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>73101010100165932222000000</argument><!-- PL VMI -->
            </call>

            <call method="addIban">
                <argument>****************************</argument><!-- PL VMI -->
            </call>
            <call method="addAccount">
                <argument>PL</argument>
                <argument>97101010100165931391200000</argument><!-- PL VMI -->
            </call>

            <!-- Hungary -->
            <call method="addIban">
                <argument>****************************</argument><!-- Webmoney account -->
            </call>
            <call method="addIban">
                <argument>****************************</argument><!-- Webmoney account -->
            </call>

            <!-- Germany -->
            <call method="addIban">
                <argument>**********************</argument><!-- MoneyBookers account -->
            </call>

            <!-- United Kingdom -->
            <call method="addIban">
                <argument>**********************</argument><!-- MoneyBookers account -->
            </call>

            <!-- Internal -->
            <call method="addEvpAccount">
                <argument>%evp_accounting.operation_processor.cashback_account%</argument><!-- EVP internal account for cashbacks -->
            </call>
            <call method="addEvpAccount">
                <argument>%evp_accounting.operation_processor.partner_commission_account%</argument><!-- EVP internal account for partner commissions -->
            </call>
            <call method="addEvpAccount">
                <argument>%evp_accounting.operation_processor.cash_account%</argument><!-- EVP internal account for cash -->
                <argument>%evp_bank_transfer.internal_cash_account_purposes%</argument>
            </call>

            <!-- Forbid internal transfers for PISP accounts -->
            <call method="addIban">
                <argument>********************</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addIban">
                <argument>********************</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addIban">
                <argument>********************</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addIban">
                <argument>********************</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addEvpAccount">
                <argument>EVP1510002952811</argument>
                <argument type="collection"/>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addEvpAccount">
                <argument>EVP7010002980708</argument>
                <argument type="collection"/>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addEvpAccount">
                <argument>EVP2110002980711</argument>
                <argument type="collection"/>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addEvpAccount">
                <argument>EVP5810002963029</argument>
                <argument type="collection"/>
                <argument>true</argument>
                <argument>false</argument>
            </call>
            <call method="addEvpAccount">
                <argument>EVP3410002234703</argument><!-- Registrų centras -->
                <argument type="collection">
                    <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\Transfer::PURPOSE_PLAIS</argument>
                </argument>
            </call>

            <!-- Russia -->
            <call method="addAccount">
                <argument>RU</argument>
                <argument>30109840600000000866</argument><!-- YANDEX.MONEY, NBCO LLC -->
            </call>

            <!-- China -->
            <call method="addAccount"><!-- BTC -->
                <argument>CN</argument>
                <argument>000000501511168204</argument>
            </call>

            <!-- sanction list countries -->
            <call method="addCountry">
                <argument>IR</argument><!-- Iran -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^IR/</argument>
            </call>

            <call method="addCountry">
                <argument>KP</argument><!-- North Korea (Democratic People's Republic of Korea) -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^KP/</argument>
            </call>

            <call method="addCountry">
                <argument>IQ</argument><!-- Iraq -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^IQ/</argument>
            </call>

            <call method="addCountry">
                <argument>CU</argument><!-- Cuba -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^CU/</argument>
            </call>

            <call method="addCountry">
                <argument>SY</argument><!-- Syria -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^SY/</argument>
            </call>

            <call method="addCountry">
                <argument>YE</argument><!-- Yemen -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^YE/</argument>
            </call>

            <call method="addCountry">
                <argument>LY</argument><!-- Libya -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^LY/</argument>
            </call>

            <call method="addCountry">
                <argument>MM</argument><!-- Myanmar -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^MM/</argument>
            </call>

            <call method="addCountry">
                <argument>SD</argument><!-- Sudan -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^SD/</argument>
            </call>

            <call method="addCountry">
                <argument>SS</argument><!-- South Sudan -->
            </call>
            <call method="addIbanRegularExpression">
                <argument>/^SS/</argument>
            </call>
        </service>

        <service id="evp_bank_transfer.transfer_match_data.equals_to_usd"
                 class="Evp\Bundle\BankTransferBundle\Entity\TransferMatchData">

            <call method="setComparisonType">
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferMatchData::COMPARISON_TYPE_EQUALS</argument>
            </call>
            <call method="setTransferCurrency">
                <argument>USD</argument>
            </call>
        </service>
        <service id="evp_bank_transfer.transfer_match_data.not_equals_to_eur"
                 class="Evp\Bundle\BankTransferBundle\Entity\TransferMatchData">

            <call method="setComparisonType">
                <argument type="constant">Evp\Bundle\BankTransferBundle\Entity\TransferMatchData::COMPARISON_TYPE_NOT_EQUALS</argument>
            </call>
            <call method="setTransferCurrency">
                <argument>EUR</argument>
            </call>
        </service>

        <service id="evp_bank_transfer.transfer_facade" class="Evp\Bundle\BankTransferBundle\Service\TransferFacade">
            <argument type="service" id="evp_bank_api.facade_repository" />
            <argument type="service" id="evp_bank_permission.permission_manager" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal" />
            <argument type="service" id="evp_protected_transfer.repository.transfer_with_password" />
        </service>

        <service id="evp_bank_transfer.transfer_internal_facade"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInternalFacade">
            <argument type="service" id="evp_bank_api.facade_repository" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.internal" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.instant" />
            <argument type="service" id="validator" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_bank_account.account_lock" />
            <argument type="service" id="evp_bank_transfer.account_resolver" />
            <argument type="service" id="evp_bank_transfer.transfer_sign_sca_manager"/>
            <argument type="service" id="evp_bank_transfer.bank_transfer_data_factory"/>
            <argument type="service" id="evp_client.client_restriction_manager"/>
        </service>

        <service id="evp_bank_transfer.transfer_internal_converter"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInternalConverter">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_internal_converter" />

            <argument type="service" id="evp_bank_transfer.internal_account_resolver" />
            <argument type="service" id="evp_bank_transfer.manager.convert_transfer_type_to_internal_voter" />
            <argument type="service" id="paysera_iban_alias.repository.iban_alias"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.transfer_out_converter"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferOutConverter">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_out_converter"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_transfer_tax.tax_beneficiary_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.transfer_ready_to_complete_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\ReadyToCompleteChecker">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_ready_to_complete_checker" />
            <argument type="service" id="logger" />

            <call method="addVoter">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\ReadyToCompleteVoterBeneficiary">
                        <!--<argument type="service" id="logger" />-->
                    </service>
                </argument>
            </call>
            <call method="addVoter">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\ReadyToCompleteVoterStatus" />
                </argument>
            </call>
            <call method="addVoter">
                <argument type="service">
                    <service class="Evp\Bundle\ProtectedTransferBundle\Service\ReadyToCompleteVoterTransferPassword">
                        <argument type="service" id="evp_protected_transfer.transfer_password_manager" />
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_bank_transfer.transfer_official_by_partner_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\OfficialByPartnerChecker">
        </service>

        <service id="evp_bank_transfer.unregistered_beneficiary_notifier"
                 class="Evp\Bundle\BankTransferBundle\Service\UnregisteredBeneficiaryNotifier">
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal" />
            <argument type="service" id="evp_client_notification.notification_sender" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.not_signed_transfer_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\NotSignedTransferManager">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_transfer.transfer_status.cancel_transfer_manager" />
            <argument type="service" id="evp_client_notification.notification_sender" />
            <argument>P2M</argument>
            <argument type="collection">
                <argument>P3D</argument>
                <argument>P14D</argument>
                <argument>P27D</argument>
                <argument>P1M14D</argument>
                <argument>P1M27D</argument>
            </argument>
            <argument type="service" id="evp_bank_transfer.repository.transfer_out" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal" />
            <argument type="service" id="evp_bank_permission.sign_permission_provider" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_beneficiary_notifier"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryNotifier">
            <argument type="service" id="evp_bank_account.statement_render.pdf" />
            <argument type="service" id="evp_bank_account.repository.statement" />
            <argument type="service" id="evp_client_notification.notification_sender" />
            <argument type="service" id="evp_bank_transfer.transfer_beneficiary_email_manager"/>
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="logger" />
            <argument type="string">%evp_sepa.bank_key%</argument>
        </service>

        <service id="evp_bank_transfer.transfer_validator"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferValidator">
            <argument type="service" id="evp_bank_transfer.transfer_status_manager" />
            <argument type="service" id="validator" />
        </service>

        <service id="evp_bank_transfer.transfer_configuration.availability_aware_matchable_route"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferConfiguration\AvailabilityAwareMatchableRoute" abstract="true">
            <argument type="service" id="evp_bank_transfer.route.availability.resolver"/>
            <argument type="service" id="evp_calendar.service.holidays"/>
            <argument type="service" id="evp_bank_transfer.working_hours_calculator"/>
            <argument>7</argument>
        </service>

        <service id="evp_bank_transfer.transfer_configuration.transfer_to_client_matchable_route"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferConfiguration\TransferToClientBankMatchableRoute" abstract="true">
            <argument type="service" id="evp_bank.iban_bank_resolver"/>
        </service>

        <service id="evp_bank_transfer.transfer_configuration.local_fee_matchable_route"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferConfiguration\LocalFeeMatchableRoute" abstract="true">
            <argument type="service" id="evp_user_client.user_rest_factory"/>
        </service>

        <service id="evp_bank_transfer.working_hours_calculator" class="Evp\Bundle\BankTransferBundle\Service\WorkingHoursCalculator">
            <argument>%evp_bank_transfer.operator_window_time%</argument> <!-- 5 minutes operator window time -->
        </service>

        <service id="evp_bank_transfer.transfer_count_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferCountManager">
            <argument type="service" id="evp_bank_transfer.repository.transfer" />
        </service>

        <service id="evp_bank_transfer.transfer_currency_convert_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferCurrencyConvertManager">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_account_rest.restriction_checker"/>
            <argument type="service" id="evp_currency.currency_conversion_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_status_manager"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_currency_convert"/>
            <argument type="service" id="evp_client_rest.client_from_scopes_provider"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_status.cancel_transfer_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_sign_manager"/>
        </service>

        <service id="evp_bank_transfer.currency_conversion.currency_convert_funds_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\CurrencyConversion\CurrencyConvertFundsChecker">
            <argument id="evp_bank_account.account_balance_manager.internal" type="service"/>
            <argument id="evp_bank_account.credit_manager" type="service"/>
            <argument id="evp_bank_transfer.currency_conversion.needed_amount_for_conversion_provider" type="service"/>
        </service>

        <service id="evp_bank_transfer.currency_conversion.needed_amount_for_conversion_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\CurrencyConversion\NeededAmountForConversionProvider">
            <argument id="evp_currency.currency_conversion_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_currency_renewer"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferCurrencyRenewer">

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_currency.currency_renewer.finalization" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.internal" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.out" />
            <argument type="service" id="evp_bank_transfer.transfer_processor.currency_convert" />
            <argument type="service" id="evp_bank_account.account_manager" />
            <argument type="service" id="evp_bank_account.internal_account_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_supplement_manager"
            class="Evp\Bundle\BankTransferBundle\Service\TransferSupplementManager">
            <argument type="service" id="evp_bank_account.full_account_balance_manager"/>
        </service>

        <service id="evp_bank_transfer.form.data_transformer.party_bank"
                 class="Evp\Bundle\BankTransferBundle\Form\DataTransformer\PartyBankTransformer">

            <argument type="service" id="doctrine.orm.default_entity_manager" />
        </service>

        <service id="evp_bank_transfer.service.evp_account_resolver"
                class="Evp\Bundle\BankTransferBundle\Service\EvpAccountResolver">
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_client.repository.client"/>
        </service>

        <service id="evp_bank_transfer.internal_account_resolver"
                class="Evp\Bundle\BankTransferBundle\Service\InternalAccountResolver">
            <argument type="service" id="evp_client.repository.transfer_service_agreement"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="paysera_iban_alias.repository.iban_alias"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.manager.transfer_charge"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferChargeManager">
            <argument type="service" id="evp_bank_charge.charge_factory"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="translator"/>
        </service>

        <service id="evp_bank_transfer.transfer_additional_commission_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferAdditionalCommissionManager">
            <tag name="monolog.logger" channel="evp_bank_transfer.transfer_charge_type"/>

            <argument type="service" id="evp_bank_transfer.transfer_charge_type.resolver"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_client.client_legal_flags_provider"/>
            <argument type="service" id="evp_currency.currency_converter.official"/>
            <argument>%evp_bank_transfer.route.matcher.sepa_countries%</argument>
            <argument>%evp_bank_transfer.route.special_client_transfer_out_commission%</argument>
            <argument type="collection">
                <argument type="collection" key="%evp_bank_transfer.transfer_charge_type.very_urgent%">
                    <argument type="collection" key="%evp_bank_transfer.client_charge_type.offshore%">
                        <argument type="service" key="%evp_bank_transfer.transfer_charge.amount%">
                            <service class="Evp\Component\Money\Money">
                                <argument>30</argument>
                                <argument>EUR</argument>
                            </service>
                        </argument>
                    </argument>
                    <argument type="collection" key="%evp_bank_transfer.client_charge_type.psp%">
                        <argument type="service" key="%evp_bank_transfer.transfer_charge.amount%">
                            <service class="Evp\Component\Money\Money">
                                <argument>30</argument>
                                <argument>EUR</argument>
                            </service>
                        </argument>
                    </argument>
                    <argument type="collection" key="%evp_bank_transfer.client_charge_type.non_sepa%">
                        <argument type="service" key="%evp_bank_transfer.transfer_charge.amount%">
                            <service class="Evp\Component\Money\Money">
                                <argument>0</argument>
                                <argument>EUR</argument>
                            </service>
                        </argument>
                    </argument>
                </argument>
                <argument type="collection" key="%evp_bank_transfer.transfer_charge_type.internal%">
                    <argument type="collection" key="%evp_bank_transfer.client_charge_type.offshore%">
                        <argument type="service" key="%evp_bank_transfer.transfer_charge.amount%">
                            <service class="Evp\Component\Money\Money">
                                <argument>1</argument>
                                <argument>EUR</argument>
                            </service>
                        </argument>
                    </argument>
                    <argument type="collection" key="%evp_bank_transfer.client_charge_type.non_sepa%">
                        <argument type="service" key="%evp_bank_transfer.transfer_charge.amount%">
                            <service class="Evp\Component\Money\Money">
                                <argument>0</argument>
                                <argument>EUR</argument>
                            </service>
                        </argument>
                    </argument>
                </argument>
            </argument>
        </service>

        <service id="evp_bank_transfer.transfer_data_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferDataResolver">
            <argument type="service" id="evp_bank_transfer.transfer_routing_information_retriever"/>
        </service>

        <service id="evp_bank_transfer.account_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\AccountResolver">
            <argument type="service" id="evp_bank_account.repository.account" />
            <argument type="service" id="evp_bank_account.account_alias_manager" />
        </service>

        <service id="evp_bank_transfer.transfer_bank_changes_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferBankChangesManager">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_transfer.repository.transfer_bank_change" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_transfer.transfer_validator_registry"/>
            <argument type="service" id="logger" />
            <argument type="service" id="evp_bank_transfer.main_payer_transfer_party_resolver"/>
        </service>

        <service id="evp_bank_transfer.transfer_validator_registry"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferValidatorRegistry">
            <argument type="service" id="evp_transfer_provider.transfer_provider_resolver"/>
            <argument type="service" id="evp_transfer_provider.transfer_provider_factory"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.transfer_solution_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSolutionManager">
            <argument type="service" id="evp_bank_account.account_currency_conversion_solver" />
            <argument type="service" id="evp_bank_transfer.transfer_official_by_partner_checker"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_transfer.transfer_out_solver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferOutSolver">
            <argument type="service" id="evp_bank_transfer.transfer_solution_manager"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_income_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferIncomeHandler">
            <tag name="evp_bank_account.account_income_handler" priority="20"/>

            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_account.account_balance_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.remote_event_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service class="Evp\Bundle\BankTransferBundle\Service\TransferInstantManager"
                 id="evp_bank_transfer.manager.transfer_instant">
            <argument type="service" id="evp_bank_transfer.repository.transfer_instant"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_internal_to_self_checker"/>
            <argument type="service" id="evp_bank_transfer.transfer_sign_sca_helper"/>
        </service>

        <service id="evp_bank_transfer.transfer_internal_to_self_checker"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInternalToSelfChecker"/>

        <service class="Evp\Bundle\BankTransferBundle\Service\TransferRelatedToChargeProviderManager"
                 id="evp_bank_transfer.manager.transfer_related_to_charge_provider">
        </service>

        <service id="evp_bank_transfer.party_client_identifier_factory"
                 class="Evp\Bundle\BankTransferBundle\Service\Factory\PartyClientIdentifierFactory">
        </service>

        <service id="evp_bank_transfer.common_client_identifier_factory"
                 class="Evp\Bundle\BankTransferBundle\Service\Factory\CommonClientIdentifierFactory">
        </service>

        <service id="evp_bank_transfer.profit_from_conversion_calculator"
                 class="Evp\Bundle\BankTransferBundle\Service\ProfitFromConversionCalculator">
            <argument id="evp_currency.currency_converter.market_value.with_time" type="service"/>
            <argument id="evp_currency.maba.math" type="service"/>
            <argument type="string">0.002</argument>
            <argument id="logger" type="service"/>

            <call method="addSafety">
                <argument>LVL</argument>
                <argument type="string">0</argument>
            </call>
            <call method="addSafety">
                <argument>EEK</argument>
                <argument type="string">0</argument>
            </call>
            <call method="addSafety">
                <argument>LTL</argument>
                <argument type="string">0</argument>
            </call>
            <call method="addSafety">
                <argument>AUD</argument>
                <argument type="string">0.00250</argument>
            </call>
            <call method="addSafety">
                <argument>BYN</argument>
                <argument type="string">0.025000</argument>
            </call>
            <call method="addSafety">
                <argument>BGN</argument>
                <argument type="string">0.00007</argument>
            </call>
            <call method="addSafety">
                <argument>CZK</argument>
                <argument type="string">0.00290</argument>
            </call>
            <call method="addSafety">
                <argument>DKK</argument>
                <argument type="string">0.00050</argument>
            </call>
            <call method="addSafety">
                <argument>GBP</argument>
                <argument type="string">0.00210</argument>
            </call>
            <call method="addSafety">
                <argument>EUR</argument>
                <argument type="string">0</argument>
            </call>
            <call method="addSafety">
                <argument>GEL</argument>
                <argument type="string">0.00104</argument>
            </call>
            <call method="addSafety">
                <argument>HKD</argument>
                <argument type="string">0.00400</argument>
            </call>
            <call method="addSafety">
                <argument>INR</argument>
                <argument type="string">0.00400</argument>
            </call>
            <call method="addSafety">
                <argument>ILS</argument>
                <argument type="string">0.00500</argument>
            </call>
            <call method="addSafety">
                <argument>JPY</argument>
                <argument type="string">0.00220</argument>
            </call>
            <call method="addSafety">
                <argument>USD</argument>
                <argument type="string">0.00154</argument>
            </call>
            <call method="addSafety">
                <argument>CAD</argument>
                <argument type="string">0.00240</argument>
            </call>
            <call method="addSafety">
                <argument>KZT</argument>
                <argument type="string">0.00845</argument>
            </call>
            <call method="addSafety">
                <argument>CNY</argument>
                <argument type="string">0.00410</argument>
            </call>
            <call method="addSafety">
                <argument>HRK</argument>
                <argument type="string">0.003300</argument>
            </call>
            <call method="addSafety">
                <argument>PLN</argument>
                <argument type="string">0.00130</argument>
            </call>
            <call method="addSafety">
                <argument>MXN</argument>
                <argument type="string">0.00230</argument>
            </call>
            <call method="addSafety">
                <argument>NZD</argument>
                <argument type="string">0.00030</argument>
            </call>
            <call method="addSafety">
                <argument>NOK</argument>
                <argument type="string">0.00300</argument>
            </call>
            <call method="addSafety">
                <argument>ZAR</argument>
                <argument type="string">0.00200</argument>
            </call>
            <call method="addSafety">
                <argument>RON</argument>
                <argument type="string">0.00039</argument>
            </call>
            <call method="addSafety">
                <argument>RUB</argument>
                <argument type="string">0.000150</argument>
            </call>
            <call method="addSafety">
                <argument>RSD</argument>
                <argument type="string">0.016000</argument>
            </call>
            <call method="addSafety">
                <argument>SGD</argument>
                <argument type="string">0.00300</argument>
            </call>
            <call method="addSafety">
                <argument>SEK</argument>
                <argument type="string">0.00250</argument>
            </call>
            <call method="addSafety">
                <argument>CHF</argument>
                <argument type="string">0.00390</argument>
            </call>
            <call method="addSafety">
                <argument>TRY</argument>
                <argument type="string">0.00250</argument>
            </call>
            <call method="addSafety">
                <argument>HUF</argument>
                <argument type="string">0.00630</argument>
            </call>
            <call method="addSafety">
                <argument>XAU</argument>
                <argument type="string">0.01</argument>
            </call>
            <call method="addSafety">
                <argument>XAG</argument>
                <argument type="string">0.01</argument>
            </call>
            <call method="addSafety">
                <argument>ALL</argument>
                <argument type="string">0.00015</argument>
            </call>
        </service>

        <service id="evp_bank_transfer.profit_from_conversion_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\ProfitFromConversionManager">
            <argument id="evp_bank_transfer.profit_from_conversion_calculator" type="service"/>
            <argument id="evp_rest_user_api_client" type="service"/>
            <argument id="evp_rest_payment_api_client" type="service"/>
        </service>

        <service id="evp_bank_transfer.party_manager"
                class="Evp\Bundle\BankTransferBundle\Service\PartyManager">
            <argument type="service" id="evp_bank_transfer.repository.party_client_identifier"/>
            <argument type="service" id="evp_bank_transfer.repository.party_additional_information"/>
        </service>

        <service id="evp_bank_transfer.client_identifier_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\ClientIdentifierManager">
            <argument type="service" id="evp_bank_transfer.repository.party_client_identifier"/>
            <argument type="service" id="evp_bank_transfer.storage.client_identifier"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.additional_information_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\AdditionalInformationManager">
            <argument type="service" id="evp_bank_transfer.repository.party_additional_information"/>
            <argument type="service" id="evp_bank_transfer.storage.additional_information"/>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
        </service>

        <service id="evp_bank_transfer.manager.convert_transfer_type_to_internal_voter"
                 class="Evp\Bundle\BankTransferBundle\Service\ConvertTransferTypeToInternalVoterManager">
        </service>

        <service id="evp_bank_transfer.transfer_notice_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferNoticeManager"/>

        <service id="evp_bank_transfer.payment_system_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\PaymentSystemResolver">
            <argument type="service" id="evp_sepa.repository.sepa_transaction_in_transfer"/>
            <argument type="service" id="paysera_sepa_instant.repository.transaction_in"/>
            <argument>%paysera_sepa_instant.sepa_instant_bank_key%</argument>
            <argument>%evp_sepa.bank_key%</argument>
        </service>

        <service id="evp_bank_transfer.transfer_matcher.transfer_matcher"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferMatcher\TransferMatcher"/>
        <service id="evp_bank_transfer.client_legal_name_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\ClientLegalNameManager">

            <argument>%evp_client.client_company_type_abbreviations%</argument>
        </service>

        <service id="evp_bank_transfer.resolver.purpose_code"
                 class="Evp\Bundle\BankTransferBundle\Service\PurposeCodeResolver">
            <argument>%evp_bank_transfer.internal_to_external_purpose_code_map%</argument>
            <argument>%evp_bank_transfer.external_to_internal_purpose_code_map%</argument>
        </service>

        <service id="evp_bank_transfer.resolver.postal_address"
                 class="Evp\Bundle\BankTransferBundle\Service\PostalAddressResolver">
            <argument id="evp_transfer_tax.payment_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.form.fail_transfer" class="Evp\Bundle\BankTransferBundle\Form\FailTransferType">
            <tag name="form.type" alias="evp_bank_transfer_fail_transfer_form"/>
            <argument type="service" id="translator"/>
        </service>

        <service id="evp_bank_transfer.funds_source_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\FundsSourceManager">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_bank_transfer.repository.funds_source" type="service"/>
            <argument id="evp_bank_transfer.funds_source_storage" type="service"/>
        </service>

        <service id="evp_bank_transfer.funds_source_storage"
                 class="Evp\Bundle\BankTransferBundle\Service\FundsSourceStorage">
            <argument type="service">
                <service class="SplObjectStorage"/>
            </argument>
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_beneficiary_email_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryEmailManager">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="evp_bank_transfer.repository.transfer_beneficiary_email" type="service"/>
            <argument id="evp_bank_transfer.service.transfer_beneficiary_email_storage" type="service"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_beneficiary_email_storage"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferBeneficiaryEmailStorage">
            <argument type="service">
                <service class="SplObjectStorage"/>
            </argument>
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.document_number_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\DocumentNumberProvider"/>

        <service id="evp_bank_transfer.storage.additional_information"
                 class="Evp\Bundle\BankTransferBundle\Service\AdditionalInformationStorage">
            <argument type="service">
                <service class="SplObjectStorage"/>
            </argument>
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.storage.client_identifier"
                 class="Evp\Bundle\BankTransferBundle\Service\ClientIdentifierStorage">
            <argument type="service">
                <service class="SplObjectStorage"/>
            </argument>
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_out_bank_beneficiary_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferOutBankBeneficiaryResolver">
            <argument id="evp_transfer_tax.repository.tax_beneficiary" type="service"/>
        </service>

        <service id="evp_bundle_bank_transfer.service_transfer_configuration.transfer_to_lb_from_paysera_account_matchable_route"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferConfiguration\TransferToLbFromPayseraAccountMatchableRoute" abstract="true">
            <argument type="service" id="evp_sepa.sepa_participant_detector" />
        </service>

        <service id="evp_bank_transfer.service.cash_limit_transformer"
                 class="Evp\Bundle\BankTransferBundle\Service\CashLimitTransformer"/>

        <service id="evp_bank_transfer.service.special_cash_limit_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\SpecialCashLimitResolver">
            <argument type="service" id="evp_bank_transfer.repository.cash_transfer_limit"/>
            <argument type="service" id="evp_bank_transfer.service.cash_limit_transformer"/>
        </service>

        <service id="evp_bank_transfer.transfer_sign_sca_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\TransferSignScaManager">
            <argument id="evp_bank_transfer.transfer_sign_sca_factory" type="service"/>
            <argument id="evp_bank_transfer.transfer_to_self_detector" type="service"/>
            <argument id="evp_bank_transfer.repository.transfer_sign_sca" type="service"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.exemption_reason_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\ExemptionReasonResolver">
            <argument type="service" id="paysera_transfer_sign.reliable_transfer_manager"/>
        </service>

        <service id="evp_bank_transfer.sca_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\ScaResolver">
            <argument id="security.token_storage" type="service"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.api_client_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\ApiClientResolver">
            <argument id="evp_bank_transfer.transfer_sign_sca_helper" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_sign_sca_factory"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\TransferSignScaFactory">
            <argument id="evp_bank_transfer.api_client_resolver" type="service"/>
            <argument id="evp_bank_transfer.exemption_reason_resolver" type="service"/>
            <argument id="evp_bank_transfer.sca_resolver" type="service"/>
            <argument id="evp_bank_transfer.transfer_sign_by_our_staff_detector" type="service"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_sign_sca_helper"/>
        </service>

        <service id="evp_bank_transfer.transfer_sign_by_our_staff_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\TransferSignByOurStaffDetector">
            <argument id="evp_bank_transfer.transfer_sign_sca_helper" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_sign_sca_helper"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferSignSca\TransferSignScaHelper"/>

        <service id="evp_bank_transfer.internal_transfer_commission_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\InternalTransferCommissionManager">
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
            <argument type="service" id="evp_client.agreement.default"/>
            <argument type="service" id="evp_bank_transfer.transfer_internal_to_self_checker"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal"/>
            <argument>%evp_bank_transfer.route.matcher.categorized_countries%</argument>
            <argument>%evp_bank_transfer.route.matcher.partners_with_no_internal_transfer_commission%</argument>
            <argument type="service" id="evp_bank_transfer.internal_count_threshold_resolver" />
            <argument type="service">
                <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                    <argument>0</argument>
                    <argument type="service">
                        <service class="Evp\Component\Money\Money">
                            <argument>0.0</argument>
                            <argument>EUR</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Component\Money\Money">
                            <argument>0.0</argument>
                            <argument>EUR</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Component\Money\Money">
                            <argument>0.1</argument>
                            <argument>EUR</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>

        <service id="evp_bank_transfer.internal_count_threshold_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\InternalTransferCountThresholdResolver">

            <argument type="service" id="evp.component.time.clock"/>
            <argument type="string">50</argument>
            <argument>%evp_bank_transfer.route.internal_transfer_count_threshold%</argument>
        </service>


        <service id="evp_bank_transfer.service.transfer_to_ignitis_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferToIgnitisDetector">
            <argument type="service" id="evp_transfer_tax.repository.tax_beneficiary"/>
            <argument>ignitis-party</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.sepa_instant_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\SepaInstantResolver">
            <argument type="service" id="paysera_sepa_instant.repository.transaction_out"/>
        </service>

        <service id="evp_bundle_bank_transfer.service_operation_processor.sepa_instant_refund_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\SepaInstantRefundResolver">
            <argument type="service" id="paysera_sepa_instant.repository.transaction_out"/>
            <argument type="service" id="paysera_sepa_instant.repository.payment_return_in"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.sepa_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\SepaResolver">
            <argument type="service" id="evp_sepa.repository.sepa_transaction_out_transfer"/>
            <argument type="string">EVP0410001000364</argument>
            <argument type="string">paysera_sepa</argument>
            <argument type="service" id="logger"/>
            <argument type="string">%evp_sepa.sepa3_account_shutdown_date%</argument>
            <argument type="string">%evp_sepa.sepa3_account_start_date%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.operation_details"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationDetailsService">
        </service>

        <service id="evp_bank_transfer.operation_processor.operation_date_service"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationDateService">
            <argument type="service" id="evp_sepa.repository.sepa_transaction_out_transfer"/>
            <argument type="service" id="paysera_sepa_instant.repository.transaction_out"/>
            <argument type="service" id="paysera_target2_rtgs.repository.transaction_out"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.operation_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationProvider">

            <argument type="collection">
                <argument type="service" id="paysera_sepa_instant.repository.transaction_in"/>
                <argument type="service" id="evp_sepa.repository.sepa_transaction_in_transfer"/>
                <argument type="service" id="paysera_target2.repository.transaction_in"/>
                <argument type="service" id="paysera_target2_rtgs.repository.transaction_in"/>
                <argument type="service" id="evp_sepa.repository.sepa_unresolved_refund_repository"/>
                <argument type="service" id="paysera_georgia_rtgs.repository.transaction_in"/>
                <argument type="service" id="paysera_versobank.repository.transaction_in"/>
            </argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\BeneficiaryPartnerEvaluationService">
            <argument type="service" id="paysera_partner.partner_account_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.operation_covenantee_id_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationCovenanteeIdResolver">
                <argument type="service" id="evp_client.repository.client"/>
        </service>

        <service id="evp_bank_transfer.transfer_in_commission_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInCommissionResolver">
            <argument type="service" id="evp_bundle_bank_transfer.preconfigured_transfer_in_special_client_commission_resolver"/>
            <argument type="service" id="evp_bank_transfer.manageable_special_client_commission_resolver"/>
            <argument type="service" id="evp_bank_transfer.default_transfer_in_commission_resolver"/>
            <argument type="service" id="evp_bank_transfer.transfer_additional_commission_manager"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="translator"/>
            <argument type="collection">
                <argument>%evp_bank_transfer.paysera_client_id%</argument>
            </argument>

            <call method="addAdditionalTextResolver">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferInCommissionCheckoutTextResolver">
                        <argument type="service" id="translator"/>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_bank_transfer.default_transfer_in_commission_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\DefaultTransferInCommissionResolver">

            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">364fa58e-b3ee-4b0e-a610-ad467cd6e1cb</argument>
                        <argument type="string">Incoming SEPA transfers for Kosovo legal clients From 2024-02-22</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>XK</argument>
                                            </argument>
                                            <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2024-02-22 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_300"/>
                    </service>
                </argument>
            </call>

            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">04825afc-d5f9-4f74-9726-d111e1c2987f</argument>
                        <argument type="string">Incoming SEPA transfers for Albanian legal clients From 2024-02-22</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>AL</argument>
                                            </argument>
                                            <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2024-02-22 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_100"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers to Offshore legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">b6dede9b-2a6b-4f31-a076-f10130032ce6</argument>
                        <argument type="string">Incoming SEPA transfers to Offshore legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInLegalClientOffshoreMatcher">
                                            <argument type="service" id="evp_client.client_legal_flags_provider"/>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>0.50</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>

            <!-- Incoming Target2 transfers to Offshore legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">3b2349ca-1d92-4b0a-89bc-dcf4eed9aaf3</argument>
                        <argument type="string">Incoming Target2 transfers to Offshore legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInLegalClientOffshoreMatcher">
                                            <argument type="service" id="evp_client.client_legal_flags_provider"/>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                                    <argument type="collection">
                                                        <!-- Returned target2 -->
                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransactionInUnresolvedReturnedTransferBankMatcher">
                                                                <argument type="service" id="paysera_target2.repository.transaction_in"/>
                                                                <argument type="service" id="paysera_target2.repository.transaction_in_unresolved"/>
                                                                <argument>lt_lb_target2</argument>
                                                            </service>
                                                        </argument>

                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryIbanMatcher">
                                                                <argument>********************</argument>
                                                            </service>
                                                        </argument>
                                                    </argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_rtgs_transaction_in_transfer_entry_matcher"/>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>4</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers to PSP legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">71807a81-e760-4869-a6b8-830eed718062</argument>
                        <argument type="string">Incoming SEPA transfers to PSP legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInLegalClientPSPMatcher">
                                            <argument type="service" id="evp_client.client_legal_flags_provider"/>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>0.50</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>

            <!-- Incoming Target2 transfers to PSP legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">775a5a48-cb8b-4855-bfe9-8779b308d21b</argument>
                        <argument type="string">Incoming Target2 transfers to PSP legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInLegalClientPSPMatcher">
                                            <argument type="service" id="evp_client.client_legal_flags_provider"/>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                                    <argument type="collection">
                                                        <!-- Returned target2 -->
                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransactionInUnresolvedReturnedTransferBankMatcher">
                                                                <argument type="service" id="paysera_target2.repository.transaction_in"/>
                                                                <argument type="service" id="paysera_target2.repository.transaction_in_unresolved"/>
                                                                <argument>lt_lb_target2</argument>
                                                            </service>
                                                        </argument>

                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryIbanMatcher">
                                                                <argument>********************</argument>
                                                            </service>
                                                        </argument>
                                                    </argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_rtgs_transaction_in_transfer_entry_matcher"/>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>4</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers for Albanian legal clients: >=10k EUR -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">********-d1a6-4991-a849-7eccf205d6ea</argument>
                        <argument type="string">Incoming SEPA transfers for Albanian legal clients: >=10k EUR</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>AL</argument>
                                            </argument>
                                            <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\GreaterOrEqualAmountMatcher">
                                            <argument type="service">
                                                <service class="Evp\Component\Money\Money">
                                                    <argument>10000</argument>
                                                    <argument>EUR</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_400"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers for Albanian legal clients: <10k EUR -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">a5901b90-66d9-43b2-b7cc-3267709d8985</argument>
                        <argument type="string">Incoming SEPA transfers for Albanian legal clients: less than 10k EUR</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>AL</argument>
                                            </argument>
                                            <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LessThanAmountMatcher">
                                            <argument type="service">
                                                <service class="Evp\Component\Money\Money">
                                                    <argument>10000</argument>
                                                    <argument>EUR</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_100"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers for Albanian clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">141af032-2cb5-44ea-be61-b349546580c8</argument>
                        <argument type="string">Incoming SEPA transfers for Albanian clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>AL</argument>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_0"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers for KOSOVO clients from 2022-04-18 -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">70d96c02-22dd-4d0c-beaf-acbc5ad38f56</argument>
                        <argument type="string">Incoming SEPA transfers for KOSOVO clients from 2022-04-18</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2022-04-18 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service">
                                                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                        <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                        <argument type="collection">
                                                            <argument>XK</argument>
                                                        </argument>
                                                        <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                    </service>
                                                </argument>
                                                <argument type="service">
                                                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\ClientResidenceCountryMatcher">
                                                        <argument type="collection">
                                                            <argument>XK</argument>
                                                        </argument>
                                                    </service>
                                                </argument>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_300"/>
                    </service>
                </argument>
            </call>

            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">a6dd379a-6dc7-48d7-b5ac-df864ced7c7c</argument>
                        <argument type="string">Incoming SEPA INSTANT transfers for ALBANIA legal clients from 2024-02-22</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2024-02-22 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                            <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                            <argument type="collection">
                                                <argument>AL</argument>
                                            </argument>
                                            <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_instant_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_100"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA INSTANT transfers for KOSOVO clients from 2022-04-18 -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">63b94fb8-f018-4482-9c9c-7dd11e7836ee</argument>
                        <argument type="string">Incoming SEPA INSTANT transfers for KOSOVO clients from 2022-04-18</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2022-04-18 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service">
                                                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                        <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                        <argument type="collection">
                                                            <argument>XK</argument>
                                                        </argument>
                                                        <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                    </service>
                                                </argument>
                                                <argument type="service">
                                                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\ClientResidenceCountryMatcher">
                                                        <argument type="collection">
                                                            <argument>XK</argument>
                                                        </argument>
                                                    </service>
                                                </argument>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_instant_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_300"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers to customers registered under partner Paysera GE (Non-SEPA country) legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">967e16eb-bb04-49b6-98cc-4522d33a74fb</argument>
                        <argument type="string">Incoming SEPA transfers to customers registered under partner Paysera GE (Non-SEPA country) legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\PartnerBeneficiaryMatcher">
                                            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
                                            <argument type="collection">
                                                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_GEORGIA</argument>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                    <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                    <argument>%evp_bank_transfer.route.matcher.sepa_countries%</argument>
                                                    <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                    <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                    <argument type="collection">
                                                        <argument>XK</argument>
                                                    </argument>
                                                    <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_0"/>
                    </service>
                </argument>
            </call>

            <!-- Incoming SEPA transfers to Non-SEPA country legal clients -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">933b100b-2c47-4d0e-9ccf-5fd9202ac2fc</argument>
                        <argument type="string">Incoming SEPA transfers to Non-SEPA country legal clients</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LegalBeneficiaryMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                    <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                    <argument>%evp_bank_transfer.route.matcher.sepa_countries%</argument>
                                                    <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransferInClientCountryMatcher">
                                                    <argument type="service" id="evp_client.user_information_provider_with_cache"/>
                                                    <argument type="collection">
                                                        <argument>XK</argument>
                                                    </argument>
                                                    <argument>Evp\Bundle\ClientBundle\Entity\ClientLegal</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service" id="evp_bank_transfer.commission_rule.eur_50"/>
                    </service>
                </argument>
            </call>

            <!-- Target2 Legal/Natural Kosovo/Albania/Non sepa/Sepa < 10000 EUR from 04-01 -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">53b5c7b0-4369-4651-83ea-a702fe5a3f50</argument>
                        <argument type="string">Target2 Legal/Natural Kosovo/Albania/Non sepa/Sepa less than 10000 EUR from 04-01</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2022-04-01 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\LessThanAmountMatcher">
                                            <argument type="service">
                                                <service class="Evp\Component\Money\Money">
                                                    <argument>10000</argument>
                                                    <argument>EUR</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryPartyIbanMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                                    <argument type="collection">
                                                        <!-- Returned target2 -->
                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransactionInUnresolvedReturnedTransferBankMatcher">
                                                                <argument type="service" id="paysera_target2.repository.transaction_in"/>
                                                                <argument type="service" id="paysera_target2.repository.transaction_in_unresolved"/>
                                                                <argument>lt_lb_target2</argument>
                                                            </service>
                                                        </argument>

                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryIbanMatcher">
                                                                <argument>********************</argument>
                                                            </service>
                                                        </argument>
                                                    </argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_rtgs_transaction_in_transfer_entry_matcher"/>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>3</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>

            <!-- Target2 Legal/Natural Kosovo/Albania/Non sepa/Sepa > 10000 EUR from 04-01 -->
            <call method="addMatchableCommission">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\MatchableCommission">
                        <argument type="string">0e8b51b2-7cdd-409c-8196-5e868d8088dd</argument>
                        <argument type="string">Target2 Legal/Natural Kosovo/Albania/Non sepa/Sepa > 10000 EUR from 04-01</argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
                                <argument type="collection">
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\FromDateMatcher">
                                            <argument type="service" id="evp.component.date.date_time_provider"/>
                                            <argument type="service">
                                                <service class="DateTimeImmutable">
                                                    <argument type="string">2022-04-01 00:00</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\CurrencyMatcher">
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\GreaterOrEqualAmountMatcher">
                                            <argument type="service">
                                                <service class="Evp\Component\Money\Money">
                                                    <argument>10000</argument>
                                                    <argument>EUR</argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryPartyIbanMatcher"/>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\NotMatcher">
                                            <argument type="service">
                                                <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                                    <argument type="collection">
                                                        <!-- Returned target2 -->
                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\TransactionInUnresolvedReturnedTransferBankMatcher">
                                                                <argument type="service" id="paysera_target2.repository.transaction_in"/>
                                                                <argument type="service" id="paysera_target2.repository.transaction_in_unresolved"/>
                                                                <argument>lt_lb_target2</argument>
                                                            </service>
                                                        </argument>

                                                        <argument type="service">
                                                            <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryIbanMatcher">
                                                                <argument>********************</argument>
                                                            </service>
                                                        </argument>
                                                    </argument>
                                                </service>
                                            </argument>
                                        </service>
                                    </argument>
                                    <argument type="service">
                                        <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                                            <argument type="collection">
                                                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_rtgs_transaction_in_transfer_entry_matcher"/>
                                            </argument>
                                        </service>
                                    </argument>
                                </argument>
                            </service>
                        </argument>
                        <argument type="service">
                            <service class="Evp\Bundle\BankCommissionBundle\Entity\CommissionRule">
                                <call method="setFix">
                                    <argument type="service">
                                        <service class="Evp\Component\Money\Money">
                                            <argument>4</argument>
                                            <argument>EUR</argument>
                                        </service>
                                    </argument>
                                </call>
                            </service>
                        </argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_bundle_bank_transfer.preconfigured_transfer_in_special_client_commission_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\PreconfiguredTransferInSpecialClientCommissionResolver">
            <argument type="collection">
                <argument type="service" key="714773">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.1</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1304711">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1195529">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.1</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1308050">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1341326">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="918394">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="918376">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1778150">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="5110560">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.15</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="1900929">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="5285898">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="5619273">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.35</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="5425929">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.03</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="5659635">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.10</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="8090427">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.5</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
                <argument type="service" key="8008335">
                    <service class="Evp\Component\Money\Money">
                        <argument>0.2</argument>
                        <argument>EUR</argument>
                    </service>
                </argument>
            </argument>
        </service>

        <service id="evp_bank_transfer.manageable_special_client_commission_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\ManageableSpecialClientCommissionResolver">
            <argument type="service" id="paysera_client_charge.client_commission_provider"/>
            <argument type="service" id="evp_bank_transfer.transfer_processor.payment_system_resolver"/>

        </service>

        <service id="evp_bank_transfer.transfer_in_event_publisher"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInEventPublisher">
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in_event"/>
        </service>

        <service id="evp_bank_transfer.transfer_in_event_resurrector"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInEventResurrector">
            <argument type="service" id="evp_bank_transfer.transfer_in_event_publisher"/>
        </service>

        <service id="evp_bank_transfer.deferred_statement_sender"
                 class="Evp\Bundle\BankTransferBundle\Service\DeferredStatementSender">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_sepa.rabbit_mq_plain_client"/>
        </service>

        <service id="evp_bank_transfer.transfer_in_event_locker"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInEventRedisLocker">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">transfer_in_event_locker</argument>
            <argument>10</argument>
        </service>

        <service id="evp_bank_transfer.transfer_in_event_storage"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInEventStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_in_event"/>
            <argument type="string">transfer_in_event_storage</argument>
        </service>

        <service id="evp_bank_transfer.transfer_event_storage"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferEventStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">transfer_event_storage</argument>
            <argument>600</argument>
        </service>

        <service id="evp_bank_transfer.processing_transaction_storage"
                 class="Evp\Bundle\BankTransferBundle\Service\ProcessingTransactionStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">evp_bank_transfer.processing_transaction</argument>
            <argument>60</argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_in_event_factory"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferInEventFactory">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.bank_transfer_data_factory"
                 class="Evp\Bundle\BankTransferBundle\Service\BankTransferDataFactory">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.transfer_request_to_bank_transfer_data_mapper"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRequestToBankTransferDataMapper">
            <argument type="service" id="evp_bank_transfer.bank_transfer_data_factory"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_bank_key_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferBankKeyResolver">
            <argument type="service" id="evp_bank_transfer.transfer_processor.payment_system_resolver"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.service.template_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\TemplateProvider">
            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_precious_metals_template_provider"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_internal_template_key_resolver"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_received_template_provider"/>
            <argument type="service" id="evp_bundle_accounting.service.external_out_bank_template_resolver"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">W2P_TRANSFER_%%bank%%_%%currency%%</argument>
            <argument type="string">TRANSFER_%%partner%%_%%currency%%</argument>
            <argument type="string">GATEWAY_RECEIVED_%%partner%%</argument>
            <argument type="string">GATEWAY_RECEIVED_%%bank%%_%%currency%%</argument>
            <argument type="string">RECEIVED_%%partner%%_%%currency%%</argument>
            <argument type="string">W2P_TRANSFER_OUT</argument>
            <argument type="string">W2P_TRANSFER_IN</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_IN</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_OUT</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_CHARGE_WALLET</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_CHARGE_MACRO</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_CARD</argument>
            <argument type="string">W2P_TRANSFER_COMMISS_CHARGE</argument>
            <argument type="string">W2P_TRANSFER_FUNDS_ON_WAY_IN</argument>
            <argument type="string">W2P_TRANSFER_FUNDS_ON_WAY_OUT</argument>
            <argument type="string">W2P_TRANSFER_USER_GOT_CASH_%%currency%%</argument>
            <argument type="string">W2P_TRANSFER_CASH_IN_TRANSFER</argument>
            <argument type="string">W2P_TRANSFER_CASH_OUT_TRANSFER</argument>
            <argument type="string">W2P_TRANSFER_USER_GAVE_CASH_%%currency%%</argument>
            <argument type="string">W2P_TRANSFER_EASY_PAY_BG_BGN</argument>
            <argument type="string">W2P_TRANSFER_CASHBACK</argument>
            <argument type="string">W2P_TRANSFER_CASHBACK_COSTS_%%partner%%_%%currency%%</argument>
            <argument type="string">REVENUE_SERVICE_DEBT_HOLD</argument>
            <argument type="string">REVENUE_SERVICE_DEBT_EXECUTION_RELEASE</argument>
            <argument type="string">TRANSFER_TO_TRANSIT_ACCOUNT</argument>
            <argument type="string">TRANSFER_FROM_TRANSIT_ACCOUNT</argument>
            <argument type="string">W2P_TRANSFER_IN_EVP_IN_ADVANCE</argument>
            <argument type="string">TRANSFER_IN_TRANSIT_ACCOUNT_TO_TRANSIT_ACCOUNT</argument>
            <argument type="string">TRANSFER_OUT_TRANSIT_ACCOUNT_TO_TRANSIT_ACCOUNT</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_OUT</argument>
            <argument type="string">TRANSFER_CASHDESK_COLLECTION_CASH_IN</argument>
            <argument type="string">TRANSFER_CASHBOX_TRANSIT_OUT</argument>
            <argument type="string">TRANSFER_CASHBOX_TRANSIT_IN</argument>
        </service>

        <service id="evp_bundle_bank_transfer.service.transfer_export_service_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferExportServiceResolver">
            <argument type="service" id="evp_transfer_provider.transfer_provider_resolver"/>
            <argument type="service" id="evp_transfer_provider.transfer_provider_factory"/>
            <argument type="service" id="service_container"/>
        </service>

        <service id="evp_bundle_bank_transfer.service.slave_connection_transfer_countries_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\SlaveConnectionTransferCountriesProvider">
            <argument type="service" id="doctrine.dbal.slave_gateway_connection_connection"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal"/>
        </service>

        <service id="evp_bundle_bank_transfer.service.iban_aliases_account_number_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\IbanAliasAccountNumberManager">
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="string">%evp_bank_account.account_number_prefix.local%</argument>

            <call method="addProvider">
                <argument type="service" id="evp_bundle_bank_transfer.service.iban_alias_account_number_partner_provider" />
            </call>
        </service>

        <service id="evp_bundle_bank_transfer.service.iban_alias_account_number_partner_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\IbanAliasAccountNumberPartnerProvider">
            <argument type="service" id="paysera_iban_alias.repository.iban_alias"/>
            <argument type="collection">
                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_LITHUANIA</argument>
                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_GEORGIA</argument>
            </argument>
            <argument type="collection">
                <argument type="string" key="GEL">%paysera_georgia_iban.iban_issuing_bank%</argument>
                <argument type="string" key="BGN">%paysera_postbank_bg_iban.iban_issuing_bank%</argument>
            </argument>
        </service>

        <service id="evp_bundle_bank_transfer.service.operation_date_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\OperationDateResolver"/>

        <service id="evp_bundle_bank_transfer.service.client_exporter.transfer_display_status_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\ClientExport\TransferDisplayStatusResolver">
            <argument type="service" id="evp_bank_transfer.mapper.transfer_to_common_transfer"/>
        </service>

        <service id="evp_bundle_bank_transfer.service.client_exporter.resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\ClientExport\ClientTransfersExporterResolver">
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>

            <call method="addExporter">
                <argument>xlsx</argument>
                <argument>paysera_ge</argument>
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\ClientExport\ClientTransfersXlsxGeExporter">
                        <argument type="service" id="evp_bank_transfer.transfer_status.translator"/>
                        <argument type="service" id="translator"/>
                        <argument type="string">%kernel.root_dir%/../src/Evp/Bundle/BankTransferBundle/Resources/assets/images/logo/logo-224x53-v2.jpg</argument>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_in_bank_key_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferIn\TransferInBankKeyResolver">
            <tag name="evp_bank_transfer.transfer_bank_key_resolver" priority="10"/>

            <call method="addMatchableBank">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\OrMatcher">
                        <argument type="collection">
                            <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
                            <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_unresolved_refund_entry_matcher"/>
                        </argument>
                    </service>
                </argument>
                <argument type="string">%evp_sepa.bank_key%</argument>
            </call>
            <call method="addMatchableBank">
                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_instant_transaction_in_transfer_entry_matcher"/>
                <argument type="string">%paysera_sepa_instant.sepa_instant_bank_key%</argument>
            </call>
            <call method="addMatchableBank">
                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_transaction_in_transfer_entry_matcher"/>
                <argument type="string">%paysera_target2.target2_bank_key%</argument>
            </call>
            <call method="addMatchableBank">
                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_target2_rtgs_transaction_in_transfer_entry_matcher"/>
                <argument type="string">%paysera_target2_rtgs.target2_bank_key%</argument>
            </call>
            <call method="addMatchableBank">
                <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_georgia_rtgs_transaction_in_transfer_entry_matcher"/>
                <argument type="string">%paysera_georgia_rtgs.georgia_rtgs_bank_key%</argument>
            </call>
        </service>

        <service id="evp_bank_transfer.operation_processor.sepa_third_paysera_account_transfer_in_bank_key_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferIn\TransferInPayseraSepa3BankKeyResolver">
            <tag name="evp_bank_transfer.transfer_bank_key_resolver" priority="1" />

            <argument type="service" id="evp_bank_transfer.transfer_processor.matcher.has_sepa_transaction_in_transfer_entry_matcher"/>
            <argument type="string">%evp_accounting.operation_processor.evp_main_account%</argument>
            <argument type="string">%evp_sepa.bank_key_transfers_from_third_paysera_account%</argument>
            <argument type="string">%evp_sepa.sepa3_account_shutdown_date%</argument>
            <argument type="string">%evp_sepa.sepa3_account_start_date%</argument>
        </service>

        <service id="evp_bundle_bank_transfer.service.transfer_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProvider">
            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
        </service>

        <service id="evp_bank_transfer.transfer_matcher.transfer_in.sanction_for_russia_whitelist"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\AndMatcher">
            <argument type="collection">
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\BeneficiaryIbanMatcher">
                        <argument type="string">********************</argument>
                    </service>
                </argument>
                <argument type="service">
                    <service class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferMatcher\PayerPartyAccountCountryMatcher">
                        <argument type="string">40702978820010004345</argument>
                        <argument type="string">RU</argument>
                        <argument type="string">IMBKRUMMXXX</argument>
                        <argument type="string">RZD JSCO INN7708503727</argument>
                    </service>
                </argument>
            </argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_by_legal_special_country_client_detector"
                 class="Evp\Bundle\BankTransferBundle\Service\SepaByLegalSpecialCountryClientDetector">
            <argument type="service" id="evp_client.service.legal_special_country_client_detector"/>
            <argument type="service" id="evp_sepa.sepa_transfer_detector"/>
            <argument type="service" id="paysera_sepa_instant.sepa_instant_transfer_detector"/>
        </service>

        <service id="evp_bank_transfer.service.force_bank_availability_by_client_feature_flag"
                 class="Evp\Bundle\BankTransferBundle\Service\ForceBankAvailabilityByClientFeatureFlag">
            <argument type="collection"></argument>
        </service>

        <service id="evp_bank_transfer.service.transfer_request_named_api_provider"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferRequestNamedApiProvider">
            <argument type="service" id="evp_bank_transfer.repository.transfer_request_named_api"/>
            <argument type="service" id="logger"/>
        </service>

        <service class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\OperationProcessorHelper"
                 id="evp_bank_transfer.operation_processor.helper">
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
            <argument>%evp_accounting.operation_processor.inbank_leasing_template_account_map%</argument>
        </service>

        <service id="evp_bank_transfer.service.client_device_manager"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferDeviceManager">

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_status.cancel_transfer_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.partner_isolation.strategy.transfer_internal"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\PartnerIsolation\TransferInternalPartnerIsolationStrategy">
            <argument type="service" id="evp_bundle_client.service.partners_isolation_manager"/>
            <argument type="service" id="evp_bank_transfer.account_resolver" />
        </service>

        <service id="evp_bank_transfer.partner_isolation.strategy.transfer_out"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\PartnerIsolation\TransferOutPartnerIsolationStrategy">
            <argument type="service" id="evp_bundle_client.service.partners_isolation_manager"/>
        </service>

        <service id="evp_bank_transfer.partner_isolation.strategy.transfer_in"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferProcessor\Step\PartnerIsolation\TransferInPartnerIsolationStrategy">
            <argument type="service" id="evp_bundle_client.service.partners_isolation_manager"/>
            <argument type="service" id="evp_bank_transfer.service.transfer_bank_key_resolver"/>
        </service>
        <service id="evp_bank_transfer.validator.transfer_internal_validator"
                 class="Evp\Bundle\BankTransferBundle\Validator\TransferInternalValidator">
            <argument type="service" id="evp_bundle_client.service.partners_isolation_manager"/>
            <argument type="service" id="evp_bank_transfer.partner_isolation.strategy.transfer_internal"/>
            <argument type="service" id="evp_bank_transfer.service.transfer_partners_isolation_feature_flag"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_partners_isolation_feature_flag"
                 class="Evp\Bundle\BankTransferBundle\Service\TransferPartnersIsolationFeatureFlag">
            <argument type="service" id="snc_redis.default"/>
            <argument>transfer_partner_isolation_feature_flag</argument>
        </service>

    </services>
</container>
