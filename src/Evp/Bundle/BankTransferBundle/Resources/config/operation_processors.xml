<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <parameters>
        <parameter key="evp_accounting.operation_processor.contis_partner_code">paysera_lt</parameter>
        <parameter key="evp_accounting.operation_processor.vmi_covenantee_id">4126</parameter>
        <parameter key="evp_accounting.operation_processor.vmi_bank_key">vmi</parameter>
        <parameter key="evp_accounting.operation_processor.cashback_account">EVP1110001030392</parameter>
        <parameter key="evp_accounting.operation_processor.partner_commission_account">EVP1110001061238</parameter>
        <parameter key="evp_accounting.operation_processor.partner_commission_without_invoice_account">EVP5110002440241</parameter>
        <parameter key="evp_accounting.operation_processor.cash_account">EVP4910001170426</parameter>
        <parameter key="evp_accounting.operation_processor.partner_charge_account">EVP8310001302142</parameter>
        <parameter key="evp_accounting.operation_processor.paysera_demo_account">EVP0610005612702</parameter>
        <parameter key="evp_accounting.operation_processor.evp_salary_account">EVP0710001560521</parameter>
        <parameter key="evp_accounting.operation_processor.evp_main_account">EVP0410001000364</parameter>
        <parameter key="evp_accounting.operation_processor.partner_account.mokipay">EVP9210001261833</parameter>
        <parameter key="evp_accounting.operation_processor.partner_account.sving">EVP9810001303604</parameter>
        <parameter key="evp_accounting.operation_processor.partner_account.mokilizingas">EVP4810007052997</parameter>
        <parameter key="evp_accounting.operation_processor.partner_account.mokilizingas_ee">EVP5810014933411</parameter>
        <parameter key="evp_accounting.operation_processor.partner_account.mokilizingas_lv">EVP2610014933409</parameter>
        <parameter key="evp_accounting.operation_processor.evp_sms">EVP6910001000362</parameter>
        <parameter key="evp_accounting.operation_processor.ltd_account">EVP8210001958838</parameter>
        <parameter key="evp_accounting.operation_processor.n_technologijos_account">EVP1810001000280</parameter>
        <parameter key="evp_accounting.operation_processor.atm.accounts" type="collection">
            <parameter key="0">EVP2610017891909</parameter>
            <parameter key="2">EVP1810018331173</parameter>
            <parameter key="3">EVP6610018331176</parameter>
            <parameter key="4">EVP3310018331180</parameter>
            <parameter key="5">EVP4810018331187</parameter>
        </parameter>

        <parameter key="evp_accounting.operation_processor.terminals.accounts" type="collection">
            <parameter>EVP4210002034447</parameter>
            <parameter>EVP5010002067282</parameter>
            <parameter>EVP4910002067288</parameter>
            <parameter>EVP9710002067291</parameter>
            <parameter>EVP4810002067294</parameter>
            <parameter>EVP9610002067297</parameter>
            <parameter>EVP4710002067300</parameter>
            <parameter>EVP9410002244237</parameter>
            <parameter>EVP0610002244765</parameter>
            <parameter>EVP5410002244768</parameter>
            <parameter>EVP0510002244771</parameter>
        </parameter>
        <parameter key="evp_accounting.operation_processor.terminals.template_for_collection">GATEWAY_TERMINAL_COLLECT</parameter>
        <parameter key="evp_accounting.operation_processor.terminals.template_for_cash_operations">GATEWAY_TERMINAL_RECEIVED</parameter>
        <parameter key="evp_accounting.operation_processor.ignored_owner_accounts" type="collection">
            <parameter>EVP0410001000364</parameter>
            <parameter>EVP0510002244771</parameter>
            <parameter>EVP0610002244765</parameter>
            <parameter>EVP0710001560521</parameter>
            <parameter>EVP1110001030392</parameter>
            <parameter>EVP1110001061238</parameter>
            <parameter>EVP1210001745567</parameter>
            <parameter>EVP1210003119572</parameter>
            <parameter>EVP1310003547045</parameter>
            <parameter>EVP1510002952811</parameter>
            <parameter>EVP1810003022924</parameter>
            <parameter>EVP1910002275533</parameter>
            <parameter>EVP2110002980711</parameter>
            <parameter>EVP3810002283858</parameter>
            <parameter>EVP4110002550687</parameter>
            <parameter>EVP4210002034447</parameter>
            <parameter>EVP4710002067300</parameter>
            <parameter>EVP4810002067294</parameter>
            <parameter>EVP4910001170426</parameter>
            <parameter>EVP4910001995702</parameter>
            <parameter>EVP4910002067288</parameter>
            <parameter>EVP5010002067282</parameter>
            <parameter>EVP5010002474585</parameter>
            <parameter>EVP5110002440241</parameter>
            <parameter>EVP5410002244768</parameter>
            <parameter>EVP5810002963029</parameter>
            <parameter>EVP6510002485262</parameter>
            <parameter>EVP6910001000362</parameter>
            <parameter>EVP7010002980708</parameter>
            <parameter>EVP7310002550689</parameter>
            <parameter>EVP7410002550683</parameter>
            <parameter>EVP7910001252987</parameter>
            <parameter>EVP8010001000102</parameter>
            <parameter>EVP8010001071106</parameter>
            <parameter>EVP8310001302142</parameter>
            <parameter>EVP8710001771695</parameter>
            <parameter>EVP9410002244237</parameter>
            <parameter>EVP9610002067297</parameter>
            <parameter>EVP9710002067291</parameter>
            <parameter>EVP0210003873031</parameter>
            <parameter>EVP4110004095994</parameter>
            <parameter>EVP9610006424149</parameter>
            <parameter>EVP6510006424141</parameter>
            <parameter>EVP0510006015161</parameter>
            <parameter>EVP2610007455485</parameter>
            <parameter>EVP5310007455517</parameter>
            <parameter>EVP5110007455529</parameter>
            <parameter>EVP5710007455493</parameter>
            <parameter>EVP7810007455561</parameter>
            <parameter>EVP3410008839433</parameter>
            <parameter>EVP3610008861925</parameter>
            <parameter>EVP8810009025349</parameter>
            <parameter>EVP3710009025364</parameter>
            <parameter>EVP8510009025367</parameter>
            <parameter>EVP3610009025370</parameter>
            <parameter>EVP3410009025382</parameter>
            <parameter>EVP8210009025385</parameter>
            <parameter>EVP3310009025388</parameter>
            <parameter>EVP8110009025391</parameter>
            <parameter>EVP8010009025397</parameter>
            <parameter>EVP7710009025415</parameter>
            <parameter>EVP2010006435469</parameter>
            <parameter>EVP7410009165986</parameter>
            <parameter>EVP2110009166013</parameter>
            <parameter>EVP2410009165995</parameter>
            <parameter>EVP2210009166007</parameter>
            <parameter>EVP5910009806276</parameter>
            <parameter>EVP3110010380005</parameter>
            <parameter>EVP7910010380008</parameter>
            <parameter>EVP7510010380032</parameter>
            <parameter>EVP2610010380035</parameter>
            <parameter>EVP2410010380047</parameter>
            <parameter>EVP7410010542416</parameter>
            <parameter>EVP7110010380056</parameter>
            <parameter>EVP7210010380050</parameter>
            <parameter>EVP2310010380053</parameter>
            <parameter>EVP7410010380038</parameter>
            <parameter>EVP9110010943215</parameter>
            <parameter>EVP2410010943326</parameter>
            <parameter>EVP2310010943332</parameter>
            <parameter>EVP6810010943353</parameter>
            <parameter>EVP7210011135971</parameter>
            <parameter>EVP8610010944118</parameter>
            <parameter>EVP3510010944133</parameter>
            <parameter>EVP8110010944148</parameter>
            <parameter>EVP3010010944163</parameter>
            <parameter>EVP2810010944175</parameter>
            <parameter>EVP7510010944184</parameter>
            <parameter>EVP7210010944202</parameter>
            <parameter>EVP2010010944223</parameter>
            <parameter>EVP6610010944238</parameter>
            <parameter>EVP6210010944262</parameter>
            <parameter>EVP6110010944268</parameter>
            <parameter>EVP5910010944280</parameter>
            <parameter>EVP1010010944283</parameter>
            <parameter>EVP0510010944313</parameter>
            <parameter>EVP5310010944316</parameter>
            <parameter>EVP5010010944334</parameter>
            <parameter>EVP4910010944340</parameter>
            <parameter>EVP9310010944367</parameter>
            <parameter>EVP6910010380068</parameter>
            <parameter>EVP2010010380071</parameter>
            <parameter>EVP6810010380074</parameter>
            <parameter>EVP1910010380077</parameter>
            <parameter>EVP1610010380095</parameter>
            <parameter>EVP1510010380101</parameter>
            <parameter>EVP6310010380104</parameter>
            <parameter>EVP1410010380107</parameter>
            <parameter>EVP6210010380110</parameter>
            <parameter>EVP1310010380113</parameter>
            <parameter>EVP1010010380131</parameter>
            <parameter>EVP5810010380134</parameter>
            <parameter>EVP0910010380137</parameter>
            <parameter>EVP5710010380140</parameter>
            <parameter>EVP5310010380164</parameter>
            <parameter>EVP5210010380170</parameter>
            <parameter>EVP4810010380194</parameter>
            <parameter>EVP2110011147917</parameter>
            <parameter>EVP0810011147995</parameter>
            <parameter>EVP1010011507077</parameter>
            <parameter>EVP4910011507134</parameter>
            <parameter>EVP4310011507170</parameter>
            <parameter>EVP9510011677287</parameter>
            <parameter>EVP9210011677305</parameter>
            <parameter>EVP8810011677329</parameter>
            <parameter>EVP2810011856557</parameter>
            <parameter>EVP5910011856565</parameter>
            <parameter>EVP8610011856597</parameter>
            <parameter>EVP8410011856609</parameter>
            <parameter>EVP1610011856629</parameter>
            <parameter>EVP4710011856637</parameter>
            <parameter>EVP1210011856653</parameter>
            <parameter>EVP6610011856717</parameter>
            <parameter>EVP2910011856745</parameter>
            <parameter>EVP9310011856749</parameter>
            <parameter>EVP6010011856753</parameter>
            <parameter>EVP4810011856825</parameter>
            <parameter>EVP4610011856837</parameter>
            <parameter>EVP4410011856849</parameter>
            <parameter>EVP4210011856861</parameter>
            <parameter>EVP0510011856889</parameter>
            <parameter>EVP1210011857041</parameter>
            <parameter>EVP7410011857057</parameter>
            <parameter>EVP6910011856893</parameter>
            <parameter>EVP3610011200498</parameter>
            <parameter>EVP7210014521853</parameter>
        </parameter>

        <parameter key="evp_accounting.operation_processor.paysera_macro_accounts" type="collection">
            <parameter>EVP8710001771695</parameter>
            <parameter>EVP4910001995702</parameter>
            <parameter>EVP0210009120149</parameter>
            <!-- Paysera account for nordea lt manual macro incomes -->
            <parameter>EVP1510002952811</parameter>
            <!-- Paysera account for seb lv manual macro incomes -->
            <parameter>EVP5810002963029</parameter>
            <parameter>EVP6510006424141</parameter>
            <!-- Paysera account for swed lv manual macro incomes -->
            <parameter>EVP1810003022924</parameter>
            <parameter>EVP9610006424149</parameter>
            <!-- Paysera account for nordea lv manual macro incomes -->
            <parameter>EVP7010002980708</parameter>
            <!-- Paysera account for nordea ee manual macro incomes -->
            <parameter>EVP2110002980711</parameter>
            <!--Verso integration account -->
            <parameter>EVP1210003119572</parameter>
            <!--Paysera account for lhv ee manual macro incomes -->
            <parameter>EVP0210003873031</parameter>
            <!--Paysera account for danske lt macro incomes -->
            <parameter>EVP4110004095994</parameter>
            <!-- Paysera account for seb_ee macro incomes -->
            <parameter>EVP0510006015161</parameter>
            <!-- Paysera account for krediidi_ee macro incomes -->
            <parameter>EVP2610007455485</parameter>
            <!-- Paysera account for citadele_lt macro incomes -->
            <parameter>EVP5310007455517</parameter>
            <!-- Paysera account for citadele_lv macro incomes -->
            <parameter>EVP5110007455529</parameter>
            <!-- Paysera account for citadele_ee macro incomes -->
            <parameter>EVP5710007455493</parameter>
            <!-- Paysera account for dnb_lv macro incomes -->
            <parameter>EVP7810007455561</parameter>
            <!-- Paysera account for ro_bcr macro incomes -->
            <parameter>EVP3410008839433</parameter>
            <!-- Paysera account for at_ing bank EUR macro incomes -->
            <parameter>EVP3610008861925</parameter>
            <!-- Paysera account for be_ing bank EUR macro incomes -->
            <parameter>EVP8810009025349</parameter>
            <!-- Paysera account for de_ing bank EUR macro incomes -->
            <parameter>EVP3710009025364</parameter>
            <!-- Paysera account for fr_ing bank EUR macro incomes -->
            <parameter>EVP8510009025367</parameter>
            <!-- Paysera account for ie_ing bank EUR macro incomes -->
            <parameter>EVP3610009025370</parameter>
            <!-- Paysera account for it_ing bank EUR macro incomes -->
            <parameter>EVP3410009025382</parameter>
            <!-- Paysera account for lu_ing bank EUR macro incomes -->
            <parameter>EVP8210009025385</parameter>
            <!-- Paysera account for nl_ing bank EUR macro incomes -->
            <parameter>EVP3310009025388</parameter>
            <!-- Paysera account for sk_ing bank EUR macro incomes -->
            <parameter>EVP8110009025391</parameter>
            <!-- Paysera account for es_ing bank EUR macro incomes -->
            <parameter>EVP8010009025397</parameter>
            <!-- Paysera account for pt_ing bank EUR macro incomes -->
            <parameter>EVP7710009025415</parameter>
            <!-- Paysera account for giro_pay bank macro incomes -->
            <parameter>EVP2010006435469</parameter>
            <!-- Paysera account for si_unicredit bank EUR macro incomes -->
            <parameter>EVP7410009165986</parameter>
            <!-- Paysera account for de_unicredit bank EUR macro incomes -->
            <parameter>EVP2110009166013</parameter>
            <!-- Paysera account for at_unicredit bank EUR macro incomes -->
            <parameter>EVP2410009165995</parameter>
            <!-- Paysera account for it_spa_unicredit bank EUR macro incomes -->
            <parameter>EVP2210009166007</parameter>
            <!-- Paysera account for it_bb_unicredit bank EUR macro incomes -->
            <parameter>EVP5910009806276</parameter>
            <!-- Paysera account for es_caixabank bank EUR macro incomes -->
            <parameter>EVP3110010380005</parameter>
            <!-- Paysera account for es_bancosantander EUR macro incomes -->
            <parameter>EVP7910010380008</parameter>
            <!-- Paysera account for es_bancodesabadell EUR macro incomes -->
            <parameter>EVP7510010380032</parameter>
            <!-- Paysera account for es_bankia EUR macro incomes -->
            <parameter>EVP2610010380035</parameter>
            <!-- Paysera account for es_bankinter EUR macro incomes -->
            <parameter>EVP2410010380047</parameter>
            <!-- Paysera account for es_bbva EUR macro incomes -->
            <parameter>EVP7410010542416</parameter>
            <!-- Paysera account for es_cajarural EUR macro incomes -->
            <parameter>EVP7110010380056</parameter>
            <!-- Paysera account for es_ibercaja EUR macro incomes -->
            <parameter>EVP7210010380050</parameter>
            <!-- Paysera account for es_kutxabank EUR macro incomes -->
            <parameter>EVP2310010380053</parameter>
            <!-- Paysera account for es_unicajabanco EUR macro incomes -->
            <parameter>EVP7410010380038</parameter>
            <!-- Paysera account for es_bankoa EUR macro incomes -->
            <parameter>EVP6910010380068</parameter>
            <!-- Paysera account for es_grupocajamar EUR macro incomes -->
            <parameter>EVP2010010380071</parameter>
            <!-- Paysera account for es_cajaarquitectos EUR macro incomes -->
            <parameter>EVP6810010380074</parameter>
            <!-- Paysera account for es_colonya EUR macro incomes -->
            <parameter>EVP1910010380077</parameter>
            <!-- Paysera account for es_eurocajarural EUR macro incomes -->
            <parameter>EVP1610010380095</parameter>
            <!-- Paysera account for es_evobanco EUR macro incomes -->
            <parameter>EVP1510010380101</parameter>
            <!-- Paysera account for es_fiarebancaetica EUR macro incomes -->
            <parameter>EVP6310010380104</parameter>
            <!-- Paysera account for es_bancopichincha EUR macro incomes -->
            <parameter>EVP1410010380107</parameter>
            <!-- Paysera account for es_cajasur EUR macro incomes -->
            <parameter>EVP6210010380110</parameter>
            <!-- Paysera account for es_renta4banco EUR macro incomes -->
            <parameter>EVP1310010380113</parameter>
            <!-- Paysera account for es_laboralkutxa EUR macro incomes -->
            <parameter>EVP1010010380131</parameter>
            <!-- Paysera account for es_bancomediolanum EUR macro incomes -->
            <parameter>EVP5810010380134</parameter>
            <!-- Paysera account for es_openbank EUR macro incomes -->
            <parameter>EVP0910010380137</parameter>
            <!-- Paysera account for es_selfbank EUR macro incomes -->
            <parameter>EVP5710010380140</parameter>
            <!-- Paysera account for es_inversis EUR macro incomes -->
            <parameter>EVP5310010380164</parameter>
            <!-- Paysera account for es_andbank EUR macro incomes -->
            <parameter>EVP5210010380170</parameter>
            <!-- Paysera account for es_wizink EUR macro incomes -->
            <parameter>EVP4810010380194</parameter>
            <!-- Paysera account for fi_nordea EUR macro incomes -->
            <parameter>EVP9110010943215</parameter>
            <!-- Paysera account for nl_abn_amro EUR macro incomes -->
            <parameter>EVP2410010943326</parameter>
            <!-- Paysera account for be_abn_amro EUR macro incomes -->
            <parameter>EVP2310010943332</parameter>
            <!-- Paysera account for de_abn_amro EUR macro incomes -->
            <parameter>EVP6810010943353</parameter>
            <!-- Paysera account for si_intesa_sanpaolo bank EUR macro incomes -->
            <parameter>EVP7210011135971</parameter>
            <!-- Paysera account for at_revolut bank EUR macro incomes -->
            <parameter>EVP8610010944118</parameter>
            <!-- Paysera account for be_revolut bank EUR macro incomes -->
            <parameter>EVP3510010944133</parameter>
            <!-- Paysera account for ee_revolut bank EUR macro incomes -->
            <parameter>EVP8110010944148</parameter>
            <!-- Paysera account for fi_revolut bank EUR macro incomes -->
            <parameter>EVP3010010944163</parameter>
            <!-- Paysera account for fr_revolut bank EUR macro incomes -->
            <parameter>EVP2810010944175</parameter>
            <!-- Paysera account for de_revolut bank EUR macro incomes -->
            <parameter>EVP7510010944184</parameter>
            <!-- Paysera account for gr_revolut bank EUR macro incomes -->
            <parameter>EVP7210010944202</parameter>
            <!-- Paysera account for ie_revolut bank EUR macro incomes -->
            <parameter>EVP2010010944223</parameter>
            <!-- Paysera account for ie_revolut bank EUR macro incomes -->
            <parameter>EVP6610010944238</parameter>
            <!-- Paysera account for lv_revolut bank EUR macro incomes -->
            <parameter>EVP6210010944262</parameter>
            <!-- Paysera account for lt_revolut bank EUR macro incomes -->
            <parameter>EVP6110010944268</parameter>
            <!-- Paysera account for lu_revolut bank EUR macro incomes -->
            <parameter>EVP5910010944280</parameter>
            <!-- Paysera account for mt_revolut bank EUR macro incomes -->
            <parameter>EVP1010010944283</parameter>
            <!-- Paysera account for nl_revolut bank EUR macro incomes -->
            <parameter>EVP0510010944313</parameter>
            <!-- Paysera account for pt_revolut bank EUR macro incomes -->
            <parameter>EVP5310010944316</parameter>
            <!-- Paysera account for sk_revolut bank EUR macro incomes -->
            <parameter>EVP5010010944334</parameter>
            <!-- Paysera account for si_revolut bank EUR macro incomes -->
            <parameter>EVP4910010944340</parameter>
            <!-- Paysera account for es_revolut bank EUR macro incomes -->
            <parameter>EVP9310010944367</parameter>
            <!-- Paysera account for at_erste_group EUR macro incomes -->
            <parameter>EVP2110011147917</parameter>
            <!-- Paysera account for nl_rabobank EUR macro incomes -->
            <parameter>EVP0810011147995</parameter>
            <!-- Paysera account for fi_handelsbanken EUR macro incomes -->
            <parameter>EVP1010011507077</parameter>
            <!-- Paysera account for nl_triodos EUR macro incomes -->
            <parameter>EVP4910011507134</parameter>
            <!-- Paysera account for be_triodos EUR macro incomes -->
            <parameter>EVP4310011507170</parameter>
            <!-- Paysera account for nl_asnbank EUR macro incomes -->
            <parameter>EVP9510011677287</parameter>
            <!-- Paysera account for nl_regiobank EUR macro incomes -->
            <parameter>EVP9210011677305</parameter>
            <!-- Paysera account for nl_snsbank EUR macro incomes -->
            <parameter>EVP8810011677329</parameter>
            <!-- Paysera account for de_n26 EUR macro incomes -->
            <parameter>EVP2810011856557</parameter>
            <!-- Paysera account for at_n26 EUR macro incomes -->
            <parameter>EVP5910011856565</parameter>
            <!-- Paysera account for ie_n26 EUR macro incomes -->
            <parameter>EVP8610011856597</parameter>
            <!-- Paysera account for fr_n26 EUR macro incomes -->
            <parameter>EVP8410011856609</parameter>
            <!-- Paysera account for es_n26 EUR macro incomes -->
            <parameter>EVP1610011856629</parameter>
            <!-- Paysera account for it_n26 EUR macro incomes -->
            <parameter>EVP4710011856637</parameter>
            <!-- Paysera account for nl_n26 EUR macro incomes -->
            <parameter>EVP1210011856653</parameter>
            <!-- Paysera account for be_n26 EUR macro incomes -->
            <parameter>EVP6610011856717</parameter>
            <!-- Paysera account for pt_n26 EUR macro incomes -->
            <parameter>EVP2910011856745</parameter>
            <!-- Paysera account for fi_n26 EUR macro incomes -->
            <parameter>EVP9310011856749</parameter>
            <!-- Paysera account for lu_n26 EUR macro incomes -->
            <parameter>EVP6010011856753</parameter>
            <!-- Paysera account for si_n26 EUR macro incomes -->
            <parameter>EVP4810011856825</parameter>
            <!-- Paysera account for ee_n26 EUR macro incomes -->
            <parameter>EVP4610011856837</parameter>
            <!-- Paysera account for gr_n26 EUR macro incomes -->
            <parameter>EVP4410011856849</parameter>
            <!-- Paysera account for sk_n26 EUR macro incomes -->
            <parameter>EVP4210011856861</parameter>
            <!-- Paysera account for mt_n26 EUR macro incomes -->
            <parameter>EVP0510011856889</parameter>
            <!-- Paysera account for lt_n26 EUR macro incomes -->
            <parameter>EVP1210011857041</parameter>
            <!-- Paysera account for lv_n26 EUR macro incomes -->
            <parameter>EVP7410011857057</parameter>
            <!-- Paysera account for cy_n26 EUR macro incomes -->
            <parameter>EVP6910011856893</parameter>
            <!-- Paysera account for ee_swedbank(hanzaee) bank EUR macro incomes -->
            <parameter>EVP3610011200498</parameter>
            <!-- Paysera account for lt_swedbank(hanza) bank EUR macro incomes -->
            <parameter>EVP7210014521853</parameter>
        </parameter>
        <parameter key="evp_accounting.operation_processor.inbank_leasing_template_account_map" type="collection">
            <parameter type="collection">
                <parameter key="account">%evp_accounting.operation_processor.partner_account.mokipay%</parameter>
                <parameter key="template">MOKIPAY_SUMOKEJIMAS</parameter>
            </parameter>
            <parameter type="collection">
                <parameter key="account">%evp_accounting.operation_processor.partner_account.sving%</parameter>
                <parameter key="template">SVING_SUMOKEJIMAS</parameter>
            </parameter>
            <parameter type="collection">
                <parameter key="account">%evp_accounting.operation_processor.partner_account.mokilizingas%</parameter>
                <parameter key="template">INBANK_LT_SUMOKEJIMAS</parameter>
            </parameter>
            <parameter type="collection">
                <parameter key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_ee%</parameter>
                <parameter key="template">TRANSFER_FROM_INBANK_EE_EUR</parameter>
            </parameter>
            <parameter type="collection">
                <parameter key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_lv%</parameter>
                <parameter key="template">TRANSFER_FROM_INBANK_LV_EUR</parameter>
            </parameter>
        </parameter>
    </parameters>

    <services>
        <service id="evp_bank_transfer.operation_processor.transfer_from_operation"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferFromOperationProvider">
        </service>

        <service id="evp_bank_transfer.operation_processor.bank_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\AccountingBankResolver">
            <call method="addResolver">
                <argument type="service" id="evp_bundle_bank_transfer.service_operation_processor.sepa_instant_refund_resolver"/>
            </call>
            <call method="addResolver">
                <argument type="service" id="evp_bank_transfer.operation_processor.sepa_instant_resolver"/>
            </call>
            <call method="addResolver">
                <argument type="service" id="evp_bank_transfer.operation_processor.sepa_resolver"/>
            </call>
        </service>

        <service id="evp_bundle_bank_transfer.service_operation_processor_transfer_in.transfer_in_accounting_bank_resolver"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferIn\TransferInAccountingBankResolver">
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.internal_operation_handler" />

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_accounting.accounting_group_key_resolver"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_precious_metals_template_provider"/>
            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_accounting.service.cash_out_precious_metals_template_provider"/>
            <argument type="service" id="evp_accounting.service.atm_template_resolver"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_covenantee_id_resolver"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument>%evp_accounting.operation_processor.owner_covenantee_id%</argument>
            <argument>W2P_TRANSFER_OUT</argument>
            <argument>W2P_TRANSFER_OUT_EVP</argument>
            <argument>W2P_TRANSFER_COMMISS_OUT</argument>
            <argument>W2P_TRANSFER_IN</argument>
            <argument>W2P_TRANSFER_IN_EVP</argument>
            <argument>W2P_TRANSFER_COMMISS_IN</argument>
            <argument>W2P_TRANSFER_COMMISS_CHARGE_MACRO</argument>
            <argument>W2P_TRANSFER_COMMISS_CHARGE_WALLET</argument>
            <argument>%evp_accounting.operation_processor.evp_sms%</argument>
            <argument>W2P_TRANSFER_OUT_EVP_SMS</argument>
            <argument>W2P_TRANSFER_IN_EVP_SMS</argument>
            <argument>%evp_accounting.operation_processor.cashback_account%</argument>
            <argument>W2P_TRANSFER_CASHBACK</argument>
            <argument>W2P_TRANSFER_CASHBACK_EVP</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_account%</argument>
            <argument>W2P_TRANSFER_PARTNER_COMMISSION</argument>
            <argument>%evp_accounting.operation_processor.partner_commission_without_invoice_account%</argument>
            <argument>PARTNER_TRANSFER_COMMISSION_NO_INVOICE</argument>
            <argument>%evp_accounting.operation_processor.cash_account%</argument>
            <argument>W2P_TRANSFER_USER_GAVE_CASH_%%currency%%</argument>
            <argument>W2P_TRANSFER_CASH_IN_TRANSFER</argument>
            <argument>W2P_TRANSFER_CASH_IN_TRANSFER_OURS</argument>
            <argument>%evp_accounting.operation_processor.evp_main_account%</argument>
            <argument>W2P_TRANSFER_USER_GOT_CASH_%%currency%%</argument>
            <argument>W2P_TRANSFER_CASH_OUT_TRANSFER</argument>
            <argument>%evp_accounting.operation_processor.partner_charge_account%</argument>
            <argument>%evp_accounting.operation_processor.paysera_demo_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_salary_account%</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="template">MOKIPAY_SUMOKEJIMAS</argument>
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokipay%</argument>
                </argument>
                <argument type="collection">
                    <argument key="template">SVING_SUMOKEJIMAS</argument>
                    <argument key="account">%evp_accounting.operation_processor.partner_account.sving%</argument>
                </argument>
                <argument type="collection">
                    <argument key="template">MOKILIZINGAS_SUMOKEJIMAS</argument>
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas%</argument>
                </argument>
            </argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_ee%</argument>
                    <argument key="template_to">TRANSFER_TO_INBANK_EE_EUR</argument>
                    <argument key="template_from">TRANSFER_FROM_INBANK_EE_EUR</argument>
                </argument>
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_lv%</argument>
                    <argument key="template_to">TRANSFER_TO_INBANK_LV_EUR</argument>
                    <argument key="template_from">TRANSFER_FROM_INBANK_LV_EUR</argument>
                </argument>
            </argument>
            <argument>%evp_accounting.operation_processor.terminals.accounts%</argument>
            <argument>%evp_accounting.operation_processor.terminals.template_for_collection%</argument>
            <argument>%evp_accounting.operation_processor.terminals.template_for_cash_operations%</argument>
            <argument>BETWEEN_ACCOUNTS_OUT</argument>
            <argument>BETWEEN_ACCOUNTS_IN</argument>
            <argument>W2P_CHECKOUT_TO_OUR_ACCOUNT</argument>
            <argument>%evp_accounting.operation_processor.ignored_owner_accounts%</argument>
            <argument type="string">~^Payment for delivery order ~</argument>
            <argument type="string">W2P_TRANSFER_IN_EVP_DELIVERY</argument>

            <call method="setAccountingClient">
                <argument type="service" id="evp_accounting.operation_processor.accounting_client"/>
            </call>
            <call method="setBuilder">
                <argument type="service" id="evp_accounting.accounting_operation_builder" />
            </call>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.operation_processor.operation_date_service"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="paysera_partner.bank_partner_provider"/>
            <argument type="service" id="paysera_partner.service.checkout_transfer_partner_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_details"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.helper.contis_type_at_date_resolver"/>
            <argument type="service" id="evp_bundle_bank_transfer.service_operation_processor_transfer_in.transfer_in_accounting_bank_resolver"/>
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferInOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transfer_from_operation"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_in_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferInPartnerOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transfer_from_operation"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_in_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferInToContisOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_out_from_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutFromContisOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_out_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutPartnerOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_out_georgia_revenue_service_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutGeorgiaRevenueServiceOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
            <argument>%paysera_georgia_rtgs.georgia_rtgs_bank_key%</argument>
        </service>

        <service id="evp_bank_transfer.operation_creator.transit_account_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransitAccountOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_creator.transit_account_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransitAccountPartnerOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_creator.transfer_in_quipu_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferInQuipuOperationCreator">

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.transfer_bank_key_resolver"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transfer_from_operation"/>
            <argument type="string">quipu</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_kiosk_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutKioskPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_agent_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutAgentPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_agent_contis_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutAgentContisOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_agent_contis_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutAgentContisPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_kiosk_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutKioskContisOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_out_kiosk_contis_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashOutKioskContisPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transit_account_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\TransitAccountOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="evp_contis_accounting.service.partner_accounting_data_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_agent_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInAgentOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_kiosk_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInKioskOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_agent_contis_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInAgentContisOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInKioskContisOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_agent_contis_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInAgentContisPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashInKioskContisPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_accounting.service.atm_partner_template_resolver"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.cash_box_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CashBoxOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.commission_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Creator\InternalOperation\CommissionOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\InternalPartnerOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_partner_operation_handler" />

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_contis_accounting.accounting_processor"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_details"/>
            <argument type="service" id="evp_bank_transfer.helper.contis_type_at_date_resolver"/>
            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_accounting.service.cash_out_precious_metals_template_provider"/>
            <argument type="service" id="paysera_partner.service.resolver.sub_type"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transit_account_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.commission_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_to_contis_between_own_accounts_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInToContisBetweenOwnAccountsOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="5"/>

            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_from_contis_between_own_accounts_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInFromContisBetweenOwnAccountsOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="6"/>

            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_contis_cash_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashIn\TransferInContisCashInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="7"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_contis_cash_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashOut\TransferInContisCashOutOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="8"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument>cash_out</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_from_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInFromContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="9"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_cash_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashIn\TransferInCashInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="10"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_operation_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_from_contis_to_main_account_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInFromContisToMainAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="11"/>

            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.cashback.transfer_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\Cashback\TransferInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="4"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.cashback.transfer_in_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\Cashback\TransferInPartnerOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="3"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_cash_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashOut\TransferInCashOutOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="20"/>

            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_bundle_accounting.service.cash_out_precious_metals_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_box_operation_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_contis_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInContisToContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="25"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInPartnerOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="30"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="40"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_to_contis_between_own_accounts_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutToContisBetweenOwnAccountsOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="5"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_from_contis_between_own_accounts_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutFromContisBetweenOwnAccountsOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="6"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_contis_cash_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashIn\TransferOutContisCashInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="7"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_contis_cash_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashOut\TransferOutContisCashOutOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="8"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_contis_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_contis_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument>cash_out</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_from_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutFromContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="9"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_cash_in_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashIn\TransferOutCashInOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="10"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_agent_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_in_kiosk_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_box_operation_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_from_contis_to_main_account_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutFromContisToMainAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="11"/>

            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_cash_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\CashOut\TransferOutCashOutOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="20"/>

            <argument type="service" id="evp_currency.currency_type_helper"/>
            <argument type="service" id="evp_bundle_accounting.service.cash_out_precious_metals_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_kiosk_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.cash_out_agent_partner_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.beneficiary_partner_evaluation_service"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_contis_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutContisToContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="25"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_contis_partner_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutContisToPartnerContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="27"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_contis_partner_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInContisToPartnerContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="29"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutPartnerOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="30"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="40"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_ltd_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutLtdOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="39"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument>%evp_accounting.operation_processor.ltd_account%</argument>
            <argument>%evp_accounting.operation_processor.evp_main_account%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_default_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutDefaultOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="50"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_operation_handler"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Handler\ExternalOperationHandler">
            <tag name="monolog.logger" channel="evp_bank_transfer.operation_processor.external_operation_handler" />

            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_details"/>
            <argument type="service" id="evp_bundle_accounting.service.vmi_template_resolver"/>
            <argument>W2P_RECEIVED</argument>
            <argument>GATEWAY_RECEIVED_MICRO</argument>
            <argument>W2P_TRANSFER_%%bank%%_%%currency%%</argument>
            <argument>W2P_TRANSFER_BANK_OUT</argument>
            <argument>W2P_TRANSFER_TO_VPO_%%s</argument>
            <argument>EMA_ADDPAYMENT_LV_LPB_%%s</argument>
            <argument>CONTIS_TOP_UP_TO_INTERMEDIARY</argument>
            <argument>CONTIS_TOP_UP_TO_INTERMEDIARY_2</argument>
            <argument>GATEWAY_RECEIVED_LB_%%s</argument>
            <argument>GATEWAY_RECEIVED_LT_LB_SEPA_INST_%%s</argument>
            <argument>GATEWAY_RECEIVED_LT_LB_TARGET2_%%s</argument>
            <argument>EMA_ADDPAYMENT_VERSOBANK_%%s</argument>
            <argument>GATEWAY_RECEIVED_%%bank%%_%%currency%%</argument>
            <argument type="collection">
                <argument>%evp_contis.contis_beneficiary_iban_gb%</argument>
                <argument>%evp_contis.contis_beneficiary_iban_lt%</argument>
            </argument>
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector"/>
            <argument>%evp_transfer_tax.vpo.covenanteeId%</argument>
            <argument type="service" id="evp_card.repository.deposit"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="evp_accounting.accounting_group_key_resolver"/>
            <argument type="service" id="evp_accounting.accounting_group_key_manager"/>
            <argument>%evp_accounting.operation_processor.paysera_macro_accounts%</argument>
            <argument type="service" id="evp_accounting.operation_processor.accounting_client"/>
            <argument type="service" id="evp_accounting.accounting_operation_builder"/>
            <argument type="service" id="evp_transfer_provider.processor.transfer_field"/>
            <argument type="service" id="evp_accounting.client.accounting_client"/>
            <argument type="collection">
                <argument type="string">%evp_sepa.bank_key%</argument>
                <argument type="string">%evp_sepa.bank_key_transfers_from_paysera_account%</argument>
                <argument type="string">%evp_sepa.bank_key_transfers_from_third_paysera_account%</argument>
            </argument>
            <argument type="collection">
                <argument type="string">%paysera_sepa_instant.sepa_instant_bank_key%</argument>
            </argument>
            <argument type="collection">
                <argument type="string">%paysera_target2_rtgs.target2_bank_key%</argument>
            </argument>
            <argument type="string">%paysera_georgia_rtgs.georgia_rtgs_bank_key%</argument>
            <argument type="collection">
                <argument key="lt_nordea_pnp">lt_nordea</argument>
                <argument key="lt_nordea_urgent_pnp">lt_nordea_urgent</argument>
            </argument>
            <argument>GATEWAY_RECEIVED_LT_LB_PAYSERA_SEPA_EUR</argument>
            <argument>%evp_sepa.sepa3_account_shutdown_date%</argument>
            <argument>%evp_sepa.sepa3_account_start_date%</argument>
            <argument>%evp_accounting.operation_processor.evp_main_account%</argument>
            <argument type="collection">
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_ee%</argument>
                    <argument key="template">W2P_RECEIVED_TRANSIT_INBANK_EE</argument>
                </argument>
                <argument type="collection">
                    <argument key="account">%evp_accounting.operation_processor.partner_account.mokilizingas_lv%</argument>
                    <argument key="template">W2P_RECEIVED_TRANSIT_INBANK_LV</argument>
                </argument>
            </argument>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_in"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferInOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInOperation"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_operation_handler"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_accounting.partner_accounting_processor"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_in_commission"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferInCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferInCommissionOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_out"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferOutOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.external_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.bank_resolver"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_date_service"/>
            <argument type="service" id="evp_accounting.partner_accounting_processor"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_out_commission"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\TransferOutCommissionOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\TransferOutCommissionOperation"/>

            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.revoke_transfer_operation"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\RevokeTransferOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferInOperation"/>
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutOperation"/>
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\RevokeTransferOutCommissionOperation"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.partial_return_transfer_operation"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\PartialReturnTransferOperationProcessor"
                 parent="evp_accounting.operation_processor.base">
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\PartialReturnTransferInOperation"/>
            <tag name="evp_accounting.operation_processor"
                 class="Evp\Bundle\BankTransferBundle\Entity\Operation\PartialReturnTransferOutOperation"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.internal_operation_handler"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.internal_partner_operation_handler"/>
            <argument type="service" id="evp_accounting.service.accounting_exception_handler"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_partner_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutPartnerToContisAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="16"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_partner_from_local_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutPartnerFromLocalToContisAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="15"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_partner_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInPartnerToContisAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="16"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider" />
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_partner_from_local_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInPartnerFromLocalToContisAccountOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="15"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_leasing_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutLeasingOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="38"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
            <argument type="string" >%evp_accounting.operation_processor.partner_charge_account%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_leasing_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInLeasingOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="31"/>

            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
            <argument type="service" id="evp_bundle_accounting.service.transfer_received_template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.operation_covenantee_id_resolver"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="logger"/>
            <argument type="string" >%evp_accounting.operation_processor.partner_charge_account%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out_same_partner_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutSamePartnerToContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="17"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in_same_partner_to_contis_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInSamePartnerToContisOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="18"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out.contis_contis.from_contis_partner_to_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutCardToCardFromContisPartnerToPartnerOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="26"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in.contis_contis.from_contis_partner_to_partner_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInCardToCardFromContisPartnerToPartnerOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="26"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="string">%evp_accounting.operation_processor.contis_partner_code%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_in.from_transit_to_transit_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferInBetweenTransitOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="in" priority="21"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.internal_partner.transfer_out.from_transit_to_transit_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\InternalPartner\TransferOutBetweenTransitOperationCreator">
            <tag name="evp_bank_transfer.internal_partner_operation_creator" type="out" priority="21"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_out_vmi_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutVMICreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector"/>
            <argument type="service" id="evp_bundle_accounting.service.vmi_template_resolver"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.transfer_out_partner_vmi_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferOutPartnerVMICreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_bank_transfer.transfer_to_vmi_detector"/>
            <argument type="service" id="evp_bundle_accounting.service.vmi_template_resolver"/>
            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.helper"/>
        </service>

        <service id="evp_bank_transfer.operation_processor.contis_fill_account_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\ContisFillAccountOperationCreator">
            <tag name="evp_bank_transfer.unique_external_partner_operation_creator"/>

            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_accounting.service.contis_fill_account_template_resolver"/>
            <argument>%evp_contis.contis_beneficiary_ibans%</argument>
        </service>

        <service id="evp_bank_transfer.operation_processor.external_partner.transfer_in_inbank_operation_creator"
                 class="Evp\Bundle\BankTransferBundle\Service\OperationProcessor\Strategy\ExternalPartner\TransferInInbankOperationCreator">
            <tag name="evp_bank_transfer.external_partner_operation_creator"/>

            <argument type="service" id="evp_bank_transfer.service.template_provider"/>
            <argument type="service" id="evp_bank_transfer.operation_processor.transfer_from_operation"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="collection">
                <argument type="string">inbank_lt_leasing</argument>
                <argument type="string">inbank_lt_bnpl_3</argument>
                <argument type="string">lt_mokilizingas</argument>
            </argument>
        </service>
    </services>
</container>
