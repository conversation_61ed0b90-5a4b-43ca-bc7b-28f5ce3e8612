<?php

namespace Evp\Bundle\BankTransferBundle\Repository;

use DateTime;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankTransferBundle\Entity\Event\TransferInEvent;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Component\GatewayCommon\BankTransfer\Entity\TransfersFilter;
use Paysera\Bundle\SepaInstantBundle\Entity\TransactionIn;
use Paysera\Bundle\TransferSurveillanceBundle\Service\TransferInspectionRepositoryHelper;

class TransferInRepository extends TransferRepository
{
    /**
     * @param DateTime $endDate
     * @param DateTime $startDate
     * @param int $limit
     * @return TransferIn[]
     */
    public function findPreparedForProcess(DateTime $endDate, DateTime $startDate, int $limit = 400): array
    {
        $queryBuilder = $this->createQueryBuilder('t')
            ->select('t')
            ->leftJoin(TransactionIn::class, 'tr', Join::WITH, 't = tr.transfer')
            ->leftJoin(TransferInEvent::class, 'tie', Join::WITH, 't = tie.transfer')
            ->andWhere('t.operationDate <= :endDate')
            ->andWhere('t.operationDate >= :startDate')
            ->andWhere('t.status = :status')
            ->andWhere('tr.id IS NULL OR tr.status = :transactionStatus')
            ->andWhere('tie.id is NULL')
            ->setParameter('transactionStatus', TransactionIn::STATUS_ACCEPTED)
            ->setParameter('endDate', $endDate)
            ->setParameter('startDate', $startDate)
            ->setParameter('status', Transfer::STATUS_PREPARED)
            ->orderBy('t.id', 'asc')
        ;

        $queryBuilder = TransferInspectionRepositoryHelper::applyTransferInInspectionCheck($queryBuilder, 't');

        return $queryBuilder
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Returns query builder with builded query for selecting ready for process transfers.
     * Must have 2 fields: status and date
     *
     * @return QueryBuilder
     */
    protected function getReadyForProcessQueryBuilder(): QueryBuilder
    {
        $queryBuilder = $this->createQueryBuilder('t')
            ->select('t')
            ->where('t.operationDate <= :date')
            ->andWhere('t.status = :status')
            ->orderBy('t.id', 'desc')
        ;

        // todo: temporary solution for TRANSFERS-1338
        $queryBuilder
            ->leftJoin(TransactionIn::class, 'tr', Join::WITH, 't = tr.transfer')
            ->andWhere('tr.id IS NULL OR tr.status = :transactionStatus')
            ->setParameter('transactionStatus', TransactionIn::STATUS_ACCEPTED)
        ;

        return TransferInspectionRepositoryHelper::applyTransferInInspectionCheck($queryBuilder, 't');
    }

    /**
     * @param string $accountNumber
     *
     * @return array
     */
    public function findTransferIdListByDebitOrCreditAccountNumber($accountNumber)
    {
        $transfers = $this->createQueryBuilder('t')
            ->select('t.id')
            ->leftJoin('t.creditAccount', 'ca')
            ->join('t.debitAccount', 'da')
            ->andWhere('(ca.number = :accountNumber OR da.number = :accountNumber)')
            ->setParameter('accountNumber', $accountNumber)
            ->getQuery()
            ->getScalarResult()
        ;

        return array_map('current', $transfers);
    }

    /**
     * @param Account $account
     * @param TransfersFilter $filter
     *
     * @deprecated returns lowercase countries
     *
     * @return array
     */
    public function findPartyAccountCountriesListByAccount(Account $account, TransfersFilter $filter)
    {
        $queryBuilder = $this
            ->createQueryBuilder('t')
            ->select('p.country')
            ->from('EvpBankTransferBundle:TransferParty\PartyAccountCountry', 'p')
            ->where('t.debitAccount = :account')
            ->andWhere('t.payer = p')
            ->andWhere('p.country IS NOT NULL')
            ->setParameter('account', $account)
        ;

        $queryBuilder = $this->applyTransfersFilterOnCountriesQueryBuilder($queryBuilder, $filter);

        $partyAccountCountries = $queryBuilder
            ->getQuery()
            ->getArrayResult()
        ;

        $countries = [];
        foreach ($partyAccountCountries as $partyAccountCountry) {
            $countries[] = strtolower($partyAccountCountry['country']); // TODO: SUPPORT-6339
        }

        if ($filter->getMinNumberOfTransactionsPerCountry() !== null) {
            return $this->removeCountriesWhereTransactionCountIsLowerThan(
                $countries,
                $filter->getMinNumberOfTransactionsPerCountry()
            );
        }

        return $countries;
    }

    /**
     * @param Account $account
     * @param TransfersFilter $filter
     *
     * @deprecated returns lowercase countries
     *
     * @return array
     */
    public function findPartyIbanCountriesListByAccount(Account $account, TransfersFilter $filter)
    {
        $queryBuilder = $this
            ->createQueryBuilder('t')
            ->select('SUBSTRING(p.iban, 1, 2) country')
            ->from('EvpBankTransferBundle:TransferParty\PartyIban', 'p')
            ->where('t.debitAccount = :account')
            ->andWhere('t.payer = p')
            ->andWhere('p.iban IS NOT NULL')
            ->setParameter('account', $account)
        ;
        $queryBuilder = $this->applyTransfersFilterOnCountriesQueryBuilder($queryBuilder, $filter);

        $partyIbanCountries = $queryBuilder
            ->getQuery()
            ->getArrayResult()
        ;

        $countries = [];
        foreach ($partyIbanCountries as $partyIbanCountry) {
            $countries[] = strtolower($partyIbanCountry['country']); // TODO: SUPPORT-6339
        }

        if ($filter->getMinNumberOfTransactionsPerCountry() !== null) {
            return $this->removeCountriesWhereTransactionCountIsLowerThan(
                $countries,
                $filter->getMinNumberOfTransactionsPerCountry()
            );
        }

        return $countries;
    }

    /**
     * @param int $relatedDebitChargeId
     *
     * @return TransferIn|null
     */
    public function findOneByDebitChargeId($relatedDebitChargeId)
    {
        /** @var TransferIn|null $transferIn */
        $transferIn = $this->findOneBy(['relatedDebitCommissionCharge' => $relatedDebitChargeId]);

        return $transferIn;
    }

    /**
     * @param string $number
     * @param DateTime|null $date
     * @return TransferIn[]
     */
    public function findByBeneficiaryNumber(string $number, DateTime $date = null): array
    {
        $query = $this->createQueryBuilder('ti')
            ->join(
                'EvpBankTransferBundle:TransferParty\PartyAccount',
                'b',
                Join::WITH,
                'ti.beneficiary = b'
            )
            ->where('b.number = :number')
            ->setParameters([
                'number' => $number,
            ])
            ->setMaxResults(100)
        ;

        if ($date !== null) {
            $query->andWhere('ti.createdAt >= :date')
                ->setParameter('date', $date)
            ;
        }

        return $query->getQuery()->getResult();
    }

    /**
     * @param string $iban
     * @param DateTime|null $date
     * @return TransferIn[]
     */
    public function findByBeneficiaryIban(string $iban, DateTime $date = null): array
    {
        $query = $this->createQueryBuilder('ti')
            ->join(
                'EvpBankTransferBundle:TransferParty\PartyIban',
                'b',
                Join::WITH,
                'ti.beneficiary = b'
            )
            ->where('b.iban = :iban')
            ->setParameters([
                'iban' => $iban,
            ])
            ->setMaxResults(100)
        ;

        if ($date !== null) {
            $query->andWhere('ti.createdAt >= :date')
                ->setParameter('date', $date)
            ;
        }

        return $query->getQuery()->getResult();
    }

    /**
     * @param QueryBuilder $queryBuilder
     * @param TransfersFilter $filter
     *
     * @return QueryBuilder
     */
    private function applyTransfersFilterOnCountriesQueryBuilder(QueryBuilder $queryBuilder, TransfersFilter $filter)
    {
        if ($filter->getCurrency()) {
            $queryBuilder
                ->andWhere('t.amountCurrency = :currency')
                ->setParameter('currency', $filter->getCurrency())
            ;
        }

        if ($filter->getFrom()) {
            $queryBuilder
                ->andWhere('t.createdAt >= :createdFrom')
                ->setParameter('createdFrom', $filter->getFrom())
            ;
        }

        if ($filter->getStatuses()) {
            $queryBuilder
                ->andWhere('t.status IN (:statuses)')
                ->setParameter('statuses', $filter->getStatuses())
            ;
        }

        if ($filter->getMinNumberOfTransactionsPerCountry() === null) {
            $queryBuilder
                ->distinct()
            ;
        }

        if (!empty($filter->getSkippedTransferPurposes())) {
            $queryBuilder
                ->andWhere('t.purpose NOT IN (:purposes)')
                ->setParameter('purposes', $filter->getSkippedTransferPurposes())
            ;
        }

        return $queryBuilder;
    }

    /**
     * @param QueryBuilder $queryBuilder
     */
    protected function groupQueryByAccount(QueryBuilder $queryBuilder)
    {
        $queryBuilder->groupBy('t.debitAccount');
    }

    /**
     * @param array $countries
     * @param int $number
     *
     * @return array
     */
    private function removeCountriesWhereTransactionCountIsLowerThan(array $countries, $number)
    {
        $countriesCount = array_count_values($countries);
        $countries = array_unique($countries);
        $countriesToRemove = [];

        foreach ($countriesCount as $country => $count) {
            if ($count < $number) {
                $countriesToRemove[] = $country;
            }
        }

        return array_diff($countries, $countriesToRemove);
    }

    public function findTransferByChargeId(int $chargeId): ?TransferIn
    {
        return $this->createQueryBuilder('t')
            ->where('t.relatedDebitCommissionCharge = :chargeId')
            ->setParameter('chargeId', $chargeId)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
}
