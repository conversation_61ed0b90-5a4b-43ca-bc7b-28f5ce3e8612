<?php

declare(strict_types=1);

namespace Evp\Bundle\BankTransferBundle\DTO;

class ValidationContextDTO
{
    /**
     * @var string[]|null
     */
    private ?array $validationGroups;

    public function __construct()
    {
        $this->validationGroups = null;
    }

    public function getValidationGroups(): ?array
    {
        return $this->validationGroups;
    }

    public function setValidationGroups(?array $validationGroups): void
    {
        $this->validationGroups = $validationGroups;
    }
}