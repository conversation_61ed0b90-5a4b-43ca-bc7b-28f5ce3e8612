<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Listener;

use ArrayObject;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Entity\DebtAwareInterface;
use Evp\Bundle\DebtBundle\Entity\SeizureManual;
use Evp\Bundle\DebtBundle\Event\CollectionEvent;
use Evp\Bundle\DebtBundle\Event\SeizureEvent;
use Exception;
use Paysera\Client\UserNotificationClient\Entity\Notification;
use Paysera\Client\UserNotificationClient\Entity\NotificationRequest;
use Paysera\Client\UserNotificationClient\UserNotificationClient;
use Psr\Log\LoggerInterface;
use Symfony\Component\Workflow\Event\Event;

class ClientNotificationListener
{
    private UserNotificationClient $userNotificationClient;
    private LoggerInterface $logger;
    private array $notificationChannels;
    private string $notificationKeyDebtActivated;
    private string $notificationKeyDebtDeactivated;

    public function __construct(
        UserNotificationClient $userNotificationClient,
        LoggerInterface $logger,
        array $notificationChannels,
        string $notificationKeyDebtActivated,
        string $notificationKeyDebtDeactivated
    ) {
        $this->userNotificationClient = $userNotificationClient;
        $this->logger = $logger;
        $this->notificationChannels = $notificationChannels;
        $this->notificationKeyDebtActivated = $notificationKeyDebtActivated;
        $this->notificationKeyDebtDeactivated = $notificationKeyDebtDeactivated;
    }

    public function onManualCollectionStatusChanged(Event $event): void
    {
        $this->logger->info('Manual collection status changed.');

        $collection = $event->getSubject();
        if (!$collection instanceof CollectionManual) {
            $this->makeError('Unsupported entity in ClientNotificationListener::onManualCollectionStatusChanged.');
            return;
        }

        if (in_array(CollectionManual::STATUS_ACTIVE, $event->getTransition()->getTos(), true)) {
            $this->debtActivated($collection);
        } elseif (in_array(CollectionManual::STATUS_ACTIVE, $event->getTransition()->getFroms(), true)) {
            $this->debtDeactivated($collection);
        }
    }

    public function onManualSeizureStatusChanged(Event $event): void
    {
        $this->logger->info('Manual seizure status changed.');

        $seizure = $event->getSubject();
        if (!$seizure instanceof SeizureManual) {
            $this->makeError('Unsupported entity in ClientNotificationListener::onManualSeizureStatusChanged.');
            return;
        }

        if (in_array(SeizureManual::STATUS_ACTIVE, $event->getTransition()->getTos(), true)) {
            $this->debtActivated($seizure);
        } elseif (in_array(SeizureManual::STATUS_ACTIVE, $event->getTransition()->getFroms(), true)) {
            $this->debtDeactivated($seizure);
        }
    }

    public function onCollectionEvent(CollectionEvent $event): void
    {
        $collection = $event->getCollection();
        $this->logger->info('Collection event.', [$collection->getNumber()]);

        if ($collection->isActive()) {
            $this->debtActivated($collection);
        } else {
            $this->debtDeactivated($collection);
        }
    }

    public function onSeizureEvent(SeizureEvent $event): void
    {
        $seizure = $event->getSeizure();
        $this->logger->info('Seizure event.', [$seizure->getNumber()]);

        if ($seizure->isActive()) {
            $this->debtActivated($seizure);
        } else {
            $this->debtDeactivated($seizure);
        }
    }

    private function debtActivated(DebtAwareInterface $debt): void
    {
        $this->logger->info('Debt activated.');
        $this->sendNotifications($debt, $this->notificationKeyDebtActivated);
    }

    private function debtDeactivated(DebtAwareInterface $debt): void
    {
        $this->logger->info('Debt deactivated.');
        $this->sendNotifications($debt, $this->notificationKeyDebtDeactivated);
    }

    private function sendNotifications(DebtAwareInterface $debt, string $key): void
    {
        if (count($this->notificationChannels) === 0) {
            $this->logger->info('No notification channels configured. Notifying skipped.');
        }

        $client = $debt->getDebtClient()->getClient();

        if ($client->getDisplayName() === null) {
            $this->makeError('Client display name is null. Cannot send notification.');
            return;
        }

        $amount = $debt->getAmountMoney() !== null ? $debt->getAmountMoney()->getAsString() : null;

        $context = new ArrayObject([
            'document_number' => $debt->getNumber(),
            'customer_name' => $client->getDisplayName(),
            'amount' => $amount,
        ]);

        $notifications = array_map(
            fn (string $channel) => $this->createNotification($client, $channel, $key, $context),
            $this->notificationChannels
        );

        try {
            $this->userNotificationClient->createNotificationRequest(
                (new NotificationRequest())
                    ->setStrategy(NotificationRequest::STRATEGY_UNIQUE_TARGET)
                    ->setNotifications($notifications)
            );
        } catch (Exception $exception) {
            $this->makeError(sprintf('Failed to send notification: %s', $exception->getMessage()));
        }
    }

    private function createNotification(Client $client, string $channel, string $key, ArrayObject $context): Notification
    {
        return (new Notification())
            ->setUserId((string) $client->getCovenanteeId())
            ->setChannel($channel)
            ->setNotificationKey($key)
            ->setLocale($client->getLocale())
            ->setContext($context)
        ;
    }

    private function makeError(string $message): void
    {
        $this->logger->error(sprintf('DebtBundle exceptional case: %s', $message));
    }
}
