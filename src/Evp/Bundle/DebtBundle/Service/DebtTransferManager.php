<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Service;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountLock;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferRequest;
use Evp\Bundle\BankTransferBundle\Entity\TransferSignature;
use Evp\Bundle\BankTransferBundle\Service\TransferProcessor\TransferInstantWithoutClientProcessor;
use Evp\Bundle\ContisBundle\Service\CardBalanceManager;
use Evp\Bundle\CurrencyBundle\Service\CurrencyConverterInterface;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Exception\DebtTransferManagerException;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Psr\Log\LoggerInterface;
use Symfony\Component\Translation\TranslatorInterface;

class DebtTransferManager
{
    private EntityManagerInterface $entityManager;
    private AccountLock $accountLock;
    private TransferInstantWithoutClientProcessor $transferInstantWithoutClientProcessor;
    private HoldHelper $holdHelper;
    private TranslatorInterface $translator;
    private LoggerInterface $logger;
    private array $internalTransferSignatureParameters;
    private array $outTransferSignatureParameters;
    private string $bankKey;
    private CurrencyConverterInterface $currencyConverter;
    private string $defaultCurrency;

    public function __construct(
        EntityManagerInterface $entityManager,
        AccountLock $accountLock,
        TransferInstantWithoutClientProcessor $transferInstantWithoutClientProcessor,
        HoldHelper $holdHelper,
        TranslatorInterface $translator,
        LoggerInterface $logger,
        array $internalTransferSignatureParameters,
        array $outTransferSignatureParameters,
        string $bankKey,
        CurrencyConverterInterface $currencyConverter,
        string $defaultCurrency
    ) {
        $this->entityManager = $entityManager;
        $this->accountLock = $accountLock;
        $this->transferInstantWithoutClientProcessor = $transferInstantWithoutClientProcessor;
        $this->holdHelper = $holdHelper;
        $this->translator = $translator;
        $this->logger = $logger;
        $this->internalTransferSignatureParameters = $internalTransferSignatureParameters;
        $this->outTransferSignatureParameters = $outTransferSignatureParameters;
        $this->bankKey = $bankKey;
        $this->currencyConverter = $currencyConverter;
        $this->defaultCurrency = $defaultCurrency;
    }

    /**
     * Make sure you are using transactions!
     *
     * @param Account $aggregatingAccount
     * @param PartyBank $beneficiaryParty
     * @param Collection $collection
     * @param Money $amount
     *
     * @throws DebtTransferManagerException
     */
    public function collectAndProcess(
        Account $aggregatingAccount,
        PartyBank $beneficiaryParty,
        Collection $collection,
        Money $amount
    ): void {
        $convertedAmount = $this->convertAmount($collection, $amount);

        if (!$this->isEnoughAmount($collection, $convertedAmount)) {
            $this->logger->error(
                'DebtBundle exceptional case: Not enough amount on hold to perform write off operation',
                [$collection, $amount]
            );

            throw new DebtTransferManagerException('Not enough amount on hold to preform write off operation');
        }

        $collectionRelatedTransfers = $this->collect($aggregatingAccount, $collection, $convertedAmount);
        $collectionRelatedTransfers[] = $this->process(
            $aggregatingAccount,
            $collection,
            $beneficiaryParty,
            $convertedAmount
        );

        foreach ($collectionRelatedTransfers as $collectionRelatedTransfer) {
            $collection->addTransfer($collectionRelatedTransfer);
        }

        $this->entityManager->persist($collection);
    }

    private function collect(
        Account $aggregatingAccount,
        Collection $collection,
        Money $amount
    ): array {
        $this->logger->info(
            'DebtTransferManager: start collecting required amount for write off!',
            [$aggregatingAccount->getIban(), $collection->getNumber(), $amount]
        );

        $this->accountLock->initContextEntryPoint(__METHOD__);
        $this->accountLock->lockAccounts($this->getAccountsToLock($collection));

        $amountToCollectLeft = clone $amount;
        $internalTransfers = [];
        $collectionActiveHolds = $this->holdHelper->filterActiveHolds($collection->getHolds()->toArray());

        foreach ($collectionActiveHolds as $collectionHold) {
            $amountForInternalTransfer = $this->convertAmount($collection, $collectionHold->getHoldAmountMoney());

            if ($amountForInternalTransfer->isGt($amountToCollectLeft)) {
                $amountForInternalTransfer = clone $amountToCollectLeft;
            }

            $this->holdHelper->releaseHold($collectionHold);

            if (
                $collectionHold->getAccount()->getId() !== $aggregatingAccount->getId()
                && $amountForInternalTransfer->isPositive()
            ) {
                $internalTransfers[] = $this->transferBetweenAccounts(
                    $collectionHold->getAccount(),
                    $aggregatingAccount,
                    $amountForInternalTransfer,
                    $collection
                );
            }

            $amountToCollectLeft = $amountToCollectLeft->sub($amountForInternalTransfer);
        }

        $this->logger->info(
            'DebtTransferManager: required amount collected!',
            [$aggregatingAccount->getIban(), $collection->getNumber(), $amount]
        );

        return $internalTransfers;
    }

    private function process(
        Account $aggregatingAccount,
        Collection $collection,
        PartyBank $beneficiaryParty,
        Money $amount
    ): TransferOutBank {
        $this->logger->info(
            'DebtTransferManager: start of write off process!',
            [$aggregatingAccount->getIban(), $amount]
        );

        $this->accountLock->initContextEntryPoint(__METHOD__);
        $this->accountLock->lockAccount($aggregatingAccount);

        $transferOut = $this->transferOutBank(
            $aggregatingAccount,
            $amount,
            $collection,
            $beneficiaryParty
        );

        $this->logger->info('DebtTransferManager: write off transfer processed!');

        return $transferOut;
    }

    /**
     * @param Collection $collection
     *
     * @return Account[]
     */
    private function getAccountsToLock(Collection $collection): array
    {
        return array_map(
            function (Hold $hold) {
                return $hold->getAccount();
            },
            $collection->getHolds()->toArray()
        );
    }

    private function transferBetweenAccounts(
        Account $creditAccount,
        Account $debitAccount,
        Money $amount,
        Collection $collection
    ): TransferInternal {
        $details = $this->translator->trans(
            'collection_transfer.internal',
            ['%number%' => $collection->getNumber(), '%account_number%' => $debitAccount->getNumber()],
            'EvpDebtBundle',
            $creditAccount->getClient()->getLocale()
        );

        $amount = $this->convertInternalTransferAmount($creditAccount, $amount);
        $transfer = new TransferInternal();
        $transfer
            ->setPayer(new PartyAccount($creditAccount->getNumber(), $creditAccount->getClient()->getDisplayName()))
            ->setBeneficiary(new PartyAccount($debitAccount->getNumber()))
            ->setDetails($details)
            ->setOperationDate(new DateTime())
            ->setDate(new DateTime())
            ->setPriority(Transfer::PRIORITY_NORMAL)
            ->setAmountMoney($amount)
            ->setClient($creditAccount->getClient())
            ->setPurpose(Transfer::PURPOSE_DEBT_AUTOMATIC)
            ->setFormedAutomatically(true)
            ->setCancelable(false)
            ->setAutoCurrencyConvert(true)
        ;

        $transferRequest = (new TransferRequest())
            ->addTransfer($transfer)
        ;

        $internalTransferSignature = (new TransferSignature())
            ->setParameters($this->internalTransferSignatureParameters)
        ;
        $internalTransferSignature->addParameter('debt_collection_number', $collection->getNumber());

        $this->transferInstantWithoutClientProcessor->processTransferRequest(
            $transferRequest,
            $internalTransferSignature
        );

        if ($transfer->getStatus() !== TransferInternal::STATUS_DONE) {
            $this->logger->error(
                'DebtBundle exceptional case: Transfer failure status',
                [$transfer->getFailureStatus()]
            );

            throw new DebtTransferManagerException('Transfer between client accounts failed!');
        }

        $this->entityManager->persist($transferRequest);

        return $transfer;
    }

    private function transferOutBank(
        Account $aggregatingAccount,
        Money $amount,
        Collection $collection,
        PartyBank $beneficiaryParty
    ): TransferOutBank {
        $details = sprintf('Payment for collection order (%s)', $collection->getNumber());

        $transfer = (new TransferOutBank())
            ->setPayer(
                new PartyIban(
                    $aggregatingAccount->getIban(),
                    $aggregatingAccount->getClient()->getDisplayName()
                )
            )
            ->setBeneficiary($beneficiaryParty)
            ->setDetails($details)
            ->setOperationDate(new DateTime())
            ->setDate(new DateTime())
            ->setPriority(Transfer::PRIORITY_NORMAL)
            ->setAmountMoney($amount)
            ->setClient($aggregatingAccount->getClient())
            ->setBank($this->bankKey)
            ->setPurpose(Transfer::PURPOSE_DEBT_AUTOMATIC)
            ->setFormedAutomatically(true)
            ->setCancelable(false)
            ->setAutoCurrencyConvert(true)
        ;

        $transferRequest = (new TransferRequest())
            ->addTransfer($transfer)
        ;
        $transferOutSignature = (new TransferSignature())
            ->setParameters($this->outTransferSignatureParameters)
        ;
        $transferOutSignature->addParameter('debt_collection_number', $collection->getNumber());

        $this->transferInstantWithoutClientProcessor->processTransferRequest(
            $transferRequest,
            $transferOutSignature
        );

        if ($transfer->getFailureStatus() !== null) {
            $this->logger->error(
                'DebtBundle exceptional case: Out Transfer failed!',
                [$transfer->getFailureStatus()->getCode(), $transfer->getFailureStatus()->getMessage()]
            );

            throw new DebtTransferManagerException(
                sprintf(
                    'DebtTransferManager: Out transfer failed (%s - %s)',
                    $transfer->getFailureStatus()->getCode(),
                    $transfer->getFailureStatus()->getMessage()
                )
            );
        }

        if (!$transfer->isReady()) {
            $this->logger->error(
                'DebtBundle exceptional case: Out Transfer not ready!',
                [$collection->getNumber(), $transfer->getStatus()]
            );

            throw new DebtTransferManagerException('DebtTransferManager: Out Transfer not ready!');
        }

        $this->entityManager->persist($transferRequest);

        return $transfer;
    }

    private function isEnoughAmount(
        Collection $collection,
        Money $requiredAmount
    ): bool {
        $totalHoldsAmount = Money::createZero($this->resolveCurrencyForConversion($collection));
        $collectionActiveHolds = $this->holdHelper->filterActiveHolds($collection->getHolds()->toArray());

        foreach ($collectionActiveHolds as $collectionHold) {
            $totalHoldsAmount = $totalHoldsAmount->add(
                $this->convertAmount($collection, $collectionHold->getHoldAmountMoney())
            );
        }

        return $totalHoldsAmount->isGte($requiredAmount);
    }

    private function resolveCurrencyForConversion(Collection $collection): string
    {
        if ($collection->getCurrency() !== null) {
            return $collection->getCurrency();
        }

        return $this->defaultCurrency;
    }

    private function convertAmount(Collection $collection, ?Money $amount = null): Money
    {
        if ($amount === null) {
            return Money::createZero($this->resolveCurrencyForConversion($collection));
        }

        return $this->currencyConverter->convert(
            $amount,
            $this->resolveCurrencyForConversion($collection),
            null,
            $collection->getClient()
        );
    }

    private function convertInternalTransferAmount(Account $creditAccount, Money $amount): Money
    {
        if (
            $creditAccount->getType() !== Account::TYPE_CONTIS
            || $amount->getCurrency() === CardBalanceManager::CONTIS_CURRENCY
        ) {
            return $amount;
        }

        return $this->currencyConverter->convert(
            $amount,
            CardBalanceManager::CONTIS_CURRENCY,
            null,
            $creditAccount->getClient()
        );
    }
}
