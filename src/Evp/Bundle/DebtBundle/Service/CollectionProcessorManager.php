<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Service;

use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\DebtBundle\DebtEvents;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Event\CollectionEvent;
use Evp\Bundle\DebtBundle\Exception\CollectionTypeNotSupportedException;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Exception\ProcessorNotExistsException;
use Evp\Bundle\DebtBundle\Service\CollectionProcessor\CollectionProcessor;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\DebtCommon\Entity\Collection\CancelCollection\CommonCancelCollection;
use Evp\Component\DebtCommon\Entity\Collection\ChangeCollection\CommonChangeCollection;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollection;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollection;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollectionResponse;
use Evp\Component\DebtCommon\Entity\CommonResponse;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class CollectionProcessorManager
{
    private HoldManager $holdManager;
    private HoldHelper $holdHelper;
    private ClientRepository $clientRepository;
    private AmountMoneyResolver $amountMoneyResolver;
    private EventDispatcherInterface $dispatcher;

    /** @var CollectionProcessor[] */
    private array $collectionProcessors;
    private DebtClientCollectionManager $debtClientCollectionManager;

    public function __construct(
        HoldManager $holdManager,
        HoldHelper $holdHelper,
        ClientRepository $clientRepository,
        AmountMoneyResolver $amountMoneyResolver,
        DebtClientCollectionManager $debtClientCollectionManager,
        EventDispatcherInterface $dispatcher
    ) {
        $this->holdManager = $holdManager;
        $this->holdHelper = $holdHelper;
        $this->clientRepository = $clientRepository;
        $this->amountMoneyResolver = $amountMoneyResolver;
        $this->debtClientCollectionManager = $debtClientCollectionManager;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param CommonCollection $commonCollection
     * @return void
     * @throws CollectionTypeNotSupportedException
     * @throws ProcessorNotExistsException
     * @throws ProcessorErrorException
     * @throws LicensedPartnerNotFoundException
     */
    public function create(CommonCollection $commonCollection): void
    {
        $processor = $this->getCollectionProcessor($commonCollection->getCollectionType());

        $client = $this->clientRepository->findOneById($commonCollection->getClientId());
        if ($client === null) {
            throw new ProcessorErrorException(CommonResponse::ERROR_CLIENT_NOT_FOUND);
        }

        if (!$this->amountMoneyResolver->isAmountValid($commonCollection->getAmount())) {
            throw new ProcessorErrorException(CommonResponse::ERROR_AMOUNT_IS_NOT_VALID);
        }

        $collection = $processor->create($commonCollection, $client);
        $debtClient = $this->debtClientCollectionManager->getOrCreateDebtClientCollection($client);
        $collection->setDebtClient($debtClient);

        $this->holdManager->processCollections($collection->getDebtClient());
        $this->dispatcher->dispatch(DebtEvents::ON_COLLECTION_CREATED, new CollectionEvent($collection));
    }

    /**
     * @param CommonCancelCollection $commonCancelCollection
     * @return void
     * @throws ProcessorNotExistsException
     * @throws ProcessorErrorException
     */
    public function cancel(CommonCancelCollection $commonCancelCollection): void
    {
        $processor = $this->getCollectionProcessor($commonCancelCollection->getCollectionType());
        $collection = $processor->cancel($commonCancelCollection);

        $this->holdHelper->releaseAllActiveCollectionHolds($collection);
        $this->holdManager->processCollections($collection->getDebtClient());
        $this->dispatcher->dispatch(DebtEvents::ON_COLLECTION_CANCELLED, new CollectionEvent($collection));
    }

    /**
     * @throws ProcessorErrorException
     */
    public function update(CommonChangeCollection $commonChangeCollection): void
    {
        $processor = $this->getCollectionProcessor($commonChangeCollection->getCollectionType());

        if (!$this->amountMoneyResolver->isAmountValid($commonChangeCollection->getAmount())) {
            throw new ProcessorErrorException(CommonResponse::ERROR_AMOUNT_IS_NOT_VALID);
        }

        $collection = $processor->update($commonChangeCollection);

        $this->holdManager->processCollections($collection->getDebtClient());
        $this->dispatcher->dispatch(DebtEvents::ON_COLLECTION_UPDATED, new CollectionEvent($collection));
    }

    /**
     * @param CommonReturnCollection $commonReturnCollection
     * @return Collection[]
     * @throws ProcessorNotExistsException
     * @throws ProcessorErrorException
     */
    public function returnCollections(CommonReturnCollection $commonReturnCollection): array
    {
        $processor = $this->getCollectionProcessor($commonReturnCollection->getCollectionType());

        $client = $this->clientRepository->findOneById($commonReturnCollection->getClientId());
        if ($client === null) {
            throw new ProcessorErrorException(CommonReturnCollectionResponse::ERROR_CLIENT_NOT_FOUND);
        }

        $returnedCollections = $processor->returnCollections($commonReturnCollection, $client);

        if (count($returnedCollections) > 0) {
            foreach ($returnedCollections as $returnedCollection) {
                $this->holdHelper->releaseAllActiveCollectionHolds($returnedCollection);
                $this->dispatcher->dispatch(
                    DebtEvents::ON_COLLECTION_RETURNED,
                    new CollectionEvent($returnedCollection)
                );
            }

            $this->holdManager->processCollections(reset($returnedCollections)->getDebtClient());
        }

        return $returnedCollections;
    }

    public function addCollectionProcessor(CollectionProcessor $collectionProcessor, string $key): void
    {
        $this->collectionProcessors[$key] = $collectionProcessor;
    }

    /**
     * @param string $key
     * @return CollectionProcessor
     * @throws ProcessorNotExistsException
     */
    private function getCollectionProcessor(string $key): CollectionProcessor
    {
        if (!array_key_exists($key, $this->collectionProcessors)) {
            throw new ProcessorNotExistsException(sprintf('Collection type %s not exists', $key));
        }

        return $this->collectionProcessors[$key];
    }
}
