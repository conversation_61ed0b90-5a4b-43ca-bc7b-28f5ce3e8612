<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Service\CollectionProcessor;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Repository\CollectionGebRepository;
use Evp\Bundle\DebtBundle\Service\AmountMoneyResolver;
use Evp\Bundle\DebtBundle\Service\HoldManager;
use Evp\Bundle\DebtBundle\Service\SeizureProcessor\SeizureGebProcessor;
use Evp\Component\DebtCommon\Entity\Collection\CancelCollection\CommonCancelCollection;
use Evp\Component\DebtCommon\Entity\Collection\CancelCollection\CommonCancelCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\ChangeCollection\CommonChangeCollection;
use Evp\Component\DebtCommon\Entity\Collection\ChangeCollection\CommonChangeCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollection;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollection;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollectionGeb;
use Evp\Component\DebtCommon\Entity\CommonResponse;

class CollectionGebProcessor extends CollectionProcessor
{
    private EntityManagerInterface $entityManager;
    private CollectionGebRepository $collectionGebRepository;
    private LicensedPartnerRepository $licensedPartnerRepository;
    private SeizureGebProcessor $seizureGebProcessor;
    private AmountMoneyResolver $amountMoneyResolver;
    private HoldManager $holdManager;
    private string $licensedPartnerCode;

    public function __construct(
        EntityManagerInterface $entityManager,
        CollectionGebRepository $collectionGebRepository,
        LicensedPartnerRepository $licensedPartnerRepository,
        SeizureGebProcessor $seizureGebProcessor,
        AmountMoneyResolver $amountMoneyResolver,
        HoldManager $holdManager,
        string $licensedPartnerCode
    ) {
        $this->entityManager = $entityManager;
        $this->collectionGebRepository = $collectionGebRepository;
        $this->licensedPartnerRepository = $licensedPartnerRepository;
        $this->seizureGebProcessor = $seizureGebProcessor;
        $this->amountMoneyResolver = $amountMoneyResolver;
        $this->holdManager = $holdManager;
        $this->licensedPartnerCode = $licensedPartnerCode;
    }

    /**
     * @param CommonCollectionGeb $commonCollection
     * @throws LicensedPartnerNotFoundException
     * @throws ProcessorErrorException
     */
    public function create(CommonCollection $commonCollection, Client $client): Collection
    {
        $collection = $this->collectionGebRepository->findOneByNumber($commonCollection->getNumber());

        // TODO: SUPPORT-92722 improve readability, without if iside if
        if ($collection !== null) {
            if (!$collection->isActive()) {
                throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_ALREADY_CANCELLED);
            }
        } else {
            $collection = new CollectionGeb();
        }

        $licensedPartner = $this->licensedPartnerRepository->findOneByPartnerCode($this->licensedPartnerCode);
        if ($licensedPartner === null) {
            throw new LicensedPartnerNotFoundException($this->licensedPartnerCode);
        }

        // TODO: SUPPORT-92722 move if logic inside enforce method
        if ($commonCollection->getSourceRestrictCode() !== null && $commonCollection->getSourceRestrictCode() !== '') {
            $this->seizureGebProcessor->enforce($commonCollection->getSourceRestrictCode(), $commonCollection);
        }

        $amountMoney = $this->amountMoneyResolver->resolve(
            $commonCollection->getAmount(),
            $commonCollection->getCurrency()
        );

        $collection->setNumber($commonCollection->getNumber())
            ->setOrganizationPriority(Collection::PRIORITY_NATIONAL_ENFORCEMENT_BUREAU)
            ->setAmountMoney($amountMoney)
            ->setAmountLeftMoney($amountMoney)
            ->setClient($client)
            ->setLicensedPartner($licensedPartner)
            ->setPriority($commonCollection->getPriority())
            ->setBeneficiaryAccountNumber($commonCollection->getReceiverAccount())
            ->setBeneficiaryBankSwiftCode($commonCollection->getReceiverCode())
        ;
        $this->entityManager->persist($collection);

        return $collection;
    }

    /**
     * @param CommonCancelCollectionGeb $commonCancelCollection
     * @return CollectionGeb
     * @throws ProcessorErrorException
     */
    public function cancel(CommonCancelCollection $commonCancelCollection): Collection
    {
        $collection = $this->collectionGebRepository->findOneByNumber($commonCancelCollection->getNumber());

        if ($collection === null) {
            throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_NOT_FOUND);
        }

        if (!$collection->isActive()) {
            throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_ALREADY_CANCELLED);
        }

        $collection->setStatus(Collection::STATUS_CANCELLED);
        $collection->resetAmountLeftMoney();

        return $collection;
    }

    /**
     * @param CommonChangeCollectionGeb $commonChangeCollection
     * @return CollectionGeb
     * @throws ProcessorErrorException
     */
    public function update(CommonChangeCollection $commonChangeCollection): Collection
    {
        $collection = $this->collectionGebRepository->findOneByNumber($commonChangeCollection->getNumber());

        if ($collection === null) {
            throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_NOT_FOUND);
        }

        if (!$collection->isActive()) {
            throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_ALREADY_CANCELLED);
        }

        if ($commonChangeCollection->getCurrency() !== $collection->getCurrency()) {
            throw new ProcessorErrorException(CommonResponse::ERROR_COLLECTION_DIFFERENT_CURRENCY);
        }

        if ($commonChangeCollection->getAmount() !== $collection->getAmount()) {
            $amountMoney = $this->amountMoneyResolver->resolve(
                $commonChangeCollection->getAmount(),
                $commonChangeCollection->getCurrency()
            );

            $collection->setAmountMoney($amountMoney);
            $collection->setAmountLeftMoney($amountMoney);
            $collection->setWriteOffStatus(null);

            $this->holdManager->moveMoneyFromCollection($collection);
        }

        return $collection;
    }

    /**
     * @param CommonReturnCollectionGeb $commonReturnCollection
     * @return CollectionGeb[]
     */
    public function returnCollections(CommonReturnCollection $commonReturnCollection, Client $client): array
    {
        $collections = $this->collectionGebRepository->findAllByClientAndStatuses(
            $client,
            [Collection::STATUS_ACTIVE]
        );

        foreach ($collections as $collection) {
            $collection->setStatus(Collection::STATUS_RETURNED);
            $collection->resetAmountLeftMoney();
        }

        return $collections;
    }
}
