<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Repository;

use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Entity\SeizureGeb;

/**
 * @method SeizureGeb|null findOneBy(array $criteria, array $orderBy = null)
 */
class SeizureGebRepository extends SeizureRepository
{
    public function findOneByNumber(string $number): ?SeizureGeb
    {
        return $this->findOneBy(['number' => $number]);
    }

    /**
     * @param Client $client
     * @return string[]
     */
    public function findActiveSeizureNumbersByClient(Client $client): array
    {
        $queryBuilder = $this->createQueryBuilder('s')
            ->select('s.number')
            ->where('s.client = :client')
            ->andWhere('s.status = :status')
            ->setParameters([
                'client' => $client,
                'status' => Seizure::STATUS_ACTIVE,
            ])
        ;
        return array_column($queryBuilder->getQuery()->getArrayResult(), 'number');
    }
}
