<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Repository;

use DateTime;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Entity\Filter\CollectionFilter;
use Evp\Component\DebtCommon\Entity\Collection\ListCollectionFilter;

/**
 * @method Collection|null findOneBy(array $criteria, array $orderBy = null)
 * @method Collection[] findAll()
 */
class CollectionRepository extends EntityRepository
{
    public function findById(int $id): ?Collection
    {
        /** @var Collection|null $collection */
        $collection = $this->find($id);
        return $collection;
    }

    public function findOneByNumber(string $number): ?Collection
    {
        /** @var Collection $collection */
        $collection = $this->findOneBy(['number' => $number], ['id' => 'DESC']);

        return $collection;
    }

    /**
     * @return Collection[]
     */
    public function findAllActiveByClient(Client $client): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.client = :client')
            ->andWhere('c.status = :status')
            ->setParameters([
                'client' => $client,
                'status' => Collection::STATUS_ACTIVE,
            ])
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @return Collection[]
     */
    public function findAllActiveByClientAndWriteOffStatuses(Client $client, ?array $writeOffStatuses): array
    {
        return $this->getQueryBuilderByClientAndWriteOffStatuses($client, $writeOffStatuses)
            ->getQuery()
            ->getResult()
        ;
    }

    public function countActiveByClientAndWriteOffStatuses(Client $client, ?array $writeOffStatuses): int
    {
        return (int) $this->getQueryBuilderByClientAndWriteOffStatuses($client, $writeOffStatuses)
            ->select('COUNT(c.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @return Client[]
     */
    public function findClientsWithActiveCollections(): array
    {
        /** @var Collection[] $collections */
        $collections = $this->createQueryBuilder('collection')
            ->join('collection.client', 'client')
            ->andWhere('collection.status = :status')
            ->andWhere('collection.writeOffStatus IS NULL')
            ->setParameters([
                'status' => Collection::STATUS_ACTIVE,
            ])
            ->groupBy('client.id')
            ->getQuery()
            ->getResult()
        ;

        $clients = [];
        foreach ($collections as $collection) {
            $clients[] = $collection->getClient();
        }

        return $clients;
    }

    public function countAllByFilter(CollectionFilter $filter): int
    {
        return (int) $this->getQueryBuilderByFilter($filter)
            ->select('COUNT(c.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param ListCollectionFilter $listCollectionFilter
     * @return Collection[]
     */
    public function findAllByListCollectionFilter(ListCollectionFilter $listCollectionFilter): array
    {
        $qb = $this->createQueryBuilder('dc')
            ->where('dc.client = :client')
            ->setParameter('client', $listCollectionFilter->getClientId())
        ;

        if ($listCollectionFilter->getCollectionType() !== null) {
            $qb->andWhere('dc INSTANCE OF :collectionType')
                ->setParameter('collectionType', $listCollectionFilter->getCollectionType())
            ;
        }

        if ($listCollectionFilter->getNumber() !== null) {
            $qb->andWhere('dc.number = :number')
                ->setParameter('number', $listCollectionFilter->getNumber())
            ;
        }

        if ($listCollectionFilter->getStatus() !== null) {
            $qb->andWhere('dc.status = :status')
                ->setParameter('status', $listCollectionFilter->getStatus())
            ;
        }

        return $qb->getQuery()
            ->getResult()
        ;
    }

    public function countCollectionsWithoutCommission(): int
    {
        $commissionSubQuery = $this->_em->createQueryBuilder()
            ->select('IDENTITY(commission.collection)')
            ->from(Commission::class, 'commission')
        ;

        return (int) $this->createQueryBuilder('c')
            ->select('COUNT(c)')
            ->innerJoin('c.transfers', 'transfers')
            ->where($commissionSubQuery->expr()->notIn('c.id', $commissionSubQuery->getDQL()))
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findCountWithClientPartnerCodes(string $status): array
    {
        $qb = $this->createQueryBuilder('c');
        return $qb
            ->select([
                'partner.partnerCode as partnerCode',
                'COUNT(c.id) as count',
            ])
            ->leftJoin('EvpClientBundle:PartnerClient', 'partner', Join::WITH, 'partner.client = c.client')
            ->where('c.status = :status')
            ->andWhere('partner.assignedFrom <= :todayDateTime')
            ->andWhere($qb->expr()->orX(
                'partner.assignedTo > :todayDateTime',
                'partner.assignedTo IS NULL'
            ))
            ->setParameters([
                'todayDateTime' => new DateTime(),
                'status' => $status,
            ])
            ->groupBy('partner.partnerCode')
            ->getQuery()
            ->getScalarResult()
        ;
    }

    private function getQueryBuilderByFilter(CollectionFilter $filter): QueryBuilder
    {
        $qb = $this->createQueryBuilder('c');

        if (count($filter->getOrganizationPriorities())) {
            $qb->andWhere('c.organizationPriority IN (:organizationPriorities)')
                ->setParameter('organizationPriorities', $filter->getOrganizationPriorities())
            ;
        }

        if (count($filter->getStatuses())) {
            $qb->andWhere('c.status IN (:statuses)')
                ->setParameter('statuses', $filter->getStatuses())
            ;
        }

        if (count($filter->getWriteOffStatuses())) {
            $qb->andWhere('c.writeOffStatus IN (:writeOffStatuses)')
                ->setParameter('writeOffStatuses', $filter->getWriteOffStatuses())
            ;
        }

        if ($filter->getCreatedFrom() !== null) {
            $qb->andWhere('c.createdAt >= :createdFrom')
                ->setParameter('createdFrom', $filter->getCreatedFrom())
            ;
        }

        if ($filter->getCreatedTo() !== null) {
            $qb->andWhere('c.createdAt < :createdTo')
                ->setParameter('createdTo', $filter->getCreatedTo())
            ;
        }

        if ($filter->getUpdatedFrom() !== null) {
            $qb->andWhere('c.updatedAt >= :updatedFrom')
                ->setParameter('updatedFrom', $filter->getUpdatedFrom())
            ;
        }

        if ($filter->getUpdatedTo() !== null) {
            $qb->andWhere('c.updatedAt < :updatedTo')
                ->setParameter('updatedTo', $filter->getUpdatedTo())
            ;
        }

        return $qb;
    }

    private function getQueryBuilderByClientAndWriteOffStatuses(Client $client, ?array $writeOffStatuses): QueryBuilder
    {
        $qb = $this->createQueryBuilder('c')
            ->where('c.client = :client')
            ->andWhere('c.status = :status')
            ->setParameters([
                'client' => $client,
                'status' => Collection::STATUS_ACTIVE,
            ])
        ;

        if ($writeOffStatuses !== null) {
            $qb->andWhere('c.writeOffStatus IN (:writeOffStatuses)')
                ->setParameter('writeOffStatuses', $writeOffStatuses);
        } else {
            $qb->andWhere('c.writeOffStatus IS NULL');
        }

        return $qb;
    }
}
