<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Evp\Bundle\DebtBundle\Admin\CollectionOrderAdmin;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionComment;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Form\CollectionCommentType;
use LogicException;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Yokai\SonataWorkflow\Controller\WorkflowControllerTrait;

class CollectionOrderAdminController extends CRUDController
{
    use WorkflowControllerTrait;

    /** @var CollectionOrderAdmin */
    protected $admin;

    protected function preApplyTransition(Collection $object, string $transition)
    {
        if (!$this->admin instanceof CollectionOrderAdmin) {
            throw new LogicException('Admin must be instanceof CollectionOrderAdmin');
        }

        if (!$this->admin->isCollectionCorrectType($object)) {
            throw new LogicException('Allowed apply transition only for manual type collection with manual trigger type');
        }

        if ($transition === CollectionManual::TRANSITION_TO_DONE) {
            $this->addFlash(
                'error',
                'Collection order will get "done" status only after amount left to collect is 0, automatically.'
            );

            return $this->redirect(
                $this->admin->generateObjectUrl(
                    'edit',
                    $object
                )
            );
        }

        $form = $this->createForm(
            CollectionCommentType::class,
            [],
            [
                'action' => $this->admin->generateObjectUrl(
                    'workflow_apply_transition',
                    $object,
                    ['transition' => $transition]
                ),
            ]
        );
        $form->handleRequest($this->getRequest());
        if (!$form->isSubmitted() || !$form->isValid()) {
            $formView = $form->createView();
            return $this->renderWithExtraParams('@EvpDebt/Admin/Collection/collection_comment.html.twig', [
                'action' => 'show',
                'form' => $formView,
                'object' => $object,
            ]);
        }

        $data = $form->getData();
        $comment = (new CollectionComment())
            ->setComment($data['comment'])
            ->setCollection($object)
            // @phpstan-ignore-next-line
            ->setUserId($this->admin->getTokenStorage()->getToken()->getUser()->getId())
            ->setStatus($object->getStatus())
        ;

        $this->admin->getEntityManager()->persist($comment);
        $object->addComment($comment);

        return null;
    }

    /**
     * @param Request $request
     * @param Collection $object
     * @return Response|null
     */
    public function preShow(Request $request, $object): ?Response
    {
        foreach ($object->getComments() as $comment) {
            $userId = $comment->getUserId();
            if ($userId !== null) {
                // @phpstan-ignore-next-line
                $user = $this->admin->getEntityManager()->getRepository(User::class)->find($userId);
                if ($user !== null) {
                    $comment->setComment(
                        sprintf(
                            '%s (%s | %s | %s)',
                            $comment->getComment(),
                            $comment->getStatus(),
                            $user->getUsername(),
                            $comment->getCreatedAt()->format('Y-m-d H:i:s')
                        )
                    );
                }
            }
        }

        return null;
    }
}
