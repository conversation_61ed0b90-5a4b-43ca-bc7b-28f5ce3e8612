<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Evp\Bundle\DebtBundle\Admin\SeizureOrdersAdmin;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Entity\SeizureComment;
use Evp\Bundle\DebtBundle\Form\SeizureCommentType;
use LogicException;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Yokai\SonataWorkflow\Controller\WorkflowControllerTrait;

class SeizureOrderAdminController extends CRUDController
{
    use WorkflowControllerTrait;

    /** @var SeizureOrdersAdmin */
    protected $admin;

    protected function preApplyTransition(Seizure $object, string $transition)
    {
        if (!$this->admin instanceof SeizureOrdersAdmin) {
            throw new LogicException('Admin must be instanceof SeizureOrdersAdmin');
        }
        if (!$this->admin->isSeizureCorrectType($object)) {
            throw new LogicException('Allowed apply transition only for manual type seizure with manual trigger type');
        }
        $form = $this->createForm(
            SeizureCommentType::class,
            [],
            [
                'action' => $this->admin->generateObjectUrl(
                    'workflow_apply_transition',
                    $object,
                    ['transition' => $transition]
                ),
            ]
        );
        $form->handleRequest($this->getRequest());
        if (!$form->isSubmitted() || !$form->isValid()) {
            $formView = $form->createView();
            return $this->renderWithExtraParams('@EvpDebt/Admin/Seizure/seizure_comment.html.twig', [
                'action' => 'show',
                'form' => $formView,
                'object' => $object,
            ]);
        }
        $data = $form->getData();
        $comment = (new SeizureComment())
            ->setComment($data['comment'])
            ->setSeizure($object)
            // @phpstan-ignore-next-line
            ->setUserId($this->admin->getTokenStorage()->getToken()->getUser()->getId())
            ->setStatus($object->getStatus())
        ;

        $this->admin->getEntityManager()->persist($comment);
        $object->addComment($comment);

        return null;
    }

    /**
     * @param Request $request
     * @param Seizure $object
     * @return Response|null
     */
    public function preShow(Request $request, $object): ?Response
    {
        foreach ($object->getComments() as $comment) {
            $userId = $comment->getUserId();
            if ($userId !== null) {
                // @phpstan-ignore-next-line
                $user = $this->admin->getEntityManager()->getRepository(User::class)->find($userId);
                if ($user !== null) {
                    $comment->setComment(
                        sprintf(
                            '%s (%s | %s | %s)',
                            $comment->getComment(),
                            $comment->getStatus(),
                            $user->getUsername(),
                            $comment->getCreatedAt()->format('Y-m-d H:i:s')
                        )
                    );
                }
            }
        }

        return null;
    }
}
