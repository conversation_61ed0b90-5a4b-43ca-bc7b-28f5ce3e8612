<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\DependencyInjection;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\DebtBundle\Entity\SeizureManual;
use Evp\Bundle\DebtBundle\Repository\SeizureManualRepository;
use LogicException;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\HttpKernel\Kernel;
use Symfony\Component\Workflow\Registry;

class SeizureManualWorkflowTest extends PersistableWebTestCase
{
    private Registry $workflowRegistry;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private SeizureManualRepository $repository;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->workflowRegistry = $this->getContainer()->get('workflow.registry');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->repository = $this->getContainer()->get('evp_debt.repository.seizure_manual');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testAcceptableFlow(): void
    {
        $this->prepareSeizure();

        $seizure = $this->repository->findAll()[0];
        $this->assertEquals(SeizureManual::STATUS_REGISTERED, $seizure->getStatus());

        $this->assertEquals(
            SeizureManual::STATUS_RETURNED,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_RETURNED))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_REGISTERED,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_REGISTERED))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_CONFIRMED,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_CONFIRMED))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_ACTIVE,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_ACTIVE))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_SUSPENDED,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_SUSPENDED))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_ACTIVE,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_ACTIVE))->getStatus()
        );

        $this->assertEquals(
            SeizureManual::STATUS_CLOSED,
            ($this->makeTransition($seizure, SeizureManual::TRANSITION_TO_CLOSED))->getStatus()
        );
    }

    public function testNotAcceptableFlow(): void
    {
        $this->prepareSeizure();

        $seizure = $this->repository->findAll()[0];
        $this->assertEquals(SeizureManual::STATUS_REGISTERED, $seizure->getStatus());

        $this->expectException(LogicException::class);

        // @phpstan-ignore-next-line
        if (Kernel::MAJOR_VERSION < 4) {
            //TODO Remove after upgrade to symfony 4
            $this->expectExceptionMessage('Unable to apply transition "to_active" for workflow "debt_seizure_manual"');
        // @phpstan-ignore-next-line
        } else {
            $this->expectExceptionMessage('Transition "to_active" is not enabled for workflow "debt_seizure_manual"');
        }

        $this->makeTransition($seizure, SeizureManual::TRANSITION_TO_ACTIVE);
    }

    private function makeTransition(SeizureManual $seizure, string $transition): SeizureManual
    {
        $workflow = $this->workflowRegistry->get($seizure);
        $workflow->apply($seizure, $transition);
        $this->entityManager->flush();

        return $this->repository->findAll()[0];
    }

    private function prepareSeizure(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtSeizureManual(
            '1',
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensedPartner
        );
        $this->entityManager->flush();
    }
}
