<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\DependencyInjection;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Repository\CollectionManualRepository;
use LogicException;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\HttpKernel\Kernel;
use Symfony\Component\Workflow\Registry;

class CollectionManualWorkflowTest extends PersistableWebTestCase
{
    private Registry $workflowRegistry;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionManualRepository $repository;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->workflowRegistry = $this->getContainer()->get('workflow.registry');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->repository = $this->getContainer()->get('evp_debt.repository.collection_manual');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testAcceptableFlow(): void
    {
        $this->prepareCollection();

        $collection = $this->repository->findAll()[0];
        $this->assertEquals(CollectionManual::STATUS_REGISTERED, $collection->getStatus());

        $this->assertEquals(
            CollectionManual::STATUS_RETURNED,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_RETURNED))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_REGISTERED,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_REGISTERED))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_CONFIRMED,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_CONFIRMED))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_ACTIVE,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_ACTIVE))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_SUSPENDED,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_SUSPENDED))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_ACTIVE,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_ACTIVE))->getStatus()
        );

        $this->assertEquals(
            CollectionManual::STATUS_DONE,
            ($this->makeTransition($collection, CollectionManual::TRANSITION_TO_DONE))->getStatus()
        );
    }

    public function testNotAcceptableFlow(): void
    {
        $this->prepareCollection();

        $collection = $this->repository->findAll()[0];
        $this->assertEquals(CollectionManual::STATUS_REGISTERED, $collection->getStatus());

        $this->expectException(LogicException::class);

        // @phpstan-ignore-next-line
        if (Kernel::MAJOR_VERSION < 4) {
            //TODO Remove after upgrade to symfony 4
            $this->expectExceptionMessage('Unable to apply transition "to_done" for workflow "debt_collection_manual"');
        // @phpstan-ignore-next-line
        } else {
            $this->expectExceptionMessage('Transition "to_done" is not enabled for workflow "debt_collection_manual"');
        }

        $this->makeTransition($collection, CollectionManual::TRANSITION_TO_DONE);
    }

    private function makeTransition(CollectionManual $collection, string $transition): CollectionManual
    {
        $workflow = $this->workflowRegistry->get($collection);
        $workflow->apply($collection, $transition);
        $this->entityManager->flush();

        return $this->repository->findAll()[0];
    }

    private function prepareCollection(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtCollectionManual(
            '1',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );
        $this->entityManager->flush();
    }
}
