<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Service\AmountMoneyResolver;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Debug\BufferingLogger;

class AmountMoneyResolverTest extends PersistableWebTestCase
{
    private AmountMoneyResolver $amountMoneyResolver;
    private MockObject $logger;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->logger = $this->createPartialMock(BufferingLogger::class, ['error']);

        $this->amountMoneyResolver = new AmountMoneyResolver(
            $this->logger,
            $this->getContainer()->getParameter('evp_debt.default_currency')
        );
    }

    /** @dataProvider resolveDataProvider */
    public function testResolve(?Money $expectedResult, ?string $amount, ?string $currency): void
    {
        $result = $this->amountMoneyResolver->resolve($amount, $currency);

        $this->assertEquals($expectedResult, $result);
    }

    public function resolveDataProvider(): array
    {
        return [
            'Null amount' => [
                'expectedResult' => null,
                'amount' => null,
                'currency' => 'EUR',
            ],
            'Null currency float' => [
                'expectedResult' => new Money('10.12', 'GEL'),
                'amount' => '10.12',
                'currency' => null,
            ],
            'Null currency int' => [
                'expectedResult' => new Money(10, 'GEL'),
                'amount' => '10',
                'currency' => null,
            ],
            'Null currency and amount' => [
                'expectedResult' => null,
                'amount' => null,
                'currency' => null,
            ],
            'Float amount EUR' => [
                'expectedResult' => new Money('10.12', 'EUR'),
                'amount' => '10.12',
                'currency' => 'EUR',
            ],
            'Int amount EUR' => [
                'expectedResult' => new Money(10, 'EUR'),
                'amount' => '10',
                'currency' => 'EUR',
            ],
            'Float amount GEL' => [
                'expectedResult' => new Money('10.12', 'GEL'),
                'amount' => '10.12',
                'currency' => 'GEL',
            ],
            'Int amount GEL' => [
                'expectedResult' => new Money(10, 'GEL'),
                'amount' => '10',
                'currency' => 'GEL',
            ],
        ];
    }

    /** @dataProvider isAmountValidDataProvider */
    public function testIsAmountValid(bool $expectedResult, ?string $amount): void
    {
        $result = $this->amountMoneyResolver->isAmountValid($amount);

        $this->assertEquals($expectedResult, $result);
    }

    public function isAmountValidDataProvider(): array
    {
        return [
            'Null amount' => [
                'expectedResult' => true,
                'amount' => null,
            ],
            'Null currency float' => [
                'expectedResult' => true,
                'amount' => '10.12',
            ],
            'Null currency int' => [
                'expectedResult' => true,
                'amount' => '10',
            ],
            'Null currency zero amount' => [
                'expectedResult' => false,
                'amount' => '0',
            ],
            'Null currency negative amount float' => [
                'expectedResult' => false,
                'amount' => '-10.12',
            ],
            'Null currency negative amount int' => [
                'expectedResult' => false,
                'amount' => '-10',
            ],
            'Null currency and amount' => [
                'expectedResult' => true,
                'amount' => null,
            ],
            'Float amount EUR' => [
                'expectedResult' => true,
                'amount' => '10.12',
            ],
            'Int amount EUR' => [
                'expectedResult' => true,
                'amount' => '10',
            ],
            '0 amount EUR' => [
                'expectedResult' => false,
                'amount' => '0',
            ],
            'Negative amount int EUR' => [
                'expectedResult' => false,
                'amount' => '-10',
            ],
            'Negative amount float EUR' => [
                'expectedResult' => false,
                'amount' => '-10.12',
            ],
            'Float amount GEL' => [
                'expectedResult' => true,
                'amount' => '10.12',
            ],
            'Int amount GEL' => [
                'expectedResult' => true,
                'amount' => '10',
            ],
            '0 amount GEL' => [
                'expectedResult' => false,
                'amount' => '0',
            ],
            'Negative amount int GEL' => [
                'expectedResult' => false,
                'amount' => '-10',
            ],
            'Negative amount float GEL' => [
                'expectedResult' => false,
                'amount' => '-10.12',
            ],
        ];
    }

    /** @dataProvider resolveAmountLeftMoneyDataProvider */
    public function testResolveAmountLeftMoney(
        ?Money $expectedResult,
        ?Money $amountLeft,
        ?Money $transferred,
        bool $expectLogError = false
    ): void {
        $collection = (new CollectionGrs())
            ->setNumber('1')
            ->setAmountMoney($amountLeft)
            ->setAmountLeftMoney($amountLeft)
        ;

        $this->logger
            ->expects($expectLogError ? $this->once() : $this->never())
            ->method('error')
            ->with('DebtBundle exceptional case: amountLeft is negative or transferred is greater than amountLeft')
        ;

        $result = $this->amountMoneyResolver->resolveAmountLeftMoney($collection, $transferred);


        $this->assertEquals($expectedResult, $result);
    }

    public function resolveAmountLeftMoneyDataProvider(): array
    {
        return [
            'Left positive, transferred null' => [
                'expectedResult' => new Money(100, 'GEL'),
                'amountLeft' => new Money(100, 'GEL'),
                'transferred' => null,
            ],
            'Left positive, transferred zero' => [
                'expectedResult' => new Money(100, 'GEL'),
                'amountLeft' => new Money(100, 'GEL'),
                'transferred' => new Money(0, 'GEL'),
            ],
            'Left positive, transferred less than left' => [
                'expectedResult' => new Money(50, 'GEL'),
                'amountLeft' => new Money(100, 'GEL'),
                'transferred' => new Money(50, 'GEL'),
            ],
            'Left positive, transferred less equal to left' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(100, 'GEL'),
                'transferred' => new Money(100, 'GEL'),
            ],
            'Left positive, transferred more than left' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(100, 'GEL'),
                'transferred' => new Money(150, 'GEL'),
                'expectLogError' => true,
            ],
            'Left zero, transferred null' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(0, 'GEL'),
                'transferred' => null,
            ],
            'Left zero, transferred zero' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(0, 'GEL'),
                'transferred' => new Money(0, 'GEL'),
            ],
            'Left zero, transferred less than left (Impossible, just for test)' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(0, 'GEL'),
                'transferred' => new Money(-50, 'GEL'),
                'expectLogError' => true,
            ],
            'Left zero, transferred more than left' => [
                'expectedResult' => new Money(0, 'GEL'),
                'amountLeft' => new Money(0, 'GEL'),
                'transferred' => new Money(50, 'GEL'),
                'expectLogError' => true,
            ],
            'Left null, transferred null' => [
                'expectedResult' => null,
                'amountLeft' => null,
                'transferred' => null,
            ],
            'Left null, transferred zero' => [
                'expectedResult' => null,
                'amountLeft' => null,
                'transferred' => new Money(0, 'GEL'),
            ],
            'Left null, transferred positive' => [
                'expectedResult' => null,
                'amountLeft' => null,
                'transferred' => new Money(100, 'GEL'),
            ],
        ];
    }
}
