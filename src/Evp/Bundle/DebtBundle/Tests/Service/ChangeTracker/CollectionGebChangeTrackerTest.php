<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\ChangeTracker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\DebtBundle\Repository\CollectionChangeLogRepository;
use Evp\Bundle\DebtBundle\Service\ChangeTracker\CollectionGebChangeTracker;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class CollectionGebChangeTrackerTest extends PersistableWebTestCase
{
    private CollectionGebChangeTracker $tracker;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionChangeLogRepository $changeLogRepository;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->tracker = $this->getContainer()->get('evp_debt.service.change_tracker.collection_geb');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->changeLogRepository = $this->getContainer()->get('evp_debt.repository.debt_collection_change_log');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testTracker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)time());
        $this->entityManager->flush();

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '00000000005',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensePartner
        );

        $this->tracker->trackChanges($collection, true);

        $this->entityManager->flush();

        $logs = $this->changeLogRepository->findBy(['collection' => $collection]);

        $this->assertNotEmpty($logs);
        $this->assertCount(22, $logs);
    }
}
