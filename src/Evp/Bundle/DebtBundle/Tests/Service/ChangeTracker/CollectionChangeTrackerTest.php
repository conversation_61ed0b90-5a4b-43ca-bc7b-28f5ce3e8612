<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\ChangeTracker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionChangeLog;
use Evp\Bundle\DebtBundle\Repository\CollectionChangeLogRepository;
use Evp\Bundle\DebtBundle\Service\ChangeTracker\CollectionChangeTracker;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class CollectionChangeTrackerTest extends PersistableWebTestCase
{
    private CollectionChangeTracker $tracker;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionChangeLogRepository $changeLogRepository;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->tracker = $this->getContainer()->get('evp_debt.service.change_tracker.collection');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->changeLogRepository = $this->getContainer()->get('evp_debt.repository.debt_collection_change_log');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testTracker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)time());
        $this->entityManager->flush();

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '00000000005',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensePartner
        );

        $this->tracker->trackChanges($collection, true);

        $this->entityManager->flush();

        $logs = $this->changeLogRepository->findBy(['collection' => $collection]);

        $this->assertNotEmpty($logs);

        /** @var CollectionChangeLog $log */
        $log = $logs[0];

        $this->assertEquals(call_user_func([$collection, 'get'.ucfirst($log->getField())]), $log->getNewValue());
    }

    public function testTrackerSecondUpdate(): void
    {
        $client = $this->fixturesHelper->createClientNatural(2);
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)(time() + 123));
        $this->entityManager->flush();

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '00000000002',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensePartner
        );

        $this->tracker->trackChanges($collection, true);

        $this->entityManager->flush();

        $logs1 = $this->changeLogRepository->findBy(['collection' => $collection]);

        /** @var Collection $collectionNew */
        $collectionNew = $this->entityManager
            ->getRepository('EvpDebtBundle:Collection')
            ->find($collection->getId())
        ;

        $collectionNew->setNumber('qwe');

        $this->entityManager->persist($collectionNew);

        $this->tracker->trackChanges($collectionNew);

        $this->entityManager->flush();

        $logs2 = $this->changeLogRepository->findBy(['collection' => $collectionNew]);

        $actualDifferent = count($logs2) - count($logs1);
        $this->assertEquals(1, $actualDifferent);
    }

    public function testTrackerAmountValue(): void
    {
        $client = $this->fixturesHelper->createClientNatural(2);
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)(time() + 123));
        $this->entityManager->flush();

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '00000000002',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensePartner
        );

        $this->tracker->trackChanges($collection, true);

        $this->entityManager->flush();

        $this->entityManager->clear();

        /** @var CollectionChangeLog $log */
        $log = $this->changeLogRepository->findOneBy([
            'collection' => $collection,
            'field' => 'amount'
        ]);


        $collection = $this->entityManager->getRepository('EvpDebtBundle:Collection')->findOneBy([
            'number' => '00000000002'
        ]);

        $this->assertNotNull($log);
        $this->assertSame($log->getNewValue(), number_format(floatval($collection->getAmount()), 6));
    }
}
