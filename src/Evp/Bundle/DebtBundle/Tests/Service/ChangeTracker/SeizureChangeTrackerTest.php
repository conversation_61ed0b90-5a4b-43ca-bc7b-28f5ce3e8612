<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\ChangeTracker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Entity\SeizureChangeLog;
use Evp\Bundle\DebtBundle\Repository\SeizureChangeLogRepository;
use Evp\Bundle\DebtBundle\Service\ChangeTracker\SeizureChangeTracker;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class SeizureChangeTrackerTest extends PersistableWebTestCase
{
    private SeizureChangeTracker $tracker;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private SeizureChangeLogRepository $changeLogRepository;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->tracker = $this->getContainer()->get('evp_debt.service.change_tracker.seizure');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->changeLogRepository = $this->getContainer()->get('evp_debt.repository.debt_seizure_change_log');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testTracker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)time());
        $this->entityManager->flush();

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '00000000005',
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensePartner
        );

        $this->tracker->trackChanges($seizure, true);

        $this->entityManager->flush();

        $logs = $this->changeLogRepository->findBy(['seizure' => $seizure]);

        $this->assertNotEmpty($logs);

        /** @var SeizureChangeLog $log */
        $log = $logs[0];

        $this->assertEquals(call_user_func([$seizure, 'get'.ucfirst($log->getField())]), $log->getNewValue());
    }

    public function testTrackerSecondUpdate(): void
    {
        $client = $this->fixturesHelper->createClientNatural(2);
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)(time() + 123));
        $this->entityManager->flush();

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '00000000002',
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensePartner
        );

        $this->tracker->trackChanges($seizure, true);

        $this->entityManager->flush();

        $logs1 = $this->changeLogRepository->findBy(['seizure' => $seizure]);

        /** @var Seizure $seizureNew */
        $seizureNew = $this->entityManager
            ->getRepository('EvpDebtBundle:Seizure')
            ->find($seizure->getId())
        ;

        $seizureNew->setNumber('qwe');

        $this->entityManager->persist($seizureNew);

        $this->tracker->trackChanges($seizureNew);

        $this->entityManager->flush();

        $logs2 = $this->changeLogRepository->findBy(['seizure' => $seizureNew]);

        $actualDifferent = count($logs2) - count($logs1);
        $this->assertEquals(1, $actualDifferent);
    }
}
