<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\ChangeTracker;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\DebtBundle\Repository\SeizureChangeLogRepository;
use Evp\Bundle\DebtBundle\Service\ChangeTracker\SeizureGebChangeTracker;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class SeizureGebChangeTrackerTest extends PersistableWebTestCase
{
    private SeizureGebChangeTracker $tracker;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private SeizureChangeLogRepository $changeLogRepository;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->tracker = $this->getContainer()->get('evp_debt.service.change_tracker.seizure_geb');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->changeLogRepository = $this->getContainer()->get('evp_debt.repository.debt_seizure_change_log');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testTracker(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensePartner = $this->fixturesHelper->createLicensedPartner((string)time());
        $this->entityManager->flush();

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '00000000005',
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensePartner
        );

        $this->tracker->trackChanges($seizure, true);

        $this->entityManager->flush();

        $logs = $this->changeLogRepository->findBy(['seizure' => $seizure]);

        $this->assertNotEmpty($logs);
        $this->assertCount(15, $logs);
    }
}
