<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\CurrencyBundle\Service\CurrencyConverterInterface;
use Evp\Bundle\DebtBundle\Repository\DebtClientCollectionRepository;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldCreatorService;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use ReflectionClass;

class HoldCreatorServiceTest extends PersistableWebTestCase
{
    private HoldCreatorService $holdCreatorService;
    private HoldHelper $holdHelper;
    private DebtClientCollectionRepository $debtClientCollectionRepository;
    private AccountManager $accountManager;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->any())
            ->method('convert')
            ->willReturnCallback(function ($amount, $toCurrency) {
                $rates = [
                    'GEL' => ['GEL' => 1, 'EUR' => 0.5],
                    'EUR' => ['GEL' => 2, 'EUR' => 1],
                ];

                $result = new Money($amount->getAmount(), $toCurrency);
                return $result->mul($rates[$amount->getCurrency()][$toCurrency]);
            })
        ;
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', $currencyConverter);

        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        /** @noinspection MissingService */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->holdCreatorService = $this->getContainer()->get('evp_debt.service.hold_service.hold_creator');
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->debtClientCollectionRepository = $this->getContainer()->get('evp_debt.repository.debt_client_collection');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param array $expectedHolds
     * @param Money|null $neededAmount
     * @param Money[]|null[] $balances
     * @param Money[]|null[] $holds
     * @param array $accountsData
     * @return void
     * @throws Exception
     * @dataProvider createCommonHoldsDataProvider
     */
    public function testCreateCommonHolds(
        array $expectedHolds,
        ?Money $neededAmount,
        array $balances,
        array $holds,
        array $accountsData = []
    ): void {
        // Prepare data
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);

        foreach ($balances as $accountId => $balance) {
            $account = $this->fixturesHelper->createAccount($client, (string) $accountId, (string) $accountId);

            if ($balance !== null) {
                $this->accountManager->fill($balance, $account);
            }

            $this->entityManager->flush();

            if (array_key_exists($accountId, $holds)) {
                $debtClientCollection->addHold($this->holdHelper->createHold($account, $holds[$accountId], ''));
            }

            if (array_key_exists($accountId, $accountsData)) {
                $this->fixturesHelper->createAccountPartner($account, $accountsData[$accountId]['partner']);
                $account->setActive($accountsData[$accountId]['active']);
                $account->setClosed($accountsData[$accountId]['closed']);
            } else {
                $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
            }
        }

        $this->entityManager->flush();

        // Run hold creator service
        $partnerCodes = [LicensedPartner::PAYSERA_GEORGIA];
        $this->holdCreatorService->createCommonHolds($debtClientCollection, $neededAmount, $partnerCodes, '');
        $this->entityManager->flush();

        // Receive new data from DB
        $debtClientCollection = $this->debtClientCollectionRepository->findOneByClient($client);
        $holds = $debtClientCollection->getHolds();

        // Check holds
        $this->assertEquals(count($expectedHolds), $holds->count(), 'Holds count mismatch');

        foreach ($holds as $key => $hold) {
            $this->assertEquals(
                $expectedHolds[$key]['status'],
                $hold->getStatus(),
                sprintf('Hold #%d. Status mismatch', $key)
            );
            $this->assertEquals(
                $expectedHolds[$key]['amount'],
                $hold->getAmountMoney(),
                sprintf('Hold #%d. Amount mismatch', $key)
            );
            $this->assertEquals(
                $expectedHolds[$key]['account'],
                $hold->getAccount()->getNumber(),
                sprintf('Hold #%d. Account mismatch', $key)
            );
        }
    }

    public function createCommonHoldsDataProvider(): array
    {
        return [
            'Balance more, no holds, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [],
            ],
            'Balance more, no holds, covers only one, another account with balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(200, 'GEL'),
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [],
            ],
            'Balance more, no holds, first account not enough to cover whole amount' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(60, 'GEL'), 'account' => '1'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(40, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(60, 'GEL'),
                    '2' => new Money(100, 'GEL'),
                ],
                'holds' => [],
            ],
            'Balance more, one hold, less amount, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => new Money(50, 'GEL'),
                ],
            ],
            'Balance more, one hold, same amount, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => new Money(100, 'GEL'),
                ],
            ],
            'Balance more, one hold, more amount, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(150, 'GEL'), 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => new Money(150, 'GEL'),
                ],
            ],
            'Balance more, one hold, null amount, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => null,
                ],
            ],
            'Balance more, one hold, covers only one, another account with balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(200, 'GEL'),
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => new Money(50, 'GEL'),
                ],
            ],
            'Balance more, one hold, first account not enough to cover whole amount' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '2' => new Money(50, 'GEL'),
                ],
            ],
            'Balance more, two holds, less amount, covers only one, another account without balance' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '1'],
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                    ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => null,
                    '2' => new Money(200, 'GEL'),
                ],
                'holds' => [
                    '1' => new Money(50, 'GEL'),
                    '2' => new Money(50, 'GEL'),
                ],
            ],
            'Balance more, two holds, same amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(200, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance more, two holds, more amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(150, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(150, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(200, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(150, 'GEL'),
                     '2' => new Money(150, 'GEL'),
                 ],
             ],
            'Balance more, two holds, null amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(200, 'GEL'),
                 ],
                 'holds' => [
                     '1' => null,
                     '2' => null,
                 ],
             ],
            'Balance more, two holds, covers only one, another account with balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(150, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(200, 'GEL'),
                     '2' => new Money(200, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(50, 'GEL'),
                     '2' => new Money(150, 'GEL'),
                 ],
             ],
            'Balance more, two holds, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(60, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(40, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(60, 'GEL'),
                     '2' => new Money(150, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(60, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance less, no holds, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(60, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Balance less, one hold, less amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(50, 'GEL'),
                 ],
             ],
            'Balance less, one hold, same amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance less, one hold, more amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(150, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(150, 'GEL'),
                 ],
             ],
            'Balance less, one hold, null amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '2' => null,
                 ],
             ],
            'Balance less, one hold, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(20, 'GEL'),
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance less, two holds, less amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(40, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(40, 'GEL'),
                     '2' => new Money(50, 'GEL'),
                 ],
             ],
            'Balance less, two holds, same amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance less, two holds, more amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(140, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(250, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(140, 'GEL'),
                     '2' => new Money(250, 'GEL'),
                 ],
             ],
            'Balance less, two holds, null amount, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '1' => null,
                     '2' => null,
                 ],
             ],
            'Balance less, two holds, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(40, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(50, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(20, 'GEL'),
                     '2' => new Money(75, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(40, 'GEL'),
                     '2' => new Money(50, 'GEL'),
                 ],
             ],
            'Balance equal, no holds, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Balance equal, no holds, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(60, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(40, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(60, 'GEL'),
                     '2' => new Money(40, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Balance equal, one hold, less amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(80, 'GEL'),
                 ],
             ],
            'Balance equal, one hold, same amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance equal, one hold, more amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(120, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(120, 'GEL'),
                 ],
             ],
            'Balance equal, one hold, null amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => null,
                 ],
             ],
            'Balance equal, one hold, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(20, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(20, 'GEL'),
                     '2' => new Money(80, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(70, 'GEL'),
                 ],
             ],
            'Balance equal, two holds, less amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(70, 'GEL'),
                     '2' => new Money(80, 'GEL'),
                 ],
             ],
            'Balance equal, two holds, same amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Balance equal, two holds, more amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(170, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(280, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(170, 'GEL'),
                     '2' => new Money(280, 'GEL'),
                 ],
             ],
            'Balance equal, two holds, null amount, covers only one, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => null,
                     '2' => null,
                 ],
             ],
            'Balance equal, two holds, first account not enough to cover whole amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(80, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(20, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(20, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_HOLD, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(100, 'GEL'),
                 'balances' => [
                     '1' => new Money(20, 'GEL'),
                     '2' => new Money(80, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(80, 'GEL'),
                     '2' => new Money(20, 'GEL'),
                 ],
             ],
            'Needed amount null, no holds, two accounts without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => null,
                     '2' => null,
                 ],
                 'holds' => [],
             ],
            'Needed amount null, no holds, another account without balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => null,
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Needed amount null, no holds, two accounts with balance' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Needed amount null, one hold, with amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Needed amount null, one hold, null amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => null,
                 ],
             ],
            'Needed amount null, two holds, with amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(100, 'GEL'), 'account' => '2'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
             ],
            'Needed amount null, two holds, null amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_WAITING_FUNDS, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => null,
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => null,
                     '2' => null,
                 ],
             ],
            'Needed amount zero, no holds' => [
                 'expectedHolds' => [],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [],
             ],
            'Needed amount zero, one hold, with amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(80, 'GEL'),
                 ],
             ],
            'Needed amount zero, one hold, null amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => null,
                 ],
             ],
            'Needed amount zero, one hold, zero amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(0, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '2' => new Money(0, 'GEL'),
                 ],
             ],
            'Needed amount zero, two holds, with amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(80, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(70, 'GEL'),
                     '2' => new Money(80, 'GEL'),
                 ],
             ],
            'Needed amount zero, two holds, null amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => null, 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => null,
                     '2' => null,
                 ],
             ],
            'Needed amount zero, two holds, zero amount' => [
                 'expectedHolds' => [
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(0, 'GEL'), 'account' => '1'],
                     ['status' => Hold::STATUS_DONE, 'amount' => new Money(0, 'GEL'), 'account' => '2'],
                 ],
                 'neededAmount' => new Money(0, 'GEL'),
                 'balances' => [
                     '1' => new Money(100, 'GEL'),
                     '2' => new Money(100, 'GEL'),
                 ],
                 'holds' => [
                     '1' => new Money(0, 'GEL'),
                     '2' => new Money(0, 'GEL'),
                 ],
             ],
            'Inactive accounts, no holds' => [
                'expectedHolds' => [],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [],
                'accountsData' => [
                    '1' => ['active' => false, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                    '2' => ['active' => false, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                ],
            ],
            'Inactive accounts, with holds more than needed' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '1'],
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(60, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [
                    '1' => new Money(70, 'GEL'),
                    '2' => new Money(60, 'GEL'),
                ],
                'accountsData' => [
                    '1' => ['active' => false, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                    '2' => ['active' => false, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                ],
            ],
            'Closed accounts, no holds' => [
                'expectedHolds' => [],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [],
                'accountsData' => [
                    '1' => ['active' => true, 'closed' => true, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                    '2' => ['active' => true, 'closed' => true, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                ],
            ],
            'Closed accounts, with holds more than needed' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '1'],
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(60, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [
                    '1' => new Money(70, 'GEL'),
                    '2' => new Money(60, 'GEL'),
                ],
                'accountsData' => [
                    '1' => ['active' => true, 'closed' => true, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                    '2' => ['active' => true, 'closed' => true, 'partner' => LicensedPartner::PAYSERA_GEORGIA],
                ],
            ],
            'Another partner accounts, no holds' => [
                'expectedHolds' => [],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [],
                'accountsData' => [
                    '1' => ['active' => true, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_ALBANIA],
                    '2' => ['active' => true, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_ALBANIA],
                ],
            ],
            'Another partner accounts, with holds more than needed' => [
                'expectedHolds' => [
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(70, 'GEL'), 'account' => '1'],
                    ['status' => Hold::STATUS_DONE, 'amount' => new Money(60, 'GEL'), 'account' => '2'],
                ],
                'neededAmount' => new Money(100, 'GEL'),
                'balances' => [
                    '1' => new Money(75, 'GEL'),
                    '2' => new Money(75, 'GEL'),
                ],
                'holds' => [
                    '1' => new Money(70, 'GEL'),
                    '2' => new Money(60, 'GEL'),
                ],
                'accountsData' => [
                    '1' => ['active' => true, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_ALBANIA],
                    '2' => ['active' => true, 'closed' => false, 'partner' => LicensedPartner::PAYSERA_ALBANIA],
                ],
            ],
        ];
    }

    /**
     * @dataProvider calculateHoldAmountsDataProvider
     * @throws Exception
     */
    public function testCalculateHoldAmounts(
        array $expected,
        ?Money $neededAmount,
        array $accountsData,
        array $partnerCodes = [LicensedPartner::PAYSERA_GEORGIA]
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClient = $this->fixturesHelper->createDebtClientCollection($client);

        foreach ($accountsData as $key => $item) {
            $account = $this->fixturesHelper->createAccount($client, (string) $key, (string) $key);
            $this->fixturesHelper->createAccountPartner($account, $item['partner']);

            foreach ($item['amounts'] as $amount) {
                $this->accountManager->fill($amount, $account);
            }

            $this->entityManager->flush();

            if (isset($item['holds'])) {
                foreach ($item['holds'] as $holdAmount) {
                    $debtClient->addHold($this->holdHelper->createHold($account, $holdAmount, ''));
                }
            }

            $account->setActive($item['active']);
            $account->setClosed($item['closed']);
        }

        $this->entityManager->flush();

        $reflection = new ReflectionClass($this->holdCreatorService);
        $calculateHoldAmounts = $reflection->getMethod('calculateHoldAmounts');
        $calculateHoldAmounts->setAccessible(true);

        $result = $calculateHoldAmounts->invokeArgs($this->holdCreatorService, [$debtClient, $neededAmount, $partnerCodes]);
        $this->assertEquals($expected, $result);
    }

    public function calculateHoldAmountsDataProvider(): array
    {
        $activeAccountSum100 = [
            'active' => true,
            'closed' => false,
            'amounts' => [new Money(50, 'GEL'), new Money(25, 'EUR')],
            'partner' => LicensedPartner::PAYSERA_GEORGIA,
        ];
        $closedAccountSum100 = array_merge($activeAccountSum100, ['closed' => true]);
        $inactiveAccountSum100 = array_merge($activeAccountSum100, ['active' => false]);
        $anotherPartnerAccountSum100 = array_merge(
            $activeAccountSum100,
            ['partner' => LicensedPartner::PAYSERA_LITHUANIA]
        );
        $activeAccountSum100Holds50 = array_merge(
            $activeAccountSum100,
            ['holds' => [new Money('25', 'GEL'), new Money('12.5', 'EUR')]]
        );
        $activeAccountSum100Holds100 = array_merge(
            $activeAccountSum100,
            ['holds' => [new Money('50', 'GEL'), new Money('25', 'EUR')]]
        );
        $activeAccountNoMoney = array_merge($activeAccountSum100, ['amounts' => []]);

        return [
            'Amount: GEL. All active. Balance: Covered without exceed amount.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Part active part closed. Balance: Covered without exceed amount.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('200', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $closedAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Part active part inactive. Balance: Covered without exceed amount.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('200', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $inactiveAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. All inactive. Balance: Zero.' => [
                'expected' => [],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $inactiveAccountSum100,
                    '2' => $inactiveAccountSum100,
                    '3' => $inactiveAccountSum100,
                ],
            ],
            'Amount: GEL. All closed. Balance: Zero.' => [
                'expected' => [],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $closedAccountSum100,
                    '2' => $closedAccountSum100,
                    '3' => $closedAccountSum100,
                ],
            ],
            'Amount: GEL. No accounts. Balance: Zero.' => [
                'expected' => [],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [],
            ],
            'Amount: GEL. Balance: Zero. All accounts for another partner' => [
                'expected' => [],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $anotherPartnerAccountSum100,
                    '2' => $anotherPartnerAccountSum100,
                    '3' => $anotherPartnerAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Covered without exceed amount. Part accounts for another partners' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('200', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $anotherPartnerAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Covered without exceed amount. Related to needed partner' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Covered with exceed amount. Partially with holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                    '4' => new Money('5.55', 'GEL'),
                ],
                'neededAmount' => new Money('305.55', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                    '4' => $activeAccountSum100,
                    '5' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Covered with exceed amount. Fully with holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                    '4' => new Money('5.55', 'GEL'),
                ],
                'neededAmount' => new Money('305.55', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100Holds100,
                    '2' => $activeAccountSum100Holds100,
                    '3' => $activeAccountSum100Holds100,
                    '4' => $activeAccountSum100Holds100,
                    '5' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: GEL. Balance: Covered with exceed amount. No holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                    '4' => new Money('5.55', 'GEL'),
                ],
                'neededAmount' => new Money('305.55', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                    '4' => $activeAccountSum100,
                    '5' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Covered without exceed amount. Partially with holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: GEL. Balance: Covered without exceed amount. Fully with holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100Holds100,
                    '2' => $activeAccountSum100Holds100,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: GEL. Balance: Covered without exceed amount. No holds.' => [
                'expected' => [
                    '1' => new Money('100', 'GEL'),
                    '2' => new Money('100', 'GEL'),
                    '3' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Not covered. Partially with holds.' => [
                'expected' => [
                    '1' => new Money('400', 'GEL'),
                    '2' => new Money('400', 'GEL'),
                    '3' => new Money('400', 'GEL'),
                ],
                'neededAmount' => new Money('400', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: GEL. Balance: Not covered. Fully with holds.' => [
                'expected' => [
                    '1' => new Money('400', 'GEL'),
                    '2' => new Money('400', 'GEL'),
                    '3' => new Money('400', 'GEL'),
                ],
                'neededAmount' => new Money('400', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100Holds100,
                    '2' => $activeAccountSum100Holds100,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: GEL. Balance: Not covered. No holds.' => [
                'expected' => [
                    '1' => new Money('400', 'GEL'),
                    '2' => new Money('400', 'GEL'),
                    '3' => new Money('400', 'GEL'),
                ],
                'neededAmount' => new Money('400', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: GEL. Balance: Zero. No holds.' => [
                'expected' => [
                    '1' => new Money('300', 'GEL'),
                    '2' => new Money('300', 'GEL'),
                    '3' => new Money('300', 'GEL'),
                ],
                'neededAmount' => new Money('300', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountNoMoney,
                    '2' => $activeAccountNoMoney,
                    '3' => $activeAccountNoMoney,
                ],
            ],
            'Amount: EUR. Balance: Covered without exceed amount. Partially with holds.' => [
                'expected' => [
                    '1' => new Money('50', 'EUR'),
                    '2' => new Money('50', 'EUR'),
                    '3' => new Money('50', 'EUR'),
                ],
                'neededAmount' => new Money('150', 'EUR'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: EUR. Balance: Covered with exceed amount. Fully with holds.' => [
                'expected' => [
                    '1' => new Money('50', 'EUR'),
                    '2' => new Money('50', 'EUR'),
                    '3' => new Money('50', 'EUR'),
                    '4' => new Money('5.50', 'EUR'),
                ],
                'neededAmount' => new Money('155.50', 'EUR'),
                'accountsData' => [
                    '1' => $activeAccountSum100Holds100,
                    '2' => $activeAccountSum100Holds100,
                    '3' => $activeAccountSum100Holds100,
                    '4' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: EUR. Balance: Not covered. No holds.' => [
                'expected' => [
                    '1' => new Money('400', 'EUR'),
                    '2' => new Money('400', 'EUR'),
                    '3' => new Money('400', 'EUR'),
                ],
                'neededAmount' => new Money('400', 'EUR'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: Zero. Balance: Covered with exceed amount. No holds.' => [
                'expected' => [],
                'neededAmount' => new Money('0', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: null. Balance: Not covered. Partially with holds.' => [
                'expected' => [
                    '1' => null,
                    '2' => null,
                    '3' => null,
                ],
                'neededAmount' => null,
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100Holds50,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: null. Balance: Not covered. Fully with holds.' => [
                'expected' => [
                    '1' => null,
                    '2' => null,
                    '3' => null,
                ],
                'neededAmount' => null,
                'accountsData' => [
                    '1' => $activeAccountSum100Holds100,
                    '2' => $activeAccountSum100Holds100,
                    '3' => $activeAccountSum100Holds100,
                ],
            ],
            'Amount: null. Balance: Not covered. No holds.' => [
                'expected' => [
                    '1' => null,
                    '2' => null,
                    '3' => null,
                ],
                'neededAmount' => null,
                'accountsData' => [
                    '1' => $activeAccountSum100,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountSum100,
                ],
            ],
            'Amount: null. Balance: Zero. No holds.' => [
                'expected' => [
                    '1' => null,
                    '2' => null,
                    '3' => null,
                ],
                'neededAmount' => null,
                'accountsData' => [
                    '1' => $activeAccountNoMoney,
                    '2' => $activeAccountNoMoney,
                    '3' => $activeAccountNoMoney,
                ],
            ],
            'Amount: zero. Balance: Zero. No holds.' => [
                'expected' => [],
                'neededAmount' => new Money('0', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountNoMoney,
                    '2' => $activeAccountNoMoney,
                    '3' => $activeAccountNoMoney,
                ],
            ],
            'Amount: 100. Balance 100, one account is zero.' => [
                'expected' => [
                    '2' => new Money('100', 'GEL'),
                ],
                'neededAmount' => new Money('100', 'GEL'),
                'accountsData' => [
                    '1' => $activeAccountNoMoney,
                    '2' => $activeAccountSum100,
                    '3' => $activeAccountNoMoney,
                ],
            ],
        ];
    }
}
