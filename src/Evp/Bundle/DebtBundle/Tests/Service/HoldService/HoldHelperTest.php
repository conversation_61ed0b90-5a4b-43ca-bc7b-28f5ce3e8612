<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\CurrencyBundle\Service\CurrencyConverterInterface;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientCollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientSeizureRepository;
use Evp\Bundle\DebtBundle\Repository\SeizureRepository;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class HoldHelperTest extends PersistableWebTestCase
{
    private HoldHelper $holdHelper;
    private EntityManager $entityManager;
    private CollectionRepository $collectionRepository;
    private SeizureRepository $seizureRepository;
    private DebtClientCollectionRepository $debtClientCollectionRepository;
    private DebtClientSeizureRepository $debtClientSeizureRepository;
    private HoldRepository $holdRepository;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->any())
            ->method('convert')
            ->willReturnCallback(function ($amount, $toCorrency) {
                $rates = [
                    'GEL' => ['GEL' => 1, 'EUR' => 0.5],
                    'EUR' => ['GEL' => 2, 'EUR' => 1],
                ];

                $result = new Money($amount->getAmount(), $toCorrency);
                return $result->mul($rates[$amount->getCurrency()][$toCorrency]);
            })
        ;
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', $currencyConverter);

        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->seizureRepository = $this->getContainer()->get('evp_debt.repository.seizure');
        $this->debtClientCollectionRepository = $this->getContainer()->get('evp_debt.repository.debt_client_collection');
        $this->debtClientSeizureRepository = $this->getContainer()->get('evp_debt.repository.debt_client_seizure');
        $this->holdRepository = $this->getContainer()->get('evp_bank_hold.repository.hold');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param string[] $expectedHolds
     * @param array $holdsData
     * @dataProvider filterActiveHoldsDataProvider
     */
    public function testFilterActiveHolds(array $expectedHolds, array $holdsData): void
    {
        $account = $this->fixturesHelper->createAccount();
        $amount = new Money(1, 'EUR');
        $holds = [];

        foreach ($holdsData as $item) {
            $holds[] = $this->fixturesHelper->createHold($account, $amount, $item['status'], $item['number']);
        }

        $result = $this->holdHelper->filterActiveHolds($holds);
        $resultNumbers = array_map(fn(Hold $hold) => $hold->getDetails(), $result);

        $this->assertEquals($expectedHolds, $resultNumbers);
    }

    public function filterActiveHoldsDataProvider(): array
    {
        return [
            'Empty' => [
                'expectedHolds' => [],
                'holdsData' => [],
            ],
            'One active' => [
                'expectedHolds' => ['1'],
                'holdsData' => [
                    ['number' => '1', 'status' => Hold::STATUS_NEW],
                ],
            ],
            'All active' => [
                'expectedHolds' => ['1', '2', '3'],
                'holdsData' => [
                    ['number' => '1', 'status' => Hold::STATUS_NEW],
                    ['number' => '2', 'status' => Hold::STATUS_WAITING_FUNDS],
                    ['number' => '3', 'status' => Hold::STATUS_HOLD],
                ],
            ],
            'One inactive' => [
                'expectedHolds' => [],
                'holdsData' => [
                    ['number' => '1', 'status' => Hold::STATUS_DONE],
                ],
            ],
            'All types' => [
                'expectedHolds' => ['1', '2', '3'],
                'holdsData' => [
                    ['number' => '1', 'status' => Hold::STATUS_NEW],
                    ['number' => '2', 'status' => Hold::STATUS_WAITING_FUNDS],
                    ['number' => '3', 'status' => Hold::STATUS_HOLD],
                    ['number' => '4', 'status' => Hold::STATUS_DONE],
                ],
            ],
        ];
    }

    /** @dataProvider isAmountEqualDataProvider */
    public function testIsAmountEqual(bool $expected, ?Money $holdAmount, ?Money $neededAmount): void
    {
        $account = $this->fixturesHelper->createAccount();
        $hold = $this->fixturesHelper->createHold($account, $holdAmount);

        $result = $this->holdHelper->isAmountEqual($hold, $neededAmount);
        $this->assertEquals($expected, $result);
    }

    public function isAmountEqualDataProvider(): array
    {
        return [
            'Case 1' => [
                'expected' => true,
                'holdAmount' => new Money(1, 'EUR'),
                'neededAmount' => new Money(1, 'EUR'),
            ],
            'Case 2' => [
                'expected' => false,
                'holdAmount' => new Money(2, 'EUR'),
                'neededAmount' => new Money(1, 'EUR'),
            ],
            'Case 3' => [
                'expected' => true,
                'holdAmount' => new Money(100, 'EUR'),
                'neededAmount' => new Money(200, 'GEL'),
            ],
            'Case 4' => [
                'expected' => true,
                'holdAmount' => new Money(200, 'GEL'),
                'neededAmount' => new Money(100, 'EUR'),
            ],
            'Case 5' => [
                'expected' => true,
                'holdAmount' => new Money(200, 'GEL'),
                'neededAmount' => new Money(200, 'GEL'),
            ],
            'Case 6' => [
                'expected' => false,
                'holdAmount' => new Money(100, 'EUR'),
                'neededAmount' => new Money(100, 'GEL'),
            ],
            'Case 7' => [
                'expected' => true,
                'holdAmount' => null,
                'neededAmount' => null,
            ],
            'Case 8' => [
                'expected' => false,
                'holdAmount' => new Money(1, 'EUR'),
                'neededAmount' => null,
            ],
            'Case 9' => [
                'expected' => false,
                'holdAmount' => null,
                'neededAmount' => new Money(1, 'EUR'),
            ],
        ];
    }

    /** @dataProvider createDetailsByCollectionsDataProvider */
    public function testCreateDetailsByCollections(string $expectedDetails, array $collectionNumbers): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClient = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        foreach ($collectionNumbers as $number) {
            $this->fixturesHelper->createDebtCollectionGrs($number, $debtClient, $licensedPartner);
        }

        $this->entityManager->flush();

        $collections = $this->collectionRepository->findAllActiveByClientAndWriteOffStatuses($client, null);
        $result = $this->holdHelper->createDetailsByCollections($collections, $client);

        $this->assertEquals($expectedDetails, $result);
    }

    public function createDetailsByCollectionsDataProvider(): array
    {
        return [
            'Case 1' => [
                'expectedDetails' => 'Restricted amount due to collection order: ',
                'collectionNumbers' => [],
            ],
            'Case 2' => [
                'expectedDetails' => 'Restricted amount due to collection order: *********',
                'collectionNumbers' => ['*********'],
            ],
            'Case 3' => [
                'expectedDetails' => 'Restricted amount due to collection order: *********, *********, *********',
                'collectionNumbers' => ['*********', '*********', '*********'],
            ],
        ];
    }

    /** @dataProvider createDetailsBySeizuresDataProvider */
    public function testCreateDetailsBySeizures(string $expectedDetails, array $seizureNumbers): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        foreach ($seizureNumbers as $number) {
            $this->fixturesHelper->createDebtSeizureGrs($number, $debtClientSeizure, $licensedPartner);
        }

        $this->entityManager->flush();

        $seizures = $this->seizureRepository->findAllActiveByClient($client);
        $result = $this->holdHelper->createDetailsBySeizures($seizures, $client);

        $this->assertEquals($expectedDetails, $result);
    }

    public function createDetailsBySeizuresDataProvider(): array
    {
        return [
            'Case 1' => [
                'expectedDetails' => 'Restricted amount due to seizure order: ',
                'seizureNumbers' => [],
            ],
            'Case 2' => [
                'expectedDetails' => 'Restricted amount due to seizure order: *********',
                'seizureNumbers' => ['*********'],
            ],
            'Case 3' => [
                'expectedDetails' => 'Restricted amount due to seizure order: *********, *********, *********',
                'seizureNumbers' => ['*********', '*********', '*********'],
            ],
        ];
    }

    /** @dataProvider releaseHoldDataProvider */
    public function testReleaseHold(string $expectedStatus, string $holdStatus): void
    {
        $account = $this->fixturesHelper->createAccount();

        $hold = $this->fixturesHelper->createHold($account, new Money(1, 'EUR'), $holdStatus);
        $this->entityManager->flush();

        $this->holdHelper->releaseHold($hold);
        $this->entityManager->flush();

        $hold = $this->holdRepository->findOneById($hold->getId());
        $this->assertEquals($expectedStatus, $hold->getStatus());
    }

    public function releaseHoldDataProvider(): array
    {
        return [
            'Case 1' => [
                'expectedStatus' => Hold::STATUS_DONE,
                'holdStatus' => Hold::STATUS_NEW,
            ],
            'Case 2' => [
                'expectedStatus' => Hold::STATUS_DONE,
                'holdStatus' => Hold::STATUS_WAITING_FUNDS,
            ],
            'Case 3' => [
                'expectedStatus' => Hold::STATUS_DONE,
                'holdStatus' => Hold::STATUS_HOLD,
            ],
            'Case 4' => [
                'expectedStatus' => Hold::STATUS_DONE,
                'holdStatus' => Hold::STATUS_DONE,
            ],
        ];
    }

    /**
     * @param string[] $expectedHolds
     * @param int $accountsCount
     * @param string[] $holds
     * @dataProvider releaseAllActiveHoldsDataProvider
     */
    public function testReleaseAllActiveDebtClientCollectionHolds(array $expectedHolds, int $accountsCount, array $holds): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $holdAmount = new Money(1, 'EUR');

        for ($i = 1; $i <= $accountsCount; $i++) {
            $account = $this->fixturesHelper->createAccount($client, (string) $i, (string) $i);

            if (isset($holds[$i])) {
                $hold = $this->fixturesHelper->createHold($account, $holdAmount, $holds[$i], (string) $i);
                $debtClientCollection->addHold($hold);
            }
        }

        $this->entityManager->flush();
        $this->holdHelper->releaseAllActiveDebtClientHolds($debtClientCollection);
        $this->entityManager->flush();

        $debtClientCollection = $this->debtClientCollectionRepository->findOneByClient($client);
        $holdsDetailsAndStatus = [];
        foreach ($debtClientCollection->getHolds() as $hold) {
            $holdsDetailsAndStatus[$hold->getDetails()] = $hold->getStatus();
        }

        $this->assertEquals($expectedHolds, $holdsDetailsAndStatus);
    }

    /**
     * @param string[] $expectedHolds
     * @param int $accountsCount
     * @param string[] $holds
     * @dataProvider releaseAllActiveHoldsDataProvider
     */
    public function testReleaseAllActiveDebtClientSeizuresHolds(array $expectedHolds, int $accountsCount, array $holds): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $holdAmount = new Money(1, 'EUR');

        for ($i = 1; $i <= $accountsCount; $i++) {
            $account = $this->fixturesHelper->createAccount($client, (string) $i, (string) $i);

            if (isset($holds[$i])) {
                $hold = $this->fixturesHelper->createHold($account, $holdAmount, $holds[$i], (string) $i);
                $debtClientSeizure->addHold($hold);
            }
        }

        $this->entityManager->flush();
        $this->holdHelper->releaseAllActiveDebtClientHolds($debtClientSeizure);
        $this->entityManager->flush();

        $debtClientSeizure = $this->debtClientSeizureRepository->findOneByClient($client);
        $holdsDetailsAndStatus = [];
        foreach ($debtClientSeizure->getHolds() as $hold) {
            $holdsDetailsAndStatus[$hold->getDetails()] = $hold->getStatus();
        }

        $this->assertEquals($expectedHolds, $holdsDetailsAndStatus);
    }

    public function releaseAllActiveHoldsDataProvider(): array
    {
        return [
            'Case 1' => [
                'expectedHolds' => [
                    '1' => Hold::STATUS_DONE,
                    '2' => Hold::STATUS_DONE,
                    '3' => Hold::STATUS_DONE,
                    '4' => Hold::STATUS_DONE,
                ],
                'accountsCount' => 4,
                'holds' => [
                    '1' => Hold::STATUS_NEW,
                    '2' => Hold::STATUS_WAITING_FUNDS,
                    '3' => Hold::STATUS_HOLD,
                    '4' => Hold::STATUS_DONE,
                ],
            ],
            'Case 2' => [
                'expectedHolds' => [],
                'accountsCount' => 4,
                'holds' => [],
            ],
            'Case 3' => [
                'expectedHolds' => [
                    '1' => Hold::STATUS_DONE,
                    '2' => Hold::STATUS_DONE,
                ],
                'accountsCount' => 4,
                'holds' => [
                    '1' => Hold::STATUS_DONE,
                    '2' => Hold::STATUS_DONE,
                ],
            ],
        ];
    }
}
