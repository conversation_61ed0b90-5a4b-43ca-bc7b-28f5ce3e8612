<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\CurrencyBundle\Service\CurrencyConverterInterface;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Entity\DebtClientCollection;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Entity\SeizureGeb;
use Evp\Bundle\DebtBundle\Entity\SeizureGrs;
use Evp\Bundle\DebtBundle\Service\DebtSorter;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldAmountProvider;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldAmountService;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class HoldAmountProviderTest extends TestCase
{
    private const DEFAULT_CURRENCY = 'GEL';

    private MockObject $holdAmountService;
    private MockObject $debtSorter;
    private MockObject $currencyConverter;
    private MockObject $logger;

    private HoldAmountProvider $holdAmountProvider;

    protected function setUp(): void
    {
        $this->holdAmountService = $this->createMock(HoldAmountService::class);
        $this->debtSorter = $this->createMock(DebtSorter::class);
        $this->currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->holdAmountProvider = new HoldAmountProvider(
            $this->createMock(HoldHelper::class),
            $this->holdAmountService,
            $this->debtSorter,
            $this->currencyConverter,
            $this->logger,
            self::DEFAULT_CURRENCY
        );
    }

    /** @dataProvider getHoldAmountForCollectionDataProvider */
    public function testGetHoldAmountForCollection(
        array $collectionData,
        Money $totalHoldAmount,
        array $sortedCollectionsData,
        array $expectedAmountsToConvert,
        Money $expected,
        array $convertedAmounts = null
    ): void {
        $client = new ClientNatural();
        $debtClient = (new DebtClientCollection())->setClient($client);

        $currency = $collectionData['currency'] ?? 'GEL';
        $amount = isset($collectionData['amount']) ? new Money($collectionData['amount'], $currency) : null;

        $collection = (new CollectionGeb())
            ->setClient($client)
            ->setDebtClient($debtClient)
            ->setNumber($collectionData['number'])
            ->setAmountMoney($amount)
            ->setAmountLeftMoney($amount)
            ->setStatus($collectionData['status'] ?? Collection::STATUS_ACTIVE)
            ->setWriteOffStatus($collectionData['writeOffStatus'] ?? null)
        ;

        $sortedCollections = [];
        foreach ($sortedCollectionsData as $number => $data) {
            $currency = $data['currency'] ?? 'GEL';
            $amount = $data['amount'] ? new Money($data['amount'], $currency) : null;
            $amountLeft = $amount;

            if (array_key_exists('amountLeft', $data)) {
                $amountLeft = $data['amountLeft'] ? new Money($data['amountLeft'], $currency) : null;
            }

            $sortedCollection = $data['type'] === Collection::TYPE_GEB ? new CollectionGeb() : new CollectionGrs();
            $sortedCollections[] = $sortedCollection
                ->setClient($client)
                ->setDebtClient($debtClient)
                ->setNumber((string) $number)
                ->setAmountMoney($amount)
                ->setAmountLeftMoney($amountLeft)
            ;
        }

        $this->holdAmountService
            ->method('countTotalHoldsHoldAmount')
            ->willReturn($totalHoldAmount)
        ;

        $this->debtSorter
            ->method('sortCollectionsByPriority')
            ->willReturn($sortedCollections)
        ;

        if ($convertedAmounts === null) {
            $convertedAmounts = array_map(fn(array $item) => $item[0], $expectedAmountsToConvert);
        }

        $this->currencyConverter
            ->method('convert')
            ->withConsecutive(...$expectedAmountsToConvert)
            ->willReturnOnConsecutiveCalls(...$convertedAmounts)
        ;

        $this->assertEquals($expected, $this->holdAmountProvider->getHoldAmountForCollection($collection));
    }

    public function getHoldAmountForCollectionDataProvider(): array
    {
        return [
            'Inactive collection' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'status' => Collection::STATUS_CANCELLED,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            'Collection with incorrect writeOff status ' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            'Collection without amount ' => [
                'collection' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            'Collection with zero amount ' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 0,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            '1 full held collection GEL' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(100, 'GEL'),
            ],
            '1 partially held collection EUR' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'currency' => 'EUR',
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100, 'currency' => 'EUR'],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'EUR'), 'GEL'],
                    [new Money(100, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(50, 'EUR'),
                'convertedAmounts' => [
                    new Money(200, 'GEL'),
                    new Money(50, 'EUR'),
                ],
            ],
            '1 non held collection EUR' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(0, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(0, 'EUR'),
            ],
            '2 partially held different collections GEL' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(200, 'GEL'),
                'sortedCollections' => [
                    '4321' => ['type' => Collection::TYPE_GRS, 'amount' => 150],
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(150, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(50, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(50, 'GEL'),
            ],
            '2 partially held different collections GEL with different amount left' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(200, 'GEL'),
                'sortedCollections' => [
                    '4321' => ['type' => Collection::TYPE_GRS, 'amount' => 150, 'amountLeft' => 120],
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(120, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(80, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(80, 'GEL'),
            ],
            'Null amount more prioritised collection' => [
                'collection' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(10_000, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GRS, 'amount' => null],
                    '4321' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(10_000, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(10_000, 'GEL'),
            ],
            'Null amount in the middle with 0' => [
                'collection' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(180, 'GEL'),
                'sortedCollections' => [
                    '4321' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Collection::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(0, 'GEL'),
            ],
            'Null amount in the middle with money' => [
                'collection' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(10_220, 'GEL'),
                'sortedCollections' => [
                    '4321' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Collection::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(10_020, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(10_020, 'GEL'),
            ],
            'Null amount in the middle with money, next collection without amount' => [
                'collection' => [
                    'number' => '4323',
                ],
                'totalHoldAmount' => new Money(10_220, 'GEL'),
                'sortedCollections' => [
                    '4321' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Collection::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(10_020, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(0, 'GEL'),
            ],
            'Collection with zero amount waiting write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 0,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            '1 full held collection GEL waiting write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(100, 'GEL'),
            ],
            '1 partially held collection EUR waiting write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'currency' => 'EUR',
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100, 'currency' => 'EUR'],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'EUR'), 'GEL'],
                    [new Money(100, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(50, 'EUR'),
                'convertedAmounts' => [
                    new Money(200, 'GEL'),
                    new Money(50, 'EUR'),
                ],
            ],
            '1 non held collection EUR waiting write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(0, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(0, 'EUR'),
            ],
            'Collection with zero amount failed write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 0,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            '1 full held collection GEL failed write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(100, 'GEL'),
            ],
            '1 partially held collection EUR failed write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'currency' => 'EUR',
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100, 'currency' => 'EUR'],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'EUR'), 'GEL'],
                    [new Money(100, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(50, 'EUR'),
                'convertedAmounts' => [
                    new Money(200, 'GEL'),
                    new Money(50, 'EUR'),
                ],
            ],
            '1 non held collection EUR failed write off' => [
                'collection' => [
                    'number' => '1234',
                    'amount' => 100,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedCollections' => [
                    '1234' => ['type' => Collection::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(0, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(0, 'EUR'),
            ],
        ];
    }

    /**
     * @dataProvider getHoldAmountForCollectionFailureDataProvider
     * @param Collection $collection
     * @param Collection[] $preparedCollections
     * @return void
     */
    public function testGetHoldAmountForCollectionFailure(Collection $collection, array $preparedCollections): void
    {
        $this->debtSorter
            ->method('sortCollectionsByPriority')
            ->willReturn($preparedCollections)
        ;

        $this->holdAmountService
            ->method('countTotalHoldsHoldAmount')
            ->willReturn(new Money(100, 'GEL'))
        ;

        $this->currencyConverter
            ->method('convert')
            ->willReturn(new Money(0, 'GEL'))
        ;

        $this->logger
            ->expects($this->once())
            ->method('error')
        ;

        $this->holdAmountProvider->getHoldAmountForCollection($collection);
    }

    public function getHoldAmountForCollectionFailureDataProvider(): array
    {
        $client = new ClientNatural();
        $debtClient = (new DebtClientCollection())->setClient($client);

        return [
            'No collections found' => [
                'collection' => (new CollectionGeb())
                    ->setId(1)
                    ->setClient($client)
                    ->setDebtClient($debtClient)
                    ->setAmountMoney(new Money(100, 'GEL'))
                    ->setAmountLeftMoney(new Money(100, 'GEL'))
                    ->setStatus(Collection::STATUS_ACTIVE)
                    ->setWriteOffStatus(null)
                    ->setNumber('12345'),
                'preparedCollections' => [],
            ],
            'No related collection found' => [
                'collection' => (new CollectionGeb())
                    ->setId(1)
                    ->setClient($client)
                    ->setDebtClient($debtClient)
                    ->setAmountMoney(new Money(100, 'GEL'))
                    ->setAmountLeftMoney(new Money(100, 'GEL'))
                    ->setStatus(Collection::STATUS_ACTIVE)
                    ->setWriteOffStatus(null)
                    ->setNumber('12345'),
                'preparedCollections' => [
                    (new CollectionGrs())
                        ->setClient($client)
                        ->setDebtClient($debtClient)
                        ->setAmountMoney(new Money(150, 'GEL'))
                        ->setAmountLeftMoney(new Money(150, 'GEL'))
                        ->setNumber('4321'),
                    (new CollectionGeb())
                        ->setClient($client)
                        ->setDebtClient($debtClient)
                        ->setAmountMoney(new Money(100, 'GEL'))
                        ->setAmountLeftMoney(new Money(100, 'GEL'))
                        ->setNumber('1234'),
                ],
            ],
        ];
    }

    /** @dataProvider getHoldAmountForSeizureDataProvider */
    public function testGetHoldAmountForSeizure(
        array $seizuresData,
        Money $totalHoldAmount,
        array $sortedSeizuresData,
        array $expectedAmountsToConvert,
        Money $expected,
        array $convertedAmounts = null
    ): void {
        $client = new ClientNatural();
        $debtClient = (new DebtClientSeizure())->setClient($client);

        $currency = $seizuresData['currency'] ?? 'GEL';
        $amount = isset($seizuresData['amount']) ? new Money($seizuresData['amount'], $currency) : null;

        $seizure = (new SeizureGeb())
            ->setClient($client)
            ->setDebtClient($debtClient)
            ->setNumber($seizuresData['number'])
            ->setAmountMoney($amount)
            ->setStatus($seizuresData['status'] ?? Seizure::STATUS_ACTIVE)
        ;

        $sortedSeizures = [];
        foreach ($sortedSeizuresData as $number => $data) {
            $currency = $data['currency'] ?? 'GEL';
            $amount = $data['amount'] ? new Money($data['amount'], $currency) : null;

            $sortedSeizure = $data['type'] === Seizure::TYPE_GEB ? new SeizureGeb() : new SeizureGrs();
            $sortedSeizures[] = $sortedSeizure
                ->setClient($client)
                ->setDebtClient($debtClient)
                ->setNumber((string) $number)
                ->setAmountMoney($amount)
            ;
        }

        $this->holdAmountService
            ->method('countTotalHoldsHoldAmount')
            ->willReturn($totalHoldAmount)
        ;

        $this->debtSorter
            ->method('sortSeizuresByPriority')
            ->willReturn($sortedSeizures)
        ;

        if ($convertedAmounts === null) {
            $convertedAmounts = array_map(fn(array $item) => $item[0], $expectedAmountsToConvert);
        }

        $this->currencyConverter
            ->method('convert')
            ->withConsecutive(...$expectedAmountsToConvert)
            ->willReturnOnConsecutiveCalls(...$convertedAmounts)
        ;

        $this->assertEquals($expected, $this->holdAmountProvider->getHoldAmountForSeizure($seizure));
    }

    public function getHoldAmountForSeizureDataProvider(): array
    {
        return [
            'Inactive seizure' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 100,
                    'status' => Seizure::STATUS_CANCELLED,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedSeizures' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            'Seizure without amount ' => [
                'seizure' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedSeizures' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            'Seizure with zero amount ' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 0,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedSeizures' => [],
                'expectedAmountToConvert' => [],
                'expected' => new Money(0, 'GEL'),
            ],
            '1 full held seizure GEL' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedSeizures' => [
                    '1234' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(100, 'GEL'),
            ],
            '1 partially held seizure EUR' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 100,
                    'currency' => 'EUR',
                ],
                'totalHoldAmount' => new Money(100, 'GEL'),
                'sortedSeizures' => [
                    '1234' => ['type' => Seizure::TYPE_GEB, 'amount' => 100, 'currency' => 'EUR'],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'EUR'), 'GEL'],
                    [new Money(100, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(50, 'EUR'),
                'convertedAmounts' => [
                    new Money(200, 'GEL'),
                    new Money(50, 'EUR'),
                ],
            ],
            '1 non held seizure EUR' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(0, 'GEL'),
                'sortedSeizures' => [
                    '1234' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(0, 'GEL'), 'EUR'],
                ],
                'expected' => new Money(0, 'EUR'),
            ],
            '2 partially held different seizures GEL' => [
                'seizure' => [
                    'number' => '1234',
                    'amount' => 100,
                ],
                'totalHoldAmount' => new Money(200, 'GEL'),
                'sortedSeizures' => [
                    '4321' => ['type' => Seizure::TYPE_GRS, 'amount' => 150],
                    '1234' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(150, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(50, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(50, 'GEL'),
            ],
            'Null amount more prioritised seizure' => [
                'seizure' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(10_000, 'GEL'),
                'sortedSeizures' => [
                    '1234' => ['type' => Seizure::TYPE_GRS, 'amount' => null],
                    '4321' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(10_000, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(10_000, 'GEL'),
            ],
            'Null amount in the middle with 0' => [
                'seizure' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(180, 'GEL'),
                'sortedSeizures' => [
                    '4321' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Seizure::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(0, 'GEL'),
            ],
            'Null amount in the middle with money' => [
                'seizure' => [
                    'number' => '1234',
                ],
                'totalHoldAmount' => new Money(10_220, 'GEL'),
                'sortedSeizures' => [
                    '4321' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Seizure::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(10_020, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(10_020, 'GEL'),
            ],
            'Null amount in the middle with money, next seizure without amount' => [
                'seizure' => [
                    'number' => '4323',
                ],
                'totalHoldAmount' => new Money(10_220, 'GEL'),
                'sortedSeizures' => [
                    '4321' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '4322' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                    '1234' => ['type' => Seizure::TYPE_GRS, 'amount' => null],
                    '4323' => ['type' => Seizure::TYPE_GEB, 'amount' => 100],
                ],
                'expectedAmountToConvert' => [
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(100, 'GEL'), 'GEL'],
                    [new Money(10_020, 'GEL'), 'GEL'],
                ],
                'expected' => new Money(0, 'GEL'),
            ],
        ];
    }

    /**
     * @dataProvider getHoldAmountForSeizureFailureDataProvider
     * @param Seizure $seizure
     * @param Seizure[] $preparedSeizures
     * @return void
     */
    public function testGetHoldAmountForSeizureFailure(Seizure $seizure, array $preparedSeizures): void
    {
        $this->debtSorter
            ->method('sortSeizuresByPriority')
            ->willReturn($preparedSeizures)
        ;

        $this->holdAmountService
            ->method('countTotalHoldsHoldAmount')
            ->willReturn(new Money(100, 'GEL'))
        ;

        $this->currencyConverter
            ->method('convert')
            ->willReturn(new Money(0, 'GEL'))
        ;

        $this->logger
            ->expects($this->once())
            ->method('error')
        ;

        $this->holdAmountProvider->getHoldAmountForSeizure($seizure);
    }

    public function getHoldAmountForSeizureFailureDataProvider(): array
    {
        $client = new ClientNatural();
        $debtClient = (new DebtClientSeizure())->setClient($client);

        return [
            'No seizures found' => [
                'seizure' => (new SeizureGeb())
                    ->setId(1)
                    ->setClient($client)
                    ->setDebtClient($debtClient)
                    ->setAmountMoney(new Money(100, 'GEL'))
                    ->setStatus(Seizure::STATUS_ACTIVE)
                    ->setNumber('12345'),
                'preparedSeizures' => [],
            ],
            'No related seizure found' => [
                'seizure' => (new SeizureGeb())
                    ->setId(1)
                    ->setClient($client)
                    ->setDebtClient($debtClient)
                    ->setAmountMoney(new Money(100, 'GEL'))
                    ->setStatus(Seizure::STATUS_ACTIVE)
                    ->setNumber('12345'),
                'preparedSeizures' => [
                    (new SeizureGrs())
                        ->setClient($client)
                        ->setDebtClient($debtClient)
                        ->setAmountMoney(new Money(150, 'GEL'))
                        ->setNumber('4321'),
                    (new SeizureGeb())
                        ->setClient($client)
                        ->setDebtClient($debtClient)
                        ->setAmountMoney(new Money(100, 'GEL'))
                        ->setNumber('1234'),
                ],
            ],
        ];
    }
}
