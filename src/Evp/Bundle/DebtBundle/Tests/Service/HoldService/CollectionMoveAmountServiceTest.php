<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\HoldService\CollectionMoveAmountService;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class CollectionMoveAmountServiceTest extends PersistableWebTestCase
{
    private CollectionMoveAmountService $collectionMoveAmountService;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private HoldHelper $holdHelper;
    private CollectionRepository $collectionRepository;
    private FixturesHelper $fixturesHelper;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', new HardcodedTestCurrencyConverter());

        $this->collectionMoveAmountService = $this->getContainer()->get('evp_debt.service.hold_service.collection_move_amount');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /** @dataProvider moveAmountFromCommonHoldsToCollectionDataProvider */
    public function testMoveAmountFromCommonHoldsToCollection(
        Money $expectedMovedAmount,
        array $expectedCommonHolds,
        array $expectedCollectionHolds,
        ?Money $commonHoldsAmount,
        ?Money $collectionAmount,
        array $commonHolds
    ): void {
        // Prepare data in database
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper
            ->createDebtCollectionGrs('1', $debtClientCollection, $licensedPartner)
            ->setAmountMoney($collectionAmount)
            ->setAmountLeftMoney($collectionAmount)
        ;

        foreach ($commonHolds as $key => $commonHold) {
            $account = $this->fixturesHelper->createAccount($client, (string) $key, (string) $key);
            $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);

            if ($commonHold['holdAmount'] !== null) {
                $this->accountManager->fill($commonHold['holdAmount'], $account);
            }

            $this->entityManager->flush();

            $hold = $this->holdHelper->createHold($account, $commonHoldsAmount, 'Hold details');
            $debtClientCollection->addHold($hold);
        }

        $this->entityManager->flush();

        // Get results
        $movedAmount = $this->collectionMoveAmountService->moveAmountFromCommonHoldsToCollection($collection);
        $this->entityManager->flush();

        /** @var Collection $collection */
        $collection = $this->collectionRepository->find($collection->getId());

        // Check moved amount
        $this->assertEquals($expectedMovedAmount, $movedAmount, 'Moved amount is not correct');

        // Check common hods
        $expectedCommonHolds = array_map(
            fn(array $item) => ['amount' => $item['amount'] ?? $commonHoldsAmount, 'holdAmount' => $item['holdAmount']],
            $expectedCommonHolds
        );

        $commonHoldsFromDb = $this->holdHelper->filterActiveHolds($collection->getDebtClient()->getHolds()->toArray());
        $commonHoldsFromDbArray = [];
        foreach ($commonHoldsFromDb as $hold) {
            $commonHoldsFromDbArray[$hold->getAccount()->getNumber()] = [
                'amount' => $hold->getAmountMoney(),
                'holdAmount' => $hold->getHoldAmountMoney(),
            ];
        }

        $this->assertEquals($expectedCommonHolds, $commonHoldsFromDbArray, 'Common holds are not correct');

        // Check collection holds
        $expectedCollectionHolds = array_map(
            fn(array $item) => ['amount' => $item['amount'] ?: $item['holdAmount'], 'holdAmount' => $item['holdAmount']],
            $expectedCollectionHolds
        );

        $collectionHolds = $this->holdHelper->filterActiveHolds($collection->getHolds()->toArray());
        $collectionHoldsArray = [];
        foreach ($collectionHolds as $hold) {
            $collectionHoldsArray[$hold->getAccount()->getNumber()] = [
                'amount' => $hold->getAmountMoney(),
                'holdAmount' => $hold->getHoldAmountMoney(),
            ];
        }

        $this->assertEquals($expectedCollectionHolds, $collectionHoldsArray, 'Collection holds are not correct');
    }

    public function moveAmountFromCommonHoldsToCollectionDataProvider(): array
    {
        $gel = [
            '0' => new Money(0, 'GEL'),
            '100' => new Money(100, 'GEL'),
            '200' => new Money(200, 'GEL'),
            '300' => new Money(300, 'GEL'),
            '400' => new Money(400, 'GEL'),
            '500' => new Money(500, 'GEL'),
            '600' => new Money(600, 'GEL'),
            '700' => new Money(700, 'GEL'),
            '800' => new Money(800, 'GEL'),
        ];
        $gelConv = [ // On test data after conversion 1 GEL is not 1 GEL
            '100' => new Money('99.99', 'GEL'),
            '200' => new Money('200.01', 'GEL'),
        ];
        $gelInEur = [
            '100' => new Money('27.64', 'EUR'),
            '200' => new Money('55.29', 'EUR'),
            '300' => new Money('82.93', 'EUR'),
            '400' => new Money('110.57', 'EUR'),
        ];

        return [
            'Case 1. Holds amount not null | Collection amount not null | Common holds: no amount and no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 2. Holds amount not null | Collection amount not null | Common holds: no amount and lower' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 3. Holds amount not null | Collection amount not null | Common holds: no amount and equal' => [
                'expectedMovedAmount' => $gel['200'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['200']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 4. Holds amount not null | Collection amount not null | Common holds: no amount and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 5. Holds amount not null | Collection amount not null | Common holds: no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
            ],
            'Case 6. Holds amount not null | Collection amount not null | Common holds: lower and no amount' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedCommonHolds' => [
                    '2' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 7. Holds amount not null | Collection amount not null | Common holds: lower and lower' => [
                'expectedMovedAmount' => $gel['200'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 8. Holds amount not null | Collection amount not null | Common holds: lower and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 9. Holds amount not null | Collection amount not null | Common holds: lower and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['200']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
                'commonHoldsAmount' => $gel['500'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 10. Holds amount not null | Collection amount not null | Common holds: lower' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 11. Holds amount not null | Collection amount not null | Common holds: equal and no amount' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['400'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 12. Holds amount not null | Collection amount not null | Common holds: equal and lower' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 13. Holds amount not null | Collection amount not null | Common holds: equal and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['600'], 'holdAmount' => $gel['300']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['600'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 14. Holds amount not null | Collection amount not null | Common holds: equal and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['700'], 'holdAmount' => $gel['400']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['700'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 15. Holds amount not null | Collection amount not null | Common holds: equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['600'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 16. Holds amount not null | Collection amount not null | Common holds: more and no amount' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['400'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 17. Holds amount not null | Collection amount not null | Common holds: more and lower' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['500'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['500'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 18. Holds amount not null | Collection amount not null | Common holds: more and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['700'], 'holdAmount' => $gel['300']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['700'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 19. Holds amount not null | Collection amount not null | Common holds: more and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['500'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['800'], 'holdAmount' => $gel['400']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['800'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 20. Holds amount not null | Collection amount not null | Common holds: more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 21. Holds amount not null | Collection amount null | Common holds: no amount and no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 22. Holds amount not null | Collection amount null | Common holds: no amount and has amount' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['400'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 23. Holds amount not null | Collection amount null | Common holds: no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => $gel['300'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
            ],
            'Case 24. Holds amount not null | Collection amount null | Common holds: has amount and no amount' => [
                'expectedMovedAmount' => $gel['400'],
                'expectedCommonHolds' => [
                    '2' => ['amount' => $gel['400'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 25. Holds amount not null | Collection amount null | Common holds: has amount and has amount' => [
                'expectedMovedAmount' => $gel['500'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['400'], 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonHoldsAmount' => $gel['500'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 26. Holds amount not null | Collection amount null | Common holds: has amount' => [
                'expectedMovedAmount' => $gel['400'],
                'expectedCommonHolds' => [],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                ],
                'commonHoldsAmount' => $gel['400'],
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 27. Holds amount null | Collection amount not null | Common holds: no amount and no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['400'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 28. Holds amount null | Collection amount not null | Common holds: no amount and lower' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedCommonHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                    '2' => ['amount' => null, 'holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gelConv['100']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 29. Holds amount null | Collection amount not null | Common holds: no amount and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 30. Holds amount null | Collection amount not null | Common holds: no amount and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gelInEur['200']],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['500']],
                ],
            ],
            'Case 31. Holds amount null | Collection amount not null | Common holds: no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
            ],
            'Case 32. Holds amount null | Collection amount not null | Common holds: lower and no amount' => [
                'expectedMovedAmount' => $gelConv['200'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['200'], 'amount' => $gelConv['200']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 33. Holds amount null | Collection amount not null | Common holds: lower and lower' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['200'], 'amount' => $gelConv['200']],
                    '2' => ['holdAmount' => $gelConv['100']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['400'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 34. Holds amount null | Collection amount not null | Common holds: lower and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gelInEur['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                    '2' => ['holdAmount' => $gelConv['200']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 35. Holds amount null | Collection amount not null | Common holds: lower and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => new Money('55.28', 'EUR')],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                    '2' => ['holdAmount' => $gelConv['200']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 36. Holds amount null | Collection amount not null | Common holds: lower' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 37. Holds amount null | Collection amount not null | Common holds: equal and no amount' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 38. Holds amount null | Collection amount not null | Common holds: equal and lower' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gelInEur['200']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 39. Holds amount null | Collection amount not null | Common holds: equal and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gelInEur['300']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 40. Holds amount null | Collection amount not null | Common holds: equal and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gelInEur['400']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 41. Holds amount null | Collection amount not null | Common holds: equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 42. Holds amount null | Collection amount not null | Common holds: more and no amount' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => $gelInEur['100']],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 43. Holds amount null | Collection amount not null | Common holds: more and lower' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => $gelInEur['100']],
                    '2' => ['holdAmount' => $gelInEur['200']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 44. Holds amount null | Collection amount not null | Common holds: more and equal' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => $gelInEur['100']],
                    '2' => ['holdAmount' => $gelInEur['300']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 45. Holds amount null | Collection amount not null | Common holds: more and more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => $gelInEur['100']],
                    '2' => ['holdAmount' => $gelInEur['400']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                    '2' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 46. Holds amount null | Collection amount not null | Common holds: more' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => $gelInEur['100']],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['300']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => $gel['300'],
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['400']],
                ],
            ],
            'Case 47. Holds amount null | Collection amount null | Common holds: no amount and no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 48. Holds amount null | Collection amount null | Common holds: no amount and has amount' => [
                'expectedMovedAmount' => $gelConv['200'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '2' => ['holdAmount' => $gel['200'], 'amount' => $gelConv['200']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 49. Holds amount null | Collection amount null | Common holds: no amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
            ],
            'Case 50. Holds amount null | Collection amount null | Common holds: has amount and no amount' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => null],
                ],
            ],
            'Case 51. Holds amount null | Collection amount null | Common holds: has amount and has amount' => [
                'expectedMovedAmount' => $gel['300'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                    '2' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                    '2' => ['holdAmount' => $gel['200'], 'amount' => $gelConv['200']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 52. Holds amount null | Collection amount null | Common holds: has amount' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedCommonHolds' => [
                    '1' => ['holdAmount' => null],
                ],
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gelConv['100']],
                ],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 53. No common holds' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedCommonHolds' => [],
                'expectedCollectionHolds' => [],
                'commonHoldsAmount' => null,
                'collectionAmount' => null,
                'commonHolds' => [],
            ],
        ];
    }
}
