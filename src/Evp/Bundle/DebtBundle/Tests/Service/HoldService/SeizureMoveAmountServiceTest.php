<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\ClientBundle\Service\LicensedPartnersManager;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Exception\DebtBundleException;
use Evp\Bundle\DebtBundle\Repository\SeizureRepository;
use Evp\Bundle\DebtBundle\Service\HoldManager;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Bundle\DebtBundle\Service\HoldService\SeizureMoveAmountService;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use ReflectionClass;

class SeizureMoveAmountServiceTest extends PersistableWebTestCase
{
    private SeizureRepository $seizureRepository;
    private SeizureMoveAmountService $seizureMoveAmountService;
    private AccountManager $accountManager;
    private HoldManager $holdManager;
    private HoldHelper $holdHelper;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', new HardcodedTestCurrencyConverter());

        $this->seizureRepository = $this->getContainer()->get('evp_debt.repository.seizure');
        $this->seizureMoveAmountService = $this->getContainer()->get('evp_debt.service.hold_service.seizure_move_amount');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->holdManager = $this->getContainer()->get('evp_debt.service.hold_manager');
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /** @dataProvider countPossibleMoveAmountDataProvider */
    public function testCountPossibleMoveAmount(
        ?Money $expectedAmount,
        int $organizationPriority,
        ?Money $holdAmount,
        array $seizures,
        bool $expectException = false
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $account = $this->fixturesHelper->createAccount($client);
        $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);

        foreach ($seizures as $number => $seizure) {
            $number = (string) ($number + 1);
            $this->fixturesHelper
                ->createDebtSeizureGrs($number, $debtClientSeizure, $licensedPartner)
                ->setOrganizationPriority($seizure['priority'])
                ->setAmountMoney($seizure['amount'])
            ;
        }

        if ($holdAmount !== null) {
            $this->accountManager->fill($holdAmount, $account);
        }

        $this->entityManager->flush();

        $this->holdManager->processSeizures($debtClientSeizure);

        if ($expectException) {
            $this->expectException(DebtBundleException::class);
            $this->expectExceptionMessage('Collection organization priority is not supported');
        }

        $result = $this->seizureMoveAmountService->countPossibleMoveAmount($debtClientSeizure, $organizationPriority);

        if (!$expectException) {
            $this->assertEquals($expectedAmount, $result);
        }
    }

    public function countPossibleMoveAmountDataProvider(): array
    {
        $gel = [
            '0' => new Money('0', 'GEL'),
            '50' => new Money('50', 'GEL'),
            '100' => new Money('100', 'GEL'),
            '150' => new Money('150', 'GEL'),
            '200' => new Money('200', 'GEL'),
            '250' => new Money('250', 'GEL'),
            '279' => new Money('279', 'GEL'),
            '300' => new Money('300', 'GEL'),
            '400' => new Money('400', 'GEL'),
            '500' => new Money('500', 'GEL'),
            '837' => new Money('837', 'GEL'),
        ];
        $eur = [
            '400' => new Money('400', 'EUR'),
        ];

        return [
            'Amount null | For higher priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 1, 'amount' => null],
                ],
            ],
            'Amount null | For higher priority | Hold amount cover higher part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 1, 'amount' => null],
                ],
            ],
            'Amount null | For lower priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For lower priority | Hold amount cover lower part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For same priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 2, 'amount' => null],
                ],
            ],
            'Amount null | For same priority | Hold amount cover same part' => [
                'expectedAmount' => new Money('99.99', 'GEL'),
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 2, 'amount' => null],
                ],
            ],
            'Amount null | For all priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['200']],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For all priority | Hold amount cover higher part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['200']],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For all priority | Hold amount cover same part' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['300'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['200']],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For all priority | Hold amount cover lower part' => [
                'expectedAmount' => new Money('300.01', 'GEL'),
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['500'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['200']],
                    ['priority' => 2, 'amount' => $gel['200']],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount null | For all priority | Hold amount cover lower part | Higher is null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['500'],
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 2, 'amount' => $gel['200']],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Amount not null | For higher priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For higher priority | Hold amount cover higher part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For higher priority | Hold amount cover higher full' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['200'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For lower priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For lower priority | Hold amount cover lower part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For lower priority | Hold amount cover lower full' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['200'],
                'seizures' => [
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For same priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For same priority | Hold amount cover same part' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For same priority | Hold amount cover same full' => [
                'expectedAmount' => $gel['200'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['200'],
                'seizures' => [
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover null' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover higher part' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['50'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover higher full' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['100'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover lower part' => [
                'expectedAmount' => $gel['150'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['250'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover lower full' => [
                'expectedAmount' => $gel['200'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['300'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover same part' => [
                'expectedAmount' => $gel['50'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['150'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover same full' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['200'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Amount not null | For all priority | Hold amount cover all' => [
                'expectedAmount' => $gel['300'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $gel['400'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'No seizures' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => null,
                'seizures' => [],
            ],
            'Exception' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_NATIONAL_BANK,
                'holdAmount' => $gel['300'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
                'expectException' => true,
            ],
            'Amount not null | For all priority | Hold amount cover all | EUR' => [
                'expectedAmount' => $gel['837'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'holdAmount' => $eur['400'],
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['279']],
                    ['priority' => 2, 'amount' => $gel['279']],
                    ['priority' => 3, 'amount' => $gel['279']],
                    ['priority' => 4, 'amount' => $gel['279']],
                ],
            ],
        ];
    }

    /** @dataProvider moveMoneyMethodDataProvider */
    public function testMoveMoneyMethod(
        Money $expectedMovedAmount,
        array $expectedSeizures,
        array $expectedCollections,
        Money $amountToMove,
        ?array $seizuresHolds,
        array $collectionsHolds
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $accounts = [];

        // Create collections
        $amount = $collectionsHolds ? reset($collectionsHolds)['amount'] : null;
        $this->fixturesHelper
            ->createDebtCollectionGrs('1', $debtClientCollection, $licensedPartner)
            ->setAmountMoney($amount)
            ->setAmountLeftMoney($amount)
        ;

        foreach ($collectionsHolds as $key => $collectionsHold) {
            if (!array_key_exists($key, $accounts)) {
                $accounts[$key] = $this->fixturesHelper->createAccount($client, (string) $key, (string) $key);
                $this->fixturesHelper->createAccountPartner($accounts[$key], LicensedPartner::PAYSERA_GEORGIA);
            }

            if ($collectionsHold['holdAmount'] !== null) {
                $this->accountManager->fill($collectionsHold['holdAmount'], $accounts[$key]);
            }
        }

        $this->entityManager->flush();
        $this->holdManager->processCollections($debtClientCollection);

        // Create seizure with needed hold
        if ($seizuresHolds !== null) {
            $this->fixturesHelper
                ->createDebtSeizureGrs('1', $debtClientSeizure, $licensedPartner)
                ->setAmountMoney($seizuresHolds ? reset($seizuresHolds)['amount'] : null)
            ;

            foreach ($seizuresHolds as $key => $seizuresHold) {
                if (!array_key_exists($key, $accounts)) {
                    $accounts[$key] = $this->fixturesHelper->createAccount($client, (string)$key, (string)$key);
                    $this->fixturesHelper->createAccountPartner($accounts[$key], LicensedPartner::PAYSERA_GEORGIA);
                }

                if ($seizuresHold['holdAmount'] !== null) {
                    $this->accountManager->fill($seizuresHold['holdAmount'], $accounts[$key]);
                }
            }
        }

        $this->entityManager->flush();
        $this->holdManager->processSeizures($debtClientSeizure);

        // Call method
        $movedAmount = $this->seizureMoveAmountService->moveMoney(
            $debtClientSeizure,
            $debtClientCollection,
            $amountToMove
        );

        // Check results
        $this->assertEquals($expectedMovedAmount, $movedAmount);

        $activeSeizureHolds = $this->holdHelper->filterActiveHolds($debtClientSeizure->getHolds()->toArray());
        $seizureHoldsToCheck = [];
        foreach ($activeSeizureHolds as $seizureHold) {
            $seizureHoldsToCheck[$seizureHold->getAccount()->getNumber()] = [
                'amount' => $seizureHold->getAmountMoney(),
                'holdAmount' => $seizureHold->getHoldAmountMoney(),
            ];
        }
        ksort($seizureHoldsToCheck);

        $this->assertEquals($expectedSeizures, $seizureHoldsToCheck);

        $collectionHolds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());
        $collectionHoldsToCheck = [];
        foreach ($collectionHolds as $collectionHold) {
            $collectionHoldsToCheck[$collectionHold->getAccount()->getNumber()] = [
                'amount' => $collectionHold->getAmountMoney(),
                'holdAmount' => $collectionHold->getHoldAmountMoney(),
            ];
        }
        ksort($collectionHoldsToCheck);

        $this->assertEquals($expectedCollections, $collectionHoldsToCheck);
    }

    public function moveMoneyMethodDataProvider(): array
    {
        $gel = [
            '0' => new Money('0', 'GEL'),
            '50' => new Money('50', 'GEL'),
            '100' => new Money('100', 'GEL'),
            '150' => new Money('150', 'GEL'),
            '200' => new Money('200', 'GEL'),
            '250' => new Money('250', 'GEL'),
            '300' => new Money('300', 'GEL'),
            '400' => new Money('400', 'GEL'),
        ];
        $gelConv = [ // On test data after conversion 1 GEL is not 1 GEL
            '50' => new Money('50.02', 'GEL'),
            '250' => new Money('250.01', 'GEL'),
        ];
        $gelInEur = [
            '50' => new Money('13.82', 'EUR'),
            '150' => new Money('41.46', 'EUR'),
            '200' => new Money('55.28', 'EUR'),
        ];

        return [
            'Case 1. Amount to move is 0' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['0'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 2. Amount to move is more than seizures amount' => [
                'expectedMovedAmount' => $gel['200'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['300']],
                ],
                'amountToMove' => $gel['400'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 3. Amount to move is equal to seizures amount (several)' => [
                'expectedMovedAmount' => $gel['200'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['300']],
                ],
                'amountToMove' => $gel['200'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 4. Amount to move is equal to seizures amount (one)' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['100'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 5. Amount to move is equal to seizures amount (one another account)' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['300']],
                ],
                'amountToMove' => $gel['100'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 6. Amount to move is less than seizures amount (several)' => [
                'expectedMovedAmount' => $gel['150'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['250']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 7. Amount to move is less than seizures amount (one)' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['150']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['50'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 8. Amount to move is less than seizures amount (one another account)' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['250']],
                ],
                'amountToMove' => $gel['50'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 9. Seizures holds null' => [
                'expectedMovedAmount' => $gel['150'],
                'expectedSeizures' => [
                    1 => ['amount' => null, 'holdAmount' => null],
                    2 => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gelConv['250']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => null, 'holdAmount' => $gel['100']],
                    2 => ['amount' => null, 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 10. Seizures holds amount null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 11. Seizures holds null and holds amount null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizures' => [
                    1 => ['amount' => null, 'holdAmount' => null],
                    2 => ['amount' => null, 'holdAmount' => null],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => null, 'holdAmount' => null],
                    2 => ['amount' => null, 'holdAmount' => null],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
            'Case 12. Collections holds not exists for all' => [
                'expectedMovedAmount' => $gel['150'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [],
            ],
            'Case 13. Collections holds not exists for one' => [
                'expectedMovedAmount' => $gel['150'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => null],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                    2 => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 14. Collections holds null' => [
                'expectedMovedAmount' => $gel['150'],
                'expectedSeizures' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => new Money('0.03', 'GEL')],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gelConv['50']],
                ],
                'expectedCollections' => [
                    1 => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                    2 => ['amount' => null, 'holdAmount' => $gelInEur['150']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => [
                    1 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'collectionsHolds' => [
                    1 => ['amount' => null, 'holdAmount' => $gel['100']],
                    2 => ['amount' => null, 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 15. No seizures holds' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizures' => [],
                'expectedCollections' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
                'amountToMove' => $gel['150'],
                'seizuresHolds' => null,
                'collectionsHolds' => [
                    1 => ['amount' => $gel['400'], 'holdAmount' => $gel['100']],
                    2 => ['amount' => $gel['400'], 'holdAmount' => $gel['200']],
                ],
            ],
        ];
    }

    /** @dataProvider moveMoneyWithinAccountDataProvider */
    public function testMoveMoneyWithinAccount(
        Money $expectedMovedAmount,
        array $expectedSeizuresHold,
        array $expectedDebtClientCollectionHolds,
        Money $neededAmount,
        array $seizuresHold,
        array $debtClientCollectionHolds
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $accounts = [];

        // Create collections
        foreach ($debtClientCollectionHolds as $key => $debtClientCollectionHold) {
            $this->fixturesHelper
                ->createDebtCollectionGrs((string) $key, $debtClientCollection, $licensedPartner)
                ->setAmountMoney($debtClientCollectionHold['amount'])
                ->setAmountLeftMoney($debtClientCollectionHold['amount'])
            ;

            if (!array_key_exists($key, $accounts)) {
                $accounts[$key] = $this->fixturesHelper->createAccount($client, (string) $key, (string) $key);
                $this->fixturesHelper->createAccountPartner($accounts[$key], LicensedPartner::PAYSERA_GEORGIA);
            }

            if ($debtClientCollectionHold['holdAmount'] !== null) {
                $this->accountManager->fill($debtClientCollectionHold['holdAmount'], $accounts[$key]);
            }
        }

        $this->entityManager->flush();
        $this->holdManager->processCollections($debtClientCollection);

        // Create seizure with needed hold
        $this->fixturesHelper
            ->createDebtSeizureGrs('1', $debtClientSeizure, $licensedPartner)
            ->setAmountMoney($seizuresHold['amount'])
        ;

        if (!array_key_exists($seizuresHold['account'], $accounts)) {
            $accounts[$seizuresHold['account']] = $this->fixturesHelper->createAccount(
                $client,
                $seizuresHold['account'],
                $seizuresHold['account']
            );
            $this->fixturesHelper->createAccountPartner($accounts[$seizuresHold['account']], LicensedPartner::PAYSERA_GEORGIA);
        }

        if ($seizuresHold['holdAmount'] !== null) {
            $this->accountManager->fill($seizuresHold['holdAmount'], $accounts[$seizuresHold['account']]);
        }

        $this->entityManager->flush();
        $this->holdManager->processSeizures($debtClientSeizure);

        // Call method
        $hold = $debtClientSeizure->getHolds()->last();

        $reflection = new ReflectionClass($this->seizureMoveAmountService);
        $method = $reflection->getMethod('moveMoneyWithinAccount');
        $method->setAccessible(true);
        $movedAmount = $method->invokeArgs($this->seizureMoveAmountService, [
            $debtClientSeizure,
            $debtClientCollection,
            $hold,
            $neededAmount,
        ]);

        // Check results
        $this->assertEquals($expectedMovedAmount, $movedAmount);

        $activeSeizureHolds = $this->holdHelper->filterActiveHolds($debtClientSeizure->getHolds()->toArray());
        $seizureHold = array_filter(
            $activeSeizureHolds,
            fn(Hold $hold) => $hold->getAccount()->getNumber() === $seizuresHold['account']
        );
        $seizureHold = reset($seizureHold);

        $this->assertEquals($expectedSeizuresHold['amount'], $seizureHold->getAmountMoney());
        $this->assertEquals($expectedSeizuresHold['holdAmount'], $seizureHold->getHoldAmountMoney());

        $collectionHolds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());
        $collectionHoldsToCheck = [];
        foreach ($collectionHolds as $collectionHold) {
            $collectionHoldsToCheck[$collectionHold->getAccount()->getNumber()] = [
                'amount' => $collectionHold->getAmountMoney(),
                'holdAmount' => $collectionHold->getHoldAmountMoney(),
            ];
        }
        ksort($collectionHoldsToCheck);

        $this->assertEquals($expectedDebtClientCollectionHolds, $collectionHoldsToCheck);
    }

    public function moveMoneyWithinAccountDataProvider(): array
    {
        $gel = [
            '0' => new Money('0', 'GEL'),
            '50' => new Money('50', 'GEL'),
            '100' => new Money('100', 'GEL'),
            '150' => new Money('150', 'GEL'),
            '200' => new Money('200', 'GEL'),
            '250' => new Money('250', 'GEL'),
        ];
        $gelConv = [ // On test data after conversion 1 GEL is not 1 GEL
            '100' => new Money('99.99', 'GEL'),
            '250' => new Money('249.99', 'GEL'),
        ];
        $gelInEur = [
            '50' => new Money('13.82', 'EUR'),
            '100' => new Money('27.64', 'EUR'),
            '150' => new Money('41.47', 'EUR'),
            '200' => new Money('55.29', 'EUR'),
            '250' => new Money('69.11', 'EUR'),
        ];

        return [
            'Case 1. Hold amount null held null | Needed more | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 2. Hold amount null held null | Needed more | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 3. Hold amount null held null | Needed more | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 4. Hold amount null held null | Needed more | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 5. Hold amount null held null | Needed more | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 6. Hold amount null held !null | Needed more | Collection hold not exists' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gelConv['100'], 'holdAmount' => $gelConv['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 7. Hold amount null held !null | Needed more | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 8. Hold amount null held !null | Needed more | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['250']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 9. Hold amount null held !null | Needed more | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 10. Hold amount null held !null | Needed more | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gelConv['250'], 'holdAmount' => $gelConv['250']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 11. Hold amount null held !null | Needed equal | Collection hold not exists' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gelConv['100'], 'holdAmount' => $gelConv['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 12. Hold amount null held !null | Needed equal | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 13. Hold amount null held !null | Needed equal | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['250']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 14. Hold amount null held !null | Needed equal | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 15. Hold amount null held !null | Needed equal | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gelConv['100'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gelConv['250'], 'holdAmount' => $gelConv['250']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 16. Hold amount null held !null | Needed less | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 17. Hold amount null held !null | Needed less | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 18. Hold amount null held !null | Needed less | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 19. Hold amount null held !null | Needed less | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['50']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 20. Hold amount null held !null | Needed less | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => null, 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 21. Hold amount !null held null | Needed more | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 22. Hold amount !null held null | Needed more | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 23. Hold amount !null held null | Needed more | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 24. Hold amount !null held null | Needed more | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 25. Hold amount !null held null | Needed more | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 26. Hold amount !null held !null | Needed more | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['200'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 27. Hold amount !null held !null | Needed more | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                ],
                'neededAmount' => $gel['200'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 28. Hold amount !null held !null | Needed more | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['250']],
                ],
                'neededAmount' => $gel['200'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 29. Hold amount !null held !null | Needed more | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['200'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => null],
                ],
            ],
            'Case 30. Hold amount !null held !null | Needed more | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['250'], 'holdAmount' => $gel['250']],
                ],
                'neededAmount' => $gel['200'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['150']],
                ],
            ],
            'Case 31. Hold amount !null held !null | Needed equal | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 32. Hold amount !null held !null | Needed equal | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 33. Hold amount !null held !null | Needed equal | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => new Money('0.03', 'GEL')], // Because of test conversions rate
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => new Money('55.28', 'EUR')],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 34. Hold amount !null held !null | Needed equal | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => null],
                ],
            ],
            'Case 35. Hold amount !null held !null | Needed equal | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 36. Hold amount !null held !null | Needed less | Collection hold not exists' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => $gel['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 37. Hold amount !null held !null | Needed less | Collection hold amount null held null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => $gel['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['50']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                ],
            ],
            'Case 38. Hold amount !null held !null | Needed less | Collection hold amount null held !null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => new Money('50.02', 'GEL')],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => new Money('41.46', 'EUR')],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 39. Hold amount !null held !null | Needed less | Collection hold amount !null held null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => $gel['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['50']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => null],
                ],
            ],
            'Case 40. Hold amount !null held !null | Needed less | Collection hold amount !null held !null' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => $gel['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['150'], 'holdAmount' => $gel['150']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 41. Needed amount is 0' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['0'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
            ],
            'Case 42. No collection holds and cannot move amount' => [
                'expectedMovedAmount' => $gel['0'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => null],
                'debtClientCollectionHolds' => [],
            ],
            'Case 43. No collection holds and needed more' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['100'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                ],
                'neededAmount' => $gel['150'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['100'], 'holdAmount' => $gel['50']],
                'debtClientCollectionHolds' => [],
            ],
            'Case 44. No collection holds and needed equal' => [
                'expectedMovedAmount' => $gel['100'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => null],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'neededAmount' => $gel['100'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [],
            ],
            'Case 45. No collection holds and needed less' => [
                'expectedMovedAmount' => $gel['50'],
                'expectedSeizuresHold' => ['amount' => $gel['150'], 'holdAmount' => $gel['50']],
                'expectedDebtClientCollectionHolds' => [
                    '1' => ['amount' => $gel['50'], 'holdAmount' => $gel['50']],
                ],
                'neededAmount' => $gel['50'],
                'seizuresHold' => ['account' => '1', 'amount' => $gel['150'], 'holdAmount' => $gel['100']],
                'debtClientCollectionHolds' => [],
            ],
        ];
    }

    /** @dataProvider getMorePrioritizedSeizuresAmountDataProvider */
    public function testGetMorePrioritizedSeizuresAmount(
        ?Money $expectedAmount,
        int $organizationPriority,
        array $seizures,
        bool $expectException = false
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        foreach ($seizures as $number => $seizure) {
            $number = (string) ($number + 1);
            $this->fixturesHelper
                ->createDebtSeizureGrs($number, $debtClientSeizure, $licensedPartner)
                ->setOrganizationPriority($seizure['priority'])
                ->setAmountMoney($seizure['amount'])
            ;
        }

        $this->entityManager->flush();

        $seizures = $this->seizureRepository->findAll();

        $reflection = new ReflectionClass($this->seizureMoveAmountService);
        $method = $reflection->getMethod('getMorePrioritizedSeizuresAmount');
        $method->setAccessible(true);

        if ($expectException) {
            $this->expectException(DebtBundleException::class);
            $this->expectExceptionMessage('Collection organization priority is not supported');
        }

        $result = $method->invokeArgs($this->seizureMoveAmountService, [$seizures, $organizationPriority]);

        if (!$expectException) {
            $this->assertEquals($expectedAmount, $result);
        }
    }

    public function getMorePrioritizedSeizuresAmountDataProvider(): array
    {
        $gel = [
            '0' => new Money('0', 'GEL'),
            '100' => new Money('100', 'GEL'),
            '400' => new Money('400', 'GEL'),
        ];
        $eur = [
            '100' => new Money('100', 'EUR'),
        ];

        return [
            'No seizures' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [],
            ],
            'Seizures for all priorities   | with amount' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for all priorities   | with amount | EUR' => [
                'expectedAmount' => new Money('361.75', 'GEL'),
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => $eur['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for all priorities   | without amount more prioritized' => [
                'expectedAmount' => null,
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for all priorities   | without amount same priority' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for all priorities   | without amount less prioritized' => [
                'expectedAmount' => $gel['100'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for all priorities   | without amount all' => [
                'expectedAmount' => null,
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 3, 'amount' => null],
                    ['priority' => 4, 'amount' => null],
                ],
            ],
            'Seizures for more prioritized | with amount' => [
                'expectedAmount' => $gel['400'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for more prioritized | without amount' => [
                'expectedAmount' => null,
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 1, 'amount' => $gel['100']],
                    ['priority' => 1, 'amount' => null],
                    ['priority' => 1, 'amount' => null],
                ],
            ],
            'Seizures for less prioritized | with amount' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for less prioritized | without amount' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 3, 'amount' => $gel['100']],
                    ['priority' => 4, 'amount' => null],
                    ['priority' => 4, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for same priority    | with amount' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                ],
            ],
            'Seizures for same priority    | without amount' => [
                'expectedAmount' => $gel['0'],
                'organizationPriority' => Collection::PRIORITY_REVENUE_SERVICE,
                'seizures' => [
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => $gel['100']],
                    ['priority' => 2, 'amount' => null],
                    ['priority' => 2, 'amount' => $gel['100']],
                ],
            ],
        ];
    }

    /** @dataProvider calculateCollectionTempAmountDataProvider */
    public function testCalculateCollectionTempAmount(
        Money $expectedAmount,
        ?array $collectionHold,
        Money $amountToMove
    ): void {
        $account = $this->fixturesHelper->createAccount();

        $hold = null;
        if ($collectionHold !== null) {
            $hold = $this->fixturesHelper->createHold($account, $collectionHold['holdAmount']);

            if ($collectionHold['holdAmount'] !== null) {
                $hold->addHoldAmountMoney($collectionHold['holdAmount']);
            }
        }

        $reflection = new ReflectionClass($this->seizureMoveAmountService);
        $method = $reflection->getMethod('calculateCollectionTempAmount');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->seizureMoveAmountService, [$hold, $amountToMove]);
        $this->assertEquals($expectedAmount, $result);
    }

    public function calculateCollectionTempAmountDataProvider(): array
    {
        return [
            'Case 1. Collection hold null' => [
                'expectedAmount' => new Money('100', 'GEL'),
                'collectionHold' => null,
                'amountToMove' => new Money('100', 'GEL'),
            ],
            'Case 2. Collection hold not null | Hold amount null' => [
                'expectedAmount' => new Money('100', 'GEL'),
                'collectionHold' => ['holdAmount' => null],
                'amountToMove' => new Money('100', 'GEL'),
            ],
            'Case 3. Collection hold not null | Hold amount not null | Amount to move 0' => [
                'expectedAmount' => new Money('200', 'GEL'),
                'collectionHold' => ['holdAmount' => new Money('200', 'GEL')],
                'amountToMove' => new Money('0', 'GEL'),
            ],
            'Case 4. Collection hold not null | Hold amount not null | Amount to move > 0' => [
                'expectedAmount' => new Money('300', 'GEL'),
                'collectionHold' => ['holdAmount' => new Money('200', 'GEL')],
                'amountToMove' => new Money('100', 'GEL'),
            ],
        ];
    }

    /** @dataProvider calculateCollectionFinalAmountDataProvider */
    public function testCalculateCollectionFinalAmount(
        ?Money $expectedAmount,
        ?array $collectionHold,
        Money $tempAmount
    ): void {
        $account = $this->fixturesHelper->createAccount();
        $hold = $collectionHold !== null
            ? $this->fixturesHelper->createHold($account, $collectionHold['amount'])
            : null
        ;

        $reflection = new ReflectionClass($this->seizureMoveAmountService);
        $method = $reflection->getMethod('calculateCollectionFinalAmount');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->seizureMoveAmountService, [$hold, $tempAmount]);
        $this->assertEquals($expectedAmount, $result);
    }

    public function calculateCollectionFinalAmountDataProvider(): array
    {
        return [
            'Case 1. Collection hold null' => [
                'expectedAmount' => new Money('100', 'GEL'),
                'collectionHold' => null,
                'tempAmount' => new Money('100', 'GEL'),
            ],
            'Case 2. Collection hold not null | Hold amount null' => [
                'expectedAmount' => null,
                'collectionHold' => ['amount' => null],
                'tempAmount' => new Money('100', 'GEL'),
            ],
            'Case 3. Collection hold not null | Hold amount not null | Temp amount 0' => [
                'expectedAmount' => new Money('200', 'GEL'),
                'collectionHold' => ['amount' => new Money('200', 'GEL')],
                'tempAmount' => new Money('0', 'GEL'),
            ],
            'Case 4. Collection hold not null | Hold amount not null | Temp amount > hold amount' => [
                'expectedAmount' => new Money('300', 'GEL'),
                'collectionHold' => ['amount' => new Money('200', 'GEL')],
                'tempAmount' => new Money('300', 'GEL'),
            ],
            'Case 5. Collection hold not null | Hold amount not null | Temp amount < hold amount' => [
                'expectedAmount' => new Money('200', 'GEL'),
                'collectionHold' => ['amount' => new Money('200', 'GEL')],
                'tempAmount' => new Money('100', 'GEL'),
            ],
            'Case 6. Collection hold not null | Hold amount not null | Temp amount = hold amount' => [
                'expectedAmount' => new Money('200', 'GEL'),
                'collectionHold' => ['amount' => new Money('200', 'GEL')],
                'tempAmount' => new Money('200', 'GEL'),
            ],
        ];
    }

    /** @dataProvider findCollectionHoldForAccountDataProvider */
    public function testFindCollectionHoldForAccount(
        ?string $expectedHoldDetails,
        int $accountNumber,
        array $holds
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);

        $accounts = [];

        foreach ($holds as $hold) {
            if (!array_key_exists($hold['account'], $accounts)) {
                $accounts[$hold['account']] = $this->fixturesHelper->createAccount($client, $hold['account'], $hold['account']);
            }

            $newHold = $this->fixturesHelper->createHold(
                $accounts[$hold['account']],
                new Money('1', 'GEL'),
                $hold['active'] ? Hold::STATUS_WAITING_FUNDS : Hold::STATUS_DONE,
                $hold['details']
            );
            $debtClientCollection->addHold($newHold);
        }

        $reflection = new ReflectionClass($this->seizureMoveAmountService);
        $method = $reflection->getMethod('findCollectionHoldForAccount');
        $method->setAccessible(true);

        $account = !array_key_exists($accountNumber, $accounts)
            ? $this->fixturesHelper->createAccount($client, (string)$accountNumber, (string)$accountNumber)
            : $accounts[$accountNumber]
        ;

        $result = $method->invokeArgs($this->seizureMoveAmountService, [$debtClientCollection, $account]);
        $this->assertEquals($expectedHoldDetails, $result !== null ? $result->getDetails() : null);
    }

    public function findCollectionHoldForAccountDataProvider(): array
    {
        return [
            'Case 1. No holds' => [
                'expectedHoldDetails' => null,
                'accountNumber' => 1,
                'holds' => [],
            ],
            'Case 2. No active holds (no for account)' => [
                'expectedHoldDetails' => null,
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => false, 'details' => '1'],
                    ['account' => 2, 'active' => false, 'details' => '2'],
                ],
            ],
            'Case 3. No active holds (not active exists for account)' => [
                'expectedHoldDetails' => null,
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => false, 'details' => '1'],
                    ['account' => 2, 'active' => false, 'details' => '2'],
                    ['account' => 3, 'active' => false, 'details' => '3'],
                ],
            ],
            'Case 4. Active holds (no for account)' => [
                'expectedHoldDetails' => null,
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => true, 'details' => '1'],
                    ['account' => 2, 'active' => true, 'details' => '2'],
                ],
            ],
            'Case 5. Active holds (exists for account)' => [
                'expectedHoldDetails' => '3',
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => true, 'details' => '1'],
                    ['account' => 2, 'active' => true, 'details' => '2'],
                    ['account' => 3, 'active' => true, 'details' => '3'],
                ],
            ],
            'Case 6. Active and not active holds (no for account)' => [
                'expectedHoldDetails' => null,
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => false, 'details' => '1'],
                    ['account' => 2, 'active' => false, 'details' => '2'],
                    ['account' => 1, 'active' => true, 'details' => '1-1'],
                    ['account' => 2, 'active' => true, 'details' => '2-2'],
                ],
            ],
            'Case 7. Active and not active holds (exists for account)' => [
                'expectedHoldDetails' => '3-3',
                'accountNumber' => 3,
                'holds' => [
                    ['account' => 1, 'active' => false, 'details' => '1'],
                    ['account' => 2, 'active' => false, 'details' => '2'],
                    ['account' => 3, 'active' => false, 'details' => '3'],
                    ['account' => 1, 'active' => true, 'details' => '1-1'],
                    ['account' => 2, 'active' => true, 'details' => '2-2'],
                    ['account' => 3, 'active' => true, 'details' => '3-3'],
                ],
            ],
        ];
    }
}
