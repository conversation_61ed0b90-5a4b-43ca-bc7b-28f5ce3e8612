<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\HoldService;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldAmountService;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class HoldAmountServiceTest extends PersistableWebTestCase
{
    private HoldAmountService $holdAmountService;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', new HardcodedTestCurrencyConverter());

        $this->holdAmountService = $this->getContainer()->get('evp_debt.service.hold_service.hold_amount');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param Money|null $expectedAmount
     * @param Money[]|null[] $amounts
     * @dataProvider countTotalDebtAmountDataProvider
     */
    public function testCountTotalCollectionsAmountLeft(?Money $expectedAmount, array $amounts): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClient = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        $collections = [];
        foreach ($amounts as $number => $amount) {
            $collection = $this->fixturesHelper->createDebtCollectionGrs((string)$number, $debtClient, $licensedPartner);
            $collection->setAmountMoney($amount);
            $collection->setAmountLeftMoney($amount);
            $collections[] = $collection;
        }

        $this->assertEquals($expectedAmount, $this->holdAmountService->countTotalCollectionsAmountLeft($collections));
    }

    /**
     * @param Money|null $expectedAmount
     * @param Money[]|null[] $amounts
     * @dataProvider countTotalDebtAmountDataProvider
     */
    public function testCountTotalSeizuresAmount(?Money $expectedAmount, array $amounts): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        $seizures = [];
        foreach ($amounts as $number => $amount) {
            $seizure = $this->fixturesHelper->createDebtSeizureGrs((string)$number, $debtClientSeizure, $licensedPartner);
            $seizure->setAmountMoney($amount);
            $seizures[] = $seizure;
        }

        $this->assertEquals($expectedAmount, $this->holdAmountService->countTotalSeizuresAmount($seizures));
    }

    public function countTotalDebtAmountDataProvider(): array
    {
        return [
            'Case 1 - no collections' => [
                'expectedAmount' => new Money(0, 'GEL'),
                'amounts' => [],
            ],
            'Case 2 - one collection with null' => [
                'expectedAmount' => null,
                'amounts' => [
                    null,
                ],
            ],
            'Case 3 - one collection with amount in GEL' => [
                'expectedAmount' => new Money(100, 'GEL'),
                'amounts' => [
                    new Money(100, 'GEL'),
                ],
            ],
            'Case 4 - one collection with amount in EUR' => [
                'expectedAmount' => new Money('361.75', 'GEL'),
                'amounts' => [
                    new Money(100, 'EUR'),
                ],
            ],
            'Case 5 - two collections, one with null, one with amount in GEL' => [
                'expectedAmount' => null,
                'amounts' => [
                    null,
                    new Money(100, 'GEL'),
                ],
            ],
            'Case 6 - two collections, one with amount in GEL, one with amount in EUR' => [
                'expectedAmount' => new Money('461.75', 'GEL'),
                'amounts' => [
                    new Money(100, 'GEL'),
                    new Money(100, 'EUR'),
                ],
            ],
            'Case 7 - ten collections' => [
                'expectedAmount' => new Money('1805.86', 'GEL'),
                'amounts' => [
                    new Money(100, 'GEL'),
                    new Money(100, 'USD'),
                    new Money(100, 'EUR'),
                    new Money(100, 'UAH'),
                    new Money(100, 'GEL'),
                    new Money(100, 'GEL'),
                    new Money(100, 'USD'),
                    new Money(100, 'EUR'),
                    new Money(100, 'UAH'),
                    new Money(100, 'GEL'),
                ],
            ],
        ];
    }

    /**
     * @param Money $expectedAmount
     * @param Money[] $holdsAmount
     * @dataProvider countTotalHoldsHoldAmountDataProvider
     */
    public function testCountTotalHoldsHoldAmount(Money $expectedAmount, array $holdsAmount): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $account = $this->fixturesHelper->createAccount($client);

        $holds = [];
        foreach ($holdsAmount as $number => $amount) {
            $holdCurrency = $amount !== null ? $amount->getCurrency() : 'GEL';
            $hold = $this->fixturesHelper->createHold($account, new Money(1000, $holdCurrency));

            if ($amount !== null) {
                $hold->addHoldAmountMoney($amount);
            }

            $holds[] = $hold;
        }

        $result = $this->holdAmountService->countTotalHoldsHoldAmount($holds);
        $this->assertEquals($expectedAmount, $result);
    }

    public function countTotalHoldsHoldAmountDataProvider(): array
    {
        return [
            'Case 1 - no holds' => [
                'expectedAmount' => new Money(0, 'GEL'),
                'holdsAmount' => [],
            ],
            'Case 2 - one hold with null' => [
                'expectedAmount' => new Money(0, 'GEL'),
                'holdsAmount' => [
                    null,
                ],
            ],
            'Case 3 - one hold with amount in GEL' => [
                'expectedAmount' => new Money(100, 'GEL'),
                'holdsAmount' => [
                    new Money(100, 'GEL'),
                ],
            ],
            'Case 4 - one hold with amount in EUR' => [
                'expectedAmount' => new Money('361.75', 'GEL'),
                'holdsAmount' => [
                    new Money(100, 'EUR'),
                ],
            ],
            'Case 5 - two holds, one with null, one with amount in GEL' => [
                'expectedAmount' => new Money(100, 'GEL'),
                'holdsAmount' => [
                    null,
                    new Money(100, 'GEL'),
                ],
            ],
            'Case 6 - two holds, one with amount in GEL, one with amount in EUR' => [
                'expectedAmount' => new Money('461.75', 'GEL'),
                'holdsAmount' => [
                    new Money(100, 'GEL'),
                    new Money(100, 'EUR'),
                ],
            ],
            'Case 7 - ten holds' => [
                'expectedAmount' => new Money('1805.86', 'GEL'),
                'holdsAmount' => [
                    new Money(100, 'GEL'),
                    new Money(100, 'USD'),
                    new Money(100, 'EUR'),
                    new Money(100, 'UAH'),
                    new Money(100, 'GEL'),
                    new Money(100, 'GEL'),
                    new Money(100, 'USD'),
                    new Money(100, 'EUR'),
                    new Money(100, 'UAH'),
                    new Money(100, 'GEL'),
                ],
            ],
        ];
    }
}
