<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\Admin;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\ClientBundle\Tests\BaseTestCase;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\Admin\CollectionOrderAdminHelper;
use Evp\Bundle\DebtBundle\Tests\FixturesHelper;
use Evp\Component\Money\Money;

class CollectionOrderAdminHelperTest extends BaseTestCase
{
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionRepository $collectionRepository;
    private CollectionOrderAdminHelper $collectionOrderAdminHelper;

    protected function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');

        $this->collectionOrderAdminHelper = $this->getContainer()->get(
            'evp_bundle_debt.service_admin.collection_order_admin_helper'
        );
    }

    /** @dataProvider transferDataProvider */
    public function testCalculateTotalAmountSent(array $transfers, int $expectedResult): void
    {
        $collection = $this->fixturesHelper->createCollection()
            ->setAmountMoney(new Money(1000, 'GEL'))
            ->setAmountLeftMoney(new Money(700, 'GEL'))
        ;

        array_map(fn(Transfer $transfer) => $this->entityManager->persist($transfer), $transfers);

        $collection->setTransfers(new ArrayCollection($transfers));

        $this->entityManager->flush();

        $collection = $this->collectionRepository->findAll()[0];

        $totalAmount = $this->collectionOrderAdminHelper->calculateTotalAmount(
            $collection->getTransfers(),
            $collection->getCurrency()
        );

        $this->assertEquals(new Money($expectedResult, $collection->getCurrency()), $totalAmount);
        $this->expectNoErrorsLogged();
    }

    /** @dataProvider commissionDataProvider */
    public function testCalculateTotalCommissionAmount(array $commissions, int $expectedResult): void
    {
        $collection = $this->fixturesHelper->createCollection()
            ->setAmountMoney(new Money(1000, 'GEL'))
            ->setAmountLeftMoney(new Money(700, 'GEL'))
        ;

        array_map(fn(Commission $commission) => $this->entityManager->persist($commission), $commissions);

        $collection->setCommissions(new ArrayCollection($commissions));

        $this->entityManager->flush();

        $collection = $this->collectionRepository->findAll()[0];

        $totalAmount = $this->collectionOrderAdminHelper->calculateTotalAmount(
            $collection->getCommissions(),
            $collection->getCurrency()
        );

        $this->assertEquals(new Money($expectedResult, $collection->getCurrency()), $totalAmount);
        $this->expectNoErrorsLogged();
    }

    public function transferDataProvider(): array
    {
        return [
            'Case 1: Test with Valid Outgoing Transfers.' => [
                [
                    $this->prepareTransferOutBank(),
                    $this->prepareTransferOutBank(),
                    $this->prepareTransferOutBank(),
                ],
                300,
            ],
            'Case 2: Test with Outgoing Transfers (different statuses).' => [
                [
                    $this->prepareTransferOutBank(),
                    $this->prepareTransferOutBank()->setStatus(Transfer::STATUS_PROCESSING),
                    $this->prepareTransferOutBank()->setStatus(Transfer::STATUS_READY),
                    $this->prepareTransferOutBank()->setStatus(Transfer::STATUS_NEW),
                ],
                100,
            ],
            'Case 3: Test with Outgoing and Incoming Transfers.' => [
                [
                    $this->prepareTransferIn(),
                    $this->prepareTransferIn(),
                ],
                0,
            ],
        ];
    }

    public function commissionDataProvider(): array
    {
        return [
            'Case 1: Test with Valid Commissions.' => [
                [
                    $this->prepareCommission(),
                    $this->prepareCommission(),
                ],
                20,
            ],
            'Case 2: Test with Commissions (different statuses).' => [
                [
                    $this->prepareCommission()->setStatus(Commission::STATUS_ACTIVE),
                    $this->prepareCommission()->setStatus(Commission::STATUS_ACTIVE),
                ],
                0,
            ],
        ];
    }

    public function prepareTransferOutBank(): TransferOutBank
    {
        return (new TransferOutBank())
            ->setAmountMoney(new Money(100, 'GEL'))
            ->setPayer(new PartyIban('********************', 'Payer name'))
            ->setBeneficiary(new PartyIban())
            ->setStatus(Transfer::STATUS_DONE)
        ;
    }

    public function prepareTransferIn(): TransferIn
    {
        return (new TransferIn())
            ->setAmountMoney(new Money(100, 'GEL'))
            ->setPayer(new PartyIban('********************', 'Payer name'))
            ->setBeneficiary(new PartyIban())
            ->setStatus(Transfer::STATUS_DONE)
        ;
    }

    public function prepareCommission(): Commission
    {
        return (new Commission())
            ->setAmountMoney(new Money(10, 'GEL'))
            ->setStatus(Commission::STATUS_DONE)
        ;
    }
}
