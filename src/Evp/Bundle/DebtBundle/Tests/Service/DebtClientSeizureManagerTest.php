<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Evp\Bundle\DebtBundle\Repository\DebtClientSeizureRepository;
use Evp\Bundle\DebtBundle\Service\DebtClientSeizureManager;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class DebtClientSeizureManagerTest extends TestCase
{
    private MockObject $debtClientSeizureRepository;
    private MockObject $entityManager;
    private DebtClientSeizureManager $debtClientSeizureManager;

    protected function setUp(): void
    {
        $this->debtClientSeizureRepository = $this->createMock(DebtClientSeizureRepository::class);
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->debtClientSeizureManager = new DebtClientSeizureManager(
            $this->debtClientSeizureRepository,
            $this->entityManager
        );
    }

    public function testReturnsExistingDebtClientSeizure(): void
    {
        $client = new ClientNatural();
        $debtClientSeizure = new DebtClientSeizure();

        $this->debtClientSeizureRepository
            ->method('findOneByClient')
            ->with($client)
            ->willReturn($debtClientSeizure);

        $this->assertEquals($debtClientSeizure, $this->debtClientSeizureManager->getOrCreateDebtClientSeizure($client));
    }

    public function testCreatesNewDebtClientSeizure(): void
    {
        $client = new ClientNatural();

        $this->debtClientSeizureRepository
            ->method('findOneByClient')
            ->with($client)
            ->willReturn(null);

        $this->entityManager->expects($this->once())->method('persist');

        $result = $this->debtClientSeizureManager->getOrCreateDebtClientSeizure($client);
        $this->assertInstanceOf(DebtClientSeizure::class, $result);
        $this->assertEquals($client, $result->getClient());
    }
}
