<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankTransferBundle\Tests\Functional\TransferInEvent\DummyPublisher;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\BalanceService;
use Evp\Bundle\DebtBundle\Service\WriteOffInitializer;
use Evp\Bundle\DebtBundle\Service\WriteOffManager;
use Evp\Bundle\DebtBundle\Worker\ResetWaitingWriteOffCollectionWorker;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Clock\ClockInterface;

class WriteOffInitializerTest extends PersistableWebTestCase
{
    private const START_WRITE_OFF_TIME = '10:00:00';
    private const END_WRITE_OFF_TIME = '17:00:00';

    private WriteOffInitializer $writeOffInitializer;
    private CollectionRepository $collectionRepository;
    private EntityManager $entityManager;
    private DummyPublisher $publisher;
    private FixturesHelper $fixturesHelper;

    private MockObject $writeOffManager;
    private MockObject $balanceService;
    private MockObject $clock;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->writeOffManager = $this->createMock(WriteOffManager::class);
        $this->balanceService = $this->createMock(BalanceService::class);
        $this->clock = $this->createMock(ClockInterface::class);
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');

        /** @var DummyPublisher $publisher */
        $publisher = $this->getContainer()->get('evp_grs.publisher.grs_remote_job_publisher');
        $this->publisher = $publisher;

        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->writeOffInitializer = new WriteOffInitializer(
            $this->collectionRepository,
            $this->getContainer()->get('evp_debt.repository.debt_client_seizure'),
            $this->balanceService,
            $this->getContainer()->get('evp_debt.service.debt_sorter'),
            $this->writeOffManager,
            $this->publisher,
            $this->clock,
            $this->getContainer()->get('logger'),
            self::START_WRITE_OFF_TIME,
            self::END_WRITE_OFF_TIME
        );
    }

    protected static function getMockedServices(): array
    {
        return ['evp_grs.publisher.grs_remote_job_publisher' => new DummyPublisher()];
    }

    /** @dataProvider initWriteOffDataProvider */
    public function testInitWriteOff(
        ?string $expectedInitializedCollection,
        array $expectedWaitingWriteOffs,
        array $collectionsData
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $this->fixturesHelper->createAccount($client);
        $this->createCollectionsFromData($collectionsData, $client);

        $this->entityManager->flush();

        $this->balanceService
            ->expects($this->any())
            ->method('isCollectionHasMoney')
            ->willReturnCallback(function(Collection $collection) use ($collectionsData) {
                return $collectionsData[$collection->getNumber()]['hasMoney'];
            })
        ;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $initializedCollection = null;
        $this->writeOffManager
            ->expects($this->exactly($expectedInitializedCollection !== null ? 1 : 0))
            ->method('startWriteOffProcess')
            ->willReturnCallback(function (Collection $collection) use (&$initializedCollection) {
                $initializedCollection = $collection->getNumber();
            })
        ;

        $this->writeOffInitializer->initWriteOff($client);
        $this->entityManager->flush();

        $this->assertEquals($expectedInitializedCollection, $initializedCollection);

        $collections = $this->collectionRepository->findAllActiveByClientAndWriteOffStatuses(
            $client,
            [Collection::WRITE_OFF_STATUS_WAITING]
        );
        $waitingWriteOffs = array_map(fn (Collection $collection) => $collection->getNumber(), $collections);
        $this->assertEquals($expectedWaitingWriteOffs, $waitingWriteOffs);
    }

    public function initWriteOffDataProvider(): array
    {
        return [
            'Regular case. collections are active, have money, no write off statuses' => [
                'expectedInitializedCollection' => 'n1',
                'expectedWaitingWriteOffs' => ['n2', 'n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'No waiting write offs' => [
                'expectedInitializedCollection' => null,
                'expectedWaitingWriteOffs' => [],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => null,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Collection are not active anymore' => [
                'expectedInitializedCollection' => 'n2',
                'expectedWaitingWriteOffs' => ['n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_CANCELLED,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Collection has done write off status' => [
                'expectedInitializedCollection' => 'n2',
                'expectedWaitingWriteOffs' => ['n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                        'hasMoney' => true,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Collection has started write off status' => [
                'expectedInitializedCollection' => null,
                'expectedWaitingWriteOffs' => ['n1', 'n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Collection has failed write off status' => [
                'expectedInitializedCollection' => null,
                'expectedWaitingWriteOffs' => ['n1', 'n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Collection has no money' => [
                'expectedInitializedCollection' => 'n2',
                'expectedWaitingWriteOffs' => ['n3'],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => false,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'All waiting write offs cannot be started' => [
                'expectedInitializedCollection' => null,
                'expectedWaitingWriteOffs' => [],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => false,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_CANCELLED,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
            'Only last waiting write off can be started' => [
                'expectedInitializedCollection' => 'n4',
                'expectedWaitingWriteOffs' => [],
                'collectionsData' => [
                    'n1' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => false,
                    ],
                    'n2' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                        'hasMoney' => true,
                    ],
                    'n3' => [
                        'status' => Collection::STATUS_CANCELLED,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                    'n4' => [
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                        'hasMoney' => true,
                    ],
                ],
            ],
        ];
    }

    public function testPublishResetWaitingWriteOffCollectionJobOnException(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $this->fixturesHelper->createAccount($client);

        $collectionsData = [
            'n1' => [
                'status' => Collection::STATUS_ACTIVE,
                'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
                'hasMoney' => true
            ],
        ];
        $this->createCollectionsFromData($collectionsData, $client);
        $this->entityManager->flush();

        $this->balanceService
            ->expects($this->any())
            ->method('isCollectionHasMoney')
            ->willReturnCallback(function(Collection $collection) use ($collectionsData) {
                return $collectionsData[$collection->getNumber()]['hasMoney'];
            })
        ;

        $this->writeOffManager
            ->expects($this->once())
            ->method('startWriteOffProcess')
            ->willThrowException(new Exception('Test exception'))
        ;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        try {
            $this->writeOffInitializer->initWriteOff($client);
        } catch (Exception $exception) {
        }

        $this->entityManager->flush();

        $waitingWriteOffsCount = $this->collectionRepository->countActiveByClientAndWriteOffStatuses(
            $client,
            [Collection::WRITE_OFF_STATUS_WAITING]
        );
        $this->assertEquals(0, $waitingWriteOffsCount);

        $publishedJobs = $this->publisher->getJobsPublished();
        $this->assertCount(1, $publishedJobs);

        $expectedJob = [
            ResetWaitingWriteOffCollectionWorker::JOB_KEY,
            [ResetWaitingWriteOffCollectionWorker::DATA_CLIENT_ID => $client->getId()],
        ];
        $this->assertEquals($expectedJob, $publishedJobs[0]);
    }

    /** @dataProvider initWriteOffOutOfWriteOffTimeDataProvider */
    public function testInitWriteOffOutOfWriteOffTime(
        int $expectedInitializedCollections,
        int $expectedWaitingWriteOffs,
        DateTimeImmutable $time
    ): void {
        $collectionBody = [
            'status' => Collection::STATUS_ACTIVE,
            'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING,
            'hasMoney' => true,
        ];
        $collectionsData = ['n1' => $collectionBody, 'n2' => $collectionBody, 'n3' => $collectionBody];

        $client = $this->fixturesHelper->createClientNatural();
        $this->fixturesHelper->createAccount($client);
        $this->createCollectionsFromData($collectionsData, $client);

        $this->balanceService
            ->expects($this->any())
            ->method('isCollectionHasMoney')
            ->willReturn(true)
        ;

        $this->writeOffManager
            ->expects($this->exactly($expectedInitializedCollections))
            ->method('startWriteOffProcess')
        ;

        $this->clock
            ->expects($this->once())
            ->method('now')
            ->willReturn($time)
        ;

        $this->entityManager->flush();
        $this->writeOffInitializer->initWriteOff($client);
        $this->entityManager->flush();

        $collectionsCount = $this->collectionRepository->countActiveByClientAndWriteOffStatuses(
            $client,
            [Collection::WRITE_OFF_STATUS_WAITING]
        );
        $this->assertEquals($expectedWaitingWriteOffs, $collectionsCount);
    }

    public function initWriteOffOutOfWriteOffTimeDataProvider(): array
    {
        return [
            'Before write off time' => [
                'expectedInitializedCollections' => 0,
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::START_WRITE_OFF_TIME))->modify('-1 minute'),
            ],
            'At the end of write off time' => [
                'expectedInitializedCollections' => 0,
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::END_WRITE_OFF_TIME)),
            ],
            'After write off time' => [
                'expectedInitializedCollections' => 0,
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::END_WRITE_OFF_TIME))->modify('+1 minute'),
            ],
            'At the start of write off time' => [
                'expectedInitializedCollections' => 1,
                'expectedWaitingWriteOffs' => 2,
                'time' => new DateTimeImmutable(self::START_WRITE_OFF_TIME),
            ],
            'In the middle of write off time' => [
                'expectedInitializedCollections' => 1,
                'expectedWaitingWriteOffs' => 2,
                'time' => (new DateTimeImmutable(self::START_WRITE_OFF_TIME))->modify('+1 hour'),
            ],
        ];
    }

    /**
     * @return Collection[]
     */
    private function createCollectionsFromData(array $collectionsData, Client $client): array
    {
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        $collections = [];

        foreach($collectionsData as $number => $collectionData) {
            $collections[$number] = $this->fixturesHelper->createDebtCollectionGrs(
                $number,
                $debtClientCollection,
                $licensedPartner,
                $collectionData['status'],
                $collectionData['writeOffStatus']
            );
        }

        return $collections;
    }
}
