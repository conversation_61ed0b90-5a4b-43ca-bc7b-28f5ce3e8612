<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\Commission;

use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountAliasManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientCollectionRepository;
use Evp\Bundle\DebtBundle\Service\CollectionProcessorManager;
use Evp\Bundle\DebtBundle\Service\Commission\CommissionProvider;
use Evp\Bundle\DebtBundle\Service\WriteOffManager;
use Evp\Bundle\GrsBundle\Service\Validator\TreasuryCodeValidator;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollection;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGrs;
use Evp\Component\Money\Money;
use Evp\Tests\Mock\UserRestFactoryMock;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use ReflectionClass;
use ReflectionException;

class CommissionProviderTest extends PersistableWebTestCase
{
    private const GEL_RATE_COMMERCIAL = '0.25';
    private const GEL_RATE_OFFICIAL = '0.125';

    private CommissionProvider $commissionProvider;
    private CollectionProcessorManager $collectionProcessorManager;
    private AccountManager $accountManager;
    private CollectionRepository $collectionRepository;
    private DebtClientCollectionRepository $debtClientCollectionRepository;
    private WriteOffManager $writeOffManager;
    private EntityManager $entityManager;
    private AccountNumberGenerator $accountNumberGenerator;
    private AccountNumberGenerator $ibanGenerator;
    private HardcodedTestCurrencyConverter $commercialCurrencyConverter;
    private FixturesHelper $fixturesHelper;
    private string $treasuryBic;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->getContainer()->set('evp_user_client.user_rest_factory', new UserRestFactoryMock());
        $this->getContainer()->set(
            'evp_bank_account.account_alias_manager',
            $this->createMock(AccountAliasManager::class)
        );

        $treasuryCodeValidator = $this->createMock(TreasuryCodeValidator::class);
        $treasuryCodeValidator
            ->expects($this->any())
            ->method('isValid')
            ->with('*********')
            ->willReturn(true)
        ;
        $this->getContainer()->set('evp_grs.service.validator.treasury_code', $treasuryCodeValidator);

        $this->commercialCurrencyConverter = $this->getMockConverter(self::GEL_RATE_COMMERCIAL);
        $officialCurrencyConverter = $this->getMockConverter(self::GEL_RATE_OFFICIAL);
        $this->getContainer()->set('evp_currency.evp_api_buy_currency_converter', $this->commercialCurrencyConverter);
        $this->getContainer()->set('evp_currency.evp_api_sell_currency_converter', $this->commercialCurrencyConverter);
        $this->getContainer()->set('evp_currency.currency_converter.official.cached', $this->commercialCurrencyConverter);
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', $officialCurrencyConverter);

        $this->commissionProvider = $this->getContainer()->get('evp_debt.service.commission.commission_provider');
        $this->collectionProcessorManager = $this->getContainer()->get('evp_debt.service.collection_processor_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->debtClientCollectionRepository = $this->getContainer()->get('evp_debt.repository.debt_client_collection');
        $this->writeOffManager = $this->getContainer()->get('evp_debt.service.write_off_manager');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator');
        $this->ibanGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator.iban');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->treasuryBic = $this->getContainer()->getParameter('evp_debt.state_treasury_outgoing_transfers_bic');

        $this->fixturesHelper->createGeorgiaRTGSParticipant(
            'TRESGE22XXX',
            new DateTime('yesterday'),
            new DateTime('tomorrow')
        );

        $this->fixturesHelper->createLicensedPartner();
    }

    /**
     * @dataProvider calculateCommissionForExchangeDataProvider
     * @throws Exception
     */
    public function testCalculateCommissionForExchange(
        Money $expectedCommission,
        array $accountBalances,
        Money $collectionAmount,
        Money $amountToWriteOff,
        int $accountsCount = 1
    ): void {
        // Set data
        $client = $this->createClientNatural();

        for ($i = 1; $i <= $accountsCount; $i++) {
            $account = $this->createAccount($client, $i);
            foreach ($accountBalances as $balance) {
                $this->accountManager->fill($balance, $account);
            }
        }

        $this->entityManager->flush();

        // Create collection
        $this->collectionProcessorManager->create($this->createCommonCollection($client, $collectionAmount));
        $this->entityManager->flush();

        $debtClient = $this->debtClientCollectionRepository->findOneByClient($client);
        $this->assertCount(1, $debtClient->getCollections());

        /** @var Collection $collection */
        $collection = $debtClient->getCollections()->first();

        // Set rates equal to official because we use commercial converter in transfers now
        $this->commercialCurrencyConverter->setRate('GEL', self::GEL_RATE_OFFICIAL);

        // Write off collection
        $this->writeOffManager->startWriteOffProcess($collection, null);
        $this->entityManager->flush();

        $collection = $this->collectionRepository->findById($collection->getId());
        $this->writeOffManager->writeOff($collection, $amountToWriteOff);
        $this->entityManager->flush();

        // Calculate commission
        $this->commercialCurrencyConverter->setRate('GEL', self::GEL_RATE_COMMERCIAL);
        $collection = $this->collectionRepository->findById($collection->getId());
        $commission = $this->callCalculateCommissionForExchange($collection, $expectedCommission->getCurrency());

        $this->assertEquals($expectedCommission, $commission);
    }

    public function calculateCommissionForExchangeDataProvider(): array
    {
        return [
            'Account: GEL + EUR + USD | Used all' => [
                'expectedCommission' => new Money(600, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'),
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                    'USD' => new Money(100, 'USD'), // 400 GEL
                ],
                'collectionAmount' => new Money(1300, 'GEL'),
                'amountToWriteOff' => new Money(1300, 'GEL'),
            ],
            'Account: GEL + EUR | Used all' => [
                'expectedCommission' => new Money(400, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'),
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                ],
                'collectionAmount' => new Money(900, 'GEL'),
                'amountToWriteOff' => new Money(900, 'GEL'),
            ],
            'Account: GEL + EUR | EUR used partially' => [
                'expectedCommission' => new Money(200, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'),
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                ],
                'collectionAmount' => new Money(500, 'GEL'),
                'amountToWriteOff' => new Money(500, 'GEL'),
            ],
            'Account: GEL + EUR | Used only GEL' => [
                'expectedCommission' => new Money(0, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'),
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                ],
                'collectionAmount' => new Money(500, 'GEL'),
                'amountToWriteOff' => new Money(100, 'GEL'),
            ],
            'Account: EUR + USD | Used all' => [
                'expectedCommission' => new Money(600, 'GEL'),
                'accountBalances' => [
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                    'USD' => new Money(100, 'USD'), // 400 GEL
                ],
                'collectionAmount' => new Money(1200, 'GEL'),
                'amountToWriteOff' => new Money(1200, 'GEL'),
            ],
            'Account: EUR + USD | USD used partially' => [
                'expectedCommission' => new Money(500, 'GEL'),
                'accountBalances' => [
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                    'USD' => new Money(100, 'USD'), // 400 GEL
                ],
                'collectionAmount' => new Money(1000, 'GEL'),
                'amountToWriteOff' => new Money(1000, 'GEL'),
            ],
            'Account: EUR + USD | Used only EUR' => [
                'expectedCommission' => new Money(400, 'GEL'),
                'accountBalances' => [
                    'EUR' => new Money(100, 'EUR'), // 800 GEL
                    'USD' => new Money(100, 'USD'), // 400 GEL
                ],
                'collectionAmount' => new Money(1200, 'GEL'),
                'amountToWriteOff' => new Money(800, 'GEL'),
            ],
            'Account: GEL + EUR + USD | Used all | 2 accounts' => [
                'expectedCommission' => new Money(1200, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'), // 100 GEL | 200 GEL total
                    'EUR' => new Money(100, 'EUR'), // 800 GEL | 1600 GEL total
                    'USD' => new Money(100, 'USD'), // 400 GEL | 800 GEL total
                ],
                'collectionAmount' => new Money(2600, 'GEL'),
                'amountToWriteOff' => new Money(2600, 'GEL'),
                'accountCount' => 2,
            ],
            'Account: GEL + EUR + USD | Used all | 10 accounts' => [
                'expectedCommission' => new Money(6000, 'GEL'),
                'accountBalances' => [
                    'GEL' => new Money(100, 'GEL'), // 100 GEL | 1000 GEL total
                    'EUR' => new Money(100, 'EUR'), // 800 GEL | 8000 GEL total
                    'USD' => new Money(100, 'USD'), // 400 GEL | 4000 GEL total
                ],
                'collectionAmount' => new Money(13000, 'GEL'),
                'amountToWriteOff' => new Money(13000, 'GEL'),
                'accountCount' => 10,
            ],
        ];
    }

    private function createCommonCollection(Client $client, Money $amount): CommonCollection
    {
        return (new CommonCollectionGrs())
            ->setPriority(1)
            ->setReceiverAccount('*********')
            ->setReceiverCode($this->treasuryBic)
            ->setOperDate(new DateTimeImmutable())
            ->setExternalStatus(1)
            ->setNote(null)
            ->setThirdPersonId(null)
            ->setThirdPersonIdAlt(null)
            ->setThirdPersonName(null)
            ->setClientId($client->getId())
            ->setAmount($amount->getAmount())
            ->setCurrency($amount->getCurrency())
            ->setNumber('**********')
        ;
    }

    private function createClientNatural(): Client
    {
        $client = $this->fixturesHelper->createClientNatural(
            1,
            IdentificationLevels::FULLY_IDENTIFIED,
            'Name',
            'Last name',
            '*********',
        );
        $client->setCountryCode('GE');
        $client->setResidenceCountry('GE');
        // Updated client id because commission matcher uses specific client id matcher now
        $this->fixturesHelper->setIdProperty($client, ********, true);

        $this->fixturesHelper->createClientPartner($client, LicensedPartner::PAYSERA_GEORGIA);

        return $client;
    }

    private function createAccount(Client $client, int $number): Account
    {
        return $this->fixturesHelper->createAccount(
            $client,
            $this->accountNumberGenerator->generateNumber($number),
            $this->ibanGenerator->generateNumber($number)
        );
    }

    /**
     * @throws ReflectionException
     */
    private function callCalculateCommissionForExchange(Collection $collection, string $currency): Money
    {
        $reflection = new ReflectionClass($this->commissionProvider);
        $method = $reflection->getMethod('calculateCommissionForExchange');
        $method->setAccessible(true);

        return $method->invokeArgs($this->commissionProvider, [$collection, $currency, null]);
    }

    private function getMockConverter(string $gel): HardcodedTestCurrencyConverter
    {
        $converter = new HardcodedTestCurrencyConverter();

        $converter->setRate('GEL', $gel);
        $converter->setRate('USD', '0.5');

        return $converter;
    }
}
