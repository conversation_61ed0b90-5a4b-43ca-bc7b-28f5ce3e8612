<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\AccountAliasManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Exception\DebtBundleException;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\CollectionProcessorManager;
use Evp\Bundle\DebtBundle\Service\DebtClientCollectionManager;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Bundle\DebtBundle\Service\WriteOffManager;
use Evp\Bundle\DebtBundle\Service\WriteOffProcessor\WriteOffProcessor;
use Evp\Bundle\GrsBundle\Service\Validator\TreasuryCodeValidator;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollection;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGrs;
use Evp\Component\Money\Money;
use Evp\Tests\Mock\UserRestFactoryMock;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Workflow\Registry;

class WriteOffManagerTest extends PersistableWebTestCase
{
    private CollectionProcessorManager $collectionProcessorManager;
    private WriteOffManager $writeOffManager;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private CollectionRepository $collectionRepository;
    private string $treasuryBic;
    private FixturesHelper $fixturesHelper;
    private DebtClientCollectionManager $debtClientCollectionManager;
    private Registry $workflowRegistry;
    private HoldHelper $holdHelper;
    private MockObject $monitoringClient;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->mockCurrencyConverter();

        $this->getContainer()->set('evp_user_client.user_rest_factory', new UserRestFactoryMock());

        $this->monitoringClient = $this->createMock(MonitoringClient::class);
        $this->getContainer()->set('paysera_monitoring.monitoring_client', $this->monitoringClient);

        $treasuryCodeValidator = $this->createMock(TreasuryCodeValidator::class);
        $treasuryCodeValidator->expects($this->any())->method('isValid')->willReturn(true);
        $this->getContainer()->set('evp_grs.service.validator.treasury_code', $treasuryCodeValidator);
        $this->getContainer()->set('evp_bank_account.account_alias_manager', $this->createMock(AccountAliasManager::class));

        $this->collectionProcessorManager = $this->getContainer()->get('evp_debt.service.collection_processor_manager');
        $this->writeOffManager = $this->getContainer()->get('evp_debt.service.write_off_manager');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->treasuryBic = $this->getContainer()->getParameter('evp_debt.state_treasury_outgoing_transfers_bic');
        $this->debtClientCollectionManager = $this->getContainer()->get('evp_debt.service.debt_client_collection_manager');
        $this->workflowRegistry = $this->getContainer()->get('workflow.registry');
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /** @dataProvider writeOffSuccessfulDataProvider */
    public function testWriteOffSuccessful(
        ?string $expectedWriteOffStatus,
        ?Money $expectedAmountLeft,
        ?Money $collectionAmount,
        ?Money $amountToWriteOff,
        string $collectionType,
        string $expectedCollectionStatus,
        array $beneficiaryData = []
    ): void {
        $client = ($this->fixturesHelper->createClientNatural())->setCode('*********');
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());

        $account = $this->fixturesHelper->createAccount($client);
        $this->accountManager->fill(new Money(1_000, 'GEL'), $account);
        $this->entityManager->flush();

        $this->createCollection($collectionType, $client, $collectionAmount, $licensedPartner, $beneficiaryData);
        $this->entityManager->flush();

        /** @var Collection $collection */
        $collection = $this->collectionRepository->findOneBy(['client' => $client]);

        $this->writeOffManager->startWriteOffProcess($collection, null);
        $this->writeOffManager->writeOff($collection, $amountToWriteOff);

        $this->assertEquals($expectedWriteOffStatus, $collection->getWriteOffStatus());
        $this->assertEquals($expectedCollectionStatus, $collection->getStatus());
        $this->assertEquals($expectedAmountLeft, $collection->getAmountLeftMoney());
    }

    public function writeOffSuccessfulDataProvider(): array
    {
        return [
            'CollectionGrs - writeOff less than collection has | Not fully written off' => [
                'expectedWriteOffStatus' => null,
                'expectedAmountLeft' => new Money(40, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(60, 'GEL'),
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionGrs - writeOff null collection has money | Not fully written off' => [
                'expectedWriteOffStatus' => null,
                'expectedAmountLeft' => new Money(100, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => null,
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionGrs - writeOff money, collection has null amount | Not fully written off' => [
                'expectedWriteOffStatus' => null,
                'expectedAmountLeft' => null,
                'collectionAmount' => null,
                'amountToWriteOff' => new Money(100, 'GEL'),
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionGrs - writeOff less than collection has | Fully written off' => [
                'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                'expectedAmountLeft' => new Money(0, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(100, 'GEL'),
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionManual - writeOff less than collection has | Not fully written off' => [
                'expectedWriteOffStatus' => null,
                'expectedAmountLeft' => new Money(40, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(60, 'GEL'),
                'collectionType' => Collection::TYPE_MANUAL,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionManual - writeOff less than collection has | Fully written off' => [
                'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                'expectedAmountLeft' => new Money(0, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(100, 'GEL'),
                'collectionType' => Collection::TYPE_MANUAL,
                'expectedCollectionStatus' => CollectionManual::STATUS_DONE,
            ],
            'CollectionManual - writeOff less than collection has EUR | Fully written off' => [
                'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                'expectedAmountLeft' => new Money(0, 'EUR'),
                'collectionAmount' => new Money(100, 'EUR'),
                'amountToWriteOff' => new Money(100, 'EUR'),
                'collectionType' => Collection::TYPE_MANUAL,
                'expectedCollectionStatus' => CollectionManual::STATUS_DONE,
                'beneficiaryData' => [
                    'swiftCode' => 'CBVILT2XXXX',
                    'bankName' => 'AB SEB bankas',
                    'accountNumber' => '********************',
                    'name' => 'Test name',
                    'identificationNumber' => '***********',
                    'currency' => 'EUR',
                    'country' => 'LT',
                    'bankCode' => '70440',
                    'assigned' => 'lb',
                    'branchCode' => null,
                ],
            ],
        ];
    }

    /** @dataProvider writeOffMonitoringDataProvider */
    public function testMonitoringOnWriteOff(
        ?string $writeOffStatus,
        ?string $expectedWriteOffStatus,
        ?Money $expectedAmountLeft,
        ?Money $collectionAmount,
        ?Money $amountToWriteOff,
        string $collectionType,
        string $expectedCollectionStatus,
        array $beneficiaryData = []
    ): void {
        $client = ($this->fixturesHelper->createClientNatural())->setCode('*********');
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());

        $account = $this->fixturesHelper->createAccount($client);
        $this->accountManager->fill(new Money(1_000, 'GEL'), $account);
        $this->entityManager->flush();

        $this->createCollection($collectionType, $client, $collectionAmount, $licensedPartner, $beneficiaryData);
        $this->entityManager->flush();

        /** @var Collection $collection */
        $collection = $this->collectionRepository->findOneBy(['client' => $client]);
        $this->checkMonitoringClientOnWriteOff($collection);

        // Clone the writeOffManager to keep it unaffected after mocking processor
        $writeOffManager = clone $this->writeOffManager;
        $this->mockWriteOffProcessor($writeOffManager, $collection->getCollectionType());

        $writeOffManager->startWriteOffProcess($collection, null);
        $writeOffManager->writeOff($collection, $amountToWriteOff);
    }

    public function writeOffMonitoringDataProvider(): array
    {
        return [
            'CollectionGrs - writeOff status done' => [
                'writeOffStatus' => null,
                'expectedWriteOffStatus' => null,
                'expectedAmountLeft' => new Money(40, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(60, 'GEL'),
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
            'CollectionGrs - writeOff status null' => [
                'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                'expectedAmountLeft' => new Money(0, 'GEL'),
                'collectionAmount' => new Money(100, 'GEL'),
                'amountToWriteOff' => new Money(100, 'GEL'),
                'collectionType' => Collection::TYPE_GRS,
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
            ],
        ];
    }

    /** @dataProvider multipleWriteOffDataProvider */
    public function testMultipleWriteOff(
        Money $accountMoney,
        ?Money $collectionAmount,
        array $writeOffs,
        string $collectionType,
        array $beneficiaryData = []
    ): void {
        $client = ($this->fixturesHelper->createClientNatural())->setCode('*********');
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());

        $account = $this->fixturesHelper->createAccount($client);
        $this->accountManager->fill($accountMoney, $account);
        $this->entityManager->flush();

        $this->createCollection($collectionType, $client, $collectionAmount, $licensedPartner, $beneficiaryData);
        $this->entityManager->flush();

        /** @var Collection $collection */
        $collection = $this->collectionRepository->findOneBy(['client' => $client]);

        foreach ($writeOffs as $writeOff) {
            if (isset($writeOff['expectException'])) {
                $this->expectException($writeOff['expectException']['class']);
                $this->expectExceptionMessage($writeOff['expectException']['message']);
            }

            $this->writeOffManager->startWriteOffProcess($collection, null);
            $this->assertEquals(Collection::WRITE_OFF_STATUS_STARTED, $collection->getWriteOffStatus());

            $this->writeOffManager->writeOff($collection, $writeOff['amount']);
            $this->assertEquals($writeOff['expectedAmountLeft'], $collection->getAmountLeftMoney());
            $this->assertEquals($writeOff['expectedWriteOffStatus'], $collection->getWriteOffStatus());
            $this->assertEquals($writeOff['expectedCollectionStatus'], $collection->getStatus());

            $this->assertEquals($writeOff['amount'], $collection->getTransfers()->last()->getAmountMoney());
        }

        $this->assertCount(count($writeOffs), $collection->getTransfers());
    }

    public function multipleWriteOffDataProvider(): array
    {
        return [
            'CollectionGrs - Several write offs to transfer all collection amount' => [
                'accountAmount' => new Money(1_000, 'GEL'),
                'collectionAmount' => new Money(400, 'GEL'),
                'writeOffs' => [[
                    'amount' => new Money(100, 'GEL'),
                    'expectedAmountLeft' => new Money(300, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(200, 'GEL'),
                    'expectedAmountLeft' => new Money(100, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(40, 'GEL'),
                    'expectedAmountLeft' => new Money(60, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(60, 'GEL'),
                    'expectedAmountLeft' => new Money(0, 'GEL'),
                    'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ]],
                'collectionType' => Collection::TYPE_GRS,
            ],
            'CollectionGrs - Several write offs to transfer more then client has on balance' => [
                'accountMoney' => new Money(20, 'GEL'),
                'collectionAmount' => new Money(30, 'GEL'),
                'writeOffs' => [[
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => new Money(20, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => new Money(10, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => new Money(0, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                    'expectException' => [
                        'class' => DebtBundleException::class,
                        'message' => 'Available balance is not positive',
                    ],
                ]],
                'collectionType' => Collection::TYPE_GRS,
            ],
            'CollectionGrs - Several write offs when collection has money but amount null' => [
                'accountMoney' => new Money(1_000, 'GEL'),
                'collectionAmount' => null,
                'writeOffs' => [[
                    'amount' => new Money(100, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(100, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(100, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ]],
                'collectionType' => Collection::TYPE_GRS,
            ],
            'CollectionGrs - Several write offs when collection has money but amount null, write off more than balance' => [
                'accountMoney' => new Money('20.01', 'GEL'),
                'collectionAmount' => null,
                'writeOffs' => [[
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(10, 'GEL'),
                    'expectedAmountLeft' => null,
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                    'expectException' => [
                        'class' => DebtBundleException::class,
                        'message' => 'Available balance is not positive',
                    ],
                ]],
                'collectionType' => Collection::TYPE_GRS,
            ],
            'CollectionManual - Several write offs to transfer all collection amount' => [
                'accountAmount' => new Money(1_000, 'GEL'),
                'collectionAmount' => new Money(400, 'GEL'),
                'writeOffs' => [[
                    'amount' => new Money(100, 'GEL'),
                    'expectedAmountLeft' => new Money(300, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(200, 'GEL'),
                    'expectedAmountLeft' => new Money(100, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(40, 'GEL'),
                    'expectedAmountLeft' => new Money(60, 'GEL'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(60, 'GEL'),
                    'expectedAmountLeft' => new Money(0, 'GEL'),
                    'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                    'expectedCollectionStatus' => CollectionManual::STATUS_DONE,
                ]],
                'collectionType' => Collection::TYPE_MANUAL,
            ],
            'CollectionManual - Several write offs to transfer all collection amount EUR' => [
                'accountAmount' => new Money(1_000, 'EUR'),
                'collectionAmount' => new Money(400, 'EUR'),
                'writeOffs' => [[
                    'amount' => new Money(100, 'EUR'),
                    'expectedAmountLeft' => new Money(300, 'EUR'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(200, 'EUR'),
                    'expectedAmountLeft' => new Money(100, 'EUR'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(40, 'EUR'),
                    'expectedAmountLeft' => new Money(60, 'EUR'),
                    'expectedWriteOffStatus' => null,
                    'expectedCollectionStatus' => CollectionManual::STATUS_ACTIVE,
                ], [
                    'amount' => new Money(60, 'EUR'),
                    'expectedAmountLeft' => new Money(0, 'EUR'),
                    'expectedWriteOffStatus' => Collection::WRITE_OFF_STATUS_DONE,
                    'expectedCollectionStatus' => CollectionManual::STATUS_DONE,
                ]],
                'collectionType' => Collection::TYPE_MANUAL,
                'beneficiaryData' => [
                    'swiftCode' => 'CBVILT2XXXX',
                    'bankName' => 'AB SEB bankas',
                    'accountNumber' => '********************',
                    'name' => 'Test name',
                    'identificationNumber' => '***********',
                    'currency' => 'EUR',
                    'country' => 'LT',
                    'bankCode' => '70440',
                    'assigned' => 'lb',
                    'branchCode' => null,
                ],
            ],
        ];
    }

    /** @dataProvider startWriteOffExceptionsDataProvider */
    public function testStartWriteOffExceptions(
        string $expectedExceptionMessage,
        string $collectionNumberToWriteOff,
        array $collectionsData
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collections = $this->createCollectionsFromData($collectionsData, $client, $licensedPartner);
        $collectionToWriteOff = $collections[$collectionNumberToWriteOff];

        $this->entityManager->flush();

        $this->expectException(DebtBundleException::class);
        $this->expectExceptionMessage($expectedExceptionMessage);

        $this->writeOffManager->startWriteOffProcess($collectionToWriteOff, $debtClientSeizure);
    }

    public function startWriteOffExceptionsDataProvider(): array
    {
        $messageCollectionStatusIsNotActive = 'Collection status is not active';
        $messageCollectionWriteOffStatusHasNotAllowedValue = 'Collection write off status has not allowed value';
        $messageBalanceIsZeroOrLess = 'Available balance is not positive';
        $messageAnotherCollectionIsBeingWrittenOff = 'One of client collection is being written off';

        return [
            'Collection status is cancelled' => [
                $messageCollectionStatusIsNotActive,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_CANCELLED, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is returned' => [
                $messageCollectionStatusIsNotActive,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_RETURNED, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active' => [
                $messageBalanceIsZeroOrLess,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is started' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED],
                ],
            ],
            'Collection status is active, write off status is waiting' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
                ],
            ],
            'Collection status is active, write off status is failed' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                ],
            ],
            'Collection status is active, write off status is done' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is started' => [
                $messageAnotherCollectionIsBeingWrittenOff,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is failed' => [
                $messageAnotherCollectionIsBeingWrittenOff,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is done' => [
                $messageBalanceIsZeroOrLess,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is null' => [
                $messageBalanceIsZeroOrLess,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is started but status is cancelled' => [
                $messageBalanceIsZeroOrLess,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_CANCELLED, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null, another collection write off status is failed but status is cancelled' => [
                $messageBalanceIsZeroOrLess,
                '2',
                [
                    ['number' => '1', 'status' => Collection::STATUS_CANCELLED, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                    ['number' => '2', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
        ];
    }

    /** @dataProvider writeOffExceptionsDataProvider */
    public function testWriteOffExceptions(
        string $expectedExceptionMessage,
        string $collectionNumberToWriteOff,
        ?Money $writeOffAmount,
        array $collectionsData
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collections = $this->createCollectionsFromData($collectionsData, $client, $licensedPartner);
        $collectionToWriteOff = $collections[$collectionNumberToWriteOff];

        $this->entityManager->flush();

        $this->expectException(DebtBundleException::class);
        $this->expectExceptionMessage($expectedExceptionMessage);

        $this->writeOffManager->writeOff($collectionToWriteOff, $writeOffAmount);
    }

    public function writeOffExceptionsDataProvider(): array
    {
        $messageCollectionStatusIsNotActive = 'Collection status is not active';
        $messageCollectionWriteOffStatusHasNotAllowedValue = 'Collection write off status has not allowed value';

        return [
            'Collection status is cancelled' => [
                $messageCollectionStatusIsNotActive,
                '1',
                null,
                [
                    ['number' => '1', 'status' => Collection::STATUS_CANCELLED, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is returned' => [
                $messageCollectionStatusIsNotActive,
                '1',
                null,
                [
                    ['number' => '1', 'status' => Collection::STATUS_RETURNED, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is null' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                null,
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
                ],
            ],
            'Collection status is active, write off status is done' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                null,
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE],
                ],
            ],
            'Collection status is active, write off status is failed' => [
                $messageCollectionWriteOffStatusHasNotAllowedValue,
                '1',
                null,
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                ],
            ],
            'Collection status is active, write off status is started' => [
                'Write-off amount "100.12 GEL" is greater than available balance "0.00 GEL". Collection number "1"',
                '1',
                new Money('100.12', 'GEL'),
                [
                    ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED],
                ],
            ],
        ];
    }

    /** @dataProvider failWriteOffDataProvider */
    public function testFailWriteOff(
        string $expectedCollectionStatus,
        string $expectedCollectionWriteOffStatus,
        array $expectedCommonHolds,
        array $collectionsData,
        array $accountsMoney,
        array $targetCollectionHolds,
        array $commonHolds
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $accounts = [];
        foreach ($accountsMoney as $number => $accountMoney) {
            $accounts[$number] = $this->fixturesHelper->createAccount($client, $number, $number);
            $this->fixturesHelper->createAccountPartner($accounts[$number], LicensedPartner::PAYSERA_GEORGIA);

            $this->accountManager->fill($accountMoney, $accounts[$number]);
        }

        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collections = [];
        foreach ($collectionsData as $number => $collectionData) {
            $collections[] = $this->fixturesHelper
                ->createDebtCollectionGrs($number, $debtClientCollection, $licensedPartner)
                ->setAmountMoney($collectionData['amount'])
                ->setAmountLeftMoney($collectionData['amount'])
                ->setStatus($collectionData['status'])
                ->setWriteOffStatus($collectionData['writeOffStatus'])
            ;
        }

        $this->entityManager->flush();
        $targetCollection = $collections[0];

        foreach ($targetCollectionHolds as $accountNumber => $holdAmount) {
            $hold = $this->holdHelper->createHold($accounts[$accountNumber], $holdAmount, 'Test');
            $targetCollection->addHold($hold);
        }

        foreach ($commonHolds as $accountNumber => $holdAmount) {
            $hold = $this->holdHelper->createHold($accounts[$accountNumber], $holdAmount, 'Test');
            $debtClientCollection->addHold($hold);
        }

        $this->entityManager->flush();

        $this->writeOffManager->failWriteOff($targetCollection);
        $this->entityManager->flush();

        $this->assertEquals($expectedCollectionStatus, $targetCollection->getStatus());
        $this->assertEquals($expectedCollectionWriteOffStatus, $targetCollection->getWriteOffStatus());

        $targetCollectionHolds = $this->holdHelper->filterActiveHolds($targetCollection->getHolds()->toArray());
        $this->assertCount(0, $targetCollectionHolds, 'Target collection holds should be empty');

        $commonHolds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());
        $commonHoldsArray = [];
        foreach ($commonHolds as $hold) {
            $commonHoldsArray[$hold->getAccount()->getNumber()] = $hold->getAmountMoney();
        }

        $this->assertEquals($expectedCommonHolds, $commonHoldsArray, 'Common holds are not as expected');
    }

    public function failWriteOffDataProvider(): array
    {
        return [
            'Success case. One collection' => [
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                'expectedCollectionWriteOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                'expectedCommonHolds' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(200, 'GEL'),
                ],
                'collectionsData' => [
                    'collection1' => [
                        'amount' => new Money(300, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    ],
                ],
                'accountsMoney' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(300, 'GEL'),
                ],
                'targetCollectionHolds' => [
                    'account1' => new Money(50, 'GEL'),
                    'account2' => new Money(150, 'GEL'),
                ],
                'commonHolds' => [],
            ],
            'Success case. One collection, no collection holds' => [
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                'expectedCollectionWriteOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                'expectedCommonHolds' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(200, 'GEL'),
                ],
                'collectionsData' => [
                    'collection1' => [
                        'amount' => new Money(300, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    ],
                ],
                'accountsMoney' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(300, 'GEL'),
                ],
                'targetCollectionHolds' => [],
                'commonHolds' => [],
            ],
            'Success case. Two collections' => [
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                'expectedCollectionWriteOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                'expectedCommonHolds' => [
                    'account1' => new Money(150, 'GEL'),
                    'account2' => new Money(350, 'GEL'),
                ],
                'collectionsData' => [
                    'collection1' => [
                        'amount' => new Money(300, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    ],
                    'collection2' => [
                        'amount' => new Money(200, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => null,
                    ],
                ],
                'accountsMoney' => [
                    'account1' => new Money(150, 'GEL'),
                    'account2' => new Money(350, 'GEL'),
                ],
                'targetCollectionHolds' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(200, 'GEL'),
                ],
                'commonHolds' => [
                    'account1' => new Money(50, 'GEL'),
                    'account2' => new Money(150, 'GEL'),
                ],
            ],
            'Success case. Two collections. Not enough money' => [
                'expectedCollectionStatus' => Collection::STATUS_ACTIVE,
                'expectedCollectionWriteOffStatus' => Collection::WRITE_OFF_STATUS_FAILED,
                'expectedCommonHolds' => [
                    'account1' => new Money(500, 'GEL'),
                    'account2' => new Money(500, 'GEL'),
                ],
                'collectionsData' => [
                    'collection1' => [
                        'amount' => new Money(300, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    ],
                    'collection2' => [
                        'amount' => new Money(200, 'GEL'),
                        'status' => Collection::STATUS_ACTIVE,
                        'writeOffStatus' => null,
                    ],
                ],
                'accountsMoney' => [
                    'account1' => new Money(100, 'GEL'),
                    'account2' => new Money(300, 'GEL'),
                ],
                'targetCollectionHolds' => [
                    'account1' => new Money(50, 'GEL'),
                    'account2' => new Money(150, 'GEL'),
                ],
                'commonHolds' => [
                    'account1' => new Money(50, 'GEL'),
                    'account2' => new Money(150, 'GEL'),
                ],
            ],
        ];
    }

    /** @dataProvider failWriteOffExceptionsDataProvider */
    public function testFailWriteOffExceptions(string $expectedExceptionMessage, array  $collectionData): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collections = $this->createCollectionsFromData([$collectionData], $client, $licensedPartner);
        $collection = $collections[array_key_first($collections)];

        $this->entityManager->flush();

        $this->expectException(DebtBundleException::class);
        $this->expectExceptionMessage($expectedExceptionMessage);

        $this->writeOffManager->failWriteOff($collection);
    }

    public function failWriteOffExceptionsDataProvider(): array
    {
        $messageCollectionStatusIsNotActive = 'Collection status is not active';
        $messageCollectionWriteOffStatusHasNotAllowedValue = 'Collection write off status has not allowed value';

        return [
            'Collection status is cancelled' => [
                'expectedExceptionMessage' => $messageCollectionStatusIsNotActive,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_CANCELLED, 'writeOffStatus' => null],
            ],
            'Collection status is returned' => [
                'expectedExceptionMessage' => $messageCollectionStatusIsNotActive,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_RETURNED, 'writeOffStatus' => null],
            ],
            'Collection status is active, write off status is null' => [
                'expectedExceptionMessage' => $messageCollectionWriteOffStatusHasNotAllowedValue,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => null],
            ],
            'Collection status is active, write off status is waiting' => [
                'expectedExceptionMessage' => $messageCollectionWriteOffStatusHasNotAllowedValue,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
            ],
            'Collection status is active, write off status is failed' => [
                'expectedExceptionMessage' => $messageCollectionWriteOffStatusHasNotAllowedValue,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
            ],
            'Collection status is active, write off status is done' => [
                'expectedExceptionMessage' => $messageCollectionWriteOffStatusHasNotAllowedValue,
                'collectionData' => ['number' => '1', 'status' => Collection::STATUS_ACTIVE, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_DONE],
            ],
        ];
    }

    /**
     * @param array $collectionsData
     * @param Client $client
     * @param LicensedPartner $licensedPartner
     * @return CollectionGrs[]
     */
    private function createCollectionsFromData(
        array $collectionsData,
        Client $client,
        LicensedPartner $licensedPartner
    ): array {
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $collections = [];

        foreach ($collectionsData as $data) {
            $collections[$data['number']] = $this->fixturesHelper->createDebtCollectionGrs(
                $data['number'],
                $debtClientCollection,
                $licensedPartner,
                $data['status'],
                $data['writeOffStatus']
            );
        }

        return $collections;
    }

    private function createCommonCollection(Client $client, ?Money $amount): CommonCollection
    {
        return (new CommonCollectionGrs())
            ->setPriority(1)
            ->setReceiverAccount('*********')
            ->setReceiverCode($this->treasuryBic)
            ->setOperDate(new DateTimeImmutable())
            ->setExternalStatus(1)
            ->setNote(null)
            ->setThirdPersonId(null)
            ->setThirdPersonIdAlt(null)
            ->setThirdPersonName(null)
            ->setClientId($client->getId())
            ->setAmount($amount ? $amount->getAmount() : null)
            ->setCurrency($amount ? $amount->getCurrency() : null)
            ->setNumber('**********')
        ;
    }

    private function createCollection(
        string $collectionType,
        ClientNatural $client,
        ?Money $collectionAmount,
        LicensedPartner $licensedPartner,
        array $beneficiaryData = []
    ): void {
        if ($collectionType === Collection::TYPE_GRS) {
            $this->collectionProcessorManager->create($this->createCommonCollection($client, $collectionAmount));

            return;
        }

        if ($collectionType === Collection::TYPE_MANUAL) {
            $this->fixturesHelper->createBankSwift(
                $beneficiaryData['swiftCode'] ?? 'BAGAGE22XXX',
                $beneficiaryData['country'] ?? 'GE',
                $beneficiaryData['bankCode'] ?? 'BG',
                $beneficiaryData['assigned'] ?? 'bg',
                $beneficiaryData['bankName'] ?? 'JSC BANK OF GEORGIA',
                $beneficiaryData['branchCode'] ?? null,
                $beneficiaryData['currency'] ?? 'GEL'
            );
            $collection = $this->fixturesHelper->createDebtCollectionManual(
                '**********',
                $this->debtClientCollectionManager->getOrCreateDebtClientCollection($client),
                $licensedPartner,
                CollectionManual::STATUS_REGISTERED,
                null,
                5,
                (float) $collectionAmount->getAmount(),
                $collectionAmount->getCurrency(),
                $beneficiaryData
            );
            $workflow = $this->workflowRegistry->get($collection);
            $workflow->apply($collection, CollectionManual::TRANSITION_TO_CONFIRMED);
            $workflow->apply($collection, CollectionManual::TRANSITION_TO_ACTIVE);
        }
    }

    private function checkMonitoringClientOnWriteOff(Collection $collection): void
    {
        $this->monitoringClient
            ->expects($this->exactly(2))
            ->method('writeValue')
            ->withConsecutive(
                $this->getMonitoringClientParameters(
                    true,
                    'write_off_started',
                    $collection->getOrganizationPriority()
                ),
                $this->getMonitoringClientParameters(
                    true,
                    'write_off_finished',
                    $collection->getOrganizationPriority()
                )
            )
        ;
    }

    private function getMonitoringClientParameters(
        ?bool $value,
        string $problem,
        int $organisationPriority
    ): array {
        return [
            'evpbank.debt_bundle',
            (int)$value,
            [
                'problem' => $problem,
                'organizationPriority' => $organisationPriority,
            ],
        ];
    }

    private function mockWriteOffProcessor(WriteOffManager $writeOffManager, string $collectionType): void
    {
        $reflectionClass = new \ReflectionClass($writeOffManager);
        $reflectionMethod = $reflectionClass->getMethod('getWriteOffProcessor');
        $reflectionMethod->setAccessible(true);

        $processorMock = $this->createMock(WriteOffProcessor::class);
        $processorMock->expects($this->once())
            ->method('writeOff');

        $reflectionMethod->invokeArgs($writeOffManager, [$collectionType]);

        $reflectionProperty = $reflectionClass->getProperty('writeOffProcessors');
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($writeOffManager, [$collectionType => $processorMock]);
    }

    private function mockCurrencyConverter(): void
    {
        $officialCurrencyConverter = new HardcodedTestCurrencyConverter();
        $officialCurrencyConverter->setRate('GEL', '0.34');

        $this->getContainer()->set(
            'evp_currency.currency_converter.official_by_partner.cached',
            $officialCurrencyConverter
        );
    }
}
