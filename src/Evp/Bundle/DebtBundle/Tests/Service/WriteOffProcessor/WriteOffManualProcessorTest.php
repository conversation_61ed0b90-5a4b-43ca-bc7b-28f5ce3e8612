<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\WriteOffProcessor;

use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\AccountRepository;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Service\LicensedPartnersManager;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Exception\WriteOffManualProcessorException;
use Evp\Bundle\DebtBundle\Exception\WriteOffProcessorException;
use Evp\Bundle\DebtBundle\Service\DebtTransferManager;
use Evp\Bundle\DebtBundle\Service\WriteOffProcessor\WriteOffManualProcessor;
use Evp\Bundle\DebtBundle\Worker\WriteOffCollectionAmount\ManualWriteOffCollectionAmountWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Component\GatewayCommon\BankAccount\Entity\AccountFilter;
use Evp\Component\Money\Money;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Traversable;

class WriteOffManualProcessorTest extends TestCase
{
    private const DATA_NUMBER = 'number';
    private const DATA_AMOUNT = 'amount';
    private const CURRENCY_GEL = 'GEL';
    private const TYPE_LOCAL = 'local';

    private MockObject $deferredJobPublisherMock;
    private MockObject $accountRepositoryMock;
    private MockObject $licensedPartnersManagerMock;
    private MockObject $debtTransferManagerMock;
    private MockObject $loggerMock;
    private WriteOffManualProcessor $processor;

    protected function setUp(): void
    {
        $this->deferredJobPublisherMock = $this->createMock(RemoteJobPublisherInterface::class);
        $this->accountRepositoryMock = $this->createMock(AccountRepository::class);
        $this->licensedPartnersManagerMock = $this->createMock(LicensedPartnersManager::class);
        $this->debtTransferManagerMock = $this->createMock(DebtTransferManager::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->processor = new WriteOffManualProcessor(
            $this->deferredJobPublisherMock,
            $this->accountRepositoryMock,
            $this->debtTransferManagerMock,
            $this->licensedPartnersManagerMock,
            $this->loggerMock,
        );
    }

    public function testStartWriteOffProcess(): void
    {
        $collection = new CollectionGrs();
        $collection->setNumber('123');
        $availableBalance = new Money(100, self::CURRENCY_GEL);

        $data = [
            self::DATA_NUMBER => $collection->getNumber(),
            self::DATA_AMOUNT => $availableBalance->getAmount()
        ];

        $this->deferredJobPublisherMock
            ->expects($this->once())
            ->method('publishJob')
            ->with(ManualWriteOffCollectionAmountWorker::JOB_KEY, $data)
        ;

        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('DebtBundle: collection amount job published in WriteOffManualProcessor.', [$data])
        ;

        $this->processor->startWriteOffProcess($collection, $availableBalance);
    }

    /**
     * @dataProvider dataProviderWriteOff
     */
    public function testWriteOff(
        bool $isAmountNullable,
        int $exactlyInfoWriteOffIsDone,
        string $collectionClass,
        int $amountMoney,
        bool $isZeroHoldsCount,
        int $exactlyFindByFilter,
        int $exactlyCollectAndProcess,
        int $exactlyGetPartnerCode,
        bool $areZeroAccountsFoundByFilter,
        string $partnerCode
    ): void {
        $clientId = 123;
        $covenanteeId = 222;
        $beneficiaryBankSwiftCode = 'CODE123';
        $beneficiaryBankName = 'bank_name';
        $beneficiaryName = 'beneficiaryName';
        $beneficiaryIdentificationNumber = '456';
        $beneficiaryAccountNumber = '3456';

        $client = new ClientNatural();
        $client->setId($clientId);
        $client->setCovenanteeId($covenanteeId);

        /** @var CollectionManual $collection */
        $collection = new $collectionClass();
        $collection->setClient($client);
        $collection->setNumber('789');
        $collection->setBeneficiaryBankSwiftCode($beneficiaryBankSwiftCode);
        $collection->setBeneficiaryBankName($beneficiaryBankName);
        $collection->setBeneficiaryName($beneficiaryName);
        $collection->setBeneficiaryIdentificationNumber($beneficiaryIdentificationNumber);
        $collection->setBeneficiaryAccountNumber($beneficiaryAccountNumber);
        if (!$isZeroHoldsCount) {
            $collection->setHolds(new ArrayCollection([new Hold()]));
        }

        $amount = new Money($amountMoney, self::CURRENCY_GEL);

        $beneficiaryParty = (new PartyIban())
            ->setBic($collection->getBeneficiaryBankSwiftCode())
            ->setBankName($collection->getBeneficiaryBankName())
            ->setIban($collection->getBeneficiaryAccountNumber())
            ->setName($collection->getBeneficiaryName())
            ->setCode($collection->getBeneficiaryIdentificationNumber())
        ;

        $accountFilter = (new AccountFilter())
            ->setOwnedByUserId($covenanteeId)
            ->setClosed(false)
            ->setActive(true)
            ->setType(self::TYPE_LOCAL)
        ;

        $accounts = [
            new Account(),
            new Account(),
        ];
        $accountForCollectAndProcess = $accounts[0];

        if ($areZeroAccountsFoundByFilter) {
            $accounts = [];
        }

        if ($areZeroAccountsFoundByFilter || $partnerCode !== LicensedPartner::PAYSERA_GEORGIA) {
            $this->loggerMock
                ->expects($this->once())
                ->method('error')
                ->with(
                    'DebtBundle exceptional case: no available accounts found!',
                    [$clientId]
                )
            ;

            $this->expectException(WriteOffManualProcessorException::class);
            $this->expectExceptionMessage('No available accounts found!');
        }

        $data = [
            self::DATA_NUMBER => $collection->getNumber(),
            self::DATA_AMOUNT => $amount->getAmount(),
        ];

        if ($isAmountNullable) {
            $amount = null;

            $this->loggerMock
                ->expects($this->once())
                ->method('error')
                ->with(
                    'DebtBundle exceptional case: null amount in WriteOffManualProcessor',
                    [$collection]
                )
            ;

            $this->expectException(WriteOffManualProcessorException::class);
            $this->expectExceptionMessage('Empty amount!');
        } else if (
            !$collection instanceof CollectionManual
            || !$amount->isPositive()
            || count($collection->getHolds()) === 0
        ) {
            $this->loggerMock
                ->expects($this->once())
                ->method('error')
                ->with(
                    'WriteOffProcessor: incorrect parameters given to process collection!',
                    [$collection, $amount]
                )
            ;

            $this->expectException(WriteOffProcessorException::class);
            $this->expectExceptionMessage('This collection cannot be processed!');
        }


        $this->accountRepositoryMock
            ->expects($this->exactly($exactlyFindByFilter))
            ->method('findByFilter')
            ->with($accountFilter)
            ->willReturn($accounts)
        ;

        $this->licensedPartnersManagerMock
            ->expects($this->exactly($exactlyGetPartnerCode))
            ->method('getPartnerCode')
            ->willReturn($partnerCode)
        ;

        $this->debtTransferManagerMock
            ->expects($this->exactly($exactlyCollectAndProcess))
            ->method('collectAndProcess')
            ->with(
                $accountForCollectAndProcess,
                $beneficiaryParty,
                $collection,
                $amount,
            )
        ;

        $this->loggerMock
            ->expects($this->exactly($exactlyInfoWriteOffIsDone))
            ->method('info')
            ->with('DebtBundle: writeOff is done in WriteOffManualProcessor.', [$data])
        ;

        $this->processor->writeOff($collection, $amount);
    }

    public function dataProviderWriteOff(): Traversable
    {
        yield 'Positive case' => $this->getWriteOffDefaults();

        yield 'Amount is nullable' => array_merge($this->getWriteOffDefaults(), [
            'isAmountNullable' => true,
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyFindByFilter' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,

        ]);

        yield 'Not CollectionManual - CollectionGrs' => array_merge($this->getWriteOffDefaults(), [
            'collectionClass' => CollectionGrs::class,
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyFindByFilter' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,
        ]);

        yield 'Not CollectionManual - CollectionGeb' => array_merge($this->getWriteOffDefaults(), [
            'collectionClass' => CollectionGeb::class,
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyFindByFilter' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,
        ]);

        yield 'amountMoney not positive' => array_merge($this->getWriteOffDefaults(), [
            'amountMoney' => -100,
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyFindByFilter' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,
        ]);

        yield 'Zero holds' => array_merge($this->getWriteOffDefaults(), [
            'isZeroHoldsCount' => true,
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyFindByFilter' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,
        ]);

        yield 'Zero Accounts Found By Filter' => array_merge($this->getWriteOffDefaults(), [
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 0,
            'areZeroAccountsFoundByFilter' => true,
        ]);

        yield 'Partner code is not Georgia' => array_merge($this->getWriteOffDefaults(), [
            'exactlyInfoWriteOffIsDone' => 0,
            'exactlyCollectAndProcess' => 0,
            'exactlyGetPartnerCode' => 2,
            'partnerCode' => 'not_georgia'
        ]);
    }

    private function getWriteOffDefaults(): array
    {
        return [
            'isAmountNullable' => false,
            'exactlyInfoWriteOffIsDone' => 1,
            'collectionClass' => CollectionManual::class,
            'amountMoney' => 100,
            'isZeroHoldsCount' => false,
            'exactlyFindByFilter' => 1,
            'exactlyCollectAndProcess' => 1,
            'exactlyGetPartnerCode' => 2,
            'areZeroAccountsFoundByFilter' => false,
            'partnerCode' => LicensedPartner::PAYSERA_GEORGIA
        ];
    }
}
