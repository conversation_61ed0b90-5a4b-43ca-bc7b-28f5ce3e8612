<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\WriteOffProcessor;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Exception\WriteOffGebProcessorException;
use Evp\Bundle\DebtBundle\Exception\WriteOffProcessorException;
use Evp\Bundle\DebtBundle\Service\DebtTransferManager;
use Evp\Bundle\DebtBundle\Service\WriteOffProcessor\WriteOffGebProcessor;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteEventPublisher;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class WriteOffGebProcessorTest extends PersistableWebTestCase
{
    private WriteOffGebProcessor $writeOffGebProcessor;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private AccountNumberGenerator $accountNumberGenerator;
    private AccountNumberGenerator $ibanAccountNumberGenerator;

    /**
     * @var RemoteEventPublisher|MockObject
     */
    private $eventPublisher;

    /**
     * @var DebtTransferManager|MockObject
     */
    private $debtTransferManager;

    /**
     * @var RemoteJobPublisherInterface|MockObject
     */
    private $jobPublisher;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->eventPublisher = $this->createMock(RemoteEventPublisher::class);
        $this->getContainer()->set('evp_debt.publisher.geb_remote_event', $this->eventPublisher);
        $this->jobPublisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set('evp_rabbit_mq_extension.deferred_remote_job_publisher', $this->jobPublisher);
        $this->debtTransferManager = $this->createMock(DebtTransferManager::class);
        $this->getContainer()->set('evp_debt.service.debt_transfer_manager', $this->debtTransferManager);
        $this->writeOffGebProcessor = $this->getContainer()->get('evp_debt.write_off_processor.geb');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator');
        $this->ibanAccountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator.iban');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testStartWriteOffProcess()
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '***********',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );
        $amount = new Money('1000', 'GEL');

        $this->jobPublisher->expects($this->once())->method('publishJob');

        $this->writeOffGebProcessor->startWriteOffProcess($collection, $amount);
    }

    /**
     * @dataProvider providerWriteOff
     * @param Money|null $amount
     * @param array $accountData
     * @param string $partnerCode
     * @param Collection|null $expectedCollection
     * @param bool|null $collectionWithHolds
     * @param string|null $exception
     * @param string|null $exceptionMessage
     * @return void
     */
    public function testWriteOff(
        ?Money $amount,
        array $accountData,
        string $partnerCode,
        ?Collection $expectedCollection = null,
        ?bool $collectionWithHolds = null,
        ?string $exception = null,
        ?string $exceptionMessage = null
    ) {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '***********',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );

        if ($expectedCollection !== null) {
            $collection = $expectedCollection;
        }

        if (
            array_key_exists('type', $accountData)
            && array_key_exists('active', $accountData)
            && array_key_exists('closed', $accountData)
        ) {
            $account = $this->fixturesHelper->createAccount(
                $client,
                $this->accountNumberGenerator->generateNumber(1),
                $this->ibanAccountNumberGenerator->generateNumber(1),
                [],
                $accountData['type'],
                $accountData['active'],
                $accountData['closed']
            );

            if ($collectionWithHolds) {
                $collection->addHold($this->fixturesHelper->createHold($account, $amount));
            }
        }

        $this->fixturesHelper->createPartnerClient($client, $partnerCode);

        if ($expectedCollection === null) {
            $this->entityManager->flush();
        }

        if ($exception === null) {
            if ($amount === null) {
                $this->eventPublisher->expects($this->never())->method('publishEvent');
                $this->debtTransferManager->expects($this->never())->method('collectAndProcess');
            } else {
                $this->eventPublisher->expects($this->once())->method('publishEvent');
                $this->debtTransferManager->expects($this->once())->method('collectAndProcess');
            }
        } else {
            $this->expectException($exception);
            $this->expectExceptionMessage($exceptionMessage);
        }

        // @phpstan-ignore-next-line
        $this->writeOffGebProcessor->writeOff($collection, $amount);
    }

    public function providerWriteOff(): array
    {
        return [
            'incorrect collection type' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [
                    'closed' => false,
                    'active' => true,
                    'type' => Account::TYPE_LOCAL,
                ],
                'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                'expectedCollection' => new CollectionGrs(),
                'collectionWithHolds' => true,
                'exception' => WriteOffProcessorException::class,
                'exceptionMessage' => 'This collection cannot be processed!',
            ],
            'collection without holds' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [
                    'closed' => false,
                    'active' => true,
                    'type' => Account::TYPE_LOCAL,
                ],
                'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                'expectedCollection' => null,
                'collectionWithHolds' => false,
                'exception' => WriteOffProcessorException::class,
                'exceptionMessage' => 'This collection cannot be processed!',
            ],
            'not paysera_ge licensed partner' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [
                    'closed' => false,
                    'active' => true,
                    'type' => Account::TYPE_LOCAL,
                ],
                'partnerCode' => LicensedPartner::PAYSERA_LITHUANIA,
                'expectedCollection' => null,
                'collectionWithHolds' => true,
                'exception' => WriteOffGebProcessorException::class,
                'exceptionMessage' => 'No available accounts found!',
            ],
            'paysera_ge, but not active accounts' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [
                    'closed' => true,
                    'active' => false,
                    'type' => Account::TYPE_LOCAL,
                ],
                'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                'expectedCollection' => null,
                'collectionWithHolds' => true,
                'exception' => WriteOffGebProcessorException::class,
                'exceptionMessage' => 'No available accounts found!',
            ],
            'paysera_ge, active accounts, but no accounts with local type' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [
                    'closed' => false,
                    'active' => true,
                    'type' => Account::TYPE_TRANSIT,
                ],
                'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                'expectedCollection' => null,
                'collectionWithHolds' => true,
                'exception' => WriteOffGebProcessorException::class,
                'exceptionMessage' => 'No available accounts found!',
            ],
            'no accounts at all' => [
                'amount' => new Money('100', 'GEL'),
                'accountData' => [],
                'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                'expectedCollection' => null,
                'collectionWithHolds' => true,
                'exception' => WriteOffProcessorException::class,
                'exceptionMessage' => 'This collection cannot be processed!',
            ],
        ];
    }
}
