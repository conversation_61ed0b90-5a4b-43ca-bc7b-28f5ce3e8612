<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\WriteOffProcessor;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Exception\WriteOffGrsProcessorException;
use Evp\Bundle\DebtBundle\Exception\WriteOffProcessorException;
use Evp\Bundle\DebtBundle\Service\DebtTransferManager;
use Evp\Bundle\DebtBundle\Service\WriteOffProcessor\WriteOffGrsProcessor;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class WriteOffGrsProcessorTest extends PersistableWebTestCase
{
    private WriteOffGrsProcessor $writeOffGrsProcessor;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private AccountNumberGenerator $accountNumberGenerator;
    private AccountNumberGenerator $ibanAccountNumberGenerator;

    /**
     * @var RemoteJobPublisherInterface|MockObject
     */
    private RemoteJobPublisherInterface $publisher;
    /**
     * @var DebtTransferManager|MockObject
     */
    private DebtTransferManager $debtTransferManager;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->publisher = $this->createMock(RemoteJobPublisherInterface::class);
        $this->getContainer()->set(
            'evp_grs.publisher.grs_deferred_remote_job_publisher',
            $this->publisher
        );
        $this->debtTransferManager = $this->createMock(DebtTransferManager::class);
        $this->getContainer()->set(
            'evp_debt.service.debt_transfer_manager',
            $this->debtTransferManager
        );

        $this->writeOffGrsProcessor = $this->getContainer()->get('evp_debt.write_off_processor.grs');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator');
        $this->ibanAccountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator.iban');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testStartWriteOffProcess(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '***********',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );
        $amount = new Money('1000', 'GEL');

        $this->publisher
            ->expects($this->once())
            ->method('publishJob')
            ->with(
                'georgia_revenue_service.debt.check_collection_amount',
                [
                    'number' => $collection->getNumber(),
                    'amount' => $amount->getAmount(),
                ]
            )
        ;

        $this->writeOffGrsProcessor->startWriteOffProcess($collection, $amount);
    }

    /**
     * @param Money $amount
     * @param array $accounts
     * @param int $expectedAccountIndex
     * @param string $partnerCode
     * @param string $receiverCode
     * @param Collection|null $expectedCollection
     * @param bool|null $collectionWithHolds
     * @param string|null $exception
     * @param string|null $exceptionMessage
     *
     * @dataProvider writeOffDataProvider
     */
    public function testWriteOff(
        ?Money $amount,
        array $accounts,
        int $expectedAccountIndex,
        string $partnerCode,
        string $receiverCode,
        ?Collection $expectedCollection = null,
        ?bool $collectionWithHolds = null,
        ?string $exception = null,
        ?string $exceptionMessage = null
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '***********',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );

        if ($expectedCollection !== null) {
            $collection = $expectedCollection;
        }

        if ($receiverCode !== null) {
            $collection->setBeneficiaryBankSwiftCode($receiverCode);
        }

        foreach ($accounts as $key => $accountData) {
            $account = $this->fixturesHelper->createAccount(
                $client,
                $this->accountNumberGenerator->generateNumber($key),
                $this->ibanAccountNumberGenerator->generateNumber($key),
                [],
                $accountData['type'],
                $accountData['active'],
                $accountData['closed']
            );

            if ($collectionWithHolds) {
                $collection->addHold($this->fixturesHelper->createHold($account, $amount));
            }
        }

        $this->fixturesHelper->createPartnerClient($client, $partnerCode);

        if ($expectedCollection === null) {
            $this->entityManager->flush();
        }

        if ($exception === null) {
            if ($amount === null) {
                $this->publisher
                    ->expects($this->never())
                    ->method('publishJob')
                ;

                $this->debtTransferManager
                    ->expects($this->never())
                    ->method('collectAndProcess')
                ;
            } else {
                $this->publisher
                    ->expects($this->once())
                    ->method('publishJob')
                    ->with(
                        'georgia_revenue_service.debt.collection_accomplish',
                        [
                            'number' => $collection->getNumber(),
                            'amount' => $amount->getAmount(),
                        ]
                    )
                ;

                $this->debtTransferManager
                    ->expects($this->once())
                    ->method('collectAndProcess')
                    ->with(
                        $client->getAccounts()[$expectedAccountIndex],
                        (new PartyAccountCountry())
                            ->setAccount($collection->getBeneficiaryAccountNumber())
                            ->setCountry('GE')
                            ->setName('State Treasury')
                            ->setBic($collection->getBeneficiaryBankSwiftCode()),
                        $collection,
                        $amount
                    )
                ;
            }
        } else {
            $this->expectException($exception);
            $this->expectExceptionMessage($exceptionMessage);
        }

        $this->writeOffGrsProcessor->writeOff($collection, $amount);
    }

    public function writeOffDataProvider(): array
    {
        return [
            'Case 1: Incorrect collection type' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                new CollectionGeb(),
                true,
                WriteOffProcessorException::class,
                'This collection cannot be processed!',
            ],
            'Case 2: Null amount given' => [
                null,
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
            ],
            'Case 3: Collection without holds' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                false,
                WriteOffProcessorException::class,
                'This collection cannot be processed!',
            ],
            'Case 4: Not paysera_ge licensed partner' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_LITHUANIA,
                'TRESGE22',
                null,
                true,
                WriteOffGrsProcessorException::class,
                'No available accounts found!',
            ],
            'Case 5: paysera_ge, but not active accounts' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => true,
                        'active' => false,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => true,
                        'active' => false,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
                WriteOffGrsProcessorException::class,
                'No available accounts found!',
            ],
            'Case 6: paysera_ge, active accounts, but no accounts with local type' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_TRANSIT,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_TECHNICAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
                WriteOffGrsProcessorException::class,
                'No available accounts found!',
            ],
            'Case 7: No accounts at all' => [
                new Money('100', 'GEL'),
                [],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
                WriteOffProcessorException::class,
                'This collection cannot be processed!',
            ],
            'Case 8: Collection with incorrect receiver code' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE23',
                null,
                true,
                WriteOffGrsProcessorException::class,
                'Not expected treasury payment BIC!',
            ],
            'Case 9: Main account index 0' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                0,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
            ],
            'Case 10: Main account index 1' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_TECHNICAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                1,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
            ],
            'Case 11: Main account index 2' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => true,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => true,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                2,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
            ],
            'Case 12: Main account index 3' => [
                new Money('100', 'GEL'),
                [
                    [
                        'closed' => true,
                        'active' => true,
                        'type' => Account::TYPE_TRANSIT,
                    ],
                    [
                        'closed' => true,
                        'active' => true,
                        'type' => Account::TYPE_TECHNICAL,
                    ],
                    [
                        'closed' => false,
                        'active' => false,
                        'type' => Account::TYPE_LOCAL,
                    ],
                    [
                        'closed' => false,
                        'active' => true,
                        'type' => Account::TYPE_LOCAL,
                    ],
                ],
                3,
                LicensedPartner::PAYSERA_GEORGIA,
                'TRESGE22',
                null,
                true,
            ],
        ];
    }
}
