<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankChargeBundle\Entity\Charge;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Service\Commission\ChargeCommissionService;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class ChargeCommissionServiceTest extends PersistableWebTestCase
{
    private ChargeCommissionService $chargeCommissionService;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->chargeCommissionService = $this->getContainer()->get('evp_debt.service.commission.charge_commission_service');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    public function testSuccessfulCharge(): void
    {
        $account = $this->prepareAccount();
        $commission = $this->prepareCommission($account, 400);
        $this->entityManager->flush();
        $this->assertEquals(null, $commission->getCharge());
        $this->chargeCommissionService->charge($commission);
        $this->entityManager->flush();

        $this->assertEquals(
            new Money(600, 'GEL'),
            $account->getCurrencyAccount('GEL')->getInternalMainAccount()->getBalanceMoney()
        );
        $this->assertEquals(
            new Money(400, 'GEL'),
            $account->getCurrencyAccount('GEL')->getInternalCommissionAccount()->getBalanceMoney()
        );

        /** @var Charge[] $charges */
        $charges = $this->entityManager->getRepository('EvpBankChargeBundle:Charge')->findAll();
        $this->assertCount(1, $charges);
        $this->assertEquals(new Money(400, 'GEL'), $charges[0]->getAmountMoney());
        $this->assertEquals(Charge::STATUS_DONE, $charges[0]->getStatus());
        $this->assertEquals('Commission payment for collection order transfer (123)', $charges[0]->getDetails());
        $this->assertEquals($commission->getCharge(), $charges[0]);
    }

    public function testWaitingFundsCharge(): void
    {
        $account = $this->prepareAccount();
        $commission = $this->prepareCommission($account, 2000);
        $this->chargeCommissionService->charge($commission);
        $this->entityManager->flush();

        /** @var Charge[] $charges */
        $charges = $this->entityManager->getRepository('EvpBankChargeBundle:Charge')->findAll();
        $this->assertCount(1, $charges);
        $this->assertEquals(new Money(2000, 'GEL'), $charges[0]->getAmountMoney());
        $this->assertEquals(Charge::STATUS_WAITING_FUNDS, $charges[0]->getStatus());
    }

    private function prepareAccount(): Account
    {
        $client = $this->fixturesHelper->createClientNatural();
        $account = $this->fixturesHelper->createAccount($client);
        $account->addCurrencyAccount(
            $this->fixturesHelper->createCurrencyAccount(
                'GEL',
                $this->fixturesHelper->createInternalMainAccount(new Money(1000, 'GEL'))
            )
        );
        $this->entityManager->flush();

        return $account;
    }

    private function prepareCommission(Account $account, int $amount): Commission
    {
        $collection = (new CollectionGrs())
            ->setNumber('123')
            ->addTransfer((new TransferOutBank())->setCreditAccount($account));
        $commission = (new Commission())->setAmountMoney(new Money($amount, 'GEL'));
        $commission->setCollection($collection);

        return $commission;
    }
}
