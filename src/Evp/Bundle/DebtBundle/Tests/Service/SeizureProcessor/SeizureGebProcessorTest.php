<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\SeizureProcessor;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use Evp\Bundle\CurrencyBundle\Service\CurrencyConverterInterface;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\SeizureGeb;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Repository\SeizureGebRepository;
use Evp\Bundle\DebtBundle\Service\AmountMoneyResolver;
use Evp\Bundle\DebtBundle\Service\SeizureProcessor\SeizureGebProcessor;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGeb;
use Evp\Component\DebtCommon\Entity\CommonResponse;
use Evp\Component\DebtCommon\Entity\Seizure\CancelSeizure\CommonCancelSeizureGeb;
use Evp\Component\DebtCommon\Entity\Seizure\ChangeSeizure\CommonChangeSeizureGeb;
use Evp\Component\DebtCommon\Entity\Seizure\Seizure\CommonSeizureGeb;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Traversable;

class SeizureGebProcessorTest extends PersistableWebTestCase
{
    private const CURRENCY_GEL = 'GEL';
    private const CURRENCY_EUR = 'EUR';

    /** @var EntityManagerInterface|MockObject  */
    private EntityManagerInterface $entityManagerMock;
    /** @var SeizureGebRepository|MockObject  */
    private SeizureGebRepository $seizureGebRepositoryMock;
    /** @var LicensedPartnerRepository|MockObject  */
    private LicensedPartnerRepository $licensedPartnerRepositoryMock;
    /** @var AmountMoneyResolver|MockObject  */
    private AmountMoneyResolver $amountMoneyResolverMock;
    private SeizureGebProcessor $processor;

    protected function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->seizureGebRepositoryMock = $this->createMock(SeizureGebRepository::class);
        $this->licensedPartnerRepositoryMock = $this->createMock(LicensedPartnerRepository::class);
        $this->amountMoneyResolverMock = $this->createMock(AmountMoneyResolver::class);
        $currencyConverter = $this->getContainer()->get('evp_currency.currency_converter.official.cached');

        $this->processor = new SeizureGebProcessor(
            $this->entityManagerMock,
            $this->seizureGebRepositoryMock,
            $this->licensedPartnerRepositoryMock,
            $this->amountMoneyResolverMock,
            $currencyConverter,
            LicensedPartner::PAYSERA_GEORGIA,
        );
    }

    /**
     * @dataProvider dataProviderCreate
     */
    public function testCreate(
        string $status,
        bool $willReturnObjFindOneByNumber,
        bool $willReturnObjFindOneByPartnerCode,
        int $exactlyFindOneByPartnerCode,
        int $exactlyResolve,
        bool $expectProcessorErrorException,
        bool $expectLicensedPartnerNotFoundException
    ): void {
        $number = '123';
        $priority = 456;

        $money = new Money(100, self::CURRENCY_GEL);
        $commonSeizure = new CommonSeizureGeb();
        $commonSeizure->setNumber($number);
        $commonSeizure->setAmount($money->getAmount());
        $commonSeizure->setCurrency($money->getCurrency());
        $commonSeizure->setPriority($priority);

        $client = new ClientNatural();

        $seizure = new SeizureGeb();
        $seizure->setStatus($status);

        $licensedPartner = new LicensedPartner();

        if ($expectProcessorErrorException) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_ALREADY_CANCELLED);
        }
        if ($expectLicensedPartnerNotFoundException) {
            $this->expectException(LicensedPartnerNotFoundException::class);
        }

        $this->seizureGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $seizure : null)
        ;

        $this->licensedPartnerRepositoryMock
            ->expects($this->exactly($exactlyFindOneByPartnerCode))
            ->method('findOneByPartnerCode')
            ->with(LicensedPartner::PAYSERA_GEORGIA)
            ->willReturn($willReturnObjFindOneByPartnerCode ? $licensedPartner : null)
        ;

        $this->amountMoneyResolverMock
            ->expects($this->exactly($exactlyResolve))
            ->method('resolve')
            ->with($money->getAmount(), $money->getCurrency())
            ->willReturn($money)
        ;

        $seizure = $this->processor->create($commonSeizure, $client);
        $this->assertInstanceOf(SeizureGeb::class, $seizure);
        $this->assertEquals($number, $seizure->getNumber());
        $this->assertEquals(Collection::PRIORITY_NATIONAL_ENFORCEMENT_BUREAU, $seizure->getOrganizationPriority());
        $this->assertEquals($money, $seizure->getAmountMoney());
        $this->assertSame($client, $seizure->getClient());
        $this->assertSame($licensedPartner, $seizure->getLicensedPartner());
        $this->assertEquals($priority, $seizure->getPriority());
    }

    public function dataProviderCreate(): Traversable
    {
        yield 'Positive seizure found' => $this->getDefaultsCreate();

        yield 'Positive seizure not found' => array_merge($this->getDefaultsCreate(), [
            'willReturnObjFindOneByNumber' => false,
        ]);

        yield 'Seizure is not active - expect exception' => array_merge($this->getDefaultsCreate(), [
            'status' => 'another status',
            'exactlyFindOneByPartnerCode' => 0,
            'exactlyResolve' => 0,
            'expectProcessorErrorException' => true,
        ]);

        yield 'Licensed partner not found - expect exception' => array_merge($this->getDefaultsCreate(), [
            'willReturnObjFindOneByPartnerCode' => false,
            'exactlyResolve' => 0,
            'expectLicensedPartnerNotFoundException' => true,
        ]);
    }

    private function getDefaultsCreate(): array
    {
        return [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => true,
            'willReturnObjFindOneByPartnerCode' => true,
            'exactlyFindOneByPartnerCode' => 1,
            'exactlyResolve' => 1,
            'expectProcessorErrorException' => false,
            'expectLicensedPartnerNotFoundException' => false,
        ];
    }

    /**
     * @dataProvider dataProviderCancel
     */
    public function testCancel(
        string $status,
        bool $willReturnObjFindOneByNumber,
        bool $expectExceptionNotFound,
        bool $expectExceptionCanceled
    ): void {
        $number = '123';
        $commonCancelSeizureGeb = new CommonCancelSeizureGeb();
        $commonCancelSeizureGeb->setNumber($number);
        $seizureGeb = new SeizureGeb();
        $seizureGeb->setAmountMoney(new Money(100, self::CURRENCY_GEL));
        $seizureGeb->setStatus($status);

        if ($expectExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_NOT_FOUND);
        }
        if ($expectExceptionCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_ALREADY_CANCELLED);
        }

        $this->seizureGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $seizureGeb : null)
        ;

        $seizure = $this->processor->cancel($commonCancelSeizureGeb);
        $this->assertInstanceOf(SeizureGeb::class, $seizure);
        $this->assertEquals(Collection::STATUS_CANCELLED, $seizure->getStatus());
    }

    public function dataProviderCancel(): Traversable
    {
        yield 'Positive' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => false,
        ];

        yield 'Expect exception not found' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => false,
            'expectExceptionNotFound' => true,
            'expectExceptionCanceled' => false,
        ];

        yield 'Status canceled - Expect exception canceled' => [
            'status' => Collection::STATUS_CANCELLED,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => true,
        ];
    }

    /**
     * @dataProvider dataProviderUpdate
     */
    public function testUpdate(
        int $amountIn,
        string $currencyIn,
        int $amountOut,
        string $currencyOut,
        string $status,
        int $exactlyResolve,
        bool $willReturnObjFindOneByNumber,
        bool $expectExceptionNotFound,
        bool $expectExceptionCanceled,
        bool $expectExceptionDifferentCurrency
    ): void {
        $number = '123';

        $moneyIn = new Money($amountIn, $currencyIn);
        $moneyOut = new Money($amountOut, $currencyOut);

        $commonChangeSeizureGeb = new CommonChangeSeizureGeb();
        $commonChangeSeizureGeb->setAmount($moneyIn->getAmount());
        $commonChangeSeizureGeb->setCurrency($moneyIn->getCurrency());
        $commonChangeSeizureGeb->setNumber($number);

        $seizureGeb = new SeizureGeb();
        $seizureGeb->setAmountMoney($moneyOut);
        $seizureGeb->setStatus($status);

        if ($expectExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_NOT_FOUND);
        }
        if ($expectExceptionCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_ALREADY_CANCELLED);
        }
        if ($expectExceptionDifferentCurrency) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_DIFFERENT_CURRENCY);
        }

        $this->seizureGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $seizureGeb : null)
        ;

        $this->amountMoneyResolverMock
            ->expects($this->exactly($exactlyResolve))
            ->method('resolve')
            ->with($commonChangeSeizureGeb->getAmount(), $commonChangeSeizureGeb->getCurrency())
            ->willReturn($moneyOut)
        ;

        $result = $this->processor->update($commonChangeSeizureGeb);
        $this->assertInstanceOf(SeizureGeb::class, $result);
        $this->assertEquals($moneyOut, $result->getAmountMoney());
    }

    public function dataProviderUpdate(): Traversable
    {
        yield 'Positive case' => $this->getUpdateDefaults();

        yield 'ExceptionNotFound' => array_merge($this->getUpdateDefaults(), [
            'willReturnObjFindOneByNumber' => false,
            'exactlyResolve' => 0,
            'expectExceptionNotFound' => true,
        ]);

        yield 'Seizure not active - ExceptionCanceled' => array_merge($this->getUpdateDefaults(), [
            'status' => 'not_active',
            'exactlyResolve' => 0,
            'expectExceptionCanceled' => true,
        ]);

        yield 'Different currencies 1 - ExceptionDifferentCurrency' => array_merge($this->getUpdateDefaults(), [
            'currencyIn' => self::CURRENCY_EUR,
            'exactlyResolve' => 0,
            'expectExceptionDifferentCurrency' => true,
        ]);

        yield 'Different currencies 2 - ExceptionDifferentCurrency' => array_merge($this->getUpdateDefaults(), [
            'currencyOut' => self::CURRENCY_EUR,
            'exactlyResolve' => 0,
            'expectExceptionDifferentCurrency' => true,
        ]);
    }

    private function getUpdateDefaults(): array
    {
        return [
            'amountIn' => 100,
            'currencyIn' => self::CURRENCY_GEL,
            'amountOut' => 200,
            'currencyOut' => self::CURRENCY_GEL,
            'status' => Collection::STATUS_ACTIVE,
            'exactlyResolve' => 1,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => false,
            'expectExceptionDifferentCurrency' => false,
        ];
    }

    /**
     * @dataProvider dataProviderEnforce
     */
    public function testEnforce(
        int $amountIn,
        string $currencyIn,
        int $amountOut,
        string $currencyOut,
        string $status,
        bool $willReturnObjFindOneByNumber,
        bool $expectExceptionNotFound,
        bool $expectExceptionCanceled,
        bool $expectExceptionHigherAmount
    ): void {
        $sourceRestrictCode = 'CODE123';

        $moneyCollection = new Money($amountIn, $currencyIn);
        $moneySeizure = new Money($amountOut, $currencyOut);

        $collectionGeb = new CommonCollectionGeb();
        $collectionGeb->setAmount($moneyCollection->getAmount());
        $collectionGeb->setCurrency($moneyCollection->getCurrency());
        $collectionGeb->setNumber('123');
        $collectionGeb->setSourceRestrictCode($sourceRestrictCode);

        $enforcedSeizure = new SeizureGeb();
        $enforcedSeizure->setAmountMoney($moneySeizure);
        $enforcedSeizure->setStatus($status);

        if ($expectExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_ENFORCED_SEIZURE_NOT_FOUND);
        }
        if ($expectExceptionCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_ENFORCED_SEIZURE_ALREADY_CANCELLED);
        }

        $this->seizureGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($sourceRestrictCode)
            ->willReturn($willReturnObjFindOneByNumber ? $enforcedSeizure : null)
        ;

        if ($expectExceptionHigherAmount) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_HIGHER_AMOUNT_THAN_SEIZURE);
        }

        $this->processor->enforce($collectionGeb->getSourceRestrictCode(), $collectionGeb);
        $this->assertEquals(SeizureGeb::STATUS_ENFORCED, $enforcedSeizure->getStatus());

    }

    public function dataProviderEnforce(): Traversable
    {
        yield 'Positive case' => $this->getEnforceDefaults();

        yield 'Positive case different currencies 1' => array_merge($this->getEnforceDefaults(), [
            'currencyIn' => self::CURRENCY_GEL,
            'currencyOut' => self::CURRENCY_EUR,
        ]);

        yield 'Positive case different currencies 2' => array_merge($this->getEnforceDefaults(), [
            'amountIn' => 1,
            'currencyIn' => self::CURRENCY_EUR,
            'amountOut' => 200,
            'currencyOut' => self::CURRENCY_GEL,
        ]);

        yield 'ExceptionNotFound' => array_merge($this->getEnforceDefaults(), [
            'willReturnObjFindOneByNumber' => false,
            'expectExceptionNotFound' => true,
        ]);

        yield 'ExceptionCanceled' => array_merge($this->getEnforceDefaults(), [
            'status' => Collection::STATUS_CANCELLED,
            'expectExceptionCanceled' => true,
        ]);

        // TODO: SUPPORT-92722 case probably shouldn't work
        yield 'ExceptionCanceled but it another' => array_merge($this->getEnforceDefaults(), [
            'status' => SeizureGeb::STATUS_ENFORCED,
            'expectExceptionCanceled' => true,
        ]);

        yield 'ExceptionHigherAmount' => array_merge($this->getEnforceDefaults(), [
            'amountIn' => 200,
            'amountOut' => 100,
            'expectExceptionHigherAmount' => true,
        ]);
    }

    private function getEnforceDefaults(): array
    {
        return [
            'amountIn' => 100,
            'currencyIn' => self::CURRENCY_GEL,
            'amountOut' => 200,
            'currencyOut' => self::CURRENCY_GEL,
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => false,
            'expectExceptionHigherAmount' => false,
        ];
    }
}
