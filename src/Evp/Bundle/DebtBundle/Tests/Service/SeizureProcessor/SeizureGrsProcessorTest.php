<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service\SeizureProcessor;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\SeizureGrs;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Repository\SeizureGrsRepository;
use Evp\Bundle\DebtBundle\Service\SeizureProcessor\SeizureGrsProcessor;
use Evp\Component\DebtCommon\Entity\CommonResponse;
use Evp\Component\DebtCommon\Entity\Seizure\CancelSeizure\CommonCancelSeizureGrs;
use Evp\Component\DebtCommon\Entity\Seizure\Seizure\CommonSeizureGrs;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Traversable;

class SeizureGrsProcessorTest extends PersistableWebTestCase
{
    private const CURRENCY_GEL = 'GEL';

    private MockObject $entityManagerMock;
    private MockObject $seizureGrsRepositoryMock;
    private MockObject $licensedPartnerRepositoryMock;
    private MockObject $loggerMock;
    private SeizureGrsProcessor $processor;

    protected function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManager::class);
        $this->seizureGrsRepositoryMock = $this->createMock(SeizureGrsRepository::class);
        $this->licensedPartnerRepositoryMock = $this->createMock(LicensedPartnerRepository::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->processor = new SeizureGrsProcessor(
            $this->entityManagerMock,
            $this->seizureGrsRepositoryMock,
            $this->licensedPartnerRepositoryMock,
            $this->getContainer()->get('evp_debt.service.amount_money_resolver'),
            $this->loggerMock,
            LicensedPartner::PAYSERA_GEORGIA,
        );
    }

    /**
     * @dataProvider dataProviderCreate
     */
    public function testCreate(
        bool $willReturnObjFindOneByNumber,
        int $exactlyFindOneByPartnerCode,
        bool $willReturnObjFindOneByPartnerCode,
        int $exactlyError,
        int $exactlyPersist,
        bool $expectProcessorErrorException,
        bool $expectLicensedPartnerNotFoundException
    ): void {
        $number = '123';
        $amount = '100';
        $currency = self::CURRENCY_GEL;

        $client = new ClientNatural();

        $commonSeizureGrs = new CommonSeizureGrs();
        $commonSeizureGrs->setNumber($number);
        $commonSeizureGrs->setAmount($amount);
        $commonSeizureGrs->setCurrency($currency);

        $seizure = new SeizureGrs();

        $licensedPartner = new LicensedPartner();
        $licensedPartner->setTitle('title');

        if ($expectProcessorErrorException) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_NUMBER_ALREADY_EXISTS);
        }

        if ($expectLicensedPartnerNotFoundException) {
            $this->expectException(LicensedPartnerNotFoundException::class);
            $this->expectExceptionMessage(LicensedPartner::PAYSERA_GEORGIA);
        }

        $this->seizureGrsRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $seizure : null)
        ;

        $this->licensedPartnerRepositoryMock
            ->expects($this->exactly($exactlyFindOneByPartnerCode))
            ->method('findOneByPartnerCode')
            ->with(LicensedPartner::PAYSERA_GEORGIA)
            ->willReturn($willReturnObjFindOneByPartnerCode ? $licensedPartner : null)
        ;

        $this->loggerMock
            ->expects($this->exactly($exactlyError))
            ->method('error')
            ->with(
                'GeorgianRevenueService integration exceptional case: Licensed partner not found',
                [$commonSeizureGrs->getNumber(), LicensedPartner::PAYSERA_GEORGIA]
            )
        ;

        $this->entityManagerMock
            ->expects($this->exactly($exactlyPersist))
            ->method('persist')
        ;

        $result = $this->processor->create($commonSeizureGrs, $client);
        $this->assertEquals($number, $result->getNumber());
        $this->assertEquals(Collection::PRIORITY_REVENUE_SERVICE, $result->getOrganizationPriority());
        $this->assertEquals($amount, $result->getAmountMoney()->getAmount());
        $this->assertEquals($currency, $result->getAmountMoney()->getCurrency());
        $this->assertSame($client, $result->getClient());
        $this->assertEquals($licensedPartner, $result->getLicensedPartner());
    }

    public function dataProviderCreate(): Traversable
    {
        yield 'Positive case' => $this->getCreateDefaults();

        yield 'Seizure already exist' => array_merge($this->getCreateDefaults(), [
            'willReturnObjFindOneByNumber' => true,
            'exactlyFindOneByPartnerCode' => 0,
            'exactlyPersist' => 0,
            'expectProcessorErrorException' => true,
        ]);

        yield 'Licenzed partner not found' => array_merge($this->getCreateDefaults(), [
            'willReturnObjFindOneByPartnerCode' => false,
            'exactlyError' => 1,
            'exactlyPersist' => 0,
            'expectLicensedPartnerNotFoundException' => true,
        ]);
    }

    private function getCreateDefaults(): array
    {
        return [
            'willReturnObjFindOneByNumber' => false,
            'exactlyFindOneByPartnerCode' => 1,
            'willReturnObjFindOneByPartnerCode' => true,
            'exactlyError' => 0,
            'exactlyPersist' => 1,
            'expectProcessorErrorException' => false,
            'expectLicensedPartnerNotFoundException' => false,
        ];
    }

    /**
     * @dataProvider dataProviderCancel
     */
    public function testCancel(
        $status,
        $willReturnObjFindOneByNumber,
        $expectProcessorErrorExceptionNotFound,
        $expectProcessorErrorExceptionAlreadyCanceled
    ): void {
        $number = '123';

        $commonCancelSeizureGrs = new CommonCancelSeizureGrs();
        $commonCancelSeizureGrs->setNumber($number);

        $seizure = new SeizureGrs();
        $seizure->setStatus($status);

        if ($expectProcessorErrorExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_NOT_FOUND);
        }
        if ($expectProcessorErrorExceptionAlreadyCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_SEIZURE_ALREADY_CANCELLED);
        }

        $this->seizureGrsRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $seizure : null)
        ;

        $result = $this->processor->cancel($commonCancelSeizureGrs);
        $this->assertEquals(Collection::STATUS_CANCELLED, $result->getStatus());
    }

    public function dataProviderCancel(): Traversable
    {
        yield 'Positive case' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => true,
            'expectProcessorErrorExceptionNotFound' => false,
            'expectProcessorErrorExceptionAlreadyCanceled' => false,
        ];

        yield 'ProcessorErrorException NotFound' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => false,
            'expectProcessorErrorExceptionNotFound' => true,
            'expectProcessorErrorExceptionAlreadyCanceled' => false,
        ];

        yield 'ProcessorErrorException AlreadyCanceled' => [
            'status' => Collection::STATUS_CANCELLED,
            'willReturnObjFindOneByNumber' => true,
            'expectProcessorErrorExceptionNotFound' => false,
            'expectProcessorErrorExceptionAlreadyCanceled' => true,
        ];
    }
}
