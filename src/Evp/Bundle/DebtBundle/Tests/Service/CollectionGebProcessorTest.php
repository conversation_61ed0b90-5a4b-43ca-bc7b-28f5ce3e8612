<?php

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Repository\CollectionGebRepository;
use Evp\Bundle\DebtBundle\Service\AmountMoneyResolver;
use Evp\Bundle\DebtBundle\Service\CollectionProcessor\CollectionGebProcessor;
use Evp\Bundle\DebtBundle\Service\HoldManager;
use Evp\Bundle\DebtBundle\Service\SeizureProcessor\SeizureGebProcessor;
use Evp\Component\DebtCommon\Entity\Collection\CancelCollection\CommonCancelCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\ChangeCollection\CommonChangeCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGeb;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollectionGeb;
use Evp\Component\DebtCommon\Entity\CommonResponse;
use Evp\Component\Money\Money;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Traversable;

class CollectionGebProcessorTest extends TestCase
{
    private const CURRENCY_GEL = 'GEL';
    private const CURRENCY_EUR = 'EUR';

    /** @var EntityManagerInterface|MockObject  */
    private EntityManagerInterface $entityManagerMock;
    /** @var CollectionGebRepository|MockObject  */
    private CollectionGebRepository $collectionGebRepositoryMock;
    /** @var LicensedPartnerRepository|MockObject  */
    private LicensedPartnerRepository $licensedPartnerRepositoryMock;
    /** @var SeizureGebProcessor|MockObject  */
    private SeizureGebProcessor $seizureGebProcessorMock;
    /** @var AmountMoneyResolver|MockObject  */
    private AmountMoneyResolver $amountMoneyResolverMock;
    /** @var HoldManager|MockObject  */
    private HoldManager $holdManagerMock;
    private CollectionGebProcessor $processor;

    protected function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->collectionGebRepositoryMock = $this->createMock(CollectionGebRepository::class);
        $this->licensedPartnerRepositoryMock = $this->createMock(LicensedPartnerRepository::class);
        $this->seizureGebProcessorMock = $this->createMock(SeizureGebProcessor::class);
        $this->amountMoneyResolverMock = $this->createMock(AmountMoneyResolver::class);
        $this->holdManagerMock = $this->createMock(HoldManager::class);
        $this->processor = new CollectionGebProcessor(
            $this->entityManagerMock,
            $this->collectionGebRepositoryMock,
            $this->licensedPartnerRepositoryMock,
            $this->seizureGebProcessorMock,
            $this->amountMoneyResolverMock,
            $this->holdManagerMock,
            LicensedPartner::PAYSERA_GEORGIA,
        );
    }

    /**
     * @dataProvider dataProviderCreate
     */
    public function testCreate(
        string $number,
        ?string $sourceRestrictCode,
        Money $money,
        string $receiverAccount,
        string $receiverCode,
        string $status,
        int $priority,
        bool $willReturnObjFindOneByNumber,
        bool $willReturnObjFindOneByPartnerCode,
        int $exactlyFindOneByPartnerCode,
        int $exactlyEnforce,
        int $exactlyMoneyResolve,
        bool $expectProcessorErrorException,
        bool $expectLicensedPartnerNotFoundException
    ): void {
        $commonCollectionGeb = new CommonCollectionGeb();
        $commonCollectionGeb->setNumber($number);
        $commonCollectionGeb->setPriority($priority);
        $commonCollectionGeb->setSourceRestrictCode($sourceRestrictCode);
        $commonCollectionGeb->setAmount($money->getAmount());
        $commonCollectionGeb->setCurrency($money->getCurrency());
        $commonCollectionGeb->setReceiverAccount($receiverAccount);
        $commonCollectionGeb->setReceiverCode($receiverCode);

        $client = new ClientNatural();
        $client->setId(123);

        $collectionGeb = new CollectionGeb();
        $collectionGeb->setStatus($status);

        $licensedPartner = new LicensedPartner();

        if ($expectProcessorErrorException) {
            $this->expectException(ProcessorErrorException::class);
        }
        if ($expectLicensedPartnerNotFoundException) {
            $this->expectException(LicensedPartnerNotFoundException::class);
        }

        $this->collectionGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $collectionGeb : null)
        ;

        $this->licensedPartnerRepositoryMock
            ->expects($this->exactly($exactlyFindOneByPartnerCode))
            ->method('findOneByPartnerCode')
            ->with(LicensedPartner::PAYSERA_GEORGIA)
            ->willReturn($willReturnObjFindOneByPartnerCode ? $licensedPartner : null)
        ;

        $this->seizureGebProcessorMock
            ->expects($this->exactly($exactlyEnforce))
            ->method('enforce')
            ->with($commonCollectionGeb->getSourceRestrictCode(), $commonCollectionGeb)
        ;

        $this->amountMoneyResolverMock
            ->expects($this->exactly($exactlyMoneyResolve))
            ->method('resolve')
            ->with($money->getAmount(), $money->getCurrency())
            ->willReturn($money)
        ;

        /** @var CollectionGeb $result */
        $result = $this->processor->create($commonCollectionGeb, $client);

        $this->assertEquals($number, $result->getNumber());
        $this->assertEquals(Collection::PRIORITY_NATIONAL_ENFORCEMENT_BUREAU, $result->getOrganizationPriority());
        $this->assertEquals($money->getAmount(), $result->getAmount());
        $this->assertEquals($money->getCurrency(), $result->getCurrency());
        $this->assertSame($client, $result->getClient());
        $this->assertSame($licensedPartner, $result->getLicensedPartner());
        $this->assertEquals($priority, $result->getPriority());
        $this->assertEquals($receiverAccount, $result->getBeneficiaryAccountNumber());
        $this->assertEquals($receiverCode, $result->getBeneficiaryBankSwiftCode());
    }

    public function dataProviderCreate(): Traversable
    {
        yield 'Default' => $this->getCreateDefaults();

        yield 'CollectionGebRepository::findOneByNumber return null' => array_merge($this->getCreateDefaults(), [
            'willReturnObjFindOneByNumber' => false,
        ]);

        yield 'Collection status canceled, expect ProcessorErrorException' => array_merge($this->getCreateDefaults(), [
            'status' => Collection::STATUS_CANCELLED,
            'exactlyFindOneByPartnerCode' => 0,
            'exactlyEnforce' => 0,
            'exactlyMoneyResolve' => 0,
            'expectProcessorErrorException' => true,
        ]);

        yield 'Collection status returned, expect ProcessorErrorException' => array_merge($this->getCreateDefaults(), [
            'status' => Collection::STATUS_RETURNED,
            'exactlyFindOneByPartnerCode' => 0,
            'exactlyEnforce' => 0,
            'exactlyMoneyResolve' => 0,
            'expectProcessorErrorException' => true,
        ]);

        yield 'LicensedPartner not found, expect LicensedPartnerNotFoundException' => array_merge(
            $this->getCreateDefaults(),
            [
                'willReturnObjFindOneByPartnerCode' => false,
                'exactlyEnforce' => 0,
                'exactlyMoneyResolve' => 0,
                'expectLicensedPartnerNotFoundException' => true,
            ],
        );

        yield 'sourceRestrictCode is null' => array_merge($this->getCreateDefaults(), [
            'sourceRestrictCode' => null,
            'exactlyEnforce' => 0,
        ]);

        yield 'sourceRestrictCode is empty string' => array_merge($this->getCreateDefaults(), [
            'sourceRestrictCode' => '',
            'exactlyEnforce' => 0,
        ]);
    }

    private function getCreateDefaults(): array
    {
        return [
            'number' => '123',
            'sourceRestrictCode' => 'code',
            'money' => new Money(100, self::CURRENCY_GEL),
            'receiverAccount' => 'receiver',
            'receiverCode' => 'RCODE',
            'status' => Collection::STATUS_ACTIVE,
            'priority' => 789,
            'willReturnObjFindOneByNumber' => true,
            'willReturnObjFindOneByPartnerCode' => true,
            'exactlyFindOneByPartnerCode' => 1,
            'exactlyEnforce' => 1,
            'exactlyMoneyResolve' => 1,
            'expectProcessorErrorException' => false,
            'expectLicensedPartnerNotFoundException' => false,
        ];
    }

    /**
     * @dataProvider dataProviderCancel
     */
    public function testCancel(
        string $status,
        bool $willReturnObjFindOneByNumber,
        bool $expectExceptionNotFound,
        bool $expectExceptionCanceled
    ): void {
        $number = '123';
        $commonCancelCollectionGeb = new CommonCancelCollectionGeb();
        $commonCancelCollectionGeb->setNumber($number);
        $collectionGeb = new CollectionGeb();
        $collectionGeb->setAmountMoney(new Money(100, self::CURRENCY_GEL));
        $collectionGeb->setStatus($status);

        if ($expectExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_NOT_FOUND);
        }
        if ($expectExceptionCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_ALREADY_CANCELLED);
        }

        $this->collectionGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $collectionGeb : null)
        ;

        $result = $this->processor->cancel($commonCancelCollectionGeb);
        $this->assertSame($result, $collectionGeb);
        $this->assertEquals(Collection::STATUS_CANCELLED, $result->getStatus());
        $this->assertEquals('0', $result->getAmountLeft());
    }

    public function dataProviderCancel(): Traversable
    {
        yield 'Positive' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => false,
        ];

        yield 'Expect exception not found' => [
            'status' => Collection::STATUS_ACTIVE,
            'willReturnObjFindOneByNumber' => false,
            'expectExceptionNotFound' => true,
            'expectExceptionCanceled' => false,
        ];

        yield 'Status canceled - Expect exception canceled' => [
            'status' => Collection::STATUS_CANCELLED,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => true,
        ];

        // TODO: SUPPORT-92722 this case possibly bug
        yield 'Status returned - Expect exception canceled' => [
            'status' => Collection::STATUS_RETURNED,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => true,
        ];
    }

    /**
     * @dataProvider dataProviderUpdate
     */
    public function testUpdate(
        int $amountIn,
        string $currencyIn,
        int $amountOut,
        string $currencyOut,
        string $status,
        int $exactlyResolve,
        int $exactlyMoveMoneyFromCollection,
        bool $willReturnObjFindOneByNumber,
        bool $expectExceptionNotFound,
        bool $expectExceptionCanceled,
        bool $expectExceptionDifferentCurrency
    ): void {
        $number = '123';

        $moneyIn = new Money($amountIn, $currencyIn);
        $moneyOut = new Money($amountOut, $currencyOut);

        $commonCancelCollectionGeb = new CommonChangeCollectionGeb();
        $commonCancelCollectionGeb->setNumber($number);
        $commonCancelCollectionGeb->setAmount($moneyIn->getAmount());
        $commonCancelCollectionGeb->setCurrency($moneyIn->getCurrency());

        $collectionGeb = new CollectionGeb();
        $collectionGeb->setAmountMoney($moneyOut);
        $collectionGeb->setStatus($status);

        if ($expectExceptionNotFound) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_NOT_FOUND);
        }
        if ($expectExceptionCanceled) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_ALREADY_CANCELLED);
        }
        if ($expectExceptionDifferentCurrency) {
            $this->expectException(ProcessorErrorException::class);
            $this->expectExceptionMessage(CommonResponse::ERROR_COLLECTION_DIFFERENT_CURRENCY);
        }

        $this->collectionGebRepositoryMock
            ->expects($this->once())
            ->method('findOneByNumber')
            ->with($number)
            ->willReturn($willReturnObjFindOneByNumber ? $collectionGeb : null)
        ;

        $this->amountMoneyResolverMock
            ->expects($this->exactly($exactlyResolve))
            ->method('resolve')
            ->with($commonCancelCollectionGeb->getAmount(), $commonCancelCollectionGeb->getCurrency())
            ->willReturn($moneyOut)
        ;

        $this->holdManagerMock
            ->expects($this->exactly($exactlyMoveMoneyFromCollection))
            ->method('moveMoneyFromCollection')
            ->with($collectionGeb)
        ;

        $result = $this->processor->update($commonCancelCollectionGeb);
        $this->assertEquals($moneyOut, $result->getAmountMoney());
        $this->assertEquals($moneyOut, $result->getAmountLeftMoney());
    }

    public function dataProviderUpdate(): Traversable
    {
        yield 'Positive case' => $this->getUpdateDefaults();

        yield 'ExceptionNotFound' => array_merge($this->getUpdateDefaults(), [
            'willReturnObjFindOneByNumber' => false,
            'exactlyResolve' => 0,
            'exactlyMoveMoneyFromCollection' => 0,
            'expectExceptionNotFound' => true,
        ]);

        yield 'Collection not active - ExceptionCanceled' => array_merge($this->getUpdateDefaults(), [
            'status' => 'not_active',
            'exactlyResolve' => 0,
            'exactlyMoveMoneyFromCollection' => 0,
            'expectExceptionCanceled' => true,
        ]);

        yield 'Different currencies 1 - ExceptionDifferentCurrency' => array_merge($this->getUpdateDefaults(), [
            'currencyIn' => self::CURRENCY_EUR,
            'exactlyResolve' => 0,
            'exactlyMoveMoneyFromCollection' => 0,
            'expectExceptionDifferentCurrency' => true,
        ]);

        yield 'Different currencies 2 - ExceptionDifferentCurrency' => array_merge($this->getUpdateDefaults(), [
            'currencyOut' => self::CURRENCY_EUR,
            'exactlyResolve' => 0,
            'exactlyMoveMoneyFromCollection' => 0,
            'expectExceptionDifferentCurrency' => true,
        ]);
    }

    private function getUpdateDefaults(): array
    {
        return [
            'amountIn' => 100,
            'currencyIn' => self::CURRENCY_GEL,
            'amountOut' => 200,
            'currencyOut' => self::CURRENCY_GEL,
            'status' => Collection::STATUS_ACTIVE,
            'exactlyResolve' => 1,
            'exactlyMoveMoneyFromCollection' => 1,
            'willReturnObjFindOneByNumber' => true,
            'expectExceptionNotFound' => false,
            'expectExceptionCanceled' => false,
            'expectExceptionDifferentCurrency' => false,
        ];
    }

    public function testReturnCollections(): void
    {
        $client = new ClientNatural();
        $commonCancelCollectionGeb = new CommonReturnCollectionGeb();

        $collections = [
            (new CollectionGeb())->setAmountMoney(new Money(100, self::CURRENCY_GEL)),
        ];

        $this->collectionGebRepositoryMock
            ->expects($this->once())
            ->method('findAllByClientAndStatuses')
            ->with($client, [Collection::STATUS_ACTIVE])
            ->willReturn($collections)
        ;

        $collections = $this->processor->returnCollections($commonCancelCollectionGeb, $client);
        $this->assertCount(1, $collections);
        foreach ($collections as $collection) {
            $this->assertEquals(Collection::STATUS_RETURNED, $collection->getStatus());
            $this->assertEquals('0', $collection->getAmountLeft());
        }
    }
}
