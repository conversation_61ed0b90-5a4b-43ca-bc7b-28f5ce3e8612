<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankHoldBundle\Repository\HoldRepository;
use Evp\Bundle\BankHoldBundle\Service\HoldProcessor;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\DebtClient;
use Evp\Bundle\DebtBundle\Entity\DebtClientCollection;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientCollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientSeizureRepository;
use Evp\Bundle\DebtBundle\Service\HoldManager;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;

class HoldManagerTest extends PersistableWebTestCase
{
    private const ARREST_TYPE_COLLECTION = 'collection';
    private const ARREST_TYPE_SEIZURE = 'seizure';

    private HoldManager $holdManager;
    private HoldHelper $holdHelper;
    private DebtClientCollectionRepository $debtClientCollectionRepository;
    private DebtClientSeizureRepository $debtClientSeizureRepository;
    private CollectionRepository $collectionRepository;
    private HoldRepository $holdRepository;
    private HoldProcessor $holdProcessor;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private FixturesHelper $fixturesHelper;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', new HardcodedTestCurrencyConverter());

        $this->holdManager = $this->getContainer()->get('evp_debt.service.hold_manager');
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->debtClientCollectionRepository = $this->getContainer()->get('evp_debt.repository.debt_client_collection');
        $this->debtClientSeizureRepository = $this->getContainer()->get('evp_debt.repository.debt_client_seizure');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->holdRepository = $this->getContainer()->get('evp_bank_hold.repository.hold');
        $this->holdProcessor = $this->getContainer()->get('evp_bank_hold.hold_processor');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @param bool $expectHolds
     * @param ?Money $expectedHoldsAmount
     * @param array $collectionsData
     * @param Money[] $holds
     * @dataProvider processCollectionsDataProvider
     */
    public function testProcessCollections(
        bool $expectHolds,
        ?Money $expectedHoldsAmount,
        array $collectionsData,
        array $holds = []
    ): void {
        // Prepare data
        $client = $this->fixturesHelper->createClientNatural();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        for ($i = 0; $i < 3; $i++) {
            $number = (string) $i;
            $account = $this->fixturesHelper->createAccount($client, $number, $number);
            $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
            $this->entityManager->flush();

            if (isset($holds[$number])) {
                $hold = $this->fixturesHelper->createHold($account, $holds[$number]);
                $debtClientCollection->addHold($hold);
            }
        }

        foreach ($collectionsData as $number => $data) {
            $collection = $this->fixturesHelper->createDebtCollectionGrs((string) $number, $debtClientCollection, $licensedPartner);
            $collection->setAmountMoney($data['amount']);
            $collection->setAmountLeftMoney($data['amount']);

            if (array_key_exists('writeOffStatus', $data)) {
                $collection->setWriteOffStatus($data['writeOffStatus']);
            }
        }

        $this->entityManager->flush();

        // Run hold manager
        $this->holdManager->processCollections($debtClientCollection);
        $this->entityManager->flush();

        // Receive new data from DB
        $debtClientCollection = $this->debtClientCollectionRepository->findOneByClient($client);
        $activeHolds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());

        // 3 because client has 3 accounts
        $this->assertCount($expectHolds ? 3 : 0, $activeHolds);

        // Check holds amount
        foreach ($activeHolds as $hold) {
            $amount = $hold->getAmountMoney();
            $this->assertEquals($expectedHoldsAmount, $amount);
        }
    }

    public function processCollectionsDataProvider(): array
    {
        return [
            'No collections' => [
                'expectHolds' => false,
                'expectedHoldsAmount' => null,
                'collections' => [],
            ],
            'One collection in GEL' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money(120, 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'GEL')],
                ],
            ],
            'One collection in EUR' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money('434.1', 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'EUR')],
                ],
            ],
            'One collection with null amount' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => null,
                'collections' => [
                    '1' => ['amount' => null],
                ],
            ],
            'Two collections both GEL' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money(200, 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'GEL')],
                    '2' => ['amount' => new Money(80, 'GEL')],
                ],
            ],
            'Two collections both EUR' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money('723.5', 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'EUR')],
                    '2' => ['amount' => new Money(80, 'EUR')],
                ],
            ],
            'Two collections one with null amount' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => null,
                'collections' => [
                    '1' => ['amount' => null],
                    '2' => ['amount' => new Money(160, 'GEL')],
                ],
            ],
            'Two collections both with null amount' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => null,
                'collections' => [
                    '1' => ['amount' => null],
                    '2' => ['amount' => null],
                ],
            ],
            'No active collections but holds exists' => [
                'expectHolds' => false,
                'expectedHoldsAmount' => null,
                'collections' => [],
                'holds' => [
                    '1' => new Money(120, 'GEL'),
                    '2' => new Money(120, 'GEL'),
                    '3' => new Money(120, 'GEL'),
                ],
            ],
            'Two collections both EUR waiting writeOffStatus' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money('723.5', 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'EUR'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
                    '2' => ['amount' => new Money(80, 'EUR'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
                ],
            ],
            'Two collections one with null amount waiting writeOffStatus' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => null,
                'collections' => [
                    '1' => ['amount' => null, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
                    '2' => ['amount' => new Money(160, 'GEL'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING],
                ],
            ],
            'Two collections both EUR failed writeOffStatus' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => new Money('723.5', 'GEL'),
                'collections' => [
                    '1' => ['amount' => new Money(120, 'EUR'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                    '2' => ['amount' => new Money(80, 'EUR'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                ],
            ],
            'Two collections one with null amount failed writeOffStatus' => [
                'expectHolds' => true,
                'expectedHoldsAmount' => null,
                'collections' => [
                    '1' => ['amount' => null, 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                    '2' => ['amount' => new Money(160, 'GEL'), 'writeOffStatus' => Collection::WRITE_OFF_STATUS_FAILED],
                ],
            ],
        ];
    }

    /**
     * @param Money[] $expectedAccountsHolds
     * @param Money[] $accountsData
     * @param Money[] $holdsData
     * @param Money[] $collectionsData
     * @return void
     * @dataProvider holdMoneyInAccountsDataProvider
     */
    public function testHoldMoneyInAccountsForCollections(
        array $expectedAccountsHolds,
        array $accountsData,
        array $holdsData,
        array $collectionsData
    ): void {
        /** @var DebtClientCollection $debtClient */
        $debtClient = $this->prepareDatabaseForTestHoldMoneyInAccounts(
            $accountsData,
            $holdsData,
            $collectionsData,
            self::ARREST_TYPE_COLLECTION
        );
        $client = $debtClient->getClient();

        $this->entityManager->flush();

        $this->holdManager->processCollections($debtClient);
        $this->entityManager->flush();

        $debtClientCollection = $this->debtClientCollectionRepository->findOneByClient($client);
        $holds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());

        $this->assertCount(count($expectedAccountsHolds), $holds);

        $accountsHolds = [];
        foreach ($holds as $hold) {
            $accountsHolds[$hold->getAccount()->getNumber()] = [
                'amount' => $hold->getAmountMoney(),
                'holdAmount' => $hold->getHoldAmountMoney(),
            ];
        }
        $this->assertEquals($expectedAccountsHolds, $accountsHolds);

        foreach ($debtClientCollection->getClient()->getAccounts() as $account) {
            $accountHolds = $this->holdRepository->findBy(['account' => $account]);
            $accountHolds = $this->holdHelper->filterActiveHolds($accountHolds);

            $expectedCount = isset($expectedAccountsHolds[$account->getNumber()]) ? 1 : 0;
            $this->assertCount($expectedCount, $accountHolds);
        }
    }

    /**
     * @param Money[] $expectedAccountsHolds
     * @param Money[] $accountsData
     * @param Money[] $holdsData
     * @param Money[] $seizuresData
     * @return void
     * @dataProvider holdMoneyInAccountsDataProvider
     */
    public function testHoldMoneyInAccountsForSeizures(
        array $expectedAccountsHolds,
        array $accountsData,
        array $holdsData,
        array $seizuresData
    ): void {
        /** @var DebtClientSeizure $debtClient */
        $debtClient = $this->prepareDatabaseForTestHoldMoneyInAccounts(
            $accountsData,
            $holdsData,
            $seizuresData,
            self::ARREST_TYPE_SEIZURE
        );
        $client = $debtClient->getClient();

        $this->entityManager->flush();

        $this->holdManager->processSeizures($debtClient);
        $this->entityManager->flush();

        $debtClientSeizure = $this->debtClientSeizureRepository->findOneByClient($client);
        $holds = $this->holdHelper->filterActiveHolds($debtClientSeizure->getHolds()->toArray());

        $this->assertCount(count($expectedAccountsHolds), $holds);

        $accountsHolds = [];
        foreach ($holds as $hold) {
            $accountsHolds[$hold->getAccount()->getNumber()] = [
                'amount' => $hold->getAmountMoney(),
                'holdAmount' => $hold->getHoldAmountMoney(),
            ];
        }
        $this->assertEquals($expectedAccountsHolds, $accountsHolds);

        foreach ($debtClientSeizure->getClient()->getAccounts() as $account) {
            $accountHolds = $this->holdRepository->findBy(['account' => $account]);
            $accountHolds = $this->holdHelper->filterActiveHolds($accountHolds);

            $expectedCount = isset($expectedAccountsHolds[$account->getNumber()]) ? 1 : 0;
            $this->assertCount($expectedCount, $accountHolds);
        }
    }

    public function holdMoneyInAccountsDataProvider(): array
    {
        $gel = [
            '0' => new Money(0, 'GEL'),
            '50' => new Money(50, 'GEL'),
            '100' => new Money(100, 'GEL'),
            '200' => new Money(200, 'GEL'),
            '300' => new Money(300, 'GEL'),
            '400' => new Money(400, 'GEL'),
            '500' => new Money(500, 'GEL'),
        ];
        $gelInEur = [
            '100' => new Money('27.64', 'EUR'),
            '200' => new Money('55.29', 'EUR'),
        ];
        $groups = [
            '0-0' => ['1' => $gel['0'], '2' => $gel['0']],
            '0-300' => ['1' => $gel['0'], '2' => $gel['300']],
            '50-100' => ['1' => $gel['50'], '2' => $gel['100']],
            '100-100' => ['1' => $gel['100'], '2' => $gel['100']],
            '100-200' => ['1' => $gel['100'], '2' => $gel['200']],
            '200-100' => ['1' => $gel['200'], '2' => $gel['100']],
            '200-200' => ['1' => $gel['200'], '2' => $gel['200']],
            '200-300' => ['1' => $gel['200'], '2' => $gel['300']],
            '300-300' => ['1' => $gel['300'], '2' => $gel['300']],
            '400-400' => ['1' => $gel['400'], '2' => $gel['400']],
            '500-200' => ['1' => $gel['500'], '2' => $gel['200']],
            '500-500' => ['1' => $gel['500'], '2' => $gel['500']],
            'null-200' => ['1' => null, '2' => $gel['200']],
        ];

        return [
            '1. Account with money | Needed not null | Money less than needed | Holds more than needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['50-100'],
                'holdsData' => $groups['500-500'],
                'collectionsData' => $groups['100-200'],
            ],
            '2. Account with money | Needed not null | Money less than needed | Holds equal to needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['50-100'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '3. Account with money | Needed not null | Money less than needed | Holds less than needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['50-100'],
                'holdsData' => $groups['200-100'],
                'collectionsData' => $groups['100-200'],
            ],
            '4. Account with money | Needed not null | Money less than needed | No holds' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['50']],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['50-100'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '5. Account with money | Needed not null | Money equal to needed | Holds more than needed | Covered by one' => [
                'expectedAccountsHolds' => [
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['0-300'],
                'holdsData' => $groups['500-500'],
                'collectionsData' => $groups['100-200'],
            ],
            '6. Account with money | Needed not null | Money equal to needed | Holds more than needed | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['500-500'],
                'collectionsData' => $groups['100-200'],
            ],
            '7. Account with money | Needed not null | Money equal to needed | Holds equal to needed  | Covered by one' => [
                'expectedAccountsHolds' => [
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['0-300'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '8. Account with money | Needed not null | Money equal to needed | Holds equal to needed  | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '9.  Account with money | Needed not null | Money equal to needed | Holds less than needed | Covered by one' => [
                'expectedAccountsHolds' => [
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['0-300'],
                'holdsData' => $groups['100-100'],
                'collectionsData' => $groups['100-200'],
            ],
            '10. Account with money | Needed not null | Money equal to needed | Holds less than needed | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['50-100'],
                'collectionsData' => $groups['100-200'],
            ],
            '11. Account with money | Needed not null | Money equal to needed | No holds | Covered by one' => [
                'expectedAccountsHolds' => [
                    '2' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['0-300'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '12. Account with money | Needed not null | Money equal to needed | No holds | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                    '2' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '13. Account with money | Needed not null | Money more than needed | Holds more than needed | Covered by one' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['500-200'],
                'holdsData' => $groups['500-500'],
                'collectionsData' => $groups['100-200'],
            ],
            '14. Account with money | Needed not null | Money more than needed | Holds more than needed | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                    '2' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['200-300'],
                'holdsData' => $groups['500-500'],
                'collectionsData' => $groups['100-200'],
            ],
            '15. Account with money | Needed not null | Money more than needed | Holds equal to needed  | Covered by one' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['500-200'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '16. Account with money | Needed not null | Money more than needed | Holds equal to needed  | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                    '2' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['200-300'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '17. Account with money | Needed not null | Money more than needed | Holds less than needed | Covered by one' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['500-200'],
                'holdsData' => $groups['200-200'],
                'collectionsData' => $groups['100-200'],
            ],
            '18. Account with money | Needed not null | Money more than needed | Holds less than needed | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                    '2' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['200-300'],
                'holdsData' => $groups['200-200'],
                'collectionsData' => $groups['100-200'],
            ],
            '19. Account with money | Needed not null | Money more than needed | No holds | Covered by one' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => $gel['300']],
                ],
                'accountsData' => $groups['500-200'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '20. Account with money | Needed not null | Money more than needed | No holds | Covered by two' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['200'], 'holdAmount' => $gel['200']],
                    '2' => ['amount' => $gel['100'], 'holdAmount' => $gel['100']],
                ],
                'accountsData' => $groups['200-300'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '21. Account without money | Needed not null | Holds more than needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => $groups['400-400'],
                'collectionsData' => $groups['100-200'],
            ],
            '22. Account without money | Needed not null | Holds equal to needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => $groups['300-300'],
                'collectionsData' => $groups['100-200'],
            ],
            '23. Account without money | Needed not null | Holds less than needed' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => $groups['200-100'],
                'collectionsData' => $groups['100-200'],
            ],
            '24. Account without money | Needed not null | No holds' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => $gel['300'], 'holdAmount' => null],
                    '2' => ['amount' => $gel['300'], 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => [],
                'collectionsData' => $groups['100-200'],
            ],
            '25. Account with money | Needed null | Holds more than account money' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                    '2' => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['400-400'],
                'collectionsData' => $groups['null-200'],
            ],
            '26. Account with money | Needed null | Holds equal to account money' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                    '2' => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['100-200'],
                'collectionsData' => $groups['null-200'],
            ],
            '27. Account with money | Needed null | Holds less than account money' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                    '2' => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['50-100'],
                'collectionsData' => $groups['null-200'],
            ],
            '28. Account with money | Needed null | No holds' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => $gelInEur['100']],
                    '2' => ['amount' => null, 'holdAmount' => $gelInEur['200']],
                ],
                'accountsData' => $groups['100-200'],
                'holdsData' => [],
                'collectionsData' => $groups['null-200'],
            ],
            '29. Account without money | Needed null | Holds' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                    '2' => ['amount' => null, 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => $groups['100-200'],
                'collectionsData' => $groups['null-200'],
            ],
            '30. Account without money | Needed null | No holds' => [
                'expectedAccountsHolds' => [
                    '1' => ['amount' => null, 'holdAmount' => null],
                    '2' => ['amount' => null, 'holdAmount' => null],
                ],
                'accountsData' => $groups['0-0'],
                'holdsData' => [],
                'collectionsData' => $groups['null-200'],
            ],
            '31. Account with money | No collections | Holds more than account money' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['400-400'],
                'collectionsData' => [],
            ],
            '32. Account with money | No collections | Holds equal to account money' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['100-200'],
                'collectionsData' => [],
            ],
            '33. Account with money | No collections | Holds less than account money' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['100-200'],
                'holdsData' => $groups['50-100'],
                'collectionsData' => [],
            ],
            '34. Account with money | No collections | No holds' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['100-200'],
                'holdsData' => [],
                'collectionsData' => [],
            ],
            '35. Account without money | No collections | Holds' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['0-0'],
                'holdsData' => $groups['400-400'],
                'collectionsData' => [],
            ],
            '36. Account without money | No collections | No holds' => [
                'expectedAccountsHolds' => [],
                'accountsData' => $groups['0-0'],
                'holdsData' => [],
                'collectionsData' => [],
            ],
        ];
    }

    /**
     * @param Money[] $accountsData
     * @param Money[] $holdsData
     * @param Money[] $debtsData
     * @param string $arrestType
     * @return DebtClient
     */
    private function prepareDatabaseForTestHoldMoneyInAccounts(
        array $accountsData,
        array $holdsData,
        array $debtsData,
        string $arrestType
    ): DebtClient {
        $client = $this->fixturesHelper->createClientNatural();
        $debtClient = $arrestType === self::ARREST_TYPE_COLLECTION
            ? $this->fixturesHelper->createDebtClientCollection($client)
            : $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $accounts = [];

        foreach ($accountsData as $number => $accountMoney) {
            $number = (string) $number;
            $account = $this->fixturesHelper->createAccount($client, $number, $number);
            $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);

            $accounts[$number] = $account;

            $this->accountManager->fill($accountMoney, $account);
        }

        $this->entityManager->flush();

        foreach ($debtsData as $number => $debtMoney) {
            $number = (string) $number;

            if ($arrestType === self::ARREST_TYPE_COLLECTION) {
                $collection = $this->fixturesHelper->createDebtCollectionGrs($number, $debtClient, $licensedPartner);
                $collection->setAmountMoney($debtMoney);
                $collection->setAmountLeftMoney($debtMoney);
                continue;
            }

            if ($arrestType === self::ARREST_TYPE_SEIZURE) {
                $seizure = $this->fixturesHelper->createDebtSeizureGrs($number, $debtClient, $licensedPartner);
                $seizure->setAmountMoney($debtMoney);
                continue;
            }
        }

        foreach ($holdsData as $accountNumber => $holdMoney) {
            $accountNumber = (string) $accountNumber;
            $hold = $this->fixturesHelper
                ->createHold($accounts[$accountNumber], $holdMoney)
                ->setInstallmentHoldAllowed(true)
            ;
            $this->holdProcessor->processHold($hold);
            $debtClient->addHold($hold);
        }

        return $debtClient;
    }

    /** @dataProvider moveMoneyToCollectionDataProvider */
    public function testMoveMoneyToCollection(
        array $expectedCollectionHolds,
        ?Money $collectionAmount,
        array $commonCollectionHolds,
        array $commonSeizureHolds
    ): void {
        // Prepare data in database
        $client = $this->fixturesHelper->createClientNatural();

        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper
            ->createDebtCollectionGrs('1', $debtClientCollection, $licensedPartner)
            ->setAmountMoney($collectionAmount)
            ->setAmountLeftMoney($collectionAmount)
        ;
        $this->fixturesHelper
            ->createDebtSeizureGrs('1', $debtClientSeizure, $licensedPartner)
            ->setAmountMoney(null)
        ;

        $accounts = [];

        foreach ($commonCollectionHolds as $key => $collectionHold) {
            $key = (string) $key;
            $accounts[$key] = $this->fixturesHelper->createAccount($client, $key, $key);
            $this->fixturesHelper->createAccountPartner($accounts[$key], LicensedPartner::PAYSERA_GEORGIA);

            if ($collectionHold['holdAmount'] !== null) {
                $this->accountManager->fill($collectionHold['holdAmount'], $accounts[$key]);
            }

            $this->entityManager->flush();

            $hold = $this->holdHelper->createHold($accounts[$key], $collectionHold['holdAmount'], 'Details');
            $debtClientCollection->addHold($hold);
        }

        foreach ($commonSeizureHolds as $key => $seizureHold) {
            $key = (string) $key;
            $account = $accounts[$key] ?? $this->fixturesHelper->createAccount($client, $key, $key);
            $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);

            if ($seizureHold['holdAmount'] !== null) {
                $this->accountManager->fill($seizureHold['holdAmount'], $account);
            }

            $this->entityManager->flush();

            $hold = $this->holdHelper->createHold($account, $seizureHold['holdAmount'], 'Details');
            $debtClientSeizure->addHold($hold);
        }

        $this->entityManager->flush();

        // Get results
        $this->holdManager->moveMoneyToCollection($collection, $debtClientSeizure);
        $this->entityManager->flush();

        // Check results
        /** @var Collection $collection */
        $collection = $this->collectionRepository->find($collection->getId());

        $expectedCollectionHolds = array_map(
            fn(array $item) => ['amount' => isset($item['amount']) ? $item['amount'] : $item['holdAmount'], 'holdAmount' => $item['holdAmount']],
            $expectedCollectionHolds
        );

        $collectionHolds = $this->holdHelper->filterActiveHolds($collection->getHolds()->toArray());
        $collectionHoldsArray = [];
        foreach ($collectionHolds as $hold) {
            $collectionHoldsArray[$hold->getAccount()->getNumber()] = [
                'amount' => $hold->getAmountMoney(),
                'holdAmount' => $hold->getHoldAmountMoney(),
            ];
        }

        $this->assertEquals($expectedCollectionHolds, $collectionHoldsArray);
    }

    public function moveMoneyToCollectionDataProvider(): array
    {
        $gel = [
            '50' => new Money(50, 'GEL'),
            '100' => new Money(100, 'GEL'),
            '150' => new Money(150, 'GEL'),
            '200' => new Money(200, 'GEL'),
            '300' => new Money(300, 'GEL'),
        ];

        return [
            'Case 1. Collection amount null | Collection holds nothing | Seizure holds nothing' => [
                'expectedCollectionHolds' => [],
                'collectionAmount' => null,
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [],
            ],
            'Case 2. Collection amount null | Collection holds nothing | Seizure holds less' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => null,
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 3. Collection amount null | Collection holds less | Seizure holds nothing' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => null,
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonSeizureHolds' => [],
            ],
            'Case 4. Collection amount null | Collection holds less | Seizure holds less' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
                'collectionAmount' => null,
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 5. Collection amount not null | Collection holds nothing | Seizure holds nothing' => [
                'expectedCollectionHolds' => [],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [],
            ],
            'Case 6. Collection amount not null | Collection holds nothing | Seizure holds less' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 7. Collection amount not null | Collection holds nothing | Seizure holds equal' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['200']],
                ],
            ],
            'Case 8. Collection amount not null | Collection holds nothing | Seizure holds more' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => $gel['300']],
                ],
            ],
            'Case 9. Collection amount not null | Collection holds less | Seizure holds nothing' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonSeizureHolds' => [],
            ],
            'Case 10. Collection amount not null | Collection holds less | Seizure holds less' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['50']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['50']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
            ],
            'Case 11. Collection amount not null | Collection holds less | Seizure holds equal' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['50']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
            ],
            'Case 12. Collection amount not null | Collection holds less | Seizure holds more' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['200']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['300'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['50']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
            ],
            'Case 13. Collection amount not null | Collection holds equal | Seizure holds nothing' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['200'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonSeizureHolds' => [],
            ],
            'Case 14. Collection amount not null | Collection holds equal | Seizure holds more' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'collectionAmount' => $gel['200'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['100']],
                    '2' => ['holdAmount' => $gel['100']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
            ],
            'Case 15. Collection amount not null | Collection holds more | Seizure holds nothing' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
                'collectionAmount' => $gel['200'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
                'commonSeizureHolds' => [],
            ],
            'Case 16. Collection amount not null | Collection holds more | Seizure holds more' => [
                'expectedCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['50']],
                ],
                'collectionAmount' => $gel['200'],
                'commonCollectionHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
                'commonSeizureHolds' => [
                    '1' => ['holdAmount' => $gel['150']],
                    '2' => ['holdAmount' => $gel['150']],
                ],
            ],
        ];
    }

    /** @dataProvider moveMoneyFromCollectionDataProvider */
    public function testMoveMoneyFromCollection(
        array $expectedCommonHolds,
        array $collectionsData,
        array $accountsMoney,
        array $targetCollectionHolds,
        array $commonHolds
    ): void {
        $client = $this->fixturesHelper->createClientNatural();
        $accounts = [];
        foreach ($accountsMoney as $number => $accountMoney) {
            $accounts[$number] = $this->fixturesHelper->createAccount($client, $number, $number);
            $this->fixturesHelper->createAccountPartner($accounts[$number], LicensedPartner::PAYSERA_GEORGIA);

            $this->accountManager->fill($accountMoney, $accounts[$number]);
        }

        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collections = [];
        foreach ($collectionsData as $number => $collectionData) {
            $collections[] = $this->fixturesHelper
                ->createDebtCollectionGrs($number, $debtClientCollection, $licensedPartner)
                ->setAmountMoney($collectionData['amount'])
                ->setAmountLeftMoney($collectionData['amount'])
            ;
        }

        $this->entityManager->flush();
        $targetCollection = $collections[0];

        foreach ($targetCollectionHolds as $accountNumber => $holdData) {
            $hold = $this->holdHelper->createHold($accounts[$accountNumber], $holdData['holdAmount'], 'Test');
            $targetCollection->addHold($hold);
        }

        foreach ($commonHolds as $accountNumber => $holdData) {
            $hold = $this->holdHelper->createHold($accounts[$accountNumber], $holdData['holdAmount'], 'Test');
            $debtClientCollection->addHold($hold);
        }

        $this->entityManager->flush();

        $this->holdManager->moveMoneyFromCollection($targetCollection);
        $this->entityManager->flush();

        $targetCollectionHolds = $this->holdHelper->filterActiveHolds($targetCollection->getHolds()->toArray());
        $this->assertCount(0, $targetCollectionHolds, 'Target collection holds should be empty');

        $commonHolds = $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());
        $commonHoldsArray = [];
        foreach ($commonHolds as $hold) {
            $commonHoldsArray[$hold->getAccount()->getNumber()] = ['holdAmount' => $hold->getHoldAmountMoney()];
        }

        $this->assertEquals($expectedCommonHolds, $commonHoldsArray, 'Common holds are not as expected');
    }

    public function moveMoneyFromCollectionDataProvider(): array
    {
        $gel = [
            '100' => new Money(100, 'GEL'),
            '200' => new Money(200, 'GEL'),
            '260' => new Money(260, 'GEL'),
            '400' => new Money(400, 'GEL'),
            '460' => new Money(460, 'GEL'),
            '660' => new Money(660, 'GEL'),
        ];

        return [
            'Case 1. One collection. Collection holds are not exists' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['100']],
                    'account2' => ['holdAmount' => $gel['100']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['100'],
                    'account2' => $gel['100'],
                ],
                'targetCollectionHolds' => [],
                'commonHolds' => [],
            ],
            'Case 2. One collection. Collection hold one exist, another not' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['100']],
                    'account2' => ['holdAmount' => $gel['100']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['100'],
                    'account2' => $gel['100'],
                ],
                'targetCollectionHolds' => [
                    'account1' => ['holdAmount' => $gel['100']],
                ],
                'commonHolds' => [],
            ],
            'Case 3. One collection. Collection holds are exists' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['100']],
                    'account2' => ['holdAmount' => $gel['100']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['100'],
                    'account2' => $gel['100'],
                ],
                'targetCollectionHolds' => [
                    'account1' => ['holdAmount' => $gel['100']],
                    'account2' => ['holdAmount' => $gel['100']],
                ],
                'commonHolds' => [],
            ],
            'Case 4. Two collections. Collection holds are not exists' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['400']],
                    'account2' => ['holdAmount' => $gel['460']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                    'collection2' => ['amount' => $gel['660']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['400'],
                    'account2' => $gel['460'],
                ],
                'targetCollectionHolds' => [],
                'commonHolds' => [
                    'account1' => ['holdAmount' => $gel['200']],
                    'account2' => ['holdAmount' => $gel['260']],
                ],
            ],
            'Case 5. Two collections. Collection hold one exist, another not' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['400']],
                    'account2' => ['holdAmount' => $gel['460']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                    'collection2' => ['amount' => $gel['660']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['400'],
                    'account2' => $gel['460'],
                ],
                'targetCollectionHolds' => [
                    'account1' => ['holdAmount' => $gel['200']],
                ],
                'commonHolds' => [
                    'account1' => ['holdAmount' => $gel['200']],
                    'account2' => ['holdAmount' => $gel['260']],
                ],
            ],
            'Case 6. Two collections. Collection holds are exists' => [
                'expectedCommonHolds' => [
                    'account1' => ['holdAmount' => $gel['400']],
                    'account2' => ['holdAmount' => $gel['460']],
                ],
                'collectionsData' => [
                    'collection1' => ['amount' => $gel['200']],
                    'collection2' => ['amount' => $gel['660']],
                ],
                'accountsMoney' => [
                    'account1' => $gel['400'],
                    'account2' => $gel['460'],
                ],
                'targetCollectionHolds' => [
                    'account1' => ['holdAmount' => $gel['200']],
                    'account2' => ['holdAmount' => $gel['200']],
                ],
                'commonHolds' => [
                    'account1' => ['holdAmount' => $gel['200']],
                    'account2' => ['holdAmount' => $gel['260']],
                ],
            ],
        ];
    }
}
