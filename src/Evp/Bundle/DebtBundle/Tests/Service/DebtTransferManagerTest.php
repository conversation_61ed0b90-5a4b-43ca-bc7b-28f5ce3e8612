<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\InternalMainAccountRepository;
use Evp\Bundle\BankAccountBundle\Service\AccountAliasManager;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator;
use Evp\Bundle\BankCommissionBundle\Entity\CommissionRule;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankHoldBundle\Service\HoldProcessor;
use Evp\Bundle\BankTransferBundle\Entity\Route;
use Evp\Bundle\BankTransferBundle\Entity\RouteAvailability;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\BankTransferBundle\Entity\TransferInternal;
use Evp\Bundle\BankTransferBundle\Entity\TransferOutBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccount;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyAccountCountry;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyBank;
use Evp\Bundle\BankTransferBundle\Entity\TransferParty\PartyIban;
use Evp\Bundle\BankTransferBundle\Entity\TransferSignature;
use Evp\Bundle\BankTransferBundle\Repository\TransferRepository;
use Evp\Bundle\BankTransferBundle\Service\RouteAvailabilityResolver;
use Evp\Bundle\BankTransferBundle\Service\RouteResolver;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Exception\DebtTransferManagerException;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\DebtTransferManager;
use Evp\Bundle\GrsBundle\Service\Validator\TreasuryCodeValidator;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Component\Money\Money;
use Evp\Tests\Mock\UserRestFactoryMock;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;

class DebtTransferManagerTest extends PersistableWebTestCase
{
    private DebtTransferManager $debtTransferManager;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private HoldProcessor $holdProcessor;
    private FixturesHelper $fixturesHelper;
    private TransferRepository $transferRepository;
    private InternalMainAccountRepository  $internalMainAccountRepository;
    private CollectionRepository $collectionRepository;
    private AccountNumberGenerator $accountNumberGenerator;
    private AccountNumberGenerator $ibanAccountNumberGenerator;

    /**
     * @var RouteResolver|MockObject
     */
    private RouteResolver $routeResolver;
    /**
     * @var RouteAvailabilityResolver|MockObject
     */
    private RouteAvailabilityResolver $routeAvailabilityResolver;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $treasuryCodeValidator = $this->createMock(TreasuryCodeValidator::class);
        $treasuryCodeValidator
            ->expects($this->any())
            ->method('isValid')
            ->with('*********')
            ->willReturn(true)
        ;
        $this->getContainer()->set('evp_grs.service.validator.treasury_code', $treasuryCodeValidator);

        $this->routeResolver = $this->createMock(RouteResolver::class);
        $this->getContainer()->set('evp_bank_transfer.route.resolver', $this->routeResolver);
        $this->routeAvailabilityResolver = $this->createMock(RouteAvailabilityResolver::class);
        $this->getContainer()->set(
            'evp_bank_transfer.route.availability.resolver',
            $this->routeAvailabilityResolver
        );
        $this->getContainer()->set(
            'evp_bank_account.account_alias_manager',
            $this->createMock(AccountAliasManager::class)
        );

        $officialCurrencyConverter = new HardcodedTestCurrencyConverter();
        $officialCurrencyConverter->setRate('GEL', '0.5');
        $this->getContainer()->set('evp_currency.currency_converter.official_by_partner.cached', $officialCurrencyConverter);

        $this->debtTransferManager = $this->getContainer()->get('evp_debt.service.debt_transfer_manager');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->holdProcessor = $this->getContainer()->get('evp_bank_hold.hold_processor');
        $this->transferRepository = $this->getContainer()->get('evp_bank_transfer.repository.transfer');
        $this->internalMainAccountRepository = $this->getContainer()->get(
            'evp_bank_account.repository.internal_account.main'
        );
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->accountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator');
        $this->ibanAccountNumberGenerator = $this->getContainer()->get('evp_bank_account.account_number_generator.iban');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    protected static function getMockedServices()
    {
        return [
            'evp_user_client.user_rest_factory' => new UserRestFactoryMock(),
        ];
    }

    /**
     * @param array $expectedDataResult
     * @param array $collectionData
     * @param array $accountsData
     * @param int $aggregatingAccountIndex
     * @param PartyBank $beneficiaryParty
     * @param Money|null $amountToWriteOff
     *
     * @dataProvider collectAndProcessDataProvider
     */
    public function testCollectAndProcess(
        array $expectedDataResult,
        array $collectionData,
        array $accountsData,
        int $aggregatingAccountIndex,
        PartyBank $beneficiaryParty,
        ?Money $amountToWriteOff = null
    ): void {
        $this->assertEquals(0, count($this->transferRepository->findAll()));

        $this->fixturesHelper->createGeorgiaRTGSParticipant();
        $client = $this->fixturesHelper->createClientNatural(
            1,
            IdentificationLevels::IDENTIFIED,
            null,
            null,
            '***********'
        );
        $collection = $this->createCollectionFromData($collectionData, $client);
        $accounts = [];

        /** @var array $accountData */
        foreach ($accountsData as $index => $accountData) {
            $account = $this->fixturesHelper->createAccount(
                $client,
                $this->accountNumberGenerator->generateNumber($index + 1),
                $this->ibanAccountNumberGenerator->generateNumber($index + 1),
                [],
                $accountData['type'] ?? Account::TYPE_LOCAL
            );

            $this->accountManager->fill($accountData['initialAmount'], $account);
            $this->entityManager->flush();

            $hold = $this->fixturesHelper->createHold($account, $accountData['holdAmount']);
            $hold->setConversionAllowed(true);
            $this->holdProcessor->processHold($hold);
            $collection->addHold($hold);

            $accounts[] = $account;
        }

        $this->entityManager->flush();

        foreach ($collection->getHolds() as $hold) {
            $this->assertTrue(
                in_array(
                    $hold->getStatus(),
                    [
                        Hold::STATUS_WAITING_FUNDS,
                        Hold::STATUS_HOLD,
                    ],
                    true
                )
            );
        }

        $aggregatingAccount = $accounts[$aggregatingAccountIndex];

        if (isset($expectedDataResult['error'])) {
            $this->expectException($expectedDataResult['error']['exception']);
            $this->expectExceptionMessage($expectedDataResult['error']['message']);
        } else {
            $this->routeResolver
                ->method('getRoute')
                ->willReturn(
                    $this->resolveGeorgiaRTGSRoute(
                        $amountToWriteOff
                    )
                )
            ;

            $this->routeAvailabilityResolver
                ->method('getRouteAvailability')
                ->willReturn(new RouteAvailability())
            ;
        }

        $this->debtTransferManager->collectAndProcess(
            $aggregatingAccount,
            $beneficiaryParty,
            $collection,
            $amountToWriteOff
        );

        if (isset($expectedDataResult['error'])) {
            return;
        }

        $this->entityManager->flush();

        $this->assertAccountBalances($accounts, $expectedDataResult);
        $this->assertCollection($client);
        $this->assertTransfers($client, $collection, $aggregatingAccount, $beneficiaryParty, $expectedDataResult);
        $this->assertHolds($client);
    }

    public function collectAndProcessDataProvider(): array
    {
        $defaultGeorgiaStateTreasuryBeneficiary = (new PartyAccountCountry())
            ->setAccount('*********')
            ->setCountry('GE')
            ->setName('State Treasury')
            ->setBic('TRESGE22')
        ;

        return [
            'Case 1: 1 account (main account, index: 0), 1 out transfer (fee 0.50 GEL)' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 0,
                    ],
                    'accountBalances' => [
                        new Money('600', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('400', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '0***********',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('1000', 'GEL'),
                        'holdAmount' => new Money('500', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('400', 'GEL'),
            ],
            'Case 2: 1 account (main account, index: 0), 1 out transfer (fee 0 GEL)' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 0,
                    ],
                    'accountBalances' => [
                        new Money('300', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('11200', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('11500', 'GEL'),
                        'holdAmount' => new Money('11500', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('11200', 'GEL'),
            ],
            'Case 3: 3 accounts, 1 out + 2 internal transfers (0 GEL fee)' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 2,
                    ],
                    'accountBalances' => [
                        new Money('0', 'GEL'),
                        new Money('0', 'GEL'),
                        new Money('1197', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('312', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('4808', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('5123', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('3', 'GEL'),
                        'holdAmount' => new Money('3', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('312', 'GEL'),
                        'holdAmount' => new Money('312', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('6005', 'GEL'),
                        'holdAmount' => new Money('6000', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('5123', 'GEL'),
            ],
            'Case 4: 5 accounts (0 GEL fee), 1 out + 4 internal transfers' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 4,
                    ],
                    'accountBalances' => [
                        new Money('100', 'GEL'),
                        new Money('0', 'GEL'),
                        new Money('0', 'GEL'),
                        new Money('0', 'GEL'),
                        new Money('300', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('1000', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('1000', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('1000', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('700', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('5600', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('2000', 'GEL'),
                        'holdAmount' => new Money('1900', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('1000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('1000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('1000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('1000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('5600', 'GEL'),
            ],
            'Case 5: 2 accounts (0 GEL fee), 1 out + 1 internal transfers' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 1,
                    ],
                    'accountBalances' => [
                        new Money('99000', 'GEL'),
                        new Money('99450', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('550', 'GEL'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('1550', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('100000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('100000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('1550', 'GEL'),
            ],
            'Case 6: 2 accounts (0 GEL fee), 1 account with empty amount, 1 out transfer' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 0,
                    ],
                    'accountBalances' => [
                        new Money('11', 'GEL'),
                        new Money('0', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('89', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('100', 'GEL'),
                        'holdAmount' => new Money('90', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('0', 'GEL'),
                        'holdAmount' => new Money('90', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('89', 'GEL'),
            ],
            'Case 7: 2 accounts, amount on hold less than required transfer amount' => [
                [
                    'error' => [
                        'exception' => DebtTransferManagerException::class,
                        'message' => 'Not enough amount on hold to preform write off operation',
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('100', 'GEL'),
                        'holdAmount' => new Money('90', 'GEL'),
                    ],
                    [
                        'initialAmount' => new Money('0', 'GEL'),
                        'holdAmount' => new Money('90', 'GEL'),
                    ],
                ],
                0,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('100', 'GEL'),
            ],
            'Case 8: 2 accounts (0 GEL fee), 1 out + 1 internal transfers contis account' => [
                [
                    'amountOfCreatedTransfers' => [
                        TransferOutBank::TYPE_OUT_BANK => 1,
                        TransferInternal::TYPE_INTERNAL => 1,
                    ],
                    'accountBalances' => [
                        new Money('500', 'EUR'),
                        new Money('450', 'GEL'),
                    ],
                    'transfers' => [
                        [
                            'amount' => new Money('500', 'EUR'),
                            'type' => TransferInternal::TYPE_INTERNAL,
                        ],
                        [
                            'amount' => new Money('1550', 'GEL'),
                            'type' => TransferOutBank::TYPE_OUT_BANK,
                        ],
                    ],
                ],
                [
                    'number' => '************',
                    'status' => Collection::STATUS_ACTIVE,
                    'writeOffStatus' => Collection::WRITE_OFF_STATUS_STARTED,
                    'collectionType' => Collection::TYPE_GRS,
                ],
                [
                    [
                        'initialAmount' => new Money('1000', 'EUR'),
                        'holdAmount' => new Money('1000', 'GEL'),
                        'type' => Account::TYPE_CONTIS,
                    ],
                    [
                        'initialAmount' => new Money('2000', 'GEL'),
                        'holdAmount' => new Money('1000', 'GEL'),
                    ],
                ],
                1,
                $defaultGeorgiaStateTreasuryBeneficiary,
                new Money('1550', 'GEL'),
            ],
        ];
    }

    /**
     * @param array $data
     * @param Client $client
     *
     * @return Collection|null
     */
    private function createCollectionFromData(array $data, Client $client): ?Collection
    {
        if ($data['collectionType'] !== Collection::TYPE_GRS) {
            /**
             * Test can be extended with different collection types.
             * Or definition of specific writeoff processor tests required.
             */

            return null;
        }

        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();

        return $this->fixturesHelper->createDebtCollectionGrs(
            $data['number'],
            $debtClientCollection,
            $licensedPartner,
            $data['status'],
            $data['writeOffStatus']
        );
    }

    /**
     * @TODO SUPPORT-73298 ( https://jira.paysera.net/browse/SUPPORT-70175?focusedCommentId=402875 )
     * This is temporary solution:
     * According to route_resolver configuration it's possible to create
     * ge_nbg_rtgs transfer only for client with specific ID: ********
     *
     * Since this test - transfer related, we have to mock routeResolver.
     * Remove mock of service when it will be possible to use any account.
     */
    private function resolveGeorgiaRTGSRoute(Money $money): Route
    {
        /** @var CommissionRule $commissionRule */
        $commissionRule = $this->getContainer()->get('evp_bank_transfer.commission_rule.gel_5');

        if ($money->isGte(new Money('30000', 'GEL'))) {
            $commissionRule = $this->getContainer()->get('evp_bank_transfer.commission_rule.gel_200');
        }

        if ($money->isGte(new Money('10000', 'GEL'))) {
            $commissionRule = $this->getContainer()->get('evp_bank_transfer.commission_rule.gel_100');
        }

        return new Route(
            'ge_nbg_rtgs',
            'ge_nbg_rtgs',
            $commissionRule
        );
    }

    private function getMainAccountBalance(Account $account, string $currency): ?Money
    {
        $balanceData = $this->internalMainAccountRepository->findFiatBalanceSumByAccountGroupedByCurrency($account);

        foreach ($balanceData as $balanceDatum) {
            if ($balanceDatum['balanceCurrency'] === $currency) {
                return new Money(
                    $balanceDatum['balanceSum'],
                    $currency
                );
            }
        }

        return null;
    }

    private function assertAccountBalances(array $accounts, array $expectedDataResult): void {
        foreach ($accounts as $key => $account) {
            /** @var Money $expectedBalance */
            $expectedBalance = $expectedDataResult['accountBalances'][$key];

            $this->assertEquals(
                $expectedBalance,
                $this->getMainAccountBalance($account, $expectedBalance->getCurrency())
            );
        }
    }

    private function assertTransfers(
        Client $client,
        Collection $collection,
        Account $aggregatingAccount,
        PartyBank $beneficiaryParty,
        array $expectedDataResult
    ): void {
        $countOfOutTransfers = $expectedDataResult['amountOfCreatedTransfers'][TransferOutBank::TYPE_OUT_BANK];
        $countOfInternalTransfers = $expectedDataResult['amountOfCreatedTransfers'][TransferInternal::TYPE_INTERNAL];
        $transfers = $this->transferRepository->findAll();

        $this->assertCount($countOfOutTransfers + $countOfInternalTransfers, $transfers);

        /** @var Transfer $transfer */
        foreach ($transfers as $transfer) {
            $this->assertCount(1, $transfer->getTransferSignatures());
            $this->assertSignature(
                $this->prepareExpectedTransferSignature($transfer, $collection),
                $transfer->getTransferSignatures()[0]
            );

            $this->assertEquals($client->getId(), $transfer->getClient()->getId());
            $this->assertEquals(Transfer::PURPOSE_DEBT_AUTOMATIC, $transfer->getPurpose());
            $this->assertTrue($transfer->isFormedAutomatically());
            $this->assertFalse($transfer->getCancelable());
            $this->assertEquals(Transfer::PRIORITY_NORMAL, $transfer->getPriority());

            /** @var Money $expectedAmount */
            $expectedAmount = $expectedDataResult['transfers'][$transfer->getTransferRequest()->getId() - 1]['amount'];
            $this->assertTrue(
                $expectedAmount->isEqual(new Money($transfer->getAmount(), $transfer->getAmountCurrency()))
            );
            $this->assertEquals($expectedAmount->getCurrency(), $transfer->getAmountCurrency());
            $this->assertEquals(
                $expectedDataResult['transfers'][$transfer->getTransferRequest()->getId() - 1]['type'],
                $transfer->getType()
            );

            if ($transfer instanceof TransferInternal) {
                $this->assertTrue($transfer->getPayer() instanceof PartyAccount);
                $this->assertTrue($transfer->getBeneficiary() instanceof PartyAccount);
                $this->assertEquals($aggregatingAccount->getNumber(), $transfer->getBeneficiary()->getNumber());
                $this->assertEquals(
                    sprintf(
                        'Internal transfer (%s) for collection order (%s)',
                        $aggregatingAccount->getNumber(),
                        $collection->getNumber(),
                    ),
                    $transfer->getDetails()
                );
                $this->assertTrue($transfer->isDone());
            }

            if ($transfer instanceof TransferOutBank) {
                $this->assertTrue($transfer->getPayer() instanceof PartyIban);
                $this->assertEquals($aggregatingAccount->getIban(), $transfer->getPayer()->getIban());

                if ($beneficiaryParty instanceof PartyAccountCountry) {
                    $transferBeneficiary = $transfer->getBeneficiary();

                    $this->assertTrue($transferBeneficiary instanceof PartyAccountCountry);
                    $this->assertEquals($beneficiaryParty->getAccount(), $beneficiaryParty->getAccount());
                    $this->assertEquals($beneficiaryParty->getCountry(), $beneficiaryParty->getCountry());
                    $this->assertEquals($beneficiaryParty->getName(), $beneficiaryParty->getName());
                    $this->assertEquals($beneficiaryParty->getBic(), $beneficiaryParty->getBic());
                }

                $this->assertEquals('ge_nbg_rtgs', $transfer->getBank());
                $this->assertTrue($transfer->isReady());
                $this->assertEquals(
                    sprintf('Payment for collection order (%s)', $collection->getNumber()),
                    $transfer->getDetails()
                );
            }
        }
    }

    private function assertHolds(Client $client): void {
        $collection = $this->collectionRepository->findOneBy(['client' => $client]);

        foreach ($collection->getHolds() as $hold) {
            $this->assertEquals(Hold::STATUS_DONE, $hold->getStatus());
        }
    }

    private function assertCollection(Client $client): void {
        $collection = $this->collectionRepository->findOneBy(['client' => $client]);

        $this->assertEquals(
            count($this->transferRepository->findAll()),
            count($collection->getTransfers())
        );
    }

    private function prepareExpectedTransferSignature(
        Transfer $transfer,
        Collection $collection
    ): TransferSignature {
        $expectedTransferSignature = new TransferSignature();

        if ($transfer instanceof TransferInternal) {
            $expectedTransferSignature->addParameter(
                'automatic',
                'paysera_ge_debt_internal_payment'
            );
        }

        if ($transfer instanceof TransferOutBank) {
            $expectedTransferSignature->addParameter(
                'automatic',
                'paysera_ge_debt_out_payment'
            );
        }

        $expectedTransferSignature->addParameter('cancellable', false);
        $expectedTransferSignature->addParameter('is_super_admin', '1');
        $expectedTransferSignature->addParameter('debt_collection_number', $collection->getNumber());

        return $expectedTransferSignature;
    }

    private function assertSignature(
        TransferSignature $expectedTransferSignature,
        TransferSignature $transferSignature
    ): void {
        $signatureRequiredKeys = [
            'automatic',
            'cancellable',
            'is_super_admin',
            'debt_collection_number',
        ];

        foreach ($signatureRequiredKeys as $signatureRequiredKey) {
            $this->assertEquals(
                $expectedTransferSignature->getParameter($signatureRequiredKey),
                $transferSignature->getParameter($signatureRequiredKey)
            );
        }
    }
}
