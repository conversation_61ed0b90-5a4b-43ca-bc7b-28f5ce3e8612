<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Listener;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Entity\SeizureManual;
use Evp\Bundle\DebtBundle\Repository\CollectionManualRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientCollectionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientSeizureRepository;
use Evp\Bundle\DebtBundle\Repository\SeizureManualRepository;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\Workflow\Registry;

class ManualDebtsListenerTest extends PersistableWebTestCase
{
    private Registry $workflowRegistry;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionManualRepository $collectionManualRepository;
    private SeizureManualRepository $seizureManualRepository;
    private HoldHelper $holdHelper;
    private DebtClientCollectionRepository $debtClientCollectionRepository;
    private DebtClientSeizureRepository $debtClientSeizureRepository;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->workflowRegistry = $this->getContainer()->get('workflow.registry');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->collectionManualRepository = $this->getContainer()->get('evp_debt.repository.collection_manual');
        $this->seizureManualRepository = $this->getContainer()->get('evp_debt.repository.seizure_manual');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->holdHelper = $this->getContainer()->get('evp_debt.service.hold_service.hold_helper');
        $this->debtClientCollectionRepository = $this->getContainer()->get('evp_debt.repository.debt_client_collection');
        $this->debtClientSeizureRepository = $this->getContainer()->get('evp_debt.repository.debt_client_seizure');
    }

    public function testCollectionActivation(): void
    {
        $this->prepareCollection();

        $collection = $this->collectionManualRepository->findAll()[0];
        $this->assertEquals(CollectionManual::STATUS_REGISTERED, $collection->getStatus());
        $this->assertCount(0, $this->getCollectionActiveHolds($collection));

        $this->workflowRegistry->get($collection)->apply($collection, CollectionManual::TRANSITION_TO_CONFIRMED);
        $this->workflowRegistry->get($collection)->apply($collection, CollectionManual::TRANSITION_TO_ACTIVE);
        $this->assertEquals(CollectionManual::STATUS_ACTIVE, $collection->getStatus());

        $activeHolds = $this->getCollectionActiveHolds($collection);
        $this->assertCount(1, $activeHolds);
        $this->assertEquals(new Money(100, 'GEL'), $activeHolds[0]->getAmountMoney());
    }

    public function testCollectionSuspending(): void
    {
        $this->testCollectionActivation();
        $collection = $this->collectionManualRepository->findAll()[0];
        $this->workflowRegistry->get($collection)->apply($collection, CollectionManual::TRANSITION_TO_SUSPENDED);
        $this->assertEquals(CollectionManual::STATUS_SUSPENDED, $collection->getStatus());
        $this->assertCount(0, $this->getCollectionActiveHolds($collection));
    }

    public function testCollectionClosure(): void
    {
        $this->testCollectionActivation();
        $collection = $this->collectionManualRepository->findAll()[0];
        $this->workflowRegistry->get($collection)->apply($collection, CollectionManual::TRANSITION_TO_CLOSED);
        $this->assertEquals(CollectionManual::STATUS_CLOSED, $collection->getStatus());
        $this->assertCount(0, $this->getCollectionActiveHolds($collection));
    }

    public function testSeizureActivation(): void
    {
        $this->prepareSeizure();

        $seizure = $this->seizureManualRepository->findAll()[0];
        $this->assertEquals(SeizureManual::STATUS_REGISTERED, $seizure->getStatus());
        $this->assertCount(0, $this->getSeizureActiveHolds($seizure));

        $this->workflowRegistry->get($seizure)->apply($seizure, SeizureManual::TRANSITION_TO_CONFIRMED);
        $this->workflowRegistry->get($seizure)->apply($seizure, SeizureManual::TRANSITION_TO_ACTIVE);
        $this->assertEquals(SeizureManual::STATUS_ACTIVE, $seizure->getStatus());

        $activeHolds = $this->getSeizureActiveHolds($seizure);
        $this->assertCount(1, $activeHolds);
        $this->assertEquals(new Money(100, 'GEL'), $activeHolds[0]->getAmountMoney());
    }

    public function testSeizureSuspending(): void
    {
        $this->testSeizureActivation();
        $seizure = $this->seizureManualRepository->findAll()[0];
        $this->workflowRegistry->get($seizure)->apply($seizure, SeizureManual::TRANSITION_TO_SUSPENDED);
        $this->assertEquals(SeizureManual::STATUS_SUSPENDED, $seizure->getStatus());
        $activeHolds = $this->getSeizureActiveHolds($seizure);
        $this->assertCount(0, $activeHolds);
    }

    public function testSeizureClosure(): void
    {
        $this->testSeizureActivation();
        $seizure = $this->seizureManualRepository->findAll()[0];
        $this->workflowRegistry->get($seizure)->apply($seizure, SeizureManual::TRANSITION_TO_CLOSED);
        $this->assertEquals(SeizureManual::STATUS_CLOSED, $seizure->getStatus());
        $activeHolds = $this->getSeizureActiveHolds($seizure);
        $this->assertCount(0, $activeHolds);
    }

    private function prepareCollection(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $account = $this->fixturesHelper->createAccount($client);
        $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtCollectionManual(
            '1',
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner
        );
        $this->entityManager->flush();
    }

    private function prepareSeizure(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $account = $this->fixturesHelper->createAccount($client);
        $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtSeizureManual(
            '1',
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensedPartner
        );
        $this->entityManager->flush();
    }

    private function getSeizureActiveHolds($seizure): array
    {
        $debtClientSeizure = $this->debtClientSeizureRepository->findOneByClient($seizure->getClient());

        return  $this->holdHelper->filterActiveHolds($debtClientSeizure->getHolds()->toArray());
    }

    private function getCollectionActiveHolds($collection): array
    {
        $debtClientCollection = $this->debtClientCollectionRepository->findOneByClient($collection->getClient());

        return $this->holdHelper->filterActiveHolds($debtClientCollection->getHolds()->toArray());
    }
}
