<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Listener;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Entity\SeizureManual;
use Evp\Bundle\DebtBundle\Repository\CollectionManualRepository;
use Evp\Bundle\DebtBundle\Repository\SeizureManualRepository;
use Evp\Bundle\DebtBundle\Service\CollectionProcessorManager;
use Evp\Bundle\DebtBundle\Service\SeizureProcessorManager;
use Evp\Component\DebtCommon\Entity\Collection\CancelCollection\CommonCancelCollectionGrs;
use Evp\Component\DebtCommon\Entity\Collection\Collection\CommonCollectionGrs;
use Evp\Component\DebtCommon\Entity\Collection\ReturnCollection\CommonReturnCollectionGrs;
use Evp\Component\DebtCommon\Entity\Seizure\CancelSeizure\CommonCancelSeizureGrs;
use Evp\Component\DebtCommon\Entity\Seizure\Seizure\CommonSeizureGrs;
use Exception;
use Paysera\Client\UserNotificationClient\Entity\Notification;
use Paysera\Client\UserNotificationClient\Entity\NotificationRequest;
use Paysera\Client\UserNotificationClient\UserNotificationClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Workflow\Registry;

class ClientNotificationListenerTest extends PersistableWebTestCase
{
    private const KEY_DEBT_ACTIVATED_NOTIFICATION = 'debt_activated_notification';
    private const KEY_DEBT_DEACTIVATED_NOTIFICATION = 'debt_deactivated_notification';

    private Registry $workflowRegistry;
    private EntityManager $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionManualRepository $collectionManualRepository;
    private SeizureManualRepository $seizureManualRepository;
    private CollectionProcessorManager $collectionProcessorManager;
    private SeizureProcessorManager $seizureProcessorManager;
    private MockObject $userNotificationClient;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();

        $this->userNotificationClient = $this->createMock(UserNotificationClient::class);
        $this->getContainer()->set('evp_debt.user_notification_client.client', $this->userNotificationClient);

        $this->workflowRegistry = $this->getContainer()->get('workflow.registry');
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        /** @noinspection MissingService */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->collectionManualRepository = $this->getContainer()->get('evp_debt.repository.collection_manual');
        $this->seizureManualRepository = $this->getContainer()->get('evp_debt.repository.seizure_manual');
        $this->collectionProcessorManager = $this->getContainer()->get('evp_debt.service.collection_processor_manager');
        $this->seizureProcessorManager = $this->getContainer()->get('evp_debt.service.seizure_processor_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @throws Exception
     * @dataProvider onManualCollectionStatusChangedDataProvider
     */
    public function testOnManualCollectionStatusChanged(array $transitions, array $expectedNotifications): void
    {
        $this->prepareCollection();

        $collection = $this->collectionManualRepository->findOneBy(['id' => 1]);
        $this->assertEquals(CollectionManual::STATUS_REGISTERED, $collection->getStatus());

        $notifications = [];
        $this->collectNotifications($notifications);



        foreach ($transitions as $transition) {
            $this->workflowRegistry->get($collection)->apply($collection, $transition);
        }

        $this->expectInfoLog('Manual collection status changed.');
        $this->assertEquals($expectedNotifications, $notifications);
    }

    public function onManualCollectionStatusChangedDataProvider(): array
    {
        return [
            'registered - confirmed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - active' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - done' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_DONE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active - done' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_DONE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active - suspended' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - suspended - active' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - active - done' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_DONE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - active - suspended' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - active - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_ACTIVE,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_SUSPENDED,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CONFIRMED,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_RETURNED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned - registered' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_RETURNED,
                    CollectionManual::TRANSITION_TO_REGISTERED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_RETURNED,
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - closed' => [
                'transitions' => [
                    CollectionManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
        ];
    }

    /**
     * @throws Exception
     * @dataProvider onManualSeizureStatusChangedDataProvider
     * */
    public function testOnManualSeizureStatusChanged(array $transitions, array $expectedNotifications): void
    {
        $this->prepareSeizure();

        $seizure = $this->seizureManualRepository->findOneBy(['id' => 1]);
        $this->assertEquals(SeizureManual::STATUS_REGISTERED, $seizure->getStatus());

        $notifications = [];
        $this->collectNotifications($notifications);

        foreach ($transitions as $transition) {
            $this->workflowRegistry->get($seizure)->apply($seizure, $transition);
        }

        $this->expectInfoLog('Manual seizure status changed.');
        $this->assertEquals($expectedNotifications, $notifications);
    }

    public function onManualSeizureStatusChangedDataProvider(): array
    {
        return [
            'registered - confirmed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - active' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active - suspended' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - active - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - suspended - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - active - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - suspended - active' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - active - suspended' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - active - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_ACTIVE,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
                    [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
                ],
            ],
            'registered - confirmed - suspended - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_SUSPENDED,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - confirmed - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CONFIRMED,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_RETURNED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned - registered' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_RETURNED,
                    SeizureManual::TRANSITION_TO_REGISTERED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - returned - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_RETURNED,
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
            'registered - closed' => [
                'transitions' => [
                    SeizureManual::TRANSITION_TO_CLOSED,
                ],
                'expectedNotifications' => [],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function testOnCollectionCreated(): void
    {
        $expectedNotifications = [
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
        ];

        $notifications = [];
        $this->collectNotifications($notifications);

        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createLicensedPartner();
        $this->entityManager->flush();

        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '1'));

        $this->expectInfoLog('Collection event.');
        $this->assertEquals($expectedNotifications, $notifications, 'Notification about collection created should be sent');
    }

    /**
     * @throws Exception
     */
    public function testOnCollectionCancelled(): void
    {
        $expectedNotifications = [
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
        ];

        $notifications = [];
        $this->collectNotifications($notifications);

        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createDebtClientCollection($account->getClient());
        $this->fixturesHelper->createLicensedPartner();
        $this->entityManager->flush();

        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '1'));
        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '2'));
        $this->entityManager->flush();

        $this->collectionProcessorManager->cancel($this->prepareCommonCancelCollectionGrs('1'));
        $this->collectionProcessorManager->cancel($this->prepareCommonCancelCollectionGrs('2'));

        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '3'));
        $this->entityManager->flush();

        $this->collectionProcessorManager->cancel($this->prepareCommonCancelCollectionGrs('3'));

        $this->expectInfoLog('Collection event.');
        $this->assertEquals($expectedNotifications, $notifications, 'Notification about collection created should be sent');
    }

    /**
     * @throws Exception
     */
    public function testOnCollectionReturned(): void
    {
        $expectedNotifications = [
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
        ];

        $notifications = [];
        $this->collectNotifications($notifications);

        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtClientCollection($account->getClient());
        $this->entityManager->flush();

        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '1'));
        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '2'));
        $this->collectionProcessorManager->create($this->prepareCommonCollectionGrs($account->getClient(), '3'));
        $this->entityManager->flush();

        $returnCollection = $this->prepareCommonReturnCollectionGrs($account->getClient());
        $this->collectionProcessorManager->returnCollections($returnCollection);

        $this->expectInfoLog('Collection event.');
        $this->assertEquals($expectedNotifications, $notifications, 'Notification about collection created should be sent');
    }

    /**
     * @throws Exception
     */
    public function testOnSeizureCreated(): void
    {
        $expectedNotifications = [
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
        ];

        $notifications = [];
        $this->collectNotifications($notifications);


        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createLicensedPartner();
        $this->entityManager->flush();

        $this->seizureProcessorManager->create($this->prepareCommonSeizureGrs($account->getClient(), '1'));

        $this->expectInfoLog('Seizure event.');
        $this->assertEquals($expectedNotifications, $notifications, 'Notification about seizure created should be sent');
    }

    /**
     * @throws Exception
     */
    public function testOnSeizureCancelled(): void
    {
        $expectedNotifications = [
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_ACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_EMAIL],
            [self::KEY_DEBT_DEACTIVATED_NOTIFICATION, Notification::CHANNEL_PUSH],
        ];

        $notifications = [];
        $this->collectNotifications($notifications);

        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createDebtClientSeizure($account->getClient());
        $this->fixturesHelper->createLicensedPartner();
        $this->entityManager->flush();

        $this->seizureProcessorManager->create($this->prepareCommonSeizureGrs($account->getClient(), '1'));
        $this->seizureProcessorManager->create($this->prepareCommonSeizureGrs($account->getClient(), '2'));
        $this->entityManager->flush();

        $this->seizureProcessorManager->cancel($this->prepareCommonCancelSeizureGrs('1'));
        $this->seizureProcessorManager->cancel($this->prepareCommonCancelSeizureGrs('2'));

        $this->seizureProcessorManager->create($this->prepareCommonSeizureGrs($account->getClient(), '3'));
        $this->entityManager->flush();

        $this->seizureProcessorManager->cancel($this->prepareCommonCancelSeizureGrs('3'));

        $this->expectInfoLog('Seizure event.');
        $this->assertEquals($expectedNotifications, $notifications, 'Notification about seizure created should be sent');
    }

    private function collectNotifications(array &$notifications): void
    {
        $this->userNotificationClient
            ->expects($this->any())
            ->method('createNotificationRequest')
            ->willReturnCallback(function (NotificationRequest $notificationRequest) use (&$notifications) {
                foreach ($notificationRequest->getNotifications() as $notification) {
                    $notifications[] = [$notification->getNotificationKey(), $notification->getChannel()];
                }
            })
        ;
    }

    /**
     * @throws Exception
     */
    private function prepareCollection(): void
    {
        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtCollectionManual(
            '1',
            $this->fixturesHelper->createDebtClientCollection($account->getClient()),
            $licensedPartner
        );
        $this->entityManager->flush();
    }

    /**
     * @throws Exception
     */
    private function prepareSeizure(): void
    {
        $account = $this->fixturesHelper->createAccount();
        $this->fixturesHelper->createAccountPartner($account, LicensedPartner::PAYSERA_GEORGIA);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtSeizureManual(
            '1',
            $this->fixturesHelper->createDebtClientSeizure($account->getClient()),
            $licensedPartner
        );
        $this->entityManager->flush();
    }

    private function prepareCommonCollectionGrs(Client $client, string $number): CommonCollectionGrs
    {
        $commonCollectionGrs = new CommonCollectionGrs();
        $commonCollectionGrs
            ->setPriority(1)
            ->setReceiverAccount('*********')
            ->setReceiverCode('TRESGE22')
            ->setOperDate(new DateTimeImmutable())
            ->setExternalStatus(1)
            ->setNote(null)
            ->setThirdPersonId(null)
            ->setThirdPersonIdAlt(null)
            ->setThirdPersonName(null)
            ->setClientId($client->getId())
            ->setAmount('100')
            ->setCurrency('GEL')
            ->setNumber($number)
        ;
        return $commonCollectionGrs;
    }

    private function prepareCommonCancelCollectionGrs(string $number): CommonCancelCollectionGrs
    {
        $commonCancelCollectionGrs = new CommonCancelCollectionGrs();
        $commonCancelCollectionGrs->setNumber($number);
        return $commonCancelCollectionGrs;
    }

    private function prepareCommonReturnCollectionGrs(Client $client): CommonReturnCollectionGrs
    {
        $commonReturnCollectionGrs = new CommonReturnCollectionGrs();
        $commonReturnCollectionGrs->setClientId($client->getId());
        return $commonReturnCollectionGrs;
    }

    private function prepareCommonSeizureGrs(Client $client, string $number): CommonSeizureGrs
    {
        $commonSeizureGrs = new CommonSeizureGrs();
        $commonSeizureGrs
            ->setClientId($client->getId())
            ->setNumber($number)
            ->setAmount('100')
            ->setCurrency('GEL')
        ;

        return $commonSeizureGrs;
    }

    private function prepareCommonCancelSeizureGrs(string $number): CommonCancelSeizureGrs
    {
        $commonCancelSeizureGrs = new CommonCancelSeizureGrs();
        $commonCancelSeizureGrs->setNumber($number);
        return $commonCancelSeizureGrs;
    }
}
