<?php

declare(strict_types=1);

namespace Command\Monitoring;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Command\Monitoring\MonitorWriteOffCommand;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\DebtClientCollection;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorWriteOffCommandTest extends PersistableWebTestCase
{
    private const COLLECTION_PRIORITIES = [1,2,3,4];
    private const PROBLEM_WRITE_OFF_STARTED_BEFORE_TODAY = 'write_off_started_before_today';
    private const PROBLEM_FAILED_OFF_STATUSES = 'failed_write_off_statuses';

    private CommandTester $commandTester;
    private FixturesHelper $fixturesHelper;
    private EntityManagerInterface $entityManager;
    private MockObject $monitoringClientMock;
    private string $monitoringKey;

    protected function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->monitoringClientMock = $this->createMock(MonitoringClient::class);
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->monitoringKey = $this->getContainer()->getParameter('evp_debt.monitor_key');

        $command = new MonitorWriteOffCommand(
            $this->getContainer()->get('evp_debt.repository.collection'),
            $this->monitoringClientMock,
            self::COLLECTION_PRIORITIES,
            $this->monitoringKey,
        );
        $this->commandTester = new CommandTester($command);
    }

    public function testProcess(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);

        $this->createStartedWriteOffsBeforeToday($debtClientCollection, $licensedPartner);
        $this->createFailedWriteOffs($debtClientCollection, $licensedPartner);

        $this->entityManager->flush();

        $this->monitoringClientMock
            ->expects($this->exactly(5))
            ->method('writeValue')
            ->withConsecutive(
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_WRITE_OFF_STARTED_BEFORE_TODAY, 'organizationPriority' => 1]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_WRITE_OFF_STARTED_BEFORE_TODAY, 'organizationPriority' => 2]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_WRITE_OFF_STARTED_BEFORE_TODAY, 'organizationPriority' => 3]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_WRITE_OFF_STARTED_BEFORE_TODAY, 'organizationPriority' => 4]],

                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_FAILED_OFF_STATUSES]],
            )
        ;
        $this->commandTester->execute([]);
    }

    private function createStartedWriteOffsBeforeToday(
        DebtClientCollection $debtClientCollection,
        LicensedPartner $licensedPartner
    ): void {
        $date = new DateTimeImmutable('yesterday');

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '1',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            Collection::WRITE_OFF_STATUS_STARTED,
            1
        );
        $collection->setUpdatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '2',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            Collection::WRITE_OFF_STATUS_STARTED,
            2
        );
        $collection->setUpdatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '3',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            Collection::WRITE_OFF_STATUS_STARTED,
            3
        );
        $collection->setUpdatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '4',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            Collection::WRITE_OFF_STATUS_STARTED,
            4
        );
        $collection->setUpdatedAt($date);
    }

    private function createFailedWriteOffs(
        DebtClientCollection $debtClientCollection,
        LicensedPartner $licensedPartner
    ): void {
        $this->fixturesHelper->createDebtCollectionGrs(
            '5',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            Collection::WRITE_OFF_STATUS_FAILED,
        );
    }
}
