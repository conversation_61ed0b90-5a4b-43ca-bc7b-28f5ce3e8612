<?php

declare(strict_types=1);

namespace Command\Monitoring;

use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Command\Monitoring\MonitorDebtsCountCommand;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\DebtClientCollection;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorDebtsCountCommandTest extends PersistableWebTestCase
{
    private const COLLECTION_PRIORITIES = [1,2,3,4];
    private const SEIZURE_PRIORITIES = [1,2,3,4];

    private const PROBLEM_ACTIVE_COLLECTIONS = 'active_collections_count';
    private const PROBLEM_ACTIVE_SEIZURES = 'active_seizures_count';
    private const PROBLEM_CREATED_COLLECTIONS = 'created_collections_count';
    private const PROBLEM_CREATED_SEIZURES = 'created_seizures_count';
    private const PROBLEM_CANCELLED_COLLECTIONS = 'cancelled_collections_count';
    private const PROBLEM_CANCELLED_SEIZURES = 'cancelled_seizures_count';
    private const PROBLEM_OLD_COLLECTIONS = 'old_collections_count';
    private const PROBLEM_OLD_SEIZURES = 'old_seizures_count';

    private CommandTester $commandTester;
    private FixturesHelper $fixturesHelper;
    private EntityManagerInterface $entityManager;
    private MockObject $monitoringClientMock;
    private string $monitoringKey;

    public function setUp(): void
    {
        $this->createClientWithNewDatabase();
        $this->monitoringClientMock = $this->createMock(MonitoringClient::class);
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->monitoringKey = $this->getContainer()->getParameter('evp_debt.monitor_key');

        $command = new MonitorDebtsCountCommand(
            $this->getContainer()->get('evp_debt.repository.collection'),
            $this->getContainer()->get('evp_debt.repository.seizure'),
            $this->monitoringClientMock,
            self::COLLECTION_PRIORITIES,
            self::SEIZURE_PRIORITIES,
            $this->monitoringKey,
        );
        $this->commandTester = new CommandTester($command);
    }

    public function testProcess(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $debtClientCollection = $this->fixturesHelper->createDebtClientCollection($client);
        $debtClientSeizure = $this->fixturesHelper->createDebtClientSeizure($client);

        $this->createActiveCollections($debtClientCollection, $licensedPartner);
        $this->createActiveSeizures($debtClientSeizure, $licensedPartner);
        $this->createCreatedCollections($debtClientCollection, $licensedPartner);
        $this->createCreatedSeizures($debtClientSeizure, $licensedPartner);
        $this->createCancelledCollections($debtClientCollection, $licensedPartner);
        $this->createCancelledSeizures($debtClientSeizure, $licensedPartner);
        $this->createOldCollections($debtClientCollection, $licensedPartner);
        $this->createOldSeizures($debtClientSeizure, $licensedPartner);

        $this->entityManager->flush();

        $this->monitoringClientMock
            ->expects($this->exactly(32))
            ->method('writeValue')
            ->withConsecutive(
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_COLLECTIONS, 'organizationPriority' => 1]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_COLLECTIONS, 'organizationPriority' => 2]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_COLLECTIONS, 'organizationPriority' => 3]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_COLLECTIONS, 'organizationPriority' => 4]],

                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_SEIZURES, 'organizationPriority' => 1]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_SEIZURES, 'organizationPriority' => 2]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_SEIZURES, 'organizationPriority' => 3]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_ACTIVE_SEIZURES, 'organizationPriority' => 4]],

                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_COLLECTIONS, 'organizationPriority' => 1]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_COLLECTIONS, 'organizationPriority' => 2]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_COLLECTIONS, 'organizationPriority' => 3]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_COLLECTIONS, 'organizationPriority' => 4]],

                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_SEIZURES, 'organizationPriority' => 1]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_SEIZURES, 'organizationPriority' => 2]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_SEIZURES, 'organizationPriority' => 3]],
                [$this->monitoringKey, 3, ['problem' => self::PROBLEM_CREATED_SEIZURES, 'organizationPriority' => 4]],

                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_COLLECTIONS, 'organizationPriority' => 1]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_COLLECTIONS, 'organizationPriority' => 2]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_COLLECTIONS, 'organizationPriority' => 3]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_COLLECTIONS, 'organizationPriority' => 4]],

                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_SEIZURES, 'organizationPriority' => 1]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_SEIZURES, 'organizationPriority' => 2]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_SEIZURES, 'organizationPriority' => 3]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_CANCELLED_SEIZURES, 'organizationPriority' => 4]],

                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_COLLECTIONS, 'organizationPriority' => 1]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_COLLECTIONS, 'organizationPriority' => 2]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_COLLECTIONS, 'organizationPriority' => 3]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_COLLECTIONS, 'organizationPriority' => 4]],

                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_SEIZURES, 'organizationPriority' => 1]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_SEIZURES, 'organizationPriority' => 2]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_SEIZURES, 'organizationPriority' => 3]],
                [$this->monitoringKey, 1, ['problem' => self::PROBLEM_OLD_SEIZURES, 'organizationPriority' => 4]],
            )
        ;

        $this->commandTester->execute([]);
    }

    private function createActiveCollections(DebtClientCollection $debtClientCollection, LicensedPartner $licensedPartner): void
    {
        $this->fixturesHelper->createDebtCollectionGrs(
            '1',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            1
        );

        $this->fixturesHelper->createDebtCollectionGeb(
            '2',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            2
        );

        $this->fixturesHelper->createDebtCollectionGrs(
            '3',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            3
        );

        $this->fixturesHelper->createDebtCollectionGeb(
            '4',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            4
        );
    }

    private function createActiveSeizures(DebtClientSeizure $debtClientSeizure, LicensedPartner $licensedPartner): void
    {
        $this->fixturesHelper->createDebtSeizureGrs(
            '1',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            1
        );

        $this->fixturesHelper->createDebtSeizureGeb(
            '2',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            2
        );

        $this->fixturesHelper->createDebtSeizureGrs(
            '3',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            3
        );

        $this->fixturesHelper->createDebtSeizureGrs(
            '4',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            4
        );
    }

    private function createCreatedCollections(DebtClientCollection $debtClientCollection, LicensedPartner $licensedPartner): void
    {
        $date = new DateTimeImmutable('today midnight');

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '5',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            1
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '6',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            2
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '7',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            3
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '8',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            4
        );
        $collection->setCreatedAt($date);
    }

    private function createCreatedSeizures(DebtClientSeizure $debtClientSeizure, LicensedPartner $licensedPartner): void
    {
        $date = new DateTimeImmutable('today midnight');

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '5',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            1
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '6',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            2
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '7',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            3
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '8',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            4
        );
        $seizure->setCreatedAt($date);
    }

    private function createCancelledCollections(DebtClientCollection $debtClientCollection, LicensedPartner $licensedPartner): void
    {
        $date = new DateTimeImmutable('today midnight');

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '9',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            null,
            1
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '10',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_RETURNED,
            null,
            2
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '11',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            null,
            3
        );
        $collection->setCreatedAt($date);

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '12',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_RETURNED,
            null,
            4
        );
        $collection->setCreatedAt($date);
    }

    private function createCancelledSeizures(DebtClientSeizure $debtClientSeizure, LicensedPartner $licensedPartner): void
    {
        $date = new DateTimeImmutable('today midnight');

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '9',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            1
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '10',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            2
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '11',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            3
        );
        $seizure->setCreatedAt($date);

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '12',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_CANCELLED,
            4
        );
        $seizure->setCreatedAt($date);
    }

    private function createOldCollections(DebtClientCollection $debtClientCollection, LicensedPartner $licensedPartner): void
    {
        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '13',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            1
        );
        $collection->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '14',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            2
        );
        $collection->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $collection = $this->fixturesHelper->createDebtCollectionGrs(
            '15',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            3
        );
        $collection->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $collection = $this->fixturesHelper->createDebtCollectionGeb(
            '16',
            $debtClientCollection,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            4
        );
        $collection->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));
    }

    private function createOldSeizures(DebtClientSeizure $debtClientSeizure, LicensedPartner $licensedPartner): void
    {
        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '13',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            1
        );
        $seizure->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '14',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            2
        );
        $seizure->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $seizure = $this->fixturesHelper->createDebtSeizureGrs(
            '15',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            3
        );
        $seizure->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));

        $seizure = $this->fixturesHelper->createDebtSeizureGeb(
            '16',
            $debtClientSeizure,
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            4
        );
        $seizure->setCreatedAt((new DateTimeImmutable())->modify('-32 days'));
    }
}
