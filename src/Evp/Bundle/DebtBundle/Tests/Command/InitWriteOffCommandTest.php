<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Command;

use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Exception\AccountManagerException;
use Evp\Bundle\BankAccountBundle\Service\AccountManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\ClientBundle\Exception\LicensedPartnerNotFoundException;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Exception\ProcessorErrorException;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\CollectionProcessorManager;
use Evp\Bundle\DebtBundle\Service\SeizureProcessorManager;
use Evp\Component\Money\Money;
use Exception;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Clock\ClockInterface;
use Symfony\Component\Console\Tester\CommandTester;

class InitWriteOffCommandTest extends CommandTestCase
{
    private const START_WRITE_OFF_TIME = '10:00:00';
    private const END_WRITE_OFF_TIME = '16:25:00';

    private CommandTester $commandTester;
    private MockObject $clock;
    private EntityManager $entityManager;
    private AccountManager $accountManager;
    private CollectionProcessorManager $collectionProcessorManager;
    private SeizureProcessorManager $seizureProcessorManager;
    private CollectionRepository $collectionRepository;
    private FixturesHelper $fixturesHelper;

    public function setUp(): void
    {
        $this->clock = $this->createMock(ClockInterface::class);
        $this->getContainer()->set('evp.component.time.clock.tbilisi', $this->clock);

        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_debt.command.write_off.init'
        );

        /** @noinspection MissingService */
        /** @noinspection PhpFieldAssignmentTypeMismatchInspection */
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->accountManager = $this->getContainer()->get('evp_bank_account.account_manager');
        $this->collectionProcessorManager = $this->getContainer()->get('evp_debt.service.collection_processor_manager');
        $this->seizureProcessorManager = $this->getContainer()->get('evp_debt.service.seizure_processor_manager');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
    }

    /**
     * @throws Exception
     */
    public function testSuccess(): void
    {
        $collectionCount = 5;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $firstClient = $this->prepareClient($licensedPartner);
        $secondClient = $this->prepareClient($licensedPartner, 2);
        $this->entityManager->flush();

        $this->createCollectionsGrs($firstClient, $collectionCount);
        $this->createCollectionsGrs($secondClient, $collectionCount);
        $this->entityManager->flush();

        $collections = $this->collectionRepository->findBy(['client' => $firstClient]);
        $collections[0]->setStatus(Collection::STATUS_CANCELLED);
        $collections[1]->setWriteOffStatus(Collection::WRITE_OFF_STATUS_DONE);

        $collections = $this->collectionRepository->findBy(['client' => $secondClient]);
        $collections[0]->setStatus(Collection::STATUS_RETURNED);
        $collections[1]->setWriteOffStatus(Collection::WRITE_OFF_STATUS_DONE);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(6, $waitingWriteOffs);

        $firstClientWaitingWriteOffs = $this->collectionRepository->countActiveByClientAndWriteOffStatuses(
            $firstClient,
            [Collection::WRITE_OFF_STATUS_WAITING]
        );
        $this->assertEquals(3, $firstClientWaitingWriteOffs);

        $secondClientWaitingWriteOffs = $this->collectionRepository->countActiveByClientAndWriteOffStatuses(
            $secondClient,
            [Collection::WRITE_OFF_STATUS_WAITING]
        );
        $this->assertEquals(3, $secondClientWaitingWriteOffs);
    }

    /**
     * @throws Exception
     */
    public function testClientHasWaitingWriteOffs(): void
    {
        $collectionCount = 2;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner);
        $this->entityManager->flush();

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $collection = $this->collectionRepository->findOneBy([]);
        $collection->setWriteOffStatus(Collection::WRITE_OFF_STATUS_WAITING);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(1, $waitingWriteOffs);
    }

    /**
     * @throws Exception
     */
    public function testClientHasStartedWriteOff(): void
    {
        $collectionCount = 2;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner);
        $this->entityManager->flush();

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $collection = $this->collectionRepository->findOneBy([]);
        $collection->setWriteOffStatus(Collection::WRITE_OFF_STATUS_STARTED);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(0, $waitingWriteOffs);
    }

    /**
     * @throws Exception
     */
    public function testClientHasFailedWriteOff(): void
    {
        $collectionCount = 2;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner);
        $this->entityManager->flush();

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $collection = $this->collectionRepository->findOneBy([]);
        $collection->setWriteOffStatus(Collection::WRITE_OFF_STATUS_FAILED);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(0, $waitingWriteOffs);
    }

    /**
     * @throws Exception
     */
    public function testClientHasNoMoney(): void
    {
        $collectionCount = 5;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner, 1, false);
        $this->entityManager->flush();

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(0, $waitingWriteOffs);
    }

    /**
     * @throws Exception
     */
    public function testPartOfCollectionsCoveredByMoneyFromSeizure(): void
    {
        $collectionCount = 5;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn(new DateTimeImmutable(self::START_WRITE_OFF_TIME))
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner);
        $this->entityManager->flush();

        $this->seizureProcessorManager->create(
            $this->fixturesHelper->createCommonSeizureGrs($client, new Money(1000, 'GEL'))
        );

        $this->collectionProcessorManager->create(
            $this->fixturesHelper->createCommonCollectionGeb($client, new Money(50, 'GEL'))
        );

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals(5, $waitingWriteOffs);
    }

    /** @dataProvider outOfWriteOffTimeDataProvider */
    public function testOutOfWriteOffTime(int $expectedWaitingWriteOffs, DateTimeImmutable $time): void
    {
        $collectionCount = 5;

        $this->clock
            ->expects($this->any())
            ->method('now')
            ->willReturn($time)
        ;

        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $client = $this->prepareClient($licensedPartner);
        $this->entityManager->flush();

        $this->createCollectionsGrs($client, $collectionCount);
        $this->entityManager->flush();

        $this->runCommand();

        $waitingWriteOffs = $this->collectionRepository->count(['writeOffStatus' => Collection::WRITE_OFF_STATUS_WAITING]);
        $this->assertEquals($expectedWaitingWriteOffs, $waitingWriteOffs);
    }

    public function outOfWriteOffTimeDataProvider(): array
    {
        return [
            'Before write off time' => [
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::START_WRITE_OFF_TIME))->modify('-1 minute'),
            ],
            'At the end of write off time' => [
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::END_WRITE_OFF_TIME)),
            ],
            'After write off time' => [
                'expectedWaitingWriteOffs' => 0,
                'time' => (new DateTimeImmutable(self::END_WRITE_OFF_TIME))->modify('+1 minute'),
            ],
            'At the start of write off time' => [
                'expectedWaitingWriteOffs' => 5,
                'time' => new DateTimeImmutable(self::START_WRITE_OFF_TIME),
            ],
            'In the middle of write off time' => [
                'expectedWaitingWriteOffs' => 5,
                'time' => (new DateTimeImmutable(self::START_WRITE_OFF_TIME))->modify('+1 hour'),
            ],
        ];
    }

    /**
     * @throws AccountManagerException
     */
    private function prepareClient(LicensedPartner $licensedPartner, int $number = 1, bool $hasMoney = true): Client
    {
        $client = $this->fixturesHelper->createClientNatural($number);
        $account = $this->fixturesHelper->createAccount($client, (string) $number, (string) $number);

        if ($hasMoney) {
            $moneyOnAccount = new Money(1000, 'GEL');
            $this->accountManager->fill($moneyOnAccount, $account);
        }

        $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());
        $this->fixturesHelper->createDebtClientCollection($client);

        return $client;
    }

    /**
     * @throws ProcessorErrorException
     * @throws LicensedPartnerNotFoundException
     */
    private function createCollectionsGrs(Client $client, int $count): void
    {
        $amount = new Money(100, 'GEL');

        for ($i = 1; $i <= $count; $i++) {
            $number = sprintf('%d_%d', $client->getId(), $i);

            $this->collectionProcessorManager->create(
                $this->fixturesHelper->createCommonCollectionGrs($client, $amount, $number)
                    ->setPriority($count - $i)
            );
        }
    }

    private function runCommand(): void
    {
        $this->commandTester->execute(array('command' => $this->command->getName()));
    }
}
