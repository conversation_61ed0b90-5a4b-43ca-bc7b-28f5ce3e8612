<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests;

use Application\Sonata\UserBundle\Entity\User;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionChangeLog;
use Evp\Bundle\DebtBundle\Entity\CollectionComment;
use Evp\Bundle\DebtBundle\Entity\CollectionGeb;
use Evp\Bundle\DebtBundle\Entity\CollectionManual;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Entity\DebtClientCollection;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Entity\SeizureComment;
use Evp\Bundle\DebtBundle\Entity\SeizureGeb;
use Evp\Component\Money\Money;

class FixturesHelper
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function createSeizureGeb(array $data = []): SeizureGeb
    {
        $client = (new ClientNatural());
        $debtClientSeizure = (new DebtClientSeizure())->setClient($client);

        $comment1 = (new SeizureComment())
            ->setComment('First comment');
        $comment2 = (new SeizureComment())
            ->setComment('Second comment');

        /** @var User $user */
        $user = array_key_exists('user', $data) ? $data['user'] : null;
        $status = array_key_exists('status', $data) ? $data['status'] : null;

        if($user instanceof User) {
            $comment1->setUserId($user->getId());
            $comment2->setUserId($user->getId());
        }

        if($status !== null) {
            $comment1->setStatus($status);
            $comment2->setStatus($status);
        }

        $licensedPartner = $this->createPartner();

        $seizure = (new SeizureGeb())
            ->setLicensedPartner($licensedPartner)
            ->setTriggerType($data['triggerType'] ?? Seizure::TRIGGER_TYPE_AUTOMATIC)
            ->setOrganizationPriority(Seizure::PRIORITY_PRIVATE_EXECUTORS)
            ->setStatus(Seizure::STATUS_ACTIVE)
            ->setNumber('documentNumber123')
            ->setOrderingDate(new \DateTimeImmutable())
            ->setAmountMoney(new Money('500', 'EUR'))
            ->setDetails('Some details about this seizure order')
            ->setClient($client)
            ->setDebtClient($debtClientSeizure)
            ->addComment($comment1)
            ->addComment($comment2);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->persist($debtClientSeizure);
        $this->entityManager->persist($comment1);
        $this->entityManager->persist($comment2);
        $this->entityManager->persist($seizure);

        return $seizure;
    }

    public function createCollection(array $data = []): Collection
    {
        $client = (new ClientNatural());
        $debtClientCollection = (new DebtClientCollection())->setClient($client);

        $licensedPartner = $this->createPartner();

        $comment1 = (new CollectionComment())
            ->setComment('First comment');
        $comment2 = (new CollectionComment())
            ->setComment('Second comment');

        /** @var User $user */
        $user = array_key_exists('user', $data) ? $data['user'] : null;
        $status = array_key_exists('status', $data) ? $data['status'] : null;

        if($user instanceof User) {
            $comment1->setUserId($user->getId());
            $comment2->setUserId($user->getId());
        }

        if($status !== null) {
            $comment1->setStatus($status);
            $comment2->setStatus($status);
        }

        $collection = array_key_exists('type', $data) && $data['type'] == 'manual'
            ? new CollectionManual()
            : new CollectionGeb();

        $collection
            ->setLicensedPartner($licensedPartner)
            ->setTriggerType($data['triggerType'] ?? Collection::TRIGGER_TYPE_AUTOMATIC)
            ->setOrganizationPriority(Seizure::PRIORITY_PRIVATE_EXECUTORS)
            ->setStatus(Seizure::STATUS_ACTIVE)
            ->setNumber('documentNumber123')
            ->setOrderingDate(new \DateTimeImmutable())
            ->setAmountMoney(new Money('500', 'EUR'))
            ->setAmountLeftMoney(new Money('150', 'EUR'))
            ->setDetails('Some details about this collection order')
            ->setClient($client)
            ->setDebtClient($debtClientCollection)
            ->setBeneficiaryAccountNumber('********************')
            ->setBeneficiaryBankName('BeneficiaryBankName')
            ->setBeneficiaryIdentificationNumber('BeneficiaryIdentificationNumber')
            ->setBeneficiaryName('BeneficiaryName')
            ->setBeneficiaryBankSwiftCode('EVIULT21XXX')
            ->addComment($comment1)
            ->addComment($comment2);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->persist($debtClientCollection);
        $this->entityManager->persist($comment1);
        $this->entityManager->persist($comment2);
        $this->entityManager->persist($collection);

        return $collection;
    }

    public function createCommission(
        Collection $collection,
        Money $commissionAmount = null,
        string $status = Commission::STATUS_ACTIVE
    ): Commission {
        if (null === $commissionAmount) {
            $commissionAmount = new Money(10, 'EUR');
        }
        $commission = (new Commission())
            ->setClient($collection->getClient())
            ->setCollection($collection)
            ->setStatus($status)
            ->setAmountMoney($commissionAmount)
        ;

        $this->entityManager->persist($commission);

        return $commission;
    }

    public function createCollectionChangeLog(
        Collection $collection,
        DateTime $date,
        int $userId,
        string $field = CollectionChangeLog::FIELD_WRITE_OFF_STATUS,
        string $newValue = Collection::WRITE_OFF_STATUS_STARTED
    ): CollectionChangeLog {
        return (new CollectionChangeLog())
            ->setCollection($collection)
            ->setDate($date)
            ->setUserId($userId)
            ->setField($field)
            ->setNewValue($newValue)
        ;
    }

    private function createPartner(): LicensedPartner
    {
        return (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
    }
}
