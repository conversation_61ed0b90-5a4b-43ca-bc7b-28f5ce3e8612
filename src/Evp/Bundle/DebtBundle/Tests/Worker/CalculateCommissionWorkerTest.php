<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Worker;

use DateInterval;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankCommissionBundle\Exception\CommissionCalculatorException;
use Evp\Bundle\BankTransferBundle\Entity\TransferIn;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\CurrencyBundle\Exception\CurrencyNotFoundException;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\CollectionGrs;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Exception\UnsupportedBeneficiaryTypeException;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Service\Commission\CommissionProvider;
use Evp\Bundle\DebtBundle\Worker\CalculateCommissionWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Exception\InvalidDataException;
use Evp\Component\Money\Money;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Traversable;

class CalculateCommissionWorkerTest extends TestCase
{
    private const DATA_COLLECTION_ID = 'collectionId';
    private const CURRENCY_GEL = 'GEL';

    private MockObject $entityManagerMock;
    private MockObject $collectionRepositoryMock;
    private MockObject $commissionProviderMock;
    private MockObject $loggerMock;
    private CalculateCommissionWorker $worker;

    protected function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManager::class);
        $this->collectionRepositoryMock = $this->createMock(CollectionRepository::class);
        $this->commissionProviderMock = $this->createMock(CommissionProvider::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->worker = new CalculateCommissionWorker(
            $this->entityManagerMock,
            $this->collectionRepositoryMock,
            $this->commissionProviderMock,
            $this->loggerMock,
        );
    }

    /**
     * @dataProvider dataProviderWork
     */
    public function testWork(
        int $collectionId,
        array $data = [self::DATA_COLLECTION_ID => 123],
        bool $isExpectExceptionCollectionIdIsNotProvided = false,
        int $exactlyCollectionRepositoryFindById = 1,
        bool $isWillReturnObjCollectionRepositoryFindById = true,
        bool $isExpectExceptionCollectionNotFound = false,
        bool $collectionWithTransfers = true,
        bool $collectionWithCommissions = true,
        bool $isTransferTimestampBiggerThanCommission = true,
        int $exactlyCommissionProviderCalculateCommission = 1,
        ?string $commissionProviderCalculateCommissionException = null,
        int $exactlyPersist = 1,
        int $exactlyFlush = 1
    ): void {
        $client = new ClientNatural();
        $collection = new CollectionGrs();
        $collection->setClient($client);

        $transfer = new TransferIn();
        $commission = new Commission();
        if ($isTransferTimestampBiggerThanCommission) {
            $transfer->setCreatedAt((new DateTime())->add(new DateInterval('PT5S')));
        } else {
            $commission->setCreatedAt((new DateTimeImmutable())->add(new DateInterval('PT5S')));
        }

        if ($collectionWithTransfers) {
            $collection->addTransfer($transfer);
        }
        if ($collectionWithCommissions) {
            $collection->addCommission($commission);
        }

        $commissionAmount = new Money(10, self::CURRENCY_GEL);

        if ($isExpectExceptionCollectionIdIsNotProvided) {
            $this->loggerMock
                ->expects($this->once())
                ->method('error')
                ->with('DebtBundle exceptional case: CollectionId is not provided for CalculateCommissionWorker.')
            ;

            $this->expectException(InvalidDataException::class);
            $this->expectExceptionMessage('CollectionId is not provided');
        }

        $this->collectionRepositoryMock
            ->expects($this->exactly($exactlyCollectionRepositoryFindById))
            ->method('findById')
            ->with($collectionId)
            ->willReturn($isWillReturnObjCollectionRepositoryFindById ? $collection : null)
        ;

        if ($isExpectExceptionCollectionNotFound) {
            $this->expectException(InvalidDataException::class);
            $this->expectExceptionMessage('DebtBundle exceptional case: Collection not found for CalculateCommissionWorker.');
        }

        try {
            $this->commissionProviderMock
                ->expects($this->exactly($exactlyCommissionProviderCalculateCommission))
                ->method('calculateCommission')
                ->with($collection)
                ->will(
                    $commissionProviderCalculateCommissionException
                        ? $this->throwException(new $commissionProviderCalculateCommissionException())
                        : $this->returnValue($commissionAmount)
                )
            ;
        } catch (CommissionCalculatorException | CurrencyNotFoundException | UnsupportedBeneficiaryTypeException $exception) {
            $this->loggerMock
                ->expects($this->once())
                ->method('error')
                ->with(
                    'DebtBundle exceptional case: exception during calculateCommission',
                    [$exception->getMessage(), $collection, $exception]
                )
            ;
        }

        $this->entityManagerMock
            ->expects($this->exactly($exactlyPersist))
            ->method('persist')
            ->with(
                $this->callback(function(Commission $object) use ($collection, $commissionAmount, $client) {
                    $this->assertSame($client, $object->getClient());
                    $this->assertSame($collection, $object->getCollection());
                    $this->assertEquals(Collection::STATUS_ACTIVE, $object->getStatus());
                    $this->assertEquals($commissionAmount, $object->getAmountMoney());

                    return true;
                })
            )
        ;

        $this->entityManagerMock
            ->expects($this->exactly($exactlyFlush))
            ->method('flush')
        ;

        $this->worker->work($data);
    }

    public function dataProviderWork(): Traversable
    {
        yield 'Positive case' => $this->getWorkDefaults();

        yield 'CollectionId is not provided' => array_merge($this->getWorkDefaults(), [
            'data' => [],
            'isExpectExceptionCollectionIdIsNotProvided' => true,
            'exactlyCollectionRepositoryFindById' => 0,
            'exactlyCommissionProviderCalculateCommission' => 0,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);

        yield 'Collection not found' => array_merge($this->getWorkDefaults(), [
            'isWillReturnObjCollectionRepositoryFindById' => false,
            'exactlyCommissionProviderCalculateCommission' => 0,
            'isExpectExceptionCollectionNotFound' => true,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);

        yield 'Collection without transfers' => array_merge($this->getWorkDefaults(), [
            'collectionWithTransfers' => false,
            'exactlyCommissionProviderCalculateCommission' => 0,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);

        yield 'Collection without commission' => array_merge($this->getWorkDefaults(), [
            'collectionWithCommissions' => false,
        ]);

        yield 'CommissionCalculatorException' => array_merge($this->getWorkDefaults(), [
            'commissionProviderCalculateCommissionException' => CommissionCalculatorException::class,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);

        yield 'CurrencyNotFoundException' => array_merge($this->getWorkDefaults(), [
            'commissionProviderCalculateCommissionException' => CurrencyNotFoundException::class,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);

        yield 'UnsupportedBeneficiaryTypeException' => array_merge($this->getWorkDefaults(), [
            'commissionProviderCalculateCommissionException' => UnsupportedBeneficiaryTypeException::class,
            'exactlyPersist' => 0,
            'exactlyFlush' => 0,
        ]);
    }

    private function getWorkDefaults(): array
    {
        return [
            'collectionId' => 123,
            'data' => [self::DATA_COLLECTION_ID => 123],
            'isExpectExceptionCollectionIdIsNotProvided' => false,
            'exactlyCollectionRepositoryFindById' => 1,
            'isWillReturnObjCollectionRepositoryFindById' => true,
            'isExpectExceptionCollectionNotFound' => false,
            'collectionWithTransfers' => true,
            'collectionWithCommissions' => true,
            'isTransferTimestampBiggerThanCommission' => true,
            'exactlyCommissionProviderCalculateCommission' => 1,
            'commissionProviderCalculateCommissionException' => null,
            'exactlyPersist' => 1,
            'exactlyFlush' => 1,
        ];
    }
}
