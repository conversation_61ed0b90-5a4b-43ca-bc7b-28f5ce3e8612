<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Worker;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\DebtBundle\Service\WriteOffInitializer;
use Evp\Bundle\DebtBundle\Worker\InitClientWriteOffWorker;
use Evp\Bundle\RabbitMqExtensionBundle\Exception\InvalidDataException;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Traversable;

class InitClientWriteOffWorkerTest extends TestCase
{
    private const DATA_CLIENT_ID = 'clientId';

    private MockObject $writeOffInitializerMock;
    private MockObject $loggerMock;
    private MockObject $entityManagerMock;
    private MockObject $clientRepositoryMock;
    private InitClientWriteOffWorker $worker;

    protected function setUp(): void
    {
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->writeOffInitializerMock = $this->createMock(WriteOffInitializer::class);
        $this->entityManagerMock = $this->createMock(EntityManager::class);
        $this->clientRepositoryMock = $this->createMock(ClientRepository::class);

        $this->worker = new InitClientWriteOffWorker(
            $this->loggerMock,
            $this->entityManagerMock,
            $this->writeOffInitializerMock,
            $this->clientRepositoryMock,
        );
    }

    /**
     * @dataProvider dataProviderWork
     */
    public function testWork(
        int $clientId,
        array $data,
        bool $isExpectExceptionClientIdIsNotProvided,
        int $exactlyClientRepositoryFind,
        bool $willReturnObjClientRepositoryFind,
        int $exactlyEntityManagerBeginTransaction,
        int $exactlyWriteOffInitializerInitWriteOff,
        int $exactlyEntityManagerBeginFlush,
        int $exactlyEntityManagerBeginCommit,
        bool $willThrowExceptionWriteOffInitializerInitWriteOff
    ): void {
        $client = new ClientNatural();
        $client->setId($clientId);

        $this->loggerMock
            ->expects($this->once())
            ->method('info')
            ->with('DebtBundle: init client write off job consumed.', [$data])
        ;

        if ($isExpectExceptionClientIdIsNotProvided) {
            $this->loggerMock
                ->expects($this->once())
                ->method('warning')
                ->with('DebtBundle exceptional case: ClientId is not provided for InitClientWriteOffWorker.')
            ;

            $this->expectException(InvalidDataException::class);
            $this->expectExceptionMessage('ClientId is not provided');
        }

        $this->clientRepositoryMock
            ->expects($this->exactly($exactlyClientRepositoryFind))
            ->method('find')
            ->with($clientId)
            ->willReturn($willReturnObjClientRepositoryFind ? $client : null)
        ;

        if (!$willReturnObjClientRepositoryFind) {
            $this->expectException(InvalidDataException::class);
            $this->expectExceptionMessage('DebtBundle exceptional case: Client not found InitClientWriteOffWorker.');
        }

        $this->entityManagerMock
            ->expects($this->exactly($exactlyEntityManagerBeginTransaction))
            ->method('beginTransaction')
        ;

        if ($willThrowExceptionWriteOffInitializerInitWriteOff) {
            $this->expectException(Exception::class);
        }
        try {
            $this->writeOffInitializerMock
                ->expects($this->exactly($exactlyWriteOffInitializerInitWriteOff))
                ->method('initWriteOff')
                ->with($client)
                ->will(
                    $willThrowExceptionWriteOffInitializerInitWriteOff
                        ? $this->throwException(new Exception())
                        : $this->returnCallback(function () {})
                )
            ;

            $this->entityManagerMock
                ->expects($this->exactly($exactlyEntityManagerBeginFlush))
                ->method('flush')
            ;

            $this->entityManagerMock
                ->expects($this->exactly($exactlyEntityManagerBeginCommit))
                ->method('commit')
            ;
        } catch (Exception $exception) {
            $this->entityManagerMock
                ->expects($this->once())
                ->method('rollback')
            ;
        }

        $this->worker->work($data);
    }

    public function dataProviderWork(): Traversable
    {
        yield 'Positive case' => $this->getWorkDefaults();

        yield 'ClientId is not provided' => array_merge($this->getWorkDefaults(), [
            'data' => [],
            'isExpectExceptionClientIdIsNotProvided' => true,
            'exactlyClientRepositoryFind' => 0,
            'exactlyEntityManagerBeginTransaction' => 0,
            'exactlyWriteOffInitializerInitWriteOff' => 0,
            'exactlyEntityManagerBeginFlush' => 0,
            'exactlyEntityManagerBeginCommit' => 0,
        ]);

        yield 'Client not found' => array_merge($this->getWorkDefaults(), [
            'willReturnObjClientRepositoryFind' => false,
            'exactlyEntityManagerBeginTransaction' => 0,
            'exactlyWriteOffInitializerInitWriteOff' => 0,
            'exactlyEntityManagerBeginFlush' => 0,
            'exactlyEntityManagerBeginCommit' => 0,
        ]);

        yield 'Exception' => array_merge($this->getWorkDefaults(), [
            'willThrowExceptionWriteOffInitializerInitWriteOff' => true,
            'exactlyEntityManagerBeginFlush' => 0,
            'exactlyEntityManagerBeginCommit' => 0,
        ]);
    }


    private function getWorkDefaults(): array
    {
        return [
            'clientId' => 123,
            'data' => [self::DATA_CLIENT_ID => 123],
            'isExpectExceptionClientIdIsNotProvided' => false,
            'exactlyClientRepositoryFind' => 1,
            'willReturnObjClientRepositoryFind' => true,
            'exactlyEntityManagerBeginTransaction' => 1,
            'exactlyWriteOffInitializerInitWriteOff' => 1,
            'exactlyEntityManagerBeginFlush' => 1,
            'exactlyEntityManagerBeginCommit' => 1,
            'willThrowExceptionWriteOffInitializerInitWriteOff' => false,
        ];
    }
}
