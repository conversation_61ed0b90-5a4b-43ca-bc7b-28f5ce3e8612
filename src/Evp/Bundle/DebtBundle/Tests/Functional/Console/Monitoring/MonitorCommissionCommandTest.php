<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Functional\Console\Monitoring;

use DateTimeImmutable;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\DebtBundle\Command\Monitoring\MonitorCommissionCommand;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Entity\DebtClientSeizure;
use Evp\Bundle\DebtBundle\Entity\Filter\CommissionFilter;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\CommissionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientRepository;
use InfluxDB\Database as InfluxDatabase;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;
use Traversable;

class MonitorCommissionCommandTest extends CommandTestCase
{
    private const COMMAND_NAME = 'paysera:debt:monitor:commission';

    private const PROBLEM_COLLECTIONS_WITHOUT_COMMISSION = 'collections_without_commission';
    private const PROBLEM_NOT_PROCESSED_COMMISSIONS = 'not_processed_commissions';
    private const PROBLEM_CREATED_COMMISSIONS = 'created_commissions';
    private const PROBLEM_CREATED_COMMISSION_CHARGES = 'created_commission_charges';

    private CommandTester $commandTester;
    private MockObject $collectionRepositoryMock;
    private MockObject $commissionRepositoryMock;
    private MockObject $debtClientRepositoryMock;
    private MockObject $monitoringClientMock;
    private string $monitoringKey;

    protected function setUp(): void
    {
        $this->collectionRepositoryMock = $this->createMock(CollectionRepository::class);
        $this->commissionRepositoryMock = $this->createMock(CommissionRepository::class);
        $this->debtClientRepositoryMock = $this->createMock(DebtClientRepository::class);
        $this->monitoringClientMock = $this->createMock(MonitoringClient::class);
        $this->monitoringKey = $this->getContainer()->getParameter('evp_debt.monitor_key');

        $command = new MonitorCommissionCommand(
            $this->collectionRepositoryMock,
            $this->commissionRepositoryMock,
            $this->debtClientRepositoryMock,
            $this->monitoringClientMock,
            $this->monitoringKey
        );
        $command->setName(self::COMMAND_NAME);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseCommon($command);
    }

    /**
     * @dataProvider dataProviderProcess
     */
    public function testProcess(?string $dateIn, string $dateInside, ?int $timestampInNanoseconds): void
    {
        $countCollectionsWithoutCommissionWillReturn = 11;
        $countAllByFilterWillReturn1 = 33;
        $countAllByFilterWillReturn2 = 44;

        $processNotProcessedCommissionsCount = 1;

        $client = new ClientNatural();
        $commissions = [
            (new Commission())->setClient($client),
        ];

        $hold = new Hold();
        $hold->setStatus(Hold::STATUS_HOLD);
        $debtClient = (new DebtClientSeizure())->addHold($hold);
        $debtClients = [$debtClient];

        $this->collectionRepositoryMock
            ->expects($this->once())
            ->method('countCollectionsWithoutCommission')
            ->willReturn($countCollectionsWithoutCommissionWillReturn)
        ;

        $this->commissionRepositoryMock
            ->expects($this->once())
            ->method('findNotProcessedCommissions')
            ->with(
                Commission::STATUS_DONE,
                $this->callback(function (DateTimeImmutable $date) {
                    $yesterday = (new DateTimeImmutable())->modify('-1 day');

                    return $date->format('Y-m-d') === $yesterday->format('Y-m-d');
                })
            )
            ->willReturn($commissions)
        ;

        $this->debtClientRepositoryMock
            ->expects($this->once())
            ->method('getAllByClient')
            ->with($client)
            ->willReturn($debtClients)
        ;

        $this->commissionRepositoryMock
            ->expects($this->exactly(2))
            ->method('countAllByFilter')
            ->withConsecutive(
                [
                    $this->callback(function (CommissionFilter $value) use ($dateInside) {
                        $this->assertEquals('0', $value->getAmountsGreaterThan());
                        $this->assertEquals($dateInside . ' 00:00:00', $value->getCreatedFrom()->format('Y-m-d H:i:s'));
                        $this->assertEquals($dateInside . ' 23:59:59', $value->getCreatedTo()->format('Y-m-d H:i:s'));

                        return true;
                    }),
                ],
                [
                    $this->callback(function (CommissionFilter $value) use ($dateInside) {
                        $this->assertEquals('0', $value->getAmountsGreaterThan());
                        $this->assertEquals($dateInside . ' 00:00:00', $value->getUpdatedFrom()->format('Y-m-d H:i:s'));
                        $this->assertEquals($dateInside . ' 23:59:59', $value->getUpdatedTo()->format('Y-m-d H:i:s'));
                        $this->assertTrue($value->getWithCharges());

                        return true;
                    }),
                ],
            )
            ->willReturnOnConsecutiveCalls($countAllByFilterWillReturn1, $countAllByFilterWillReturn2)
        ;

        $this->monitoringClientMock
            ->expects($this->exactly(5))
            ->method('writeValue')
            ->withConsecutive(
                [
                    $this->monitoringKey,
                    $countCollectionsWithoutCommissionWillReturn,
                    ['problem' => self::PROBLEM_COLLECTIONS_WITHOUT_COMMISSION],
                    [],
                    InfluxDatabase::PRECISION_NANOSECONDS,
                    null,
                ],
                [
                    $this->monitoringKey,
                    $countCollectionsWithoutCommissionWillReturn,
                    ['problem' => self::PROBLEM_COLLECTIONS_WITHOUT_COMMISSION],
                    [],
                    InfluxDatabase::PRECISION_NANOSECONDS,
                    null,
                ],
                [
                    $this->monitoringKey,
                    $processNotProcessedCommissionsCount,
                    ['problem' => self::PROBLEM_NOT_PROCESSED_COMMISSIONS],
                    [],
                    InfluxDatabase::PRECISION_NANOSECONDS,
                    null,
                ],
                [
                    $this->monitoringKey,
                    $countAllByFilterWillReturn1,
                    ['problem' => self::PROBLEM_CREATED_COMMISSIONS],
                    [],
                    InfluxDatabase::PRECISION_NANOSECONDS,
                    $timestampInNanoseconds,
                ],
                [
                    $this->monitoringKey,
                    $countAllByFilterWillReturn2,
                    ['problem' => self::PROBLEM_CREATED_COMMISSION_CHARGES],
                    [],
                    InfluxDatabase::PRECISION_NANOSECONDS,
                    $timestampInNanoseconds,
                ],
            )
        ;

        $options = [];
        if (null !== $dateIn) {
            $options = ['--date' => $dateIn];
        }
        $this->commandTester->execute(array_merge(['command' => self::COMMAND_NAME], $options));
        $this->assertSame(0, $this->commandTester->getStatusCode());
    }

    public function dataProviderProcess(): Traversable
    {
        $date = (new DateTimeImmutable());
        yield 'Date is nullable' => [
            'dateIn' => null,
            'dateInside' => $date->format('Y-m-d'),
            'timestampInNanoseconds' => null,
        ];

        yield 'Date is today' => [
            'dateIn' => $date->format('Y-m-d'),
            'dateInside' => $date->format('Y-m-d'),
            'timestampInNanoseconds' => null,
        ];

        $date = (new DateTimeImmutable('yesterday'));
        yield 'Date is previous time' => [
            'dateIn' => $date->format('Y-m-d'),
            'dateInside' => $date->format('Y-m-d'),
            'timestampInNanoseconds' => (int)(($date->getTimestamp() + $date->getOffset()) * 1e9),
        ];
    }
}
