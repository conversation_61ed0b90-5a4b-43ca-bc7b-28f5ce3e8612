<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Functional\Console\Monitoring;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Command\Monitoring\MonitorClientPartnersCommand;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\SeizureRepository;
use InfluxDB\Database as InfluxDatabase;
use InfluxDB\Point;
use Paysera\Bundle\MonitoringBundle\Service\MonitoringClient;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Tester\CommandTester;

class MonitorClientPartnersCommandTest extends CommandTestCase
{
    private const COMMAND_NAME = 'paysera:debt:monitor:client_partners';

    private const ENVIRONMENT = 'environment';
    private const PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE = 'active_collection_client_partner_code';
    private const PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE = 'active_seizure_client_partner_code';
    private const PAYSERA_ANOTHER = 'another';

    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private CollectionRepository $collectionRepository;
    private SeizureRepository $seizureRepository;
    private MockObject $influxDatabaseMock;
    private MockObject $loggerMock;
    private string $monitoringKey;

    protected function setUp(): void
    {
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->seizureRepository = $this->getContainer()->get('evp_debt.repository.seizure');
        $this->monitoringKey = $this->getContainer()->getParameter('evp_debt.monitor_key');

        $this->influxDatabaseMock = $this->getMockBuilder(InfluxDatabase::class)->disableOriginalConstructor()->getMock();
        $this->loggerMock = $this->getMockBuilder(LoggerInterface::class)->getMock();
        $monitoringClient = new MonitoringClient(
            $this->influxDatabaseMock,
            self::ENVIRONMENT,
            $this->loggerMock,
            true,
        );

        $command = new MonitorClientPartnersCommand(
            $this->collectionRepository,
            $this->seizureRepository,
            $monitoringClient,
            $this->monitoringKey,
        );
        $command->setName(self::COMMAND_NAME);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseCommon($command);
    }

    public function testExecute(): void
    {
        $this->createData();

        $this->assertInfluxDatabaseMock();
        $this->assertLoggerMock();

        $this->commandTester->execute(['command' => self::COMMAND_NAME]);
    }

    private function createData(): void
    {
        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createPartnerClient($client, $licensedPartner->getPartnerCode());

        $collection = $this->fixturesHelper->createDebtClientCollection($client);
        $this->fixturesHelper->createDebtCollectionGrs(
            'collection_number',
            $collection,
            $licensedPartner
        );

        $seizure = $this->fixturesHelper->createDebtClientSeizure($client);
        $this->fixturesHelper->createDebtSeizureGrs(
            'seizure_number',
            $seizure,
            $licensedPartner
        );
        $this->entityManager->flush();
    }

    private function assertInfluxDatabaseMock(): void
    {
        $this->influxDatabaseMock->expects($this->exactly(10))
            ->method('writePoints')
            ->with($this->logicalOr(
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                        ]
                        && $point->getFields() === ['value' => 1.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_LITHUANIA,
                        ]
                        && $point->getFields() === ['value' => 1.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_ALBANIA,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_KOSOVO,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_COLLECTIONS_CLIENT_PARTNER_CODE,
                            'partnerCode' => self::PAYSERA_ANOTHER,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_GEORGIA,
                        ]
                        && $point->getFields() === ['value' => 1.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_LITHUANIA,
                        ]
                        && $point->getFields() === ['value' => 1.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_ALBANIA,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE,
                            'partnerCode' => LicensedPartner::PAYSERA_KOSOVO,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
                $this->callback(function (array $points){
                    /** @var Point $point */
                    $point = $points[0];
                    if (null === $point) {
                        return true;
                    }

                    return $point->getTags() === [
                            'env' => self::ENVIRONMENT,
                            'problem' => self::PROBLEM_ACTIVE_SEIZURES_CLIENT_PARTNER_CODE,
                            'partnerCode' => self::PAYSERA_ANOTHER,
                        ]
                        && $point->getFields() === ['value' => 0.0]
                    ;
                }),
            ))
        ;
    }

    private function assertLoggerMock(): void
    {
        $this->loggerMock->expects($this->exactly(20))
            ->method('debug')
            ->with(
                $this->logicalOr('Monitoring START', 'Monitoring END'),
                ['key' => $this->monitoringKey],
            )
        ;
    }
}
