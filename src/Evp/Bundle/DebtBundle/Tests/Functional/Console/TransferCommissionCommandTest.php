<?php

namespace Evp\Bundle\DebtBundle\Tests\Functional\Console;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Repository\CommissionRepository;
use Evp\Bundle\DebtBundle\Tests\FixturesHelper as DebtFixturesHelper;
use Evp\Component\Money\Money;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class TransferCommissionCommandTest extends CommandTestCase
{
    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private DebtFixturesHelper $debtFixturesHelper;
    private CommissionRepository $commissionRepository;

    public function setUp(): void
    {
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->debtFixturesHelper = new DebtFixturesHelper($this->entityManager);
        $this->commandTester = $this->createCommandTesterAndNewDatabaseByServiceId(
            'evp_debt.command.transfer_commission_command'
        );
        $this->commissionRepository = $this->getContainer()->get('evp_debt.repository.commission');
    }

    public function testExecute(): void
    {
        $commission = $this->createCommission();

        $this->commandTester->execute(['command' => $this->command->getName()]);
        $display = $this->commandTester->getDisplay();
        $this->assertStringContainsString('100%', $display);

        $commission = $this->commissionRepository->find($commission->getId());
        $this->assertEquals(Commission::STATUS_DONE, $commission->getStatus());
    }

    private function createCommission(): Commission
    {
        $collectionNumber = 'collection_number';
        $commissionAmount = '2.22';
        $commissionCurrency = 'GEL';

        $client = $this->fixturesHelper->createClientNatural(2);
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $collection = $this->fixturesHelper->createDebtCollectionManual(
            $collectionNumber,
            $this->fixturesHelper->createDebtClientCollection($client),
            $licensedPartner,
            Collection::STATUS_ACTIVE,
            null,
            Collection::PRIORITY_REVENUE_SERVICE,
        );

        $transferOutBank = $this->fixturesHelper->createTransferOutBank();
        $collection->addTransfer($transferOutBank);

        $commission = $this->debtFixturesHelper->createCommission(
            $collection,
            new Money($commissionAmount, $commissionCurrency),
        );

        $this->entityManager->flush();

        return $commission;
    }
}
