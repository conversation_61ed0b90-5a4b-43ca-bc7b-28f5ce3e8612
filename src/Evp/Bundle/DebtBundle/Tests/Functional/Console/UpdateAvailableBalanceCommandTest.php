<?php

namespace Evp\Bundle\DebtBundle\Tests\Functional\Console;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\DebtBundle\Command\UpdateAvailableBalanceCommand;
use Evp\Bundle\DebtBundle\Service\UpdateAvailableBalancePublisher;
use Evp\Bundle\RabbitMqExtensionBundle\Service\RemoteJobPublisherInterface;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\CommandTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Tester\CommandTester;

class UpdateAvailableBalanceCommandTest extends CommandTestCase
{
    private const BALANCE_INCREASED_JOB_KEY = 'georgia_enforcement_bureau.client.balance_increased';

    private ClientRepository $clientRepository;
    private CommandTester $commandTester;
    private EntityManagerInterface $entityManager;
    private FixturesHelper $fixturesHelper;
    private MockObject $publisher;
    private MockObject $logger;

    protected function setUp(): void
    {
        $this->entityManager = $this->getContainer()->get('doctrine.orm.entity_manager');
        $this->clientRepository = $this->getContainer()->get('evp_client.repository.client');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);

        $this->publisher = $this->getMockBuilder(RemoteJobPublisherInterface::class)->getMock();
        $this->logger = $this->getMockBuilder(LoggerInterface::class)->getMock();

        $command = new UpdateAvailableBalanceCommand(
            $this->clientRepository,
            new UpdateAvailableBalancePublisher($this->publisher, $this->logger),
            $this->logger
        );
        $command->setName('paysera:debt:update-available-balance');
        $this->commandTester = $this->createCommandTesterAndNewDatabaseCommon($command);
    }

    public function testExecute(): void
    {
        $client = $this->createTestClient();
        $publishJobData = [
            'client_id' => $client->getId()
        ];

        $this->publisher->expects($this->once())
            ->method('publishJob')
            ->with(self::BALANCE_INCREASED_JOB_KEY, $publishJobData)
        ;

        $this->logger->expects($this->exactly(3))
            ->method('info')
            ->with($this->logicalOr(
                'Sending requests for processing available balances started.',
                sprintf('Georgia Enforcement Bureau integration: %s job is published.', self::BALANCE_INCREASED_JOB_KEY),
                'Sending requests for processing available balances ended.',
            ))
        ;

        $this->commandTester->execute(['command' => $this->command->getName()]);
        $display = $this->commandTester->getDisplay();
        $this->assertStringContainsString('Done', $display);
    }

    public function createTestClient(): ClientNatural
    {
        $seizureNumber = 'seizure_number';

        $client = $this->fixturesHelper->createClientNatural();
        $licensedPartner = $this->fixturesHelper->createLicensedPartner();
        $this->fixturesHelper->createDebtSeizureGeb(
            $seizureNumber,
            $this->fixturesHelper->createDebtClientSeizure($client),
            $licensedPartner,
        );
        $this->entityManager->flush();

        return $client;
    }
}
