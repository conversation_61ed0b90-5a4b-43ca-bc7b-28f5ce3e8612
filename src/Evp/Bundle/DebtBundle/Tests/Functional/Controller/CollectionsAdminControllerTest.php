<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Functional\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Repository\CollectionChangeLogRepository;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Tests\FixturesHelper;
use Paysera\Component\Tests\Fixtures\FixturesHelper as GlobalFixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\Routing\Router;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class CollectionsAdminControllerTest extends PersistableWebTestCase
{
    private EntityManagerInterface $entityManager;
    private Router $router;
    private CollectionRepository $collectionRepository;
    private CollectionChangeLogRepository $collectionChangeLogRepository;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->router = $this->client->getContainer()->get('router');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->collectionRepository = $this->getContainer()->get('evp_debt.repository.collection');
        $this->collectionChangeLogRepository = $this->getContainer()->get('evp_debt.repository.debt_collection_change_log');
    }

    public function testShowComment(): void
    {
        $user = $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_LIST',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_VIEW',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_EDIT',
        ]);

        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createCollection(['type' => 'manual', 'triggerType' => 'manual', 'status' => 'registered', 'user' => $user]);
        $this->entityManager->flush();

        $crawler = $this->client->request(
            'GET',
            $this->router->generate('admin_evp_debt_collection_workflow_apply_transition', ['id' => '1', 'transition' => 'to_closed'])
        );
        $tokenNode = $crawler->filter('input[name="collection_comment[_token]"]');
        $tokenValue = $tokenNode->attr('value');

        $this->client->request(
            'POST',
            $this->router->generate('admin_evp_debt_collection_workflow_apply_transition', ['id' => '1', 'transition' => 'to_closed']),
            [
                'collection_comment' => [
                    'comment' => 'TEST',
                    '_token' => $tokenValue,
                ],
                'btn_update_and_edit' => '',
            ]
        );

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->assertEquals(
            $this->router->generate('admin_evp_debt_collection_edit', ['id' => '1']),
            $this->client->getResponse()->headers->get('Location')
        );

        $crawler = $this->client->request(
            'GET',
            $this->router->generate('admin_evp_debt_collection_show', ['id' => '1'])
        );

        $hasChecked = false;
        $crawler->filter('tr')->each(function ($trNode) use (&$hasChecked) {
            $thNode = $trNode->filter('th')->reduce(function ($node) {
                return trim($node->text()) === 'Comments';
            });

            if ($thNode->count() > 0) {
                $hasChecked = true;
                $liElements = $trNode->filter('li');
                $this->assertCount(1, $liElements);
                $liElements->each(function ($liNode) {
                    $liContent = $liNode->text();
                    $this->assertMatchesRegularExpression('/\w+ \(.+? \| [\w.]+ \| \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\)/', $liContent);
                });
            }
        });

        $this->assertTrue($hasChecked);
    }

    public function testShowList(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createCollection();
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_LIST',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_VIEW',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_EDIT',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/list');
        $responseContent = $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertStringContainsString('Paysera Georgia', $responseContent);
        $this->assertStringContainsString('automatic', $responseContent);
        $this->assertStringContainsString('Private executor', $responseContent);
        $this->assertStringContainsString('documentNumber123', $responseContent);
        $this->assertStringContainsString('500', $responseContent);
        $this->assertStringContainsString('EUR', $responseContent);
        $this->assertStringContainsString('active', $responseContent);
        $this->assertStringContainsString('Some details about this collection order', $responseContent);
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(0, $crawler->filter('.btn-default.edit_link'));
    }

    public function testShowEditButtonForManualSeizureInListView(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createCollection(['type' => 'manual', 'triggerType' => 'manual']);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_LIST',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_VIEW',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_EDIT',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/list');
        $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(1, $crawler->filter('.btn-default.edit_link'));
    }

    public function testShowSingleEntity(): void
    {
        $user = $this->logIn(['ROLE_ADMIN', 'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_VIEW']);

        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createCollection(['user' => $user, 'status' => 'registered']);
        $this->entityManager->flush();

        $this->client->request('GET', '/admin/evp/debt/collection/1/show');
        $responseContent = $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString('Paysera Georgia', $responseContent);
        $this->assertStringContainsString('automatic', $responseContent);
        $this->assertStringContainsString('Private executor', $responseContent);
        $this->assertStringContainsString('documentNumber123', $responseContent);
        $this->assertStringContainsString('500', $responseContent);
        $this->assertStringContainsString('EUR', $responseContent);
        $this->assertStringContainsString('150', $responseContent);
        $this->assertStringContainsString('active', $responseContent);
        $this->assertStringContainsString('Some details about this collection order', $responseContent);
        $this->assertStringContainsString('First comment', $responseContent);
        $this->assertStringContainsString('Second comment', $responseContent);
    }

    public function testCreateCollection(): void
    {
        $licensedPartner = (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
        $client = (new ClientLegal())
            ->setName('John Doe')
            ->setCovenanteeId(882);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->flush();

        $this->logIn(['ROLE_ADMIN', 'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_CREATE']);
        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/create?subclass=Manual%20Collection%20Order');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14',
            'receivedDate' => '2023-08-30 11:10:14',
            'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
            'beneficiaryBankName' => '*********',
            'beneficiaryAccountNumber' => '********************',
            'beneficiaryName' => '*********',
            'beneficiaryIdentificationNumber' => '*********',
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $collection = $this->collectionRepository->findById(1);
        $this->assertEquals('DocumentNumber123', $collection->getNumber());
        $this->assertEquals('Paysera Georgia', $collection->getLicensedPartner()->getTitle());
        $this->assertEquals('John Doe', $collection->getClient()->getDisplayName());
        $this->assertEquals('10.12', $collection->getAmount());
        $this->assertEquals('EUR', $collection->getCurrency());
        $this->assertEquals(5, $collection->getOrganizationPriority());
        $this->assertEquals('2023-08-28 10:10:14', $collection->getOrderingDate()->format('Y-m-d h:i:s'));

        $this->assertNotEmpty($this->collectionChangeLogRepository->findAll());
    }

    public function testEditSeizure(): void
    {
        $licensedPartner = (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
        $client = (new ClientLegal())
            ->setName('John Doe')
            ->setCovenanteeId(882);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_CREATE',
            'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_EDIT',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/create?subclass=Manual%20Collection%20Order');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14',
            'receivedDate' => '2023-08-30 11:10:14',
            'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
            'beneficiaryBankName' => '*********',
            'beneficiaryAccountNumber' => '********************',
            'beneficiaryName' => '*********',
            'beneficiaryIdentificationNumber' => '*********',
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/1/edit');
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Update');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumberEDITED',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14'
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $log = $this->collectionChangeLogRepository->findOneBy([
            'collection' => $this->collectionRepository->findById(1),
            'field' => 'number',
            'newValue' => 'DocumentNumberEDITED'
        ]);

        $this->assertNotNull($log);
    }

    private function logIn(array $roles): User
    {
        $session = $this->client->getContainer()->get('session');

        $user = new User();
        $user
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test')
        ;

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $token = new UsernamePasswordToken(
            $user,
            null,
            'main',
            $roles
        );

        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);

        return $user;
    }

    /**
     * @dataProvider invalidDataProvider
     */
    public function testCreateCollectionWithInvalidBeneficiaryData(array $data, string $expectedErrorMessage): void
    {
        $fixturesHelper = new GlobalFixturesHelper($this->entityManager);

        $fixturesHelper->createLicensedPartner();
        $fixturesHelper->createClientLegal();

        $this->logIn(['ROLE_ADMIN', 'ROLE_EVP_DEBT_ADMIN_COLLECTION_ORDER_ADMIN_CREATE']);
        $crawler = $this->client->request('GET', '/admin/evp/debt/collection/create?subclass=Manual%20Collection%20Order');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14',
            'receivedDate' => '2023-08-30 11:10:14',
            'beneficiaryBankSwiftCode' => $data['beneficiaryBankSwiftCode'],
            'beneficiaryBankName' => $data['beneficiaryBankName'],
            'beneficiaryAccountNumber' => $data['beneficiaryAccountNumber'],
            'beneficiaryName' => $data['beneficiaryName'],
            'beneficiaryIdentificationNumber' => $data['beneficiaryIdentificationNumber'],
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString($expectedErrorMessage, $this->client->getResponse()->getContent());
    }

    public function invalidDataProvider(): array
    {
        return [
            'Test with invalid Beneficiary Swift Code' => [
                [
                    'beneficiaryBankSwiftCode' => '12',
                    'beneficiaryBankName' => 'BeneficiaryBankName',
                    'beneficiaryAccountNumber' => '********************',
                    'beneficiaryName' => 'BeneficiaryName',
                    'beneficiaryIdentificationNumber' => 'BeneficiaryIdentificationNumber'
                ],
                'Provided SWIFT code is not valid',
            ],
            'Test with invalid Beneficiary Account Number' => [
                [
                    'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
                    'beneficiaryBankName' => 'BeneficiaryBankName',
                    'beneficiaryAccountNumber' => '*********',
                    'beneficiaryName' => 'BeneficiaryName',
                    'beneficiaryIdentificationNumber' => 'BeneficiaryIdentificationNumber'
                ],
                'Provided IBAN is not valid',
            ],
            'Test with invalid Beneficiary Bank Name' => [
                [
                    'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
                    'beneficiaryBankName' => '12',
                    'beneficiaryAccountNumber' => '********************',
                    'beneficiaryName' => 'BeneficiaryName',
                    'beneficiaryIdentificationNumber' => 'BeneficiaryIdentificationNumber'
                ],
                'This value is too short. It should have 3 characters or more.',
            ],
            'Test with invalid Beneficiary Name' => [
                [
                    'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
                    'beneficiaryBankName' => 'BeneficiaryBankName',
                    'beneficiaryAccountNumber' => '********************',
                    'beneficiaryName' => '12',
                    'beneficiaryIdentificationNumber' => 'BeneficiaryIdentificationNumber'
                ],
                'This value is too short. It should have 3 characters or more.',
            ],
            'Test with invalid Beneficiary Identification Number' => [
                [
                    'beneficiaryBankSwiftCode' => 'EVIULT21XXX',
                    'beneficiaryBankName' => 'BeneficiaryBankName',
                    'beneficiaryAccountNumber' => '********************',
                    'beneficiaryName' => 'BeneficiaryName',
                    'beneficiaryIdentificationNumber' => '12',
                ],
                'This value is too short. It should have 3 characters or more.',
            ],
        ];
    }
}
