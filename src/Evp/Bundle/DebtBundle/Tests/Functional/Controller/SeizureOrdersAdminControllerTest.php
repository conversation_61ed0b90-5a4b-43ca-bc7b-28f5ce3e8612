<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Tests\Functional\Controller;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Bundle\DebtBundle\Repository\SeizureRepository;
use Evp\Bundle\DebtBundle\Tests\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\Routing\Router;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class SeizureOrdersAdminControllerTest extends PersistableWebTestCase
{
    private EntityManagerInterface $entityManager;
    private SeizureRepository $seizureRepository;
    private Router $router;

    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase();
        $this->router = $this->client->getContainer()->get('router');
        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');
        $this->seizureRepository = $this->getContainer()->get('evp_debt.repository.seizure');
    }

    public function testShowComment(): void
    {
        $licensedPartner = (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
        $client = (new ClientLegal())
            ->setName('John Doe')
            ->setCovenanteeId(882);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_CREATE',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_VIEW',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_EDIT',
        ]);

        $crawler = $this->client->request('GET', $this->router->generate('admin_evp_debt_seizure_create',['subclass' => 'manual seizure']));

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14'
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $crawler = $this->client->request(
            'GET',
            $this->router->generate('admin_evp_debt_seizure_workflow_apply_transition', ['id' => '1', 'transition' => 'to_closed'])
        );
        $tokenNode = $crawler->filter('input[name="seizure_comment[_token]"]');
        $tokenValue = $tokenNode->attr('value');

        $this->client->request(
            'POST',
            $this->router->generate('admin_evp_debt_seizure_workflow_apply_transition', ['id' => '1', 'transition' => 'to_closed']),
            [
                'seizure_comment' => [
                    'comment' => 'TEST',
                    '_token' => $tokenValue,
                ],
                'btn_update_and_edit' => '',
            ]
        );

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue($this->client->getResponse()->isRedirect());
        $this->assertEquals(
            $this->router->generate('admin_evp_debt_seizure_edit', ['id' => '1']),
            $this->client->getResponse()->headers->get('Location')
        );

        $crawler = $this->client->request('GET', $this->router->generate('admin_evp_debt_seizure_show', ['id' => '1']));

        $hasChecked = false;
        $crawler->filter('tr')->each(function ($trNode) use (&$hasChecked)  {
            $thNode = $trNode->filter('th')->reduce(function ($node) {
                return trim($node->text()) === 'Comments';
            });

            if ($thNode->count() > 0) {
                $hasChecked = true;
                $liElements = $trNode->filter('li');
                $this->assertCount(1, $liElements);
                $liElements->each(function ($liNode) {
                    $liContent = $liNode->text();
                    $this->assertMatchesRegularExpression('/\w+ \(.+? \| [\w.]+ \| \d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\)/', $liContent);
                });
            }
        });

        $this->assertTrue($hasChecked);
    }

    public function testNotLoggedIn(): void
    {
        $this->client->request('GET', '/admin/evp/debt/seizure/list');

        $this->assertEquals(302, $this->client->getResponse()->getStatusCode());
        $this->assertTrue(
            strpos($this->client->getResponse()->headers->get('Location'), '/login') !== false
        );
    }

    public function testShowList(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createSeizureGeb();
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_LIST',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_VIEW'
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/seizure/list');
        $responseContent = $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertStringContainsString('Paysera Georgia', $responseContent);
        $this->assertStringContainsString('automatic', $responseContent);
        $this->assertStringContainsString('Private executor', $responseContent);
        $this->assertStringContainsString('documentNumber123', $responseContent);
        $this->assertStringContainsString('500', $responseContent);
        $this->assertStringContainsString('EUR', $responseContent);
        $this->assertStringContainsString('active', $responseContent);
        $this->assertStringContainsString('Some details about this seizure order', $responseContent);
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(0, $crawler->filter('.btn-default.edit_link'));
    }

    public function testShowEditButtonForManualSeizureInListView(): void
    {
        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createSeizureGeb(['triggerType' => 'manual']);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_LIST',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_VIEW',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_EDIT',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/seizure/list');
        $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertEquals(1, $crawler->filter('tbody tr')->count());
        $this->assertCount(1, $crawler->filter('.btn-default.view_link'));
        $this->assertCount(1, $crawler->filter('.btn-default.edit_link'));
    }

    public function testShowSingleEntity(): void
    {
        $user = $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_VIEW'
        ]);

        $fixturesHelper = new FixturesHelper($this->entityManager);
        $fixturesHelper->createSeizureGeb(['status' => 'registered', 'user' => $user]);
        $this->entityManager->flush();

        $this->client->request('GET', '/admin/evp/debt/seizure/1/show');
        $responseContent = $this->client->getResponse()->getContent();

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $this->assertStringContainsString('Paysera Georgia', $responseContent);
        $this->assertStringContainsString('automatic', $responseContent);
        $this->assertStringContainsString('Private executor', $responseContent);
        $this->assertStringContainsString('documentNumber123', $responseContent);
        $this->assertStringContainsString('500', $responseContent);
        $this->assertStringContainsString('EUR', $responseContent);
        $this->assertStringContainsString('active', $responseContent);
        $this->assertStringContainsString('Some details about this seizure order', $responseContent);
        $this->assertStringContainsString('First comment', $responseContent);
        $this->assertStringContainsString('Second comment', $responseContent);
    }

    public function testCreateSeizure(): void
    {
        $licensedPartner = (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
        $client = (new ClientLegal())
            ->setName('John Doe')
            ->setCovenanteeId(882);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->flush();

        $this->logIn(['ROLE_ADMIN', 'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_CREATE']);
        $crawler = $this->client->request('GET', '/admin/evp/debt/seizure/create?subclass=manual%20seizure');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14'
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();
        $seizure = $this->seizureRepository->find(1);
        $this->assertEquals('DocumentNumber123', $seizure->getNumber());
        $this->assertEquals('Paysera Georgia', $seizure->getLicensedPartner()->getTitle());
        $this->assertEquals('John Doe', $seizure->getClient()->getName());
        $this->assertEquals('10.12', $seizure->getAmount());
        $this->assertEquals('EUR', $seizure->getCurrency());
        $this->assertEquals(5, $seizure->getOrganizationPriority());
        $this->assertEquals('2023-08-28 10:10:14', $seizure->getOrderingDate()->format('Y-m-d h:i:s'));

        $this->assertNotEmpty(
            $this->entityManager->getRepository('EvpDebtBundle:SeizureChangeLog')->findAll()
        );
    }

    public function testEditSeizure(): void
    {
        $licensedPartner = (new LicensedPartner())
            ->setTitle('Paysera Georgia')
            ->setPartnerCode('paysera_ge')
            ->setOfficialName('Paysera Georgia')
            ->setOfficialAddress('Paysera Georgia address')
            ->setBankIdentificationCode('*********')
        ;
        $client = (new ClientLegal())
            ->setName('John Doe')
            ->setCovenanteeId(882);

        $this->entityManager->persist($licensedPartner);
        $this->entityManager->persist($client);
        $this->entityManager->flush();

        $this->logIn([
            'ROLE_ADMIN',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_CREATE',
            'ROLE_EVP_DEBT_ADMIN_SEIZURE_ORDERS_ADMIN_EDIT',
        ]);
        $crawler = $this->client->request('GET', '/admin/evp/debt/seizure/create?subclass=manual%20seizure');

        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Create');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumber123',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14'
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $crawler = $this->client->request('GET', '/admin/evp/debt/seizure/1/edit');
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
        $buttonCrawlerNode = $crawler->selectButton('Update');
        $form = $buttonCrawlerNode->form();

        $formValues = [
            'number' => 'DocumentNumberEDITED',
            'licensedPartner' => 1,
            'organizationPriority' => 5,
            'client' => 882,
            'amount' => '10.12',
            'currency' => 'EUR',
            'orderingDate' => '2023-08-28 11:10:14'
        ];

        foreach ($form->all() as $key => $field) {
            if (preg_match('/\[(\w+)]$/', $key, $matches)) {
                if (array_key_exists($matches[1], $formValues)) {
                    $form->setValues([$key => $formValues[$matches[1]]]);
                }
            }
        }

        $this->client->submit($form);
        $this->entityManager->clear();

        $log = $this->entityManager->getRepository('EvpDebtBundle:SeizureChangeLog')->findOneBy(
            [
                'seizure' => $this->seizureRepository->find(1),
                'field' => 'number',
                'newValue' => 'DocumentNumberEDITED'
            ]
        );

        $this->assertNotNull($log);
    }

    private function logIn(array $roles): User
    {
        $session = $this->client->getContainer()->get('session');

        $user = new User();
        $user
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test')
        ;

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $token = new UsernamePasswordToken(
            $user,
            null,
            'main',
            $roles
        );

        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);

        return $user;
    }
}
