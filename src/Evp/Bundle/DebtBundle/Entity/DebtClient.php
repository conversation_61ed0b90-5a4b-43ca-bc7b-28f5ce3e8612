<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Entity;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\Client;

abstract class DebtClient
{
    const TYPE_COLLECTION = 'collection';
    const TYPE_SEIZURE = 'seizure';

    protected int $id;
    protected Client $client;
    protected DateTimeImmutable $createdAt;

    /**
     * @var Hold[]|ArrayCollection
     */
    protected $holds;

    public function __construct()
    {
        $this->holds = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    abstract public function getType(): string;

    public function getId(): int
    {
        return $this->id;
    }

    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @return $this
     */
    public function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @return Hold[]|ArrayCollection
     */
    public function getHolds()
    {
        return $this->holds;
    }

    /**
     * @param Hold[]|ArrayCollection $holds
     * @return $this
     */
    public function setHolds($holds): self
    {
        $this->holds = $holds;
        return $this;
    }

    public function addHold(Hold $hold): self
    {
        $this->holds->add($hold);
        return $this;
    }
}
