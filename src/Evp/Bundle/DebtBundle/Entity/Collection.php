<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Entity;

use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\BankTransferBundle\Entity\Transfer;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Component\Money\Money;

abstract class Collection implements DebtAwareInterface
{
    public const TYPE_GRS = 'grs';
    public const TYPE_GEB = 'geb';
    public const TYPE_MANUAL = 'manual';

    public const TRIGGER_TYPE_AUTOMATIC = 'automatic';
    public const TRIGGER_TYPE_MANUAL = 'manual';
    public const TRIGGER_TYPES = [
        self::TRIGGER_TYPE_AUTOMATIC,
        self::TRIGGER_TYPE_MANUAL,
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_RETURNED = 'returned';

    const PRIORITY_NATIONAL_BANK = 1;
    const PRIORITY_REVENUE_SERVICE = 2;
    const PRIORITY_MINISTRY_OF_FINANCE = 3;
    const PRIORITY_NATIONAL_ENFORCEMENT_BUREAU = 4;
    const PRIORITY_PRIVATE_ENFORCEMENT_OFFICERS = 4;
    const PRIORITY_PRIVATE_EXECUTORS = 5;

    const WRITE_OFF_STATUS_WAITING = 'waiting'; // When write off is waiting for start
    const WRITE_OFF_STATUS_STARTED = 'started'; // When write off is In Progress now
    const WRITE_OFF_STATUS_DONE = 'done'; // When there is nothing left to write off. If something left, status will be null
    const WRITE_OFF_STATUS_FAILED = 'failed'; // When the system couldn't perform write off. Waiting for manual input.

    protected int $id;
    protected string $number;
    protected LicensedPartner $licensedPartner;
    protected string $collectionType;
    protected string $triggerType;
    protected ?string $details;
    protected ?Account $account;
    protected ?bool $appliedToAllAccounts;
    protected ?DateTimeImmutable $orderingDate;
    protected ?string $beneficiaryBankSwiftCode;
    protected ?string $beneficiaryBankName;
    protected ?string $beneficiaryAccountNumber;
    protected ?string $beneficiaryName;
    protected ?string $beneficiaryIdentificationNumber;
    protected ?int $organizationPriority;
    protected string $status;
    protected ?string $writeOffStatus;
    protected ?string $amount;
    protected ?string $currency;
    protected ?string $amountLeft;
    protected Client $client;
    protected DebtClientCollection $debtClient;
    protected DateTimeImmutable $createdAt;
    protected DateTimeImmutable $updatedAt;

    /** @var Hold[]|ArrayCollection */
    protected $holds;
    /** @var Transfer[]|ArrayCollection */
    protected $transfers;
    /** @var Commission[]|ArrayCollection */
    protected $commissions;
    /** @var CollectionComment[]|ArrayCollection */
    protected $comments;
    protected int $version;

    public function __construct()
    {
        $this->status = self::STATUS_ACTIVE;
        $this->writeOffStatus = null;
        $this->holds = new ArrayCollection();
        $this->transfers = new ArrayCollection();
        $this->commissions = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
        $this->comments = new ArrayCollection();
        $this->triggerType = self::TRIGGER_TYPE_AUTOMATIC;
    }

    abstract public function getCollectionType(): string;

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    /**
     * @return $this
     */
    public function setNumber(string $number): self
    {
        $this->number = $number;
        return $this;
    }

    public function getLicensedPartner(): LicensedPartner
    {
        return $this->licensedPartner;
    }

    /**
     * @return $this
     */
    public function setLicensedPartner(LicensedPartner $licensedPartner): self
    {
        $this->licensedPartner = $licensedPartner;
        return $this;
    }

    public function getOrganizationPriority(): int
    {
        return $this->organizationPriority;
    }

    /**
     * @return $this
     */
    public function setOrganizationPriority(int $organizationPriority): self
    {
        $this->organizationPriority = $organizationPriority;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getWriteOffStatus(): ?string
    {
        return $this->writeOffStatus;
    }

    /**
     * @return $this
     */
    public function setWriteOffStatus(?string $writeOffStatus): self
    {
        $this->writeOffStatus = $writeOffStatus;
        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function getAmountMoney(): ?Money
    {
        return $this->amount !== null && $this->currency !== null
            ? new Money($this->amount, $this->currency)
            : null;
    }

    /**
     * @return $this
     */
    public function setAmountMoney(?Money $amountMoney): self
    {
        if ($amountMoney !== null) {
            $this->amount = $amountMoney->getAmount();
            $this->currency = $amountMoney->getCurrency();
        } else {
            $this->amount = null;
            $this->currency = null;
        }
        return $this;
    }

    public function getAmountLeft(): ?string
    {
        return $this->amountLeft;
    }

    public function getAmountLeftMoney(): ?Money
    {
        return $this->amountLeft !== null && $this->currency !== null
            ? new Money($this->amountLeft, $this->currency)
            : null;
    }

    /**
     * @return $this
     */
    public function setAmountLeftMoney(?Money $amountLeftMoney): self
    {
        $this->amountLeft = $amountLeftMoney !== null ? $amountLeftMoney->getAmount() : null;
        return $this;
    }

    public function resetAmountLeftMoney(): self
    {
        $this->amountLeft = $this->currency !== null ? '0' : null;
        return $this;
    }

    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @return $this
     */
    public function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    public function getDebtClient(): DebtClientCollection
    {
        return $this->debtClient;
    }

    /**
     * @return $this
     */
    public function setDebtClient(DebtClientCollection $debtClient): self
    {
        $this->debtClient = $debtClient;
        $debtClient->addCollection($this);
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @return $this
     */
    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * @return $this
     */
    public function setUpdatedAt(DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * @return ArrayCollection|Hold[]
     */
    public function getHolds()
    {
        return $this->holds;
    }

    /**
     * @param ArrayCollection|Hold[] $holds
     * @return $this
     */
    public function setHolds($holds): self
    {
        $this->holds = $holds;
        return $this;
    }

    public function addHold(Hold $hold): self
    {
        $this->holds->add($hold);
        return $this;
    }

    /**
     * @return ArrayCollection|Transfer[]
     */
    public function getTransfers()
    {
        return $this->transfers;
    }

    /**
     * @param ArrayCollection|Transfer[] $transfers
     * @return $this
     */
    public function setTransfers($transfers): self
    {
        $this->transfers = $transfers;
        return $this;
    }

    public function addTransfer(Transfer $transfer): self
    {
        $this->transfers->add($transfer);
        return $this;
    }

    /**
     * @return ArrayCollection|Commission[]
     */
    public function getCommissions()
    {
        return $this->commissions;
    }

    /**
     * @param ArrayCollection|Commission[] $commissions
     * @return $this
     */
    public function setCommissions($commissions): self
    {
        $this->commissions = $commissions;
        return $this;
    }

    public function addCommission(Commission $commission): self
    {
        if (!$this->commissions->contains($commission)) {
            $this->commissions->add($commission);
        }

        return $this;
    }

    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isWriteOffStatusStartedOrDone(): bool
    {
        return in_array($this->getWriteOffStatus(), [self::WRITE_OFF_STATUS_STARTED, self::WRITE_OFF_STATUS_DONE], true);
    }

    /**
     * @return ArrayCollection|CollectionComment[]
     */
    public function getComments()
    {
        return $this->comments;
    }

    /**
     * @param ArrayCollection|CollectionComment[] $comments
     * @return $this
     */
    public function setComments($comments): self
    {
        $this->comments = $comments;

        return $this;
    }

    public function addComment(CollectionComment $comment): self
    {
        if (!$this->comments->contains($comment)) {
            $this->comments->add($comment);
        }

        return $this;
    }

    public function getTriggerType(): string
    {
        return $this->triggerType;
    }

    /**
     * @return $this
     */
    public function setTriggerType(string $triggerType): self
    {
        if (!in_array($triggerType, self::TRIGGER_TYPES)) {
            throw new \InvalidArgumentException(sprintf(
                'Supported triggers are: %s',
                implode(', ',self::TRIGGER_TYPES)
            ));
        }

        $this->triggerType = $triggerType;

        return $this;
    }

    public function getDetails(): ?string
    {
        return $this->details;
    }

    /**
     * @return $this
     */
    public function setDetails(string $details): self
    {
        $this->details = $details;

        return $this;
    }

    public function getAccount(): ?Account
    {
        return $this->account;
    }

    /**
     * @return $this
     */
    public function setAccount(Account $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function isAppliedToAllAccounts(): ?bool
    {
        return $this->appliedToAllAccounts;
    }

    /**
     * @return $this
     */
    public function setAppliedToAllAccounts(bool $appliedToAllAccounts): self
    {
        $this->appliedToAllAccounts = $appliedToAllAccounts;

        return $this;
    }

    public function getOrderingDate(): ?DateTimeImmutable
    {
        return $this->orderingDate;
    }

    /**
     * @return $this
     */
    public function setOrderingDate(DateTimeInterface $orderingDate): self
    {
        if ($orderingDate instanceof DateTime) {
            $orderingDate = DateTimeImmutable::createFromMutable($orderingDate);
        }
        $this->orderingDate = $orderingDate;

        return $this;
    }

    public function getBeneficiaryBankSwiftCode(): ?string
    {
        return $this->beneficiaryBankSwiftCode;
    }

    /**
     * @return $this
     */
    public function setBeneficiaryBankSwiftCode(string $beneficiaryBankSwiftCode): self
    {
        $this->beneficiaryBankSwiftCode = $beneficiaryBankSwiftCode;

        return $this;
    }

    public function getBeneficiaryBankName(): ?string
    {
        return $this->beneficiaryBankName;
    }

    /**
     * @return $this
     */
    public function setBeneficiaryBankName(string $beneficiaryBankName): self
    {
        $this->beneficiaryBankName = $beneficiaryBankName;

        return $this;
    }

    public function getBeneficiaryAccountNumber(): ?string
    {
        return $this->beneficiaryAccountNumber;
    }

    /**
     * @return $this
     */
    public function setBeneficiaryAccountNumber(string $beneficiaryAccountNumber): self
    {
        $this->beneficiaryAccountNumber = $beneficiaryAccountNumber;

        return $this;
    }

    public function getBeneficiaryName(): ?string
    {
        return $this->beneficiaryName;
    }

    /**
     * @return $this
     */
    public function setBeneficiaryName(string $beneficiaryName): self
    {
        $this->beneficiaryName = $beneficiaryName;

        return $this;
    }

    public function getBeneficiaryIdentificationNumber(): ?string
    {
        return $this->beneficiaryIdentificationNumber;
    }

    /**
     * @return $this
     */
    public function setBeneficiaryIdentificationNumber(string $beneficiaryIdentificationNumber): self
    {
        $this->beneficiaryIdentificationNumber = $beneficiaryIdentificationNumber;

        return $this;
    }
}
