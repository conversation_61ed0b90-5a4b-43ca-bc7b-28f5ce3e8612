<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Entity;

use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;
use Evp\Component\Money\Money;

abstract class Seizure implements DebtAwareInterface
{
    // TODO: SUPPORT-92722 public
    const TYPE_GRS = 'grs';
    public const TYPE_GEB = 'geb';
    public const TYPE_MANUAL = 'manual';

    public const TRIGGER_TYPE_AUTOMATIC = 'automatic';
    public const TRIGGER_TYPE_MANUAL = 'manual';
    public const TRIGGER_TYPES = [
        self::TRIGGER_TYPE_AUTOMATIC,
        self::TRIGGER_TYPE_MANUAL,
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_CANCELLED = 'cancelled';
    const STATUSES = [
        self::STATUS_ACTIVE,
        self::STATUS_CANCELLED,
    ];

    const PRIORITY_NATIONAL_BANK = 1;
    const PRIORITY_REVENUE_SERVICE = 2;
    const PRIORITY_MINISTRY_OF_FINANCE = 3;
    const PRIORITY_NATIONAL_ENFORCEMENT_BUREAU = 4;
    const PRIORITY_PRIVATE_ENFORCEMENT_OFFICERS = 4;
    const PRIORITY_PRIVATE_EXECUTORS = 5;
    const PRIORITIES = [
        self::PRIORITY_NATIONAL_BANK,
        self::PRIORITY_REVENUE_SERVICE,
        self::PRIORITY_MINISTRY_OF_FINANCE,
        self::PRIORITY_NATIONAL_ENFORCEMENT_BUREAU,
        self::PRIORITY_PRIVATE_EXECUTORS,
    ];

    protected int $id;
    protected string $number;
    protected LicensedPartner $licensedPartner;
    protected string $seizureType;
    protected string $triggerType;
    protected ?string $details;
    protected ?Account $account;
    protected ?bool $appliedToAllAccounts;
    protected ?DateTimeImmutable $orderingDate;
    protected int $organizationPriority;
    protected string $status;
    protected ?string $amount;
    protected ?string $currency;
    protected Client $client;
    protected DebtClientSeizure $debtClient;
    protected DateTimeImmutable $createdAt;
    protected DateTimeImmutable $updatedAt;
    /** @var SeizureComment[]|ArrayCollection */
    protected $comments;
    protected int $version;

    public function __construct()
    {
        $this->status = self::STATUS_ACTIVE;
        $this->createdAt = new DateTimeImmutable();
        $this->comments = new ArrayCollection();
        $this->triggerType = self::TRIGGER_TYPE_AUTOMATIC;
    }

    abstract public function getSeizureType(): string;

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return $this
     */
    public function setId(int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    /**
     * @return $this
     */
    public function setNumber(string $number): self
    {
        $this->number = $number;
        return $this;
    }

    public function getLicensedPartner(): LicensedPartner
    {
        return $this->licensedPartner;
    }

    /**
     * @return $this
     */
    public function setLicensedPartner(LicensedPartner $licensedPartner): self
    {
        $this->licensedPartner = $licensedPartner;
        return $this;
    }

    public function getOrganizationPriority(): int
    {
        return $this->organizationPriority;
    }

    /**
     * @return $this
     */
    public function setOrganizationPriority(int $organizationPriority): self
    {
        $this->organizationPriority = $organizationPriority;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @return $this
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function getAmountMoney(): ?Money
    {
        return $this->amount !== null && $this->currency !== null
            ? new Money($this->amount, $this->currency)
            : null
        ;
    }

    /**
     * @return $this
     */
    public function setAmountMoney(?Money $amountMoney): self
    {
        if ($amountMoney !== null) {
            $this->amount = $amountMoney->getAmount();
            $this->currency = $amountMoney->getCurrency();
        } else {
            $this->amount = null;
            $this->currency = null;
        }

        return $this;
    }

    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * @return $this
     */
    public function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    public function getDebtClient(): DebtClientSeizure
    {
        return $this->debtClient;
    }

    /**
     * @return $this
     */
    public function setDebtClient(DebtClientSeizure $debtClient): self
    {
        $this->debtClient = $debtClient;
        $debtClient->addSeizure($this);
        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    /**
     * @return $this
     */
    public function setCreatedAt(DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    /**
     * @return $this
     */
    public function setUpdatedAt(DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * @return ArrayCollection|SeizureComment[]
     */
    public function getComments()
    {
        return $this->comments;
    }

    /**
     * @param ArrayCollection|SeizureComment[] $comments
     * @return $this
     */
    public function setComments($comments): self
    {
        $this->comments = $comments;

        return $this;
    }

    /**
     * @return $this
     */
    public function addComment(SeizureComment $comment): self
    {
        if (!$this->comments->contains($comment)) {
            $this->comments->add($comment);
        }

        return $this;
    }

    public function getTriggerType(): string
    {
        return $this->triggerType;
    }

    /**
     * @return $this
     */
    public function setTriggerType(string $triggerType): self
    {
        if (!in_array($triggerType, self::TRIGGER_TYPES)) {
            throw new \InvalidArgumentException(sprintf(
                'Supported triggers are: %s',
                implode(', ',self::TRIGGER_TYPES)
            ));
        }

        $this->triggerType = $triggerType;

        return $this;
    }

    public function getDetails(): ?string
    {
        return $this->details;
    }

    /**
     * @return $this
     */
    public function setDetails(string $details): self
    {
        $this->details = $details;

        return $this;
    }

    public function getAccount(): ?Account
    {
        return $this->account;
    }

    /**
     * @return $this
     */
    public function setAccount(Account $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function isAppliedToAllAccounts(): ?bool
    {
        return $this->appliedToAllAccounts;
    }

    /**
     * @return $this
     */
    public function setAppliedToAllAccounts(bool $appliedToAllAccounts): self
    {
        $this->appliedToAllAccounts = $appliedToAllAccounts;

        return $this;
    }

    public function getOrderingDate(): ?DateTimeImmutable
    {
        return $this->orderingDate;
    }

    /**
     * @return $this
     */
    public function setOrderingDate(?DateTimeInterface $orderingDate): self
    {
        if ($orderingDate instanceof DateTime) {
            $orderingDate = DateTimeImmutable::createFromMutable($orderingDate);
        }

        $this->orderingDate = $orderingDate;

        return $this;
    }
}
