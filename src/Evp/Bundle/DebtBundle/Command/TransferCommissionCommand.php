<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Command;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankHoldBundle\Entity\Hold;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\DebtBundle\Entity\Commission;
use Evp\Bundle\DebtBundle\Repository\CommissionRepository;
use Evp\Bundle\DebtBundle\Repository\DebtClientRepository;
use Evp\Bundle\DebtBundle\Service\Commission\ChargeCommissionService;
use Exception;
use Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TransferCommissionCommand extends Command
{
    private const BATCH_SIZE = 1_000;

    private ChargeCommissionService $chargeCommissionService;
    private CommissionRepository $commissionRepository;
    private DebtClientRepository $debtClientRepository;
    private EntityManager $entityManager;
    private ConsoleProgressBarHelper $progressBarHelper;
    private LoggerInterface $logger;

    public function __construct(
        ChargeCommissionService $chargeCommissionService,
        CommissionRepository $commissionRepository,
        DebtClientRepository $debtClientRepository,
        EntityManager $entityManager,
        ConsoleProgressBarHelper $progressBarHelper,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->chargeCommissionService = $chargeCommissionService;
        $this->commissionRepository = $commissionRepository;
        $this->debtClientRepository = $debtClientRepository;
        $this->entityManager = $entityManager;
        $this->progressBarHelper = $progressBarHelper;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this->setDescription('Command will check all active commissions and do transfers for them if possible');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $total = $this->commissionRepository->countByStatus(Commission::STATUS_ACTIVE);
        $progressBar = $this->progressBarHelper->createProgressBar($output, $total);
        $progressBar->setRedrawFrequency(1);
        $progressBar->start();

        $offset = 0;
        $commissions = $this->getBatchCommissions($offset);

        while (count($commissions)) {
            foreach ($commissions as $commission) {
                $progressBar->advance();
                if ($this->isClientHasNotCoveredDebts($commission->getClient())) {
                    continue;
                }

                $this->entityManager->beginTransaction();

                try {
                    $this->chargeCommissionService->charge($commission);

                    $this->entityManager->flush();
                    $this->entityManager->commit();
                } catch (Exception $exception) {
                    $this->entityManager->rollback();
                    $this->logger->error(
                        'DebtBundle exceptional case: Exception occurred while transferring commission',
                        [$exception, $commission]
                    );
                }
            }

            $offset += self::BATCH_SIZE;
            $commissions = $this->getBatchCommissions($offset);
        }

        $output->writeln("\n");
        $output->writeln('Done.');
        return 0;
    }

    private function isClientHasNotCoveredDebts(Client $client): bool
    {
        $debtClients = $this->debtClientRepository->getAllByClient($client);

        foreach ($debtClients as $debtClient) {
            foreach ($debtClient->getHolds() as $hold) {
                // If client has active collection or seizure hold will exist, no need to check collection status
                if (in_array($hold->getStatus(), [Hold::STATUS_NEW, Hold::STATUS_WAITING_FUNDS])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @return Commission[]
     */
    private function getBatchCommissions(int $offset): array
    {
        return $this->commissionRepository->getAllByStatus(
            Commission::STATUS_ACTIVE,
            $offset,
            self::BATCH_SIZE
        );
    }
}
