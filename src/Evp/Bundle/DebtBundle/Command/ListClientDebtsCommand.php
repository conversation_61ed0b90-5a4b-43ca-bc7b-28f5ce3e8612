<?php

declare(strict_types=1);

namespace Evp\Bundle\DebtBundle\Command;

use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\DebtBundle\Entity\Collection;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\DebtBundle\Exception\DebtBundleException;
use Evp\Bundle\DebtBundle\Repository\CollectionRepository;
use Evp\Bundle\DebtBundle\Repository\SeizureRepository;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldAmountProvider;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldAmountService;
use Evp\Bundle\DebtBundle\Service\HoldService\HoldHelper;
use Evp\Bundle\DebtBundle\Service\HoldService\SeizureMoveAmountService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ListClientDebtsCommand extends Command
{
    private ClientRepository $clientRepository;
    private CollectionRepository $collectionRepository;
    private SeizureRepository $seizureRepository;
    private HoldHelper $holdHelper;
    private HoldAmountService $holdAmountService;
    private HoldAmountProvider $holdAmountProvider;
    private SeizureMoveAmountService $seizureMoveAmountService;

    public function __construct(
        ClientRepository $clientRepository,
        CollectionRepository $collectionRepository,
        SeizureRepository $seizureRepository,
        HoldHelper $holdHelper,
        HoldAmountService $holdAmountService,
        HoldAmountProvider $holdAmountProvider,
        SeizureMoveAmountService $seizureMoveAmountService
    ) {
        parent::__construct();

        $this->clientRepository = $clientRepository;
        $this->collectionRepository = $collectionRepository;
        $this->seizureRepository = $seizureRepository;
        $this->holdHelper = $holdHelper;
        $this->holdAmountService = $holdAmountService;
        $this->holdAmountProvider = $holdAmountProvider;
        $this->seizureMoveAmountService = $seizureMoveAmountService;
    }

    protected function configure()
    {
        $this
            ->setDescription('Command will list all debts for client and available money for them.')
            ->addArgument('clientId', InputArgument::REQUIRED, 'Client ID')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $client = $this->clientRepository->findOneById((int) $input->getArgument('clientId'));
        if ($client === null) {
            $output->writeln('Client not found');
            return 1;
        }

        $collections = $this->collectionRepository->findAllActiveByClientAndWriteOffStatuses($client, null);
        $seizures = $this->seizureRepository->findAllActiveByClient($client);

        $output->writeln(sprintf('Client: %s - %s', $client->getId(), $client->getDisplayName()));

        $output->writeln("\n\nCollections: ");
        $this->processCollections($output, $collections, $seizures);

        $output->writeln("\n\nSeizures: ");
        $this->processSeizures($output, $seizures);

        return 0;
    }

    /**
     * @param OutputInterface $output
     * @param Collection[] $collections
     * @param Seizure[] $seizures
     * @return void
     */
    private function processCollections(OutputInterface $output, array $collections, array $seizures): void
    {
        if (count($collections) === 0) {
            $output->writeln('No active collections found.');
            return;
        }

        $table = new Table($output);
        $table->setHeaders(['ID', 'Type', 'Priority', 'Number', 'Amount', 'Amount left', 'Hold amount', 'Seizure move']);

        $debtClientSeizure = count($seizures) ? reset($seizures)->getDebtClient() : null;

        $totalCollectionsAmountLeft = $this->holdAmountService->countTotalCollectionsAmountLeft($collections);
        $totalHoldAmount = $this->holdAmountService->countTotalHoldsHoldAmount(
            $this->holdHelper->filterActiveHolds(
                reset($collections)->getDebtClient()->getHolds()->toArray()
            )
        );

        foreach ($collections as $collection) {
            $holdAmount = $this->holdAmountProvider->getHoldAmountForCollection($collection);

            $seizureMove = null;
            if ($debtClientSeizure !== null) {
                try {
                    $seizureMove = $this->seizureMoveAmountService->countPossibleMoveAmount(
                        $debtClientSeizure,
                        $collection->getOrganizationPriority()
                    );
                } catch (DebtBundleException $exception) {
                }
            }

            $table->addRow([
                $collection->getId(),
                $collection->getCollectionType(),
                $collection->getOrganizationPriority(),
                $collection->getNumber(),
                $collection->getAmountMoney() ?? 'null',
                $collection->getAmountLeftMoney() ?? 'null',
                $holdAmount,
                $seizureMove,
            ]);
        }

        $table->render();

        $output->writeln(sprintf('Total collections amount left: %s', $totalCollectionsAmountLeft ?? 'infinite'));
        $output->writeln(sprintf('Total hold amount: %s', $totalHoldAmount));
    }

    /**
     * @param OutputInterface $output
     * @param Seizure[] $seizures
     * @return void
     */
    private function processSeizures(OutputInterface $output, array $seizures): void
    {
        if (count($seizures) === 0) {
            $output->writeln('No active seizures found.');
            return;
        }

        $table = new Table($output);
        $table->setHeaders(['ID', 'Type', 'Priority', 'Number', 'Amount', 'Hold amount']);

        $totalSeizuresAmount = $this->holdAmountService->countTotalSeizuresAmount($seizures);
        $totalHoldAmount = $this->holdAmountService->countTotalHoldsHoldAmount(
            $this->holdHelper->filterActiveHolds(
                reset($seizures)->getDebtClient()->getHolds()->toArray()
            )
        );

        foreach ($seizures as $seizure) {
            $holdAmount = $this->holdAmountProvider->getHoldAmountForSeizure($seizure);

            $table->addRow([
                $seizure->getId(),
                $seizure->getSeizureType(),
                $seizure->getOrganizationPriority(),
                $seizure->getNumber(),
                $seizure->getAmountMoney() ?? 'null',
                $holdAmount,
            ]);
        }

        $table->render();

        $output->writeln(sprintf('Total seizures amount: %s', $totalSeizuresAmount ?? 'infinite'));
        $output->writeln(sprintf('Total available amount: %s', $totalHoldAmount));
    }
}
