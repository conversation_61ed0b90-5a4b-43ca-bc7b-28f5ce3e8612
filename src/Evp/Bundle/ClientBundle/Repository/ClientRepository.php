<?php

namespace Evp\Bundle\ClientBundle\Repository;

use DateTime;
use DateTimeInterface;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientJoint;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\PartnerClient;
use Evp\Bundle\ClientBundle\Entity\TransferService\Agreement;
use Evp\Bundle\ClientRestBundle\Entity\Filter\ClientFilter;
use Evp\Bundle\DebtBundle\Entity\Seizure;
use Evp\Bundle\QuestionnaireBundle\Entity\Questionnaire;
use Evp\Component\GatewayCommon\BankClient\Entity\ClientFilter as LegacyClientFilter;
use Generator;
use Paysera\Bundle\RestBundle\Repository\FilterAwareRepositoryInterface;
use Paysera\Component\Serializer\Entity\Filter;
use Paysera\Pagination\Entity\Doctrine\ConfiguredQuery;
use Paysera\Pagination\Entity\OrderingConfiguration;
use Sonata\DoctrineORMAdminBundle\Datagrid\ProxyQuery;

class ClientRepository extends EntityRepository implements FilterAwareRepositoryInterface
{
    public const CLIENT_TYPE_REPOSITORY_MAP = [
        Client::TYPE_LEGAL => 'EvpClientBundle:ClientLegal',
        Client::TYPE_JOINT => 'EvpClientBundle:ClientJoint',
        Client::TYPE_NATURAL => 'EvpClientBundle:ClientNatural',
        Client::TYPE_APPLICATION => 'EvpClientBundle:ClientApplication',
    ];

    public const CLIENT_TYPE_LIKELY_TERM_MAP = [
        Client::TYPE_LEGAL => [
            'name',
            'code',
        ],
        Client::TYPE_NATURAL => [
            'firstName',
            'lastName',
        ],
        Client::TYPE_JOINT => [
            'contractNumber',
        ],
    ];

    /**
     * @param int $id
     *
     * @return Client|null
     */
    public function findOneById($id)
    {
        /** @var Client|null $client */
        $client = $this->find($id);

        return $client;
    }

    /**
     * Finds Client by ID. Also fetches transfer service agreement
     *
     * @param int $id
     *
     * @return Client|null
     */
    public function findOneByIdAndJoinServiceAgreement($id)
    {
        return $this->createQueryBuilder('c')
            ->select('c, a')
            ->leftJoin('c.transferServiceAgreement', 'a')
            ->where('c = :client')
            ->setParameter('client', $id)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param int $covenanteeId
     *
     * @return Client|null
     */
    public function findOneByCovenanteeId($covenanteeId)
    {
        return $this->findOneBy(['covenanteeId' => $covenanteeId]);
    }

    /**
     * @param int $idFrom
     * @param int $idTo
     * @return Client[]
     */
    public function getByIdInterval(int $idFrom, int $idTo)
    {
        return $this->createQueryBuilder('c')
            ->where('c.id >= :idFrom')
            ->andWhere('c.id < :idTo')
            ->setParameters([
                'idFrom' => $idFrom,
                'idTo' => $idTo,
            ])
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @return QueryBuilder
     */
    private function getAllNaturalAndLegalClientsQueryBuilder()
    {
        $queryBuilder = $this->createQueryBuilder('Client');

        $or = $queryBuilder->expr()->orX(
            'Client INSTANCE OF ' . ClientNatural::class,
            'Client INSTANCE OF ' . ClientLegal::class,
            'Client INSTANCE OF ' . ClientJoint::class
        );

        return $queryBuilder
            ->where($or)
            ->andWhere('Client.covenanteeId IS NOT NULL')
        ;
    }

    /**
     * Finds Client by Code. Also fetches transfer service agreement
     *
     * @param string $code
     *
     * @return Client
     */
    public function findOneByCode($code)
    {
        return $this->createQueryBuilder('c')
            ->select('c, a')
            ->leftJoin('c.transferServiceAgreement', 'a')
            ->where('c.code = :code')
            ->setParameters([
                'code' => $code,
            ])
            ->getQuery()
            ->getSingleResult()
        ;
    }

    /**
     * Finds Clients without TransferService\Agreement
     *
     * @return Client[]
     */
    public function findWithoutTransferServiceAgreement()
    {
        $query = $this->getEntityManager()->createQuery("
            SELECT c
            FROM EvpClientBundle:Client c
            WHERE NOT EXISTS (
                SELECT a.id
                FROM EvpClientBundle:TransferService\Agreement a
                WHERE a.client = c.id
            )
            ORDER BY c.id ASC
        ");

        return $query->getResult();
    }

    /**
     * Find likely clients by term
     *
     * @param string $term
     * @param int $limit
     *
     * @return mixed
     */
    public function findLikelyClientsByTerm($term, $limit = null)
    {
        $queryBuilders = [];

        $queryBuilders[] = $this->getEntityManager()
            ->getRepository('EvpClientBundle:ClientLegal')
            ->createQueryBuilder('cl')
            ->where('cl.code LIKE :term')
            ->orWhere('cl.vatCode LIKE :term')
            ->orWhere('cl.name LIKE :term')
            ->setParameter('term', '%' . $term . '%')
        ;
        $splitTerm = explode(' ', $term);
        $queryBuilderForNaturalClient = $this->getEntityManager()
            ->getRepository('EvpClientBundle:ClientNatural')
            ->createQueryBuilder('cn')
        ;
        foreach ($splitTerm as $item) {
            $queryBuilders[] = $queryBuilderForNaturalClient
                ->where('cn.code LIKE :term')
                ->orWhere('cn.firstName LIKE :term')
                ->orWhere('cn.lastName LIKE :term')
                ->setParameter('term', '%' . $item . '%')
            ;
        }

        $queryBuilders[] = $this->getEntityManager()
            ->getRepository('EvpClientBundle:ClientApplication')
            ->createQueryBuilder('ca')
            ->where('ca.name LIKE :term')
            ->setParameter('term', '%' . $term . '%')
        ;

        if ($limit) {
            foreach ($queryBuilders as $qb) {
                $qb->setMaxResults($limit);
            }
        }

        $result = [];
        foreach ($queryBuilders as $qb) {
            $result = array_merge($result, $qb->getQuery()->getResult());
        }

        if ($limit) {
            $result = array_slice($result, 0, $limit);
        }

        return $result;
    }

    /**
     * @param LegacyClientFilter $filter
     *
     * @return Client[]
     */
    public function findByFilter($filter)
    {
        $queryBuilder = $this->createAccountQueryBuilderForFilter($filter);
        $this->addLimits($queryBuilder, $filter);
        return $queryBuilder
            ->addOrderBy('c.id', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param LegacyClientFilter $filter
     *
     * @return int
     */
    public function findCountByFilter($filter)
    {
        return $this->createAccountQueryBuilderForFilter($filter)
            ->select('count(c)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function findByIban(string $iban, int $hydrationMode = AbstractQuery::HYDRATE_OBJECT): ?Client
    {
        return $this->createQueryBuilder('c')
            ->join('c.accounts', 'a')
            ->where('a.iban = :iban')
            ->andWhere('c.covenanteeId is not null')
            ->setParameter('iban', $iban)
            ->groupBy('c.id')
            ->getQuery()
            ->getOneOrNullResult($hydrationMode)
        ;
    }

    /**
     * @param array $ibanList
     * @return Client[]
     */
    public function findByIbanList(array $ibanList): array
    {
        return $this->createQueryBuilder('c')
            ->join('c.accounts', 'a')
            ->where('a.iban IN (:ibanList)')
            ->andWhere('c.covenanteeId is not null')
            ->setParameter(
                'ibanList',
                $ibanList
            )
            ->groupBy('c.id')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param LegacyClientFilter|ClientFilter $filter
     *
     * @return QueryBuilder
     */
    protected function createAccountQueryBuilderForFilter($filter): QueryBuilder
    {
        if ($filter->getType() !== null && isset(self::CLIENT_TYPE_REPOSITORY_MAP[$filter->getType()])) {
            $queryBuilder = $this->getEntityManager()
                ->getRepository(self::CLIENT_TYPE_REPOSITORY_MAP[$filter->getType()])
                ->createQueryBuilder('c')
            ;
        } else {
            $queryBuilder = $this->createQueryBuilder('c');
        }

        if (count($filter->getUserIds()) > 0) {
            $queryBuilder
                ->andWhere('c.covenanteeId IN (:userIds)')
                ->setParameter('userIds', $filter->getUserIds())
            ;
        }

        if ($filter->getCreditAllowed() !== null) {
            $queryBuilder
                ->join('c.transferServiceAgreement', 'agreement')
                ->andWhere('agreement.creditAllowed = :allowed')
                ->setParameter('allowed', $filter->getCreditAllowed())
            ;
        }

        if (
            $filter->getTerm() !== null
            && $filter->getType() !== null
            && isset(self::CLIENT_TYPE_LIKELY_TERM_MAP[$filter->getType()])
            && strlen($filter->getTerm()) >= 3
        ) {
            foreach (self::CLIENT_TYPE_LIKELY_TERM_MAP[$filter->getType()] as $term) {
                $queryBuilder->orWhere($queryBuilder->expr()->like('c.' . $term, ':term'));
            }

            $queryBuilder->setParameter('term', $filter->getTerm() . '%');
        }

        if (count($filter->getExcludeUserIds()) > 0) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->notIn('c.id', ':excludedIds'))
                ->setParameter('excludedIds', $filter->getExcludeUserIds());
        }

        return $queryBuilder;
    }

    private function createAccountQueryBuilderForV2Filter(ClientFilter $filter): QueryBuilder
    {
        $queryBuilder = $this->createAccountQueryBuilderForFilter($filter);

        $ibanAlias = $filter->getIbanAlias();
        $ibanAliasActive = $filter->getIbanALiasActive();
        if ($ibanAlias !== null || $ibanAliasActive !== null) {
            $queryBuilder
                ->join('c.accounts', 'a')
                ->join('a.ibanAliases', 'ia')
            ;
        }

        if ($ibanAlias !== null) {
            $queryBuilder
                ->andWhere('ia.iban = :ibanAlias')
                ->setParameter('ibanAlias', $ibanAlias)
            ;
        }

        if ($ibanAliasActive !== null) {
            $queryBuilder->andWhere($ibanAliasActive ? 'ia.isActive = true' : 'ia.isActive is null');
        }

        return $queryBuilder;
    }

    /**
     * Adds limit and offset by given filter
     *
     * @param QueryBuilder $queryBuilder
     * @param Filter $filter
     */
    protected function addLimits(QueryBuilder $queryBuilder, Filter $filter)
    {
        if ($filter->getLimit() !== null) {
            $queryBuilder->setMaxResults($filter->getLimit());
        }
        if ($filter->getOffset() !== null && $filter->getOffset() !== 0) {
            $queryBuilder->setFirstResult($filter->getOffset());
        }
    }

    /**
     * @param ProxyQuery $proxyQuery
     * @param string $alias
     * @param string $searchString
     */
    public function updateQueryBuilderForLikelyClientsByCovenanteeIdOrCode(
        ProxyQuery $proxyQuery,
        $alias,
        $searchString
    ) {
        $proxyQuery->getQueryBuilder()
            ->where($alias . '.covenanteeId = :covenanteeId')
            ->orWhere($alias . '.code LIKE :code')
            ->orWhere($alias . '.id = :clientId AND ' . $alias . ' INSTANCE OF :clientType')
            ->setParameter('covenanteeId', $searchString)
            ->setParameter('clientId', $searchString)
            ->setParameter('code', $searchString . '%')
            ->setParameter('clientType', Client::TYPE_APPLICATION)
        ;
    }

    /**
     * @param string[] $covenanteeIds
     *
     * @return Client[]
     */
    public function findByCovenanteeIdList(array $covenanteeIds): array
    {
        return $this->getFindByCovenanteeIdListQueryBuilder($covenanteeIds)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param string[] $covenanteeIds
     *
     * @return QueryBuilder
     */
    public function getFindByCovenanteeIdListQueryBuilder(array $covenanteeIds): QueryBuilder
    {
        return $this->createQueryBuilder('c')
            ->where('c.covenanteeId IN (:covenanteeIds)')
            ->setParameter('covenanteeIds', $covenanteeIds)
        ;
    }

    /**
     * @param int $questionnaireId
     * @return Client|null
     */
    public function findOneByQuestionnaireId(int $questionnaireId)
    {
        return $this->createQueryBuilder('c')
            ->join('EvpQuestionnaireBundle:Questionnaire', 'q', 'WITH', 'q.client = c')
            ->where('q.id = :questionnaire_id')
            ->setParameter('questionnaire_id', $questionnaireId)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param int $managerClientId
     *
     * @return Client[]|null
     */
    public function findManagedClientsByClientId(int $managerClientId)
    {
        $queryBuilder = $this->createQueryBuilder('c');

        return $queryBuilder
            ->select('c')
            ->join('c.accounts', 'a')
            ->join('a.permissions', 'p')
            ->join('p.clients', 'c2')
            ->where('c2.id = :id')
            ->andWhere('p.validFrom <= :date')
            ->andWhere($queryBuilder->expr()->orX('p.validTo is null', 'p.validTo > :date'))
            ->andWhere($queryBuilder->expr()->orX('c instance of :legal', 'c instance of :natural'))
            ->setParameters(
                [
                    'id' => $managerClientId,
                    'date' => new DateTime(),
                    'legal' => Client::TYPE_LEGAL,
                    'natural' => Client::TYPE_NATURAL,
                ]
            )
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param int $offset
     * @param int $limit
     * @param int[] $excludedIds
     *
     * @return Client[]|null
     */
    public function findByIdRangeAndType(int $offset, int $limit, array $excludedIds): ?array
    {
        $queryBuilder = $this->createQueryBuilder('c');

        return $queryBuilder
            ->select('c.id')
            ->where($queryBuilder->expr()->orX('c instance of :legal', 'c instance of :natural'))
            ->andWhere('c.covenanteeId is not null')
            ->andWhere('c.id not in (:excludedIds)')
            ->andWhere('c.id > :offset')
            ->setParameters(
                [
                    'legal' => Client::TYPE_LEGAL,
                    'natural' => Client::TYPE_NATURAL,
                    'excludedIds' => $excludedIds,
                    'offset' => $offset
                ]
            )
            ->setMaxResults($limit)
            ->orderBy('c.id', 'asc')
            ->getQuery()
            ->getScalarResult()
        ;
    }

    /**
     * @param int[] $idList
     * @return Client[]|null
     */
    public function findByIdList(array $idList): ?array
    {
        return $this->createQueryBuilder('c')
            ->where('c.id in (:idList)')
            ->setParameter('idList', $idList)
            ->getQuery()
            ->getResult()
        ;
    }

    public function getAllReportableByInterval(
        int $idFrom,
        int $idTo,
        bool $forSkis,
        int $limit = 1000
    ): Generator {
        $queryBuilder = $this->getReportableClientsByIntervalQueryBuilder($idFrom, $idTo, $forSkis);
        $offset = 0;

        $queryBuilder->setMaxResults($limit);

        while (true) {
            $queryBuilder->setFirstResult($offset);

            $clients = $queryBuilder->getQuery()->getResult();

            if (count($clients) === 0) {
                break;
            }

            yield $clients;

            $offset = $offset + $limit;
        }
    }

    public function getTotalReportableClientsByInterval(
        int $idFrom,
        int $idTo,
        bool $forSkis
    ): int {
        return (int)$this->getReportableClientsByIntervalQueryBuilder($idFrom, $idTo, $forSkis)
            ->select('COUNT(c.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    private function getReportableClientsByIntervalQueryBuilder(
        int $idFrom,
        int $idTo,
        bool $forSkis
    ): QueryBuilder {
        $acceptedQuestionnaireQueryBuilder = $this->_em->createQueryBuilder();
        $acceptedQuestionnaireQueryBuilder
            ->select('MAX(acceptedQuestionnaire.id)')
            ->from(Questionnaire::class, 'acceptedQuestionnaire')
            ->where(
                $acceptedQuestionnaireQueryBuilder->expr()->andX(
                    'acceptedQuestionnaire.client = c',
                    $acceptedQuestionnaireQueryBuilder->expr()->eq(
                        'acceptedQuestionnaire.status',
                        ':questionnaireStatus'
                    )
                )
            )
            ->groupBy('acceptedQuestionnaire.client')
        ;

        $partnerClientQueryBuilder = $this->_em->createQueryBuilder();
        $partnerClientQueryBuilder
            ->select('MAX(partnerClient.id)')
            ->from(PartnerClient::class, 'partnerClient')
            ->where(
                $partnerClientQueryBuilder->expr()->andX(
                    'partnerClient.client = c',
                    $partnerClientQueryBuilder->expr()->eq('partnerClient.partnerCode', ':partnerCode'),
                    $partnerClientQueryBuilder->expr()->isNull('partnerClient.assignedTo')
                )
            )
            ->groupBy('partnerClient.client')
        ;

        $queryBuilder = $this->createQueryBuilder('c');

        $queryBuilder
            ->leftJoin(Questionnaire::class, 'q', 'WITH', 'q.client = c')
            ->leftJoin(PartnerClient::class, 'pc', 'WITH', 'pc.client = c')
            ->where(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->gte('c.id', ':idFrom'),
                    $queryBuilder->expr()->lt('c.id', ':idTo'),
                    $queryBuilder->expr()->in(
                        'q.id',
                        $acceptedQuestionnaireQueryBuilder->getDQL()
                    ),
                    $queryBuilder->expr()->in(
                        'pc.id',
                        $partnerClientQueryBuilder->getDQL()
                    )
                )
            )
            ->setParameters([
                'idFrom' => $idFrom,
                'idTo' => $idTo,
                'questionnaireStatus' => Questionnaire::STATUS_ACCEPTED,
                'partnerCode' => 'paysera_lt',
            ])
            ->orderBy('c.id', 'ASC')
        ;

        if ($forSkis) {
            $queryBuilder
                ->leftJoin(Agreement::class, 'tsa', 'WITH', 'tsa.client = c')
                ->andWhere('tsa.creditAllowed = :creditAllowed')
                ->setParameter('creditAllowed', true)
            ;
        }

        return $queryBuilder;
    }

    public function findNaturalAndLegalClientCovenanteeIds(
        ?int $idFrom,
        ?string $partnerCode,
        int $limit,
        DateTimeInterface $date,
        ?array $covenanteeIds = null
    ): array {
        $queryBuilder = $this
            ->getAllNaturalAndLegalClientsQueryBuilder()
            ->select('Client.covenanteeId')
            ->orderBy('Client.covenanteeId', 'ASC')
            ->setMaxResults($limit)
        ;

        if ($idFrom !== null) {
            $queryBuilder
                ->andWhere('Client.covenanteeId > :idFrom')
                ->setParameter('idFrom', $idFrom)
            ;
        }

        if ($covenanteeIds !== null) {
            $queryBuilder
                ->andWhere('Client.covenanteeId IN (:covenanteeIds)')
                ->setParameter('covenanteeIds', $covenanteeIds)
            ;
        }

        if ($partnerCode !== null) {
            $queryBuilder
                ->join(PartnerClient::class, 'pc', 'WITH', 'pc.client = Client')
                ->andWhere('pc.assignedFrom <= :date')
                ->andWhere('pc.assignedTo > :date OR pc.assignedTo is null')
                ->setParameter('date', $date)
                ->andWhere('pc.partnerCode = :partnerCode')
                ->setParameter('partnerCode', $partnerCode)
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param string $seizureType
     * @param string $seizureStatus
     * @param int $limit
     * @param int $offset
     * @return Client[]
     */
    public function findBySeizureTypeAndStatus(
        string $seizureType,
        string $seizureStatus,
        int $limit = 100,
        int $offset = 0
    ): array {
        $queryBuilder = $this->createQueryBuilder('c')
            ->join(
                Seizure::class,
                's',
                Join::WITH,
                's.client = c.id'
            )
            ->where('s INSTANCE OF :seizureType')
            ->andWhere('s.status = :seizureStatus')
            ->setParameters([
                'seizureType' => $seizureType,
                'seizureStatus' => $seizureStatus,
            ])
            ->orderBy('c.id', 'ASC')
            ->setFirstResult($offset)
            ->setMaxResults($limit)
        ;

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param int $offsetId
     * @param int $limit
     * @param string[] $countryCodes
     * @return string[]|null
     */
    public function findIdsByRangeAndCountryCodes(int $offsetId, int $limit, array $countryCodes): ?array
    {
        $clientIds = $this->createQueryBuilder('c')
            ->select('c.id')
            ->where('c.countryCode IN (:countryCodes)')
            ->andWhere('c.id > :offsetId')
            ->setParameters(
                [
                    'countryCodes' => $countryCodes,
                    'offsetId' => $offsetId,
                ]
            )
            ->setMaxResults($limit)
            ->orderBy('c.id', 'asc')
            ->getQuery()
            ->getScalarResult()
        ;

        return array_column($clientIds, 'id');
    }

    /**
     * @param string $partnerCode
     * @param int $limit
     * @param int $offset
     * @return Client[]
     */
    public function findByPartnerCode(string $partnerCode, int $limit, int $offset): array
    {
        return $this->createQueryBuilder('c')
            ->join(PartnerClient::class, 'pc', Join::WITH, 'pc.client = c')
            ->where('pc.partnerCode = :partnerCode')
            ->andWhere('pc.assignedFrom <= :date')
            ->andWhere('pc.assignedTo > :date OR pc.assignedTo IS NULL')
            ->setParameters([
                'partnerCode' => $partnerCode,
                'date' => new DateTime(),
            ])
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param ClientFilter $filter
     * @param array $orderingPairs
     *
     * @return ConfiguredQuery
     */
    public function buildConfiguredQuery(ClientFilter $filter, array $orderingPairs): ConfiguredQuery
    {
        $configuredQuery = new ConfiguredQuery($this->createAccountQueryBuilderForV2Filter($filter));

        if (count($orderingPairs) === 0) {
            $configuredQuery->addOrderingConfiguration(
                'id',
                (new OrderingConfiguration('c.id', 'id'))
                    ->setOrderAscending(true)
            );
        }

        foreach ($orderingPairs as $orderingPair) {
            $orderBy = $orderingPair->getOrderBy();
            $configuredQuery->addOrderingConfiguration(
                $orderBy,
                new OrderingConfiguration(sprintf('c.%s', $orderBy), $orderBy)
            );
        }

        return $configuredQuery;
    }

    public function countClientIdsByTypeLevel(
        ?int $idFrom = null,
        ?int $idTo = null,
        ?string $clientType = null,
        array $clientLevels = []
    ): int {
        return (int) $this->getClientIdsByTypeLevelQB($idFrom, $idTo, $clientType, $clientLevels)
            ->select('COUNT(c.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findClientIdsByTypeLevel(
        ?int $idFrom = null,
        ?int $idTo = null,
        ?string $clientType = null,
        array $clientLevels = []
    ): IterableResult {
        return $this->getClientIdsByTypeLevelQB($idFrom, $idTo, $clientType, $clientLevels)
            ->select('c.id')
            ->orderBy('c.id', 'DESC')
            ->getQuery()
            ->iterate()
        ;
    }

    public function getClientIdsByTypeLevelQB(
        ?int $idFrom,
        ?int $idTo,
        ?string $clientType,
        array $clientLevels
    ): QueryBuilder {
        if ($clientType !== null && isset(self::CLIENT_TYPE_REPOSITORY_MAP[$clientType])) {
            $qb = $this->getEntityManager()
                ->getRepository(self::CLIENT_TYPE_REPOSITORY_MAP[$clientType])
                ->createQueryBuilder('c')
            ;
        } else {
            $qb = $this->createQueryBuilder('c');
        }

        if ($idFrom !== null) {
            $qb
                ->andWhere('c.covenanteeId >= :idFrom')
                ->setParameter('idFrom', $idFrom)
            ;
        }

        if ($idTo !== null) {
            $qb
                ->andWhere('c.covenanteeId <= :idTo')
                ->setParameter('idTo', $idTo)
            ;
        }

        if ($clientType !== null) {
            $qb
                ->andWhere('c instance of :clientType')
                ->setParameter('clientType', $clientType)
            ;
        }

        if ($clientType === Client::TYPE_NATURAL && count($clientLevels) > 0) {
            $qb
                ->andWhere('c.level in (:clientLevels)')
                ->setParameter('clientLevels', $clientLevels)
            ;
        }

        return $qb->andWhere('c.covenanteeId IS NOT NULL');
    }
}
