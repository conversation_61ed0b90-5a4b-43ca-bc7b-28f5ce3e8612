<?php

namespace Evp\Bundle\ClientBundle\Repository;

use DateTimeImmutable;
use Evp\Bundle\BankTransferBundle\Entity\PartyName;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;

/**
 * ClientNaturalRepository
 */
class ClientNaturalRepository extends ClientRepository
{
    /**
     * Finds Client by Code. Also fetches transfer service agreement
     *
     * @param string $code
     * @param string|null $country
     *
     * @return \Evp\Bundle\ClientBundle\Entity\ClientLegal
     */
    public function findOneByCode($code, $country = null)
    {
        $qb = $this->createQueryBuilder('c')
            ->select('c, a')
            ->leftJoin('c.transferServiceAgreement', 'a')
            ->where('c.code = :code')
            ->setParameter('code', $code)
        ;

        if ($country !== null) {
            $qb
                ->andWhere('c.countryCode = :countryCode')
                ->setParameter('countryCode', $country)
            ;

        }

        return $qb
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param null $limit
     * @param null $offset
     *
     * @return ClientNatural[]
     */
    public function findHavingCode($limit = null, $offset = null)
    {
        $qb = $this->createQueryBuilder('c')
            ->andWhere('c.code IS NOT NULL')
            ->orderBy('c.createdAt', 'ASC')
        ;

        if ($limit !== null) {
            $qb->setMaxResults($limit);
        }
        if ($offset !== null) {
            $qb->setFirstResult($offset);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @return int
     */
    public function getVerifiedClientsAccountCount()
    {
        $qb = $this->createQueryBuilder('c');
        $qb->select('COUNT(a)')
            ->from('Evp\Bundle\BankAccountBundle\Entity\Account', 'a')
            ->andWhere('a.client = c')
            ->andWhere('c.code IS NOT NULL')
            ->andWhere('a.closed = false')
        ;

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * Finds Clients without Account
     *
     * @return \Evp\Bundle\ClientBundle\Entity\ClientNatural[]
     */
    public function findWithoutAccount()
    {
        return $this->createQueryBuilder('c')
            ->leftJoin('c.accounts', 'a')
            ->join('c.transferServiceAgreement', 'ts')
            ->andWhere('a.id IS NULL')
            ->andWhere('ts.enabled = 1')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param int $limit
     * @param int $offset
     * @return null|ClientNatural[]
     */
    public function findUnidentified($limit = 100, $offset = 0)
    {
        return $this
            ->createQueryBuilder('c')
            ->select('c.id')
            ->where('c.level IN (:levels)')
            ->orWhere('c.level IS NULL')
            ->setParameter('levels', [IdentificationLevels::UNIDENTIFIED, IdentificationLevels::SEMI_IDENTIFIED])
            ->orderBy('c.id', 'ASC')
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
            ->getArrayResult()
        ;
    }

    /**
     * @param int $limit
     * @param int $lastId
     * @return ClientNatural[]
     */
    public function findUnidentifiedWithIDOffset(int $limit = 100, int $lastId = 0): array
    {
        $query = $this->createQueryBuilder('c');

        return $query->select('c.id')
            ->where('c.id > :lastId')
            ->andWhere('c INSTANCE OF :clientType')
            ->andWhere(
                $query->expr()->orX(
                    $query->expr()->in('c.level', ':levels'),
                    $query->expr()->isNull('c.level')
                )
            )
            ->setParameter('lastId', $lastId)
            ->setParameter('levels', [IdentificationLevels::UNIDENTIFIED, IdentificationLevels::SEMI_IDENTIFIED])
            ->setParameter('clientType', Client::TYPE_NATURAL)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getArrayResult()
        ;
    }

    /**
     * @param string $code
     * @param string $countryCode
     * @return ClientNatural|null
     */
    public function findOneByCodeAndCountryCode($code, $countryCode)
    {
        return $this
            ->createQueryBuilder('c')
            ->where('c.code = :code')
            ->andWhere('c.countryCode = :countryCode')
            ->setParameters([
                'code' => $code,
                'countryCode' => $countryCode,
            ])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param string $code
     * @param int|null $covenanteeId
     *
     * @return ClientNatural|null
     */
    public function findOneByCodeAndCovenanteeId(string $code, $covenanteeId = null)
    {
        return $this
            ->createQueryBuilder('c')
            ->where('c.code = :code')
            ->andWhere('c.covenanteeId = :covenanteeId')
            ->setParameters([
                'code' => $code,
                'covenanteeId' => $covenanteeId,
            ])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param string $code
     * @param PartyName $partyName
     * @return ClientNatural|null
     */
    public function findOneByNameAndCode(string $code, PartyName $partyName)
    {
        $qb = $this->createQueryBuilder('c');
        return $qb->where(
                $qb->expr()->like(
                    $qb->expr()->concat('c.firstName', $qb->expr()->concat($qb->expr()->literal(' '), 'c.lastName')),
                    ':phrase'
                )
            )
            ->andWhere('c.code = :code')
            ->setParameters([
                'phrase' => $partyName->getFirstName().' '.$partyName->getLastName().'%',
                'code' => $code,
            ])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param string[] $countryCodes
     * @param DateTimeImmutable $date
     * @param bool $unidentifiedOnly
     * @param bool $excludeCountries
     *
     * @return ClientNatural[]
     */
    public function findByResidenceCountryAndDate(
        array $countryCodes,
        DateTimeImmutable $date,
        bool $unidentifiedOnly,
        bool $excludeCountries
    ): array {
        $queryBuilder = $this->createQueryBuilder('c')
            ->where('c.createdAt >= :dateFrom')
            ->setParameter('dateFrom', $date)
        ;

        if ($excludeCountries) {
            $queryBuilder
                ->andWhere('c.residenceCountry not in (:residenceCountry)')
                ->setParameter('residenceCountry', $countryCodes)
            ;
        } else {
            $queryBuilder
                ->andWhere('c.residenceCountry  in (:residenceCountry)')
                ->setParameter('residenceCountry', $countryCodes)
            ;
        }

        if ($unidentifiedOnly) {
            $queryBuilder
                ->andWhere('c.level in (:level)')
                ->setParameter('level', ['unidentified', 'semi_identified'])
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    public function findOneByCodeAndResidenceCountry(string $code, string $residenceCountry): ?Client
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.code = :code')
            ->andWhere('c.residenceCountry = :residenceCountry')
            ->setParameters(['code' => $code, 'residenceCountry' => $residenceCountry])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function findClientIds(int $fromId, int $limit): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.id')
            ->andWhere('c.id > :fromId')
            ->setParameter('fromId', $fromId)
            ->orderBy('c.id', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getArrayResult()
        ;
    }
}
