<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Tests\Functional\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankPermissionBundle\Entity\Permission;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Component\HttpFoundation\Request;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\IdentificationDocument\IdentityReportClient;
use Evp\Component\UserRestClient\User\UserClient;
use Evp\Component\UserRestClient\UserRestFactory;
use Sonata\UserBundle\Model\UserInterface;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\PersistableWebTestCase;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Router;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class ClientAccountManagementControllerTest extends PersistableWebTestCase
{
    /** @var \Symfony\Bundle\FrameworkBundle\Client  */
    protected $client;

    protected FixturesHelper $fixturesHelper;
    private Router $router;

    private EntityManagerInterface $entityManager;

    private ClientRepository $clientRepository;

    protected function setUp(): void
    {
        $container = $this->getContainer();

        $this->entityManager = $this->getEntityManager();
        $this->client = $this->createClientWithNewDatabase(__DIR__ . '/../../DataFixtures');
        $this->router = $container->get('router');
        $this->clientRepository = $this->getContainer()->get('evp_client.repository.client');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->mockUserRestFactory();
    }

    public function testCreateAccountAction()
    {
        $clientId = 4;
        $this->sendRequest('GET', 'Evp_Bundle_ClientBundle_Client_createAccountAction', [
            'clientId' => $clientId,
        ]);

        /** @var RedirectResponse $response */
        $response = $this->client->getResponse();

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('http://example.com', $response->getTargetUrl());
    }

    public function testEditWrongIdPermissionAction()
    {
        $wrongPermissionId = 9999;
        $this->sendRequest('GET', 'Evp_Bundle_ClientBundle_Client_editPermission', [
            'permissionId' => $wrongPermissionId,
        ]);

        $response = $this->client->getResponse();
        $this->assertEquals(404, $response->getStatusCode());
    }

    public function testEditPermissionAction()
    {
        /** @var Client $client */
        $client = $this->clientRepository->find(1);
        $account = $client->getAccounts()->first();

        $signPermissions = $this->fixturesHelper->createSignPermissions([$client], $account);
        $this->entityManager->flush();

        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_editPermission', [
            'permissionId' => (int) $signPermissions->getId(),
        ]);

        $response = $this->client->getResponse();

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('http://example.com', $response->getTargetUrl());
    }

    public function testRemovePermissionAction_WrongPermissionId()
    {
        $wrongPermissionId = 9999;
        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_removePermission', [
            'permissionId' => $wrongPermissionId,
            'clientId' => 1
        ]);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
    }

    /**
     * @dataProvider addPermissionDataProvider
     */
    public function testAddPermissionAction($type, $clientId)
    {
        /** @var Client $client */
        $client = $this->clientRepository->find(1);
        $account = $client->getAccounts()->first();

        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_addPermission', [
            'type' => $type,
            'accountId' => $account->getId(),
            'clientId' => $clientId
        ]);

        $response = $this->client->getResponse();
        $validPermissionTypes = [
            Permission::TYPE_SIGN,
            Permission::TYPE_READ,
            Permission::TYPE_WRITE,
            Permission::TYPE_ADMINISTRATE,
            'all'
        ];

        if (in_array($type, $validPermissionTypes)) {
            $this->assertInstanceOf(RedirectResponse::class, $response);
            $this->assertEquals('http://example.com', $response->getTargetUrl());
        } else {
            $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
        }

    }

    public function addPermissionDataProvider()
    {
        return [
            'type_read' => [
                'type' => Permission::TYPE_READ,
                'clientId' => 1

            ],
            'type_write' => [
                'type' => Permission::TYPE_WRITE,
                'clientId' => 1
            ],
            'type_administrate' => [
                'type' => Permission::TYPE_ADMINISTRATE,
                'clientId' => 1
            ],
            'type_sign' => [
                'type' => Permission::TYPE_SIGN,
                'clientId' => 1
            ],
            'wrong_type' => [
                'type' => 'wrong_non_existing_type',
                'clientId' => 1
            ],
            'type_all' => [
                'type' => 'all',
                'clientId' => 1
            ],
        ];
    }

    public function testAddPermissionAction_WrongAccount()
    {
        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_addPermission', [
            'type' => Permission::TYPE_READ,
            'accountId' => 9999,
            'clientId' => 1
        ]);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
    }

    public function testEnableDisableAccountAction()
    {
        /** @var Client $client */
        $client = $this->clientRepository->find(1);
        $account = $client->getAccounts()->first();

        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_enableDisableAccount', [
            'accountId' => $account->getId(),
            'enable' => 0
        ]);

        $response = $this->client->getResponse();
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('http://example.com', $response->getTargetUrl());
    }

    public function testEnableDisableAccountAction_WrongAccount()
    {
        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_enableDisableAccount', [
            'request' => new Request(),
            'accountId' => 9999,
            'enable' => 0
        ]);

        $this->assertEquals(404, $this->client->getResponse()->getStatusCode());
    }

    public function testForceCloseAccountAction()
    {
        /** @var Client $client */
        $client = $this->clientRepository->find(1);
        $account = $client->getAccounts()->first();

        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_forceCloseAccount', [
            'accountId' => $account->getId(),
        ]);

        $response = $this->client->getResponse();
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('http://example.com', $response->getTargetUrl());
    }

    /**
     * @dataProvider changeFactoringValueDataProvider
     */
    public function testChangeFactoringAccountValueAction(int $value)
    {
        /** @var Client $client */
        $client = $this->clientRepository->find(1);
        $account = $client->getAccounts()->first();

        $this->sendRequest('POST', 'Evp_Bundle_ClientBundle_Client_changeFactoringAccountValue', [
            'accountId' => $account->getId(),
            'value' => $value
        ]);

        $response = $this->client->getResponse();
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals('http://example.com', $response->getTargetUrl());
    }

    public function changeFactoringValueDataProvider()
    {
        return [
            'value_true' => [1],
            'value_false' => [0],
        ];
    }

    private function logIn(array $roles): void
    {
        $user = new ClientNatural();
        $user->setId(5);

        $session = $this->client->getContainer()->get('session');
        $roles[] = UserInterface::ROLE_DEFAULT;
        $token = new UsernamePasswordToken(
            $user,
            null,
            'main',
            $roles
        );
        $session->set('_security_main', serialize($token));
        $session->save();
        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    private function sendRequest(string $method, string $route, array $arguments = [], string $content = null)
    {
        $this->logIn(['ROLE_ADMIN', 'ROLE_CLIENT']);

        $this->client->request(
            $method,
            $this->router->generate($route, $arguments),
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_REFERER' => 'http://example.com'
            ],
            $content
        );
    }

    private function mockUserRestFactory(): void
    {
        $userClientMock = $this->createMock(UserClient::class);
        $userClientMock->method('getUser')->willReturn(new UserInformation());

        $identityReportClientMock = $this->createMock(IdentityReportClient::class);

        $userRestFactoryMock = $this->createMock(UserRestFactory::class);
        $userRestFactoryMock->method('userClient')->willReturn($userClientMock);
        $userRestFactoryMock->method('identityReportClient')->willReturn($identityReportClientMock);


        $this->client->getContainer()->set('evp_user_client.user_rest_factory', $userRestFactoryMock);
    }
}
