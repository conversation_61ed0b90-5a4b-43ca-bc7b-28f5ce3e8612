<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Service;

use Evp\Bundle\ClientBundle\Entity\ClientRequest;
use Evp\Component\UserCommon\Entity\UserInformation;

class RequestAwareUserInformationProvider
{
    public function getUserInformation(ClientRequest $clientRequest): UserInformation
    {
        $userInformation = new UserInformation();

        if ($clientRequest->getClient() === null) {
            return $userInformation;
        }

        $userInformation->setNationality($clientRequest->getClient()->getCountryCode());

        $clientLegalFlagsRequest = $clientRequest->getClientLegalFlagsRequest();

        if ($clientLegalFlagsRequest === null) {
            return $userInformation;
        }

        $userInformation->setPaymentServiceProvider($clientLegalFlagsRequest->isPsp());
        $userInformation->setLocatedInOffshore($clientLegalFlagsRequest->isOffshore());

        return $userInformation;
    }
}
