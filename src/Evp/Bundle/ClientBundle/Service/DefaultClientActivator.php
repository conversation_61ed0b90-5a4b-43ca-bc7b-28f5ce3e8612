<?php

namespace Evp\Bundle\ClientBundle\Service;

use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Service\LocalAccountProvider;
use Evp\Bundle\BankPermissionBundle\Service\LevelLimitsHelper;
use Evp\Bundle\BankPermissionBundle\Service\LevelLimitsProvider;
use Evp\Bundle\BankPermissionBundle\Service\PermissionEntityManager;
use Evp\Bundle\ClientBundle\Entity\ClientApplication;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManagerInterface;
use Evp\Bundle\ClientBundle\Entity\ClientNatural;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\AdministratePermission;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\WritePermission;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\ReadPermission;
use Evp\Bundle\IdentificationLevelCommonBundle\Service\LimitsFixer;
use Evp\Component\UserCommon\Entity\UserInformation;
use Psr\Log\LoggerInterface;
use InvalidArgumentException;

/**
 * Activates client with default values - creates transfer agreement, account, permissions with default limits
 */
class DefaultClientActivator
{
    protected $entityManager;
    protected $permissionManager;
    protected $permissionEntityManager;
    protected $localAccountProvider;
    protected $levelProvider;
    protected $levelLimitsProvider;
    protected $levelLimitsHelper;
    protected $logger;
    protected $limitsFixer;

    public function __construct(
        EntityManager $entityManager,
        PermissionManagerInterface $permissionManager,
        PermissionEntityManager $permissionEntityManager,
        LocalAccountProvider $localAccountProvider,
        LevelLimitsProvider $levelLimitsProvider,
        LevelLimitsHelper $levelLimitsHelper,
        LoggerInterface $logger,
        LimitsFixer $limitsFixer
    ) {
        $this->entityManager = $entityManager;
        $this->permissionManager = $permissionManager;
        $this->permissionEntityManager = $permissionEntityManager;
        $this->localAccountProvider = $localAccountProvider;
        $this->levelLimitsProvider = $levelLimitsProvider;
        $this->levelLimitsHelper = $levelLimitsHelper;
        $this->logger = $logger;
        $this->limitsFixer = $limitsFixer;
    }

    /**
     * Creates agreement and account for client
     * If client is natural, creates account permissions with default limits
     * Does not check if client already has any of these
     */
    public function activateClient(Client $client, UserInformation $userInformation = null): Account
    {
        $this->logger->debug('Activating client', [$client]);

        $account = $this->localAccountProvider->createAccount($client, $userInformation);

        if ($client instanceof ClientNatural) {
            if ($account->getType() === Account::TYPE_TECHNICAL) {
                $this->logger->info(
                    'Skipping adding permissions for client to a technical account when activating a client',
                    [$client, $account]
                );
                return $account;
            }
            $this->createReadPermission($client, $account);
            $this->createWritePermission($client, $account);
            $this->createAdministratePermission($client, $account);
            $this->createSignPermission($client, $account);
        }

        return $account;
    }

    /**
     * Creates missing permissions for client. Client must be Natural or Application client
     *
     * @param Client  $client
     * @param Account $account
     */
    public function createMissingPermissions(Client $client, Account $account)
    {
        if (!$this->canCreateMissingPermissions($client)) {
            $this->logger->debug('Unsupported client type passed', [$client]);

            throw new InvalidArgumentException(
                'Expected natural or application client, got ' . get_class($client)
            );
        }

        if ($account->getType() === Account::TYPE_TECHNICAL) {
            $this->logger->info(
                'Skipping adding permissions for client to a technical account',
                [$client, $account]
            );
            return;
        }

        $this->logger->debug('Creating missing permissions for client and account', [$client, $account]);

        if (!$this->permissionManager->hasReadPermission($client, $account, false)) {
            $this->createReadPermission($client, $account);
        }
        if (!$this->permissionManager->hasWritePermission($client, $account, false)) {
            $this->createWritePermission($client, $account);
        }
        if (!$this->permissionManager->hasAdministratePermission($client, $account, false)) {
            $this->createAdministratePermission($client, $account);
        }
        if (!$this->permissionManager->hasSignPermission($client, $account, false)) {
            $this->createSignPermission($client, $account);
        }
    }

    public function canCreateMissingPermissions(Client $client): bool
    {
        return $client instanceof ClientNatural || $client instanceof ClientApplication;
    }

    /**
     * Creates and persists read permission
     *
     * @param Client  $client
     * @param Account $account
     *
     * @return ReadPermission
     */
    public function createReadPermission(Client $client, Account $account)
    {
        $this->logger->debug('Creating read permission', [$client, $account]);

        $readPermission = new ReadPermission();
        $readPermission->setAccount($account);
        $readPermission->addClient($client);
        $readPermission->setValidFrom(new \DateTime());

        $this->permissionEntityManager->persistPermission($readPermission);

        return $readPermission;
    }

    /**
     * Creates and persists write permission
     *
     * @param Client  $client
     * @param Account $account
     *
     * @return WritePermission
     */
    public function createWritePermission(Client $client, Account $account)
    {
        $this->logger->debug('Creating write permission', [$client, $account]);

        $writePermission = new WritePermission();
        $writePermission->setAccount($account);
        $writePermission->addClient($client);
        $writePermission->setValidFrom(new \DateTime());

        $this->permissionEntityManager->persistPermission($writePermission);

        return $writePermission;
    }

    /**
     * Creates and persists administrate permission
     *
     * @param Client  $client
     * @param Account $account
     *
     * @return AdministratePermission
     */
    public function createAdministratePermission(Client $client, Account $account)
    {
        $this->logger->debug('Creating administrate permission', [$client, $account]);

        $administratePermission = new AdministratePermission();
        $administratePermission->setAccount($account);
        $administratePermission->addClient($client);
        $administratePermission->setValidFrom(new \DateTime());

        $this->permissionEntityManager->persistPermission($administratePermission);

        return $administratePermission;
    }

    /**
     * Creates and persists sign permission with default limits
     *
     * @param Client  $client
     * @param Account $account
     *
     * @return SignPermission|null
     */
    public function createSignPermission(Client $client, Account $account)
    {
        $this->logger->debug('Creating sign permission', [$client, $account]);

        $signPermission = new SignPermission();
        $signPermission->setLevel(SignPermission::LEVEL_A);
        $signPermission->setAccount($account);
        $signPermission->addClient($client);
        $signPermission->setValidFrom(new \DateTime());

        if ($account->getClient() instanceof ClientNatural || $account->getClient() instanceof ClientLegal) {
            $this->logger->debug('Setting default limits for client ' . $account->getClient()->getClientType());

            $levelLimits = $this->levelLimitsProvider->getStandardLimitsByClient($client);
            $levelLimits = $this->limitsFixer->resolveAllLimits($levelLimits);

            $this->levelLimitsHelper->applyLevelLimitsForSignPermission($levelLimits, $signPermission);
        } else {
            $this->logger->debug('Unsupported client, sign permission was not persisted');

            return null;
        }

        $this->permissionEntityManager->persistPermission($signPermission);

        return $signPermission;
    }
}
