<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/*.xml"/>
    </imports>

    <parameters>
        <parameter type="string" key="evp_client.default_client_company_type_country_code">GB</parameter>
        <parameter key="evp_client.client_company_type_abbreviations" type="collection">
            <parameter key="LT" type="collection">
                <parameter key="private_limited_liability">UAB</parameter>
                <parameter key="sole_proprietorship">IĮ</parameter>
                <parameter key="joint_stock">AB</parameter>
                <parameter key="association_apartment_house_owners">DNSB</parameter>
                <parameter key="residential_construction_association">GNSB</parameter>
                <parameter key="representative_office">Įmonės atstovybė</parameter>
                <parameter key="branch_legal_entity">Juridinio asmens filialas</parameter>
                <parameter key="cooperative_enterprise">Kooperatinė bendrovė</parameter>
                <parameter key="credit_union">Kredito unija</parameter>
                <parameter key="charitable_foundation">Labdaros fondas ir organizacija</parameter>
                <parameter key="trade_union">Profesinė sąjunga</parameter>
                <parameter key="municipal_budgetary_establishment">Savivaldybės biudžetinė įstaiga</parameter>
                <parameter key="community_gardeners">Sodininkų bendrija</parameter>
                <parameter key="general_partnership">Tikroji ūkinė bendrija</parameter>
                <parameter key="association">Asociacija</parameter>
                <parameter key="traditional_religious_community">Tradicinė religinė bendruomenė ar bendrija</parameter>
                <parameter key="representative_office_foreign_company">Užsienio įmonės atstovybė</parameter>
                <parameter key="branch_foreign_company">Užsienio įmonės filialas</parameter>
                <parameter key="state_budgetary_establishment">Valstybės biudžetinė įstaiga</parameter>
                <parameter key="state_enterprise">VĮ</parameter>
                <parameter key="public_institution">VšĮ</parameter>
                <parameter key="non_governmental_organization">Visuomeninė organizacija</parameter>
                <parameter key="agricultural_enterprise">ŽŪB</parameter>
                <parameter key="little_community">MB</parameter>
            </parameter>
            <parameter key="GB" type="collection">
                <parameter key="private_limited_liability">Ltd</parameter>
                <parameter key="sole_proprietorship">SP</parameter>
                <parameter key="joint_stock">JSC</parameter>
                <parameter key="association">Association</parameter>
                <parameter key="association_apartment_house_owners">MHA</parameter>
                <parameter key="residential_construction_association">AHB</parameter>
                <parameter key="representative_office">Representative office</parameter>
                <parameter key="branch_legal_entity">Branch of a legal entity</parameter>
                <parameter key="cooperative_enterprise">Cooperative enterprise</parameter>
                <parameter key="credit_union">Credit union</parameter>
                <parameter key="charitable_foundation">Charitable foundation and organization</parameter>
                <parameter key="trade_union">Trade union</parameter>
                <parameter key="municipal_budgetary_establishment">Municipal budgetary establishment</parameter>
                <parameter key="community_gardeners">Community gardeners</parameter>
                <parameter key="general_partnership">General partnership</parameter>
                <parameter key="traditional_religious_community">Traditional religious community or association</parameter>
                <parameter key="representative_office_foreign_company">Representative office of a foreign company</parameter>
                <parameter key="branch_foreign_company">Branch of a foreign company</parameter>
                <parameter key="state_budgetary_establishment">State budgetary establishment</parameter>
                <parameter key="state_enterprise">SE</parameter>
                <parameter key="public_institution">PI</parameter>
                <parameter key="non_governmental_organization">NGO</parameter>
                <parameter key="agricultural_enterprise">AC</parameter>
                <parameter key="little_community">MC</parameter>
            </parameter>
            <parameter key="AL" type="collection">
                <parameter key="private_limited_liability">Shpk</parameter>
                <parameter key="sole_proprietorship">SP</parameter>
                <parameter key="joint_stock">JSC</parameter>
                <parameter key="association">Shoqatë</parameter>
                <parameter key="association_apartment_house_owners">MHA</parameter>
                <parameter key="residential_construction_association">AHB</parameter>
                <parameter key="representative_office">Zyra e përfaqësimit</parameter>
                <parameter key="branch_legal_entity">Filijala e entitetit legal</parameter>
                <parameter key="cooperative_enterprise">Ndërmarrje kooperativë</parameter>
                <parameter key="credit_union">Union kreditor</parameter>
                <parameter key="charitable_foundation">Fondacioni dhe organizata e bamirësisë</parameter>
                <parameter key="trade_union">Unioni tregtar</parameter>
                <parameter key="municipal_budgetary_establishment">Ndërmarrja komunale buxhetore</parameter>
                <parameter key="community_gardeners">Kopshtarët e komunitetit</parameter>
                <parameter key="general_partnership">Partnershipi i përgjithshëm</parameter>
                <parameter key="traditional_religious_community">Komuniteti tradicional fetar ose shoqata</parameter>
                <parameter key="representative_office_foreign_company">Zyra përfaqësuese e një kompanie të huaj</parameter>
                <parameter key="branch_foreign_company">Filijala e një kompanie të huaj</parameter>
                <parameter key="state_budgetary_establishment">Ndërmarrja shtetërore buxhetore</parameter>
                <parameter key="state_enterprise">SE</parameter>
                <parameter key="public_institution">PI</parameter>
                <parameter key="non_governmental_organization">Organizata joqeveritare</parameter>
                <parameter key="agricultural_enterprise">AC</parameter>
                <parameter key="little_community">MC</parameter>
            </parameter>
            <parameter key="RU" type="collection">
                <parameter key="private_limited_liability">ЗАО</parameter>
                <parameter key="sole_proprietorship">ЧП</parameter>
                <parameter key="joint_stock">АО</parameter>
                <parameter key="association">Ассоциация</parameter>
                <parameter key="association_apartment_house_owners">ОСМД</parameter>
                <parameter key="residential_construction_association">ЖА</parameter>
                <parameter key="representative_office">Представительство компании</parameter>
                <parameter key="branch_legal_entity">Филиал юридического лица</parameter>
                <parameter key="cooperative_enterprise">КП</parameter>
                <parameter key="credit_union">Кредитный союз</parameter>
                <parameter key="charitable_foundation">Благотворительный фонд и организация</parameter>
                <parameter key="trade_union">Профессиональный союз</parameter>
                <parameter key="municipal_budgetary_establishment">МБУ</parameter>
                <parameter key="community_gardeners">Сообщество садоводов</parameter>
                <parameter key="general_partnership">Полное хозяйственное товарищество</parameter>
                <parameter key="traditional_religious_community">Традиционная религиозная община или общество</parameter>
                <parameter key="representative_office_foreign_company">Представительство иностранной компании</parameter>
                <parameter key="branch_foreign_company">Филиал иностранной компании</parameter>
                <parameter key="state_budgetary_establishment">ГБУ</parameter>
                <parameter key="state_enterprise">ГП</parameter>
                <parameter key="public_institution">ОУ</parameter>
                <parameter key="non_governmental_organization">Общественная организация</parameter>
                <parameter key="agricultural_enterprise">АФ</parameter>
                <parameter key="little_community">МО</parameter>
            </parameter>
            <parameter key="LV" type="collection">
                <parameter key="private_limited_liability">SIA</parameter>
                <parameter key="sole_proprietorship">IK</parameter>
                <parameter key="joint_stock">AS</parameter>
                <parameter key="association">Biedrība</parameter>
                <parameter key="association_apartment_house_owners">MHA</parameter>
                <parameter key="residential_construction_association">AHB</parameter>
                <parameter key="representative_office">Pārstāvniecība</parameter>
                <parameter key="branch_legal_entity">Uzņēmuma filiāle</parameter>
                <parameter key="cooperative_enterprise">Kooperatīvā sabiedrība</parameter>
                <parameter key="credit_union">Kredītiestāde</parameter>
                <parameter key="charitable_foundation">Labdarības fonds un organizācija</parameter>
                <parameter key="trade_union">Tirdzniecības uzņēmums</parameter>
                <parameter key="municipal_budgetary_establishment">Pašvaldības iestāde</parameter>
                <parameter key="community_gardeners">Dārzkopības sabiedrība</parameter>
                <parameter key="general_partnership">Pilnsabierdība</parameter>
                <parameter key="traditional_religious_community">Reliģiskā organizācija</parameter>
                <parameter key="representative_office_foreign_company">Ārvalstu uzņēmuma pārstāvniecība</parameter>
                <parameter key="branch_foreign_company">Ārvalsts uzņēmuma filiāle</parameter>
                <parameter key="state_budgetary_establishment">Valsts iestāde</parameter>
                <parameter key="state_enterprise">VU</parameter>
                <parameter key="public_institution">PI</parameter>
                <parameter key="non_governmental_organization">NVO</parameter>
                <parameter key="agricultural_enterprise">ZS</parameter>
                <parameter key="little_community">MC</parameter>
            </parameter>
            <parameter key="BG" type="collection">
                <parameter key="private_limited_liability">ООД</parameter>
                <parameter key="sole_proprietorship">ЕТ</parameter>
                <parameter key="joint_stock">АД</parameter>
                <parameter key="association">Асоциация</parameter>
                <parameter key="association_apartment_house_owners">АСЖС</parameter>
                <parameter key="residential_construction_association">AHB</parameter>
                <parameter key="representative_office">Представителен офис</parameter>
                <parameter key="branch_legal_entity">Клон на юридическо лице</parameter>
                <parameter key="cooperative_enterprise">Кооперативно предприятие</parameter>
                <parameter key="credit_union">Кредитен съюз</parameter>
                <parameter key="charitable_foundation">Благотворителна фондация и организация</parameter>
                <parameter key="trade_union">Професионален съюз</parameter>
                <parameter key="municipal_budgetary_establishment">Общинско бюджетно учреждение</parameter>
                <parameter key="community_gardeners">Общност на градинари</parameter>
                <parameter key="general_partnership">Събирателно дружество</parameter>
                <parameter key="traditional_religious_community">Традиционна религиозна общност или сдружение</parameter>
                <parameter key="representative_office_foreign_company">Представителство на чуждестранна фирма</parameter>
                <parameter key="branch_foreign_company">Клон на чуждестранно дружество</parameter>
                <parameter key="state_budgetary_establishment">Държавно бюджетно учреждение</parameter>
                <parameter key="state_enterprise">ДП</parameter>
                <parameter key="public_institution">ПИ</parameter>
                <parameter key="non_governmental_organization">Неправителствена организация</parameter>
                <parameter key="agricultural_enterprise">AC</parameter>
                <parameter key="little_community">MC</parameter>
            </parameter>
            <parameter key="PL" type="collection">
                <parameter key="private_limited_liability">ZSA</parameter>
                <parameter key="sole_proprietorship">SO</parameter>
                <parameter key="joint_stock">SA</parameter>
                <parameter key="association">Asocjacja</parameter>
                <parameter key="association_apartment_house_owners">WM</parameter>
                <parameter key="residential_construction_association">SM</parameter>
                <parameter key="representative_office">Przedstawicielstwo firmy</parameter>
                <parameter key="branch_legal_entity">Oddział osoby prawnej</parameter>
                <parameter key="cooperative_enterprise">Spółka korporacyjna</parameter>
                <parameter key="credit_union">Unia kredytowa</parameter>
                <parameter key="charitable_foundation">Fundusz i organizacja dobroczynna</parameter>
                <parameter key="trade_union">Związek zawodowy</parameter>
                <parameter key="municipal_budgetary_establishment">Gminna jednostka budżetowe</parameter>
                <parameter key="community_gardeners">Wspólnota gruntowa</parameter>
                <parameter key="general_partnership">Spółka jawna</parameter>
                <parameter key="traditional_religious_community">Tradycyjna wspólnota lub gmina religijna</parameter>
                <parameter key="representative_office_foreign_company">Przedstawicielstwo firmy zagranicznej</parameter>
                <parameter key="branch_foreign_company">Oddział firmy zagranicznej</parameter>
                <parameter key="state_budgetary_establishment">Państwowa jednostka budżetowa</parameter>
                <parameter key="state_enterprise">PP</parameter>
                <parameter key="public_institution">IP</parameter>
                <parameter key="non_governmental_organization">OS</parameter>
                <parameter key="agricultural_enterprise">SR</parameter>
                <parameter key="little_community">MW</parameter>
            </parameter>
            <parameter key="ES" type="collection">
                <parameter key="private_limited_liability">SRL</parameter>
                <parameter key="sole_proprietorship">SP</parameter>
                <parameter key="joint_stock">SR</parameter>
                <parameter key="association">Asociación</parameter>
                <parameter key="association_apartment_house_owners">DNSB</parameter>
                <parameter key="residential_construction_association">AHB</parameter>
                <parameter key="representative_office">Oficina representativa</parameter>
                <parameter key="branch_legal_entity">Rama de una persona jurídica</parameter>
                <parameter key="cooperative_enterprise">Empresa cooperativa</parameter>
                <parameter key="credit_union">Unión de crédito</parameter>
                <parameter key="charitable_foundation">Fundación y organización caritativa</parameter>
                <parameter key="trade_union">Sindicato</parameter>
                <parameter key="municipal_budgetary_establishment">Establecimiento presupuestario municipal</parameter>
                <parameter key="community_gardeners">Comunidad de los jardineros</parameter>
                <parameter key="general_partnership">Sociedad General</parameter>
                <parameter key="traditional_religious_community">La comunidad religiosa tradicional o asociación</parameter>
                <parameter key="representative_office_foreign_company">Representación de una empresa extranjera</parameter>
                <parameter key="branch_foreign_company">Rama de una empresa extranjera</parameter>
                <parameter key="state_budgetary_establishment">Institución presupuestaria de estado</parameter>
                <parameter key="state_enterprise">EP</parameter>
                <parameter key="public_institution">EP</parameter>
                <parameter key="non_governmental_organization">NGO</parameter>
                <parameter key="agricultural_enterprise">AC</parameter>
                <parameter key="little_community">MC</parameter>
            </parameter>
        </parameter>
        <parameter type="collection" key="evp_client.sepa_legal_special_countries">
            <parameter>LT</parameter>
            <parameter>RO</parameter>
            <parameter>BG</parameter>
        </parameter>
    </parameters>

    <services>

        <service id="evp_client.form.transfer_service_agreemeent_type" class="Evp\Bundle\ClientBundle\Form\TransferServiceAgreementType">
            <tag name="form.type" alias="evp_client_transfer_service_agreement" />
        </service>

        <service id="evp_client.default_client_activator" class="Evp\Bundle\ClientBundle\Service\DefaultClientActivator">
            <tag name="monolog.logger" channel="evp_client.default_client_activator" />

            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_permission.permission_manager" />
            <argument type="service" id="evp_bank_permission.permission_entity_manager" />
            <argument type="service" id="evp_bank_account.local_account_provider" />
            <argument type="service" id="evp_bank_permission.provider.level_limits" />
            <argument type="service" id="evp_bank_permission.level_limits_helper" />
            <argument type="service" id="logger" />
            <argument type="service" id="evp_identification_level_common.limits_fixer"/>
        </service>

        <service id="evp_client.fixer.client_natural_account"
                 class="Evp\Bundle\ClientBundle\Service\Fixer\ClientNaturalAccountFixer">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_client.repository.client" />
            <argument type="service" id="evp_client.repository.client_natural" />
            <argument type="service" id="evp_client.default_client_activator" />
        </service>

        <service id="evp_client.filter_storage.session" class="Evp\Bundle\ClientBundle\Service\SessionFilterStorage">
            <argument type="service" id="session" />
            <argument type="service" id="doctrine.orm.entity_manager" />
        </service>

        <service id="evp_client.account_helper" class="Evp\Bundle\ClientBundle\Service\ClientAccountHelper">
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_client.form.limits_type" class="Evp\Bundle\ClientBundle\Form\Type\Client\LimitsType">
            <tag name="form.type" alias="evp_client_limits"/>
        </service>
        <service id="evp_client.form.new_permission_type" class="Evp\Bundle\ClientBundle\Form\Type\Client\NewPermissionType">
            <tag name="form.type" alias="evp_client_new_permission"/>
            <argument type="service" id="sonata.admin.manager.orm"/>
        </service>

        <service id="evp_client.user_integration" class="Evp\Bundle\ClientBundle\Service\UserIntegration">
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument>gateway</argument>
            <argument type="service" id="logger"/>
        </service>

        <!-- ClientInformationBuilder & steps -->
        <service id="evp_client.client_information.builder" class="Evp\Bundle\ClientBundle\Service\ClientInformationBuilder">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>

            <call method="addStep">
                <argument type="service" id="evp_client.client_information.step.attach_primary_payer_iban_list"/>
            </call>
            <call method="addStep">
                <argument type="service" id="evp_client.client_information.step.blacklists"/>
            </call>
            <call method="addStep">
                <argument type="service" id="evp_client.client_information.step.authorizations"/>
            </call>
        </service>
        <service id="evp_client.client_information.step.attach_primary_payer_iban_list"
                 class="Evp\Bundle\ClientBundle\Service\ClientInformationBuilder\Step\AttachPrimaryPayerIbanList"/>
        <service id="evp_client.client_information.step.blacklists"
                 class="Evp\Bundle\ClientBundle\Service\ClientInformationBuilder\Step\Blacklists">
            <argument type="service" id="evp_blacklist.manager"/>
            <argument type="service" id="evp_blacklist.repository.blacklist"/>
        </service>

        <service id="evp_client.client_information.step.authorizations"
                 class="Evp\Bundle\ClientBundle\Service\ClientInformationBuilder\Step\Authorizations">
            <argument type="service" id="evp_bank_permission.repository.sign_permission"/>
        </service>

        <service id="evp_client.restriction_limit_manager" class="Evp\Bundle\ClientBundle\Service\RestrictionLimitManager">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_client.repository.restriction_limit"/>
        </service>

        <service id="evp_client.transfer_restriction_manager" class="Evp\Bundle\ClientBundle\Service\TransferRestrictionManager">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_client.repository.transfer_restriction" />
        </service>

        <service id="evp_client.client_extractor" class="Evp\Bundle\ClientBundle\Service\ClientExtractor">
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="security.token_storage"/>
        </service>

        <service id="evp_client.transfer_service_agreement_builder"
                 class="Evp\Bundle\ClientBundle\Service\TransferServiceAgreementBuilder">
            <argument type="service" id="validator"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_client.client.comment"
                 class="Paysera\Client\CommentClient\CommentClient">
            <factory service="paysera.client.comment_client.client_factory" method="getCommentClient"/>
        </service>

        <service id="evp_client.client_restriction_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientRestrictionManager">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_client.repository.client_restriction"/>
            <argument type="service" id="evp_client.repository.client_restricted_actions"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service">
                <service class="Paysera\Client\BlacklistRestrictionClient">
                    <factory service="paysera.client.blacklist_restriction_client.client_factory" method="getBlacklistRestrictionClient"/>
                </service>
            </argument>
            <argument type="service" id="Evp\Bundle\ClientBundle\Service\CommonRestrictionFactory" />
            <argument type="service" id="evp_user_client.user_rest_factory"/>
            <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            <argument type="service" id="evp_client.client.comment"/>
            <argument type="service" id="logger" />
        </service>

        <service id="evp_client.client_iban_relation_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientIbanRelationManager">
            <argument type="service" id="evp_client.transfer_service_agreement_builder"/>
            <argument type="collection">
                <argument>**********************</argument><!-- Revolut -->
            </argument>
        </service>

        <service id="evp_client.user_address_provider"
                 class="Evp\Bundle\ClientBundle\Service\AddressProvider">
            <argument type="service" id="evp_sepa.user_client"/>
            <argument type="service" id="paysera.transfer_surveillance.client.user_address"/>
            <argument type="service" id="evp_commons.service.user_identifier_hash_generator"/>
            <argument type="service" id="evp_client.user_address_mapper"/>
        </service>

        <service id="evp_client.user_address_mapper" class="\Evp\Bundle\ClientBundle\Service\UserAddressMapper"/>

        <service id="evp_client.client_information.company_type_abbreviation_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientInformationBuilder\CompanyTypeAbbreviationProvider">
            <argument>%evp_client.client_company_type_abbreviations%</argument>
            <argument>%evp_client.default_client_company_type_country_code%</argument>
        </service>

        <service id="evp_client.user_information_provider"
                 class="Evp\Bundle\ClientBundle\Service\UserInformationProvider">
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="evp_client.user_information_normalizer"/>
        </service>

        <service id="evp_client.user_information_provider_with_cache"
                 class="Evp\Bundle\ClientBundle\Service\UserInformationProviderWithCache">
            <argument id="evp_client.user_information_provider" type="service"/>
        </service>

        <service id="evp_client.email_validator"
                 class="Evp\Bundle\ClientBundle\Service\EmailValidator">
            <argument id="evp_client.util.email_validator" type="service"/>
            <argument id="validator" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_client.util.email_validator"
                 class="Egulias\EmailValidator\EmailValidator"/>

        <service id="evp_client.system_username_exclude_user_id_provider"
                 class="Evp\Bundle\ClientBundle\Service\SystemUsernameExcludeUserIdProvider">
            <tag name="maba_gentle_force.identifier_provider" identifierType="system_username_exclude_user_id"/>
            <argument id="evp_client_rest.client_from_scopes_provider" type="service"/>
        </service>

        <service id="evp_client.client_activity_indicator" class="Evp\Bundle\ClientBundle\Service\ClientActivityIndicator">
            <argument id="evp_bank_account.repository.account" type="service"/>
            <argument id="evp_client.repository.client" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_client.client.user_country_resolver" class="Evp\Bundle\ClientBundle\Service\UserCountryResolver">
            <argument id="evp_questionnaire_service.repositories.questionnaire" type="service"/>
            <argument type="service" id="evp_client.user_address_provider"/>
            <argument type="service" id="evp_client.repository.client_natural"/>
            <argument type="service" id="evp_client.service.user_nationality_provider"/>
            <argument type="service" id="evp_bundle_client.service.client_tax_information_provider"/>
        </service>

        <service id="evp_client.client_legal_flags_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientLegalFlagsProvider">
            <argument id="evp_client.repository.client_legal_flags" type="service"/>
            <argument id="evp_client.user_information_provider_with_cache" type="service"/>
            <argument id="evp_client.client_legal_flags_mapper" type="service"/>
            <argument id="evp_rabbit_mq_extension.remote_job_publisher" type="service"/>
        </service>

        <service id="evp_client.client_legal_flags_mapper"
                 class="Evp\Bundle\ClientBundle\Service\ClientLegalFlagsMapper"/>

        <service id="evp_client.service.client_country_codes_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientCountryCodesProvider">
            <argument type="service">
                <service class="League\ISO3166\ISO3166"/>
            </argument>
        </service>

        <service id="evp_client.validator.email_constraint"
                 class="Evp\Bundle\ClientBundle\Validator\EmailConstraintValidator">
            <argument type="service" id="evp_client.email_validator" />
            <tag name="validator.constraint_validator" alias="evp_client.validator.email_constraint" />
        </service>

        <service id="evp_client.service.client_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientProvider">
            <argument type="service" id="evp_client.repository.client"/>
        </service>
        <service id="evp_client.service.user_nationality_provider"
                 class="Evp\Bundle\ClientBundle\Service\UserNationalityProvider">
            <argument type="service" id="evp_vmi_account_report.identification_client"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bundle_client.service.partner_client_manager"
                 class="Evp\Bundle\ClientBundle\Service\PartnerClientManager">
            <argument type="service" id="evp_client.repository.partner_client"/>
            <argument type="service" id="evp_bank_account.repository.account_partner"/>
            <argument type="service" id="evp_bank_account.account_owner_resolver"/>
            <argument type="service" id="paysera_partner.service.partner_data_provider"/>
            <argument type="service" id="paysera_partner_common.service.partner_resolver"/>
            <argument type="service" id="evp_accounting.accounting_api.accounting_api.client"/>
            <argument type="service" id="Paysera\Bundle\PartnerBundle\Service\CachedAccountingPartnerProvider"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bundle_client.service.licensed_partners_manager"
                 class="Evp\Bundle\ClientBundle\Service\LicensedPartnersManager">
            <argument type="service" id="evp_bank_account.repository.account_partner"/>
            <argument type="service" id="evp_client.repository.partner_client"/>
            <argument type="service" id="evp_client.repository.licensed_partner"/>
            <argument type="collection">
                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_KOSOVO</argument>
                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_ALBANIA</argument>
                <argument type="constant">Evp\Bundle\ClientBundle\Entity\LicensedPartner::PAYSERA_GEORGIA</argument>
            </argument>
        </service>

        <service id="evp_bundle_client.resolver.account_agreements"
                 class="Evp\Bundle\ClientBundle\Service\Remover\Resolver\AccountAgreementsResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\BankAccountBundle\Entity\Account"/>

            <argument type="service" id="evp_client.repository.transfer_service_agreement"/>
        </service>

        <service id="evp_bank_account.service_remover_resolver.partner_clients_resolver"
                 class="Evp\Bundle\ClientBundle\Service\Remover\Resolver\PartnerClientsResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\ClientBundle\Entity\Client"/>

            <argument type="service" id="evp_client.repository.partner_client"/>
        </service>

        <service id="evp_client.service.client_identification_document_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientIdentificationDocumentProvider">
            <argument type="service" id="evp_bank_transfer.static_cache"/>
            <argument type="service" id="evp_client.client.identification_request"/>
            <argument type="service" id="evp_identification_document.normalizer.identification_request_normalizer"/>
        </service>

        <service id="evp_client.service.client_natural_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientNaturalProvider">
            <argument type="service" id="evp_client.repository.client_natural"/>
        </service>

        <service id="evp_client.service.client_validation_helper"
                 class="Evp\Bundle\ClientBundle\Service\ClientValidationHelper">
        </service>

        <service id="evp_client.service.client_code_change_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientCodeChangeManager">
            <argument type="service" id="evp_client.repository.client_logs"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bundle_client.service.tax_residence_information_resolver"
                 class="Evp\Bundle\ClientBundle\Service\TaxResidenceInformationResolver\TaxResidenceInformationResolver">
            <argument type="service" id="evp_bundle_client.service.client_tax_information_provider"/>
        </service>

        <service id="evp_bundle_client.service.client_tax_information_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientTaxInformationProvider">
            <argument type="service" id="tax_information_client.client.rest_client"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_client.service.client_contact_information_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientContactInformationManager">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_client.repository.client_contact_information_phones"/>
            <argument type="service" id="evp_client.repository.client_contact_information_emails"/>
        </service>

        <service id="evp_client.service.client_contact_information_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientContactInformationProvider">
            <argument type="service" id="evp_client.repository.client_contact_information_phones"/>
            <argument type="service" id="evp_client.repository.client_contact_information_emails"/>
        </service>

        <service id="evp_client.service.licensed_partners_data_manager"
                 class="Evp\Bundle\ClientBundle\Service\LicensedPartnersDataManager">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="evp_client.repository.licensed_partner"/>
            <argument type="service" id="validator"/>
        </service>

        <service id="evp_client.service.client_identifier_configuration"
                 class="Evp\Bundle\ClientBundle\Service\ClientIdentifier\ClientIdentifierConfiguration">

            <call method="addResolver">
                <argument type="constant">Paysera\Bundle\PartnerCommonBundle\Partners::PAYSERA_GEORGIA</argument>
                <argument type="service">
                    <service class="Evp\Bundle\ClientBundle\Service\ClientIdentifier\ClientIdentifierResolverGE">
                        <argument type="service" id="evp_client.service.client_identification_document_provider"/>
                        <argument type="service" id="tax_information_client.client.rest_client"/>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_client.service.client_identifier_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientIdentifier\ClientIdentifierManager">
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_client.service.client_identifier_configuration"/>

        </service>
        <service id="evp_client.service.legal_special_country_client_detector"
                 class="Evp\Bundle\ClientBundle\Service\LegalSpecialCountryClientDetector">
            <argument>%evp_client.sepa_legal_special_countries%</argument>
        </service>

        <service id="evp_client.service.account_description_duplicator"
                 class="Evp\Bundle\ClientBundle\Service\AccountDescriptionDuplicator">
            <argument type="service" id="evp_bank_account.repository.account_description"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="Evp\Bundle\ClientBundle\Service\CommonRestrictionFactory" />

        <service id="Evp\Bundle\ClientBundle\Service\ClientManager" >
            <argument type="service" id="evp_client.repository.client" />
        </service>

        <service id="evp_client.service.client_information_provider"
                 class="Evp\Bundle\ClientBundle\Service\ClientInformationProvider">
            <argument type="service" id="evp_questionnaire_service.repositories.questionnaire"/>
        </service>

        <service id="evp_bundle_client.service.partners_isolation_resolver"
                 class="Evp\Bundle\ClientBundle\Service\PartnersIsolationResolver">

            <call method="addStrategy">
                <argument type="service" id="evp_bank_transfer.partner_isolation.strategy.transfer_internal"/>
            </call>

            <call method="addStrategy">
                <argument type="service" id="evp_bank_transfer.partner_isolation.strategy.transfer_out"/>
            </call>

            <call method="addStrategy">
                <argument type="service" id="evp_bank_transfer.partner_isolation.strategy.transfer_in"/>
            </call>

        </service>

        <service id="evp_bundle_client.service.partners_isolation_manager"
                 class="Evp\Bundle\ClientBundle\Service\PartnersIsolationManager">
            <argument type="service" id="evp_bundle_client.service.licensed_partners_manager"/>
            <argument type="service" id="evp_client.repository.licensed_partner"/>
            <argument>%evp_client.partner_integrations%</argument>
            <argument>%evp_client.partner_iban_countries%</argument>
        </service>

        <service id="evp_bundle_client.user_notification_client"
                 class="Paysera\Client\UserNotificationClient\UserNotificationClient">
            <factory service="paysera.client.user_notification_client.client_factory"
                     method="getUserNotificationClient"/>
        </service>

        <service id="evp_bundle_client.service.partner_isolation_notification_builder"
                 class="Evp\Bundle\ClientBundle\Service\PartnerIsolationNotificationBuilder">
            <argument>partner_isolation_enabled</argument>
            <argument>partner_isolation_disabled</argument>
        </service>

        <service id="Evp\Bundle\ClientBundle\Service\RequestAwareUserInformationProvider">
            <argument type="service" id="evp_client.normalizer.client_request_normalizer"/>
            <argument type="service" id="request_stack"/>
        </service>
    </services>
</container>
