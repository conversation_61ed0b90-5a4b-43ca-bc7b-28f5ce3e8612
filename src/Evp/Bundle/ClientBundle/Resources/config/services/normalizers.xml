<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="evp_client.normalizer.client"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientNormalizer">
            <argument type="service" id="evp_client.service.client_validation_helper"/>
        </service>

        <service id="evp_client.normalizer.client_result" parent="paysera_rest.normalizer.result">
            <argument>clients</argument>
            <argument type="service" id="evp_client.normalizer.client"/>
        </service>

        <service id="evp_client.normalizer.restriction_limit"
                 class="Evp\Bundle\ClientBundle\Normalizer\RestrictionLimitNormalizer"/>

        <service id="evp_client.normalizer.transfer_restriction"
                 class="Evp\Bundle\ClientBundle\Normalizer\TransferRestrictionNormalizer"/>

        <service id="evp_client.normalizer.restriction_limit_result"
                 class="Paysera\Component\Serializer\Normalizer\ResultNormalizer">
            <argument>restriction_limits</argument>
            <argument type="service" id="evp_client.normalizer.restriction_limit"/>

            <call method="setMetadataNormalizer">
                <argument type="service" id="evp_gateway.normalizer.result_metadata"/>
            </call>
        </service>

        <service id="evp_client.user_information_normalizer" parent="evp_user.normalizer.user">
            <tag name="remote_event_denormalizer" object_type="user.user"/>
        </service>

        <service id="evp_client.normalizer.client_restriction"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientRestrictionNormalizer"/>

        <service id="evp_client.normalizer.partner_client"
                 class="Evp\Bundle\ClientBundle\Normalizer\PartnerClientNormalizer"/>

        <service id="evp_client.normalizer.client_legal_normalizer"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientLegalNormalizer">
            <argument type="service" id="evp_client.service.client_validation_helper"/>
            <tag name="evp_client.normalizer.client_type_normalizer" type="legal" />
        </service>

        <service id="evp_client.normalizer.client_natural_normalizer"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientNaturalNormalizer">
            <tag name="evp_client.normalizer.client_type_normalizer" type="natural" />
        </service>

        <service id="evp_client.normalizer.client_request_normalizer"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientRequestNormalizer">
            <argument type="service" id="evp_client.normalizer.client"/>
            <argument type="service" id="evp_client.normalizer.client_legal_flags_request"/>
        </service>

        <service id="evp_client.normalizer.user_contact_information_normalizer"
                 class="Paysera\Component\Serializer\Normalizer\PlainNormalizer">
            <tag name="remote_event_denormalizer" object_type="user.user_contact_information"/>
        </service>

        <service id="evp_client.normalizer.licensed_partner"
                 class="Evp\Bundle\ClientBundle\Normalizer\LicensedPartnerNormalizer"/>

        <service id="evp_client.normalizer.client_legal_flags_request"
                 class="Evp\Bundle\ClientBundle\Normalizer\ClientLegalFlagsRequestNormalizer"/>
    </services>
</container>
