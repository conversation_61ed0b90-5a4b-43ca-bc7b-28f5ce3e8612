<?xml version="1.0" encoding="utf-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity repository-class="Evp\Bundle\ClientBundle\Repository\LicensedPartnerRepository"
            name="Evp\Bundle\ClientBundle\Entity\LicensedPartner"
            table="licensed_partners">

        <id name="id" type="integer" column="id">
            <generator strategy="AUTO"/>
        </id>

        <field name="title" type="string" column="title" length="100" nullable="false"/>
        <field name="partnerCode" type="string" column="partner_code" length="100" nullable="false" unique="true"/>
        <field name="officialName" type="string" column="official_name" length="255" nullable="false"/>
        <field name="officialAddress" type="string" column="official_address" length="255" nullable="false"/>
        <field name="createdAt" type="datetime_immutable" column="created_at"/>
        <field name="updatedAt" type="datetime_immutable" column="updated_at"/>
        <field name="isolated" type="boolean" />
        <field name="bankIdentificationCode" type="string" column="bank_identification_code" length="20" nullable="true"/>
    </entity>
</doctrine-mapping>
