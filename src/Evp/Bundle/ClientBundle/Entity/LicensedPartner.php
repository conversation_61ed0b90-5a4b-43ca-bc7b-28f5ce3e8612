<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Entity;

use DateTimeImmutable;

class LicensedPartner
{
    public const PAYSERA_ALBANIA = 'paysera_al';
    public const PAYSERA_GEORGIA = 'paysera_ge';
    public const PAYSERA_KOSOVO = 'paysera_xk';
    public const PAYSERA_LITHUANIA = 'paysera_lt';

    private int $id;
    private string $title;
    private string $partnerCode;
    private string $officialName;
    private string $officialAddress;
    private DateTimeImmutable $createdAt;
    private DateTimeImmutable $updatedAt;
    private bool $isolated;
    private string $bankIdentificationCode;

    public function __construct()
    {
        $this->createdAt = new DateTimeImmutable();
        $this->updatedAt = new DateTimeImmutable();
        $this->isolated = false;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getPartnerCode(): string
    {
        return $this->partnerCode;
    }

    public function setPartnerCode(string $partnerCode): self
    {
        $this->partnerCode = $partnerCode;

        return $this;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(DateTimeImmutable $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function __toString(): string
    {
        return $this->title;
    }

    public function isIsolated(): bool
    {
        return $this->isolated;
    }

    public function setIsolated(bool $isolated): self
    {
        $this->isolated = $isolated;

        return $this;
    }

    public function getOfficialName(): string
    {
        return $this->officialName;
    }

    public function setOfficialName(string $officialName): self
    {
        $this->officialName = $officialName;

        return $this;
    }

    public function getOfficialAddress(): string
    {
        return $this->officialAddress;
    }

    public function setOfficialAddress(string $officialAddress): self
    {
        $this->officialAddress = $officialAddress;

        return $this;
    }

    public function getBankIdentificationCode(): string
    {
        return $this->bankIdentificationCode;
    }

    public function setBankIdentificationCode(string $bankIdentificationCode): self
    {
        $this->bankIdentificationCode = $bankIdentificationCode;

        return $this;
    }
}
