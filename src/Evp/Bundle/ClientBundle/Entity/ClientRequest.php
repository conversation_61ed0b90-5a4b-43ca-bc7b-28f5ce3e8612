<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Entity;

class ClientRequest
{
    private ?Client $client;
    private bool $isAccountActive;
    private ?ClientLegalFlagsRequest $clientLegalFlagsRequest;

    public function __construct()
    {
        $this->isAccountActive = true;
        $this->clientLegalFlagsRequest = null;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    public function isAccountActive(): bool
    {
        return $this->isAccountActive;
    }

    public function setIsAccountActive(bool $isAccountActive): self
    {
        $this->isAccountActive = $isAccountActive;

        return $this;
    }

    public function getClientLegalFlagsRequest(): ?ClientLegalFlagsRequest
    {
        return $this->clientLegalFlagsRequest;
    }

    public function setClientLegalFlagsRequest(ClientLegalFlagsRequest $clientLegalFlagsRequest): self
    {
        $this->clientLegalFlagsRequest = $clientLegalFlagsRequest;

        return $this;
    }
}
