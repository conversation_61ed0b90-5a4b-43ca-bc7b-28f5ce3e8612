<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Entity;

class ClientLegalFlagsRequest
{
    private bool $isPsp;
    private bool $isOffshore;

    public function __construct(bool $isPsp, bool $isOffshore)
    {
        $this->isPsp = $isPsp;
        $this->isOffshore = $isOffshore;
    }

    public function isPsp(): bool
    {
        return $this->isPsp;
    }

    public function isOffshore(): bool
    {
        return $this->isOffshore;
    }
}
