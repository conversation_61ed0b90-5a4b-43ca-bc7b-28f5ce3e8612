<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Normalizer;

use Evp\Bundle\ClientBundle\Entity\ClientLegalFlagsRequest;
use InvalidArgumentException;
use Paysera\Component\Serializer\Normalizer\DenormalizerInterface;

class ClientLegalFlagsRequestNormalizer implements DenormalizerInterface
{
    public function mapToEntity($data): ClientLegalFlagsRequest
    {
        return new ClientLegalFlagsRequest(
            $this->getRequiredBoolean($data, 'payment_service_provider'),
            $this->getRequiredBoolean($data, 'located_in_offshore')
        );
    }

    private function getRequiredBoolean(array $data, string $key): bool
    {
        if (!isset($data[$key])) {
            throw new InvalidArgumentException(sprintf('Argument %s is required', $key));
        }

        if (!is_bool($data[$key])) {
            throw new InvalidArgumentException(sprintf('Argument %s is not bool', $key));
        }

        return $data[$key];
    }
}
