<?php

declare(strict_types=1);

namespace Evp\Bundle\ClientBundle\Normalizer;

use Payser<PERSON>\Component\Serializer\Normalizer\DenormalizerInterface;
use Paysera\Component\Serializer\Exception\InvalidDataException;
use Evp\Bundle\ClientBundle\Entity\ClientRequest;

class ClientRequestNormalizer implements DenormalizerInterface
{
    private DenormalizerInterface $clientNormalizer;
    private DenormalizerInterface $clientLegalFlagsRequestNormalizer;

    public function __construct(
        DenormalizerInterface $clientNormalizer,
        DenormalizerInterface $clientLegalFlagsRequestNormalizer
    ) {
        $this->clientNormalizer = $clientNormalizer;
        $this->clientLegalFlagsRequestNormalizer = $clientLegalFlagsRequestNormalizer;
    }

    public function mapToEntity($data): ClientRequest
    {
        $clientRequest = new ClientRequest();

        if (isset($data['client'])) {
            $clientRequest->setClient($this->clientNormalizer->mapToEntity($data['client']));
        }

        if (isset($data['client_legal_flags'])) {
            $clientRequest->setClientLegalFlagsRequest(
                $this->clientLegalFlagsRequestNormalizer->mapToEntity($data['client_legal_flags'])
            );
        }

        return $clientRequest->setIsAccountActive($this->getRequiredBoolean($data, 'is_account_active'));
    }

    private function getRequiredBoolean(array $data, string $key): bool
    {
        if (!isset($data[$key])) {
            throw new InvalidDataException(sprintf('Argument %s is required', $key));
        }

        if (!is_bool($data[$key])) {
            throw new InvalidDataException(sprintf('Argument %s type invalid', $key));
        }

        return $data[$key];
    }
}
