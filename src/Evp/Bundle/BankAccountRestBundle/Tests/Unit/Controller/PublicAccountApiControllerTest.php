<?php

namespace Evp\Bundle\BankAccountRestBundle\Tests\Unit\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\AccountAlias;
use Evp\Bundle\BankAccountBundle\Repository\AccountRepository;
use Evp\Bundle\BankPermissionBundle\Entity\Permission\SignPermission;
use Evp\Bundle\BankPermissionBundle\Repository\PermissionRepository;
use Evp\Bundle\IdentificationLevelCommonBundle\IdentificationLevels;
use Evp\Component\Money\Money;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\IdentificationDocument\IdentityReportClient;
use Evp\Component\UserRestClient\User\UserClient;
use Evp\Component\UserRestClient\UserRestFactory;
use Paysera\Bundle\JWTAuthenticationBundle\Authentication\Token;
use Paysera\Bundle\SecurityBundle\Entity\AccessedBy;
use Paysera\Bundle\SecurityBundle\Entity\Context;
use Paysera\Component\AppScopes;
use Paysera\Component\Tests\Fixtures\FixturesHelper;
use Paysera\Component\Tests\TestCase\RestTestCase;
use Symfony\Bundle\FrameworkBundle\Client;
use Symfony\Component\Routing\Router;

class PublicAccountApiControllerTest extends RestTestCase
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var Router
     */
    protected $router;

    /**
     * @var EntityManagerInterface
     */
    protected $entityManager;

    /**
     * @var FixturesHelper
     */
    protected $fixturesHelper;

    /**
     * @var AccountRepository
     */
    private $accountsRepository;

    /**
     * @var PermissionRepository
     */
    private $permissionRepository;


    public function setUp(): void
    {
        $this->client = $this->createClientWithNewDatabase(__DIR__ . '/../../DataFixtures');
        $this->router = $this->client->getContainer()->get('router');
        $this->entityManager = $this->getEntityManager();
        $this->accountsRepository = $this->getContainer()->get('evp_bank_account.repository.account');
        $this->fixturesHelper = new FixturesHelper($this->entityManager);
        $this->permissionRepository = $this->getContainer()->get('evp_bank_permission.repository.permission');
        $this->mockUserRestFactory();
    }

    /**
     * @param array $filterData
     * @param int|null $authorizedUserId
     * @param array $expectedAccounts
     * @param int $expectedStatusCode
     * @dataProvider providerTestGetAccounts
     */
    public function testGetAccountDescriptionsByFilter(
        $filterData,
        $authorizedUserId,
        $expectedAccounts,
        $expectedStatusCode
    ) {
        if ($authorizedUserId !== null) {
            $this->mockTokenAsAuthenticated($authorizedUserId);
        }

        $uri = $this->router->generate('evp_bank_account_rest.public.v1.get_accounts');

        $this->client->request('GET', $uri, $filterData, [], []);

        if ($expectedStatusCode === 200) {
            $response = json_decode($this->client->getResponse()->getContent(), true);
            $this->assertArrayHasKey('items', $response);
            foreach ($response['items'] as &$account) {
                if (array_key_exists('created_at', $account)) {
                    $account['created_at'] = null;
                }
            }
            $this->assertEquals($expectedAccounts, $response['items']);
        }

        $this->assertEquals($expectedStatusCode, $this->client->getResponse()->getStatusCode());
    }

    public function testGetAccountAliases()
    {
        $account = $this->mockAccount();
        $this->mockTokenAsAuthenticated($account->getClient()->getCovenanteeId());
        $accountAlias = $this->addAliasToAccount($account);

        $this->client->request(
            'GET',
            $this->router->generate(
                'evp_bank_account_rest.public.v1.get_account_aliases',
                [
                    'accountNumber' => $account->getNumber(),
                ]
            )
        );
        $response = $this->client->getResponse();

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertStringContainsString($accountAlias->getIdentifier(), $response->getContent());
    }

    public function testSignLimitsWhenCreatingAccountShouldNotBeNull()
    {
        $this->mockTokenAsAuthenticated(4);
        $uri = $this->router->generate(
            'evp_bank_account_rest.public.v1.create_account',
            ['covenanteeId' => 4]
        );
        $this->client->request(
            'POST',
            $uri
        );
        $response = json_decode($this->client->getResponse()->getContent(), true);
        $account = $this->accountsRepository->findOneByNumber($response['number']);
        /** @var SignPermission[] $signPermission */
        $signPermission = $this->permissionRepository->findActiveByAccountAndType($account, SignPermission::class);
        self::assertCount(1, $signPermission);
        self::assertTrue($signPermission[0]->getDayLimit()->isEqual(new Money('1200.00', 'EUR')));
        self::assertTrue($signPermission[0]->getMonthLimit()->isEqual(new Money('3000.00', 'EUR')));
        self::assertTrue($signPermission[0]->getYearLimit()->isEqual(new Money('36000.00', 'EUR')));
    }

    public function providerTestGetAccounts()
    {
        return [
            [
                ['owned_by_user_id' => 1],
                1,
                [
                    [
                        'number' => 'EVP1',
                        'owner_id' => 1,
                        'created_at' => null,
                        'active' => true,
                        'closed' => false,
                        'type' => 'local',
                        'iban_list' => ['LT551', 'RO0321'],
                        'flags' => [
                            'savings' => false,
                            'public' => false,
                        ],
                    ]
                ],
                200
            ],
            [
                ['owned_by_user_id' => 1],
                2,
                [],
                200
            ],
            [
                [
                    'offset' => 2,
                    'owned_by_user_id' => 2
                ],
                2,
                [],
                200
            ],
            [
                ['owned_by_user_id' => 9],
                null,
                [],
                200
            ],
            [
                ['owned_by_user_id' => 9],
                9,
                [],
                200
            ],
            [
                [
                    'owned_by_user_id' => 1,
                    'account_numbers' => ['EVP2'],
                ],
                1,
                [],
                200
            ],
            [
                [
                    'owned_by_user_id' => 1,
                    'type' => 'random'
                ],
                1,
                [],
                200
            ],
            [
                [
                    'owned_by_user_id' => 1,
                    'active' => true,
                    'closed' => false
                ],
                1,
                [
                    [
                        'number' => 'EVP1',
                        'owner_id' => 1,
                        'created_at' => null,
                        'active' => true,
                        'closed' => false,
                        'type' => 'local',
                        'iban_list' => ['LT551', 'RO0321'],
                        'flags' => [
                            'savings' => false,
                            'public' => false,
                        ],
                    ]
                ],
                200
            ],
        ];
    }

    /**
     * @param int $authorizedUserId
     */
    private function mockTokenAsAuthenticated($authorizedUserId)
    {
        $token = new Token([AppScopes::LOGGED_IN, AppScopes::CONFIRMED_LOG_IN], new Context(), new AccessedBy(
            [
                'user_id' => $authorizedUserId,
            ]
        ), 'sessionId');
        $token->setAuthenticated(true);
        $this->getContainer()->get('security.token_storage')->setToken($token);
    }

    private function mockAccount(): Account
    {
        $client = $this->fixturesHelper->createClientNatural(789456, IdentificationLevels::IDENTIFIED);
        $account = $this->fixturesHelper->createAccount($client);
        $this->entityManager->flush();

        return $account;
    }

    private function addAliasToAccount(Account $account): AccountAlias
    {
        $accountAlias = $this->fixturesHelper->createAccountAlias($account);
        $this->entityManager->flush();

        return $accountAlias;
    }

    private function mockUserRestFactory(): void
    {
        $userClientMock = $this->createMock(UserClient::class);
        $userClientMock->method('getUser')->willReturn(new UserInformation());

        $identityReportClientMock = $this->createMock(IdentityReportClient::class);

        $userRestFactoryMock = $this->createMock(UserRestFactory::class);
        $userRestFactoryMock->method('userClient')->willReturn($userClientMock);
        $userRestFactoryMock->method('identityReportClient')->willReturn($identityReportClientMock);


        $this->client->getContainer()->set('evp_user_client.user_rest_factory', $userRestFactoryMock);
    }
}
