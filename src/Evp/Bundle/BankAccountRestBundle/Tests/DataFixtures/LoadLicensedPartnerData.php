<?php

declare(strict_types=1);

namespace Evp\Bundle\BankAccountRestBundle\Tests\DataFixtures;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use Evp\Bundle\ClientBundle\Entity\LicensedPartner;

class LoadLicensedPartnerData extends AbstractFixture implements OrderedFixtureInterface
{
    public function load(ObjectManager $manager)
    {
        $partner1 = new LicensedPartner();
        $partner1->setTitle('Paysera Lithuania');
        $partner1->setPartnerCode(LicensedPartner::PAYSERA_LITHUANIA);
        $partner1->setOfficialName('Paysera Lithuania');
        $partner1->setOfficialAddress('some address');
        $partner1->setBankIdentificationCode('EVIULT20XXX');
        $manager->persist($partner1);

        $partner2 = new LicensedPartner();
        $partner2->setTitle('Paysera Albania');
        $partner2->setPartnerCode(LicensedPartner::PAYSERA_ALBANIA);
        $partner2->setOfficialName('Paysera Albania');
        $partner2->setOfficialAddress('some address');
        $partner2->setBankIdentificationCode('PYALALT2XXX');
        $manager->persist($partner2);

        $partner3 = new LicensedPartner();
        $partner3->setTitle('Paysera Georgia');
        $partner3->setPartnerCode(LicensedPartner::PAYSERA_GEORGIA);
        $partner3->setOfficialName('Paysera Georgia');
        $partner3->setOfficialAddress('some address');
        $partner3->setBankIdentificationCode('PSRAGE220XXX');
        $manager->persist($partner3);

        $partner4 = new LicensedPartner();
        $partner4->setTitle('Paysera Kosovo');
        $partner4->setPartnerCode(LicensedPartner::PAYSERA_KOSOVO);
        $partner4->setOfficialName('Paysera Kosovo');
        $partner4->setOfficialAddress('some address');
        $partner4->setBankIdentificationCode('PAYKXKP20XXX');
        $manager->persist($partner4);

        $manager->flush();

        $this->addReference(LicensedPartner::PAYSERA_LITHUANIA, $partner1);
        $this->addReference(LicensedPartner::PAYSERA_ALBANIA, $partner2);
        $this->addReference(LicensedPartner::PAYSERA_GEORGIA, $partner3);
        $this->addReference(LicensedPartner::PAYSERA_KOSOVO, $partner4);
    }

    public function getOrder(): int
    {
        return 80;
    }
}
