<?php

declare(strict_types=1);

namespace Evp\Bundle\BankAccountBundle\Tests\Service;

use DateTime;
use Doctrine\ORM\AbstractQuery;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\StatementRepository;
use Evp\Bundle\BankAccountBundle\Service\AccountBalanceDataProvider;
use Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider;
use Evp\Component\Doctrine\Connection\TransactionAwareConnection;
use Evp\Component\GatewayCommon\BankAccount\Entity\AccountBalance;
use Evp\Component\GatewayCommon\BankAccount\Entity\Balance;
use Evp\Component\Money\Money;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class AccountBalanceDataProviderTest extends TestCase
{
    private AccountBalanceDataProvider $accountBalanceDataProvider;
    private MockObject $statementRepository;
    private MockObject $slaveConnection;
    private MockObject $query;
    private MockObject $account;
    private MockObject $date;
    private MockObject $accountTypeAtDateProvider;

    protected function setUp(): void
    {
        $this->statementRepository = $this->createMock(StatementRepository::class);
        $this->slaveConnection = $this->createMock(TransactionAwareConnection::class);
        $this->accountTypeAtDateProvider = $this->createMock(AccountTypeAtDateProvider::class);
        $this->accountBalanceDataProvider = new AccountBalanceDataProvider(
            $this->slaveConnection,
            $this->statementRepository,
            $this->accountTypeAtDateProvider
        );
        $this->query = $this->createMock(AbstractQuery::class);
        $this->query
            ->method('getParameters')
            ->withAnyParameters()
            ->willReturn([])
        ;
        $this->query
            ->method('getSQL')
            ->willReturn('')
        ;
        $this->account = $this->createMock(Account::class);
        $this->date = $this->createMock(DateTime::class);
    }

    public function testFindAccountBalanceForDate(): void
    {
        $this->statementRepository
            ->method('getFindAccountBalanceForDateQuery')
            ->withAnyParameters()
            ->willReturn($this->query)
        ;
        $sqlResult = [
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 100,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'EUR'
            ],
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 200,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'USD'
            ],
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 300,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'GBP'
            ],
        ];
        $this->slaveConnection
            ->method('fetchAll')
            ->withAnyParameters()
            ->willReturn($sqlResult)
        ;

        $expected = [
            'EUR' => new Money(100, 'EUR'),
            'USD' => new Money(200, 'USD'),
            'GBP' => new Money(300, 'GBP'),
        ];
        $this->assertEquals(
            $expected,
            $this->accountBalanceDataProvider->findAccountBalanceForDate($this->account, $this->date),
        );
    }

    public function testGetLocalAccountBalanceForDate(): void
    {
        $this->accountTypeAtDateProvider
            ->method('getAccountTypeAtDate')
            ->withAnyParameters()
            ->willReturnOnConsecutiveCalls('local')
        ;

        $this->statementRepository
            ->method('getFindAccountBalanceForDateQuery')
            ->withAnyParameters()
            ->willReturn($this->query)
        ;
        $sqlResult = [
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 100,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'EUR'
            ],
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 200,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'USD'
            ],
            [
                AccountBalanceDataProvider::RESULT_KEY_AMOUNT => 300,
                AccountBalanceDataProvider::RESULT_KEY_CURRENCY => 'GBP'
            ],
        ];
        $this->slaveConnection
            ->method('fetchAll')
            ->withAnyParameters()
            ->willReturn($sqlResult)
        ;

        $accountNumber = 'EVP1012947652';
        $this->account
            ->method('getNumber')
            ->withAnyParameters()
            ->willReturn($accountNumber)
        ;

        $expected = (new AccountBalance())
            ->setAccountNumber($accountNumber)
            ->setBalance(
                (new Balance())
                    ->setMain(new Money(100, 'EUR'))
                    ->setMain(new Money(200, 'USD'))
                    ->setMain(new Money(300, 'GBP'))
            )
        ;
        $this->assertEquals(
            $expected,
            $this->accountBalanceDataProvider->getLocalAccountBalanceForDate($this->account, $this->date)
        );
    }

    public function testFindAccountBalanceForDateWithEmptyResult(): void
    {
        $expected = [];
        $this->statementRepository
            ->method('getFindAccountBalanceForDateQuery')
            ->withAnyParameters()
            ->willReturn(null)
        ;

        $this->assertEquals(
            $expected,
            $this->accountBalanceDataProvider->findAccountBalanceForDate($this->account, $this->date)
        );
    }
}
