<?php

declare(strict_types=1);

namespace Evp\Bundle\BankAccountBundle\Tests\Functional\Admin;

use Application\Sonata\UserBundle\Entity\User;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\Admin\ClearingAccountAdmin;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\AccountDescription;
use Evp\Bundle\BankAccountBundle\Entity\ClearingAccount;
use Evp\Bundle\BankAccountBundle\Tests\Functional\DataFixtures\LoadData;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Repository\ClientRepository;
use Evp\Bundle\TestCaseBundle\Context\Functional\Database\PersistableWebTestCase;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\IdentificationDocument\IdentityReportClient;
use Evp\Component\UserRestClient\User\UserClient;
use Evp\Component\UserRestClient\UserRestFactory;
use Symfony\Bundle\FrameworkBundle\Client as KernelBrowserClient;
use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class ClearingAccountAdminTest extends PersistableWebTestCase
{
    private const PATH_CLEARING_ACCOUNT_LIST = '/admin/evp/bankaccount/clearingaccount/list';
    private const PATH_CLEARING_ACCOUNT_CREATE = '/admin/evp/bankaccount/clearingaccount/create';

    private ?EntityManager $entityManager;
    private ?ClearingAccountAdmin $admin;
    private ?ClientRepository $clientRepository;
    private ?KernelBrowserClient $kernelBrowserClient;
    private ?UserClient $userClientMock;
    private ?UserRestFactory $userRestFactoryMock;

    protected function setUp(): void
    {
        $this->userClientMock = $this->createMock(UserClient::class);
        $this->userRestFactoryMock = $this->createMock(UserRestFactory::class);

        $this->getContainer()->set('evp_sepa.user_client', $this->userClientMock);
        $this->getContainer()->set('evp_user_client.user_rest_factory', $this->userRestFactoryMock);

        $this->entityManager = $this->getContainer()->get('doctrine.orm.default_entity_manager');

        $this->admin = $this->getContainer()->get('evp_bank_account.admin.clearing_account');

        $this->accountRepository = $this->getContainer()->get('evp_bank_account.repository.account');
        $this->clientRepository = $this->getContainer()->get('evp_client.repository.client');

        $this->kernelBrowserClient = $this->createClientWithNewDatabase(__DIR__ . '/../DataFixtures');
    }

    /**
     * @dataProvider prePersistProvider
     */
    public function testPrePersist(int $covenanteeId, string $descriptionText): void
    {
        $description = new AccountDescription();
        $description->setDescription($descriptionText);

        $this->userClientMock->method('getUser')->willReturn(new UserInformation());
        $this->userRestFactoryMock->method('userClient')->willReturn($this->userClientMock);
        $this->userRestFactoryMock->method('identityReportClient')->willReturn($this->createMock(IdentityReportClient::class));

        /** @var Client $client */
        $client = $this->clientRepository->findOneBy(['covenanteeId' => $covenanteeId]);
        $clearingAccount = (new ClearingAccount())
            ->setClient($client)
            ->setDescription($description)
        ;

        $this->admin->prePersist($clearingAccount);
        $result = $clearingAccount->getAccount();

        self::assertNotNull($result);
        self::assertEquals(Account::TYPE_CLEARING, $result->getType());
        self::assertEquals($client, $result->getClient());
    }

    public function prePersistProvider(): array
    {
        return [
            'Legal client' => [
                'covenanteeId' => LoadData::COVENANTEE_ID_LEGAL_CLIENT,
                'descriptionText' => 'Account Legal',
            ],
            'Natural client' => [
                'covenanteeId' => LoadData::COVENANTEE_ID_NATURAL_CLIENT,
                'descriptionText' => 'Account Natural',
            ],
        ];
    }

    public function testListingNotLoggedIn(): void
    {
        $response = $this->requestClearingAccountListing(null);

        self::assertTrue($response->isRedirect());
    }

    /**
     * @dataProvider listingNtEnoughRightsProvider
     *
     * @param string[] $roles
     * @return void
    */
    public function testListingNotEnoughRights(array $roles): void
    {
        $response = $this->requestClearingAccountListing($roles);

        self::assertTrue($response->isForbidden());
    }

    public function listingNtEnoughRightsProvider(): array
    {
        return [
            'User Role used' => [
                'roles' => [
                    'ROLE_USER',
                ],
            ],
            'Unknown Role used' => [
                'roles' => [
                    'UNKNOWN_ROLE',
                ],
            ],
        ];
    }

    public function testListing(): void
    {
        $response = $this->requestClearingAccountListing(
            [
                'ROLE_ADMIN',
                'ROLE_EVP_BANK_ACCOUNT_ADMIN_CLEARING_ACCOUNT_LIST',
            ]
        );

        self::assertTrue($response->isSuccessful());
    }

    public function testCreateNotLoggedIn(): void
    {
        $response = $this->requestClearingAccountCreate(null);

        self::assertTrue($response->isRedirect());
    }

    /**
     * @dataProvider createNotEnoughRightsProvider
     *
     * @param string[] $roles
     * @return void
     */
    public function testCreateNotEnoughRights(array $roles): void
    {
        $response = $this->requestClearingAccountCreate(
            [
                'ROLE_ADMIN',
            ]
        );

        self::assertTrue($response->isForbidden());
    }

    public function createNotEnoughRightsProvider(): array
    {
        return [
            'User Role Admin' => [
                'roles' => [
                    'ROLE_ADMIN',
                ],
            ],
            'Unknown Role used' => [
                'roles' => [
                    'UNKNOWN_ROLE',
                ],
            ],
        ];
    }

    public function testCreate(): void
    {
        $response = $this->requestClearingAccountCreate(
            [
                'ROLE_ADMIN',
                'ROLE_EVP_BANK_ACCOUNT_ADMIN_CLEARING_ACCOUNT_CREATE',
            ]
        );

        self::assertTrue($response->isSuccessful());
    }

    /**
     * @param string[] $roles
     * @return void
     */
    private function logIn(array $roles): void
    {
        $user = (new User())
            ->setUsername('test')
            ->setEmail('<EMAIL>')
            ->setPassword('test')
        ;

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $token = new UsernamePasswordToken($user, null, 'main', $roles);

        $session = $this->client->getContainer()->get('session');
        $session->set('_security_main', serialize($token));
        $session->save();

        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

    /**
     * @param string[]|null $roles
     * @param string $path
     * @return Response
     */
    private function requestWithRoles(?array $roles, string $path): Response
    {
        if ($roles !== null) {
            $this->logIn($roles);
        }

        $this->kernelBrowserClient->request('GET', $path);

        return $this->kernelBrowserClient->getResponse();
    }

    /**
     * @param string[]|null $roles
     * @return Response
     */
    private function requestClearingAccountListing(?array $roles): Response
    {
        return $this->requestWithRoles($roles, self::PATH_CLEARING_ACCOUNT_LIST);
    }

    /**
     * @param string[]|null $roles
     * @return Response
     */
    private function requestClearingAccountCreate(?array $roles): Response
    {
        return $this->requestWithRoles($roles, self::PATH_CLEARING_ACCOUNT_CREATE);
    }
}
