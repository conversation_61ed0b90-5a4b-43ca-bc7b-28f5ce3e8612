<?php

namespace Evp\Bundle\BankAccountBundle\Entity\Event;

use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Component\UserCommon\Entity\UserInformation;
use Symfony\Component\EventDispatcher\Event;

class AccountEvent extends Event
{
    private Account $account;

    private ?UserInformation $userInformation;

    public function __construct(Account $account, UserInformation $userInformation = null)
    {
        $this->account = $account;
        $this->userInformation = $userInformation;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;

        return $this;
    }

    public function getUserInformation(): ?UserInformation
    {
        return $this->userInformation;
    }
}
