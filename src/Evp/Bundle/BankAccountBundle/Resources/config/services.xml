<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <imports>
        <import resource="services/*.xml"/>
    </imports>

    <services>
        <!-- Service -->
        <service id="evp_bank_account.account_manager" class="%evp_bank_account.account_manager.class%">
            <tag name="monolog.logger" channel="evp_bank_account.account_manager" />

            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_account.generic_account_manager" class="Evp\Bundle\BankAccountBundle\Service\GenericAccountManager">
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_account.account_balance_manager" />
            <argument type="service" id="evp_bank_charge.charge_helper"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_user_client.user_rest_factory"/>
        </service>

        <service id="evp_bank_account.full_account_balance_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\FullAccountBalanceManager">
            <argument type="service" id="evp_bank_account.account_balance_manager"/>
            <argument type="service" id="evp_bank_charge.repository.charge"/>
            <argument type="service" id="evp_bank_refund.repository.refund"/>
            <argument type="service" id="evp_bank_refund.refund_helper"/>
        </service>

        <service id="evp_bank_account.full_account_balance_manager.internal"
                 parent="evp_bank_account.full_account_balance_manager">
            <argument index="0" type="service" id="evp_bank_account.account_balance_manager.internal"/>
        </service>

        <service id="evp_bank_account.account_balance_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountBalanceManager"/>

        <service id="evp_bank_account.account_balance_manager.internal"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountBalanceManager">
            <argument type="constant">Evp\Bundle\BankAccountBundle\Service\AccountBalanceManager::USAGE_INTERNAL</argument>
        </service>

        <service id="evp_bank_account.local_account_balance_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\LocalAccountBalanceProvider">
            <tag name="evp_bank_account.account_balance_provider" key="local" />
            <tag name="evp_bank_account.account_balance_provider" key="technical"/>
            <tag name="evp_bank_account.account_balance_provider" key="transit"/>
            <tag name="evp_bank_account.account_balance_provider" key="clearing"/>
        </service>

        <service id="evp_bank_account.client_balance_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\ClientBalanceProvider">
            <argument id="evp_bank_account.account_balance_manager" type="service"/>
        </service>

        <service id="evp_bank_account.statement_order_id_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementOrderIdManager">
            <argument id="evp_bank_transfer.repository.bank_transfer_data" type="service"/>
        </service>

        <service id="evp_bank_account.statement_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementManager">
        </service>

        <service id="evp_bank_account.local_statement_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\LocalStatementProvider">
            <tag name="evp_bank_account.statement_provider" key="local"/>
            <tag name="evp_bank_account.statement_provider" key="contis"/>
            <tag name="evp_bank_account.statement_provider" key="technical"/>
            <tag name="evp_bank_account.statement_provider" key="transit"/>
            <tag name="evp_bank_account.statement_provider" key="airwallex"/>
            <tag name="evp_bank_account.statement_provider" key="clearing"/>

            <argument type="service" id="evp_bank_account.repository.statement" />
        </service>

        <service id="evp_bank_account.local_account_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\LocalAccountProvider">
            <tag name="evp_bank_account.account_provider" key="local" />
            <tag name="evp_bank_account.account_provider" key="technical" />
            <tag name="evp_bank_account.account_provider" key="transit" />
            <tag name="evp_bank_account.account_provider" key="clearing" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.internal_account_manager" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_account.generic_account_manager" />
            <argument type="service" id="evp_bank_account.internal_account_transfer_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_account.internal_account_manager" class="%evp_bank_account.internal_account_manager.class%">
            <tag name="monolog.logger" channel="evp_bank_account.internal_account_manager" />

            <argument type="service" id="evp_bank_account.service.negative_balance_availability_resolver" />
            <argument type="service" id="evp_bank_account.current_total_balance_amount_provider" />
            <argument type="service" id="evp_currency.currency_manager" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_account.credit_manager" class="Evp\Bundle\BankAccountBundle\Service\CreditManager">
            <argument type="service" id="evp_currency.currency_converter.official" />
            <argument type="service" id="evp_bank_account.account_balance_manager.internal" />
            <argument type="service" id="evp_currency.provider.currency" />
        </service>

        <service id="evp_bank_account.operation_manager" class="%evp_bank_account.operation_manager.class%">
            <tag name="monolog.logger" channel="evp_bank_account.operation_manager" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.statement.assembler_resolver" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_currency.currency_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="evp_bank_account.statement.assembler_resolver" class="%evp_bank_account.statement.assembler_resolver.class%">
            <tag name="monolog.logger" channel="evp_bank_account.statement.assembler_resolver" />

            <argument type="service" id="logger" />

            <call method="setContainer">
                <argument type="service" id="service_container" />
            </call>

            <call method="registerStatementAssembler">
                <argument type="string">Evp\Bundle\BankAccountBundle\Entity\Operation\CurrencyConvertOperation</argument>
                <argument type="string">evp_bank_account.statement.assembler.currency_convert</argument>
            </call>
        </service>

        <service id="evp_bank_account.statement_assembler.base"
                 class="Evp\Bundle\BankAccountBundle\Service\Operation\Statement\BaseStatementAssembler">
            <argument type="service" id="evp_bank_transfer.additional_information_manager"/>
            <argument type="service" id="evp_bank_transfer.client_identifier_manager"/>
        </service>

        <service id="evp_bank_account.statement.assembler.currency_convert" class="Evp\Bundle\BankAccountBundle\Service\Operation\Statement\CurrencyConvertStatementAssembler">
            <argument type="service" id="translator" />
            <argument type="service" id="paysera_partner.service.partner_statement_party_provider" />
        </service>

        <service id="evp_bank_account.account_number_generator"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator">
            <argument>%evp_bank_account.account_number_prefix.local%</argument>
            <argument>***********</argument>
        </service>

        <service id="evp_bank_account.account_number_generator.iban"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountNumberGenerator">
            <argument>%evp_bank_account.account_number_prefix.iban%</argument>
            <argument>35000***********</argument>
        </service>

        <service id="evp_bank_account.available_iban_storage"
                 class="Evp\Bundle\BankAccountBundle\Service\AvailableIbanStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">evp_bank:available_lt_ibans:list</argument>
            <argument type="string">evp_bank:available_lt_ibans:last_id</argument>
        </service>

        <service id="evp_bank_account.account_currency_conversion_solver"
            class="Evp\Bundle\BankAccountBundle\Service\AccountCurrencyConversionSolver">
            <tag name="monolog.logger" channel="evp_bank_account.account_currency_conversion_solver" />

            <argument type="service" id="evp_currency.currency_conversion_manager" />
            <argument type="service" id="evp_bank_account.account_balance_manager.internal" />
            <argument type="service" id="evp_bank_account.credit_manager" />
            <argument type="service" id="evp_currency.currency_manager" />
            <argument type="service" id="logger" />
            <argument>%evp_bank_account.account_currency_conversion_solver.currency%</argument>
        </service>

        <service id="evp_bank_account.multiple_account_currency_conversion_solver"
            class="Evp\Bundle\BankAccountBundle\Service\MultipleAccountCurrencyConversionSolver">

            <argument type="service" id="evp_bank_account.account_currency_conversion_solver" />
        </service>

        <service id="evp_bank_account.account_currency_conversion_solver.local"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountCurrencyConversionSolver">
            <tag name="monolog.logger" channel="evp_bank_account.account_currency_conversion_solver.local" />

            <argument type="service" id="evp_currency.currency_conversion_manager" />
            <argument type="service" id="evp_bank_account.local_account_balance_provider" />
            <argument type="service" id="evp_bank_account.credit_manager" />
            <argument type="service" id="evp_currency.currency_manager" />
            <argument type="service" id="logger" />
            <argument>%evp_bank_account.account_currency_conversion_solver.currency%</argument>
        </service>

        <service id="evp_bank_account.account_alias_facade"
            class="Evp\Bundle\BankAccountBundle\Service\AccountAliasFacade">
            <tag name="monolog.logger" channel="evp_bank_account.account_alias_facade" />

            <argument type="service" id="evp_bank_account.account_alias_manager" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="evp_bank_account.account_description_manager" />
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_account.repository.account_alias"/>
            <argument type="service" id="logger" />
        </service>
        <service id="evp_bank_account.account_description_manager"
            class="Evp\Bundle\BankAccountBundle\Service\AccountDescriptionManager">
            <tag name="monolog.logger" channel="evp_bank_account.account_description_manager" />

            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="validator" />
            <argument type="service" id="translator" />
            <argument type="service" id="logger" />
        </service>
        <service id="evp_bank_account.account_alias_manager"
            class="Evp\Bundle\BankAccountBundle\Service\AccountAliasManager">
            <tag name="monolog.logger" channel="evp_bank_account.account_alias_manager" />

            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="doctrine.orm.entity_manager" />
            <argument type="service" id="validator" />
            <argument type="service" id="evp_currency.currency_converter.official" />
            <argument type="service" id="evp_bank_account.repository.account_alias"/>
            <argument type="service" id="evp_bank_account.account_facade"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer_internal"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.account_iban.manager.iban"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountIbanManager">
            <argument type="service" id="evp_bank_account.account_iban.registry.eligibility_checker"/>
            <argument type="service" id="evp_bank_account.account_iban.registry.issuer"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="event_dispatcher" />
        </service>

        <service id="evp_bank_account.account_iban.registry.eligibility_checker"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanIssuer\EligibilityCheckerRegistry">
            <tag name="monolog.logger" channel="account_iban"/>

            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.account_iban.eligibility_checker"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanIssuer\EligibilityChecker">
            <tag name="evp_bank_account.account_iban.iban_eligibility_checker"
                 partnerCode="%main_partner_code%"/>
            <tag name="evp_bank_account.account_iban.iban_eligibility_checker" partnerCode="paysera_al"/>
            <tag name="evp_bank_account.account_iban.iban_eligibility_checker" partnerCode="paysera_xk"/>
            <tag name="evp_bank_account.account_iban.iban_eligibility_checker" partnerCode="paysera_bg"/>
            <tag name="evp_bank_account.account_iban.iban_eligibility_checker"
                 partnerCode="%paysera_georgia_iban.client_partner_code%"/>

            <argument type="service" id="evp_identification_level_common.level_hierarchy"/>
            <argument type="service" id="evp_bundle_client.service.partner_client_manager"/>
            <argument type="service" id="evp_client.repository.licensed_partner"/>
            <argument type="collection">
                <argument>%main_partner_code%</argument>
                <argument>paysera_al</argument>
                <argument>paysera_xk</argument>
                <argument>paysera_bg</argument>
                <argument>%paysera_georgia_iban.client_partner_code%</argument>
            </argument>
        </service>

        <service id="evp_bank_account.account_iban.registry.issuer"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanIssuer\AccountIbanIssuerRegistry">
            <tag name="monolog.logger" channel="account_iban"/>

            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.account_iban.issuer"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanIssuer\AccountIbanIssuer">
            <tag name="evp_bank_account.account_iban.iban_issuer" partnerCode="%main_partner_code%"/>
            <tag name="evp_bank_account.account_iban.iban_issuer" partnerCode="paysera_al"/>
            <tag name="evp_bank_account.account_iban.iban_issuer" partnerCode="paysera_xk"/>
            <tag name="evp_bank_account.account_iban.iban_issuer" partnerCode="paysera_bg"/>
            <tag name="evp_bank_account.account_iban.iban_issuer" partnerCode="%paysera_georgia_iban.client_partner_code%"/>

            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_account.account_number_generator.iban"/>
        </service>

         <!--Admin-->
        <service id="evp_bank_account.admin.account" class="%evp_bank_account.admin.account.class%">
            <tag name="sonata.admin" manager_type="orm" group="%evp_bank_account.admin.groupname%" label="bank_account" show_in_dashboard="false" />

            <argument />
            <argument>%evp_bank_account.admin.account.entity%</argument>
            <argument />

            <call method="setAccountRepository">
                <argument type="service" id="evp_bank_account.repository.account"/>
            </call>
            <call method="setTranslationDomain">
                <argument>%evp_bank_account.admin.account.translation_domain%</argument>
            </call>
        </service>

        <!--Admin-->
        <service id="evp_bank_account.admin.transit_account"
                 class="Evp\Bundle\BankAccountBundle\Admin\TransitAccountAdmin">
            <tag name="sonata.admin"
                 manager_type="orm"
                 group="Special accounts"
                 label="Transit accounts" />

            <argument />
            <argument>Evp\Bundle\BankAccountBundle\Entity\TransitAccount</argument>
            <argument />

            <call method="setTransitAccountRepository">
                <argument type="service" id="evp_bank_account.repository.transit_account"/>
            </call>
            <call method="setGenericAccountManager">
                <argument type="service" id="evp_bank_account.generic_account_manager"/>
            </call>
            <call method="setClientRepository">
                <argument type="service" id="evp_client.repository.client"/>
            </call>
            <call method="setOwnerClientIdForTransitAccounts">
                <argument>%paysera_client_id%</argument>
            </call>
            <call method="setTranslationDomain">
                <argument>%evp_bank_account.admin.account.translation_domain%</argument>
            </call>
        </service>

        <service id="evp_bank_account.admin.clearing_account"
                 class="Evp\Bundle\BankAccountBundle\Admin\ClearingAccountAdmin">
            <tag name="sonata.admin"
                 manager_type="orm"
                 group="Special accounts"
                 label="Clearing accounts" />

            <argument />
            <argument>Evp\Bundle\BankAccountBundle\Entity\ClearingAccount</argument>
            <argument>Evp\Bundle\BankAccountBundle\Controller\ClearingAccountAdminController</argument>

            <call method="setGenericAccountManager">
                <argument type="service" id="evp_bank_account.generic_account_manager"/>
            </call>

            <call method="setCovenanteeIdProvider">
                <argument type="service" id="paysera_partner.partner_covenantee_id_provider"/>
            </call>

            <call method="setClientRepository">
                <argument type="service" id="evp_client.repository.client"/>
            </call>

            <call method="setTemplate">
                <argument>edit</argument>
                <argument>EvpBankAccountBundle:Admin/Forms:clearing_account_edit.html.twig</argument>
            </call>
        </service>

        <service id="app.admin.account_description"
                 class="Evp\Bundle\BankAccountBundle\Admin\AccountDescriptionAdmin">
            <tag name="sonata.admin"
                 manager_type="orm"
                 show_in_dashboard="false"
                 label="Account Description" />

            <argument/>
            <argument>Evp\Bundle\BankAccountBundle\Entity\AccountDescription</argument>
            <argument/>
        </service>

        <service id="evp_bank_account.account_event_subscriber" class="Evp\Bundle\BankAccountBundle\Event\AccountEventSubscriber">
            <tag name="doctrine.event_subscriber" />

            <argument type="service" id="evp_bank_account.account_number_generator" />
        </service>

        <!-- validators -->
        <service id="evp_bank_account.validator.client_alias_limit_validator"
            class="Evp\Bundle\BankAccountBundle\Validator\ClientAliasLimitValidator">
            <tag name="validator.constraint_validator" alias="evp_client_alias_limit_validator" />

            <argument type="service" id="doctrine.orm.default_entity_manager" />
        </service>

        <service id="evp_bank_account.validator.account_alias_validator"
                 class="Evp\Bundle\BankAccountBundle\Validator\AccountAliasValidator">
            <tag name="validator.constraint_validator" alias="evp_account_alias_validator" />

            <argument type="service" id="evp_bank_account.account_alias_manager" />
        </service>

        <service id="client_bundle.client_activity_manager"
                 class="Evp\Bundle\ClientBundle\Service\ClientActivityManager">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.account_alias_facade" />
            <argument type="service" id="evp_bank_account.generic_account_manager" />
            <argument type="service" id="paysera_client_monthly_charge_fee.handler.client_monthly_charge_fee" />
        </service>

        <service id="evp_bank_account.reservation_statement_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\ReservationStatementManager">
        </service>

        <service id="evp_bank_account.account_facade" class="Evp\Bundle\BankAccountBundle\Service\AccountFacade">
            <tag name="monolog.logger" channel="evp_bank_account.account_facade"/>

            <argument type="service" id="evp_bank_api.facade_repository"/>
            <argument type="service" id="evp_bank_transfer.turnover_calculator"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.credit_manager"/>
            <argument type="service" id="evp_bank_account.account_balance_manager"/>
            <argument type="service" id="evp_bank_account.account_activator"/>
            <argument type="service" id="evp_bank_permission.repository.permission"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="logger"/>
        </service>

        <service class="Evp\Bundle\BankAccountBundle\Service\AccountActivator"
                 id="evp_bank_account.account_activator">
            <argument id="event_dispatcher" type="service"/>
            <argument id="evp_bank_account.full_account_balance_manager" type="service"/>
            <argument id="evp_debtor.account_debt_checker" type="service"/>
            <argument id="evp_contis.repository.card" type="service"/>
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="Evp\Bundle\BankAccountBundle\Service\LegalClientAccountDecisionManager" type="service"/>
        </service>

        <service id="evp_bank_account.account_lock" class="Evp\Bundle\BankAccountBundle\Service\AccountLock">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.account_lock.context"/>
            <argument type="service" id="logger"/>
            <argument type="service" id="paysera.context_container"/>
        </service>

        <service id="evp_bank_account.currency_finalizer.account_balance" class="Evp\Bundle\BankAccountBundle\Service\CurrencyFinalizer\Finalizers\AccountBalanceFinalizer">
            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.repository.currency_account"/>
            <argument type="service" id="evp_bank_transfer.transfer_without_client_processor.instant.currency_finalization" />
            <argument type="service" id="evp_bank_charge.charge_factory"/>
            <argument type="service" id="evp_bank_charge.processor.charge"/>
            <argument type="service" id="translator" />
            <argument type="service" id="evp_currency.currency_renewer.finalization" />
            <argument type="service" id="logger"/>
            <argument type="service" id="evp_bank_account.internal_account_manager" />
            <argument type="service" id="evp_bank_account.account_balance_manager" />
            <argument type="service" id="evp_bank_account.repository.temporary_reservation_conversion"/>
        </service>

        <service id="evp_bank_account.currency_finalizer.pending_transfers" class="Evp\Bundle\BankAccountBundle\Service\CurrencyFinalizer\Finalizers\PendingTransfersFinalizer">
            <argument type="service" id="evp_bank_transfer.transfer_currency_renewer" />
            <argument type="service" id="doctrine.orm.default_entity_manager" />

            <call method="addTransferRepository">
                <argument type="service" id="evp_bank_transfer.repository.transfer_internal"/>
            </call>
        </service>

        <service id="evp_bank_account.currency_finalizers_chain" class="Evp\Bundle\BankAccountBundle\Service\CurrencyFinalizer\CurrencyFinalizerChained">
            <call method="addFinalizer">
                <argument type="service" id="evp_bank_account.currency_finalizer.pending_transfers"/>
                <argument type="string">pending_transfers</argument>
            </call>
            <call method="addFinalizer">
                <argument type="service" id="evp_bank_hold.currency_finalizer.holds"/>
                <argument type="string">holds</argument>
            </call>
            <call method="addFinalizer">
                <argument type="service" id="evp_bank_account.currency_finalizer.account_balance"/>
                <argument type="string">account_balance</argument>
            </call>
            <call method="addFinalizer">
                <argument type="service" id="evp_bank_charge.charge_finalizer"/>
                <argument type="string">charges</argument>
            </call>
        </service>

        <service id="evp_bank_account.commission_account_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\CommissionAccountResolver" />

        <service id="evp_bank_account.filter.translation_safe"
                 class="Evp\Component\TextFilter\TranslationSafeFilter" />

        <service id="evp_bank_account.current_total_balance_amount_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\CurrentTotalBalanceAmountProvider">
            <argument type="service" id="doctrine.dbal.slave_gateway_connection_connection"/>
            <argument type="collection">
                <argument>2</argument>
                <argument>685391</argument>
            </argument>
        </service>

        <service id="evp_bank_account.statement_render.pdf"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementRender\PdfStatementRender">
            <argument type="service" id="knp_snappy.pdf"/>
            <argument type="service" id="evp_signer.pdf_signer"/>
            <argument type="service" id="evp_bank_account.statement_render.html" />
            <argument type="service" id="translator"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.statement_render.html"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementRender\HtmlStatementRender" lazy="true">
            <argument type="service" id="templating"/>
            <argument type="service" id="evp_rest_user_api_client"/>
            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
            <argument type="service" id="evp_bank_account.filter.translation_safe"/>
            <argument type="service" id="translator" />
            <argument>http://www.paysera.lt/assets/image/logo/224_51_white.png</argument>
            <argument type="collection">
                <argument>%evp_sepa.bank_key%</argument>
                <argument>%evp_sepa.bank_key_transfers_from_paysera_account%</argument>
            </argument>
        </service>

        <service id="evp_bank_account.twig_extension.datetime_format"
                 class="Evp\Bundle\BankAccountBundle\Twig\DateTimeFormatExtension">
            <tag name="twig.extension" />
        </service>

        <service id="evp_bank_account.conversion_provider.local_account"
                 class="Evp\Bundle\BankAccountBundle\Service\LocalAccountConversionProvider">
            <tag name="evp_currency.currency_conversion_provider" key="local"/>
            <tag name="evp_currency.currency_conversion_provider" key="technical"/>
            <tag name="evp_currency.currency_conversion_provider" key="transit"/>
            <tag name="evp_currency.currency_conversion_provider" key="clearing"/>

            <argument type="service" id="evp_bank_account.internal_account_manager"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.statement_turnover_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementTurnoverProvider">
            <argument type="service" id="evp_currency.currency_converter.official.cached" />
            <argument type="service" id="evp_bank_account.repository.statement"/>
            <argument type="service" id="paysera.component.doctrine.slave_entity_manager_factory"/>
        </service>

        <service id="evp_bank_account.role_voter.account"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\AccountRoleVoter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_bank_account_rest.restriction_checker"/>
        </service>

        <service id="evp_bank_account.scope_voter.account_filter"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\AccountFilterScopeVoter"
                 parent="paysera_security.security.context_aware_scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_bank_account.role_voter.account_filter"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\AccountFilterRoleVoter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_bank_account.role_voter.client"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\ClientRoleVoter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
        </service>

        <service id="evp_bank_account.scope_voter.account"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\AccountScopeVoter"
                 parent="paysera_security.security.context_aware_scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_identification_level_common.level_hierarchy"/>
        </service>

        <service id="evp_bank_account.scope_voter.client"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\ClientScopeVoter"
                 parent="paysera_security.security.context_aware_scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="evp_client.client_extractor"/>
        </service>

        <service id="evp_bank_account.scope_voter.client_deprecated"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\DeprecatedClientScopeVoter"
                 parent="paysera_security.security.scope_voter"
                 public="false">
            <tag name="security.voter"/>

            <argument type="service" id="evp_client.client_extractor"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.scope_voter.account_deprecated"
                 class="Evp\Bundle\BankAccountBundle\Service\Voter\DeprecatedAccountScopeVoter"
                 parent="paysera_security.security.scope_voter"
                 public="false">
            <tag name="security.voter"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.provider.filter_aware_account"
                 class="Evp\Bundle\BankAccountBundle\Service\FilterAwareAccountProvider">

            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_permission.repository.permission"/>
            <argument type="service" id="evp_bank_permission.repository.administrate_permission"/>
            <argument type="service" id="evp_client.repository.client"/>
            <argument type="service" id="evp_bank_permission.permission_manager"/>
            <argument type="service" id="paysera_partner.isolation_service"/>
        </service>

        <service class="Evp\Bundle\BankAccountBundle\Service\AccountTypeIntervalsProvider"
                 id="evp_bank_account.service.account_type_intervals_provider">
            <argument type="service" id="evp_bank_account.repository.account_type_change"/>
        </service>

        <service class="Evp\Bundle\BankAccountBundle\Service\AccountTypeAtDateProvider"
                 id="evp_bank_account.service.account_type_at_date_provider">
            <argument id="evp_bank_account.repository.account_type_change" type="service"/>
        </service>

        <service class="Evp\Bundle\BankAccountBundle\Service\InternalAccountDebtProvider"
                 id="evp_bank_account.service.internal_account_debt_provider">
            <argument id="evp_bank_account.repository.internal_account.main" type="service"/>
            <argument id="evp_bank_account.credit_manager" type="service"/>
            <argument id="evp_debtor.debt_factory" type="service"/>
            <argument id="evp_rabbit_mq_extension.deferred_remote_job_publisher" type="service"/>
            <argument id="evp_contis.repository.contis_account" type="service"/>
        </service>

        <service id="evp_bank_account.result_provider.statement" parent="paysera_rest.result_provider">
            <argument type="service" id="evp_bank_account.repository.statement"/>
        </service>

        <!-- addProvider is called for each service tagged with "evp_bank_account.account_income_handler" -->
        <service id="evp_bank_account.service.account_income_manager" class="Evp\Bundle\BankAccountBundle\Service\AccountIncomeManager">
            <argument type="service" id="evp_bank_account.repository.currency_account"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="service" id="evp_bank_account.account_lock"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.service.account_by_type_filter"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountByTypeFilter">
            <argument id="evp_bank_account.service.account_type_at_date_provider" type="service"/>
        </service>

        <service id="evp_bank_account.internal_account_transfer_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\InternalAccountTransferManager">
            <argument id="evp_bank_account.internal_account_manager" type="service"/>
            <argument id="event_dispatcher" type="service"/>
            <argument id="logger" type="service"/>
        </service>

        <service id="evp_bank_account.account_count_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountCountManager">
            <argument id="evp_bank_account.repository.account" type="service"/>
            <argument>%evp_bank_account_rest.max_active_accounts%</argument>
            <argument>%evp_bank_account_rest.max_accounts%</argument>
        </service>

        <service id="postbank_bg_iban.client.client_factory"
                 class="Paysera\Client\PostbankBgIbanClient\ClientFactory">
            <argument type="collection">
                <argument key="base_url">%env(REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_BASE_URL)%</argument>
                <argument key="mac" type="collection">
                    <argument key="mac_id">%env(REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_USERNAME)%</argument>
                    <argument key="mac_secret">%env(REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD)%</argument>
                </argument>
            </argument>
        </service>

        <service id="postbank_bg_iban.client.rest_client"
                 class="Paysera\Client\PostbankBgIbanClient\PostbankBgIbanClient">
            <factory service="postbank_bg_iban.client.client_factory" method="getPostbankBgIbanClient" />
        </service>

        <service id="evp_bank_account.doctrine_query_helper"
                class="Evp\Component\Doctrine\DoctrineQueryHelper">
            <argument>30</argument>
        </service>

        <service id="evp_bank_account.transit_account_client_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\TransitAccountClientResolver">
            <argument id="evp_bank_account.repository.transit_account" type="service"/>
        </service>

        <service id="evp_bank_account.account_owner_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountOwnerResolver">
            <argument id="evp_bank_account.transit_account_client_resolver" type="service"/>
            <argument type="service" id="evp_client.repository.merged_client"/>
        </service>

        <service id="evp_bank_account.account_type_change_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountTypeChangeManager">
            <argument id="doctrine.orm.default_entity_manager" type="service"/>
            <argument id="event_dispatcher" type="service"/>
            <argument id="evp_client.repository.client" type="service"/>
            <argument id="evp_accounting.accounting_soap_client" type="service" />
            <argument id="evp_accounting.accounting_operation_builder" type="service"/>
            <argument id="paysera_statement_turnover.balance_helper" type="service"/>
            <argument id="paysera_partner.partner_covenantee_id_provider" type="service"/>
            <argument>%paysera_client_id%</argument>
            <argument>TRANSFER_TO_TRANSIT_ACCOUNT</argument>
        </service>

        <service id="evp_bank_account.account_provider" class="Evp\Bundle\BankAccountBundle\Service\AccountProvider">
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <!-- LEVEL 1 -->
        <service id="evp_bank_account.3_or_4_unique_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\UniqueCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">3_or_4_unique</argument>
                            <argument>1</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>3</argument>
                            <argument>4</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.8_or_more_repeat_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">8_or_more_repeat</argument>
                            <argument>1</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>8</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.7_or_more_sequence_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\SequenceCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">7_or_more_sequence</argument>
                            <argument>1</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>7</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>

        <!-- LEVEL 2 -->
        <service id="evp_bank_account.5_or_6_repeat_excluding_first_three_zeros_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">5_or_6_repeat_excluding_first_three_zeros</argument>
                            <argument>2</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>5</argument>
                            <argument>6</argument>
                        </service>
                    </argument>
                    <argument>1</argument>
                    <argument>/000/</argument>
                </service>
            </argument>
        </service>

        <!-- LEVEL 3 -->
        <service id="evp_bank_account.4_or_more_two_digits_repeat_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">4_or_more_two_digits_repeat</argument>
                            <argument>3</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>4</argument>
                        </service>
                    </argument>
                    <argument>2</argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.3_or_more_three_digits_repeat_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">3_or_more_three_digits_repeat</argument>
                            <argument>3</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>3</argument>
                        </service>
                    </argument>
                    <argument>3</argument>
                </service>
            </argument>
        </service>

        <!-- LEVEL 4 -->
        <service id="evp_bank_account.5_unique_incremental_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\UniqueCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">5_unique_incremental</argument>
                            <argument>4</argument>
                            <argument>true</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>5</argument>
                            <argument>5</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.5_or_6_sequence_incremental_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\SequenceCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">5_or_6_sequence_incremental</argument>
                            <argument>4</argument>
                            <argument>true</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>5</argument>
                            <argument>6</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.5_or_6_repeat_incremental_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">5_or_6_repeat_incremental</argument>
                            <argument>4</argument>
                            <argument>true</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>5</argument>
                            <argument>6</argument>
                        </service>
                    </argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.3_or_more_repeat_two_digits_incremental_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">3_or_more_repeat_two_digits_incremental</argument>
                            <argument>4</argument>
                            <argument>true</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>3</argument>
                        </service>
                    </argument>
                    <argument>2</argument>
                </service>
            </argument>
        </service>
        <service id="evp_bank_account.2_or_more_repeat_four_digits_incremental_counter"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanLevel\RepeatCounter">
            <argument type="service">
                <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\CountingRule">
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\BeautyLevel">
                            <argument type="string">2_or_more_repeat_four_digits_incremental</argument>
                            <argument>4</argument>
                            <argument>true</argument>
                        </service>
                    </argument>
                    <argument type="service">
                        <service class="Evp\Bundle\BankAccountBundle\Entity\IbanLevel\IntRange">
                            <argument>2</argument>
                        </service>
                    </argument>
                    <argument>4</argument>
                </service>
            </argument>
        </service>

        <service id="evp_bank_account.iban_beauty_level_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\IbanBeautyLevelResolver">
            <argument type="service" id="logger"/>

            <call method="addCounter">
                <argument type="service" id="evp_bank_account.3_or_4_unique_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.8_or_more_repeat_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.7_or_more_sequence_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.5_or_6_repeat_excluding_first_three_zeros_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.4_or_more_two_digits_repeat_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.3_or_more_three_digits_repeat_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.5_unique_incremental_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.5_or_6_sequence_incremental_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.5_or_6_repeat_incremental_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.3_or_more_repeat_two_digits_incremental_counter"/>
            </call>
            <call method="addCounter">
                <argument type="service" id="evp_bank_account.2_or_more_repeat_four_digits_incremental_counter"/>
            </call>
        </service>

        <service id="evp_bank_account.service.account_balance_data_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountBalanceDataProvider">
            <argument type="service" id="doctrine.dbal.slave_gateway_connection_connection"/>
            <argument type="service" id="evp_bank_account.repository.statement"/>
            <argument type="service" id="evp_bank_account.service.account_type_at_date_provider"/>
        </service>

        <service id="evp_bank_account.resolver.client_accounts"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Resolver\ClientAccountsResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\ClientBundle\Entity\Client"/>

            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_bank_account.resolver.account_aliases"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Resolver\AccountAliasesResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\BankAccountBundle\Entity\Account"/>

            <argument type="service" id="evp_bank_account.repository.account_alias"/>
        </service>

        <service id="evp_bank_account.resolver.account_partners"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Resolver\AccountPartnersResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\BankAccountBundle\Entity\Account"/>

            <argument type="service" id="evp_bank_account.repository.account_partner"/>
        </service>

        <service id="evp_bank_account.resolver.client_contact_information_email"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Resolver\ClientContactInformationEmailResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\ClientBundle\Entity\Client"/>

            <argument type="service" id="evp_client.repository.client_contact_information_emails"/>
        </service>

        <service id="evp_bank_account.resolver.client_contact_information_phone"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Resolver\ClientContactInformationPhoneResolver">
            <tag name="evp.entity_removal_resolver" class="Evp\Bundle\ClientBundle\Entity\Client"/>

            <argument type="service" id="evp_client.repository.client_contact_information_phones"/>
        </service>

        <service id="evp_bank_account.service.migration_feature_flag_storage"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountingMigrationFeatureFlagStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">accounting_migration_feature_flag</argument>
        </service>

        <service id="evp_bank_account.service.transfer_event_storage_flag_storage"
                 class="Evp\Bundle\BankAccountBundle\Service\TransferEventStorageFlagStorage">
            <argument type="service" id="snc_redis.default"/>
            <argument type="string">transfer_event_storage_flag</argument>
        </service>

        <service id="evp_bank_account.service.negative_balance_availability_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\NegativeBalanceAvailabilityResolver"/>

        <service id="evp_bank_account.service.credit_availability_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\CreditAvailabilityResolver">
            <tag name="negative_balance_availability_resolver"/>

            <argument type="service" id="evp_bank_account.credit_manager"/>
        </service>

        <service id="evp_bank_account.service.reservation_statement_mapper"
                 class="Evp\Bundle\BankAccountBundle\Service\ReservationStatementMapper">

            <argument type="service" id="evp_bank_account.statement_assembler.base" />
            <argument type="service" id="evp_contis.repository.contis_account" />
            <argument type="service" id="translator"/>
        </service>

        <service id="evp_bank_account.service.statement_money_movement_handler"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementMoneyMovementHandler">
            <argument type="service" id="paysera_partner.repository.statement_transaction_map"/>
            <argument type="service" id="paysera_statement_turnover.repository.statement_turnover"/>
            <argument type="service" id="paysera_partner.internal_account_balance_mover"/>
            <argument type="service" id="evp_bank_account.internal_account_manager"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="evp_rabbit_mq_extension.deferred_remote_job_publisher"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp_bank_account.service.account_number_resolver"
                 class="Evp\Bundle\BankAccountBundle\Service\AccountNumberResolver">
            <argument type="service" id="evp_bundle_bank_transfer.service.iban_aliases_account_number_manager"/>
            <argument type="service" id="evp_bank_account.repository.account"/>
        </service>

        <service id="evp_bank_account.service.clearing_account_feature_flag"
                 class="Evp\Bundle\BankAccountBundle\Service\ClearingAccountFeatureFlag">
            <argument type="string">%kernel.environment%</argument>
            <argument>%evp_bank_account.feature_flag.allowed_environments%</argument>
            <argument>%evp_bank_account.feature_flag.clearing_account_allowed_users%</argument>
        </service>

        <service id="evp_bank_account.service.client_accounts_restrictor"
                 class="Evp\Bundle\BankAccountBundle\Service\Remover\Restrictor\ClientAccountsRestrictor">
            <tag name="evp.entity_removal_restrictor" class="Evp\Bundle\BankAccountBundle\Entity\Account"/>

            <argument type="service" id="evp_bank_transfer.repository.transfer"/>
        </service>

        <service id="Evp\Bundle\BankAccountBundle\Service\LegalClientAccountDecisionManager">
            <argument type="service" id="evp_questionnaire_service.questionnaire" />
        </service>

        <service id="evp_bank_account.service.statement_party_manager"
                 class="Evp\Bundle\BankAccountBundle\Service\StatementPartyManager">
            <argument type="service" id="evp_bank_account.repository.account"/>
            <argument type="string">%evp_bank_account.account_number_prefix.local%</argument>
        </service>

        <service id="evp_bank_account.form.account_credit_limit_type"
                 class="Evp\Bundle\BankAccountBundle\Form\AccountCreditLimitType">
            <tag name="form.type" alias="evp_bank_account_credit_limit"/>
        </service>

        <service id="evp_bank_account.form.account_with_credit_limit_type"
                 class="Evp\Bundle\BankAccountBundle\Form\AccountWithCreditLimitType">
            <tag name="form.type" alias="evp_bank_account_with_credit_limit"/>
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
        </service>

        <service id="evp_bank_account.end_day_account_balance_provider"
                 class="Evp\Bundle\BankAccountBundle\Service\EndDayAccountBalanceProvider">
            <argument type="service" id="evp_bank_account.repository.currency_account_end_day_balance_repository"/>
        </service>

    </services>
</container>
