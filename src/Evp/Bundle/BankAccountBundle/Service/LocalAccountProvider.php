<?php

namespace Evp\Bundle\BankAccountBundle\Service;

use Doctrine\ORM\EntityManagerInterface;
use Evp\Bundle\BankAccountBundle\AccountEvents;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction;
use Evp\Bundle\BankAccountBundle\Entity\Event\CurrencyAccountEvent;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Component\Money\Money;
use Evp\Component\UserCommon\Entity\UserInformation;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class LocalAccountProvider implements AccountProviderInterface
{
    const PROVIDER_KEY = 'local';

    private $entityManager;
    private $internalService;
    private $dispatcher;
    private $genericAccountManager;
    private $internalAccountTransferManager;
    private $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        InternalAccountManager $internalService,
        EventDispatcherInterface $dispatcher,
        GenericAccountManager $genericAccountManager,
        InternalAccountTransferManager $internalAccountTransferManager,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->internalService = $internalService;
        $this->dispatcher = $dispatcher;
        $this->genericAccountManager = $genericAccountManager;
        $this->internalAccountTransferManager = $internalAccountTransferManager;
        $this->logger = $logger;
    }

    /**
     * Transfer money from one Account to other
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $src
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $dst
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function transfer(Money $amount, Account $src, Account $dst)
    {
        $this->logger->info(__METHOD__, [$amount, $src, $dst]);

        $srcCurrencyAccount = $src->getCurrencyAccount($amount->getCurrency());
        $dstCurrencyAccount = $dst->getCurrencyAccount($amount->getCurrency());

        $transaction = $this->internalService->createTransaction();
        $this->internalService->appendTransaction(
            $transaction,
            $amount,
            $srcCurrencyAccount->getInternalMainAccount(),
            $srcCurrencyAccount->getInternalTransferAccount()
        );
        $this->internalService->appendTransaction(
            $transaction,
            $amount,
            $dstCurrencyAccount->getInternalFillAccount(),
            $dstCurrencyAccount->getInternalMainAccount()
        );
        $this->internalService->commitTransaction($transaction);

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_DECREASE,
            new CurrencyAccountEvent($srcCurrencyAccount, $amount)
        );
        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_INCREASE,
            new CurrencyAccountEvent($dstCurrencyAccount, $amount)
        );

        return $transaction;
    }

    /**
     * Fill specified account with inbound money
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function fill(Money $amount, Account $account)
    {
        $this->logger->info(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalFillAccount(),
            $currencyAccount->getInternalMainAccount()
        );

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_INCREASE,
            new CurrencyAccountEvent($currencyAccount, $amount)
        );

        return $transaction;
    }

    /**
     * @param Money $amount
     * @param Account $account
     * @param bool $allowToGoBelowZero
     *
     * @return AccountingTransaction
     */
    public function mainToTransfer(Money $amount, Account $account, $allowToGoBelowZero = false)
    {
        return $this->internalAccountTransferManager->mainToTransfer($amount, $account, $allowToGoBelowZero);
    }

    /**
     * Fill specified account with inbound money and make instant reservation of all money
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function fillToReserve(Money $amount, Account $account)
    {
        $this->logger->info(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalFillAccount(),
            $currencyAccount->getInternalReservationAccount()
        );

        return $transaction;
    }

    /**
     * Move money from specified account to commiss
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function toCommission(Money $amount, Account $account)
    {
        $this->logger->info(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalMainAccount(),
            $currencyAccount->getInternalCommissionAccount()
        );

        if ($amount->isPositive()) {
            $this->dispatcher->dispatch(
                AccountEvents::ON_BALANCE_DECREASE,
                new CurrencyAccountEvent($currencyAccount, $amount)
            );
        } else {
            $positiveAmount = $amount->abs();

            $this->dispatcher->dispatch(
                AccountEvents::ON_BALANCE_INCREASE,
                new CurrencyAccountEvent($currencyAccount, $positiveAmount)
            );
        }
        return $transaction;
    }

    /**
     * Reserve money in specified account
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function reserve(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalMainAccount(),
            $currencyAccount->getInternalReservationAccount()
        );

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_DECREASE,
            new CurrencyAccountEvent($currencyAccount, $amount)
        );

        return $transaction;
    }

    /**
     * Release reserved money from specified account
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function reserveRelease(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalReservationAccount(),
            $currencyAccount->getInternalMainAccount()
        );

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_INCREASE,
            new CurrencyAccountEvent($currencyAccount, $amount)
        );

        return $transaction;
    }

    /**
     * Move reserved money from specified account to commiss
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function reserveToCommission(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());

        return $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalReservationAccount(),
            $currencyAccount->getInternalCommissionAccount()
        );
    }

    /**
     * Move reserved money from specified account to external
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function reserveToTransfer(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());

        return $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalReservationAccount(),
            $currencyAccount->getInternalTransferAccount()
        );
    }

    /**
     * Refund comission - move money from commission to main account
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function refundCommission(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalCommissionAccount(),
            $currencyAccount->getInternalMainAccount()
        );

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_INCREASE,
            new CurrencyAccountEvent($currencyAccount, $amount)
        );

        return $transaction;
    }

    /**
     * Refund transfer - move money from transfer to main account
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     * @param bool $dispatchEvent
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function refundTransfer(Money $amount, Account $account, $dispatchEvent = true)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalTransferAccount(),
            $currencyAccount->getInternalMainAccount()
        );

        if ($dispatchEvent) {
            $this->dispatcher->dispatch(
                AccountEvents::ON_BALANCE_INCREASE,
                new CurrencyAccountEvent($currencyAccount, $amount)
            );
        }

        return $transaction;
    }

    /**
     * Refund fill - move money from main to fill account
     *
     * @param \Evp\Component\Money\Money $amount
     * @param \Evp\Bundle\BankAccountBundle\Entity\Account $account
     *
     * @return \Evp\Bundle\BankAccountBundle\Entity\AccountingTransaction
     *
     * @throws \Evp\Bundle\BankAccountBundle\Exception\AccountManagerException
     */
    public function refundFill(Money $amount, Account $account)
    {
        $this->logger->debug(__METHOD__, [$amount, $account]);

        $currencyAccount = $account->getCurrencyAccount($amount->getCurrency());
        $transaction = $this->internalService->transfer(
            $amount,
            $currencyAccount->getInternalMainAccount(),
            $currencyAccount->getInternalFillAccount()
        );

        $this->dispatcher->dispatch(
            AccountEvents::ON_BALANCE_DECREASE,
            new CurrencyAccountEvent($currencyAccount, $amount)
        );

        return $transaction;
    }

    public function createAccount(Client $client, UserInformation $userInformation = null): Account
    {
        return $this->genericAccountManager->createAccount($client, self::PROVIDER_KEY, $userInformation);
    }
}
