<?php

declare(strict_types=1);

namespace Evp\Bundle\BankAccountBundle\Service;

use DateTime;
use Doctrine\ORM\EntityManager;
use Evp\Bundle\BankAccountBundle\AccountEvents;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Entity\Event\AccountEvent;
use Evp\Bundle\BankAccountBundle\Exception\CloseAccountException;
use Evp\Bundle\BankChargeBundle\Service\ChargeHelper;
use Evp\Bundle\BankPermissionBundle\Entity\Permission;
use Evp\Bundle\BankPermissionBundle\Service\PermissionManager;
use Evp\Bundle\ClientBundle\Entity\Client;
use Evp\Bundle\ClientBundle\Entity\ClientLegal;
use Evp\Bundle\ClientBundle\Entity\TransferService\Agreement;
use Evp\Component\UserCommon\Entity\UserInformation;
use Evp\Component\UserRestClient\UserRestFactory;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class GenericAccountManager
{
    private EntityManager $entityManager;
    private EventDispatcherInterface $eventDispatcher;
    private AccountBalanceManager $accountBalanceManager;
    private ChargeHelper $chargeHelper;
    private PermissionManager $permissionManager;
    private UserRestFactory $userRestFactory;

    public function __construct(
        EntityManager $entityManager,
        EventDispatcherInterface $eventDispatcher,
        AccountBalanceManager $accountBalanceManager,
        ChargeHelper $chargeHelper,
        PermissionManager $permissionManager,
        UserRestFactory $userRestFactory
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->accountBalanceManager = $accountBalanceManager;
        $this->chargeHelper = $chargeHelper;
        $this->permissionManager = $permissionManager;
        $this->userRestFactory = $userRestFactory;
    }

    public function createAccount(Client $client, string $type, UserInformation $userInformation = null): Account
    {
        if (!$transferServiceAgreement = $client->getTransferServiceAgreement()) {
            $transferServiceAgreement = new Agreement();
            $transferServiceAgreement->setEnabled(true);
            $client->setTransferServiceAgreement($transferServiceAgreement);
            $this->entityManager->persist($transferServiceAgreement);
        }
        $account = new Account();
        $account->setActive(true);
        $account->setClient($client);
        $account->setType($type);

        $client->addAccount($account);

        $this->entityManager->persist($account);

        if (!$client->getTransferServiceAgreement()->getMainAccount()) {
            $transferServiceAgreement->setMainAccount($account);
        }
        if (!$client->getTransferServiceAgreement()->getCommissionAccount()) {
            $transferServiceAgreement->setCommissionAccount($account);
        }

        if ($client instanceof ClientLegal && $userInformation === null) {
            $userInformation = $this->userRestFactory->userClient()->getUser($client->getCovenanteeId());
        }

        $this->eventDispatcher->dispatch(
            AccountEvents::ON_OPENED,
            new AccountEvent($account, $userInformation)
        );

        return $account;
    }

    /**
     * @param Account $account
     * @param bool $closed
     *
     * @throws CloseAccountException
     */
    public function toggleAccountStatusOpenOrClosed(Account $account, bool $closed = false)
    {
        if ($closed) {
            if (!$this->accountBalanceManager->isZeroAccountBalance($account)) {
                throw new CloseAccountException('balance_not_zero', 'Account has non zero balance');
            }

            if ($this->chargeHelper->hasPendingCharges($account)) {
                throw new CloseAccountException('active_charges', 'Account has active charges');
            }
            $account->setClosed(true);
            $this->eventDispatcher->dispatch(AccountEvents::ON_CLOSED, new AccountEvent($account));
        } else {
            $account->setClosed(false);
            $userInformation = $this->userRestFactory->userClient()->getUser($account->getClient()->getCovenanteeId());
            $this->eventDispatcher->dispatch(
                AccountEvents::ON_OPENED,
                new AccountEvent($account, $userInformation)
            );
        }
    }

    /**
     * @param Account $account
     *
     * @throws CloseAccountException
     */
    public function forceCloseAccount(Account $account): void
    {
        if ($account->isClosed()) {
            throw new CloseAccountException('account_closed', 'Account is already closed');
        }

        if (!$this->accountBalanceManager->isZeroAccountBalance($account)) {
            throw new CloseAccountException('balance_not_zero', 'Account has non zero balance');
        }

        if ($this->chargeHelper->hasPendingCharges($account)) {
            throw new CloseAccountException('active_charges', 'Account has active charges');
        }

        $account->setClosed(true);
        $this->permissionManager->removeAllAccountPermissions($account);
        $this->eventDispatcher->dispatch(AccountEvents::ON_CLOSED, new AccountEvent($account));
    }

    public function toggleFactoringOn(Account $account): void
    {
        $owner = $account->getClient();
        foreach ($account->getPermissions() as $permission) {
            $this->permissionManager->invalidatePermission($permission);
        }

        $this->permissionManager->addPermission(
            $this->buildPermission(new Permission\WritePermission(), $account, [$owner])
        );
        $this->permissionManager->addPermission(
            $this->buildPermission(new Permission\ReadPermission(), $account, [$owner])
        );

        $account->setFactoringAccount(true);
    }

    public function toggleFactoringOff(Account $account): void
    {
        $account->setFactoringAccount(false);
    }

    /**
     * @param Permission $permission
     * @param Account $account
     * @param Client[] $clients
     * @return Permission
     */
    private function buildPermission(Permission $permission, Account $account, array $clients): Permission
    {
        return $permission->setAccount($account)
            ->setClients($clients)
            ->setValidFrom(new DateTime())
        ;
    }
}
