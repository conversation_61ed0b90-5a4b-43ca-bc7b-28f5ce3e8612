<?php

declare(strict_types=1);

namespace Evp\Bundle\BankAccountBundle\Service;

use DateTime;
use Doctrine\ORM\AbstractQuery;
use Evp\Bundle\BankAccountBundle\Entity\Account;
use Evp\Bundle\BankAccountBundle\Repository\StatementRepository;
use Evp\Component\Doctrine\Connection\TransactionAwareConnection;
use Evp\Component\GatewayCommon\BankAccount\Entity\AccountBalance;
use Evp\Component\GatewayCommon\BankAccount\Entity\Balance;
use Evp\Component\Money\Money;

class AccountBalanceDataProvider
{
    public const RESULT_KEY_AMOUNT = 'sclr_0';
    public const RESULT_KEY_CURRENCY = 'amount_currency_1';

    private TransactionAwareConnection $slaveConnection;
    private StatementRepository $statementRepository;
    private AccountTypeAtDateProvider $accountTypeAtDateProvider;

    public function __construct(
        TransactionAwareConnection $slaveConnection,
        StatementRepository $statementRepository,
        AccountTypeAtDateProvider $accountTypeAtDateProvider
    ) {
        $this->slaveConnection = $slaveConnection;
        $this->statementRepository = $statementRepository;
        $this->accountTypeAtDateProvider = $accountTypeAtDateProvider;
    }

    /**
     * @return Money[]
     */
    public function findAccountBalanceForDate(
        Account $account,
        DateTime $toDateInclusive,
        array $currencyList = null,
        bool $includeHiddenStatements = true
    ): array {
        $query = $this->statementRepository->getFindAccountBalanceForDateQuery(
            $account,
            $toDateInclusive,
            $currencyList,
            $includeHiddenStatements
        );

        return $this->findAccountBalance($query);
    }

    /**
     * @param AbstractQuery|null $query
     * @return Money[]
     */
    private function findAccountBalance(?AbstractQuery $query): array
    {
        if ($query === null) {
            return [];
        }

        $params = $types = [];
        foreach ($query->getParameters() as $parameter) {
            $params[] = $parameter->getValue();
            $types[] = $parameter->getType();
        }

        $queryResult = $this->slaveConnection->fetchAll($query->getSQL(), $params, $types);

        $balanceResult = [];
        foreach ($queryResult as $row) {
            $balanceResult[$row[self::RESULT_KEY_CURRENCY]] = new Money(
                $row[self::RESULT_KEY_AMOUNT],
                $row[self::RESULT_KEY_CURRENCY]
            );
        }

        return $balanceResult;
    }

    public function getLocalAccountBalanceForDate(
        Account $account,
        DateTime $toDateInclusive,
        bool $includeHiddenStatements = true
    ): AccountBalance {
        /** @var Money[] $totalBalance */
        $totalBalance = [];
        if ($this->accountIsContis($account, $toDateInclusive)) {
            return $this->constructAccountBalance($account->getNumber(), $totalBalance);
        }

        $balancesAtInterval = $this->findAccountBalanceForDate(
            $account,
            $toDateInclusive,
            null,
            $includeHiddenStatements
        );
        foreach ($balancesAtInterval as $balance) {
            $totalBalance[$balance->getCurrency()] = isset($totalBalance[$balance->getCurrency()])
                ? $totalBalance[$balance->getCurrency()]->add($balance)
                : $balance
            ;
        }

        return $this->constructAccountBalance($account->getNumber(), $totalBalance);
    }

    /**
     * @param string $accountNumber
     * @param Money[] $totalBalance
     *
     * @return AccountBalance
     */
    private function constructAccountBalance(string $accountNumber, array $totalBalance): AccountBalance
    {
        $accountBalance = new AccountBalance();
        $accountBalance->setAccountNumber($accountNumber);
        $balance = new Balance();
        foreach ($totalBalance as $money) {
            $balance->setMain($money->floor());
        }
        $accountBalance->setBalance($balance);

        return $accountBalance;
    }

    private function accountIsContis(Account $account, DateTime $toDateInclusive): bool
    {
        return Account::TYPE_CONTIS === $this->accountTypeAtDateProvider->getAccountTypeAtDate(
            $account,
            $toDateInclusive
        );
    }
}
