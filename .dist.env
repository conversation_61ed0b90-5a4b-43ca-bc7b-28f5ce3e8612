## General app and framework config
#
APP_ENV=dev
SECRET=f4a73050db846be070f20b1abf4cc66074aef7dc
SYSTEM_HOSTNAME=http://evpbank.dev.docker
EVP_CALLBACK_REMOTE_HOST=http://evpbank.dev.docker
PROXY_HOST=http://127.0.0.1:3128
TRUSTED_PROXIES='[]'
SHARED_PRIVATE_UPLOADS_DIR=/home/<USER>/src/app/Resources/files
TEMPORARY_FILES_DIRECTORY=/tmp
SYSTEM_SECURITY_SUPERVISOR_EMAIL=

## Database config
#
DATABASE_HOST=gateway.dev.lan
DATABASE_PORT=7450
DATABASE_NAME=gateway
DATABASE_USER="{{db_username}}"
DATABASE_PASSWORD="{{db_password}}"
DATABASE_SOCKET=/var/run/mysql-gateway-wallet-rw.sock

## Mailer config
#
MAILER_HOST=mailhog.dev.docker
MAILER_PORT=1025

## User type AI resolving config
#
REMOTE_CREDENTIALS_USER_TYPE_AI_URL=http://user-type-ai.dev.docker/user-type/rest/v1/

## Sentry config
#
SENTRY_DSN=

## Graylog config
#
GRAYLOG_HOSTNAME=graylog.dev.docker
GRAYLOG_PORT=12201

## Elasticsearch config
#
ELASTICSEARCH_HOST_V1=localhost
ELASTICSEARCH_PORT_V1=9200
ELASTICSEARCH_PASSWORD_V1=

## Rabbitmq config
#
RABBIT_MQ_HOST=rabbitmq.dev.docker
RABBIT_MQ_PORT=5672
RABBIT_MQ_USERNAME=guest
RABBIT_MQ_PASSWORD=guest

## Rabbitmq extra config
#
RABBIT_MQ_EXTRA_HOST=rabbitmq.dev.docker
RABBIT_MQ_EXTRA_PORT=5672
RABBIT_MQ_EXTRA_USERNAME=guest
RABBIT_MQ_EXTRA_PASSWORD=guest

## Local credentials config
#
LOCAL_CREDENTIALS_MOKEJIMAI_PASSWORD=pass
LOCAL_CREDENTIALS_WALLET_API_PASSWORD=pass
LOCAL_CREDENTIALS_WALLET_FRONTEND_PASSWORD=pass
LOCAL_CREDENTIALS_SURVEILLANCE_ENGINE_PASSWORD=pass
LOCAL_CREDENTIALS_BLACKLIST_PASSWORD=pass
LOCAL_CREDENTIALS_INFORMATION_PASSWORD=pass
LOCAL_CREDENTIALS_CHECKOUT_PASSWORD=pass
LOCAL_CREDENTIALS_SAVINGS_PASSWORD=pass
LOCAL_CREDENTIALS_RECURRING_PAYMENTS_PASSWORD=pass
LOCAL_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD=pass
LOCAL_CREDENTIALS_CHALLENGE_PASSWORD=pass
LOCAL_CREDENTIALS_PARTNER_FRONTEND_PASSWORD=pass
LOCAL_CREDENTIALS_GEORGIA_REVENUE_SERVICE_PASSWORD=pass
LOCAL_CREDENTIALS_GEORGIA_ENFORCEMENT_BUREAU_PASSWORD=pass
LOCAL_CREDENTIALS_ACCOUNTING_OPERATION_MANAGER_PASSWORD=pass
LOCAL_CREDENTIALS_RISK_LEVEL_PASSWORD=pass
LOCAL_CREDENTIALS_OPEN_BANKING_API_PASSWORD=pass
LOCAL_CREDENTIALS_CARD_PASSWORD=pass
LOCAL_CREDENTIALS_LOANS_API_PASSWORD=pass
LOCAL_CREDENTIALS_SPREADSHEET_UPDATER_PASSWORD=pass
LOCAL_CREDENTIALS_CARD_SWITCH_PASSWORD=pass
LOCAL_CREDENTIALS_CHECKOUT_MERCHANT_PROJECT_PASSWORD=pass
LOCAL_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_PASSWORD=pass
LOCAL_CREDENTIALS_CREDIT_ONLINE=pass
LOCAL_CREDENTIALS_ACCOUNTING_EVENTS_PASSWORD=pass

## Remote credentials mokejimai config
#
REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL=http://mokejimai.dev.docker
REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME=evpbank
REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD=pass

## Remote credentials postbank bg iban config
REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_BASE_URL=http://postbank-bg-iban.dev.docker/postbank-bg-iban/rest/v1/
REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_USERNAME=evpbank
REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD=pass

## Remote credentials blacklist restriction config
#
REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_URL=http://blacklist.dev.docker/app_dev.php/restriction/rest/v1/
REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_USERNAME=evpbank
REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_PASSWORD=pass

## Remote credentials auth api config
#
REMOTE_CREDENTIALS_AUTH_API_URL=http://auth-api.dev.docker/authentication/rest/v1/
REMOTE_CREDENTIALS_AUTH_API_USERNAME=evpbank
REMOTE_CREDENTIALS_AUTH_API_PASSWORD=pass

## Remote credentials georgia revenue service config
#
REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_URL=http://georgia-revenue-service.dev.docker/rest/v1/
REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_USERNAME=evpbank
REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_PASSWORD=pass

## Remote credentials risk-level config
#
REMOTE_CREDENTIALS_RISK_LEVEL_BASE_URL=http://risk-level.dev.docker
REMOTE_CREDENTIALS_RISK_LEVEL_USERNAME=evpbank
REMOTE_CREDENTIALS_RISK_LEVEL_PASSWORD=pass

## Remote credentials card-switch service config
#
CARD_SWITCH_BASE_URL=http://card-switch.dev.docker
REMOTE_CREDENTIALS_CARD_SWITCH_USERNAME=evpbank
REMOTE_CREDENTIALS_CARD_SWITCH_PASSWORD=pass

## Remote credentials Transfer Surveillance Assistant service config
#
REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_BASE_URL=http://transfer-surveillance-assistant.dev.docker
REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_USERNAME=evpbank
REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_PASSWORD=pass

## VMI payments client config
#
VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_USERNAME=
VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_PASSWORD=
VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_WSDL_PATH=/../vendor/evp/vmi-mgw-client/src/VmiMgwClient/Resources/MGW_PUBLIC_FUNCTIONS_STAGING.wsdl
VMI_PAYMENTS_VMI_MGW_CLIENT_PRIVATE_KEY=evp.pfx
VMI_PAYMENTS_VMI_MGW_CLIENT_PRIVATE_KEY_PASSWORD=evp123

## Client registration bonus config
#
REGISTRATION_BONUS_TRANSFER_SIGNER_CLIEND_ID=0
REGISTRATION_BONUS_CREDIT_ACCOUNT=EVP

## Payza config
#
PAYZA_USERNAME=
PAYZA_PASSWORD=

## Card gateway config
#
CARD_GATEWAY_REST_URL=http://card-gateway.dev.docker/app_dev.php
CARD_REDIRECT_URL_PATTERN=http://card-gateway-frontend.dev.docker/%s/authorize/%s
REMOTE_CREDENTIALS_CARD_GATEWAY_PASSWORD=pass

## Wallet oauth client config
#
WALLET_OAUTH_CLIENT_API_ENDPOINT=http://wallet-api.dev.docker
WALLET_OAUTH_CLIENT_AUTH_ENDPOINT=https://bank.paysera.com.dev/frontend
WALLET_OAUTH_CLIENT_MAC_ID=123
WALLET_OAUTH_CLIENT_MAC_SECRET=abc

## Contis config
#
CONTIS_STATEMENTS_SFTP_HOST=testsftp.contisgroup.com
CONTIS_STATEMENTS_SFTP_USERNAME=payserbeta
CONTIS_STATEMENTS_SFTP_PASSPHRASE=nmPt0QF700yLQUy4
CONTIS_STATEMENTS_SFTP_PORT=22
CONTIS_STATEMENTS_SFTP_PRIVATE_KEY=/keys/dev/contis-sftp-sandbox.pem
CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS=2019421
CONTIS_PAYSERA_ACCOUNT_NUMBER_SORT_CODE=39600
CONTIS_CLIENT_LOGIN_USERNAME=payserbeta
CONTIS_CLIENT_LOGIN_PASSWORD="pAyS3RBEt!"
CONTIS_CLIENT_BASE_URL=https://sandboxapi.contis.com
CONTIS_INFORMATION_EMAIL=<EMAIL>
CONTIS_RESTRICTED_CARD_MASKED_NUMBER_PATTERN="/CONTIS_CARD_PREFIX.*/i"
CONTIS_PUSH_NOTIFICATIONS_SECURITY_KEY="****************"
CONTIS_CARDS_MATURITY_INTERVAL=PT10M
CONTIS_LEGAL_CLIENT_AGREEMENT_CODE=W4PJKDMC

## Translation proxy client config
#
TRANSLATION_PROXY_CLIENT_ENDPOINT=https://translation-proxy.paysera.net

## LB integration config
#
LB_INTEGRATION_SIGNATURE_API_URI=http://hsm-api.dev.docker/security/rest/v1/signature/lb
LB_INTEGRATION_SIGNATURE_API_USERNAME=admin
LB_INTEGRATION_SIGNATURE_API_PASSWORD=admin123

## Banking history config
#
BANKING_HISTORY_CLIENT_HOST=http://banking-history.dev.docker
BANKING_HISTORY_CLIENT_AUTH_USERNAME=evpbank
BANKING_HISTORY_CLIENT_AUTH_PASSWORD=pass

## Intis config
#
INTIS_LOGIN=user
INTIS_API_KEY=key
INTIS_API_HOST=host

## Mobile payments config
#
MOBILE_PAYMENTS_API_BASE_URI=http://mobile-payments-api.dev.docker/rest/v1/
MOBILE_PAYMENTS_API_USERNAME=evpbank
MOBILE_PAYMENTS_API_PASSWORD=pass

## Client notification config
#
CLIENT_NOTIFICATION_CALLBACK_PRIVATE_KEY=/keys/dev/evp.pem
CLIENT_NOTIFICATION_CALLBACK_PRIVATE_KEY_CONTENT=
## DowJones config
#
DOWJONES_HOST=https://dow-jones.paysera-staging.net/
DOWJONES_USERNAME=test
DOWJONES_PASSWORD=test
DOWJONES_RISK_CENTER_USERNAME=test
DOWJONES_RISK_CENTER_PASSWORD=test

## Sepa Instant config
#
SEPA_INSTANT_WEB_SERVICE_URL=
SEPA_INSTANT_LB_PUBLIC_KEY=/keys/dev/lb_public_key.pem

## InfluxDB config
#
INFLUXDB_STATISTICS_HOST=**********5
INFLUXDB_STATISTICS_DATABASE=statistics
INFLUXDB_STATISTICS_USERNAME=
INFLUXDB_STATISTICS_PASSWORD=

## User identifier hash ids config
#
USER_IDENTIFIER_HASH_IDS_SALT="vN@'^x34v&TN)*em"

## Whitelisted ips config
#
WHITELISTED_IPS='["127.0.0.1", "**********/24", "**********/16"]'

## Redis sentinel config
#
REDIS_SENTINEL_1=redis://127.0.0.1
REDIS_SENTINEL_2=redis://127.0.0.1
REDIS_SENTINEL_3=redis://127.0.0.1
REDIS_PASSWORD=
REDIS_SENTINEL_MASTER=master01

## Bank transfer api v1 config
#
BANK_TRANSFER_API_V1_PRIVATE_KEY=/keys/dev/evp.pem
BANK_TRANSFER_API_V1_PRIVATE_KEY_PASSWORD=
BANK_TRANSFER_API_V1_PUBLIC_KEY=/keys/dev/evp.pub.pem

## Webmoney WMXI config
#
WMXI_CERTIFICATE=
WMXI_LIGHT_PRIVATE_KEY=
WMXI_LIGHT_PRIVATE_KEY_PASS=
WMXI_LIGHT_CERTIFICATE=

## VMI account report config
#
VMI_ACCOUNT_REPORT_GPG_HOME_DIR=
VMI_ACCOUNT_REPORT_EMAIL_PROCESSOR_IMAP_USERNAME=
VMI_ACCOUNT_REPORT_EMAIL_PROCESSOR_IMAP_PASSWORD=

## VMI report
#
VMI_REPORT_KEY=/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/key.pem
VMI_REPORT_CERT=/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/cert.pem
VMI_REPORT_PASS=Zw71wYiHZaO1XC11hzvf9WR8
VMI_REPORT_WSDL="/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/Schemas/TIESService(Demo).wsdl"
VMI_REPORT_VMI_PUBLIC_KEY_PATH="/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/ties.vmi.lt_viesas.der"
VMI_REPORT_VMI_PUBLIC_KEY_FILE=

VMI_REPORT_CONTIS_KEY=/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/key.pem
VMI_REPORT_CONTIS_CERT=/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/cert.pem
VMI_REPORT_CONTIS_PASS=Zw71wYiHZaO1XC11hzvf9WR8
VMI_REPORT_CONTIS_WSDL="/../vendor/evp/vmi-mgw-client/src/VmiTiesClient/Resources/Schemas/TIESService(Demo).wsdl"

## Pdf signer config
#
PDF_SIGNER_KEY=pdf1403Dev
PDF_SIGNER_JAVA_PATH=/usr/bin/java
PDF_SIGNER_KEY_STORE=/keys/dev/keystore.ks
PDF_SIGNER_CERTIFICATE=/keys/dev/evp.cer

SHARD_NAME=a

## Fntt client configuration
#
FNTT_CLIENT_USERNAME=PayseraSI
FNTT_CLIENT_URL=https://ppp.fntt.lt:8444/cxf/CTRDataService?wsdl
FNTT_CLIENT_PASSWORD=<uVOL^1vN

## Push Notifications Contis's IP
#
CONTIS_PUSH_NOTIFICATIONS_IP_1=***********/0
CONTIS_PUSH_NOTIFICATIONS_IP_2=**********
CONTIS_PUSH_NOTIFICATIONS_IP_3=**********
CONTIS_PUSH_NOTIFICATIONS_IP_4=**********
CONTIS_PUSH_NOTIFICATIONS_IP_5=**********
CONTIS_PUSH_NOTIFICATIONS_IP_6=**********

## Push Notifications Contis's Testing IPs
#
CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_1=**********
CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_2=**********
CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_3=**********
CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_4=**********

## Nelmio CORS config
#
CORS_MOKEJIMAI_DOMAIN="^https://bank.paysera.com.dev$"
CORS_MOKEJIMAI_OFFICE_DOMAIN="^https://bank.paysera.net.dev$"
CORS_IMPERSONATION_MOKEJIMAI_DOMAIN="^https://impersonate.paysera.com.dev$"
CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN="^https://mokejimai.dev.docker$"

## Google recaptcha config
#
RECAPTCHA_SITE_KEY=key
RECAPTCHA_SITE_SECRET=secret

## Google Maps API key
#
GOOGLE_MAPS_API_KEY=api_key

## Libra API config
#
LIBRA_API_URL="https://api-test.librabank.ro:8243"
LIBRA_USERNAME="paysera"
LIBRA_PASSWORD="e97]?.Lh"
LIBRA_CUSTOMER_KEY="GJTWAhGawp6TLqPdn_1f4nL0_4Ia"
LIBRA_CUSTOMER_SECRET="LP2I4Ee3IIfXSHlaVeGyepfQ6N8a"
LIBRA_MASTER_ACCOUNT=************************
LIBRA_ACCOUNTS='["************************","************************","************************","************************"]'
LIBRA_PAYMENTS_API_PREFIX="/PAYMENTS_API/v1/fintech"
LIBRA_SUB_ACCOUNTS_API_PREFIX="/SUB_ACCOUNTS/v1"

LIBRA_BANK_X_API_KEY="api_key"

## Currency One
#
CURRENCY_ONE_PRIVATE_KEY_PATH=/keys/dev/currency_one_private.key
CURRENCY_ONE_PRIVATE_KEY_PASSWORD=
CURRENCY_ONE_API_KEY=1gk0kn59xn647z6jbfyn8epe3
CURRENCY_ONE_BASE_URL=https://api.walutomat.dev

## Georgia RTGS
#
GEORGIA_RTGS_MESSAGE_SIGNATURE_CERTIFICATE_PATH=/keys/dev/georgia_rtgs_message_certificate.crt
GEORGIA_RTGS_MESSAGE_SIGNATURE_PRIVATE_KEY_PATH=/keys/dev/georgia_rtgs_message_private.key
GEORGIA_RTGS_MESSAGE_SIGNATURE_PRIVATE_KEY_PASSWORD=123456

## AFEX
#
AFEX_PARTNER_API_URL="https://demo.api.afex.com:7885"
AFEX_API_URL="https://webapidemo.afex.com"
AFEX_API_KEY="********Vebe2278f-f3d7-ea11-99ab-0050569b0074"
AFEX_API_PASSWORD="6c679a43-f3d7-ea11-80f7-0050569b43ff"
AFEX_ACCOUNT_NUMBER="********"

REMOTE_CREDENTIALS_CHALLENGE_URI=http://challenge.dev.docker/
REMOTE_CREDENTIALS_CHALLENGE_USERNAME=evpbank
REMOTE_CREDENTIALS_CHALLENGE_PASSWORD=pass

## TNT
#
TNT_CLIENT_LOGIN_COMPANY="LT001154ST"
TNT_CLIENT_LOGIN_PASSWORD="TbDuVcKvzC"
TNT_CLIENT_LOGIN_APP_ID="EC"
TNT_CLIENT_LOGIN_APP_VERSION="3.1"

DATABASE_SLAVE_GATEWAY_HOST=gateway.dev.lan
DATABASE_SLAVE_GATEWAY_PORT=7450
DATABASE_SLAVE_GATEWAY_NAME=gateway
DATABASE_SLAVE_GATEWAY_USER=app
DATABASE_SLAVE_GATEWAY_PASSWORD=pass

## ACCOUNTING API
#
ACCOUNTING_API_BASE_URL=http://accounting-api.dev.docker/rest/v1/
ACCOUNTING_API_USERNAME=evpbank
ACCOUNTING_API_PASSWORD=pass

## Accounting Operation Manager
#
ACCOUNTING_OPERATION_MANAGER_API_BASE_URL=http://accounting-operation-manager.dev.docker/rest/v1/
ACCOUNTING_OPERATION_MANAGER_API_USERNAME=evpbank
ACCOUNTING_OPERATION_MANAGER_API_PASSWORD=pass

## SINGLE WINDOW
#
SINGLE_WINDOW_TRANSFER_API_POINT_CODE="PSR0002"
SINGLE_WINDOW_TRANSFER_API_USERNAME="PseClerk"
SINGLE_WINDOW_TRANSFER_API_PASSWORD="7944LsPJtm3FYKb"
SINGLE_WINDOW_REPORT_API_POINT_CODE="PSR0001"
SINGLE_WINDOW_REPORT_API_USERNAME="PseReport"
SINGLE_WINDOW_REPORT_API_PASSWORD="SjA9N162yxkA7Q4O"
SINGLE_WINDOW_ADMIN_API_POINT_CODE="PSR0001"
SINGLE_WINDOW_ADMIN_API_USERNAME="PseAdmin"
SINGLE_WINDOW_ADMIN_API_PASSWORD="S6VSaFbmAMtS8Mz"
SINGLE_WINDOW_SOAP_CLIENT_WSDL_FILENAME="single_window_dev.wsdl"

## GLOBUS BANK
#
GLOBUS_BANK_SFTP_USERNAME="paysera_test"

## RIA
#
RIA_FTP_HOST=stgftps.riaenvia.net
RIA_FTP_PORT=21
RIA_FTP_USERNAME=********
RIA_FTP_PASSWORD=6xEvwQF9p56Q

## Browser API config
#
BROWSER_API_DOMAIN=http://browser.dev.docker
BROWSER_API_USER=pisp
BROWSER_API_PASSWORD=pass

## Dow Jones AI config
#
REMOTE_CREDENTIALS_DOW_JONES_AI_URL=http://dow-jones-ai.dev.docker/search-results/rest/v1/

## DHL client Config
#
DHL_CLIENT_API_URL=https://express.api.dhl.com/mydhlapi/test
DHL_CLIENT_LOGIN_COMPANY=payseraltLT
DHL_CLIENT_LOGIN_PASSWORD=U@6lO$6nD!2e

## Currencycloud
#
CURRENCYCLOUD_SESSION_ENVIRONMENT=demonstration
CURRENCYCLOUD_LOGIN_ID=<EMAIL>
CURRENCYCLOUD_API_KEY=9cb4b2c6bf6fef1922d4761c6cc641116c428a6c4ccc2dfe4735be1012f3c72f
CURRENCYCLOUD_ACCOUNT_ID=221117-97209

## Airwallex
#
AIRWALLEX_API_URL=https://api-demo.airwallex.com/api/v1/
AIRWALLEX_API_KEY=838e30499f71dc32f0570707b286adb109d303e2dc4bcf4429aaf8fe361215981677f2ee11b40b0df74cf6540a81ef8e
AIRWALLEX_CLIENT_ID=OgVeE8JZRgikdvePE8VP7Q
AIRWALLEX_ACCOUNT_ID=acct_tVqIoLvpMXK5UdicX1BLyw
## PrivatMoney
#
PRIVAT_MONEY_API_URL=https://unigatetest.privatbank.ua/Privatmoney/Privatmoney
PRIVAT_MONEY_POINT_ID=9N001
PRIVAT_MONEY_BANK_ID=pb
PRIVAT_MONEY_SALE_TYPE=uniwin
PRIVAT_MONEY_DESCRIPTION=apipaysera

## Georgia Debtor Registry
#
GEORGIA_DEBTOR_REGISTRY_BASE_URL=http://**************
GEORGIA_DEBTOR_REGISTRY_USERNAME=
GEORGIA_DEBTOR_PASSWORD=

## Mokejimai Admin URL
#
MOKEJIMAI_ADMIN_URL=https://bank.paysera.com.dev/admin/

## Currency pricing URL
# For local currency-exchange use
#CURRENCY_PRICING_URL=http://currency-exchange.dev.docker
CURRENCY_PRICING_URL=http://api.evp.lt

CONTIS_CLIENT_3DES_IV_KEY=
CONTIS_CLIENT_3DES_SECRET_KEY=
CONTIS_TRANSFER_CLIENT_LOGIN_PASSWORD=
CONTIS_TRANSFER_CLIENT_LOGIN_USERNAME=
CONTIS_TRANSFER_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS=
CONTIS_TRANSFER_PAYSERA_ACCOUNT_NUMBER_SORT_CODE=
CONTIS_TRANSFER_PRIVATE_KEY_PATH=
CONTIS_TRANSFER_PUSH_NOTIFICATIONS_SECURITY_KEY=
CONTIS_TRANSFER_SFTP_HOST=
CONTIS_TRANSFER_SFTP_PORT=
CONTIS_TRANSFER_SFTP_USERNAME=
MAC_AUTHENTICATION_CACHE_NAMESPACE=
SINGLE_WINDOW_SOAP_API_URL=
SINGLE_WINDOW_XML_TARGET_NAMESPACE=
TRANSFERMATE_CLIENT_API_ENDPOINT=
TRANSFERMATE_CLIENT_PASSWORD=
TRANSFERMATE_CLIENT_SECRET_WORD=
TRANSFERMATE_CLIENT_USER=
TRANSFERMATE_VIRTUAL_ACCOUNTS=
AUTH_API_PRIVATE_KEY=
AUTH_API_PUBLIC_KEY=
MOKEJIMAI_PUBLIC_KEY=
PRIVAT_MONEY_BINARY_SECURITY_TOKEN=
PRIVAT_MONEY_PRIVATE_KEY=
PRIVAT_MONEY_PUBLIC_KEY=
SINGLE_WINDOW_CLIENT_CERTIFICATE=
SINGLE_WINDOW_GLOBUS_BANK_SFTP_PRIVATE_KEY=
SINGLE_WINDOW_PRIVATE_KEY=
SINGLE_WINDOW_ROOT_CA=
CONTIS_TRANSFER_PRIVATE_KEY=
CURRENCY_ONE_PRIVATE_KEY=
EVP_PRIVATE_KEY=
EVP_PUBLIC_KEY=
LB_PUBLIC_KEY=
NBG_PAYSERA_AGENT_CERTIFICATE=
NBG_PAYSERA_AGENT_PRIVATE_KEY=

TIER=
APPLICATION_NAME=

## Ceph API config
#
CEPH_API_BASE_URL=http://s3-integration.dev.docker:9004
CEPH_API_KEY=app
CEPH_API_SECRET=password

## ChatGPT API config
#
CHAT_GPT_API_BASE_URL=https://api.openai.com/v1/
CHAT_GPT_API_KEY=

## Anthropic API config
#
ANTHROPIC_API_BASE_URL=https://api.anthropic.com/v1/
ANTHROPIC_API_KEY=

ACCOUNTING_SOAP_CLIENT_WSDL_FILENAME=accounting_dev.xml

## Remote credentials checkout config
#
REMOTE_CREDENTIALS_CHECKOUT_BASE_URL=https://checkout.dev.docker
REMOTE_CREDENTIALS_CHECKOUT_USERNAME=evp
REMOTE_CREDENTIALS_CHECKOUT_PASSWORD=pass

## Context aware translator config
#
CONTEXT_AWARE_TRANSLATOR_BASE_URL=https://context-translator-api.paysera-staging.net/

## Remote credentials app-card service config
#
REMOTE_CREDENTIALS_CARD_BASE_URL=https://card.dev.docker
REMOTE_CREDENTIALS_CARD_USERNAME=evpbank
REMOTE_CREDENTIALS_CARD_PASSWORD=pass

## Redash api key
REDASH_HOST=https://graphs.paysera.net
REDASH_API_KEY=

## OpenAI Assistant API
OPENAI_ASSISTANT_API_BASE_URL=https://api.openai.com/v1/
OPENAI_ASSISTANT_API_KEY=

## IPID config
IPID_API_ENDPOINT=https://sandbox.ipid.works
IPID_API_KEY=
IPID_CUSTOMER_ID=
IPID_CLIENT_PRIVATE_KEY=
IPID_API_TIMEOUT=
IPID_API_RETRY_ATTEMPTS=
