config:
  data:
    TIER: Tier-1
    APPLICATION_NAME: evpbank-k8s
    APP_ENV: ''
    SYSTEM_HOSTNAME: ''
    EVP_CALLBACK_REMOTE_HOST: ''
    PROXY_HOST: ''
    TRUSTED_PROXIES: ''
    MAC_AUTHENTICATION_CACHE_NAMESPACE: ''
    SHARED_PRIVATE_UPLOADS_DIR: ''
    TEMPORARY_FILES_DIRECTORY: ''
    SYSTEM_SECURITY_SUPERVISOR_EMAIL: ''
    DATABASE_HOST: ''
    DATABASE_PORT: ''
    DATABASE_NAME: ''
    DATABASE_USER: ''
    DATABASE_SOCKET: ''
    DATABASE_SLAVE_GATEWAY_HOST: ''
    DATABASE_SLAVE_GATEWAY_PORT: ''
    DATABASE_SLAVE_GATEWAY_NAME: ''
    DATABASE_SLAVE_GATEWAY_USER: ''
    MAILER_HOST: ''
    MAILER_PORT: ''
    G<PERSON><PERSON><PERSON><PERSON><PERSON>_HOSTNAME: ''
    GRAYLOG_PORT: ''
    ELAST<PERSON><PERSON>ARCH_HOST_V1: ''
    ELASTICSEARCH_PORT_V1: ''
    RABBIT_MQ_HOST: ''
    RABBIT_MQ_PORT: ''
    RABBIT_MQ_USERNAME: ''
    RABBIT_MQ_EXTRA_HOST: ''
    RABBIT_MQ_EXTRA_PORT: ''
    RABBIT_MQ_EXTRA_USERNAME: ''
    REMOTE_CREDENTIALS_CHALLENGE_URI: ''
    REMOTE_CREDENTIALS_CHALLENGE_USERNAME: ''
    TNT_CLIENT_LOGIN_COMPANY: ''
    TNT_CLIENT_LOGIN_APP_ID: ''
    TNT_CLIENT_LOGIN_APP_VERSION: ''
    REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL: ''
    REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME: ''
    REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_URL: ''
    REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_USERNAME: ''
    REMOTE_CREDENTIALS_AUTH_API_URL: ''
    REMOTE_CREDENTIALS_AUTH_API_USERNAME: ''
    REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_BASE_URL: ''
    REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_USERNAME: ''
    REMOTE_CREDENTIALS_RISK_LEVEL_BASE_URL: ''
    REMOTE_CREDENTIALS_RISK_LEVEL_USERNAME: ''
    VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_USERNAME: ''
    VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_WSDL_PATH: ''
    VMI_PAYMENTS_VMI_MGW_CLIENT_PRIVATE_KEY: ''
    REGISTRATION_BONUS_TRANSFER_SIGNER_CLIEND_ID: ''
    REGISTRATION_BONUS_CREDIT_ACCOUNT: ''
    PAYZA_USERNAME: ''
    CARD_GATEWAY_REST_URL: ''
    CARD_REDIRECT_URL_PATTERN: ''
    WALLET_OAUTH_CLIENT_API_ENDPOINT: ''
    WALLET_OAUTH_CLIENT_AUTH_ENDPOINT: ''
    WALLET_OAUTH_CLIENT_MAC_ID: ''
    CONTIS_STATEMENTS_SFTP_HOST: ''
    CONTIS_STATEMENTS_SFTP_USERNAME: ''
    CONTIS_STATEMENTS_SFTP_PASSPHRASE: ''
    CONTIS_STATEMENTS_SFTP_PORT: ''
    CONTIS_STATEMENTS_SFTP_PRIVATE_KEY: ''
    CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS: ''
    CONTIS_PAYSERA_ACCOUNT_NUMBER_SORT_CODE: ''
    CONTIS_CLIENT_BASE_URL: ''
    CONTIS_CLIENT_LOGIN_USERNAME: ''
    CONTIS_INFORMATION_EMAIL: ''
    CONTIS_RESTRICTED_CARD_MASKED_NUMBER_PATTERN: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_1: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_2: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_3: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_4: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_5: ''
    CONTIS_PUSH_NOTIFICATIONS_IP_6: ''
    CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_1: ''
    CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_2: ''
    CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_3: ''
    CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_4: ''
    TRANSLATION_PROXY_CLIENT_ENDPOINT: ''
    LB_INTEGRATION_SIGNATURE_API_URI: ''
    LB_INTEGRATION_SIGNATURE_API_USERNAME: ''
    REMOTE_CREDENTIALS_USER_TYPE_AI_URL: ''
    BANKING_HISTORY_CLIENT_HOST: ''
    BANKING_HISTORY_CLIENT_AUTH_USERNAME: ''
    INTIS_LOGIN: ''
    INTIS_API_HOST: ''
    TRANSFERMATE_CLIENT_API_ENDPOINT: ''
    TRANSFERMATE_CLIENT_USER: ''
    TRANSFERMATE_VIRTUAL_ACCOUNTS: ''
    MOBILE_PAYMENTS_API_BASE_URI: ''
    MOBILE_PAYMENTS_API_USERNAME: ''
    CLIENT_NOTIFICATION_CALLBACK_PRIVATE_KEY: ''
    DOWJONES_HOST: ''
    DOWJONES_USERNAME: ''
    DOWJONES_RISK_CENTER_USERNAME: ''
    SEPA_INSTANT_WEB_SERVICE_URL: ''
    SEPA_INSTANT_LB_PUBLIC_KEY: ''
    INFLUXDB_STATISTICS_HOST: ''
    INFLUXDB_STATISTICS_DATABASE: ''
    INFLUXDB_STATISTICS_USERNAME: ''
    WHITELISTED_IPS: ''
    REDIS_SENTINEL_1: ''
    REDIS_SENTINEL_2: ''
    REDIS_SENTINEL_3: ''
    REDIS_SENTINEL_MASTER: master01
    BANK_TRANSFER_API_V1_PRIVATE_KEY: ''
    BANK_TRANSFER_API_V1_PUBLIC_KEY: ''
    WMXI_CERTIFICATE: ''
    WMXI_LIGHT_PRIVATE_KEY: ''
    WMXI_LIGHT_CERTIFICATE: ''
    VMI_ACCOUNT_REPORT_GPG_HOME_DIR: ''
    VMI_ACCOUNT_REPORT_EMAIL_PROCESSOR_IMAP_USERNAME: ''
    VMI_REPORT_KEY: ''
    VMI_REPORT_CERT: ''
    VMI_REPORT_WSDL: ''
    VMI_REPORT_CONTIS_KEY: ''
    VMI_REPORT_CONTIS_CERT: ''
    VMI_REPORT_CONTIS_WSDL: ''
    VMI_REPORT_VMI_PUBLIC_KEY_PATH: ''
    PDF_SIGNER_JAVA_PATH: ''
    PDF_SIGNER_KEY_STORE: ''
    PDF_SIGNER_CERTIFICATE: ''
    SHARD_NAME: ''
    FNTT_CLIENT_USERNAME: ''
    FNTT_CLIENT_URL: ''
    CORS_MOKEJIMAI_DOMAIN: ''
    CORS_MOKEJIMAI_OFFICE_DOMAIN: ''
    CORS_IMPERSONATION_MOKEJIMAI_DOMAIN: ''
    CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN: ''
    LIBRA_API_URL: ''
    LIBRA_USERNAME: ''
    LIBRA_MASTER_ACCOUNT: ''
    LIBRA_ACCOUNTS: ''
    LIBRA_PAYMENTS_API_PREFIX: ''
    LIBRA_SUB_ACCOUNTS_API_PREFIX: ''
    LIBRA_BANK_X_API_KEY: ''
    CURRENCY_ONE_PRIVATE_KEY_PATH: ''
    CURRENCY_ONE_BASE_URL: ''
    AFEX_PARTNER_API_URL: ''
    AFEX_API_URL: ''
    AFEX_ACCOUNT_NUMBER: ''
    ACCOUNTING_API_BASE_URL: ''
    ACCOUNTING_API_USERNAME: ''
    SINGLE_WINDOW_SOAP_API_URL: ''
    SINGLE_WINDOW_XML_TARGET_NAMESPACE: ''
    SINGLE_WINDOW_TRANSFER_API_POINT_CODE: ''
    SINGLE_WINDOW_TRANSFER_API_USERNAME: ''
    SINGLE_WINDOW_REPORT_API_POINT_CODE: ''
    SINGLE_WINDOW_REPORT_API_USERNAME: ''
    SINGLE_WINDOW_ADMIN_API_POINT_CODE: ''
    SINGLE_WINDOW_ADMIN_API_USERNAME: ''
    SINGLE_WINDOW_SOAP_CLIENT_WSDL_FILENAME: ''
    GLOBUS_BANK_SFTP_USERNAME: ''
    RIA_FTP_HOST: ''
    RIA_FTP_PORT: ''
    RIA_FTP_USERNAME: ''
    BROWSER_API_DOMAIN: ''
    BROWSER_API_USER: ''
    REMOTE_CREDENTIALS_DOW_JONES_AI_URL: ''
    DHL_CLIENT_API_URL: ''
    DHL_CLIENT_LOGIN_COMPANY: ''
    CURRENCYCLOUD_SESSION_ENVIRONMENT: ''
    CURRENCYCLOUD_LOGIN_ID: ''
    CURRENCYCLOUD_ACCOUNT_ID: ''
    AIRWALLEX_API_URL: ''
    AIRWALLEX_CLIENT_ID: ''
    PRIVAT_MONEY_API_URL: ''
    PRIVAT_MONEY_POINT_ID: ''
    PRIVAT_MONEY_BANK_ID: ''
    PRIVAT_MONEY_SALE_TYPE: ''
    PRIVAT_MONEY_DESCRIPTION: ''
    MOKEJIMAI_ADMIN_URL: ''
    REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_URL: ''
    REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_USERNAME: ''
    GEORGIA_RTGS_MESSAGE_SIGNATURE_CERTIFICATE_PATH: ''
    GEORGIA_RTGS_MESSAGE_SIGNATURE_PRIVATE_KEY_PATH: ''
    ACCOUNTING_OPERATION_MANAGER_API_BASE_URL: ''
    ACCOUNTING_OPERATION_MANAGER_API_USERNAME: ''
    GEORGIA_DEBTOR_REGISTRY_BASE_URL: ''
    GEORGIA_DEBTOR_REGISTRY_USERNAME: ''
    CONTIS_TRANSFER_SFTP_HOST: ''
    CONTIS_TRANSFER_SFTP_PORT: ''
    CONTIS_TRANSFER_SFTP_USERNAME: ''
    CONTIS_TRANSFER_PRIVATE_KEY_PATH: ''
    CONTIS_TRANSFER_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS: ''
    CONTIS_TRANSFER_PAYSERA_ACCOUNT_NUMBER_SORT_CODE: ''
    CONTIS_TRANSFER_CLIENT_LOGIN_USERNAME: ''
    CARD_SWITCH_BASE_URL: ''
    REMOTE_CREDENTIALS_CARD_SWITCH_USERNAME: ''
    REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_BASE_URL: ''
    REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_USERNAME: ''
    AIRWALLEX_ACCOUNT_ID: ''
    RECAPTCHA_SITE_KEY: ''
    RECAPTCHA_SITE_SECRET: ''
    CURRENCY_PRICING_URL: ''
    CEPH_API_BASE_URL: ''
    CEPH_API_KEY: ''
    CEPH_API_SECRET: ''
    ACCOUNTING_SOAP_CLIENT_WSDL_FILENAME: ''
    REMOTE_CREDENTIALS_CARD_BASE_URL: ''
    REMOTE_CREDENTIALS_CARD_USERNAME: ''
    OPENAI_ASSISTANT_API_BASE_URL: ''

secret:
  data:
    ACCOUNTING_API_PASSWORD: ''
    AFEX_API_KEY: ''
    AFEX_API_PASSWORD: ''
    BANK_TRANSFER_API_V1_PRIVATE_KEY_PASSWORD: ''
    BANKING_HISTORY_CLIENT_AUTH_PASSWORD: ''
    BROWSER_API_PASSWORD: ''
    CONTIS_CLIENT_3DES_IV_KEY: ''
    CONTIS_CLIENT_3DES_SECRET_KEY: ''
    CONTIS_CLIENT_LOGIN_PASSWORD: ''
    CONTIS_PUSH_NOTIFICATIONS_SECURITY_KEY: ''
    CONTIS_CARDS_MATURITY_INTERVAL: ''
    CONTIS_LEGAL_CLIENT_AGREEMENT_CODE: ''
    CURRENCY_ONE_API_KEY: ''
    CURRENCY_ONE_PRIVATE_KEY_PASSWORD: ''
    DATABASE_PASSWORD: ''
    DATABASE_SLAVE_GATEWAY_PASSWORD: ''
    DOWJONES_PASSWORD: ''
    DOWJONES_RISK_CENTER_PASSWORD: ''
    ELASTICSEARCH_PASSWORD_V1: ''
    FNTT_CLIENT_PASSWORD: ''
    GOOGLE_MAPS_API_KEY: ''
    INFLUXDB_STATISTICS_PASSWORD: ''
    INTIS_API_KEY: ''
    LB_INTEGRATION_SIGNATURE_API_PASSWORD: ''
    LIBRA_CUSTOMER_KEY: ''
    LIBRA_CUSTOMER_SECRET: ''
    LIBRA_PASSWORD: ''
    LOCAL_CREDENTIALS_BLACKLIST_PASSWORD: ''
    LOCAL_CREDENTIALS_CHALLENGE_PASSWORD: ''
    LOCAL_CREDENTIALS_CHECKOUT_PASSWORD: ''
    LOCAL_CREDENTIALS_INFORMATION_PASSWORD: ''
    LOCAL_CREDENTIALS_MOKEJIMAI_PASSWORD: ''
    LOCAL_CREDENTIALS_PARTNER_FRONTEND_PASSWORD: ''
    LOCAL_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD: ''
    LOCAL_CREDENTIALS_RECURRING_PAYMENTS_PASSWORD: ''
    LOCAL_CREDENTIALS_SAVINGS_PASSWORD: ''
    LOCAL_CREDENTIALS_SURVEILLANCE_ENGINE_PASSWORD: ''
    LOCAL_CREDENTIALS_WALLET_API_PASSWORD: ''
    LOCAL_CREDENTIALS_WALLET_FRONTEND_PASSWORD: ''
    LOCAL_CREDENTIALS_GEORGIA_REVENUE_SERVICE_PASSWORD: ''
    LOCAL_CREDENTIALS_SPREADSHEET_UPDATER_PASSWORD: ''
    LOCAL_CREDENTIALS_CREDIT_ONLINE: ''
    MOBILE_PAYMENTS_API_PASSWORD: ''
    PAYZA_PASSWORD: ''
    PDF_SIGNER_KEY: ''
    RABBIT_MQ_EXTRA_PASSWORD: ''
    RABBIT_MQ_PASSWORD: ''
    REDIS_PASSWORD: ''
    REMOTE_CREDENTIALS_AUTH_API_PASSWORD: ''
    REMOTE_CREDENTIALS_BLACKLIST_RESTRICTION_PASSWORD: ''
    REMOTE_CREDENTIALS_CARD_GATEWAY_PASSWORD: ''
    REMOTE_CREDENTIALS_CHALLENGE_PASSWORD: ''
    REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD: ''
    REMOTE_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD: ''
    SECRET: ''
    SENTRY_DSN: ''
    SINGLE_WINDOW_ADMIN_API_PASSWORD: ''
    SINGLE_WINDOW_REPORT_API_PASSWORD: ''
    SINGLE_WINDOW_TRANSFER_API_PASSWORD: ''
    TNT_CLIENT_LOGIN_PASSWORD: ''
    TRANSFERMATE_CLIENT_PASSWORD: ''
    TRANSFERMATE_CLIENT_SECRET_WORD: ''
    USER_IDENTIFIER_HASH_IDS_SALT: ''
    VMI_ACCOUNT_REPORT_EMAIL_PROCESSOR_IMAP_PASSWORD: ''
    VMI_PAYMENTS_VMI_MGW_CLIENT_PRIVATE_KEY_PASSWORD: ''
    VMI_PAYMENTS_VMI_MGW_CLIENT_SOAP_PASSWORD: ''
    VMI_REPORT_PASS: ''
    VMI_REPORT_CONTIS_PASS: ''
    WALLET_OAUTH_CLIENT_MAC_SECRET: ''
    WMXI_LIGHT_PRIVATE_KEY_PASS: ''
    RIA_FTP_PASSWORD: ''
    CURRENCYCLOUD_API_KEY: ''
    DHL_CLIENT_LOGIN_PASSWORD: ''
    AUTH_API_PRIVATE_KEY: ''
    AUTH_API_PUBLIC_KEY: ''
    AIRWALLEX_API_KEY: ''
    LOCAL_CREDENTIALS_GEORGIA_ENFORCEMENT_BUREAU_PASSWORD: ''
    LOCAL_CREDENTIALS_ACCOUNTING_OPERATION_MANAGER_PASSWORD: ''
    LOCAL_CREDENTIALS_OPEN_BANKING_API_PASSWORD: ''
    LOCAL_CREDENTIALS_RISK_LEVEL_PASSWORD: ''
    LOCAL_CREDENTIALS_CARD_PASSWORD: ''
    LOCAL_CREDENTIALS_LOANS_API_PASSWORD: ''
    LOCAL_CREDENTIALS_CARD_SWITCH_PASSWORD: ''
    LOCAL_CREDENTIALS_CHECKOUT_MERCHANT_PROJECT_PASSWORD: ''
    LOCAL_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_PASSWORD: ''
    ACCOUNTING_OPERATION_MANAGER_API_PASSWORD: ''
    CONTIS_TRANSFER_CLIENT_LOGIN_PASSWORD: ''
    CONTIS_TRANSFER_PUSH_NOTIFICATIONS_SECURITY_KEY: ''
    GEORGIA_DEBTOR_PASSWORD: ''
    GEORGIA_RTGS_MESSAGE_SIGNATURE_PRIVATE_KEY_PASSWORD: ''
    REMOTE_CREDENTIALS_CARD_SWITCH_PASSWORD: ''
    REMOTE_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_PASSWORD: ''
    REMOTE_CREDENTIALS_GEORGIA_REVENUE_SERVICE_PASSWORD: ''
    MOKEJIMAI_PUBLIC_KEY: ''
    PRIVAT_MONEY_BINARY_SECURITY_TOKEN: ''
    PRIVAT_MONEY_PRIVATE_KEY: ''
    PRIVAT_MONEY_PUBLIC_KEY: ''
    SINGLE_WINDOW_CLIENT_CERTIFICATE: ''
    SINGLE_WINDOW_GLOBUS_BANK_SFTP_PRIVATE_KEY: ''
    SINGLE_WINDOW_PRIVATE_KEY: ''
    SINGLE_WINDOW_ROOT_CA: ''
    CONTIS_TRANSFER_PRIVATE_KEY: ''
    CURRENCY_ONE_PRIVATE_KEY: ''
    CLIENT_NOTIFICATION_CALLBACK_PRIVATE_KEY_CONTENT: ''
    EVP_PRIVATE_KEY: ''
    EVP_PUBLIC_KEY: ''
    LB_PUBLIC_KEY: ''
    NBG_PAYSERA_AGENT_CERTIFICATE: ''
    NBG_PAYSERA_AGENT_PRIVATE_KEY: ''
    REMOTE_CREDENTIALS_RISK_LEVEL_PASSWORD: ''
    CHAT_GPT_API_BASE_URL: ''
    CHAT_GPT_API_KEY: ''
    ANTHROPIC_API_BASE_URL: ''
    ANTHROPIC_API_KEY: ''
    REMOTE_CREDENTIALS_CHECKOUT_BASE_URL: ''
    REMOTE_CREDENTIALS_CHECKOUT_USERNAME: ''
    REMOTE_CREDENTIALS_CHECKOUT_PASSWORD: ''
    CONTEXT_AWARE_TRANSLATOR_BASE_URL: ''
    LOCAL_CREDENTIALS_ACCOUNTING_EVENTS_PASSWORD: ''
    REMOTE_CREDENTIALS_CARD_PASSWORD: ''
    REDASH_HOST: ''
    REDASH_API_KEY: ''
    OPENAI_ASSISTANT_API_KEY: ''
    VMI_REPORT_VMI_PUBLIC_KEY_FILE: ''

imagePullSecret: gitlab-image-pull-secret

replicas: 1

image:
  nginx:
    repository: gitlab.paysera.net:5050/paysera/app-evpbank/nginx
    tag: init
  fpm:
    repository: gitlab.paysera.net:5050/paysera/app-evpbank/fpm
    tag: init
  job:
    repository: gitlab.paysera.net:5050/paysera/app-evpbank/job
    tag: init

resources:
  nginx:
    requests:
      memory: 100Mi
      cpu: 10m
    limits:
      memory: 100Mi
      cpu: 50m
  fpm:
    requests:
      memory: 1Gi
      cpu: 750m
    limits:
      memory: 1Gi
      cpu: 1000m
  job:
    requests:
      cpu: 100m
      memory: 1Gi
    limits:
      cpu: 200m
      memory: 1Gi

modifyNfsVolumes:
  enabled: false

runMigrations:
  command: "app/console doctrine:migrations:migrate --no-interaction"

cronjobs:
  # Nova
  ## Persistable events
  - name: recover-stuck-priority-events
    schedule: '*/5 * * * *'
    command: 'app/console evp:persistable-event:recover-stuck-priority-events --quiet || true'
  ## Bank account
  - name: account-publish-money-movement-requests
    schedule: '*/2 * * * *'
    command: 'bin/console --quiet evp:bank-account:publish-statement-money-movement-requests || true'
  ## Transfer bundle
#  - name: cancel-not-signed-transfers
#    schedule: '35 * * * *'
#    command: 'bin/console --quiet evp:transfer:cancel-not-signed || true'
#  - name: clean-up-transfer-in-events
#    schedule: '16 */3 * * *'
#    command: 'bin/console --quiet evp:bank_transfer:clean-up-transfer-in-events || true'
#  - name: fail-new-transfers
#    schedule: '* * * * *'
#    command: 'bin/console --quiet evp:transfer:fail-new-transfers || true'
#  - name: resurrect-transfer-in-events
#    schedule: '*/10 * * * *'
#    command: 'app/console evp:bank_transfer:resurrect-transfer-in-events --quiet -- "-10 minutes" || true'
#  - name: revoke-transfer-import
#    schedule: '0 */12 * * *'
#    command: 'app/console paysera:transfer-import:revoke-processing --quiet'
  ## Client bundle
  - name: check-client-partner-changes
    schedule: '5 * * * *'
    command: 'app/console evp:client:check-client-partner-changes || true'
#  - name: fix-client-locale
#    schedule: '*/5 * * * *'
#    command: 'app/console evp:client:fix-client-locale || true'
  ## Turnovers
  - name: create-statement-turnover
    schedule: '*/5 * * * *'
    command: 'app/console --quiet paysera:statement-turnover:create-statement-turnover-from-registry || true'
  ## SEPA INST
#  - name: process-si-cancellation-request-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:cancellation-request-out:process'
#  - name: process-investigation-resolution-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:investigation-resolution-out:process'
#  - name: process-transaction-in-return
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:transaction-in-return:process'
#  - name: process-payment-status-request-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:payment-status-request-out:process'
#  - name: process-unhandled-messages
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:process-unhandled-messages'
#  - name: process-unhandled-messages-by-type
#    schedule: '0 15 * * 5'
#    command: 'app/console paysera:sepa-instant:process-unhandled-messages-by-type debit_credit_notification'
#  - name: unhandled-messages-cleaner
#    schedule: '* * * * *'
#    command: 'app/console paysera:sepa-instant:unhandled-messages-cleaner'
#  - name: process-in-post-prepare
#    schedule: '*/1 * * * *'
#    command: "app/console evp:transfer:process-transfer-in-post-prepare 'today' 'yesterday'"
  ## SEPA
#  - name: process-transfer-in-returns
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:process-transfer-in-returns --quiet'
#  - name: transaction-out-processor
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:transaction-out-processor --quiet'
#  - name: process-cancellation-request-out
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:cancellation-request-out-processor --quiet'
#  - name: process-investigate-resolution-out
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:investigation-resolution-out-processor --quiet'
#  - name: process-claim-non-receipt-in-resp
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:claim-non-receipt-in-response-processor --quiet'
#  - name: process-claim-non-receipt-out-resp
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:claim-non-receipt-out-request-processor --quiet'
#  - name: unhandled-messages-cleaner
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:unhandled-messages-cleaner'
#  - name: send-status-report-request-response
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:send-status-report-request-response'
#  - name: sepa-process-unhandled-messages
#    schedule: '* * * * *'
#    command: 'app/console evp:sepa:process-unhandled-messages'
#  - name: sepa-unresolved-in-cancellation
#    schedule: '0 * * * *'
#    command: 'app/console evp:sepa:process-cancellation-request-in-unresolved'
  ## Currency One
  - name: co-send-transfers
    schedule: '* * * * *'
    command: 'app/console paysera:currency-one:send-transfers --quiet'
  - name: co-import-statements
    schedule: '* * * * *'
    command: 'app/console paysera:currency-one:import-statements --quiet'
  - name: co-process-statements
    schedule: '* * * * *'
    command: 'app/console paysera:currency-one:process-statements --quiet'
#  - name: co-save-day-balances
#    schedule: '0 3 * * *'
#    command: 'app/console paysera:currency-one:save-day-balances --quiet'
#  - name: co-send-day-balances
#    schedule: '5 4 * * *'
#    command: 'app/console paysera:currency-one:send-day-balances --quiet'
#  - name: co-resend-waiting-funds-transaction
#    schedule: '*/5 * * * *'
#    command: 'app/console paysera:currency-one:resend-waiting-funds-transactions --quiet'
#  - name: co-fix-failed-transactions
#    schedule: '*/5 * * * *'
#    command: 'app/console paysera:currency-one:fix-failed-transactions --quiet'
  ## Target2
#  - name: process-t2-transaction-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2:transaction-out-processor'
#  - name: process-t2-statement
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2:statement-processor'
#  - name: process-t2-unresolved-transactions
#    schedule: '*/5 * * * *'
#    command: 'app/console paysera:target2:process-unresolved-transactions --quiet'
#  - name: process-t2-collect-participants
#    schedule: '14 4 * * *'
#    command: 'app/console paysera:target2:collect-participants --quiet'
  ## Target2 RTGS
#  - name: t2-rtgs-process-cancellation-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:cancellation-request-out-processor'
#  - name: t2-rtgs-create-payment-return-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:payment-return-out-creation-processor'
#  - name: t2-rtgs-process-payment-return-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:payment-return-out-processor'
#  - name: t2-rtgs-process-transaction-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:transaction-out-processor'
#  - name: t2-rtgs-create-transaction-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:transaction-out-creation-processor'
#  - name: t2-rtgs-process-unhandled
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:process_unhandled_messages'
#  - name: t2-rtgs-process-invest-resolution
#    schedule: '* * * * *'
#    command: 'app/console paysera:target2_rtgs:resolution_of_investigation_out_processing --quiet'
#  - name: t2-rtgs-process-interbank-out
#    schedule: '*/15 * * * *'
#    command: 'app/console paysera:target2_rtgs:interbank-payment-out-processor'
  ## GeorgiaRTGS
#  - name: g-rtgs-process-ready
#    schedule: '* * * * *'
#    command: 'app/console paysera:georgia-rtgs:process-ready-transfers'
#  - name: g-rtgs-process-return-out
#    schedule: '* * * * *'
#    command: 'app/console paysera:georgia-rtgs:payment-return-out-processor'
#  - name: g-rtgs-send-unifi
#    schedule: '* * * * *'
#    command: 'app/console paysera:georgia-rtgs:send-unifi-transactions'
#  - name: g-rtgs-send-swift
#    schedule: '* * * * *'
#    command: 'app/console paysera:georgia-rtgs:send-swift-transactions'
#  - name: g-rtgs-send-swift-interbank
#    schedule: '* * * * *'
#    command: 'app/console paysera:georgia-rtgs:send-swift-interbank-transactions'
  ## GlobusBankBundle
  - name: globus-register-transfers
    schedule: '0,6,12,18,24,30,36,42,48,54 * * * *'
    command: 'app/console paysera:globus-bank:register-transfers'
  - name: globus-confirm-transfers
    schedule: '3,9,15,21,27,33,39,45,51,57 * * * *'
    command: 'app/console paysera:globus-bank:confirm-transfers'
  - name: globus-process-transaction-reports
    schedule: '5,17,35,53 * * * *'
    command: 'app/console paysera:globus_bank:process-transaction-reports "1 day ago" "now"'
  - name: globus-process-report-transactions
    schedule: '1,7,13,19,25,31,37,43,49,55 * * * *'
    command: 'app/console paysera:globus_bank:process-report-transactions'
  - name: globus-sync-confirmed-transfers
    schedule: '0 * * * *'
    command: 'app/console paysera:globus-bank:sync-confirmed-transfers'
  - name: globus-fail-transfers
    schedule: '*/10 * * * *'
    command: 'app/console paysera:globus-bank:fail-transfers'
  - name: globus-sync-transfers
    schedule: '*/15 * * * *'
    command: 'app/console paysera:globus-bank:sync-transfers'
  - name: globus-import-bank-statements
    schedule: '0 6 * * *'
    command: 'app/console paysera:globus-bank:import-bank-statements'
  - name: globus-monitor-history-statement
    schedule: '10 6 * * *'
    command: 'app/console paysera:globus-bank:monitor-bank-history-statement-report-delivery-overview'
  - name: globus-monitor-statement-alert
    schedule: '10 6 * * 2-6'
    command: 'app/console paysera:globus-bank:monitor-statement-report-delivery-alert'
  - name: globus-publish-bank-statements
    schedule: '30 6 * * *'
    command: 'app/console paysera:globus-bank:publish-bank-statements'
  - name: globus-send-day-balances
    schedule: '40 7 * * 1-5'
    command: 'app/console paysera:globus-bank:send-day-balances'
  - name: globus-monitoring-problems
    schedule: '0 * * * *'
    command: 'app/console paysera:globus-bank:monitoring-problems'
  ## Card V2
  - name: cv2-ensure-cards-balances
    schedule: '*/10 * * * *'
    command: 'app/console paysera:card_v2:ensure-cards-balances --quiet'
  ## Debtor
#  - name: debtor-create-report
#    schedule: '0 3 27 * *'
#    command: 'php -d memory_limit=2G app/console evp:debtor:create-report --quiet'
#  - name: debtor-export-report
#    schedule: '0 20 27-31 * *'
#    command: 'php -d memory_limit=4G app/console evp:debtor:export-report > /home/<USER>/files/debtors.csv'
    ## AbaCodesBundle
  - name: aba-codes-downloader
    schedule: '0 0 4 1 *'
    command: ' app/console paysera:aba-codes-downloader --quiet'
  ## TechVentures
#  - name: techventures-monitor-iban-requests
#    schedule: '0 8 * * *'
#    command: 'app/console paysera:techventures:monitor-iban-requests'
#  - name: techventures-create-monthly-charges
#    schedule: '0 1 1-4 * *'
#    command: 'app/console paysera:techventures:create-monthly-charges'
#  - name: techventures-monitor-iban-charges
#    schedule: '0 8 5 * *'
#    command: 'app/console paysera:techventures:monitor-iban-charges'
#  - name: techventures-deactivate-unpaid-ibans
#    schedule: '0 8 25 * *'
#    command: 'app/console paysera:techventures:deactivate-unpaid-ibans'
  ## PrivatbankBundle
  - name: privatbank-register-transfers
    schedule: '0,6,12,18,24,30,36,42,48,54 * * * *'
    command: 'app/console paysera:privatbank:register-transfers'
  - name: privatbank-confirm-transfers
    schedule: '3,9,15,21,27,33,39,45,51,57 * * * *'
    command: 'app/console paysera:privatbank:confirm-transfers'
  - name: privatbank-process-transaction-reports
    schedule: '0 8 * * *'
    command: 'app/console paysera:privatbank:process-transaction-reports -- "-1 day"'
  - name: privatbank-process-statement-reports
    schedule: '15 8 * * *'
    command: 'app/console paysera:privatbank:process-statement-reports'
  - name: privatbank-process-report-transactions
    schedule: '1,7,13,19,25,31,37,43,49,55 * * * *'
    command: 'app/console paysera:privatbank:process-report-transactions'
  - name: privatbank-fail-transfers
    schedule: '*/10 * * * *'
    command: 'app/console paysera:privatbank:fail-transfers'
  - name: privatbank-succeed-cash-transfers
    schedule: '9 * * * *'
    command: 'app/console paysera:privatbank:succeed-cash-transfers'
  - name: privatbank-payout-refund
    schedule: '15 * * * *'
    command: 'app/console paysera:privatbank:payout-refund'
  - name: privatbank-send-day-balances
    schedule: '40 7 * * *'
    command: 'app/console paysera:privatbank:send-day-balances'
  - name: privatbank-update-day-balances
    schedule: '6 * * * *'
    command: 'app/console paysera:privatbank:update-day-balances'
  - name: privatbank-transfers-monitoring
    schedule: '0 * * * *'
    command: 'app/console paysera:privatbank:transfers-monitoring'
  ## ClientChargeBundle
#  - name: client-charge-publish-monthly-charge
#    schedule: '2 * * * *'
#    command: 'app/console paysera:client-charge:publish-monthly-charge'
  ## BankChargeBundle
#  - name: bank-charge-monitor-stuck-processing
#    schedule: '*/5 * * * *'
#    command: 'app/console evp:bank-charge:monitor-stuck-processing'
#  - name: bank-charge-fix-stuck-specific-account-charges
#    schedule: '*/5 * * * *'
#    command: 'app/console evp:charge:fix-stuck-specific-account-charges'
  ## ClientAverageBalanceBundle
#  - name: client-average-balance-calculate-daily-client-average-balance
#    schedule: '0 1 * * *'
#    command: 'app/console paysera:client_average_balance:calculate-daily-client-average-balance'
  ## ClientMonthlyChargeFeeBundle
#  - name: client-monthly-charge-fee-publish-client-monthly-charge-fee
#    schedule: '0 2 1 * *'
#    command: 'app/console paysera:client_monthly_charge_fee:publish-client-monthly-charge-fee'
#  - name: client-monthly-charge-fee-process-client-monthly-transfer-commission
#    schedule: '* * 1 * *'
#    command: 'app/console paysera:client_monthly_charge_fee:process-client-monthly-transfer_commission'
  ## TransferPlanBundle
#  - name: transfer-plan-charge
#    schedule: '1 0,1 1 * *'
#    command: 'app/console evp:transfer_plan:charge'
    ## Libra
  - name: libra-import-statements-daily
    schedule: '0 8 * * *'
    command: 'app/console paysera:libra:import-statements --periodFrom=P1D --periodTo=P1D'
  - name: libra-import-statements-every-5-minutes
    schedule: '*/5 * * * *'
    command: 'app/console paysera:libra:import-statements'
  - name: libra-process-transfers
    schedule: '*/10 * * * *'
    command: 'app/console paysera:libra:process-transfers'
  - name: libra-check-sent-transfers-status
    schedule: '* * * * *'
    command: 'app/console paysera:libra:check-sent-transfers-status'
  - name: libra-monitor-sent-transactions
    schedule: '* * * * *'
    command: 'app/console paysera:libra:monitor-sent-transactions-without-payment-id'
  - name: libra-reprocess-stuck-new-transactions
    schedule: '*/15 * * * *'
    command: "app/console paysera:libra:reprocess-stuck-new-transactions '1 hour'"
#  - name: libra-send-pending-kyc-request
#    schedule: '0 18 * * 1-5'
#    command: 'app/console paysera:libra:send-pending-kyc-request'

  # Dragons
  ## AccountingBundle
  - name: accounting-publish-bank-operations
    schedule: '* * * * *'
    command: 'app/console evp:accounting:publish-bank-operations --quiet'
  - name: accounting-bank-account-reprocess-operations-transfer
    schedule: '0 * * * *'
    command: 'app/console evp_bank_account:reprocess_operations:transfer --quiet'
  ## AccountingDiscrepancyBundle
  - name: accounting-discrepancy-daily-gain-loss-generation
    schedule: '0 3 * * *'
    command: 'app/console paysera:accounting-discrepancy:daily-gain-loss-generation ''paysera_ge'' --quiet'
  ## ContisAccountingBundle
  - name: contis-accounting-process-accounting
    schedule: '*/5 * * * *'
    command: 'app/console evp:contis:process-accounting --quiet'
  - name: contis-accounting-process-partners-accounting
    schedule: '*/5 * * * *'
    command: 'app/console evp:contis:process-partners-accounting --quiet'
  - name: contis-accounting-update-status-partners-accounting
    schedule: '15 3 28 * *'
    command: 'app/console evp:contis:update-status-partners-accounting yesterday --quiet'
  - name: contis-accounting-republish-partners-accounting
    schedule: '*/40 21-23,0-6 * * *'
    command: 'app/console evp:contis:republish-partners-accounting --quiet'
  - name: contis-accounting-send-accounting-report
    schedule: '0 10 * * *'
    command: 'app/console evp:contis:send-accounting-report --quiet'
  ## PartnerBundle
  - name: partner-client-partner-change
    schedule: '57 3,5,7,13,15,17 * * *'
    command: 'app/console paysera:partner:client-partner-change'
  - name: accounting-publish-new-remote-operation-requests
    schedule: '33 */6 * * *'
    command: 'app/console evp:accounting:publish-new-remote-operation-requests'
  - name: accounting-publish-failed-remote-operation-requests
    schedule: '*/15 22-23,0-6 * * *'
    command: 'app/console evp:accounting:publish-failed-remote-operation-requests'
  - name: partner-handle-debt-settlements
    schedule: '3,33 * * * *'
    command: 'app/console paysera:partner:handle-debt-settlements'

  # CardIO
  ## ContisBundle
  - name: contis-process-new-cards
    schedule: '*/10 * * * *'
    command: 'app/console evp:contis:process-queued-cards --quiet'
  - name: contis-process-pending-cards
    schedule: '*/10 * * * *'
    command: 'app/console evp:contis:process-pending-cards --quiet'
#  - name: contis-ensure-cards-balances
#    schedule: '0 * * * *'
#    command: 'app/console evp:contis:ensure-cards-balances --quiet'
#  - name: contis-accounts-synchronize
#    schedule: '0 5 * * *'
#    command: 'app/console evp:contis:synchronize-accounts --quiet'

  # Team-2
  ## QuestionnaireBundle
#  - name: fix-questionnaires-stuck-in-identification-pending-status
#    schedule: '0 1 * * *'
#    command: 'app/console evp:questionnaire:fix-stuck-questionnaires'
#  - name: questionnaire-documents-cleanup
#    schedule: '0 0 * * *'
#    command: 'app/console evp:questionnaire:cleanup-stalled-questionnaire-documents --quiet'
#  - name: monitor-questionnaires-count
#    schedule: '*/5 * * * *'
#    command: 'app/console evp:questionnaire:monitor-questionnaires-count'
#  - name: update-needed-questionnaire
#    schedule: '8 * * * * '
#    command: 'app/console evp:questionnaire:transition-to-update-needed --quiet'
#  - name: process-abandoned-questionnaire
#    schedule: '15 0 10 * *'
#    command: 'app/console evp:questionnaire:process-abandoned-questionnaire --quiet'
#  - name: restrict-rejected-questionnaires
#    schedule: '30 1 * * *'
#    command: 'app/console evp:questionnaire:restrict-rejected-questionnaires paysera_al --quiet'

#Questionnaire AI
  - name: rc-questionnaire-ai-audits-clear-prompts
    schedule: '0 0 * * *'
    command: 'app/console evp:questionnaire_ai:audit:clear-prompts'
  - name: rc-questionnaire-ai-audits-publish-audits
    schedule: '* 3-23 * * 6'
    command: 'app/console evp:questionnaire_ai:audit:publish --limit=500'

jobs:
  - name: rc-questionnaire-revocation
    replicas: 1
    command: 'app/console rabbitmq:consumer job_questionnaire_revocation'
  - name: rc-rejected-questionnaire-restrictions
    replicas: 1
    command: 'app/console rabbitmq:consumer job_rejected_questionnaire_process_restrictions'
  - name: rc-questionnaire-client-activity
    replicas: 1
    command: 'app/console rabbitmq:consumer job_client_activity_questionnaire'
  - name: rc-event-blacklist-restriction
    replicas: 1
    command: 'app/console rabbitmq:consumer event_blacklist_restriction'
  - name: rc-save-banking-history-address
    replicas: 1
    command: 'app/console rabbitmq:consumer event_save_banking_history_address --memory-limit=200'
  - name: rc-event-user-phone
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_phone'
  - name: rc-review-pending-questionnaires
    replicas: 1
    command: 'app/console rabbitmq:consumer job_review_pending_questionnaires'
  - name: rc-natural-beneficiary-check
    replicas: 1
    command: 'app/console rabbitmq:consumer job_natural_beneficiary_risk_center_check'
  - name: rc-user-contact-information
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_contact_information'
  - name: rc-questionnaire-process-restrictions
    replicas: 1
    command: 'app/console rabbitmq:consumer job_questionnaire_process_restrictions'
  - name: rc-process-abandoned-questionnaire
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_abandoned_questionnaire'
  - name: rc-questionnaire-submitted-process
    replicas: 1
    command: 'app/console rabbitmq:consumer job_questionnaire_submitted_process'
  - name: rc-pep-related-questionnaire
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_pep_related_questionnaire'
  - name: rc-questionnaire-translations
    replicas: 1
    command: 'app/console rabbitmq:consumer job_translate_questionnaire_fields'
  - name: rc-questionnaire-translations-high-load
    replicas: 10
    command: 'app/console rabbitmq:consumer job_translate_questionnaire_fields_high_load'
  - name: rc-risk-level-calculated
    replicas: 1
    command: 'app/console rabbitmq:consumer event_risk_level_calculated --memory-limit=200'
  - name: rc-high-load-risk-level-calculated
    replicas: 1
    command: 'app/console rabbitmq:consumer event_high_load_risk_level_calculated --memory-limit=200'
  - name: rc-transfer-sign-async
    replicas: 5
    command: 'app/console rabbitmq:consumer job_gateway_transfer_async_sign_request --memory-limit=200'
#  - name: rc-user-removal
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_user_removal'
  # CipherPol
  ##VMI
  - name: daemon-vmi-payments-process-paid-payments
    replicas: 1
    command: 'app/console evp:vmi-payments:process-paid-payments --deamonize --processing-time=3600 120'
  - name: rc-vmi-payments-paid
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_vmi_payments_paid_payment'
  ## Banking history rabbit consumers
  - name: rc-save-bh-transfer
    replicas: 1
    command: 'app/console rabbitmq:consumer job_save_banking_history_transfer'
  - name: rc-save-bh-transfer-sepa
    replicas: 1
    command: 'app/console rabbitmq:consumer job_save_banking_history_transfer_sepa'
  - name: rc-save-bh-transfer-sepa-inst-in
    replicas: 1
    command: 'app/console rabbitmq:consumer job_save_banking_history_transfer_sepa_inst_in'
  - name: rc-save-bh-transfer-sepa-inst-out
    replicas: 1
    command: 'app/console rabbitmq:consumer job_save_banking_history_transfer_sepa_inst_out'
  - name: rc-save-bh-transfer-instant
    replicas: 1
    command: 'app/console rabbitmq:consumer job_save_banking_history_transfer_instant'
  - name: rc-save-bh-transfer-migration
    replicas: 1
    command: 'app/console rabbitmq:consumer high_load_job_save_banking_history_transfer_migration'
  - name: rc-import-bh-client
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_import_banking_history_client'
  - name: rc-import-bh-client-migration
    replicas: 1
    command: 'app/console rabbitmq:consumer high_load_job_gateway_import_banking_history_client_migration'
  - name: rc-update-bh-client
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_update_banking_history_client'
  ## BlackList
  - name: rc-gateway-blacklist-check
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_blacklist_check'
  - name: rc-gateway-blacklist-user-report
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_blacklist_check_user_generate_report'
  - name: rc-gateway-blacklist-user-report-file
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_blacklist_check_user_generate_report_file'
  - name: rc-gateway-blacklist-updated-profile
    replicas: 1
    command: 'app/console rabbitmq:consumer job_check_blacklist_updated_profile'
  - name: rc-gateway-blacklist-incremental-import
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_blacklist_dow_jones_incremental_import'
  - name: rc-gateway-blacklist-full-import
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_blacklist_dow_jones_full_import'
  ## Transfer surveillance inspection rabbit consumers
  - name: rc-transfer-inspection-in
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in'
  - name: rc-transfer-inspection-internal
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_internal'
  - name: rc-transfer-inspection-audit
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_audit'
  - name: rc-transfer-inspection-whitelisted
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_whitelisted'
  - name: rc-transfer-inspection-auto-accept
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_auto_accept_transfer_inspection'
  - name: rc-transfer-inspection
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection'
  - name: rc-transfer-inspection-handle-audit
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_handle_audit'
  - name: rc-transfer-inspection-wl-approve
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_whitelist_approve'
  - name: rc-transfer-inspection-inst-intrn
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_inst_internal'
  - name: rc-transfer-inspection-in-clearing
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in_clearing'
  - name: rc-transfer-inspection-sepa-inst-in
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_in'
  - name: rc-transfer-inspection-sepa-instout
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_out'
  - name: rc-transfer-inspection-mac-mic-in
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_macro_micro_in'
  ## Notifications
  - name: notification-process-events
    replicas: 1
    command: 'app/console evp:notification:process-events --deamonize 20'
  - name: notification-process-notifications
    replicas: 1
    command: 'app/console evp:notification:process-notifications --deamonize 10'
  ## Transfer details translation
  - name: rc-translate-transfer-details
    replicas: 1
    command: 'app/console rabbitmq:consumer job_translate_transfer_details'
  ## AI Assistant
  - name: rc-ai-assistant-task-completion
    replicas: 1
    command: 'app/console rabbitmq:consumer job_ai_assistant_task_completion'

  # Nova
  ## Bank
#  - name: rc-save-swift
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_save_swift'
  ## Bank account
  - name: rc-statement-money-movement-request
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_statement_money_movement_request'
  - name: rc-account-activation
    replicas: 1
    command: 'app/console rabbitmq:consumer job_account_activation'
  ## IBAN alias
  - name: rc-issue-iban-alias
    replicas: 1
    command: 'app/console rabbitmq:consumer job_issue_iban_alias'
  - name: rc-deactivate-iban-alias
    replicas: 1
    command: 'app/console rabbitmq:consumer job_deactivate_iban_alias'
  ## Transfer bundle
  - name: process-transfer-currency-convert
    replicas: 1
    command: 'app/console evp:transfer:process:distributed:currency_convert --deamonize 4'
  - name: publish-transfer-in
    replicas: 1
    command: 'app/console evp:bank_transfer:publish-transfer-in-events --deamonize 2'
  - name: process-transfer-in
    replicas: 1
    command: 'app/console evp:transfer:process:in --deamonize 60'
  - name: process-transfer-internal
    replicas: 1
    command: 'app/console evp:transfer:process:internal --deamonize 2'
  - name: post-prepared-transfer-in
    replicas: 1
    command: 'app/console evp:transfer:process-transfer-in-post-prepare "today" "yesterday" --deamonize 6'
  - name: process-distributed-internal
    replicas: 1
    command: 'app/console evp:transfer:process:distributed:internal --deamonize 10'
  - name: process-transfer-out
    replicas: 1
    command: 'app/console evp:transfer:process:distributed:out --deamonize 4'
  - name: process-transfer-out-success
    replicas: 1
    command: 'app/console evp:transfer:process:distributed:out:success --deamonize 4'
  - name: process-transfer-out-tax
    replicas: 1
    command: 'app/console evp:transfer:tax:ready --deamonize 60'
#  - name: process-transfer-waiting
#    replicas: 1
#    command: 'app/console evp:transfer:process:waiting-transfers --deamonize 60'
#  - name: rc-revoke-restricted-client-transfers
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_revoke_restricted_client_transfers'
#  - name: rc-resurrect-waiting-funds-transfer
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_resurrect_waiting_funds_transfer'
  - name: rc-profit-from-conversion
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_profit_from_conversion'
  - name: rc-high-load-job-maintenance
    replicas: 1
    command: 'app/console rabbitmq:consumer high_load_job_maintenance --memory-limit=200'
  - name: rc-process-transfer-in-event
    replicas: 1
    command: 'app/console rabbitmq:consumer job_transfer_in_event_process'
  - name: rc-process-transfer-import
    replicas: 1
    command: 'app/console rabbitmq:consumer job_gateway_process_transfer_import'
  ## Client bundle
#  - name: rc-client-cache-user-information
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_client_cache_user_information'
  - name: rc-user-events
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_user'
  - name: rc-user-email
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_email'
  - name: rc-user-inactivity
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_user_inactivity'
  - name: rc-user-identity-document
    replicas: 1
    command: 'app/console rabbitmq:consumer event_user_identity_document'
#  - name: rc-user-merge
#    replicas: 1
#    command: 'app/console rabbitmq:consumer event_user_merge'
  ## Bank hold
#  - name: release-high-load-holds
#    replicas: 1
#    command: 'app/console evp:hold:high_load_release:process --deamonize 5'
  - name: process-holds
    replicas: 1
    command: 'app/console evp:hold:process --deamonize 5'
  - name: release-holds
    replicas: 1
    command: 'app/console evp:hold:release:process --deamonize 5'
  - name: rc-isolation-notification
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_isolation_notification'
  ## Bank permission bundle
#  - name: check-ended-permissions
#    replicas: 1
#    command: 'app/console evp:permission:check-ended --deamonize 15'
  ## Client allowance bundle
  - name: rc-update-client-allowance
    replicas: 1
    command: 'app/console rabbitmq:consumer job_update_client_allowance'
  - name: rc-delete-client-allowance
    replicas: 1
    command: 'app/console rabbitmq:consumer job_delete_client_allowance'
  ## Turnovers
  - name: rc-turnover-calculation
    replicas: 1
    command: 'app/console rabbitmq:consumer job_turnover_calculation'
  - name: rc-create-statement-turnover
    replicas: 1
    command: 'app/console rabbitmq:consumer job_create_statement_turnover --memory-limit=400'
  - name: rc-intermediate-statement-turnover
    replicas: 1
    command: 'app/console rabbitmq:consumer high_load_job_create_statement_turnover --memory-limit=400'
  ## Refund
  - name: process-refund
    replicas: 1
    command: 'app/console evp:refund:process --deamonize 60'
  ## Persistable events
  - name: publish-persistable-event
    replicas: 1
    command: 'app/console evp:persistable-event:publish --deamonize 2'
  - name: rc-process-persistable-event
    replicas: 1
    command: 'app/console rabbitmq:consumer job_persistable_event_process_persistable_event'
  - name: rc-process-priority-persistable-event
    replicas: 1
    command: 'app/console rabbitmq:consumer job_persistable_event_process_priority_persistable_event'
  - name: clean-up-persistable-event
    replicas: 1
    command: 'app/console evp:persistable-event:clean-up --deamonize 3600'
  ## SEPA INST
  - name: publish-ready-transfers
    replicas: 1
    command: 'app/console paysera:sepa-instant:ready-transfers:publish --deamonize 2'
#  - name: rc-process-status-report
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_status_report'
  - name: rc-process-create-transfer
    replicas: 1
    command: 'app/console rabbitmq:consumer process_sepa_instant_credit_transfer'
#  - name: rc-send-transaction-out
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_sepa_instant_send_transaction_out'
#  - name: rc-archive-message
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_sepa_instant_archive_message'
#  - name: rc-process-cancellation-request
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_cancellation_request'
#  - name: rc-debit-credit-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_debit_credit_notification'
#  - name: rc-investigation-resolution
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_investigation_resolution'
  - name: rc-process-participant
    replicas: 1
    command: 'app/console rabbitmq:consumer process_sepa_instant_participant'
#  - name: rc-process-payment-return
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_payment_return'
#  - name: rc-status-request
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_status_request'
#  - name: rc-instant-liquidity
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_liquidity'
#  - name: rc-participant-unavailability
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_participant_unavailability'
#  - name: rc-liquidity-transfer-request
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_liquidity_transfer_request'
#  - name: rc-liquidity-position-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_sepa_instant_liquidity_position_notification'
  ## SEPA
#  - name: rc-lb-process-liquidity
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_liquidity'
#  - name: rc-lb-process-participants-list
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_sepa_participants_list'
#  - name: rc-lb-process-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_notification'
#  - name: rc-lb-process-errors
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_errors'
#  - name: rc-lb-process-status-report
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_status_report'
#  - name: rc-lb-process-credit-transfer
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_credit_transaction'
#  - name: rc-lb-process-payment-return
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_payment_return'
#  - name: rc-lb-process-claim-correction
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_claim_value_date_correction'
#  - name: rc-lb-process-status-update
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_request_for_status_update'
#  - name: rc-lb-process-claim-non-receipt
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_claim_non_receipt'
#  - name: rc-lb-process-cancellation-request
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_cancellation_request'
#  - name: rc-lb-process-investig-resolution
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_investigation_resolution'
#  - name: rc-lb-process-balance-report
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_balance_report'
#  - name: rc-lb-process-investig-result
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_investigation_result'
#  - name: rc-lb-process-d-c-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_debit_credit_notification'
#  - name: rc-lb-process-debit-transaction
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_lb_debit_transaction'
  ## Target2
#  - name: rc-t2-process-payment-status
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_payment_status'
#  - name: rc-t2-process-credit-transfer
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_credit_transfer'
#  - name: rc-t2-process-participant
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_participant'
#  - name: rc-t2-process-financial-document
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_financial_document'
#  - name: rc-t2-process-connection-result
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_connection_result'
#  - name: rc-t2-process-syntax-result
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_syntax_result'
  ## Target2 RTGS
#  - name: rc-t2-rtgs-process-admin-notif
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_admi_notifications'
#  - name: rc-t2-rtgs-process-status-reports
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_status_reports'
#  - name: rc-t2-rtgs-process-credit-transfer
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_customer_credit_transfer'
#  - name: rc-t2-rtgs-process-return-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_payment_return_in'
#  - name: rc-t2-rtgs-process-cancellation-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_cancellation_request_in'
#  - name: rc-t2-rtgs-process-investigation
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_resolution_of_investigation'
#  - name: rc-t2-rtgs-process-receipt-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_receipt_in'
#  - name: rc-t2-rtgs-process-d-c-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_debit_credit_notification'
#  - name: rc-t2-rtgs-process-participant
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_participant'
#  - name: rc-t2-rtgs-process-interbank-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_interbank_payment_in'
#  - name: rc-t2-rtgs-process-b2c-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_target2_rtgs_bank_to_customer_statement'
  ## GeorgiaRTGS
#  - name: rc-g-process-revenue-service-debts
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_georgia_revenue_service_debts'
#  - name: rc-g-rtgs-process-file-reject
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_file_reject'
#  - name: rc-g-rtgs-process-swift-status
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_swift_status_report'
#  - name: rc-g-rtgs-process-swift-dc-confirm
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_swift_debit_credit_confirmation'
#  - name: rc-g-rtgs-process-iso-status-report
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_iso_status_report'
#  - name: rc-g-rtgs-process-swift-transfer-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_swift_transfer_in'
#  - name: rc-g-rtgs-process-unifi-transfer-in
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_unifi_transfer_in'
#  - name: rc-g-rtgs-process-dc-notification
#    replicas: 1
#    command: 'app/console rabbitmq:consumer process_georgia_rtgs_debit_credit_notification'
  ## GlobusBankBundle
  - name: rc-globus-register-transfers
    replicas: 1
    command: 'app/console rabbitmq:consumer job_globus_register_transfers'
  - name: rc-globus-confirm-transfers
    replicas: 1
    command: 'app/console rabbitmq:consumer job_globus_confirm_transfers'
  ## PrivatbankBundle
  - name: rc-privatbank-register-transfers
    replicas: 1
    command: 'app/console rabbitmq:consumer job_privatbank_register_transfers'
  - name: rc-privatbank-confirm-transfers
    replicas: 1
    command: 'app/console rabbitmq:consumer job_privatbank_confirm_transfers'
  - name: privatbank-succeed-transfers-daemon
    replicas: 1
    command: 'app/console paysera:privatbank:succeed-transfers --deamonize 120'
#  ## BankChargeBundle
#  - name: 'rc-find-charge-debts'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer high_load_gateway_find_charge_debts'
#  ## ContisBundle
#  - name: 'rc-check-for-negative-balance-in-contis'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_check_for_negative_balance_in_contis'
#  ## BankAccountBundle
#  - name: 'rc-internal-account-debt-date-calculation'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_internal_account_debt_date_calculation'
#  ## DebtorBundle
#  - name: 'rc-gateway-process-debt-notification'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_process_debt_notification'
  - name: rc-create-inspection-transfer-internal
    replicas: 1
    command: 'app/console evp:transfer:create-inspection:distributed:internal'
  ## ClientChargeBundle
#  - name: 'rc-client-monthly-charge'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_client_monthly_charge'
  ## BankChargeBundle
#  - name: 'process-charge'
#    replicas: 1
#    command: 'app/console evp:charge:process --deamonize 60'
#  - name: 'rc-process-charge'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_gateway_process_charge'
#  - name: 'rc-activate-prepared-charge'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_activate_prepared_charge'
  ## ClientAverageBalanceBundle
#  - name: 'daily-client-average-balance'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_daily_client_average_balance'
#  - name: 'monthly-client-average-balance'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_monthly_client_average_balance'
  ## ClientMonthlyChargeFeeBundle
#  - name: 'daily-client-average-balance'
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_client_monthly_charge_fee'
  ## Libra
  - name: rc-libra-process-transfers
    replicas: 1
    command: 'app/console rabbitmq:consumer job_libra_process_transfers'

  # Dragons
  ## PartnerBundle
  - name: rc-partner-intermediate-statement-request
    replicas: 1
    command: 'app/console rabbitmq:consumer job_partner_intermediate_statement_request --memory-limit=400'
  - name: rc-partner-accounting-operation-reprocessing
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_partner_accounting_operation_reprocessing --memory-limit=400'
  - name: rc-process-remote-operation-request
    replicas: 1
    command: 'app/console rabbitmq:consumer high_load_job_process_remote_operation_request'
  - name: rc-recreate-remote-operation-request
    replicas: 1
    command: 'app/console rabbitmq:consumer job_high_load_recreate_remote_operation_request'
  ## AccountingOperationBundle
  - name: rc-accounting-operation-notify
    replicas: 1
    command: 'app/console rabbitmq:consumer job_accounting_operation_notify'
  ## AccountingBundle
  - name: rc-create-accounting-operation
    replicas: 1
    command: 'app/console rabbitmq:consumer job_create_accounting_operation'
  ## ContisAccountingBundle
  - name: rc-process-contis-partner-accounting-operation
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_contis_partner_accounting_operation --memory-limit=400'
  ## AccountingDiscrepancyBundle
  - name: rc-process-accounting-discrepancy-cause-request
    replicas: 1
    command: 'app/console rabbitmq:consumer job_process_accounting_discrepancy_cause_request'
  - name: rc-accounting-discrepancy-check-user
    replicas: 1
    command: 'app/console rabbitmq:consumer job_accounting_discrepancy_check_user'
  - name: rc-accounting-discrepancy-create-user
    replicas: 1
    command: 'app/console rabbitmq:consumer job_accounting_discrepancy_create_user'
  - name: rc-app-client-info
    replicas: 1
    command: 'app/console rabbitmq:consumer event_app_client_info'

  # CardIO
  ## ContisBunble
  - name: rc-contis-process-new-cards
    replicas: 1
    command: 'app/console rabbitmq:consumer job_contis_process_queued_card'
  - name: rc-contis-update-account-balance
    replicas: 1
    command: 'app/console rabbitmq:consumer job_contis_update_account_balance'
  - name: rc-contis-toggle-active-card-status
    replicas: 1
    command: 'app/console rabbitmq:consumer job_contis_toggle_active_card_status_on_balance_change'
#  - name: rc-contis-ensure-account-balance
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_contis_ensure_account_balance'
#  - name: rc-contis-accounts-synchronize
#    replicas: 1
#    command: 'app/console rabbitmq:consumer job_contis_accounts_synchronize'

  #Questionnaire AI
  - name: rc-questionnaire-ai-audit-validators
    replicas: 3
    command: 'app/console rabbitmq:consumer job_client_activity_questionnaire_ai_analysis_validator'
  - name: rc-questionnaire-ai-audits
    replicas: 5
    command: 'app/console rabbitmq:consumer job_client_activity_questionnaire_ai_analysis'
service:
  enabled: true

startupProbe:
  fpm:
    exec:
      command:
        - app/console
        - list

monitoring:
  enabled: true
