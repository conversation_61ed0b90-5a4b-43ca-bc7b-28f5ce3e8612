ABSTRACT
This document describes how iPiD customers can integrate with the iPiD Node to
perform requester and responder functionalities for Bank Account Validation
Schemes.
Confidentiality : Low
Revision Status : V 2.7
Revision : April 2025

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

1. Introduction.................................................................................................................. 4
1.1.

Hosting ........................................................................................................................ 4

1.2.

URLs ............................................................................................................................ 4

1.3. Rate Limiting ..................................................................................................................... 4
1.4. IP Restriction ..................................................................................................................... 4
1.5. Authentication Options ..................................................................................................... 5
1.6. Customer Id ...................................................................................................................... 5

2. Overview ...................................................................................................................... 6
3. Register Client Credentials ............................................................................................ 7
4. Token Generation ......................................................................................................... 9
5. Outbound – BAV Requests .......................................................................................... 10
6. Outbound – BAV Requests with OpenPGP Encryption .................................................. 15
7. Inbound – Data Hub Reference Upload (Bulk File)........................................................ 16
8. Inbound – Data Hub Reference Update (Single Entry) .................................................. 18
9. Inbound – Data Hub Alternative Methods ................................................................... 19
10. Data Model .............................................................................................................. 20
11. Appendix .................................................................................................................. 22
11.1. Response code and message .......................................................................................... 22
11.2. Identity Type ................................................................................................................. 23
11.3. Scoring Guide ................................................................................................................ 24
11.4. Scheme Match and Reason Codes .................................................................................. 25
11.5. Outbound BAV Coverage Details .................................................................................... 27
11.6. Corridor Request and Response Specifications ............................................................... 29
11.7. Node Selection .............................................................................................................. 32
11.8 Encryption Guide ............................................................................................................ 32
11.9 JWS Guide ...................................................................................................................... 33
11.10 Active Directory SSO configuration ................................................................................ 34
11.11 mTLS Certification Creation ........................................................................................... 35

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Version
1.0
1.1
2.0
2.1

Date
May 2024
Aug 2024
Oct 2024
Oct 2024

Author
Samantha Bautista
Ivan Chong
Ivan Chong
Samantha Bautista

2.2

Nov 2024

Samantha Bautista

2.3

Dec 2024

2.4

Jan 2025

Samantha Bautista
and Ivan Chong
Samantha Bautista

2.5

Jan 2025

Ivan Chong

2.6

Mar 2025

Samantha Bautista

2.7

Apr 2025

Samantha Bautista

Summary
Release of version 1.0
Added Registration API
Added v2 validation API
Updated column headers of
inbound reference datahub
update file
Updated with VOP use cases
in section 5 and 9
Updated with encryption guide
in appendix 11.8
Updated with newly released
Global Validate countries and
guide to interpret name match
forensic codes
Added mTLS Certificate
Creation section in Appendix
Added additional option for
data hub update (single entry
update) and updated APIs for
further VOP compatibility
Added outbound flow diagram
to section 2

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

1. Introduction
The purpose of this document is to provide developers an official guideline on how to
interact with the iPiD Node via APIs.

1.1.

Hosting

The iPiD node can either be deployed on-premise or hosted by iPiD via cloud.
Depending on the client’s preferred option, certain configuration details and
requirements may change.
This document is written out under the assumption that client will call an iPiD-hosted
node.

1.2.

URLs

Environment
Production
Sandbox

URL
TBD based on deployment approach
TBD based on deployment approach

1.3. Rate Limiting
Our rate limit is 1000 RPS (request per second).
For outbound requests, please note that not all corridors can support such a high RPS.
To provide a better integration experience, we will perform internal rate limiting based
on each corridor. If we are internally rate limiting a particular corridor, you may notice
higher than normal latency. In the worst-case scenario, we may return an HTTP 429
status code even if you have not reached 1000 RPS, as we might be experiencing high
traffic for that particular corridor. We also monitor for anomalies and will throttle or
block any suspicious usage patterns.

1.4. IP Restriction
Outbound
If there are any restriction for outbound traffic: please refer to latest list of IP to
whitelist from here
https://www.microsoft.com/en-us/download/details.aspx?id=56519
Inbound
User will also need to provide IP of the system from which you make the request to be
whitelisted. If it is not whitelisted, iPiD will reject the incoming request.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

1.5. Authentication Options
The iPiD node supports two main authorization options: via API key or via the OAuth
2.0 client credential grant type. Each option requires different headers in its API
requests. All Admin APIs will use the API key, while other APIs will use the
`access_token` generated from the `client_credentials` grant type.
API keys can be acquired from an iPiD representative. API key would be sent with every
request, via header `x-api-key`.
For OAuth 2.0, the `client_id` and `client_secret` can be obtained from the /register API
using the API key provided by iPiD. These credentials can then be used to generate a
token from the /token API. The generated Bearer token must then be included in the
request's Authorization headers.

1.6. Customer Id
Every request should be accompanied by a valid customer id, via header x-customerid. This can be acquired from an iPiD representative.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

2. Overview
The iPiD Node enables clients to participate in Bank Account Validation schemes,
such as the UK’s Confirmation of Payee (COP) scheme or the EU’s Verification of
Payee (VOP) scheme for the Eurozone. This means two main functionalities: (1) the
ability to make BAV requests; and, (2) for account-holding institutions, the ability to
respond to requests using client account and customer records as a reference.
iPiD, in its capacity as an official Aggregator for the Confirmation of Payee (COP)
scheme and a Routing and/or Verification Mechanism (RVM) for the Verification of
Payee (VOP) scheme, enables easy compliance to requestor/responder requirements
via the Node.
This document details 3 main activities:
- Credential Registration and Access Token generation via OAuth 2.0 as the
general authentication method to interact with the node
- Outbound functionality, or the ability to send BAV requests to both COP and to
iPiD’s global coverage
- Inbound functionality, or the ability to maintain up-to-date reference information
in compliance to scheme response requirements (specifically for COP)
The general outbound flow (including token generation) can be summarized in the
following diagram:

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

3. Register Client Credentials
POST /oauth/api/v1/register
This API is used to register new client credentials. It will return client_id and
client_secret that can be used to generate token with /token API.

3.1. Request
Headers
Name
x-api-key

Mandatory Type
Y
string

Description
API Key provided by iPiD

Body
Name
Mandatory Type
client_name
Y
string
token_endpoint_auth_method Y
string

Description
Client name
Authentication method. List
of possible value:
client_secret_basic,
client_secret_post, none

Sample Request
1. {
2.
3.
4. }
5.

"client_name": "ipid-node",
"token_endpoint_auth_method":"client_secret_post"

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

3.2. Response
Body
Name
client_id

Mandatory Type
Y
string

client_secret

Y

string

client_id_issued_at

Y

string

client_secret_expires_at

Y

string

client_name

Y

string

token_endpoint_auth_method Y

string

Description
client_id to be used in
/token endpoint
client_secret to be used in
/token endpoint
Timestamp of id issued
Timestamp of secret
expiry. If value is 0, it
means the secret will not
expire
Echo from API request
payload
Echo from API request
payload

Sample Response
1. {
2.
"message": {
3.
"client_id": "pbzIc0qsOctfitiRT3Hc",
4.
"client_secret": "M2MzMmNiNTUtYWQ4ZS00ZTNmLTg4MTYtN2IyZjcyOGQ4Yzk1",
5.
"client_id_issued_at": 1723098698,
6.
"client_secret_expires_at": 0,
7.
"client_name": "ipid-node",
8.
"token_endpoint_auth_method": "client_secret_post"
9.
}
10. }

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

4. Token Generation
POST /oauth/api/v1/token
This API is used to generate Bearer token. This endpoint supports both
application/json and application/x-www-form-urlencoded

4.1. Request
Body
Name
client_id
client_secret
grant_type

Mandatory
Y
Y
Y

Type
string
string
string

Description
client_id provided by iPiD
client_secret provided by iPiD
Fixed to “client_credentials"

Sample Request
1. {
2.
3.
4.
5. }

"client_id": "test-test",
"client_secret": "V4P12341231234123432341sQ5dBpmwaKF-pJYhJMfI8e6ZEgF677O",
"grant_type": "client_credentials"

4.2. Response
Body
Name
response_code
response_message
access_token

Mandatory
Y
Y
Y

Type
string
string
string

scope

Y

string

expires_in

Y

string

token_type

Y

string

Description
Refer to section 11.1 for full list
Refer to section 11.1 for full list
Access token issued by
authorization server
Refers to scope of access
granted to the client.
Duration of access_token
validity, in seconds
Type of token, default is “Bearer”

Sample Response
1. {
2.
3.
4.
5.
6.
7.
8. }

"response_code": 2000,
"response_message": "Success",
"access_token": " ODQWMZG3ZGMTOTNIMI0ZYJC1LTGXOWUTMDDMZTJLMTQZZTFJ",
"scope": "validation",
"expires_in": 7200,
"token_type": "Bearer"

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

5. Outbound – BAV Requests
POST /api/v1/bank-account/validate
This API is used to perform bank account validation

5.1. Request
Headers
Name
Authorization
Body
Name
country_code

Mandatory Type
Y
string

Mandatory
Y

Type
string

node_id

N

string

creditor

Y

object

creditor_account

Y

object

creditor_agent

Conditional object
(depending
on
corridor)
Optional
object
for VOP

debtor_agent

Description
Bearer {{access_token}}

Description
Alpha-2 country code based on ISO
3166
Applicable for certain
countries/corridors to fulfill specific
validation function (e.g. US, CN)
refer to “Creditor” data model in
section 10.1
refer to “Creditor_Account” data
model in section 10.2
refer to “Creditor_Agent” data model
in section 10.3

Sample Request (UK / COP)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12.
13. }

"country_code": "GB",
“"creditor": {
"name": "SHERLOCK HOLMES"
},
"creditor_account": {
"account_id": "********",
"account_holder_type": "personal"
},
"creditor_agent": {
"clearing_system_id": "112233"
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Sample Request (EU / VOP | Name Match)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12.
13.
14.
15. }

"country_code": "DE",
"debtor_agent": { //optional but recommended especially for multi-bic organizations
"bic": "ABCDFRP1XXX"
},
"creditor": {
"name": "Otto Larsen"
},
"creditor_account": {
"iban": "**********************"
},
"creditor_agent": { //optional but recommended for best routing
"bic": "WXYZDEFFXXX"
},

Sample Request (EU / VOP | ID Match)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12.
13.
14.
15.
16.
17.
18.
19.
20. }

"country_code": "DE",
"debtor_agent": { //optional but recommended especially for multi-bic organizations
"bic": "ABCDFRP1XXX"
},
"creditor": {
"identification": [
{
"value": "FR12949982110",
"type": "SREN"
}
]
},
"creditor_account": {
"iban": "***************************"
},
"creditor_agent": { //optional but recommended for best routing
"bic": "WXYZFRP1XXX"
},

Sample Request (Other)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12. }

"country_code": "ID",
"creditor": {
"name": "FIRA DIYANKA"
},
"creditor_account": {
"account_id": "**********"
},
"creditor_agent": {
"bic": "CENAIDJA"
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

5.2. Response
Headers
Name
Content-Type

Mandatory Type
Y
string

Body
Name
response_code
response_message
data

Mandatory
Y
Y
Y

Type
string
string
string

Description
application/json

Description
Refer to section 11.1 for full list
Refer to section 11.1 for full list
Consist of match_score,
match_score_description, scheme
specific reason codes for COP and
VOP, and the data objects: creditor,
creditor_account and creditor_agent,
typically including creditor’s name
(and name translation when
available) and account’s currency
and holder type when available.
Response payload is only populated
with data that deviates from the
original Request payload values. Else,
Response payload object fields will
not be populated.

Data
Name
match_score

Mandatory Type
Conditional string

match_score_
description

Conditional string

cop_matched

Conditional string

cop_reason

vop_name_ma
tch

Description
Scoring based on Creditor.Name or
Creditor.Given_name+Creditor.Surnam
e
Interpretation of match_score, with
additional supporting information
(depending on corridor); refer to
section 11.3
Present only for UK validations;
matched result from COP response

Conditional string

Refer to section 11.4 for more details
Present only for UK validations; reason
code from COP response

Conditional string

Refer to section 11.4 for more details
Present only for EU validations done
within VOP scheme
Refer to section 11.4 for more details

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

vop_id_match

Conditional string

logic_score

Conditional string

reason_code

Conditional string

creditor

Y

object

creditor_acco
unt
creditor_agent

Y

object

Y

object

Present only for EU validations done
within VOP scheme
Refer to section 11.4 for more details
Score value between 0-100 indicating
likelihood of account<>routing_code
validation (0-24 indicates invalid
account; 25+ indicates logic test
passed)
Further reason code for certain 2100 or
2103 responses when it is explicitly
identified that account structure is
invalid or bank is out of coverage
refer to “Creditor” data model in section
10.1
refer to “Creditor_Account” data model
in section 10.2
refer to “Creditor_Agent” data model in
section 10.3

Sample Response (UK / COP)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12.
13.
14.
15.
16. }

"response_code": 2000,
"response_message": "ValidationSucceeded",
"data": {
"match_score": 0.5,
"match_score_description": "Partial Match",
"cop_matched": "false",
"cop_reason": "MBAM",
"creditor": {
"name": "Sherlock Holmes"
}
"creditor_account": {
"account_holder_type": "personal"
}
}

Sample Response (EU / VOP | Name Match)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12. }

"response_code": 2000,
"response_message": "ValidationSucceeded",
"data": {
"match_score": 0.8,
"match_score_description": "Partial Match",
"vop_name_match": "CMTC",
"creditor_account": {
"name": "Victor Hugo”
}
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Sample Response (EU / VOP | ID Match)
1. {
2.
3.
4.
5.
6.
7.
8.
9. }

"response_code": 2000,
"response_message": "ValidationSucceeded",
"data": {
"match_score": 1,
"match_score_description": "Strong Match",
"vop_id_match": "MTCH"
}

Sample Response (Other)
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12. }

"response_code": 2000,
"response_message": "ValidationSucceeded",
"data": {
"match_score": 1,
"match_score_description": "Strong Match",
"creditor": {
"name": "北京苹果和橙子公司",
"name_translation": "Beijing Apple and Orange Company"
}
}

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

6. Outbound – BAV Requests with OpenPGP Encryption
POST /api/v2/bank-account/validate
This API is used to perform bank account validation, similar to v1. The difference is
the payload is encrypted with OpenPGP (see appendix 9.8 for encryption guide) and
requires signature with JWS.

6.1. Request
Headers
Name
Authorization
x-jws-signature

Mandatory Type
Y
string
Y
string

Body
Name
Mandatory Type
encrypted_payload Y
string

Description
Bearer {{access_token}}
Refer to guide on JWS generation in
Appendix 11.9.

Description
Payload from 5.1 encrypted with
OpenPGP

Sample Request (UK)
1. {
2.
"encrypted_payload": "-----BEGIN PGP MESSAGE----\n\nwX4DP5LZAhwM0aMSAgME3GTdpvAVmr290f8ClL1oZPXqN0VU5+IQNISQH0+T\nIcq3wBPG+QvEHT+uiOvCbGYQoIKQM9
cuzCkiW3xcnevFyjBtNfyYh36djT0i\niLpedAAJbIvgAUM1zebrk7Ua2+OoFHKtpXaatkAoWotV2Irf/qrSsgF4F25Y\n8N
9jV7We1GNTj8YUG6ikzxXzrME5+IeJVBpOHYNqisLw7T/1WYbwHCQiM/Vh\n4J+jtIv1hEXuLXIMnxN26H2gtbPjLJeJvc6U
8v7zsxC6kQbJastKsnDtncLO\nF45rcHHwSYJklDDZglt/oBguMeiQe6GXFG5X/nsuD4BksAdS2mPGYplFki3m\nxngbLxcq
+KR/nS58JLVtjqvCcI1AYvrSu8I/i6IuUQPL6VHlNoM=\n=PrVO\n-----END PGP MESSAGE-----\n"
3. }

6.2. Response
The API response here would be the same as section 5.2.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

7. Inbound – Data Hub Reference Upload (Bulk File)
POST /datahub/api/v1/upload
This API is used to update the Node with latest customer / account information to
comply BAV responder responsibilities. Data hub population with customer reference
information is also possible through SFTP setup between iPiD and the client.

7.1. Request
Headers
Name
Authorization
Content-Type

Mandatory Type
Y
string
Y
string

Body
Name
files

Description
Bearer {{access_token}}
Fixed as “application/csv”

Mandatory Type
Y
.csv

Description
File containing delta updates of
customer and account information;
recommended to be uploaded at
regular fixed time intervals (minimum
daily, but shorter intervals are also
accepted)

7.2. Response
Headers
Name
Content-Type

Mandatory Type
Y
string

Body
Name
response_code
response_message

Description
application/json

Mandatory Type
Y
string
Y
string

Description
Refer to section 11.1 for full list
Refer to section 11.2 for full list

7.3. Reference Data
The reference data .csv must contain the following columns, and each record must
pass the fields marked as mandatory. For all optional information that will not be
supplied, please leave the value blank/null
on
Column Name
countryCode

Mandatory
Y

iban

Conditional
String
(required for VOP)
Conditional
String
(required for VOP)

bic

Type
string

Description
Alpha-2 country code based on
ISO 3166
Iban bank account identifier
Institution BICFI; 8 or 11 character
ISO code identifying the institution

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

scan

createDate

Conditional
(required for COP)
Conditional
(required for COP)
Conditional
(required for COP)
Y

lastUpdateDate

Y

accountStatus
accountHolderType
accountName

Y
Y
Y

businessName

N

firstName

N

lastName

N

currency
identificationType
identificationValue
secondaryReference

N
N
N
N

optOut

N

sortCode
accountNumber

String
String

Sort code+account number
(14digits); primary key
Institution sort code (6 digits)

String

internal account number (8 digits)

Datetime Account opening/creation date
(ISO8061)
Datetime Date record was last updated; as
(ISO8061) recommended cadence of upload
is daily, each file would have the
same last_update_date
String
Active/Blocked/Closed
String
Personal or business
String
Name associated with the
account (aka account owner
name); if joint concatenate owners
with “ AND ” in between
Array
List of business names / DBA of
owning institution (individual
values of array separated by “|”)
Array
List of first names of all joint
owners (sequence matches with
last name array; individual values
of array separated by “|”)
array
List of last names of all joint
owners (sequence matches with
first name array; individual values
of array separated by “|”))
array
Currency of account
Array
See Appendix 11.2
Array
ID numbers
string
Additional reference code for COP
look-up (if available)
string
TRUE,FALSE ; flag if client wants
to exempt this particular account
number from inbound validation

7.2. Response
Headers
Name
Content-Type
Body
Name
response_code
response_message

Mandatory Type
Y
string

Mandatory Type
Y
string
Y
string

Description
application/json

Description
Refer to section 11.1 for full list
Refer to section 11.2 for full list

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

8. Inbound – Data Hub Reference Update (Single Entry)
POST /datahub/api/v1/reference-data
This API is used to update the iPiD node with the latest customer / account
information to comply BAV responder responsibilities on a per update basis.

8.1. Request
Headers
Name
Authorization

Mandatory Type
Y
string

Description
Bearer {{access_token}}

Body
Name
countryCode
iban

Type
string
String

Description

String

String

Institution BICFI; 8 or 11 character ISO
code identifying the institution
Sort code+account number (14digits);
primary key
Institution sort code (6 digits)

String

internal account number (8 digits)

accountStatus
accountHolderType
accountName

Mandatory
Y
Conditional
(required for VOP)
Conditional
(required for VOP)
Conditional
(required for COP)
Conditional
(required for COP)
Conditional
(required for COP)
Y
Y
Y

String
String
String

businessName

N

Array

firstName

N

Array

lastName

N

Array

identificationType
identificationValue
currency
secondaryReference

N
N
N
N

Array
Array
Array
string

optOut

N

string

Active/Blocked/Closed
Personal or business
Name associated with the account (aka
account owner name); if joint
concatenate owners with “ AND ” in
between
List of business names / DBA of owning
institution (individual values of array
separated by “|”)
List of first names of all joint owners
(sequence matches with last name array;
individual values of array separated by
“|”)
List of last names of all joint owners
(sequence matches with first name array;
individual values of array separated by
“|”))
See Appendix 11.2
ID numbers
Currency of account
Additional reference code for COP lookup (if available)
TRUE,FALSE ; flag if client wants to
exempt this particular account number
from inbound validation

bic
scan
sortCode
accountNumber

String

Alpha-2 country code based on ISO 3166
Iban bank account identifier

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

Sample Request
1. {
2.
"countryCode": "FR",
3.
"iban": "***************************",
4.
"bic": "ABCDFRP1XXX",
5.
"accountStatus": "Active",
6.
"accountHolderType": "Business",
7.
"accountName": "THOUSAND SUNNY INC.",
8.
"businessName": ["THOUSAND SUNNY INC.", "TSI INC."],
9.
"identificationType": ["LEI", "SREN"],
10.
"identificationValue": ["6ZVKRRPV38XR8O0B928P", "*********"],
11.
"currency": "EUR",
12.
"optOut": false
13. }

8.2. Response
Headers
Name
Content-Type
Body
Name
response_code
response_message

Mandatory Type
Y
string

Mandatory Type
Y
string
Y
string

Description
application/json

Description
Refer to section 11.1 for full list
Refer to section 11.2 for full list

9. Inbound – Data Hub Alternative Methods
iPiD is also able to support alternative methods of data reference, both in bulk (SFTP
to fixed urls, etc.) or in lookup calls. iPiD can also customize its node to connect to
existing client APIs for customer/account lookup as long as it meets local scheme
(COP) data requirements and performance/response time benchmarks.
Please contact your iPiD representative to discuss any required customizations.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

10. Data Model
10.1. Creditor
Name
name

Mandatory
Conditional

Type
string

given_name

Conditional

string

surname

Conditional

string

name_translation

Response
Only

string

identification

N

object

Description
Name that will be used for name matching
purpose; must have either name or
given_name supplied
Given name that will be used for name
matching purpose; must have either name
or given_name
Surname that will be used for name
matching purpose; optionally used with
given name for certain corridors (US)
In case of name returned in foreign
characters from beneficiary institution, a
translated name value in latin characters
will also be returned
refer to “Identification” object schema

10.1.1. Identification
Name
value
type

Mandatory
Y
Y

Type
string
string

Description
Value of identity / identification number
Can default to registration_id. Refer to
section 11.2 for list of expected types

10.2. Creditor_account
Name
iban

Mandatory Type
Conditional string

account_id

Conditional string

currency

N

string

account_type

N

string

account_holder_type N

string

Description
Must have either IBAN or account Id. Will
prioritize IBAN if both are supplied
For corridors where IBAN-only validation
is provided, this becomes the only
mandatory field in the request payload
Must have either IBAN or account Id. Will
prioritize IBAN if both supplied
Returns as part of response when
available; currency code, based on ISO
4217. Eg: SGD
Returns as part of response when
available; specifies nature or use of
account Eg: NRE account (for India)
Returns as part of response when
available; specifies nature of account
holder (personal, business)

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

10.3. Creditor_agent
Name
bic
clearing_system_id

Mandatory
Conditional
Conditional

Type
string
string

bank_name

N

string

bank_address

N

string

payment_scheme_eligibility N

string

Description
BIC code, either 8 or 11 digits
National clearing code (e.g. UK sort
code)
Returns as part of response when
available; name of institution
Returns as part of response when
available; address of institution
Returns as part of response when
available; ability of account to
receive funds through payment
schemes such as SEPA credit
transfer, CORE1 DD, and more
(available for IBAN logic validation
only)

10.4. Debtor_agent
Name
bic

Mandatory
Conditional

Type
string

Description
BIC code, either 8 or 11 digits; O
Optional field for VOP node; used to
determine which certificate to use for
authentication, especially for a multi-BIC
organization. If it is not supplied, will
default to the primary BIC and certificate
identified by the client upon setup of
node

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

11. Appendix
11.1. Response code and message
Other than relying on HTTP status code, we will be using response_code &
response_message within response body to further explain different type response.
HTTP Status Code – 200
Code

Message

2000

Successful Validation

2001

Format Validation
Successful

2002

Account Active
Confirmed

2100
2101
2102
2103
2104
2105
2106

Creditor Account not
found
Creditor Account in
flagged status
Creditor Agent Invalid
Creditor Account details
could not be validated
Creditor Name Match
could not be completed
Format Validation
Unsuccessful
Invalid registration ID

Description
Successfully found creditor_agent and
creditor_account to conduct validation and
generate match_score
Logic-based check passed (account structure),
most relevant to IBAN validation
Confirmation of account existence and activeness
(account is in a status to receive funds
successfully)
creditor_account.account_id not found from data
provider's response
Creditor Account is closed, dormant or blocked
Invalid BIC code or clearing system ID sent, or
unable to lookup data provider
Data provider could not confirm is account is
within creditor agent
Data provider could not complete name match
given valid account details
Logic-based check failed (account structure),
most relevant to IBAN validation
For validations that require an identification
number component, the id number supplied is
missing or invalid

HTTP Status Code - 400
Code
4001
4004
4005

Message
Not Authorised
Invalid Corridor
Insufficient Inputs

Description
API key invalid
Country not supported by Validate
Input fields do not meet corridor requirements

HTTP Status Code - 500
Code
5000
5001
5002

Message
General exception
Internal server error.
Please contact iPiD
Support.
Could not communicate
with financial institution.
Please retry.

Description
Unknown error
Either iPiD or data provider service is currently
down.
Timeout or traffic issue on data provider side;
resolved with subsequent retry.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

11.2. Identity Type
Code
registration_id
LEI
BIC

Identity Type
Default for passing identification
value
LegalEntityIdentifier
BusinessIdentifierCode

BANK

BankPartyIdentification

CBID

CentralBankIdentificationNumber

CHID

ClearingIdentificationNumber

CINC

CertificateOfIncorporationNumber

COID

CountryIdentificationCode

CUST

CustomerNumber

DUNS

DataUniversalNumberingSystem

EMPL

EmployerIdentificationNumber

GS1G

GS1GLNIdentifier

SREN

SIREN

SRET

SIRET

TXID

TaxIdentificationNumber

Description
Can default to this in any non-VOP use case

Unique and unambiguous assignment
made by a specific bank or similar financial
institution to identify a relationship as
defined between the bank and its client.
A unique identification number assigned by
a central bank to identify an organisation.
A unique identification number assigned by
a clearing house to identify an organisation
A unique identification number assigned by
a designated authority to a certificate of
incorporation and used to identify an
organisation.
Country authority given organisation
identification (e.g., corporate registration
number)
Number assigned by an issuer to identify a
customer.
Number assigned by a party to identify a
creditor or debtor relationship.
A unique identification number provided by
Dun & Bradstreet to identify an
organisation.
Number assigned by a registration
authority to an employer.
Global Location Number. A non-significant
reference number used to identify legal
entities, functional entities, or physical
entities according to GS1 numbering
scheme rules.The number is used to
retrieve detailed information that is linked
to it.
The SIREN number is a 9 digit code
assigned by INSEE, the French National
Institute for Statistics and Economic
Studies, to identify an organisation in
France.
The SIRET number is a 14 digit code
assigned by INSEE, the French National
Institute for Statistics and Economic
Studies, to identify an organisation unit in
France. It consists of the SIREN number,
followed by a five digit classification
number, to identify the local geographical
unit of that entity
Number assigned by a tax authority to
identify an organisation.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

11.3. Scoring Guide
Match_score_description does not apply to a fixed range of match_score values and
takes into consideration the differences between data providers. Name match
classification thresholds and scoring variable weights are adjustable depending on
client needs and use cases. It is advised to have a discovery session with your iPiD
technical representatives before adjusting the name match algorithm.
Score
Description
Strong
Match

Indicative
Match_Score
> 0.85

COP

VOP

Partial
Match

0.45 to 0.85

Outbound: 0.5
(MBAM,
BAMM,
PAMM)
Inbound: 0.45
to 0.95

Outbound: 0.8
(CMTC)
Inbound: 0.80
to 0.99

Weak Match

< 0.45

Outbound: 0
(ANNM)
Inbound: <
0.45

Outbound: 0
(NMTC)
Inbound: <
0.80

Description

Outbound: 1
Outbound: 1
Creditor Account
(MATC,
(MTCH)
found and is a highly
BANM, PANM) Inbound: >0.99 confident match
Inbound: >0.95
Creditor Account
found and is a partial
match. Additional
information can be
supplied depending
on the data corridor,
such as the
identification of
mismatched name
part.
Creditor Account
found but name is
likely not a match.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

11.4. Scheme Match and Reason Codes
CONFIRMATION OF PAYEE (UK)
Contains all information captured in the standard COP response; name is only returned
in case of a close match
COP
Match
True

Reason
Code
MATC

COP Name
Match
Match

False

ANNM

Not a Match

False

MBAM

Close Match

False

BANM

Match

False

PANM

Match

False

BAMM

Close Match

False

PAMM

Close Match

False
False

AC01
IVCR

False

ACNS

False

OPTO

False

CASS

False

SCNS

Long Description
The CoP Responder has performed the matching and
confirms it is a match
The CoP Responder has performed the matching and
confirms it is not a match
The CoP Responder has performed the matching and
it is a close match
The CoP Requester indicated that the Initiator intends
to pay/debit a Personal Account, but the actual
account is a Business Account and the name
matches
The CoP Requester indicated that the Initiator intends
to pay/debit a Business Account, but the actual
account is a Personal Account and the name matches
The CoP Requester indicated that the Initiator intends
to pay/debit a Personal Account, but the actual
account is a Business Account and the name is a
close match
The CoP Requester indicated that the Initiator intends
to pay/debit a Business Account, but the actual
account is a Personal Account and the name is a
close match
Account does not exist in the CoP Responder’s books
The CoP Responder was unable to locate the
customer account based on the secondary reference
data contained within the SecondaryIdentification
field
Account not supported for CoP by the CoP Responder
This code should also be used by a sponsor bank if the
account relates to a collection account held by an
ASPSP that is not reachable for CoP and they instruct
payers to enter the SRD account level name for
inbound payments as the sponsor will not be able to
match based on the collection account name
Payee/Payer has been opted out of the CoP service
by the CoP Responder
The Payee/Payer’s account has been switched using
the Current Account Switch Service (CASS)
The CoP Responder has incorrectly received a CoP
request for a sort code that does not belong to them

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

VERIFICATION OF PAYEE (EU)
Contains all information captured in the standard VOP response; name is only returned
in case of a close match (CMTC)
Validation
Type
Name
Name
Name
Name

VOP Reason
Code
MTCH
NMTC
CMTC
NOAP

ID
ID
ID

MTCH
NMTC
NOAP

Long Description
Name verification done - Match
Name verification done – No Match
Name verification done – Close Match
Matching not possible for the responding application for
any reason
Id verification done - Match
Id verification done – No Match
Matching not possible for the responding application for
any reason

NAME MATCH FORENSIC REASON CODES
Explains the reason codes captured when inbound match score results are returned.
Reason Code
NPM
SSM
CNN
BCN
INT
INF
JRA
BPA

Reason Description
Name Part Match
Solitary Spelling Mistake
Common Nickname
Business Common Name
Initials Match
Initials Not Match
Joint Account Reference
Business Purpose Account

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

11.5. Outbound BAV Coverage Details
Corridor

Integration Type

Coverage

Remarks

Response Time

UK via COP

Direct-to-FI

All participants of Confirmation of Payee (COP)
scheme

2-5 seconds

Eurozone via VOP,
coming soon
US

Direct-to-FI

All participants of Verification of Payee (VOP)
scheme
All ABA-registered institution; incomplete withininstitution coverage due to contributed
database limitations
All ABA-registered institution

Validates only domestic currency (GBP
accounts); restricts access to account holder
name to only partial matches
Validates only accounts capable of SEPA
instant/credit transfers
Responses based on data consortium, leading
to higher chance of inconclusive results;
restricts access to account holder name
Return a logic score on the likelihood of
whether an account and routing code pair
exists; does not perform any name/ownership
validation
Real-time microdeposit, returns certainty of
account existence, but no ownership/ name
check
Validates only domestic currency (INR
accounts)

Analytics-based +
Logic-based
Logic-based

Direct-to-FI

All RTP® Network Participating Financial
Institutions (~483 banks / financial institutions)

India

Direct-to-FI

Indonesia
Vietnam

Direct-to-FI
Direct-to-FI

All IMPS live members (~855 banks / financial
institutions)
142 major banks / financial institutions
43 major banks / financial institutions (NAPAS)

Nigeria
Nepal

Direct-to-FI
Direct-to-FI

40 major banks / financial institutions
23 major banks / financial institutions

Pakistan

Direct-to-FI

32 major banks / financial institutions

China

2,000+ institutions within UnionPay network

South Korea

Direct-to-FI
(Individuals)
Direct-to-FI
(Institutions)
Direct-to-FI

Bangladesh

Direct-to-FI

Brazil

Direct-to-FI

3 banks (Mutual Trust Bank, Dhaka Bank,
Eastern), more in progress
148 major banks / financial institutions

141 major banks / financial institutions
48 major banks / financial institutions

Validates only domestic currency (VND
accounts)
Restricts access to account holder name to
only those with match_score > 0.8
Validates only domestic currency (PKR
accounts)
Restricts access to account holder name

2-5 seconds
2-5 seconds
1-2 seconds

5-10 seconds
3-7 seconds
2-3 seconds
2-3 seconds
2-5 seconds
2-3 seconds
2-3 seconds
2-3 seconds

Account holder name returned in certain match
scenarios (exact match with USCC)
Validates only domestic currency (KRW
accounts)
Restricts access to account holder name

8-12 seconds

Validates only domestic currency (BRL
accounts)

5-8 seconds

2-3 seconds
2-3 seconds

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor
Mexico

Integration Type
Direct-to-FI

Coverage
129 major banks / financial institutions

Uganda
Argentina

Direct-to-FI
Direct-to-FI

22 major banks / financial institutions
63 major banks / financial institutions

Belgium
Italy

Hybrid (AB /DFI)
Hybrid (AB /DFI)

Uruguay
Peru
Malaysia

Direct-to-FI
Direct-to-FI
Direct-to-FI

All financial institutions; incomplete withininstitution coverage due to contributed
database limitations
17 major banks / financial institutions
4 major banks
37 major banks / financial institutions

Turkey

Direct-to-FI

50+ major banks/financial institutions

South Africa

Direct-to-FI

11 major banks / financial institutions

France, coming soon
Netherlands, coming
soon
Poland, coming soon
Chile, coming soon
Colombia, coming
soon
Ecuador, coming
soon
Saudi Arabia, coming
soon
-many- (IBAN
Validation)

Hybrid (AB /DFI)
Hybrid (AB /DFI)

All financial institutions; incomplete withininstitution coverage due to contributed
database limitations

Hybrid (AB /DFI)
Direct-to-FI
Direct-to-FI
Direct-to-FI
Direct-to-FI
Logic-based

Remarks
Validates only domestic currency (MXN
accounts)

Validates only domestic currency (ARL
accounts)
Validates business accounts only
Validates business accounts only
Name returned is masked
Validates only domestic currency (MYR
accounts)
Validates only domestic currency (TRY
accounts); name returned is masked
Returns certainty of account existence only;
ownership confirmation subject to availability
of information
Validates business accounts only
Validates business accounts only
Validates business accounts only

20 major banks/financial institutions
1 bank (Bancolombia), more in progress

Response Time
(requires repeat
request after 5
minutes)
2-5 seconds
2-5 seconds
5-10 seconds
5-10 seconds
5-8 seconds
5-8 seconds
2-3 seconds
2-5 seconds
5-8 seconds
tba
tba
tba
tba
tba

3 banks (Guayaquil Bank, Pacific Bank,
Produbanco), more in progress
14 major banks/financial institutions

tba

83 IBAN countries: AL, AD, AT, AZ, BH, BE, BA,
BR, BG, BI, CR, HR, CY, CZ, DK, DJ, DO, EG, SV,
EE, FO, FI, FR, GE, DE, GB, GI, GR, GL, GT, HU, IS,
IQ, IE, IL, IT, JO, KZ, XK, KW, LV, LB, LY, LI, LT, LU,
MK, MT, MR, MU, MD, MC, ME, NL, NO, PK, PS,
PL, PT, QA, BY, RO, RU, LC, SM, ST, SA, RS, SC,
SK, SI, SO, ES, SD, SE, CH, TL, TN, TR, UA, AE, VA,
VG

1-2 seconds

tba

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

11.6. Corridor Request and Response Specifications
Corridor
Creditor

Request Parameter Recommendations (preferred inputs with the
conditional fields)
Creditor
Creditor
Creditor
Creditor Agent
Identification
Account
Account
Holder Type
n/a
beneficiary
Personal,
Sort code as clearing
bank account
Business
system ID
ID or IBAN

Response Specifications
Match_Score

Match_Score_Desc
ription

Logic_Score

Data

Discrete values
representative of
strength of match (1,
0.5, 0)
Discrete values
representative of
strength of match (1,
0.8, 0)
Discrete values
representative of
strength of match (1,
0.5, 0, etc.)
n/a

Strong, Partial,
Weak

n/a

Strong, Partial,
Weak

n/a

Strong, Partial
(with additional
detail), Weak

Score value
indicating likelihood
of validity(0…100)

Supplies full name
string in case of
partial match only,
currency
Supplies full name
string in case of
partial match only,
currency
n/a

UK via COP

name

Eurozone via VOP

Name
(required
for name
validation)
given_nam
e and
surname

LEI or VAT
(required for id
validation;
business only)
n/a

IBAN

n/a

n/a

beneficiary
bank account
ID

n/a

US ABA code as
clearing system ID

US (logic-based)
– node 2

n/a

n/a

n/a

US ABA code as
clearing system ID

US (Direct-to-FI) –
node 3

n/a

n/a

n/a

US ABA code as
clearing system ID

n/a

n/a

n/a

n/a

India

name

n/a

n/a

IFSC code as clearing
system ID

n/a

Supplies full name
string, currency

name

n/a

n/a

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string

Vietnam

name

n/a

n/a

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string, currency

Nigeria

name

n/a

n/a

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string

Nepal

name

n/a

beneficiary
bank account
ID

n/a

BIC Code

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)

Strong, Partial,
Weak

Indonesia

beneficiary
bank account
ID
beneficiary
bank account
ID
beneficiary
bank account
ID
beneficiary
bank account
ID
beneficiary
bank account
ID
NUBAN as
account ID

Strong, Partial,
Weak

n/a

Pakistan

name

n/a

n/a

BIC Code

n/a

name

n/a

n/a

BIC Code or local bank
code as clearing
system ID

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)

Strong, Partial,
Weak

South Korea

beneficiary
bank account
ID or IBAN
beneficiary
bank account
ID

Supplies full name
string in case of
match_score > 0.8
only
Supplies full name
string, currency

Strong, Partial,
Weak

n/a

US (analyticsbased) - node 1

n/a

n/a

Supplies full name
string (in hangul),
name translation,
currency

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor

Request Parameter Recommendations (preferred inputs with the conditional fields)

Response Specifications

Creditor

Creditor
Identification

Creditor
Account

name (in
Chinese
characters
)
name

n/a

beneficiary
bank account
ID (in 62* PAN
format)
beneficiary
bank account
ID

Bangladesh

name

n/a

Brazil

name

Mexico

name

CPF in
000.000.000-00
OR CNPJ in
00.000.000/000000 format as
registration_id
n/a

Uganda

name

n/a

Argentina

name

n/a

Belgium

name

Italy

Creditor
Account
Holder Type
n/a

Creditor Agent

Match_Score

Match_Score_Desc
ription

Logic_Score

Data

n/a

Discrete values
representative of
strength of match (1, 0)

Strong, Weak

n/a

n/a

n/a

BIC Code

Continuous variable,
indicating strength of
match (0…1)

Strong, Partial,
Weak

n/a

beneficiary
bank account
ID
IBAN

n/a

Discrete values
representative of
strength of match (1, 0)
Continuous variable,
indicating strength of
match (0…1)

Strong, \Weak

n/a

n/a

BIC Code (MTBLBDDH,
DHBLBDDH,
EBLDBDDH)
n/a

Supplies full name
string (in chinese
simplified) and
name translation in
UCC-match
scenarios
n/a

Strong, Partial,
Weak

n/a

Supplies full name
string, currency

CLABE as
account ID

n/a

n/a

Strong, Partial,
Weak

n/a

Supplies full name
string, currency

beneficiary
bank account
ID
CBU as account
ID

n/a

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string

n/a

n/a

Strong, Partial,
Weak

n/a

Supplies full name
string, currency

VAT, Company
No. as reg_id

IBAN

n/a

n/a

Strong, Partial,
Weak

n/a

name

VAT as reg_id

IBAN

n/a

n/a

Strong, Partial,
Weak

n/a

Uruguay

name

n/a

beneficiary
bank account
ID

n/a

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string in registration
ID match scenarios
Supplies full name
string in registration
ID match scenarios
Supplies partially
masked name string,
currency

Peru

name

n/a

CCI as account
ID

n/a

n/a

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Discrete values
representative of
strength of match (1,
0.5, 0)
Continuous variable,
indicating strength of
match (0…1)

Strong, Partial,
Weak

n/a

China (individual)
– node 1
China (business)
– node 2

Uniform credit
code (18 char
alphanumeric
code) as
registration_id

Supplies full name
string, currency

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

Corridor

Request Parameter Recommendations (preferred inputs with the conditional fields)

Response Specifications

Creditor

Creditor
Identification

Creditor
Account

Malaysia

name

n/a

Turkey

name

n/a

beneficiary
bank account
ID
IBAN

South Africa

Name

n/a

France, coming
soon

name

Netherlands,
coming soon

Creditor
Account
Holder Type
n/a

Creditor Agent

Match_Score

Match_Score_Desc
ription

Logic_Score

Data

BIC Code

Strong, Partial,
Weak

n/a

Supplies full name
string, currency

n/a

n/a

Continuous variable,
indicating strength of
match (0…1)
Discrete values
representative of
strength of match (1,

Strong, Partial,
Weak

n/a
n/a
n/a

Supplies partially
masked name string

beneficiary
bank account
ID

n/a

BIC Code or branch
code as clearing
system id

0.5, 0)
Discrete value (1)
confirmation of

VAT, SIREN as
reg_id

IBAN

n/a

n/a

name

VAT as reg_id

IBAN

n/a

n/a

Poland, coming
soon

name

VAT, NIP, REGON
as reg_id

IBAN

n/a

n/a

Chile, coming
soon

name

RUT as reg_id

n/a

Colombia, coming
soon

name

n/a

Ecuador, coming
soon

Name

n/a

Saudi Arabia,
coming soon

Name

n/a

BIC Code or local bank
code as clearing
system id
BIC Code or local bank
code as clearing
system id
BIC Code or local bank
code as clearing
system id
n/a

-many- (IBAN
Validation)

n/a

For indivials:
National ID OR
IQAMA
For businesses:
Unified National
Number AND
commercial
registration code
n/a

beneficiary
bank account
ID
beneficiary
bank account
ID
beneficiary
bank account
ID
IBAN

Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)
Continuous variable,
indicating strength of
match (0…1)

Strong match
(when ownership
confirmation is
available)
Strong, Partial,
Weak

IBAN

n/a

n/a

n/a
n/a

matched ownership
when available
n/a

n/a

n/a

Strong, Partial,
Weak

n/a

Strong, Partial,
Weak

n/a

Strong, Partial,
Weak

n/a

Supplies full name
string in registration
ID match scenarios
Supplies full name
string in registration
ID match scenarios
Supplies full name
string in registration
ID match scenarios
Supplies full name
string

Strong, Partial,
Weak

Supplies full name
string

Strong, Partial,
Weak

Supplies full name
string

Strong, Partial,
Weak

Supplies full name
string

n/a

Supplies bank name,
bank code, BIC, and
bank address
Validation)

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this
document, in whole or in part, is strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd. assumes no
liability for any loss or damage arising from the unauthorized use or distribution of this document.

11.7. Node Selection
Certain corridors allow for different validation functionality when a unique node_id field is passed
as part of the validation request. The details of all distinct node_ids are as follows:
Corridor /
country code
US

CN

IBAN (pass this
in country_code
parameter
during public
key step)

Node ID

Method

Description

us-node-01
us-node-02

Analyticsbased
Logic-based

us-node-03

Direct-to-FI

cn-node-01

Direct-to-FI
(individual)

cn-node-02

Direct-to-FI
(business)

iban-node-01

Logic-based

Lower coverage; ability to validate
ownership/name check
Returns likelihood account and routing
number combination is a valid pair;
definitive in identifying invalid accounts
Real-time microdeposit, returns certainty
of account existence, but no ownership/
name check
Full validation for Chinese bank accounts
for individuals (in the 62* PAN format);
requires account number only; returns
indication of name match only
Full validatnion for Chinese bank accounts
for institutions; requires uniform credit
code, name, bank account number and
BIC; returns full name and translation in
UCC-match scenario
Default node to run logic-based validation
(including those for full validation
countries like GB, BR, PK, BE, FR, IT, NL, PL,
etc.)

Status
LIVE
LIVE
LIVE
LIVE

LIVE

LIVE

11.8 Encryption Guide
Encrypted payload is generated from a JSON payload. Following is an example of a valid JSON
payload.
1. {
2.
3.
4.
5.
6.
7.
8.
9.
10.
11.
12.
13. }

"country_code": "GB",
“"creditor": {
"name": "SHERLOCK HOLMES"
},
"creditor_account": {
"account_id": "********",
"account_holder_type": "personal"
},
"creditor_agent": {
"clearing_system_id": "112233"
}

Encryption can be done with OpenPGP protocol. You may refer to this guide for Javascript
implementation: https://github.com/openpgpjs/openpgpjs
The recommended algorithm to generate PGP keys is as following:
1. {
2.
3.
4. }

“type” : “rsa”,
“curve” : “4096”

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an
@ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is strictly prohibited. If you have
received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd.
assumes no liability for any loss or damage arising from the unauthorized use or distribution of this document.

or
1. {
2.
3.
4. }

“type” : “ecc”,
“curve” : “p256”

Please note that any newlines in the encrypted payload should be replaced with \n to ensure
proper handling and transmission of the data. For example:

The encrypted payload will then need to be encoded in Base64. This encoding ensures that the
encrypted data can be safely transmitted as text, without losing any information during transport.

11.9 JWS Guide
JSON Web Signature (JWS) Requirement Walkthrough
The JSON Web Signature (JWS) is a compact, URL-safe means of representing claims to be
transferred between two parties. It is used to ensure the integrity and authenticity of the data.
To generate keypair, use following command
1. openssl req -x509 -newkey rsa:2048 -nodes -keyout key.pem -out cert.pem -days 365

To convert public key from x509 format to SPKI format, use following command:
1. openssl x509 -in cert.pem -pubkey -noout > publickey.pem

PS256 Algorithm
PS256 stands for RSA Probabilistic Signature Scheme with SHA-256. It is a widely used algorithm
that combines the RSA encryption algorithm with the SHA-256 hashing algorithm, providing a high
level of security and resistance to certain types of cryptographic attacks.
Steps to Implement PS256 in JWS with Detached Payload
First, generate a pair of RSA keys (private and public). The private key is used to sign the data, while
the public key is used to verify the signature.
Next, create the JWS header. The header typically includes the type of token (JWT) and the signing
algorithm (PS256). For example:
1. {
2.
"alg": "PS256",
3.
"typ": "JWT"
4. }

Prepare the payload, which contains the claims or data you want to transfer. It is usually a JSON
object. For example:
1. {
2.
"sub": "1234567890",
3.
"name": "John Doe",
4.
"iat": 1516239022
5. }
DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an
@ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is strictly prohibited. If you have
received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd.
assumes no liability for any loss or damage arising from the unauthorized use or distribution of this document.

Use the private RSA key to sign the payload. In detached mode, the payload is not included in the
JWS itself but is provided separately. The resulting JWS will have two parts: the header and the
signature, separated by a dot.
One important thing to note is that when signing the payload, the payload must be in the same
format as the payload before encrypted that will be transmitted separately. Any additional space or
incorrect order will result in a different signature.
Transmit the detached payload separately from the JWS. The recipient must have access to the
detached payload to verify the signature.
Finally, the recipient uses the public RSA key to verify the signature. They combine the detached
payload with the JWS header and signature to perform the verification. If the signature is valid, it
confirms that the data is authentic and has not been altered.
Example JWS with Detached Payload
Here is an example of a JWS using PS256 in detached mode:
eyJhbGciOiJQUzI1NiIsInR5cCI6IkpXVCJ9..SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
In this example, the first part is the base64-encoded header, and the second part is the base64encoded signature. The payload is transmitted separately.

11.10 Active Directory SSO configuration
iPiD Portal supports SSO via LDAP or SAML. Please note that SSO implementation will place user
authorization capabilities solely in the client/user’s responsibility. Thus, please reach out to your
iPiD representatives to discuss its implementation.
The following information are expected from AD:
Claim name
http://schemas.microsoft.co
m/identity/claims/displaynam
e
http://schemas.microsoft.co
m/identity/claims/role

http://schemas.xmlsoap.org/
ws/2005/05/identity/claims/e
mailaddress
ipid.tech/institute

Description
Display name of user, which it will be used in Portal
Role of user. This is mandatory for Portal role-based
access. Value will have to be following list:
- admin
- standard
- channel
Email address of user.
Company name.

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an
@ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is strictly prohibited. If you have
received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd.
assumes no liability for any loss or damage arising from the unauthorized use or distribution of this document.

11.11 mTLS Certification Creation
Key generation
openssl genpkey -algorithm RSA -out ipid-prd.key -pkeyopt rsa_keygen_bits:2048
Passphrase
Create a file named ‘passphrase’ and include a passphrase inside it.
Generate CSR
openssl req -new -key ipid-prd.key -passin file:passphrase -out ipid-prd.csr -sha256
Verify Client Certificate (Optional)
To verify the CSR, use following command:
openssl req -text -in ipid-prd.csr -noout -verify

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly from an
@ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is strictly prohibited. If you have
received this document in error, please notify the sender immediately and delete it from your system. International Payments Identity (iPiD) Pte. Ltd.
assumes no liability for any loss or damage arising from the unauthorized use or distribution of this document.

ABOUT iPiD
iPiD is a fast-growing, venture-backed fintech start-up that was founded in late 2021 by a
global team who have held senior roles at major payments and technology companies,
including SWIFT and Thomson Reuters. In addition to our HQ in Singapore, our global team
has representatives in India, Belgium, Malaysia, Netherlands, UAE, Spain, and Vietnam. iPiD’s
vision is to make cross-border payments easy, secure, and seamless. We achieve this by
partnering with financial services providers (banks, payment systems, payment fintechs,
wallets…) to deliver an addressing data platform that helps the payment industry to provide a
more efficient and user-friendly payment journey.
iPiD is built for all – we do not replace banks, payment fintechs, wallets or remittance
companies; nor do we replace existing payment rails. Our Advisory Board includes senior
figures from across the industry: Christian Sarafidis, Microsoft Chief Business Development
Officer WWFSI; Kosta Peric, Deputy Director, Financial Services for the Poor, the Bill & Melinda
Gates Foundation; and Nick Lewins, former banking Chief Technology Officer and now an
advisor in data and AI, cloud technology and digital transformation.
www.ipid.tech

DISCLAIMER and CONFIDENTIALITY: This document is confidential and intended solely for recipient(s) who received this directly
from an @ipid.tech email address. Any unauthorized distribution, copying, or disclosure of this document, in whole or in part, is
strictly prohibited. If you have received this document in error, please notify the sender immediately and delete it from your system.
International Payments Identity (iPiD) Pte. Ltd. assumes no liability for any loss or damage arising from the unauthorized use or
distribution of this document.

