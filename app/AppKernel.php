<?php

use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\HttpKernel\Kernel;

class AppKernel extends Kernel
{
    public function __construct($environment, $debug)
    {
        parent::__construct($environment, $debug);

        if ($this->debug) {
            error_reporting(error_reporting() & ~E_STRICT & ~E_DEPRECATED);
        }
    }

    public function registerBundles()
    {
        $bundles = [
            // Vendor specifics bundles
            new Symfony\Bundle\FrameworkBundle\FrameworkBundle(),
            new Symfony\Bundle\SecurityBundle\SecurityBundle(),
            new Symfony\Bundle\TwigBundle\TwigBundle(),
            new Symfony\Bundle\MonologBundle\MonologBundle(),
            new Symfony\Bundle\SwiftmailerBundle\SwiftmailerBundle(),
            new Doctrine\Bundle\DoctrineBundle\DoctrineBundle(),
            new Doctrine\Bundle\DoctrineCacheBundle\DoctrineCacheBundle(),
            new Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle(),
            new Knp\Bundle\MenuBundle\KnpMenuBundle(),
            new Knp\Bundle\PaginatorBundle\KnpPaginatorBundle(),
            new BeSimple\SoapBundle\BeSimpleSoapBundle(),
            new FOS\UserBundle\FOSUserBundle(),
            new FOS\RestBundle\FOSRestBundle(),
            new FOS\JsRoutingBundle\FOSJsRoutingBundle(),
            new JMS\SerializerBundle\JMSSerializerBundle(),
            new Stof\DoctrineExtensionsBundle\StofDoctrineExtensionsBundle(),
            new Exercise\HTMLPurifierBundle\ExerciseHTMLPurifierBundle(),
            new Knp\Bundle\GaufretteBundle\KnpGaufretteBundle(),
            new Khepin\DrupalTransliteratorBundle\KhepinDrupalTransliteratorBundle(),
            new Evp\Bundle\CallbackBundle\EvpCallbackBundle(),
            new FOS\ElasticaBundle\FOSElasticaBundle(),
            new OldSound\RabbitMqBundle\OldSoundRabbitMqBundle(),
            new Knp\Bundle\SnappyBundle\KnpSnappyBundle(),
            new Nelmio\CorsBundle\NelmioCorsBundle(),
            new Algatux\InfluxDbBundle\InfluxDbBundle(),
            new Snc\RedisBundle\SncRedisBundle(),
            new Maba\Bundle\GentleForceBundle\MabaGentleForceBundle(),
            new Maba\Bundle\WebpackBundle\MabaWebpackBundle(),

            // Sonata Admin-related Bundles
            new Evp\Bundle\AdminBundle\EvpAdminBundle(),
            new Sonata\UserBundle\SonataUserBundle(),
            new Sonata\MediaBundle\SonataMediaBundle(),
            new Sonata\AdminBundle\SonataAdminBundle(),
            new Sonata\BlockBundle\SonataBlockBundle(),
            new Sonata\CacheBundle\SonataCacheBundle(),
            new Sonata\EasyExtendsBundle\SonataEasyExtendsBundle(),
            new Sonata\DoctrineORMAdminBundle\SonataDoctrineORMAdminBundle(),
            new Sonata\CoreBundle\SonataCoreBundle(),
            new Sonata\ClassificationBundle\SonataClassificationBundle(),

            // Application Bundles
            new Application\Sonata\UserBundle\ApplicationSonataUserBundle(),
            new Application\Sonata\MediaBundle\ApplicationSonataMediaBundle(),
            new Application\Sonata\ClassificationBundle\ApplicationSonataClassificationBundle(),

            // Evp vendor bundles
            new Evp\Bundle\WebToPayBundle\EvpWebToPayBundle(),
            new Evp\Bundle\GsmsBundle\EvpGsmsBundle(),
            new Evp\Bundle\MacAuthenticationBundle\EvpMacAuthenticationBundle(),
            new Evp\Bundle\RabbitMqExtensionBundle\EvpRabbitMqExtensionBundle(),
            new Evp\Bundle\LimitBundle\EvpLimitBundle(),
            new Evp\Bundle\RestClientBundle\EvpRestClientBundle(),
            new Evp\Bundle\GatewayCommonBundle\EvpGatewayCommonBundle(),
            new Evp\Bundle\AuditBundle\EvpAuditBundle(),
            new Evp\Bundle\CurrencyPricingBundle\EvpCurrencyPricingBundle(),
            new Evp\Bundle\IbanBundle\EvpIbanBundle(),
            new Evp\Bundle\IdentificationLevelCommonBundle\EvpIdentificationLevelCommonBundle(),
            new Evp\Bundle\ApplicationLoggingBundle\EvpApplicationLoggingBundle(),
            new Evp\Bundle\NameMatcherBundle\EvpNameMatcherBundle(),
            new Evp\Bundle\UtilBundle\EvpUtilBundle(),
            new Evp\Bundle\LbClientBundle\EvpLbClientBundle(),
            new Evp\Bundle\TranslationProxyClientBundle\EvpTranslationProxyClientBundle(),
            new Evp\Bundle\LuceneQueryBundle\EvpLuceneQueryBundle(),
            new Evp\Bundle\BankingHistoryClientBundle\EvpBankingHistoryClientBundle(),
            new Paysera\Bundle\ExternalRoutingBundle\PayseraExternalRoutingBundle(),
            new Paysera\Bundle\LockBundle\PayseraLockBundle(),
            new Paysera\Bundle\NormalizationBundle\PayseraNormalizationBundle(),
            new Paysera\Bundle\ApiBundle\PayseraApiBundle(),
            new Paysera\Bundle\RestMigrationBundle\PayseraRestMigrationBundle(),
            new Paysera\Bundle\PartnerCommonBundle\PayseraPartnerCommonBundle(),
            new Evp\Bundle\CurrencyProvider\EvpCurrencyProviderBundle(),

            // Evp Bundles
            new Evp\Bundle\AccountingBundle\EvpAccountingBundle(),
            new Evp\Bundle\ClientBundle\EvpClientBundle(),
            new Evp\Bundle\CurrencyBundle\EvpCurrencyBundle(),
            new Evp\Bundle\BankAccountBundle\EvpBankAccountBundle(),
            new Evp\Bundle\BankCommissionBundle\EvpBankCommissionBundle(),
            new Evp\Bundle\BankTransferBundle\EvpBankTransferBundle(),
            new Evp\Bundle\OperatorWidgetBundle\EvpOperatorWidgetBundle(),
            new Evp\Bundle\WorkflowBundle\EvpWorkflowBundle(),
            new Evp\Bundle\TransferProviderBundle\EvpTransferProviderBundle(),
            new Evp\Bundle\BankApiBundle\EvpBankApiBundle(),
            new Evp\Bundle\BankPermissionBundle\EvpBankPermissionBundle(),
            new Evp\Bundle\BankChargeBundle\EvpBankChargeBundle(),
            new Evp\Bundle\BankChargeRestBundle\EvpBankChargeRestBundle(),
            new Evp\Bundle\BankHoldBundle\EvpBankHoldBundle(),
            new Evp\Bundle\BankHoldRestBundle\EvpBankHoldRestBundle(),
            new Evp\Bundle\BankBundle\EvpBankBundle(),
            new Evp\Bundle\BankRefundBundle\EvpBankRefundBundle(),
            new Evp\Bundle\BankRefundRestBundle\EvpBankRefundRestBundle(),
            new Evp\Bundle\BankTransferTemplateBundle\EvpBankTransferTemplateBundle(),
            new Evp\Bundle\ClientNotificationBundle\EvpClientNotificationBundle(),
            new Evp\Bundle\BankSmsQueryBundle\EvpBankSmsQueryBundle(),
            new Evp\Bundle\PersistableEventBundle\EvpPersistableEventBundle(),
            new Evp\Bundle\BankProcessorBundle\EvpBankProcessorBundle(),
            new Evp\Bundle\VmiPaymentsBundle\EvpVmiPaymentsBundle(),
            new Evp\Bundle\TransferTaxBundle\EvpTransferTaxBundle(),
            new Evp\Bundle\WmxiBundle\EvpWmxiBundle(),
            new Evp\Bundle\CommonsBundle\EvpCommonsBundle(),
            new Evp\Bundle\BlacklistBundle\EvpBlacklistBundle(),
            new Evp\Bundle\TransferPlanBundle\EvpTransferPlanBundle(),
            new Evp\Bundle\SodraBundle\EvpSodraBundle(),
            new Evp\Bundle\ProtectedTransferBundle\EvpProtectedTransferBundle(),
            new Evp\Bundle\PayzaBundle\EvpPayzaBundle(),
            new Evp\Bundle\SepaBundle\EvpSepaBundle(),

            new Evp\Bundle\BankAccountRestBundle\EvpBankAccountRestBundle(),
            new Evp\Bundle\BankTransferRestBundle\EvpBankTransferRestBundle(),
            new Evp\Bundle\ClientRestBundle\EvpClientRestBundle(),
            new Evp\Bundle\RemoteEventIntegrationBundle\EvpRemoteEventIntegrationBundle(),
            new Evp\Bundle\CalendarBundle\EvpCalendarBundle(),
            new Evp\Bundle\BankPermissionRestBundle\EvpBankPermissionRestBundle(),
            new Evp\Bundle\SignerBundle\EvpSignerBundle(),
            new Evp\Bundle\BankTransferCommissionDiscountBundle\EvpBankTransferCommissionDiscountBundle(),
            new Evp\Bundle\VmiAccountReportBundle\EvpVmiAccountReportBundle(),
            new Evp\Bundle\TwigExtensionsBundle\EvpTwigExtensionsBundle(),
            new Evp\Bundle\CardBundle\EvpCardBundle(),
            new Evp\Bundle\CardGatewayCommonBundle\EvpCardGatewayCommonBundle(),
            new Evp\Bundle\CardGatewayRestBundle\EvpCardGatewayRestBundle(),
            new Evp\Bundle\QuestionnaireBundle\EvpQuestionnaireBundle(),
            new Evp\Bundle\QuestionnaireAIBundle\EvpQuestionnaireAIBundle(),
            new Evp\Bundle\BankAccountArrestBundle\EvpBankAccountArrestBundle(),
            new Evp\Bundle\UserBundle\EvpUserBundle(),
            new Evp\Bundle\ContisAccountingBundle\EvpContisAccountingBundle(),
            new Evp\Bundle\ContisBundle\EvpContisBundle(),
            new Evp\Bundle\ContisClientBundle\EvpContisClientBundle(),
            new Evp\Bundle\StatisticsBundle\EvpStatisticsBundle(),
            new Evp\Bundle\RelatedBankAccountsBundle\EvpRelatedBankAccountsBundle(),
            new Evp\Bundle\BankAccountBalanceBundle\EvpBankAccountBalanceBundle(),
            new Evp\Bundle\PlaisBundle\EvpPlaisBundle(),
            new Evp\Bundle\LbIntegrationBundle\EvpLbIntegrationBundle(),
            new Evp\Bundle\DebtorBundle\EvpDebtorBundle(),
            new Evp\Bundle\EntityRemoverBundle\EntityRemoverBundle(),
            new Evp\Bundle\CurrencyRestBundle\EvpCurrencyRestBundle(),
            new Evp\Bundle\NordeaLtPnpBundle\EvpNordeaLtPnpBundle(),
            new Evp\Bundle\MailerBundle\EvpMailerBundle(),
            new Evp\Bundle\BankingHistoryIntegrationBundle\EvpBankingHistoryIntegrationBundle(),
            new Evp\Bundle\GrsBundle\EvpGrsBundle(),
            new Evp\Bundle\DebtBundle\EvpDebtBundle(),
            new Evp\Bundle\VmiTiesReportBundle\EvpVmiTiesReportBundle(),
            new Evp\Bundle\IpidBundle\EvpIpidBundle(),

            // Paysera Bundles
            new Paysera\Bundle\FileSystemBundle\PayseraFileSystemBundle(),
            new Paysera\Bundle\DocumentBundle\PayseraDocumentBundle(),
            new Paysera\Bundle\RandomHashBundle\PayseraRandomHashBundle(),
            new Paysera\Bundle\FeatureFlagBundle\PayseraFeatureFlagBundle(),
            new Paysera\Bundle\RestBundle\PayseraRestBundle(),
            new Paysera\Bundle\TransferSurveillanceBundle\PayseraTransferSurveillanceBundle(),
            new Paysera\Bundle\TransferPartnerBundle\PayseraTransferPartnerBundle(),
            new Paysera\Bundle\InformationBundle\PayseraInformationBundle(),
            new Paysera\Bundle\MaintenanceTaskBundle\PayseraMaintenanceTaskBundle(),
            new Paysera\Bundle\MaintenanceBundle\PayseraMaintenanceBundle(),
            new Paysera\Bundle\TransferCallbackBundle\PayseraTransferCallbackBundle(),
            new Paysera\Bundle\MobilePaymentsBundle\PayseraMobilePaymentsBundle(),
            new Paysera\Bundle\AbaCodesBundle\PayseraAbaCodesBundle(),
            new Paysera\Bundle\TransferAmlBundle\PayseraTransferAmlBundle(),
            new Paysera\Bundle\JWTAuthenticationBundle\PayseraJWTAuthenticationBundle(),
            new Paysera\Bundle\SecurityBundle\PayseraSecurityBundle(),
            new Paysera\Bundle\UnidentifiedClientHoldBundle\PayseraUnidentifiedClientHoldBundle(),
            new Paysera\Bundle\CorrespondentBankBundle\CorrespondentBankBundle(),
            new Paysera\Bundle\VersobankBundle\PayseraVersobankBundle(),
            new Paysera\Bundle\Iso20022Bundle\PayseraIso20022Bundle(),
            new Paysera\Bundle\SepaInstantBundle\PayseraSepaInstantBundle(),
            new Paysera\AsyncEventBundle\PayseraAsyncEventBundle(),
            new Paysera\Bundle\ContisDebtRepaymentBundle\PayseraContisDebtRepaymentBundle(),
            new Paysera\Bundle\BankAccountFlagsBundle\PayseraBankAccountFlagsBundle(),
            new Paysera\Bundle\ClientChargeBundle\PayseraClientChargeBundle(),
            new Paysera\Bundle\MonitoringBundle\PayseraMonitoringBundle(),
            new Paysera\Bundle\TransferSignBundle\PayseraTransferSignBundle(),
            new Paysera\Bundle\InstaremBundle\PayseraInstaremBundle(),
            new Paysera\Bundle\ClientAllowanceBundle\PayseraClientAllowanceBundle(),
            new Paysera\Bundle\ClientAllowanceRestBundle\PayseraClientAllowanceRestBundle(),
            new Paysera\Bundle\TrustedBeneficiaryBundle\PayseraTrustedBeneficiaryBundle(),
            new Paysera\Bundle\MtBundle\PayseraMtBundle(),
            new Paysera\Bundle\SwiftMessagingBundle\PayseraSwiftMessagingBundle(),
            new Paysera\Bundle\SwiftBundle\PayseraSwiftBundle(),
            new Paysera\Bundle\Target2Bundle\PayseraTarget2Bundle(),
            new Paysera\Bundle\FnttBundle\PayseraFnttBundle(),
            new Paysera\Bundle\ContisPushNotificationsBundle\PayseraContisPushNotificationsBundle(),
            new Paysera\Bundle\PostbankBgIbanBundle\PayseraPostbankBgIbanBundle(),
            new Paysera\Bundle\TransferNormalizationRestBundle\PayseraTransferNormalizationRestBundle(),
            new Paysera\Bundle\ElasticsearchQueryMatcherBundle\PayseraElasticsearchQueryMatcherBundle(),
            new Paysera\Bundle\RiaBundle\PayseraRiaBundle(),
            new Paysera\Bundle\IfxBundle\PayseraIfxBundle(),
            new Paysera\Bundle\StatementTurnoverBundle\PayseraStatementTurnoverBundle(),
            new Paysera\Bundle\AccountingDiscrepancyBundle\PayseraAccountingDiscrepancyBundle(),
            new Paysera\Bundle\BgBudgetPaymentBundle\PayseraBgBudgetPaymentBundle(),
            new Paysera\Bundle\CardDesignBundle\PayseraCardDesignBundle(),
            new Paysera\Bundle\GeocodingBundle\PayseraGeocodingBundle(),
            new Paysera\Bundle\CurrencyExchangeBundle\PayseraCurrencyExchangeBundle(),
            new Paysera\Bundle\CurrencyOneBundle\PayseraCurrencyOneBundle(),
            new Paysera\Bundle\IbanAliasBundle\PayseraIbanAliasBundle(),
            new Paysera\Bundle\LibraBankBundle\PayseraLibraBankBundle(),
            new Paysera\Bundle\TransferImportBundle\PayseraTransferImportBundle(),
            new Paysera\Bundle\DoctrineCacheBundle\PayseraDoctrineCacheBundle(),
            new Paysera\Bundle\BullionBundle\PayseraBullionBundle(),
            new Paysera\Bundle\CommissionReportBundle\PayseraCommissionReportBundle(),
            new Paysera\Bundle\TransferAmlInformationBundle\PayseraTransferAmlInformationBundle(),
            new Paysera\Bundle\AfexBundle\PayseraAfexBundle(),
            new Paysera\Bundle\BpbBundle\PayseraBpbBundle(),
            new Paysera\Bundle\TransferSignWithAppBundle\PayseraTransferSignWithAppBundle(),
            new Paysera\Bundle\TntClientBundle\PayseraTntClientBundle(),
            new Paysera\Bundle\ContisRestClientBundle\PayseraContisRestClientBundle(),
            new Paysera\Bundle\DatabasePrimaryKeyCheckerBundle\PayseraDatabasePrimaryKeyCheckerBundle(),
            new Paysera\Bundle\TechVenturesBankBundle\PayseraTechVenturesBankBundle(),
            new Paysera\Bundle\SingleWindowBundle\PayseraSingleWindowBundle(),
            new Paysera\Bundle\GlobusBankBundle\PayseraGlobusBankBundle(),
            new Paysera\Bundle\PartnerRestBundle\PayseraPartnerRestBundle(),
            new Paysera\Bundle\LpbBankBundle\PayseraLpbBankBundle(),
            new Paysera\Bundle\TiranaBankBundle\PayseraTiranaBankBundle(),
            new Paysera\Bundle\PartnerBundle\PayseraPartnerBundle(),
            new Paysera\Bundle\BankaEkonomikeBundle\PayseraBankaEkonomikeBundle(),
            new Paysera\Bundle\KipsBundle\PayseraKipsBundle(),
            new Paysera\Bundle\RiaSingleWindowBundle\PayseraRiaSingleWindowBundle(),
            new Paysera\Bundle\PrivatbankBundle\PayseraPrivatbankBundle(),
            new Paysera\Bundle\EpayBundle\PayseraEpayBundle(),
            new Paysera\Bundle\CredinsBankAlBundle\CredinsBankAlBundle(),
            new Paysera\Bundle\BrowserApiBundle\PayseraBrowserApiBundle(),
            new Paysera\Bundle\Target2RTGSBundle\PayseraTarget2RTGSBundle(),
            new Paysera\Bundle\GeorgiaRTGSBundle\PayseraGeorgiaRTGSBundle(),
            new Paysera\Bundle\EpayClientBundle\PayseraEpayClientBundle(),
            new Paysera\Bundle\AccountingOperationBundle\PayseraAccountingOperationBundle(),
            new Paysera\Bundle\AirwallexClientBundle\PayseraAirwallexClientBundle(),
            new Paysera\Bundle\AirwallexBundle\PayseraAirwallexBundle(),
            new Paysera\Bundle\DhlClientBundle\PayseraDhlClientBundle(),
            new Paysera\Bundle\ContisMasterAccountBundle\PayseraContisMasterAccountBundle(),
            new Paysera\Bundle\CurrencycloudBundle\CurrencycloudBundle(),
            new Paysera\Bundle\GeorgiaIbanBundle\GeorgiaIbanBundle(),
            new Paysera\Bundle\GeorgiaDebtorRegistryBundle\GeorgiaDebtorRegistryBundle(),
            new Paysera\Bundle\PribankBundle\PayseraPribankBundle(),
            new Paysera\Bundle\KosovoIbanBundle\KosovoIbanBundle(),
            new Paysera\Bundle\GeorgiaBalanceAccountBundle\GeorgiaBalanceAccountBundle(),
            new Paysera\Bundle\CashPickupTransferBundle\PayseraCashPickupTransferBundle(),
            new Paysera\Bundle\GeorgiaTreasuryPaymentBundle\PayseraGeorgiaTreasuryPaymentBundle(),
            new Paysera\Bundle\GeorgiaRTGSRestBundle\PayseraGeorgiaRTGSRestBundle(),
            new Paysera\Bundle\TurnoverBundle\PayseraTurnoverBundle(),
            new Paysera\Bundle\TurnoverRestBundle\PayseraTurnoverRestBundle(),
            new Paysera\Bundle\IbanDetailsBundle\PayseraIbanDetailsBundle(),
            new Paysera\Bundle\TransferValidatorBundle\TransferValidatorBundle(),
            new Paysera\Bundle\ClientAverageBalanceBundle\PayseraClientAverageBalanceBundle(),
            new Paysera\Bundle\ClientMonthlyChargeFeeBundle\PayseraClientMonthlyChargeFeeBundle(),
            new Paysera\Bundle\CardIntegrationBundle\PayseraCardIntegrationBundle(),
            new Paysera\Bundle\PayseraRiskLevelIntegrationBundle\PayseraRiskLevelIntegrationBundle(),
            new Paysera\Bundle\UnhandledMessageBundle\UnhandledMessageBundle(),
            new Paysera\Bundle\FileManagementBundle\PayseraFileManagementBundle(),
            new Paysera\Bundle\CardSwitchBundle\CardSwitchBundle(),
            new Paysera\Bundle\CompassPlusBundle\PayseraCompassPlusBundle(),
            new Paysera\Bundle\TransferDetailsTranslationBundle\PayseraTransferDetailsTranslationBundle(),
            new Paysera\Bundle\AIAssistantBundle\PayseraAIAssistantBundle(),
            new Paysera\Bundle\IntermediateStatementBundle\PayseraIntermediateStatementBundle(),
            new Paysera\Bundle\DelayedCommissionBundle\PayseraDelayedCommissionBundle(),
            new Paysera\Bundle\SwiftISOBundle\PayseraSwiftISOBundle(),
        ];

        if ($this->getEnvironment() === 'sandbox') {
            $bundles[] = new Evp\Bundle\SandboxBundle\EvpSandboxBundle();
        }

        if ($this->getEnvironment() !== 'dev') {
            $bundles[] = new Evp\Bundle\BankGatewayBundle\EvpBankGatewayBundle();
        }

        if (in_array($this->getEnvironment(), ['dev', 'test', 'test_file_db', 'dj_test'])) {
            $bundles[] = new Symfony\Bundle\WebProfilerBundle\WebProfilerBundle();
            $bundles[] = new Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle();
            $bundles[] = new Symfony\Bundle\DebugBundle\DebugBundle();
            $bundles[] = new Evp\Bundle\TestCaseBundle\EvpTestCaseBundle();
            $bundles[] = new Paysera\Bundle\DeveloperBundle\PayseraDeveloperBundle();
            $bundles[] = new Paysera\Bundle\DatabaseInitBundle\PayseraDatabaseInitBundle();
            $bundles[] = new Paysera\Bundle\MtGeneratorBundle\PayseraMtGeneratorBundle();
        }

        return $bundles;
    }

    public function registerContainerConfiguration(LoaderInterface $loader)
    {
        $loader->load(__DIR__ . '/config/config_' . $this->getEnvironment() . '.yml');
    }

    public function boot()
    {
        setlocale(LC_ALL, 'en_US.utf8');
        mb_internal_encoding('UTF-8');
        parent::boot();
    }
}
