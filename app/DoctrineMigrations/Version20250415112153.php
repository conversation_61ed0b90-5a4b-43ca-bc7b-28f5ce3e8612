<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250415112153 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->abortIf(
            $this->connection->getDatabasePlatform()->getName() !== 'mysql',
            'Migration can only be executed safely on \'mysql\'.'
        );

        $this->addSql('CREATE TABLE checkout_delayed_commission (
                id BIGINT AUTO_INCREMENT NOT NULL, 
                commission_hold_id BIGINT NOT NULL, 
                project_id VARCHAR(40) NOT NULL, 
                payment_id VARCHAR(40) NOT NULL, 
                UNIQUE INDEX UNIQ_CC19462F4C46099 (commission_hold_id), 
                INDEX IDX_CC19462F166D1F9C (project_id), 
                PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');

        $this->addSql('CREATE TABLE bank_commission_hold (
                id BIGINT AUTO_INCREMENT NOT NULL, 
                hold_id INT NOT NULL, 
                created_at DATETIME NOT NULL, 
                status VARCHAR(50) NOT NULL, 
                UNIQUE INDEX UNIQ_A5C3F8E9AD118649 (hold_id), 
                INDEX IDX_A5C3F8E97B00651C (status), 
                INDEX IDX_A5C3F8E98B8E8428 (created_at), 
                INDEX IDX_A5C3F8E97B00651C8B8E8428 (status, created_at), 
                PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');

        $this->addSql('ALTER TABLE checkout_delayed_commission ADD CONSTRAINT FK_CC19462F4C46099 FOREIGN KEY (commission_hold_id) REFERENCES bank_commission_hold (id)');
        $this->addSql('ALTER TABLE bank_commission_hold ADD CONSTRAINT FK_A5C3F8E9AD118649 FOREIGN KEY (hold_id) REFERENCES bank_hold (id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(
            $this->connection->getDatabasePlatform()->getName() !== 'mysql',
            'Migration can only be executed safely on \'mysql\'.'
        );

        $this->addSql('ALTER TABLE checkout_delayed_commission DROP FOREIGN KEY FK_CC19462F4C46099');
        $this->addSql('DROP TABLE checkout_delayed_commission');
        $this->addSql('DROP TABLE bank_commission_hold');
    }
}
