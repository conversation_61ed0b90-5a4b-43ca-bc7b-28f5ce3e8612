<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250516092435 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE licensed_partners ADD official_name VARCHAR(255) NULL, ADD official_address VARCHAR(255) NULL, ADD bank_identification_code VARCHAR(20) NULL');

        $this->addSql("UPDATE licensed_partners SET official_name = 'Paysera LT, UAB', official_address = 'Pilaitės pr. 16, Vilnius, LT-04352, Lithuania', bank_identification_code = 'EVIULT2VXXX' WHERE partner_code = 'paysera_lt'");
        $this->addSql("UPDATE licensed_partners SET official_name = 'Paysera Bank Georgia, JSC', official_address = 'Akaki Tsereteli Ave. 4/2, Tbilisi, 0112, Georgia', bank_identification_code = 'PSRAGE22XXX'  WHERE partner_code = 'paysera_ge'");
        $this->addSql("UPDATE licensed_partners SET official_name = 'Paysera Kosova', official_address = 'Rr. Perandori Justinian, No 132, 10000 Prishtina, Kosovo', bank_identification_code = 'PAYKXKP2XXX'  WHERE partner_code = 'paysera_xk'");
        $this->addSql("UPDATE licensed_partners SET official_name = 'Paysera Albania SH.P.K.', official_address = 'Njesia Administrative Nr. 2, Rr. Fadil Rada, Pallati Donika, Kati i tretë, Tirana, Albania', bank_identification_code = 'PYALALT2XXX'  WHERE partner_code = 'paysera_al'");

        $this->addSql('ALTER TABLE licensed_partners MODIFY official_name VARCHAR(255) NOT NULL, MODIFY official_address VARCHAR(255) NOT NULL, MODIFY bank_identification_code VARCHAR(11) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE licensed_partners DROP official_name, DROP official_address, DROP bank_identification_code');
    }
}
