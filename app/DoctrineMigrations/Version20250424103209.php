<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250424103209 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE transfer_surveillance_match_module_fields DROP FOREIGN KEY FK_17460099AFC2B591');
        $this->addSql('DROP INDEX UNIQ_17460099AFC2B5915E237E06 ON transfer_surveillance_match_module_fields');

        $this->addSql('
            ALTER TABLE transfer_surveillance_match_module_fields 
            CHANGE name system_key VARCHAR(100) NOT NULL,
            ADD title VARCHAR(100) NOT NULL AFTER system_key
        ');

        $this->addSql('ALTER TABLE transfer_surveillance_match_module_fields ADD CONSTRAINT FK_17460099AFC2B591 FOREIGN KEY (module_id) REFERENCES transfer_surveillance_match_modules (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_17460099AFC2B59147280172 ON transfer_surveillance_match_module_fields (module_id, system_key)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE transfer_surveillance_match_module_fields DROP FOREIGN KEY FK_17460099AFC2B591');
        $this->addSql('DROP INDEX UNIQ_17460099AFC2B59147280172 ON transfer_surveillance_match_module_fields');

        $this->addSql('
            ALTER TABLE transfer_surveillance_match_module_fields 
            DROP title,
            CHANGE system_key name VARCHAR(100) CHARACTER SET utf8mb3 NOT NULL COLLATE `utf8mb3_unicode_ci`
        ');

        $this->addSql('ALTER TABLE transfer_surveillance_match_module_fields ADD CONSTRAINT FK_17460099AFC2B591 FOREIGN KEY (module_id) REFERENCES transfer_surveillance_match_modules (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_17460099AFC2B5915E237E06 ON transfer_surveillance_match_module_fields (module_id, name)');
    }
}
