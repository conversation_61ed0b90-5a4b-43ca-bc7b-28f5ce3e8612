<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * iPiD Bundle Migration: Create ipid_api_response and transfer_ipid_consent tables 
 * with all required columns including response_code, response_message, and scheme-specific fields per validate_api.txt and node_api.txt
 */
final class Version20250126000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create iPiD Bundle tables with complete structure including response codes and scheme-specific fields (VOP/COP)';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        // Create ipid_api_response table with all required columns including scheme-specific fields
        $this->addSql('CREATE TABLE ipid_api_response (
            id INT AUTO_INCREMENT PRIMARY KEY,
            request_hash VARCHAR(64) NOT NULL,
            country VARCHAR(2) NOT NULL,
            match_score FLOAT NULL,
            match_level VARCHAR(32) NULL,
            response_status VARCHAR(32) NOT NULL,
            response_code VARCHAR(4) NOT NULL,
            response_message VARCHAR(255) NOT NULL,
            requires_consent BOOLEAN NULL,
            created_at DATETIME NOT NULL,
            vop_id_match VARCHAR(16) NULL COMMENT "VOP ID verification result (MTCH/NMTC/NOAP)",
            vop_name_match VARCHAR(16) NULL COMMENT "VOP name verification result (MTCH/NMTC/CMTC/NOAP)",
            cop_matched TINYINT(1) NULL COMMENT "COP match result (true/false)",
            cop_reason VARCHAR(16) NULL COMMENT "COP reason code (MATC/ANNM/MBAM/etc)",
            reason_code VARCHAR(16) NULL COMMENT "Additional reason code for validation failures",
            INDEX idx_request_hash_country (request_hash, country),
            INDEX idx_created_at (created_at),
            INDEX idx_response_code (response_code),
            INDEX idx_vop_id_match (vop_id_match),
            INDEX idx_vop_name_match (vop_name_match),
            INDEX idx_cop_matched (cop_matched),
            INDEX idx_cop_reason (cop_reason)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

        // Create transfer_ipid_consent table
        $this->addSql('CREATE TABLE transfer_ipid_consent (
            id INT AUTO_INCREMENT PRIMARY KEY,
            transfer_id INT NOT NULL,
            ipid_api_response_id INT NOT NULL,
            consent_status VARCHAR(32) NOT NULL,
            match_level VARCHAR(32) NOT NULL,
            requires_manual_review BOOLEAN NOT NULL DEFAULT FALSE,
            validation_notes TEXT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL,
            INDEX idx_transfer_id (transfer_id),
            INDEX idx_consent_status (consent_status),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (ipid_api_response_id) REFERENCES ipid_api_response(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE transfer_ipid_consent');
        $this->addSql('DROP TABLE ipid_api_response');
    }
} 