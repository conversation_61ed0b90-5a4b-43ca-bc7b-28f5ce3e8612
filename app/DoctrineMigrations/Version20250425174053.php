<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250425174053 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE TABLE blacklist_names_comparison_results (id BIGINT AUTO_INCREMENT NOT NULL, transfer_id INT DEFAULT NULL, check_result_id INT DEFAULT NULL, left_name VARCHAR(255) NOT NULL, right_name VARCHAR(255) NOT NULL, model_answer LONGTEXT DEFAULT NULL, decision VARCHAR(255) NOT NULL, failure_reason VARCHAR(255) DEFAULT NULL, response_code INT NOT NULL, request_duration INT NOT NULL, model VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_64F264E184ACBE48 (decision), INDEX IDX_64F264E1902D5744 (failure_reason), PRIMARY KEY(id)) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE blacklist_names_comparison_results ADD CONSTRAINT FK_64F264E1537048AF FOREIGN KEY (transfer_id) REFERENCES bank_transfer (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE blacklist_names_comparison_results ADD CONSTRAINT FK_64F264E1AD400483 FOREIGN KEY (check_result_id) REFERENCES blacklist_client_blacklist_check_results (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE blacklist_names_comparison_results');
    }
}
