<?php

declare(strict_types=1);

namespace Application\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250509091011 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql(
            'CREATE TABLE transfer_surveillance_maintenance_transfer_inspection_change_log (
            id INT AUTO_INCREMENT NOT NULL, 
            inspection_id INT NOT NULL, 
            date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            field VARCHAR(50) NOT NULL, 
            from_value LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\', 
            to_value LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\', 
            task_identifier VARCHAR(50) NOT NULL, 
            INDEX IDX_128BC0666B3DC8C5 (task_identifier), 
            PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET UTF8 COLLATE `UTF8_unicode_ci` ENGINE = InnoDB'
        );
        $this->addSql(
            'ALTER TABLE transfer_surveillance_maintenance_transfer_inspection_change_log 
            ADD CONSTRAINT FK_128BC066F02F2DDF 
            FOREIGN KEY (inspection_id) 
            REFERENCES transfer_surveillance_transfer_inspections (id)'
        );
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->abortIf($this->connection->getDatabasePlatform()->getName() !== 'mysql', 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP TABLE transfer_surveillance_maintenance_transfer_inspection_change_log');
    }
}
