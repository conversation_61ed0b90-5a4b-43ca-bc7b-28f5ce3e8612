<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="evp_calendar.mock.holidays.lt_lb" type="collection">
            <parameter type="collection">
                <parameter>1</parameter>
                <parameter>1</parameter>
            </parameter>
        </parameter>

        <parameter key="evp_calendar.mock.holidays.lt_nordea_urgent.usd" type="collection">
            <parameter type="collection">
                <parameter>1</parameter>
                <parameter>1</parameter>
            </parameter>
            <parameter type="collection">
                <parameter>1</parameter>
                <parameter>2</parameter>
            </parameter>
        </parameter>

        <parameter key="evp_bank_transfer.route.matcher.offshore_countries" type="collection">
            <parameter>CY</parameter>
            <parameter>LI</parameter>
            <parameter>LU</parameter>
            <parameter>MT</parameter>
            <parameter>NL</parameter>
            <parameter>CH</parameter>
            <parameter>GB</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.route.matcher.high_risk_countries" type="collection">
            <parameter>AF</parameter>
            <parameter>LA</parameter>
            <parameter>UG</parameter>
            <parameter>PK</parameter>
            <parameter>ML</parameter>
            <parameter>MZ</parameter>
            <parameter>KH</parameter>
            <parameter>GW</parameter>
            <parameter>TZ</parameter>
            <parameter>KE</parameter>
            <parameter>LR</parameter>
        </parameter>

        <parameter key="evp_bank_transfer.route.matcher.categorized_countries" type="collection">
            <parameter type="collection" key="eu">
                <parameter>AT</parameter>
                <parameter>BE</parameter>
                <parameter>BG</parameter>
                <parameter>CY</parameter>
                <parameter>CZ</parameter>
                <parameter>DK</parameter>
                <parameter>EE</parameter>
                <parameter>FI</parameter>
                <parameter>FR</parameter>
                <parameter>DE</parameter>
                <parameter>GR</parameter>
                <parameter>SE</parameter>
                <parameter>GB</parameter>
            </parameter>
            <parameter type="collection" key="eea">
                <parameter>AT</parameter>
                <parameter>BE</parameter>
                <parameter>BG</parameter>
                <parameter>CY</parameter>
                <parameter>CZ</parameter>
                <parameter>DK</parameter>
                <parameter>EE</parameter>
                <parameter>FI</parameter>
                <parameter>FR</parameter>
                <parameter>DE</parameter>
                <parameter>GR</parameter>
                <parameter>HU</parameter>
                <parameter>HR</parameter>
                <parameter>IE</parameter>
                <parameter>IT</parameter>
                <parameter>LV</parameter>
                <parameter>LT</parameter>
                <parameter>LU</parameter>
                <parameter>MT</parameter>
                <parameter>NL</parameter>
                <parameter>PL</parameter>
                <parameter>PT</parameter>
                <parameter>RO</parameter>
                <parameter>SK</parameter>
                <parameter>SI</parameter>
                <parameter>ES</parameter>
                <parameter>SE</parameter>
                <parameter>NO</parameter>
                <parameter>IS</parameter>
                <parameter>LI</parameter>
            </parameter>
            <parameter type="collection" key="africa">
                <parameter>ZM</parameter>
                <parameter>SC</parameter>
                <parameter>NA</parameter>
                <parameter>MA</parameter>
            </parameter>
            <parameter key="offshore" type="string">%evp_bank_transfer.route.matcher.offshore_countries%</parameter>
            <parameter key="high_risk" type="string">%evp_bank_transfer.route.matcher.high_risk_countries%</parameter>
            <parameter type="collection" key="%evp_bank_transfer.country_list.eu_commission_hr%">
                <parameter>AF</parameter>
                <parameter>AE</parameter>
                <parameter>BB</parameter>
                <parameter>BF</parameter>
                <parameter>BS</parameter>
                <parameter>BW</parameter>
                <parameter>GI</parameter>
                <parameter>GY</parameter>
                <parameter>GY</parameter>
                <parameter>HT</parameter>
                <parameter>IQ</parameter>
                <parameter>JM</parameter>
                <parameter>JO</parameter>
                <parameter>KH</parameter>
                <parameter>KY</parameter>
                <parameter>MA</parameter>
                <parameter>ML</parameter>
                <parameter>MM</parameter>
                <parameter>MU</parameter>
                <parameter>MZ</parameter>
                <parameter>PA</parameter>
                <parameter>PH</parameter>
                <parameter>SN</parameter>
                <parameter>SS</parameter>
                <parameter>SY</parameter>
                <parameter>TT</parameter>
                <parameter>TZ</parameter>
                <parameter>UG</parameter>
                <parameter>VU</parameter>
                <parameter>YE</parameter>
            </parameter>
            <parameter type="collection" key="%evp_bank_transfer.country_list.fatf_hr%">
                <parameter>AE</parameter>
                <parameter>BB</parameter>
                <parameter>BF</parameter>
                <parameter>CD</parameter>
                <parameter>GI</parameter>
                <parameter>HT</parameter>
                <parameter>IR</parameter>
                <parameter>JM</parameter>
                <parameter>JO</parameter>
                <parameter>KP</parameter>
                <parameter>KY</parameter>
                <parameter>ML</parameter>
                <parameter>MM</parameter>
                <parameter>MZ</parameter>
                <parameter>NG</parameter>
                <parameter>PA</parameter>
                <parameter>PH</parameter>
                <parameter>SN</parameter>
                <parameter>SS</parameter>
                <parameter>SY</parameter>
                <parameter>TR</parameter>
                <parameter>TZ</parameter>
                <parameter>UG</parameter>
                <parameter>YE</parameter>
                <parameter>ZA</parameter>
            </parameter>
            <parameter type="collection" key="basel_index_hr">
                <parameter>CD</parameter>
                <parameter>HT</parameter>
                <parameter>KH</parameter>
                <parameter>KY</parameter>
                <parameter>MG</parameter>
                <parameter>ML</parameter>
                <parameter>MM</parameter>
                <parameter>MR</parameter>
                <parameter>MZ</parameter>
                <parameter>SN</parameter>
                <parameter>UG</parameter>
            </parameter>
            <parameter type="collection" key="aml_natural_user_hr">
                <parameter>BJ</parameter>
                <parameter>CM</parameter>
                <parameter>CG</parameter>
            </parameter>
        </parameter>
        <parameter key="paysera_statement_turnover.create_statement_turnover_worker_batch_limit">10</parameter>
        <parameter key="paysera_statement_turnover.batch_size">5</parameter>
        <parameter key="evp_sepa.shared_directory" type="string">%kernel.root_dir%/../src/Evp/Bundle/SepaBundle/Tests/Resources/</parameter>
        <parameter type="string" key="evp_debt.start_write_off_time">10:00:00</parameter>
        <parameter type="string" key="evp_debt.end_write_off_time">16:25:00</parameter>
    </parameters>

    <services>
        <service id="evp_commons.dedicated_entity_manager_builder"
                 class="Evp\Bundle\CommonsBundle\Tests\TestService\DedicatedEntityManagerBuilder">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="doctrine.dbal.connection_factory"/>
        </service>

        <service id="evp_translation_proxy_client.service.client"
                 class="Evp\Bundle\CommonsBundle\Tests\Mock\TranslationProxyClientMock">
        </service>

        <service id="evp_bank_account.local_account_provider"
                 class="Evp\Bundle\BankAccountBundle\Tests\TestService\LocalAccountProvider">
            <tag name="evp_bank_account.account_provider" key="local"/>
            <tag name="evp_bank_account.account_provider" key="technical"/>
            <tag name="evp_bank_account.account_provider" key="transit"/>

            <argument type="service" id="doctrine.orm.default_entity_manager" />
            <argument type="service" id="evp_bank_account.internal_account_manager" />
            <argument type="service" id="event_dispatcher" />
            <argument type="service" id="evp_bank_account.generic_account_manager" />
            <argument type="service" id="evp_bank_account.internal_account_transfer_manager" />
            <argument type="service" id="logger" />
        </service>

        <service id="fos_elastica.finder.sanctions_production_v1.profile" alias="fos_elastica.finder.test_sanctions_production_v1.profile"/>
        <service id="fos_elastica.finder.sanctions_secondary_v1.profile" alias="fos_elastica.finder.test_sanctions_secondary_v1.profile"/>

        <service id="fos_elastica.object_persister.sanctions_secondary_v1.profile" alias="fos_elastica.object_persister.test_sanctions_secondary_v1.profile"/>

        <service id="evp_contis.rabbit_mq_plain_client"
                 class="Evp\Bundle\ContisBundle\Tests\TestService\RabbitMqExtention\PlainClient">
            <argument type="service" id="old_sound_rabbit_mq.connection.records_queue"/>
        </service>

        <service id="evp_bank_account.doctrine_query_helper"
            class="Evp\Component\Doctrine\Tests\Mocks\MockDoctrineQueryHelper">
            <argument>30</argument>
        </service>

        <service id="evp_sepa.sepa_participant_detector"
                 class="Evp\Bundle\BankTransferBundle\Tests\Mocks\MockSepaParticipantDetector">
            <argument type="service" id="evp_bank.swift_manager"/>
            <argument type="service" id="evp_bank.swift_country_resolver"/>
            <argument type="service" id="evp_sepa.repository.sepa_participant"/>
            <argument type="service" id="doctrine_cache.providers.sepa_participant_cache"/>
            <argument type="service" id="logger"/>
            <argument type="collection">
                <argument>CBVILT2XXXX</argument>
                <argument>RBOSGB2LXXX</argument>
                <argument>AGBLLT2XXXX</argument>
                <argument>NDEALT2XXXX</argument>
                <argument>HABALT22XXX</argument>
                <argument>HABALTXXX21</argument>
            </argument>
        </service>

        <service id="evp_lb_client.client" class="Evp\Bundle\LbClientBundle\Service\ConfiguredClient">
            <call method="setBicByIbanList">
                <argument type="collection">
                    <argument type="collection" key="********************">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="********************">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="********************">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="********************">
                        <argument>AGBLLT2XXXX</argument>
                        <argument>DNB</argument>
                    </argument>
                    <argument type="collection" key="********************">
                        <argument>AGBLLT2XXXX</argument>
                        <argument>DNB</argument>
                    </argument>
                    <argument type="collection" key="LT967044060001033651">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT027300010079113705">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT047300010127345658">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT297300010135784887">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT437044060003520036">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT212140030003000061">
                        <argument>NDEALT2XXXX</argument>
                        <argument>Nordea</argument>
                    </argument>
                    <argument type="collection" key="LT687044060001234557">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT037400024432223810">
                        <argument>SMPOLT22XXX</argument>
                        <argument>Danske</argument>
                    </argument>
                    <argument type="collection" key="LT817044060003086732">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT267300010038631875">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT437044060002780217">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT197044000328642743">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT297044060001919885">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT814010051001935349">
                        <argument>AGBLLT2XXXX</argument>
                        <argument>DNB</argument>
                    </argument>
                    <argument type="collection" key="LT947300010099140415">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT497044060004930793">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT667300010140328186">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT137300010000202164">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT757300010071007071">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT557300010036894083">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT777300010097151639">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT314010045500050077">
                        <argument>AGBLLT2XXXX</argument>
                        <argument>DNB</argument>
                    </argument>
                    <argument type="collection" key="LT394010042500896166">
                        <argument>AGBLLT2XXXX</argument>
                        <argument>DNB</argument>
                    </argument>
                    <argument type="collection" key="LT217300010092146283">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT617300010089139436">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="LT217044060007678919">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT707400030959823830">
                        <argument>SMPOLT22XXX</argument>
                        <argument>Danske</argument>
                    </argument>
                    <argument type="collection" key="LT677300010103261170">
                        <argument>HABALT22XXX</argument>
                        <argument>Swedbank</argument>
                    </argument>
                    <argument type="collection" key="********************">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="LT547300010114335215">
                        <argument>CBVILT2XXXX</argument>
                        <argument>SEB</argument>
                    </argument>
                    <argument type="collection" key="GB48RBOS16108510094218">
                        <argument>RBOSGB2LXXX</argument>
                        <argument>ROYAL BANK OF SCOTLAND</argument>
                    </argument>
                </argument>
            </call>
        </service>

        <service id="evp_rest_user_api_client" class="Evp\Bundle\BankingHistoryIntegrationBundle\Tests\TestService\MockRestUserClient"/>
        <service id="evp_sepa.user_client" class="Evp\Bundle\SepaBundle\Tests\TestService\MockUserClient"/>
        <service id="evp_blacklist.user_client" class="Evp\Bundle\BlacklistBundle\Tests\Service\MockUserClient"/>

        <service id="evp_contis.account_state_helper"
                 class="Evp\Bundle\ContisBundle\Tests\TestService\MockAccountStateHelper">
            <argument id="paysera_contis_rest_client.account_client" type="service"/>
            <argument id="evp_contis.repository.contis_account" type="service"/>
            <argument id="evp_bank_transfer.repository.transfer" type="service"/>
            <argument id="evp_contis.account_balance_helper" type="service"/>
            <argument id="evp_contis.card_holder_to_close_account_resolver" type="service"/>
        </service>

        <service id="evp_lb_integration.sign_manager"
                 class="Paysera\Bundle\SepaInstantBundle\Tests\Mocks\Service\MockSignManager">
            <argument type="service">
                <service class="GuzzleHttp\Client">
                    <argument type="collection">
                        <argument key="base_uri">%env(LB_INTEGRATION_SIGNATURE_API_URI)%</argument>
                        <argument key="timeout">10</argument>
                    </argument>
                </service>
            </argument>
            <argument type="service" id="paysera_monitoring.monitoring_client"/>
            <argument>%sepa_instant.lb_public_key%</argument>
            <argument>0</argument>
            <argument type="string"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="paysera_sepa_instant.sepa_instant_client"
                 class="Paysera\Bundle\SepaInstantBundle\Tests\Mocks\Service\MockSepaInstantClient"/>

        <service id="evp_calendar.service.holidays" class="Evp\Bundle\CalendarBundle\Service\HolidaysService">
            <argument type="service" id="evp_bank.resolver.country.bank_key"/>

            <call method="addBankHolidays">
                <argument>lt_lb</argument>
                <argument type="collection">
                    <argument key="allCurrencies">%evp_calendar.mock.holidays.lt_lb%</argument>
                </argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
            </call>

            <call method="addBankHolidays">
                <argument>lt_lb_sepa_local</argument>
                <argument type="collection">
                    <argument key="allCurrencies">%evp_calendar.holidays.lt_lb_sepa%</argument>
                </argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>true</argument>
            </call>

            <call method="addBankHolidays">
                <argument>lt_nordea_urgent_international</argument>
                <argument type="collection">
                    <argument key="USD">%evp_calendar.mock.holidays.lt_nordea_urgent.usd%</argument>
                </argument>
                <argument>true</argument>
                <argument>true</argument>
                <argument>false</argument>
            </call>

        </service>
        <service id="paysera_fntt.fntt_client" class="Paysera\Bundle\FnttBundle\Tests\Mocks\MockFnttClient"/>

        <service id="evp_currency.currency_converter.official"
                 class="Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter"/>
        <service id="evp_currency.currency_converter.market_value"
                 class="Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter"/>
        <service id="evp_currency.evp_api_buy_currency_converter"
                 class="Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter"/>
        <service id="evp_currency.evp_api_sell_currency_converter"
                 class="Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter"/>
        <service id="evp_currency.currency_converter.official_by_partner"
                 class="Evp\Bundle\CurrencyBundle\Tests\Fixtures\HardcodedTestCurrencyConverter"/>

        <service id="logger" class="Symfony\Component\Debug\BufferingLogger"/>
        <service id="monolog.logger_prototype" class="\Paysera\Component\Tests\Mocks\LoggerDecorator">
            <call method="setLogger">
                <argument type="service" id="logger"/>
            </call>
            <argument>name</argument>
        </service>

        <service id="maba_gentle_force.throttler" class="Maba\GentleForce\NullThrottler"/>

        <service id="test.client" class="Evp\Component\BrowserKit\Client">
            <argument type="service" id="kernel"/>
            <argument>%test.client.parameters%</argument>
            <argument type="service" id="test.client.history"/>
            <argument type="service" id="test.client.cookiejar"/>
        </service>

        <service id="paysera_geocoding_budle.provider.google_maps_api"
                 class="Paysera\Bundle\GeocodingBundle\Tests\Fixtures\GoogleMapsApiMock"/>

        <service id="evp_wmxi.wmxi" class="Evp\Bundle\WmxiBundle\Tests\Fixtures\WMXIMock"/>
        <service id="evp_bank_account.available_iban_storage"
                 class="Evp\Bundle\BankAccountBundle\Tests\Service\IbanStorageMock"/>

        <service id="paysera_client_charge.client_commission_provider"
                 class="Paysera\Bundle\ClientChargeBundle\Tests\Mocks\ClientCommissionProviderMock">
            <argument type="service" id="paysera_client_charge.repository.client_commission"/>
        </service>

        <service id="evp_vmi_account_report.identification_client" class="Evp\Tests\Mock\IdentificationClientMock">
            <argument type="collection">
                <argument type="service">
                    <service class="Evp\Component\UserCommon\Identification\Entity\Identity">
                        <call method="setNationality">
                            <argument>LT</argument>
                        </call>
                        <call method="setCountryOfIssue">
                            <argument>LT</argument>
                        </call>
                    </service>
                </argument>
            </argument>
        </service>

        <service id="tax_information_client.client.rest_client" class="Evp\Tests\Mock\TaxInformationClientMock">
            <argument type="service">
                <service class="Paysera\Client\TaxInformationClient\Entity\TaxInformationIdentifiersList">
                    <call method="setItems">
                        <argument type="collection">
                            <service class="Paysera\Client\TaxInformationClient\Entity\TaxInformationIdentifier">
                                <call method="setUuid">
                                    <argument>xhas-h123-auuid</argument>
                                </call>
                                <call method="setTin">
                                    <argument>***********</argument>
                                </call>
                            </service>
                        </argument>
                    </call>
                </service>
            </argument>
        </service>

        <service id="evp_accounting.service.record_provider"
                 class="Evp\Bundle\AccountingBundle\Tests\Mocks\MockRecordProvider">
            <argument id="paysera_accounting_maintenance.client" type="service"/>
            <argument id="evp_accounting.denormalizer.record_result" type="service"/>
        </service>

        <service id="evp_bank_transfer.transfer_in_event_storage"
                 class="Evp\Bundle\BankTransferBundle\Tests\Mocks\MockTransferInEventStorage"/>

        <service id="evp_bank_transfer.transfer_in_event_locker"
                 class="Evp\Bundle\BankTransferBundle\Tests\Functional\TransferInEvent\MockTransferInEventLocker"/>

        <service id="evp_client.service.client_identification_document_provider"
                 class="Evp\Bundle\ClientBundle\Tests\Mock\MockClientIdentificationDocumentProvider"/>

        <service id="paysera_transfer_surveillance.service.comment_provider"
                 class="Paysera\Bundle\TransferSurveillanceBundle\Tests\Mocks\MockCommentProvider"/>

        <service id="paysera_transfer_surveillance.service.comment_cached_provider"
                 class="Paysera\Bundle\TransferSurveillanceBundle\Tests\Mocks\MockCommentCachedProvider"/>

        <service id="evp_questionnaire.service.questionnaire_cached_result_provider"
                 class="Evp\Bundle\QuestionnaireBundle\Tests\Mocks\MockQuestionnaireCachedResultProvider">
            <argument type="service" id="evp_questionnaire_service.repositories.questionnaire" />
        </service>

        <service id="evp_bank_account.service.migration_feature_flag_storage"
                 class="Evp\Bundle\BankAccountBundle\Tests\Service\AccountingMigrationFeatureFlagStorageMock"/>

        <service id="evp_blacklist.multi_index_listener_elasticsearch6"
                 class="Evp\Bundle\BlacklistBundle\Tests\Service\MultiIndexListenerMock">
        </service>

        <service id="evp_bank_account.account_lock.context" class="Evp\Bundle\BankAccountBundle\Service\AccountLock\LockExecutorProxy">
            <argument type="service" id="evp_bank_account.account_lock.strategy.repository"/>
        </service>
        <service id="evp_bundle_vmi_account_report.service.vmi_ties_client"
                 class="Evp\Bundle\VmiAccountReportBundle\Service\VmiTiesClientMock">
            <argument type="service" id="evp_vmi_account_report.helper.document_identification_generator"/>
        </service>
        <service id="evp_bundle_vmi_account_report.service.payload_handler"
                 class="Evp\Bundle\VmiAccountReportBundle\Service\PayloadHandlerMock">
        </service>

        <service id="evp_debt.user_notification_client.client"
                 class="Evp\Tests\Mock\UserNotificationClientMock">
        </service>

        <service id="evp_persistable_event.service.lock_transfer_id"
                 class="Evp\Bundle\PersistableEventBundle\Tests\Mock\MockPersistableEventTransferLocker"/>

        <service id="paysera_lock.lock_store"
                 class="Symfony\Component\Lock\Store\FlockStore" />

        <service id="paysera.component.doctrine.slave_entity_manager_factory"
                 class="Paysera\Component\Doctrine\EntityManagerFactory">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="doctrine.dbal.default_connection"/>
        </service>

        <service id="evp_bank_transfer.service.transfer_partners_isolation_feature_flag"
                 class="Evp\Bundle\BankTransferBundle\Tests\Fixtures\TransferPartnersIsolationFeatureFlagMock"/>

        <service id="evp_rabbit_mq_extension.producer.lb" class="Evp\Tests\Integration\RabbitMq\Producer\ExtendedProducer">
            <factory service="evp_rabbit_mq_extension.producer_registry" method="getProducer"/>
            <argument type="string">lb_xml_message</argument>
        </service>
    </services>
</container>
