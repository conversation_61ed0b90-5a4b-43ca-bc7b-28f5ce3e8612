imports:
    - { resource: security.yml }
    - { resource: services.xml }
    - { resource: sonata.yml }
    - { resource: gentle_force.yml }
    - { resource: archive_extensions.yml }

parameters:
    locale: en
    available_locales: ['lt','en','ru','lv','pl','bg','et','de','ro','sq','xk','fr','ka','ar','es','uk','pl']
    application_name: 'app-evpbank'
    database_server_version: 'mariadb-10.6.13'
    sepa.currency: EUR
    sepa.information_email: <EMAIL>
    paysera_bic_main: 'EVIULT2VXXX'
    paysera_bic_additional: 'EVIULT21XXX'
    lb_paysera_bic_codes: ['%paysera_bic_main%', '%paysera_bic_additional%']
    lb_integration_bic_code: '%paysera_bic_main%'
    public_url: 'https://accounts.paysera.com'
    lb_integration_sender_bic_code: '%paysera_bic_main%'
    lb_target2_bic_code: '%paysera_bic_main%'
    influxdb.enabled: false
    audit.type: 'null'
    proxy_host: '%env(PROXY_HOST)%'
    temporary_files_directory: '%env(TEMPORARY_FILES_DIRECTORY)%'
    evp_currency_pricing.uri: '%env(CURRENCY_PRICING_URL)%'

    in_memory_users:
        mokejimai:
            password: '%env(LOCAL_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::CREATE_CLIENT
                - !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_MANAGE
        mokejimai_rest:
            password: '%env(LOCAL_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_ACCESS_CURRENCIES
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PERMISSIONS
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_AIRWALLEX
                - !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MERGES_MANAGE
                - !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MANAGE
        partner_frontend:
            password: '%env(LOCAL_CREDENTIALS_PARTNER_FRONTEND_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_ACCESS_CURRENCIES
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
        wallet_api:
            password: '%env(LOCAL_CREDENTIALS_WALLET_API_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - pb
        wallet_api_rest:
            password: '%env(LOCAL_CREDENTIALS_WALLET_API_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
        wallet_frontend:
            password: '%env(LOCAL_CREDENTIALS_WALLET_FRONTEND_PASSWORD)%'
            roles:
                - ROLE_SOAP
        surveillance_engine:
            password: '%env(LOCAL_CREDENTIALS_SURVEILLANCE_ENGINE_PASSWORD)%'
            roles:
                - ROLE_SOAP
        blacklist:
            password: '%env(LOCAL_CREDENTIALS_BLACKLIST_PASSWORD)%'
            roles:
                - ROLE_SOAP
        information:
            password: '%env(LOCAL_CREDENTIALS_INFORMATION_PASSWORD)%'
            roles:
                - ROLE_INFORMATION_STATUS_IP
                - ROLE_INFORMATION_INFO_PHPINFO
        checkout:
            password: '%env(LOCAL_CREDENTIALS_CHECKOUT_PASSWORD)%'
            scopes:
                - c:c
                - d:c:macro_payout
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
                - !php/const Paysera\Component\AppScopes::DIRECT_ACCESS_DEPOSITS_READ
            context:
                api_key: !php/const Evp\Bundle\BankTransferBundle\Entity\TransferRequestNamedApi::API_KEY_MACRO_PAYOUT
        savings:
            password: '%env(LOCAL_CREDENTIALS_SAVINGS_PASSWORD)%'
            roles:
                - ROLE_SOAP
        recurring_payments:
            password: '%env(LOCAL_CREDENTIALS_RECURRING_PAYMENTS_PASSWORD)%'
            roles:
                - ROLE_SOAP
        postbank_bg_iban:
            password: '%env(LOCAL_CREDENTIALS_POSTBANK_BG_IBAN_PASSWORD)%'
            roles:
                - ROLE_SOAP
        challenge:
            password: '%env(LOCAL_CREDENTIALS_CHALLENGE_PASSWORD)%'
            roles:
                - ROLE_SOAP
        georgia-revenue-service:
            password: '%env(LOCAL_CREDENTIALS_GEORGIA_REVENUE_SERVICE_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_MANAGE
        georgia-enforcement-bureau:
            password: '%env(LOCAL_CREDENTIALS_GEORGIA_ENFORCEMENT_BUREAU_PASSWORD)%'
            roles:
                - ROLE_SOAP
        accounting-operation-manager: # TODO: SUPPORT-104910 remove "ROLE_SOAP" if user is not using any other APIs that require this role
            password: '%env(LOCAL_CREDENTIALS_ACCOUNTING_OPERATION_MANAGER_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MANAGE
        risk-level:
            password: '%env(LOCAL_CREDENTIALS_RISK_LEVEL_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_TURNOVERS_MANAGE
                - !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_MANAGE
        open-banking-api:
            password: '%env(LOCAL_CREDENTIALS_OPEN_BANKING_API_PASSWORD)%'
            roles:
                - ROLE_SOAP
        open-banking-api-rest:
            password: '%env(LOCAL_CREDENTIALS_OPEN_BANKING_API_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
        card:
            password: '%env(LOCAL_CREDENTIALS_CARD_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MANAGE
        loans-api:
            password: '%env(LOCAL_CREDENTIALS_LOANS_API_PASSWORD)%'
            roles:
                - ROLE_SOAP
        loans-api-rest:
            password: '%env(LOCAL_CREDENTIALS_LOANS_API_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS
        spreadsheet-updater:
            password: '%env(LOCAL_CREDENTIALS_SPREADSHEET_UPDATER_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_MANAGE
        card-switch:
            password: '%env(LOCAL_CREDENTIALS_CARD_SWITCH_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MANAGE
        checkout-merchant-project:
            password: '%env(LOCAL_CREDENTIALS_CHECKOUT_MERCHANT_PROJECT_PASSWORD)%'
            roles:
                - ROLE_SOAP
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_DELAYED_COMMISSION
        transfer-surveillance-assistant:
            password: '%env(LOCAL_CREDENTIALS_TRANSFER_SURVEILLANCE_ASSISTANT_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_TRANSFER_SURVEILLANCE_MANAGE
        credit-online:
            password: '%env(LOCAL_CREDENTIALS_CREDIT_ONLINE)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_CHARGES_READ
                - !php/const Paysera\Component\AppScopes::GLOBAL_CHARGES_CREATE
        accounting_events:
            password: '%env(LOCAL_CREDENTIALS_ACCOUNTING_EVENTS_PASSWORD)%'
            scopes:
                - !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS

    app.shared_private_uploads_dir: '%env(SHARED_PRIVATE_UPLOADS_DIR)%'
    besimple.soap.client.builder.class: Evp\Bundle\AccountingBundle\Soap\Builder
    evp_rabbit_mq_extension.producer_system: gateway
    evp_blacklist.blacklist_current_configuration: sanctions_production_v1
    evp_blacklist.blacklist_configuration_name_v1: sanctions_production_v1
    evp_blacklist.blacklist_alias_name_active: sanctions_active
    evp_blacklist.blacklist_configuration_name_secondary_v1: sanctions_secondary_v1
    evp_blacklist.blacklist_alias_name_importing: sanctions_importing
    evp_blacklist.blacklist_elasticsearch_index_number_of_shards: 3
    evp_blacklist.blacklist_elasticsearch6_template_name: elasticsearch6
    paysera_transfer_surveillance.elasticsearch_shard_preference: _replica
    paysera_transfer_surveillance.transfer_aml_details_step.enabled_user_covenantee_ids:
        - 8219335
    paysera_transfer_surveillance.transfer_aml_details_step.enabled_for_transfer_in: true
    paysera_transfer_surveillance.transfer_aml_details_step.enabled_for_transfer_out: false
    paysera_transfer_surveillance.transfer_aml_details_step.enabled_for_transfer_internal: false
    evp_accounting.accounting_soap_client.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    evp_accounting.accounting_soap_client.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    rest_user_api.uri: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/rest/user/v1'
    rest_user_api.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    rest_user_api.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    rest_payment_api.uri: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/rest/payment/v1'
    rest_payment_api.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    rest_payment_api.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    rest_notification_api.uri: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/rest/notification/v1'
    rest_notification_api.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    rest_notification_api.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    rest_statistics_api.uri: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/rest/statistics/v1'
    rest_statistics_api.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    rest_statistics_api.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    evp_accounting.client.router.domain: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%'
    evp_accounting.client.auth.type: mac
    evp_accounting.client.auth.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    evp_accounting.client.auth.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    evp_bank_account.account_filter_normalizer.type_map:
        issued_card: contis
    rest_distributor_client.router.domain: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%'
    rest_distributor_client.auth.type: mac
    rest_distributor_client.auth.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    rest_distributor_client.auth.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    paysera_currency_conversion_profit_client.router.domain: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%'
    paysera_currency_conversion_profit_client.auth.type: mac
    paysera_currency_conversion_profit_client.auth.username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
    paysera_currency_conversion_profit_client.auth.password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    system_hostname: '%env(SYSTEM_HOSTNAME)%'
    evp_callback.remote_host: '%env(EVP_CALLBACK_REMOTE_HOST)%'
    evp_contis_client.login_username: '%env(CONTIS_CLIENT_LOGIN_USERNAME)%'
    evp_contis_client.login_password: '%env(CONTIS_CLIENT_LOGIN_PASSWORD)%'
    evp_contis_client.base_url: '%env(CONTIS_CLIENT_BASE_URL)%'
    evp_contis.push_notifications_ips:
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_1)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_2)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_3)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_4)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_5)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_IP_6)%'
    evp_contis.push_notifications_testing_ips:
        - '%env(CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_1)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_2)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_3)%'
        - '%env(CONTIS_PUSH_NOTIFICATIONS_TESTING_IP_4)%'
    lb.service_url.bic_by_iban: http://www.lb.lt/funkcijos/sepa.asmx/GetBICforIBAN
    lb.service_url.iban_bic_check: http://www.lb.lt/funkcijos/sepa.asmx/CheckStrictIBANBIC
    sepa_instant.lb_public_key: 'file://%kernel.root_dir%%env(SEPA_INSTANT_LB_PUBLIC_KEY)%'
    evp_accounting.accounting_soap_client.wsdl: '%kernel.root_dir%/../src/Evp/Bundle/AccountingBundle/Resources/wsdl/%env(ACCOUNTING_SOAP_CLIENT_WSDL_FILENAME)%'
    paysera_swift.paysera_bic: '%paysera_bic_main%'
    paysera_swift.correspondent_bank: NUROTRIS
    transfer_mapper.transfers_limit: 500
    transfer.revoke_interval: P5D
    scheduled_notification.enabled: true
    accounting_api_base_url: '%env(ACCOUNTING_API_BASE_URL)%'
    paysera_health_report.redis_sentinel: '%env(REDIS_SENTINEL_1)%'
    paysera_health_report.rabbitmq_host: '%env(RABBIT_MQ_HOST)%'
    paysera_health_report.rabbitmq_extra_host: '%env(RABBIT_MQ_EXTRA_HOST)%'
    airwallex_client_id: '%env(AIRWALLEX_CLIENT_ID)%'
    paysera_libra_bank.whitelist_ips:
        - '************'
    paysera_georgia_rtgs.nbg_exchange_ips:
        - '*************'
    dj_periodical_check_result_files_bucket: dj-periodical-check-result-files
    transfer_aml_document_file_bucket: transfer-aml-document-file
    transfer_aml_information_request_document_file_bucket: transfer-aml-information-request-document-file
    party_comment_attachment_bucket: party-comment-attachment
    sepa_instant_archive_bucket: sepa-instant-archive
    transfer_import_document_file_bucket: transfer-import-document-file
    ceph_api_base_url: '%env(CEPH_API_BASE_URL)%'
    ceph_api_key: '%env(CEPH_API_KEY)%'
    ceph_api_secret: '%env(CEPH_API_SECRET)%'

framework:
    translator:
        fallback: '%locale%'
        logging: '%kernel.debug%'
    secret:          '%env(SECRET)%'
    router:          { resource: "%kernel.root_dir%/config/routing.yml" }
    form:            true
    csrf_protection: true
    validation:      { enable_annotations: true }
    templating:
        engines: ['twig']
    assets:
        version: 'v20140328'
    default_locale:  '%locale%'
    trusted_hosts:   []
    session:
        handler_id: snc_redis.session.handler

# Twig Configuration
twig:
    debug:            '%kernel.debug%'
    strict_variables: '%kernel.debug%'
    form_themes:
        - 'SonataCoreBundle:Form:datepicker.html.twig'
        - 'EvpAdminBundle:form:number_range_for_filter.html.twig'

# Doctrine Configuration
doctrine:
    dbal:
        default_connection: host_connection
        connections:
            host_connection:
                server_version: '%database_server_version%'
                driver:       'pdo_mysql'
                host:         '%env(DATABASE_HOST)%'
                port:         '%env(DATABASE_PORT)%'
                dbname:       '%env(DATABASE_NAME)%'
                user:         '%env(DATABASE_USER)%'
                password:     '%env(DATABASE_PASSWORD)%'
                wrapper_class: \Evp\Component\Doctrine\Connection\TransactionAwareConnection
                charset:      UTF8
                logging:      true
            sock_connection:
                server_version: '%database_server_version%'
                driver:       'pdo_mysql'
                unix_socket:  '%env(DATABASE_SOCKET)%'
                dbname:       '%env(DATABASE_NAME)%'
                user:         '%env(DATABASE_USER)%'
                password:     '%env(DATABASE_PASSWORD)%'
                wrapper_class: \Evp\Component\Doctrine\Connection\TransactionAwareConnection
                charset:      UTF8
                logging:      true
            slave_gateway_connection:
                server_version: '%database_server_version%'
                driver:       'pdo_mysql'
                host:         '%env(DATABASE_SLAVE_GATEWAY_HOST)%'
                port:         '%env(DATABASE_SLAVE_GATEWAY_PORT)%'
                dbname:       '%env(DATABASE_SLAVE_GATEWAY_NAME)%'
                user:         '%env(DATABASE_SLAVE_GATEWAY_USER)%'
                password:     '%env(DATABASE_SLAVE_GATEWAY_PASSWORD)%'
                wrapper_class: \Evp\Component\Doctrine\Connection\TransactionAwareConnection
                charset:      UTF8
                logging:      true
        types:
            json: Sonata\Doctrine\Types\JsonType
            paysera_single_window.transaction_status_type: Paysera\Bundle\SingleWindowBundle\Doctrine\Types\TransactionStatusType

    orm:
        connection: host_connection
        auto_generate_proxy_classes: '%kernel.debug%'
        auto_mapping: true
        dql:
            string_functions:
                field: DoctrineExtensions\Query\Mysql\Field
                greatest: DoctrineExtensions\Query\Mysql\Greatest
                cast: DoctrineExtensions\Query\Mysql\Cast

            numeric_functions:
                ROUND: Evp\Component\Doctrine\Extension\Round
                IF: Evp\Component\Doctrine\Extension\IfElse
                DATE: Evp\Component\Doctrine\Extension\Date
        mappings:
            gedmo_loggable:
                type: annotation
                prefix: Gedmo\Loggable\Entity
                dir: '%kernel.root_dir%/../vendor/gedmo/doctrine-extensions/lib/Gedmo/Loggable/Entity'
                alias: Gedmo
                is_bundle: false
            SonataUserBundle: ~
            FOSUserBundle: ~

doctrine_cache:
    providers:
        validator_mapping_array_cache:
            type: array
        validator_mapping_apcu_cache:
            type: apcu
            namespace: "validator_mapping_cache_apcu_%kernel.root_dir%"
        validator_mapping_file_system_cache:
            file_system:
                extension: '.cache'
                directory: "%kernel.cache_dir%/validator/"
        validator_mapping_cache:
            chain:
                providers:
                    - validator_mapping_array_cache # required because cron servers have `apc.enable_cli = 0`
                    - validator_mapping_apcu_cache
                    - validator_mapping_file_system_cache
        token_relation_cache:
            type: predis
            namespace: 'auth-api:token_relations:'
            predis:
                client_id: 'snc_redis.default'
        sepa_participant_cache:
            type: array
        tax_beneficiary_cache:
            type: array

doctrine_migrations:
    custom_template: "%kernel.project_dir%/app/config/migrations/migration.tpl"

stof_doctrine_extensions:
    orm:
        sock_connection:
            loggable: true
            timestampable: true
        host_connection:
            loggable: true
            timestampable: true

    class:
        loggable: Evp\Bundle\ClientBundle\Listener\OverrideLoggableListener

# Swiftmailer Configuration
swiftmailer:
    transport: 'smtp'
    host:      '%env(MAILER_HOST)%'
    port:      '%env(int:MAILER_PORT)%'
    username:  ~
    password:  ~

jms_serializer:
    metadata:
        directories:
            Money:
                namespace_prefix: "Evp\\Component\\Money"
                path: '%kernel.root_dir%/../vendor/evp/money/src/serializer'

# BeSimple
be_simple_soap:
    cache:
        type:     disk
        lifetime: 86400
        limit:    5
    clients:
        - name: accounting
          wsdl: '%kernel.root_dir%/../src/Evp/Bundle/AccountingBundle/Resources/wsdl/accounting_prod.xml'
          user_agent: Mozilla/5.0 (compatible; SOAP; +http://www.mokejimai.lt/)
    services:
        account:
            namespace:     http://gateway.mokejimai.lt/api/account/
            binding:       rpc-literal
            resource:      "@EvpBankAccountBundle/Controller/SoapController.php"
            resource_type: annotation
        management:
            namespace:     http://gateway.mokejimai.lt/api/management/
            binding:       rpc-literal
            resource:      "@EvpBankApiBundle/Controller/SoapController.php"
            resource_type: annotation
        webtopay:
            namespace:     http://gateway.mokejimai.lt/api/webtopay/
            binding:       rpc-literal
            resource:      "@EvpBankApiBundle/Controller/WebToPaySoapController.php"
            resource_type: annotation

# FOS User
fos_user:
    db_driver: orm
    firewall_name: main
    user_class: Application\Sonata\UserBundle\Entity\User
    group:
        group_class: Application\Sonata\UserBundle\Entity\Group
    from_email:
        address: <EMAIL>
        sender_name: Paysera

# KnpLabs Menu
knp_menu:
    twig: true
    templating: false
    default_renderer: twig

# KnpLabs Paginator
knp_paginator:
#    templating: ~

snc_redis:
    clients:
        default:
            type: predis
            alias: default
            dsn:
                - '%env(REDIS_SENTINEL_1)%'
            options:
                connection_timeout: 1
    session:
        client: default
        prefix: 'evpbank:session:'
        ttl: 900
    class:
        client_options: \Evp\Component\Cache\Predis\Options

exercise_html_purifier:
    default:
        Cache.SerializerPath: '%env(TEMPORARY_FILES_DIRECTORY)%/htmlpurifier'
        Core.EscapeInvalidTags: true
        HTML.Doctype: 'HTML 4.01 Transitional'
        HTML.Allowed: ''

fos_rest:
    routing_loader:
        default_format: json
    view:
        formats:
            json: true
        force_redirects:
            html: true
        failed_validation: HTTP_BAD_REQUEST
        default_engine: twig

evp_web_to_pay:
    credentials:
        project_id: 19264
        sign_password: ae7ccf3b05a225121e4f9f9eccd6ece6

evp_gsms:
    credentials:
        username: <EMAIL>
        password: pad3kite
    from: Paysera

# Blacklist search
evp_blacklist:
    enabled: true

fos_elastica:
    index_templates:
        elasticsearch6:
            client: default
            template_name: "%evp_blacklist.blacklist_elasticsearch6_template_name%"
            template: "%evp_blacklist.blacklist_configuration_name_v1%_*"
            settings:
                number_of_shards: "%evp_blacklist.blacklist_elasticsearch_index_number_of_shards%"
                number_of_replicas: 1
            types:
                profile:
                    properties:
                        displayName:
                            type: "text"
                        blacklist:
                            type: "integer"
                        country:
                            type: "keyword"
                        birthday:
                            type: "keyword"
                        type:
                            type: "keyword"
                        phone:
                            type: "keyword"
                        code:
                            type: "keyword"
                        categories:
                            type: "object"
                            properties:
                                id:
                                    type: "integer"
                                name:
                                    type: "keyword"
                        alternative_names:
                            type: "object"
                            properties:
                                displayName:
                                    type: "text"
                        alternative_birthdays:
                            type: "object"
                            properties:
                                date:
                                    type: "keyword"

    clients:
        default:
            host: '%env(ELASTICSEARCH_HOST_V1)%'
            port: '%env(ELASTICSEARCH_PORT_V1)%'
            username: 'evpbank'
            password: '%env(ELASTICSEARCH_PASSWORD_V1)%'
    indexes:
        '%evp_blacklist.blacklist_configuration_name_v1%':
            use_alias: true
            index_name: '%evp_blacklist.blacklist_alias_name_active%'
            client: default
            settings:
                index:
                    analysis:
                        normalizer:
                            full_name:
                                type: custom
                                filter: [ "lowercase", "asciifolding" ]
                        analyzer:
                            full_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding" ]
                            partial:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "name_length" ]
                            partial_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "name_ngrams" ]
                            mixed_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "word_delimiter" ]
                        filter:
                            name_ngrams:
                                type: "nGram"
                                min_gram: 3
                                max_gram: 4
                            name_length:
                                type: "length"
                                min: 3
                                max: 50

            types:
                profile:
                    properties:
                        displayName:
                            type: "text"
                            fields:
                                raw: { analyzer: "full_name", type: "text", "boost": 10 }
                                fuzzy_name: { analyzer: "full_name", type: "text", "boost": 5 }
                                mixed_name: { analyzer: "mixed_name", type: "text", "boost": 5 }
                                partial_name: { analyzer: "partial_name", type: "text" }
                        blacklist: { analyzer: "full_name", type: "integer", property_path: "blacklist.id" }
                        country: { normalizer: "full_name", type: "keyword", "boost": 1 }
                        birthday: { analyzer: "full_name", type: "text" }
                        type: { normalizer: "full_name", type: "keyword" }
                        phone: { normalizer: "full_name", type: "keyword", "boost": 4 }
                        code: { normalizer: "full_name", type: "keyword", "boost": 4 }
                        categories:
                            type: "object"
                            properties:
                                id:
                                    type: integer
                                name:
                                    type: keyword
                        alternative_names:
                            type: "object"
                            properties:
                                displayName:
                                    fields:
                                        displayName: { analyzer: "full_name", type: "text", "boost": 10 }
                                        fuzzy_name: { analyzer: "full_name", type: "text", "boost": 5 }
                                        mixed_name: { analyzer: "mixed_name", type: "text", "boost": 5 }
                                        partial_name: { analyzer: "partial_name", type: "text" }
                        alternative_birthdays:
                            type: "object"
                            properties:
                                date:
                                    type: "keyword"

                    persistence:
                        driver: orm
                        model: Evp\Bundle\BlacklistBundle\Entity\Profile
                        provider: ~
                        finder: ~
                        listener:
                            insert: false
                            update: false
                            delete: false
                        elastica_to_model_transformer:
                            query_builder_method: createSearchQueryBuilder
                            ignore_missing: true

        '%evp_blacklist.blacklist_configuration_name_secondary_v1%':
            use_alias: true
            index_name: '%evp_blacklist.blacklist_alias_name_importing%'
            client: default
            settings:
                index:
                    analysis:
                        normalizer:
                            full_name:
                                type: custom
                                filter: [ "lowercase", "asciifolding" ]
                        analyzer:
                            full_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding" ]
                            partial:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "name_length" ]
                            partial_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "name_ngrams" ]
                            mixed_name:
                                type: custom
                                tokenizer: standard
                                filter: [ "lowercase", "asciifolding", "word_delimiter" ]
                        filter:
                            name_ngrams:
                                type: "nGram"
                                min_gram: 3
                                max_gram: 4
                            name_length:
                                type: "length"
                                min: 3
                                max: 50
            types:
                profile:
                    properties:
                        displayName:
                            fields:
                                displayName: { analyzer: "full_name", type: "text", "boost": 10 }
                                fuzzy_name: { analyzer: "full_name", type: "text", "boost": 5 }
                                mixed_name: { analyzer: "mixed_name", type: "text", "boost": 5 }
                                partial_name: { analyzer: "partial_name", type: "text" }
                        blacklist: { analyzer: "full_name", type: "integer", property_path: "blacklist.id" }
                        country: { normalizer: "full_name", type: "keyword", "boost": 1 }
                        birthday: { analyzer: "full_name", type: "text" }
                        type: { normalizer: "full_name", type: "keyword" }
                        phone: { normalizer: "full_name", type: "keyword", "boost": 4 }
                        code: { normalizer: "full_name", type: "keyword", "boost": 4 }
                        categories:
                            type: "object"
                            properties:
                                id:
                                    type: integer
                                name:
                                    type: keyword
                        alternative_names:
                            type: "object"
                            properties:
                                displayName:
                                    fields:
                                        displayName: { analyzer: "full_name", type: "text", "boost": 10 }
                                        fuzzy_name: { analyzer: "full_name", type: "text", "boost": 5 }
                                        mixed_name: { analyzer: "mixed_name", type: "text", "boost": 5 }
                                        partial_name: { analyzer: "partial_name", type: "text" }
                        alternative_birthdays:
                            type: "object"
                            properties:
                                date:
                                    type: "keyword"

                    persistence:
                        driver: orm
                        model: Evp\Bundle\BlacklistBundle\Entity\Profile
                        provider: ~
                        finder: ~
                        listener:
                            service: 'evp_blacklist.multi_index_listener_elasticsearch6'
                        elastica_to_model_transformer:
                            query_builder_method: createSearchQueryBuilder
                            ignore_missing: true

evp_payza:
    username: '%env(PAYZA_USERNAME)%'
    password: '%env(PAYZA_PASSWORD)%'
    test_mode: false

evp_rabbit_mq_extension:
    reconnectable: true
    producer_max_reconnect_retries: 3

old_sound_rabbit_mq:
    connections:
        default:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'evp'
            lazy:      true
            heartbeat: 180
            read_write_timeout: 360

        default_without_heartbeat:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'evp'
            lazy:      true

        records_queue:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'stomp'
            lazy: true

        lb:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'lb'
            lazy:      true
            heartbeat: 120
            read_write_timeout: 240

        lb_without_heartbeat:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'lb'
            lazy:      true

        audit:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'audit'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        high_load:
            host:      '%env(RABBIT_MQ_EXTRA_HOST)%'
            port:      '%env(RABBIT_MQ_EXTRA_PORT)%'
            user:      '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password:  '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost:     'evpbank'
            lazy: true
            heartbeat: 60
            read_write_timeout: 120

        high_load_without_heartbeat:
            host: '%env(RABBIT_MQ_EXTRA_HOST)%'
            port: '%env(RABBIT_MQ_EXTRA_PORT)%'
            user: '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password: '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost: 'evpbank'
            lazy: true

        high_load_events:
            host:      '%env(RABBIT_MQ_EXTRA_HOST)%'
            port:      '%env(RABBIT_MQ_EXTRA_PORT)%'
            user:      '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password:  '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost:     'paysera'
            lazy: true
            heartbeat: 60
            read_write_timeout: 120

        high_load_mokejimai:
            host:      '%env(RABBIT_MQ_EXTRA_HOST)%'
            port:      '%env(RABBIT_MQ_EXTRA_PORT)%'
            user:      '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password:  '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost:     'mokejimai'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        high_load_events_risk_level:
            host: '%env(RABBIT_MQ_EXTRA_HOST)%'
            port: '%env(RABBIT_MQ_EXTRA_PORT)%'
            user: '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password: '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost: 'risk-level'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        sepa_instant:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'transfer-integration'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        swift:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'transfer-integration'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        georgia_rtgs:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'lb'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        ria:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'transfer-integration'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        afex:
            host:      '%env(RABBIT_MQ_HOST)%'
            port:      '%env(RABBIT_MQ_PORT)%'
            user:      '%env(RABBIT_MQ_USERNAME)%'
            password:  '%env(RABBIT_MQ_PASSWORD)%'
            vhost:     'transfer-integration'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        postbank_bg_iban:
            host: '%env(RABBIT_MQ_HOST)%'
            port: '%env(RABBIT_MQ_PORT)%'
            user: '%env(RABBIT_MQ_USERNAME)%'
            password: '%env(RABBIT_MQ_PASSWORD)%'
            vhost: 'postbank-bg-iban'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        default_high_load:
            host: '%env(RABBIT_MQ_EXTRA_HOST)%'
            port: '%env(RABBIT_MQ_EXTRA_PORT)%'
            user: '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password: '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost: 'paysera'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        georgia_revenue_service:
            host: '%env(RABBIT_MQ_HOST)%'
            port: '%env(RABBIT_MQ_PORT)%'
            user: '%env(RABBIT_MQ_USERNAME)%'
            password: '%env(RABBIT_MQ_PASSWORD)%'
            vhost: 'georgia-revenue-service'
            lazy: true
            heartbeat: 180
            read_write_timeout: 360

        georgia_revenue_service_high_load:
            host: '%env(RABBIT_MQ_EXTRA_HOST)%'
            port: '%env(RABBIT_MQ_EXTRA_PORT)%'
            user: '%env(RABBIT_MQ_EXTRA_USERNAME)%'
            password: '%env(RABBIT_MQ_EXTRA_PASSWORD)%'
            vhost: 'georgia-revenue-service'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        georgia_enforcement_bureau:
            host: '%env(RABBIT_MQ_HOST)%'
            port: '%env(RABBIT_MQ_PORT)%'
            user: '%env(RABBIT_MQ_USERNAME)%'
            password: '%env(RABBIT_MQ_PASSWORD)%'
            vhost: 'georgia-enforcement-bureau'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

        card:
            host: '%env(RABBIT_MQ_HOST)%'
            port: '%env(RABBIT_MQ_PORT)%'
            user: '%env(RABBIT_MQ_USERNAME)%'
            password: '%env(RABBIT_MQ_PASSWORD)%'
            vhost: 'card'
            lazy: true
            heartbeat: 30
            read_write_timeout: 60

    producers:
        json_search:
            connection: default
            exchange_options: {name: 'json_search', type: topic}
        lb_xml_message:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
        audit_json_message:
            connection: audit
            exchange_options: { name: 'json_message', type: topic }
        high_load_json_job:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
        high_load_json_event:
            connection: high_load_events
            exchange_options: {name: 'json_event', type: topic}
        sepa_instant_xml_message:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
        target2_rtgs_xml_message:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
        georgia_rtgs_xml_message:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
        sepa_instant_json_job:
            connection: sepa_instant
            exchange_options: {name: 'json_job', type: topic}
        swift_json_job:
            connection: swift
            exchange_options: {name: 'json_job', type: topic}
        swift_xml_message:
            connection: swift
            exchange_options: {name: 'xml_message', type: topic}
        ria_json_job:
            connection: ria
            exchange_options: {name: 'json_job', type: topic}
        afex_json_job:
            connection: afex
            exchange_options: {name: 'json_job', type: topic}
        postbank_bg_iban_json_job:
            connection: postbank_bg_iban
            exchange_options: { name: 'json_job', type: topic }
        high_load_events_risk_level:
            connection: high_load_events_risk_level
            exchange_options: { name: 'json_job', type: topic }
        high_load_events_paysera_json_job:
            connection: default_high_load
            exchange_options: { name: 'json_job', type: topic }
        georgia_revenue_service_json_job:
            connection: georgia_revenue_service
            exchange_options: { name: 'json_job', type: topic }
        georgia_revenue_service_high_load_json_job:
            connection: georgia_revenue_service_high_load
            exchange_options: { name: 'json_job', type: topic }
        georgia_enforcement_bureau_json_job:
            connection: georgia_enforcement_bureau
            exchange_options: { name: 'json_job', type: topic }
        georgia_enforcement_bureau_json_event:
            connection: georgia_enforcement_bureau
            exchange_options: { name: 'json_event', type: topic }
        high_load_mokejimai:
            connection: high_load_mokejimai
            exchange_options: { name: 'json_job', type: topic }
        card_json_event:
            connection: card
            exchange_options: { name: 'json_event', type: topic }

    consumers:
        event_user_user:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.user.user'
                routing_keys:
                  - 'user.user.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        high_load_event_user_user:
            connection: high_load_mokejimai
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.user.user'
                routing_keys:
                  - 'user.user.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_phone:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.user.phone'
                routing_keys:
                  - 'user.phone.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_email:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.user.email'
                routing_keys:
                  - 'user.email.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_contact_information:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.user.user_contact_information'
                routing_keys:
                    - 'user.user_contact_information.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_user_inactivity:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.user.user_inactivity'
                routing_keys:
                    - 'user.user_inactivity.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_identity_document:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.user.identity_document'
                routing_keys:
                    - 'user.identity_document.document_changed'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_gateway_contis_card:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.gateway.contis_card'
                routing_keys:
                  - 'gateway.contis_card.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_mailer_email_bounce:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.gateway.json_event.mailer.email.bounce'
                routing_keys:
                  - 'mailer.email.bounce'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_risk_level_calculated:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.risk-level.risk_level'
                routing_keys:
                    - 'risk-level.risk_level.calculated'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_high_load_risk_level_calculated:
            connection: high_load_events_risk_level
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.risk-level.high_load_risk_level'
                routing_keys:
                    - 'risk-level.risk_level.calculated'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 120
        event_save_banking_history_address:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.user.json_event.user.address'
                routing_keys:
                  - 'user.user_address.changed'
            callback: evp_rabbit_mq_extension.banking_history_events_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_create_accounting_operation:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.create_accounting_operation'
                routing_keys:
                  - 'gateway.create_accounting_operation'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_ensure_account_balance:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.contis_ensure_account_balance'
                routing_keys:
                  - 'gateway.contis_ensure_account_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_top_up_ensure_account_balance:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.contis_top_up_ensure_account_balance'
                routing_keys:
                  - 'gateway.contis_top_up_ensure_account_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_client_log_invalid_client_creation:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.client_log_invalid_client_creation'
                routing_keys:
                  - 'gateway.client_log_invalid_client_creation'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_client_cache_user_information:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.client_cache_user_information'
                routing_keys:
                    - 'gateway.client_cache_user_information'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_contis_account_top_up_accounting_operations:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_contis_account_top_up_accounting_operations'
                routing_keys:
                  - 'gateway.process_contis_account_top_up_accounting_operations'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_synchronize_contis_cards:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.synchronize_contis_cards'
                routing_keys:
                  - 'gateway.synchronize_contis_cards'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_accounts_synchronize:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.contis_accounts_synchronize'
                routing_keys:
                    - 'gateway.contis_accounts_synchronize'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_update_contis_cardholders:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.update_contis_cardholders'
                routing_keys:
                    - 'gateway.update_contis_cardholders'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_contis_card_charge_discount:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_contis_card_charge_discount'
                routing_keys:
                  - 'gateway.process_contis_card_charge_discount'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_unpaid_contis_cards:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_unpaid_contis_cards'
                routing_keys:
                  - 'gateway.process_unpaid_contis_cards'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_check_for_negative_balance_in_contis:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.check_for_negative_balance_in_contis'
                routing_keys:
                    - 'gateway.check_for_negative_balance_in_contis'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_ordered_virtual_cards_response:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.issuing_api.ordered_virtual_cards_response'
                routing_keys:
                    - 'issuing_api.ordered_virtual_cards_response'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_resurrect_waiting_funds_transfer:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.resurrect_waiting_funds_transfer'
                routing_keys:
                  - 'gateway.resurrect_waiting_funds_transfer'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_transfer_client_type_ai_checker_worker_transfer_out:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.transfer_client_type_ai_checker_worker_transfer_out'
                routing_keys:
                    - 'gateway.transfer_client_type_ai_checker_worker_transfer_out'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_transfer_client_type_ai_checker_worker_transfer_in:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.transfer_client_type_ai_checker_worker_transfer_in'
                routing_keys:
                    - 'gateway.transfer_client_type_ai_checker_worker_transfer_in'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_profit_from_conversion:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.calculate_profit_from_conversion'
                routing_keys:
                  - 'gateway.calculate_profit_from_conversion'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_process_callbacks:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_callbacks'
                routing_keys:
                  - 'gateway.process_callbacks'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_persistable_event_process_persistable_event:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.persistable_event.process_persistable_event'
                routing_keys:
                  - 'persistable_event.process_persistable_event'
            callback: evp_persistable_event.consumer.persistable_event
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        # Priority queue for processing high-volume persistable events
        job_persistable_event_process_priority_persistable_event:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.persistable_event.process_priority_persistable_event'
                routing_keys:
                  - 'persistable_event.process_priority_persistable_event'
            callback: evp_persistable_event.consumer.persistable_event
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_job_process_remote_operation_request:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_remote_operation_request'
                routing_keys:
                    - 'gateway.process_remote_operation_request'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_save_swift:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.save_swift'
                routing_keys:
                  - 'gateway.save_swift'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_plais_balance_report:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.plais_balance_report'
                routing_keys:
                    - 'gateway.plais_balance_report'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_plais_balance_report_publisher:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.plais_balance_report_publisher'
                routing_keys:
                    - 'gateway.plais_balance_report_publisher'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_plais_cancel_active_arrests:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.plais_cancel_active_arrests'
                routing_keys:
                    - 'gateway.plais_cancel_active_arrests'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_import_banking_history_client:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.import_banking_history_client'
                routing_keys:
                  - 'gateway.import_banking_history_client'
            callback: evp_banking_history_integration.client_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_job_gateway_import_banking_history_client_migration:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.import_banking_history_client_migration'
                routing_keys:
                  - 'gateway.import_banking_history_client_migration'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_update_banking_history_client:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.update_banking_history_client'
                routing_keys:
                  - 'gateway.update_banking_history_client'
            callback: evp_banking_history_integration.client_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_save_banking_history_transfer:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer'
                routing_keys:
                  - 'gateway.save_banking_history_transfer'
            callback: evp_banking_history_integration.transfer_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_save_banking_history_transfer_sepa:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer_sepa'
                routing_keys:
                  - 'gateway.save_banking_history_transfer_sepa'
            callback: evp_banking_history_integration.transfer_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_save_banking_history_transfer_sepa_inst_in:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer_sepa_inst_in'
                routing_keys:
                    - 'gateway.save_banking_history_transfer_sepa_inst_in'
            callback: evp_banking_history_integration.transfer_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_save_banking_history_transfer_sepa_inst_out:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer_sepa_inst_out'
                routing_keys:
                    - 'gateway.save_banking_history_transfer_sepa_inst_out'
            callback: evp_banking_history_integration.transfer_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_save_banking_history_transfer_instant:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer_instant'
                routing_keys:
                  - 'gateway.save_banking_history_transfer_instant'
            callback: evp_banking_history_integration.transfer_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_job_save_banking_history_transfer_migration:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.save_banking_history_transfer_migration'
                routing_keys:
                  - 'gateway.save_banking_history_transfer_migration'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_job_maintenance:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.maintenance'
                routing_keys:
                    - 'gateway.maintenance'
                    - 'gateway.maintenance.blacklist'
                    - 'gateway.maintenance.create_transfer_hash'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_process_incoming_funds_notifications:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_incoming_funds_notifications'
                routing_keys:
                - 'gateway.process_incoming_funds_notifications'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_process_charge_notifications:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_charge_notifications'
                routing_keys:
                    - 'gateway.process_charge_notifications'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_notification:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_notification_processor'
                routing_keys:
                  - 'in.send_result.control'
                  - 'in.send_result.synres'
                  - 'in.send_result.authres'
            callback: evp_sepa.consumer.lb_notification
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_errors:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_errors_processor'
                routing_keys:
                  - 'in.errors'
            callback: evp_sepa.consumer.lb_errors
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_status_report:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_status_report_processor'
                routing_keys:
                  - 'in.inbox.ffpsrprt'
            callback: evp_sepa.consumer.lb_status_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_credit_transaction:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_credit_transfer_processor'
                routing_keys:
                  - 'in.inbox.ffcctrns'
            callback: evp_sepa.consumer.lb_credit_transaction
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_payment_return:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_payment_return_processor'
                routing_keys:
                  - 'in.inbox.prtrn'
            callback: evp_sepa.consumer.lb_payment_return
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_claim_value_date_correction:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_claim_value_date_correction_processor'
                routing_keys:
                    - 'in.inbox.reqmp'
            callback: evp_sepa.consumer.lb_claim_value_date_correction
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_request_for_status_update:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_request_for_status_update'
                routing_keys:
                    - 'in.inbox.ffpsrprq'
            callback: evp_sepa.consumer.lb_request_for_status_update
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_claim_non_receipt:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_claim_non_receipt_processor'
                routing_keys:
                    - 'in.inbox.cnrecp'
            callback: evp_sepa.consumer.lb_claim_non_receipt
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_cancellation_request:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_cancellation_request_processor'
                routing_keys:
                  - 'in.inbox.ffpcrqst'
            callback: evp_sepa.consumer.lb_cancellation_request
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_investigation_resolution:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_investigation_resolution_processor'
                routing_keys:
                  - 'in.inbox.roinvstg'
            callback: evp_sepa.consumer.lb_investigation_resolution
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_investigation_result:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_investigation_result_processor'
                routing_keys:
                  - 'in.inbox.resinv'
            callback: evp_sepa.consumer.lb_investigation_result
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_balance_report:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_balance_report_processor'
                routing_keys:
                - 'in.inbox.balance'
            callback: evp_sepa.consumer.lb_balance_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_debit_transaction:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_debit_transaction_processor'
                routing_keys:
                  - 'in.inbox.pha.debit'
            callback: evp_sepa.consumer.lb_debit_transaction
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_debit_credit_notification:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_debit_credit_notification_processor'
                routing_keys:
                  - 'in.inbox.btcdcntf'
            callback: evp_sepa.consumer.lb_debit_credit_notification
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_liquidity:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_liquidity_processor'
                routing_keys:
                  - 'in.inbox.iltsoinf'
            callback: evp_sepa.consumer.lb_liquidity
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_plais_arrest:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_plais_arrest_processor'
                routing_keys:
                  - 'in.inbox.kart.cardfile'
            callback: evp_plais.consumer.arrest
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_plais_arrest_payment_request:
            connection: lb_without_heartbeat
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_plais_arrest_payment_request_processor'
                routing_keys:
                  - 'in.inbox.kart.cctinit'
            callback: evp_plais.consumer.arrest_payment_request
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_plais_archived:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.lb_plais_archived_processor'
                routing_keys:
                    - 'in.inbox.kart.archived'
            callback: evp_plais.consumer.archived
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_plais_sent_file:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.lb_plais_sent_file'
                routing_keys:
                    - 'in.sent_file'
            callback: evp_plais.consumer.sent_file
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_lb_sepa_participants_list:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.lb_sepa_participants_list_processor'
                routing_keys:
                  - 'in.inbox.dictionr'
            callback: evp_sepa.consumer.lb_sepa_participants_list
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_blacklist_dow_jones_full_import:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.blacklist_dow_jones_full_import'
                routing_keys:
                    - 'gateway.blacklist_dow_jones_full_import'
            callback: evp_blacklist.consumer.dow_jones_import.full_import
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_blacklist_dow_jones_incremental_import:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.blacklist_dow_jones_incremental_import'
                routing_keys:
                    - 'gateway.blacklist_dow_jones_incremental_import'
            callback: evp_blacklist.consumer.dow_jones_import.incremental_import
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_blacklist_check:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.check_blacklist_users'
                routing_keys:
                  - 'gateway.check_blacklist_users'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_blacklist_check_user_generate_report:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.check_blacklist_user_generate_report'
                routing_keys:
                  - 'gateway.check_blacklist_user_generate_report'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_blacklist_check_user_generate_report_file:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.check_blacklist_user_generate_report_file'
                routing_keys:
                  - 'gateway.check_blacklist_user_generate_report_file'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_questionnaire_process_restrictions:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_restriction_processing'
                routing_keys:
                    - 'gateway.questionnaire_restriction_processing'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_client_activity_questionnaire:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_client_activity'
                routing_keys:
                    - 'gateway.questionnaire_client_activity'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_rejected_questionnaire_process_restrictions:
          connection: default
          exchange_options: {name: 'json_job', type: topic}
          queue_options:
            name: 'ha.json_job.gateway.rejected_questionnaire_restriction_processing'
            routing_keys:
              - 'gateway.rejected_questionnaire_restriction_processing'
          callback: evp_rabbit_mq_extension.consumed_jobs_worker
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800

        job_client_activity_questionnaire_ai_analysis_validator:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_ai_analysis_validator'
                routing_keys:
                    - !php/const Evp\Bundle\QuestionnaireAIBundle\Service\AuditValidatorProcessor::JOB_KEY
            callback: evp_bundle_questionnaire_ai.consumer.audit_validator
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_client_activity_questionnaire_ai_analysis:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_ai_analysis'
                routing_keys:
                    - !php/const Evp\Bundle\QuestionnaireAIBundle\Service\AuditMessageProcessor::JOB_KEY
            callback: evp_bundle_questionnaire_ai.consumer.audit
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_questionnaire_revocation:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_revocation'
                routing_keys:
                    - 'gateway.questionnaire_revocation'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_whitelisted:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_whitelisted'
                routing_keys:
                  - 'transfer_surveillance.transfer_inspection.whitelisted'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_aml_notification:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.notify_aml'
                routing_keys:
                  - 'transfer_surveillance.notify_aml'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_auto_accept_transfer_inspection:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.auto_accept_transfer_inspection'
                routing_keys:
                  - 'transfer_surveillance.auto_accept_transfer_inspection'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_fntt_create_report:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'paysera_fntt.create_report'
                routing_keys:
                  - 'paysera_fntt.create_report'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection'
                routing_keys:
                  - 'transfer_surveillance.transfer_inspection.new'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_internal:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_internal'
                routing_keys:
                    - 'transfer_surveillance.transfer_inspection.internal'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_audit:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_audit'
                routing_keys:
                    - 'transfer_surveillance.transfer_inspection.audit'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_handle_audit:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection.handle_audit'
                routing_keys:
                    - 'transfer_surveillance.transfer_inspection.handle_audit'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_whitelist_approve:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection.whitelist_approve'
                routing_keys:
                    - 'transfer_surveillance.transfer_inspection.whitelist_approve'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_synchronize_whitelisted_profiles:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'transfer_surveillance.synchronize_whitelisted_profiles'
                routing_keys:
                    - 'transfer_surveillance.synchronize_whitelisted_profiles'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_inst_internal:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_inst_internal'
                routing_keys:
                -  'transfer_surveillance.transfer_inspection.inst_internal'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_in:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_in'
                routing_keys:
                -  'transfer_surveillance.transfer_inspection.in'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_in_clearing:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_in_clearing'
                routing_keys:
                -  'transfer_surveillance.transfer_inspection.in_clearing'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_sepa_inst_in:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_sepa_inst_in'
                routing_keys:
                -  'transfer_surveillance.transfer_inspection.sepa_inst_in'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_sepa_inst_out:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_sepa_inst_out'
                routing_keys:
                -  'transfer_surveillance.transfer_inspection.sepa_inst_out'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_transfer_inspection_macro_micro_in:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_macro_micro_in'
                routing_keys:
                    -  'transfer_surveillance.transfer_inspection.macro_micro_in'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_hold_available_unidentified_client_balance:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.hold_available_unidentified_client_balance'
                routing_keys:
                  - 'gateway.hold_available_unidentified_client_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_sepa_instant_send_transaction_out:
            connection: sepa_instant
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.sepa_instant_send_transaction_out'
                routing_keys:
                  - 'gateway.sepa_instant_send_transaction_out'
            callback: paysera_sepa_instant.consumer.transaction_out
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_sepa_instant_archive_message:
            connection: sepa_instant
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.sepa_instant_archive_message'
                routing_keys:
                    - 'gateway.sepa_instant_archive_message'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_cancellation_request:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_cancellation_request_processor'
                routing_keys:
                  - 'in.ifpcrqst'
            callback: paysera_sepa_instant.consumer.cancellation_request
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_credit_transfer:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_credit_transfer_processor'
                routing_keys:
                  - 'in.ifcctrns'
            callback: paysera_sepa_instant.consumer.credit_transfer
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_debit_credit_notification:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_debit_credit_notification_processor'
                routing_keys:
                  - 'in.itcdcntf'
            callback: paysera_sepa_instant.consumer.debit_credit_notification
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_investigation_resolution:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_investigation_resolution_processor'
                routing_keys:
                  - 'in.iroinvst'
            callback: paysera_sepa_instant.consumer.investigation_resolution
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_participant:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_participant_processor'
                routing_keys:
                  - 'in.dictionr'
            callback: paysera_sepa_instant.consumer.participant
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_payment_return:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_payment_return_processor'
                routing_keys:
                  - 'in.iprtrn'
                  - 'in.iprtrnd'
            callback: paysera_sepa_instant.consumer.payment_return
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_status_report:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_status_report_processor'
                routing_keys:
                  - 'in.ifpsrprt'
            callback: paysera_sepa_instant.consumer.status_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_status_request:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_status_request_processor'
                routing_keys:
                  -  'in.ifpsrprq'
            callback: paysera_sepa_instant.consumer.status_request
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_liquidity:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_liquidity_processor'
                routing_keys:
                  -  'in.iltsoinf'
            callback: paysera_sepa_instant.consumer.liquidity
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_bank_to_customer_account_report:
            connection: sepa_instant
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.sepa_instant_bank_to_customer_account_report_processor'
                routing_keys:
                  -  'in.ibalpat'
            callback: paysera_sepa_instant.consumer.bank_to_customer_account_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_sepa_instant_participant_unavailability:
          connection: sepa_instant
          exchange_options: {name: 'xml_message', type: topic}
          queue_options:
            name: 'ha.gateway.sepa_instant_participant_unavailability_processor'
            routing_keys:
              -  'in.iunanotf'
          callback: paysera_sepa_instant.consumer.participant_unavailability
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800

        process_sepa_instant_liquidity_position_notification:
          connection: sepa_instant
          exchange_options: {name: 'xml_message', type: topic}
          queue_options:
            name: 'ha.gateway.sepa_instant_liquidity_position_notification_processor'
            routing_keys:
              -  'in.ilpnot'
          callback: paysera_sepa_instant.consumer.liquidity_position_notification
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800

        process_sepa_instant_liquidity_transfer_request:
          connection: sepa_instant
          exchange_options: {name: 'xml_message', type: topic}
          queue_options:
            name: 'ha.gateway.sepa_instant_liquidity_transfer_request_processor'
            routing_keys:
              -  'in.iltreq'
          callback: paysera_sepa_instant.consumer.liquidity_transfer_request
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800

        process_target2_payment_status:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_payment_status_processor'
                routing_keys:
                  - 'in.inbox.pha.finsta'
            callback: paysera_target2.consumer.payment_status
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_credit_transfer:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_credit_transfer_processor'
                routing_keys:
                  - 'in.inbox.pha.tcreadv'
            callback: paysera_target2.consumer.credit_transfer
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_connection_result:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_connection_result_processor'
                routing_keys:
                  - 'in.send_result.conres'
            callback: paysera_target2.consumer.connection_result
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_syntax_result:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_syntax_result_processor'
                routing_keys:
                    - 'in.send_result.synres'
            callback: paysera_target2.consumer.syntax_result
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_participant:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_participant_processor'
                routing_keys:
                  - 'in.inbox.dictionr.target2'
            callback: paysera_target2.consumer.participant
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_financial_document:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_financial_document_processor'
                routing_keys:
                  - 'in.inbox.pha.findocs'
            callback: paysera_target2.consumer.financial_document
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_admi_notifications:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_notification_in'
                routing_keys:
                    - 'in.target2_rtgs.notifications.admi'
            callback: paysera_target2_rtgs.consumer.notification_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_status_reports:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_status_reports'
                routing_keys:
                    - 'in.target2_rtgs.status_reports'
            callback: paysera_target2_rtgs.consumer.status_reports
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_customer_credit_transfer:
            connection: lb
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.target2_rtgs_customer_credit_transfer'
                routing_keys:
                  - 'in.target2_rtgs.credit_transfer'
            callback: paysera_target2_rtgs.consumer.customer_credit_transfer
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_payment_return_in:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_payment_return_in'
                routing_keys:
                    - 'in.target2_rtgs.prtrn_in'
            callback: paysera_target2_rtgs.consumer.payment_return_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_cancellation_request_in:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_cancellation_request_in'
                routing_keys:
                    - 'in.target2_rtgs.cancellation_in'
            callback: paysera_target2_rtgs.consumer.cancellation_request_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_resolution_of_investigation:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_resolution_of_investigation_in'
                routing_keys:
                    - 'in.target2_rtgs.resolution_of_investigation_in'
            callback: paysera_target2_rtgs.consumer.resolution_of_investigation
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_receipt_in:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_receipt_in'
                routing_keys:
                    - 'in.target2_rtgs.receipt_in'
            callback: paysera_target2_rtgs.consumer.receipt
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_debit_credit_notification:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_debit_credit_notification'
                routing_keys:
                    - 'in.target2_rtgs.debit_credit_notification'
            callback: paysera_target2_rtgs.consumer.debit_credit_notification
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_participant:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_participant'
                routing_keys:
                    - 'in.target2_rtgs.participant'
            callback: paysera_target2_rtgs.consumer.participant
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_interbank_payment_in:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_interbank_payment_in'
                routing_keys:
                    - 'in.target2_rtgs.interbank_payment_in'
            callback: paysera_target2_rtgs.consumer.interbank_payment_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_target2_rtgs_bank_to_customer_statement:
            connection: lb
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.target2_rtgs_bank_to_customer_statement'
                routing_keys:
                    - 'in.target2_rtgs.bank_to_customer_statement'
            callback: paysera_target2_rtgs.consumer.bank_to_customer_statement
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_file_reject:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_file_reject'
                routing_keys:
                    - 'in.georgia_rtgs.file_reject'
            callback: paysera_georgia_rtgs.consumer.file_reject
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_swift_status_report:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_swift_status_report'
                routing_keys:
                    - 'in.georgia_rtgs.swift_status_report'
            callback: paysera_georgia_rtgs.consumer.swift_status_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_swift_debit_credit_confirmation:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_swift_debit_credit_confirmation'
                routing_keys:
                    - 'in.georgia_rtgs.swift_debit_credit_confirmation'
            callback: paysera_georgia_rtgs.consumer.swift_debit_credit_confirmation
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_iso_status_report:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_iso_status_report'
                routing_keys:
                    - 'in.georgia_rtgs.iso_status_report'
            callback: paysera_georgia_rtgs.consumer.iso_status_report
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_swift_transfer_in:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_swift_transfer_in'
                routing_keys:
                    - 'in.georgia_rtgs.swift_transfer_in'
            callback: paysera_georgia_rtgs.consumer.swift_transfer_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_unifi_transfer_in:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_unifi_transfer_in'
                routing_keys:
                    - 'in.georgia_rtgs.unifi_transfer_in'
            callback: paysera_georgia_rtgs.consumer.unifi_transfer_in
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_georgia_rtgs_debit_credit_notification:
            connection: georgia_rtgs
            exchange_options: { name: 'xml_message', type: topic }
            queue_options:
                name: 'ha.gateway.georgia_rtgs_debit_credit_notification'
                routing_keys:
                    - 'in.georgia_rtgs.debit_credit_notification'
            callback: paysera_georgia_rtgs.consumer.debit_credit_notification
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_bank_account_statement_renewal:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.statement_renewal'
                routing_keys:
                  - 'gateway.statement_renewal'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_client_monthly_charge:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.client_monthly_charge'
                routing_keys:
                  - 'gateway.client_monthly_charge'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_libra_process_transfers:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.libra_process_transfers'
                routing_keys:
                    - 'gateway.libra_process_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_swift_send_transaction_out:
            connection: swift
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.swift_send_transaction_out'
                routing_keys:
                    - 'gateway.swift_send_transaction_out'
            callback: paysera_swift.consumer.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_swift_fin_message:
            connection: swift
            exchange_options: {name: 'xml_message', type: topic}
            queue_options:
                name: 'ha.gateway.swift_fin_message_processor'
                routing_keys:
                    - 'gateway.swift_fin_message_processor'
            callback: paysera_swift_messaging.consumer.message_processor
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        process_swift_error_message:
            connection: swift
            exchange_options: {name: 'simple_message', type: topic}
            queue_options:
                name: 'paysera_swift_error'
                routing_keys:
                    - 'gateway.swift_error_message_processor'
            callback: paysera_swift_messaging.consumer.error_message_processor
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_process_charge:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_charge'
                routing_keys:
                    - 'gateway.process_charge'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_gateway_find_charge_debts:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.find_charge_debts'
                routing_keys:
                    - 'gateway.find_charge_debts'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_ria_send_transaction_out:
            connection: ria
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.ria_send_transaction_out'
                routing_keys:
                    - 'gateway.ria_send_transaction_out'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_gateway_transfer_async_sign_request:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.transfer_sign_request'
                routing_keys:
                    - !php/const Evp\Bundle\BankTransferBundle\Service\TransferSignAsync\TransferSignRequestProcessor::JOB_KEY
            callback: evp_bank_transfer.transfer_sign_async.consumer.transfer_sign_request
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_create_statement_turnover:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.create_statement_turnover'
                routing_keys:
                    - 'gateway.create_statement_turnover'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable_with_persistent_connections
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_job_create_statement_turnover:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.create_intermediate_statement_turnover'
                routing_keys:
                    - 'gateway.create_intermediate_statement_turnover'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable_with_persistent_connections
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_partner_intermediate_statement_request:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.partner_intermediate_statement_request'
                routing_keys:
                    - 'gateway.partner_intermediate_statement_request'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_partner_accounting_operation_reprocessing:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.partner_accounting_operation_reprocessing'
                routing_keys:
                    - 'gateway.partner_accounting_operation_reprocessing'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_high_load_recreate_remote_operation_request:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.recreate_remote_operation_request'
                routing_keys:
                    - 'gateway.recreate_remote_operation_request'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_statement_money_movement_request:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_statement_money_movement_request'
                routing_keys:
                    - 'gateway.process_statement_money_movement_request'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_accounting_discrepancy_cause_request:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_accounting_discrepancy_cause_request'
                routing_keys:
                    - 'gateway.process_accounting_discrepancy_cause_request'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_accounting_discrepancy_check_user:
            connection: high_load_without_heartbeat
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.accounting_discrepancy_check_user'
                routing_keys:
                    - 'gateway.accounting_discrepancy_check_user'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_accounting_discrepancy_create_user:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.accounting_discrepancy_create_user'
                routing_keys:
                    - 'gateway.accounting_discrepancy_create_user'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 3600

        job_contis_update_account_balance:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.contis_update_account_balance'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\UpdateAccountBalanceWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_review_pending_questionnaires:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.review_pending_questionnaire'
                routing_keys:
                    - !php/const Evp\Bundle\QuestionnaireBundle\Worker\QuestionnaireReviewWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_account_activation:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.account_activation'
                routing_keys:
                    - !php/const Evp\Bundle\BankAccountBundle\Workers\AccountActivationWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_issue_iban_alias:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.issue_iban_alias'
                routing_keys:
                    - !php/const Paysera\Bundle\IbanAliasBundle\Worker\IssueIbanAliasWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_deactivate_iban_alias:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.deactivate_iban_alias'
                routing_keys:
                    - !php/const Paysera\Bundle\IbanAliasBundle\Worker\DeactivateIbanAliasWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_update_client_allowance:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.gateway.update_client_allowance'
                routing_keys:
                    - !php/const Paysera\Bundle\ClientAllowanceBundle\Worker\UpdateClientAllowanceWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable_with_persistent_connections
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_delete_client_allowance:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.gateway.delete_client_allowance'
                routing_keys:
                    - !php/const Paysera\Bundle\ClientAllowanceBundle\Worker\DeleteClientAllowanceWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_process_transfer_import:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_transfer_import'
                routing_keys:
                    - !php/const \Paysera\Bundle\TransferImportBundle\Service\TransferImportWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_afex_transfers_provisioning:
            connection: afex
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.afex_transfers_provisioning'
                routing_keys:
                    - 'gateway.afex_transfers_provisioning'
            callback: paysera_afex.consumer.transfer_provisioning
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_afex_transfers_process:
            connection: afex
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.afex_transfers_process'
                routing_keys:
                    - 'gateway.afex_transfers_process'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_afex_transfers_refund:
            connection: afex
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.afex_transfers_refund'
                routing_keys:
                    - 'gateway.afex_transfers_refund'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_process_inactive_client:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_inactive_client'
                routing_keys:
                    - 'gateway.process_inactive_client'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_process_inactivity_notification:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.process_inactivity_notification'
                routing_keys:
                    - 'gateway.process_inactivity_notification'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_debt_notification:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_debt_notification'
                routing_keys:
                    - 'gateway.process_debt_notification'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_check_legal_client_inactivity:
            connection: default
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.json_job.gateway.check_legal_client_inactivity'
                routing_keys:
                    - 'gateway.check_legal_client_inactivity'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_activate_prepared_charge:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.activate_prepared_charge'
                routing_keys:
                    - 'gateway.activate_prepared_charge'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_recreate_vmi_report_item:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.recreate_vmi_report_item'
                routing_keys:
                    - 'gateway.recreate_vmi_report_item'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_submit_vmi_report:
            connection: default_without_heartbeat
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.submit_vmi_report'
                routing_keys:
                    - 'gateway.submit_vmi_report'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_vmi_payments_paid_payment:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.vmi_payments_paid_payment'
                routing_keys:
                    - 'gateway.vmi_payments_paid_payment'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        high_load_job_generate_crs_dac2_report_data:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.generate_crs_dac2_report_data'
                routing_keys:
                    - 'gateway.generate_crs_dac2_report_data'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        high_load_job_crs_dac2_account_check:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.crs_dac2_account_check'
                routing_keys:
                    - 'gateway.crs_dac2_account_check'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        high_load_job_generate_mai55_report_data:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.generate_mai55_report_data'
                routing_keys:
                    - 'gateway.generate_mai55_report_data'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        high_load_job_send_mmr_sask_report:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.send_mmr_sask_report'
                routing_keys:
                    - 'gateway.send_mmr_sask_report'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_transfer_in_event_process:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.transfer_in_event_process'
                routing_keys:
                    - 'gateway.transfer_in_event_process'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_globus_register_transfers:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.globus_register_transfers'
                routing_keys:
                    - 'gateway.globus_register_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_globus_confirm_transfers:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.globus_confirm_transfers'
                routing_keys:
                    - 'gateway.globus_confirm_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_privatbank_register_transfers:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.privatbank_register_transfers'
                routing_keys:
                    - 'gateway.privatbank_register_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_privatbank_confirm_transfers:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.privatbank_confirmation_transfers'
                routing_keys:
                    - 'gateway.privatbank_confirmation_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_natural_beneficiary_risk_center_check:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.natural_beneficiary_risk_center_check'
                routing_keys:
                    - 'gateway.natural_beneficiary_risk_center_check'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        high_load_transfer_surveillance_maintenance:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'transfer_surveillance.transfer_inspection_maintenance'
                routing_keys:
                    - 'transfer_surveillance.transfer_inspection.new'
            callback: evp_rabbit_mq_extension.banking_history_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_process_card_monthly_charge:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_process_card_monthly_charge'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\ProcessMonthlyChargeWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_top_up_negative_balance_from_own_account:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_top_up_negative_balance_from_own_account'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\TopUpNegativeBalanceFromOwnAccountWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_toggle_active_card_status_on_balance_change:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_toggle_active_card_status_on_balance_change'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\ToggleActiveCardStatusOnBalanceChangeWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_update_card_xpay_tokens_status:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_update_card_xpay_tokens_status'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\UpdateCardXPayTokensStatusWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_contis_process_queued_card:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_process_queued_card'
                routing_keys:
                    - !php/const Evp\Bundle\ContisBundle\Worker\ProcessQueuedCardWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_accounting_operation_notify:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.accounting_operation_notify'
                routing_keys:
                    - 'accounting_operation.notify'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_ria_send_transfers:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.ria_send_transfers'
                routing_keys:
                    - 'gateway.ria_send_transfers'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_internal_account_debt_date_calculation:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.internal_account_debt_date_calculation'
                routing_keys:
                    - 'gateway.internal_account_debt_date_calculation'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        event_challenge_challenge:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.challenge.challenge'
                routing_keys:
                    - 'challenge.challenge.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_process_contis_partner_accounting_operation:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_contis_partner_accounting'
                routing_keys:
                    - 'gateway.process_contis_partner_accounting'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800


        job_airwallex_register_transfer:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.airwallex_register_transfer'
                routing_keys:
                    - 'gateway.airwallex_register_transfer'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_airwallex_handle_webhook:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.airwallex_handle_webhook'
                routing_keys:
                    - 'gateway.airwallex_handle_webhook'
            callback: paysera_airwallex.consumer.webhook_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_airwallex_create_connected_account:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.airwallex_create_connected_account'
                routing_keys:
                    - 'gateway.airwallex_create_connected_account'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_airwallex_create_global_account:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.airwallex_create_global_account'
                routing_keys:
                    - 'gateway.airwallex_create_global_account'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_airwallex_internal_transfer_register:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.airwallex_internal_transfer_register'
                routing_keys:
                    - 'gateway.airwallex_internal_transfer_register'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_abandoned_questionnaire:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_abandoned_questionnaire'
                routing_keys:
                    - 'gateway.process_abandoned_questionnaire'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_log_bank_permission_change:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.log_bank_permission_change'
                routing_keys:
                    - 'gateway.log_bank_permission_change'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_maintenance_task:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_maintenance_task'
                routing_keys:
                    - 'gateway.process_maintenance_task'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_georgia_revenue_service_debts:
            connection: georgia_revenue_service
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.georgia_revenue_service_debts'
                routing_keys:
                    - 'gateway.debts.grs_write_off_collection_amount'
                    - 'gateway.debts.manual_write_off_collection_amount'
                    - 'gateway.debts.init_client_write_off'
                    - 'gateway.debts.calculate_commission'
                    - 'gateway.debts.reset_waiting_write_off_collection'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_georgia_enforcement_bureau_write_off_collection_amount:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.debts.geb_write_off_collection_amount'
                routing_keys:
                    - 'gateway.debts.geb_write_off_collection_amount'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_blacklist_periodic_check_process_data:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.blacklist_periodic_check_process_data'
                routing_keys:
                    - 'gateway.blacklist_periodic_check_process_data'
            callback: evp_rabbit_mq_extension.periodic_check_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_lb_integration_empty_files:
            connection: lb
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.lb_integration_empty_files'
                routing_keys:
                    - !php/const Paysera\Bundle\MonitoringBundle\Worker\LbIntegrationEmptyFilesWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_blacklist_restriction:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.blacklist.restriction'
                routing_keys:
                    - 'blacklist.restriction.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_contis_process_debt_repayment:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.contis_process_debt_repayment'
                routing_keys:
                    - !php/const Paysera\Bundle\ContisDebtRepaymentBundle\Worker\ProcessDebtRepaymentWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        event_user_merge:
            connection: default
            exchange_options: {name: 'json_event', type: topic}
            queue_options:
                name: 'ha.evpbank.json_event.user.user_merge'
                routing_keys:
                    - 'user.user_merge.merged'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_turnover_calculation:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.turnover_calculation'
                routing_keys:
                    - !php/const Paysera\Bundle\TurnoverBundle\Worker\CalculateTurnoverWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
        job_check_blacklist_updated_profile:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.check_blacklist_updated_profile'
                routing_keys:
                    - 'gateway.check_blacklist_updated_profile'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_daily_client_average_balance:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.daily_client_average_balance'
                routing_keys:
                    - 'gateway.daily_client_average_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_monthly_client_average_balance:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.monthly_client_average_balance'
                routing_keys:
                    - 'gateway.monthly_client_average_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_client_monthly_charge_fee:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.client_monthly_charge_fee'
                routing_keys:
                    - 'gateway.client_monthly_charge_fee'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            idle_timeout: 120
        job_questionnaire_submitted_process:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.questionnaire_submitted_process'
                routing_keys:
                    - 'gateway.questionnaire_submitted_process'
            callback: evp_bundle_questionnaire.consumed_jobs_worker.retriable_5_times
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_process_pep_related_questionnaire:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_pep_related_questionnaire'
                routing_keys:
                    - 'gateway.process_pep_related_questionnaire'
            callback: evp_bundle_questionnaire.consumed_jobs_worker.retriable_5_times
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_translate_questionnaire_fields:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.translate_questionnaire_field'
                routing_keys:
                    - 'gateway.translate_questionnaire_field'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_translate_questionnaire_fields_high_load:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.translate_questionnaire_field'
                routing_keys:
                    - 'gateway.translate_questionnaire_field'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_process_statement_turnover_end_day_balance:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.statement_turnover_end_day_balance'
                routing_keys:
                    - 'gateway.statement_turnover_end_day_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_user_removal:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.job_user_removal'
                routing_keys:
                    - 'gateway.job_user_removal'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 600
        event_app_client_info:
            connection: default
            exchange_options: { name: 'json_event', type: topic }
            queue_options:
                name: 'ha.gateway.json_event.wallet.app_client_info'
                routing_keys:
                    - 'wallet.app_client_info.#'
            callback: evp_rabbit_mq_extension.consumed_events_dispatcher.retriable_with_persistent_connections
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 300

        job_translate_transfer_details:
          connection: default
          exchange_options: { name: 'json_job', type: topic }
          queue_options:
            name: 'ha.json_job.gateway.translate_transfer_details'
            routing_keys:
              - 'gateway.translate_transfer_details'
          callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800
        job_gateway_card_v2_ensure_account_balance:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.card_v2_ensure_account_balance'
                routing_keys:
                    - 'gateway.card_v2_ensure_account_balance'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_process_card_v2_statement:
            connection: default
            exchange_options: { name: 'simple_message', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_card_v2_statement'
                routing_keys:
                    - 'gateway.process_card_v2_statement'
            callback: paysera_card_v2.consumer.card_v2_process_statement.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_process_card_v2_async_deposit_result:
            connection: default
            exchange_options: { name: 'simple_message', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.card_v2_async_deposit_result'
                routing_keys:
                    - 'gateway.card_v2_async_deposit_result'
            callback: paysera_card_v2.consumer.card_v2_async_deposit_result.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_gateway_process_card_v2_account_closure:
            connection: default
            exchange_options: { name: 'simple_message', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_card_v2_account_closure'
                routing_keys:
                    - 'gateway.process_card_v2_account_closure'
            callback: paysera_card_v2.consumer.card_v2_process_account_closure.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800

        job_transfer_surveillance_analysis_notification:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.transfer_surveillance.analysis_notification'
                routing_keys:
                    - !php/const Paysera\Bundle\TransferSurveillanceBundle\Worker\AnalysisNotificationWorker::JOB_KEY
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 300
        job_ai_assistant_task_completion:
          connection: default
          exchange_options: { name: 'json_job', type: topic }
          queue_options:
            name: 'ha.json_job.gateway.ai_assistant_task_completion'
            routing_keys:
              - 'gateway.ai_assistant.task_completion'
          callback: evp_rabbit_mq_extension.consumed_jobs_worker
          qos_options:
            prefetch_count: 1
          graceful_max_execution:
            timeout: 1800

        job_process_isolation_notification:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.process_isolation_notification'
                routing_keys:
                    - 'gateway.process_isolation_notification'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker
            qos_options:
                prefetch_count: 1
            graceful_max_execution:
                timeout: 1800
        job_checkout_delayed_commission_charge:
            connection: default
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.json_job.gateway.checkout_delayed_commission_charge'
                routing_keys:
                    - 'gateway.checkout_delayed_commission_charge'
            callback: evp_rabbit_mq_extension.consumed_jobs_worker.retriable
            qos_options:
                prefetch_count: 1
            idle_timeout: 120
    batch_consumers:
        high_load_batch_job_save_banking_history_transfer:
            connection: high_load
            exchange_options: { name: 'json_job', type: topic }
            queue_options:
                name: 'ha.batch.json_job.gateway.save_banking_history_transfer'
                routing_keys:
                    - 'gateway.batch_save_banking_history_transfer'
            callback: evp_bundle_rabbit_mq_extension.service.retriable_batch_consumer
            qos_options:
                prefetch_count: 25
            graceful_max_execution:
                timeout: 1800
        high_load_batch_job_save_banking_history_transfer_migration:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.batch.json_job.gateway.save_banking_history_transfer_migration'
                routing_keys:
                    - 'gateway.batch_save_banking_history_transfer_migration'
            callback: evp_bundle_rabbit_mq_extension.service.retriable_batch_consumer
            qos_options:
                prefetch_count: 50
            graceful_max_execution:
                timeout: 1800
        high_load_batch_job_save_banking_history_card_transaction:
            connection: high_load
            exchange_options: {name: 'json_job', type: topic}
            queue_options:
                name: 'ha.batch.json_job.gateway.save_banking_history_card_transaction'
                routing_keys:
                    - 'gateway.batch_save_banking_history_card_transaction'
            callback: evp_bundle_rabbit_mq_extension.service.retriable_batch_consumer
            qos_options:
                prefetch_count: 1000
            graceful_max_execution:
                timeout: 1800
            idle_timeout: 120
            idle_timeout_exit_code: 0

evp_audit:
    type: '%audit.type%'
    application_name: '%application_name%'

knp_snappy:
    pdf:
        binary: '%kernel.root_dir%/../bin/wkhtmltopdf-amd64'
    process_timeout: 5

# Rest Clients
evp_rest_client:
    user:
        base_url: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%'
        auth_type: mac
        username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
        password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'
    location:
        base_url: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%'
        auth_type: mac
        username: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%'
        password: '%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%'

evp_card_gateway_rest:
    router:
        domain: '%env(CARD_GATEWAY_REST_URL)%'
    auth:
        type: mac
        username: evpbank
        password: '%env(REMOTE_CREDENTIALS_CARD_GATEWAY_PASSWORD)%'

evp_contis_client:
    use_proxy: false
    env: dev
    cache_provider: array
    master_account_number: '%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_AT_CONTIS)%'
    master_account_sort_code: '%env(CONTIS_PAYSERA_ACCOUNT_NUMBER_SORT_CODE)%'

evp_contis:
    provider: local

evp_callback:
    system_name: gateway
    test_mode: '%kernel.debug%'
    proxy_host: '%proxy_host%'

evp_application_logging:
    application_name: '%application_name%'
    sentry:
        dsn: '%env(SENTRY_DSN)%'
        log_level: '%logging.sentry_level%'
        raven_options:
            excluded_exceptions:
                - Paysera\Bundle\StatementTurnoverBundle\Exception\CurrencyAccountLockException
    graylog:
        hostname: '%env(GRAYLOG_HOSTNAME)%'
        port: '%env(GRAYLOG_PORT)%'
        log_level: '%logging.graylog_level%'
    normalization_depth: 2

evp_lb_client:
    http_client:
        proxy_host: '%proxy_host%'
    service_url:
        bic_by_iban: '%lb.service_url.bic_by_iban%'
        iban_bic_check: '%lb.service_url.iban_bic_check%'

evp_translation_proxy_client:
    endpoint: '%env(TRANSLATION_PROXY_CLIENT_ENDPOINT)%'
    projects:
        - { name: app_evpbank, type: static }
    source_locale: en
    locales: '%available_locales%'

evp_banking_history_client:
    host: '%env(BANKING_HISTORY_CLIENT_HOST)%'
    certificate: '%kernel.root_dir%/Resources/certificates/cacert.pem'
    auth:
        type: mac
        username: '%env(BANKING_HISTORY_CLIENT_AUTH_USERNAME)%'
        password: '%env(BANKING_HISTORY_CLIENT_AUTH_PASSWORD)%'
    timeout: 180

paysera_libra_bank:
    api_url: '%env(LIBRA_API_URL)%'
    username: '%env(LIBRA_USERNAME)%'
    password: '%env(LIBRA_PASSWORD)%'
    customer_key: '%env(LIBRA_CUSTOMER_KEY)%'
    customer_secret: '%env(LIBRA_CUSTOMER_SECRET)%'
    master_account: '%env(LIBRA_MASTER_ACCOUNT)%'
    payments_api_prefix: '%env(LIBRA_PAYMENTS_API_PREFIX)%'
    sub_accounts_api_prefix: '%env(LIBRA_SUB_ACCOUNTS_API_PREFIX)%'

paysera_single_window:
    root_certificate_authority: '%kernel.root_dir%/config/keys/%kernel.environment%/single_window/root-ca.cer'
    client_certificate: '%kernel.root_dir%/config/keys/%kernel.environment%/single_window/client.cer'
    private_key: '%kernel.root_dir%/config/keys/%kernel.environment%/single_window/private.key'
    wsdl: '%kernel.root_dir%/../src/Paysera/Bundle/SingleWindowBundle/Resources/wsdl/%env(SINGLE_WINDOW_SOAP_CLIENT_WSDL_FILENAME)%'

paysera_globus_bank:
    transfer_api_point_code: '%env(SINGLE_WINDOW_TRANSFER_API_POINT_CODE)%'
    transfer_api_username: '%env(SINGLE_WINDOW_TRANSFER_API_USERNAME)%'
    transfer_api_password: '%env(SINGLE_WINDOW_TRANSFER_API_PASSWORD)%'
    report_api_point_code: '%env(SINGLE_WINDOW_REPORT_API_POINT_CODE)%'
    report_api_username: '%env(SINGLE_WINDOW_REPORT_API_USERNAME)%'
    report_api_password: '%env(SINGLE_WINDOW_REPORT_API_PASSWORD)%'
    admin_api_point_code: '%env(SINGLE_WINDOW_ADMIN_API_POINT_CODE)%'
    admin_api_username: '%env(SINGLE_WINDOW_ADMIN_API_USERNAME)%'
    admin_api_password: '%env(SINGLE_WINDOW_ADMIN_API_PASSWORD)%'
    bank_sftp_username: '%env(GLOBUS_BANK_SFTP_USERNAME)%'
    bank_sftp_private_key_path: '%kernel.root_dir%/config/keys/%kernel.environment%/single_window/globus_bank_sftp/private_key.pem'
    bank_sftp_host: srv.globusbank.ua
    bank_sftp_port: 20202
    bank_sftp_root_directory: './'

paysera_ria_single_window:
    transfer_api_point_code: '%env(SINGLE_WINDOW_TRANSFER_API_POINT_CODE)%'
    transfer_api_username: '%env(SINGLE_WINDOW_TRANSFER_API_USERNAME)%'
    transfer_api_password: '%env(SINGLE_WINDOW_TRANSFER_API_PASSWORD)%'
    ftp_host: '%env(RIA_FTP_HOST)%'
    ftp_port: '%env(RIA_FTP_PORT)%'
    ftp_ssl: true
    ftp_username: '%env(RIA_FTP_USERNAME)%'
    ftp_password: '%env(RIA_FTP_PASSWORD)%'
    ftp_root_directory: './'
    ftp_report_directory: 'Reports'

paysera_privatbank:
    api_url: '%env(PRIVAT_MONEY_API_URL)%'
    api_point_id: '%env(PRIVAT_MONEY_POINT_ID)%'
    api_bank_id: '%env(PRIVAT_MONEY_BANK_ID)%'
    api_sale_type: '%env(PRIVAT_MONEY_SALE_TYPE)%'
    api_description: '%env(PRIVAT_MONEY_DESCRIPTION)%'

paysera_maintenance_task:
    namespace: Paysera\Bundle\MaintenanceBundle\Task

paysera_file_system:
    root_dir: '%app.shared_private_uploads_dir%'
    mime_types:
      transfer_aml_document_file:
          - "image/png"
          - "image/jpeg"
          - "application/pdf"
          - "application/msword"
          - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          - "application/x-www-form-urlencoded"
          - "application/zip"
      transfer_aml_information_request_document_file:
          - "image/png"
          - "image/jpeg"
          - "application/pdf"
          - "application/msword"
          - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          - "application/zip"
      questionnaire_real_beneficiary_risk_center_check_report:
          - "application/pdf"

paysera_jwt_authentication:
    audience: 'evpbank'
    keys:
        -
            algorithm: 'RS256'
            public_key_path: '%kernel.root_dir%/config/keys/%kernel.environment%/jwt/mokejimai/public.pem'
            issuer: 'mokejimai'
        -
            algorithm: 'RS256'
            public_key_path: '%kernel.root_dir%/config/keys/%kernel.environment%/jwt/auth_api/public.pem'
            private_key_path: '%kernel.root_dir%/config/keys/%kernel.environment%/jwt/auth_api/private.pem'
            issuer: 'auth_api'

nelmio_cors:
  defaults:
    max_age: 3600
  paths:
    '^/transfer-surveillance/rest/v1/':
      origin_regex: true
      allow_origin:
          - '%env(CORS_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_MOKEJIMAI_OFFICE_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN)%'
      allow_headers: ['*']
      allow_methods: ['GET', 'POST', 'PUT', 'DELETE']
    '^/public/':
      origin_regex: true
      allow_origin:
          - '%env(CORS_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_MOKEJIMAI_OFFICE_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN)%'
      allow_headers: ['*']
      expose_headers: ['*']
      allow_methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    '^/accounting-discrepancies/rest/v1/':
      origin_regex: true
      allow_origin:
          - '%env(CORS_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_MOKEJIMAI_OFFICE_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_DOMAIN)%'
          - '%env(CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN)%'
      allow_headers: ['*']
      allow_methods: ['GET', 'POST', 'PUT']
    '^/questionnaire/rest/v2':
        origin_regex: true
        allow_origin:
            - '%env(CORS_MOKEJIMAI_DOMAIN)%'
            - '%env(CORS_MOKEJIMAI_OFFICE_DOMAIN)%'
            - '%env(CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN)%'
        allow_headers: [ '*' ]
        allow_methods: [ 'GET', 'PUT', 'POST', 'OPTIONS' ]
    '^/ai-assistant/rest/v1/':
        origin_regex: true
        allow_origin:
            - '%env(CORS_MOKEJIMAI_DOMAIN)%'
            - '%env(CORS_MOKEJIMAI_OFFICE_DOMAIN)%'
            - '%env(CORS_IMPERSONATION_MOKEJIMAI_DOMAIN)%'
            - '%env(CORS_IMPERSONATION_MOKEJIMAI_OFFICE_DOMAIN)%'
        allow_headers: [ '*' ]
        allow_methods: [ 'GET', 'POST', 'OPTIONS' ]

influx_db:
    default_connection: statistics
    connections:
        statistics:
            host:     '%env(INFLUXDB_STATISTICS_HOST)%'
            database: '%env(INFLUXDB_STATISTICS_DATABASE)%'
            username: '%env(INFLUXDB_STATISTICS_USERNAME)%'
            password: '%env(INFLUXDB_STATISTICS_PASSWORD)%'
            timeout: 0.5
            connect_timeout: 0.5

paysera_monitoring:
    environment: dev

paysera_security:
    scopes:
        !php/const Paysera\Component\AppScopes::GLOBAL_CHARGES_CREATE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_CHARGES_READ:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_CHALLENGES_CREATE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_BANK_INFORMATION_READ:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_TRANSFER_SURVEILLANCE_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_BLACKLISTED_PROFILES_READ:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_ACCESS_CURRENCIES:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PERMISSIONS:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_PARTNERS:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::LEGAL_CLIENT_MANAGE_PARTNERS:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::NATURAL_CLIENT_MANAGE_PARTNERS:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_GEORGIAN_BALANCE_ACCOUNTS:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::CREATE_CLIENT:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_CONFIGURATION_READ:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_QUESTIONNAIRE_APPROVE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MERGES_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_CLIENT_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::LOGGED_IN: ~
        !php/const Paysera\Component\AppScopes::CONFIRMED_LOG_IN: ~
        !php/const Paysera\Component\AppScopes::CREATE_ACCOUNT: ~
        !php/const Paysera\Component\AppScopes::SIGN_TRANSFER_ASYNC:
            context_requirements:
                - sign_request_hash
        !php/const Paysera\Component\AppScopes::SIGN_TRANSFER:
            context_requirements:
                - transfer_id_list
        !php/const Paysera\Component\AppScopes::CREATE_TRANSFERS:
            context_requirements:
                - account_number
        !php/const Paysera\Component\AppScopes::MANAGE_TRANSFERS:
            context_requirements:
                - account_number
        !php/const Paysera\Component\AppScopes::GET_TRANSFER: ~
        !php/const Paysera\Component\AppScopes::GET_TRANSFER_TEMPLATES: ~
        !php/const Paysera\Component\AppScopes::DIRECT_ACCESS_DEPOSITS_CREATE:
            skip_access_checking: true
            context_requirements:
                - api_key
        !php/const Paysera\Component\AppScopes::DIRECT_ACCESS_DEPOSITS_READ:
            skip_access_checking: true
            context_requirements:
                - api_key
        !php/const Paysera\Component\AppScopes::CHECK_USER_ALLOWANCES:
            context_requirements:
                - user_id
        !php/const Paysera\Component\AppScopes::SMS_CHARGE_CREATE: ~
        !php/const Paysera\Component\AppScopes::GLOBAL_ACCOUNTING_DISCREPANCY_READ:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_MANAGE_AIRWALLEX:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_TURNOVERS_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_AI_ASSISTANT_MANAGE:
            skip_access_checking: true
        !php/const Paysera\Component\AppScopes::GLOBAL_DELAYED_COMMISSION:
            skip_access_checking: true
    scope_hierarchy:
        !php/const Paysera\Component\AppScopes::LOGGED_IN:
            - !php/const Paysera\Component\AppScopes::GLOBAL_BANK_INFORMATION_READ
        !php/const Paysera\Component\AppScopes::CONFIRMED_LOG_IN:
            - !php/const Paysera\Component\AppScopes::LOGGED_IN
        !php/const Paysera\Component\AppScopes::DEPRECATED_GLOBAL_CHARGES_CREATE:
            - !php/const Paysera\Component\AppScopes::GLOBAL_CHARGES_CREATE
        !php/const Paysera\Component\AppScopes::DEPRECATED_PUBLIC:
            - !php/const Paysera\Component\AppScopes::GLOBAL_BANK_INFORMATION_READ
        !php/const Paysera\Component\AppScopes::DEPRECATED_TRANSFER_SURVEILLANCE_MANAGE:
            - !php/const Paysera\Component\AppScopes::GLOBAL_TRANSFER_SURVEILLANCE_MANAGE
        !php/const Paysera\Component\AppScopes::DEPRECATED_BLACKLISTS_ALL_READ:
            - !php/const Paysera\Component\AppScopes::GLOBAL_BLACKLISTED_PROFILES_READ

paysera_rest:
    locales: '%available_locales%'

paysera_api:
    locales: '%available_locales%'
    pagination:
        total_count_strategy: optional
        maximum_offset: 1000
        maximum_limit: 1000
        default_limit: 100
    path_attribute_resolvers:
        Evp\Bundle\ClientBundle\Entity\Client:
            field: covenanteeId
        Evp\Bundle\BankAccountBundle\Entity\Account:
            field: number

paysera_statement_turnover:
    redis_service_id: 'snc_redis.default'

paysera_client_allowance:
    redis_service_id: 'snc_redis.default'

paysera_lock:
    ttl: 5
    redis_client: 'snc_redis.default'

paysera_normalization:
    register_normalizers:
        date_time:
            format: "U"

paysera_database_primary_key_checker:
    ignored_table_list:
        - 'bank_transfer_status_20201031'


maba_webpack:
    enabled_bundles:
        - FOSJsRoutingBundle
        - EvpAdminBundle
        - SonataCoreBundle
        - SonataAdminBundle
        - EvpClientBundle
        - EvpContisBundle
        - EvpOperatorWidgetBundle
        - EvpSepaBundle
        - ApplicationSonataUserBundle
        - PayseraSepaInstantBundle
        - PayseraTarget2Bundle
        - PayseraAirwallexBundle
        - EvpBlacklistBundle
        - PayseraCardIntegrationBundle
    twig:
        additional_directories:
            - "%kernel.root_dir%/Resources/partials"
        suppress_errors:      true              # whether files not found or twig parse errors should be ignored
          # defaults to true in dev environment
          # defaults to "ignore_unkwowns" in prod - this option ignores
          #     unknown functions etc., but fails on syntax errors
        # set to false to always fail on any twig error
    config:
        path:                 '%kernel.root_dir%/config/webpack.config.js'
        parameters:           []        # additional parameters passed to webpack config file
          # for example, set dev_server_public_path and public_path to overwrite
          # //localhost:8080/compiled/ and /compiled/
        # see inside your webpack.config.js for more info
        # set location of cached manifests. Useful for deploy, when you don't want to include your cache directory
        manifest_file_path:        '%kernel.cache_dir%/webpack_manifest.php'

    aliases:                            # allows to set aliases inside require() in your JS files
        path_in_bundle:       /Resources/assets     # this means that require('@acme_hello/a.js')
          # will include something like
          # src/Acme/Bundles/AcmeHelloBundle/Resources/assets/a.js
        # see "Aliases" for more information
        prefix:               '@'           # configure default prefix to be added to aliases.
        additional:                       # provide any other aliases, prefix is always added automatically
            webJs: '%kernel.root_dir%/../web/js'
    bin:
        webpack:
            executable: # how maba:webpack:compile executes webpack
                # should be array, for example ['/usr/bin/node', 'node_modules/webpack/bin/webpack.js']
                - node_modules/.bin/webpack
            arguments:        []        # additional parameters to pass to webpack
            # --config with configuration path is always passed
        dev_server:
            executable: # how maba:webpack:dev-server executes webpack-dev-server
                - node_modules/.bin/webpack-dev-server
            arguments:  # additional parameters to pass to webpack-dev-server; these are default ones
                - --hot
                - --history-api-fallback
                - --inline
        disable_tty: false      # disables TTY setting. Defaults to false in dev environment, true in others.
          # TTY is needed to run dashboard and/or to display colors, but does not work
        # in some environments like AWS
        working_directory: "%kernel.root_dir%/.."

    dashboard:                  # configuration for dashboard plugin - only works when TTY available
        enabled: dev_server     # `always` for both compile and dev-server, `false` to disable
        executable:
            - node_modules/.bin/webpack-dashboard

paysera_browser_api:
    domain: '%env(BROWSER_API_DOMAIN)%'
    user: '%env(BROWSER_API_USER)%'
    password: '%env(BROWSER_API_PASSWORD)%'

paysera_epay_client:
    base_url: ""
    cin: ""
    secret: ""
    base_cancel_url: ""

currencycloud:
    session_environment: '%env(CURRENCYCLOUD_SESSION_ENVIRONMENT)%'
    login_id: '%env(CURRENCYCLOUD_LOGIN_ID)%'
    api_key: '%env(CURRENCYCLOUD_API_KEY)%'

paysera_airwallex_client:
    api_url: '%env(AIRWALLEX_API_URL)%'
    api_key: '%env(AIRWALLEX_API_KEY)%'
    client_id: '%env(AIRWALLEX_CLIENT_ID)%'

evp_questionnaire_ai:
    statement_provider_strategy: elasticsearch
