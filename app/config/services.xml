<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <parameters>
        <parameter key="logging.graylog_level" type="constant">Monolog\Logger::INFO</parameter>
        <parameter key="logging.sentry_level" type="constant">Monolog\Logger::ERROR</parameter>
        <parameter key="paysera_client_id">2</parameter>
    </parameters>

    <!-- This file should be loaded only in config.yml -->

    <services>
        <!-- Might be overriden in other environments -->
        <service id="evp_vmi_payments.vmi_mgw_client" alias="evp_vmi_payments.vmi_mgw_client.prod"/>

        <!-- Rest clients -->
        <service id="evp_rest_user_api_client" class="Evp_RestApi_RestUserClient">
            <argument>%rest_user_api.uri%</argument>
            <argument>%rest_user_api.username%</argument>
            <argument>%rest_user_api.password%</argument>
        </service>
        <service id="evp_rest_statistics_api_client" class="Evp_RestApi_RestStatisticsClient">
            <argument>%rest_statistics_api.uri%</argument>
            <argument>%rest_statistics_api.username%</argument>
            <argument>%rest_statistics_api.password%</argument>
        </service>
        <service id="evp_rest_payment_api_client" class="Evp_RestApi_RestPaymentClient">
            <argument>%rest_payment_api.uri%</argument>
            <argument>%rest_payment_api.username%</argument>
            <argument>%rest_payment_api.password%</argument>
        </service>

        <service id="evp_money_normalizer" class="Evp\Component\Money\MoneyNormalizer"/>
        <service id="evp_money_array_normalizer" class="Paysera\Component\Serializer\Normalizer\ArrayNormalizer">
            <argument type="service" id="evp_money_normalizer"/>
        </service>
        <service id="evp_money_math" class="Evp\Component\Money\BcMath">
            <argument type="constant">Evp\Component\Money\Money::DEFAULT_SCALE</argument>
        </service>

        <service id="system.security.in_memory_user_provider.api"
                 class="Paysera\Bundle\SecurityBundle\Service\InMemoryScopesAwareUserProvider">
            <argument>%in_memory_users%</argument>
        </service>

        <service id="evp_component.doctrine.auto_commit"
                 class="Evp\Component\Doctrine\AutoCommit">
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>
        <service id="evp.component.doctrine.auto_commit_subscriber"
                 class="Evp\Component\Doctrine\AutoCommitSubscriber">
            <tag name="kernel.event_subscriber"/>
            <argument type="service" id="evp_component.doctrine.auto_commit"/>
            <argument type="service" id="logger"/>
        </service>

        <service id="evp.component.date.date_time_provider" class="Evp\Component\Date\DateTimeProvider"/>
        <service id="evp.component.date.date_time_immutable_provider" class="Evp\Component\Date\DateTimeImmutableProvider"/>

        <service id="validator.mapping.cache" public="false"
                 class="Symfony\Component\Validator\Mapping\Cache\DoctrineCache">
            <argument type="service" id="doctrine_cache.providers.validator_mapping_cache"/>
        </service>

        <service id="doctrine.dbal.logger" class="%doctrine.dbal.logger.class%" public="false">
            <tag name="monolog.logger" channel="doctrine" />
            <argument type="service" id="logger" on-invalid="null" />
        </service>

        <service id="evp_rabbit_mq_extension.banking_history_jobs_worker"
                parent="evp_rabbit_mq_extension.consumed_jobs_worker.retriable">
            <call method="setExceptionHandler">
                <argument type="service">
                    <service class="Evp\Bundle\BankingHistoryIntegrationBundle\Service\ExceptionHandler">
                        <argument type="service" id="logger"/>
                    </service>
                </argument>
            </call>
        </service>

        <service id="evp_rabbit_mq_extension.banking_history_events_worker"
                 parent="evp_rabbit_mq_extension.consumed_events_dispatcher.retriable">
            <call method="setExceptionHandler">
                <argument type="service">
                    <service class="Evp\Bundle\BankingHistoryIntegrationBundle\Service\ExceptionHandler">
                        <argument type="service" id="logger"/>
                    </service>
                </argument>
            </call>
        </service>

        <service id="paysera.csv_duplicate_checker"
                 class="Paysera\Component\CsvDuplicateChecker\CsvDuplicateChecker">
            <argument type="service" id="paysera.csv_reader"/>
        </service>

        <service id="paysera.csv_reader" class="Paysera\Component\CsvReader\CsvReader"/>

        <service id="paysera.csv_writer" class="Paysera\Component\CsvWriter\CsvWriter"/>

        <service id="paysera.file_archiver_enhanced" class="Paysera\Component\FileArchiver\FileArchiverEnhanced">
            <argument>%archive_extensions%</argument>
        </service>

        <service id="paysera.component.whitespace_remover.whitespace_remover"
                 class="Paysera\Component\WhitespaceRemover\WhitespaceRemover"/>

        <service id="paysera.component.doctrine.slave_entity_manager_factory"
                 class="Paysera\Component\Doctrine\EntityManagerFactory">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>
            <argument type="service" id="doctrine.dbal.slave_gateway_connection_connection"/>
        </service>

        <service id="paysera.client.user_notification_client"
                 class="Paysera\Client\UserNotificationClient\UserNotificationClient">
            <factory service="paysera.client.user_notification_client_factory" method="getUserNotificationClient" />
        </service>

        <service id="paysera.client.user_notification_client_factory"
                 class="Paysera\Client\UserNotificationClient\ClientFactory">
            <factory class="Paysera\Client\UserNotificationClient\ClientFactory" method="create" />
            <argument type="collection">
                <argument key="base_url">%env(REMOTE_CREDENTIALS_MOKEJIMAI_BASE_URL)%/user-notification/rest/v1/</argument>
                <argument type="collection" key="mac">
                    <argument key="mac_id">%env(REMOTE_CREDENTIALS_MOKEJIMAI_USERNAME)%</argument>
                    <argument key="mac_secret">%env(REMOTE_CREDENTIALS_MOKEJIMAI_PASSWORD)%</argument>
                </argument>
            </argument>
        </service>

        <service id="paysera.chat_gpt_client.4o-mini"
                 class="Paysera\Component\AIClient\ChatGptClient">
            <argument type="service">
                <service class="GuzzleHttp\Client">
                    <argument type="collection">
                        <argument key="base_uri">%env(CHAT_GPT_API_BASE_URL)%</argument>
                        <argument key="headers" type="collection">
                            <argument key="Authorization">Bearer %env(CHAT_GPT_API_KEY)%</argument>
                            <argument key="Content-Type">application/json</argument>
                        </argument>
                        <argument key="timeout">6</argument>
                    </argument>
                </service>
            </argument>
            <argument>gpt-4o-mini</argument>
            <argument>150</argument>
        </service>

        <service id="paysera.anthropic.claude_sonnet_37" class="Paysera\Component\AIClient\AnthropicClient">
            <argument type="service">
                <service class="GuzzleHttp\Client">
                    <argument type="collection">
                        <argument key="base_uri">%env(ANTHROPIC_API_BASE_URL)%</argument>
                        <argument key="headers" type="collection">
                            <argument key="x-api-key">%env(ANTHROPIC_API_KEY)%</argument>
                            <argument key="anthropic-version">2023-06-01</argument>
                            <argument key="Content-Type">application/json</argument>
                        </argument>
                        <argument key="timeout">60</argument>
                    </argument>
                </service>
            </argument>
            <argument>claude-3-7-sonnet-20250219</argument>
            <argument>4000</argument>
            <argument>0.1</argument>
        </service>

        <service id="paysera.anthropic.claude_sonnet_4" class="Paysera\Component\AIClient\AnthropicClient">
            <argument type="service">
                <service class="GuzzleHttp\Client">
                    <argument type="collection">
                        <argument key="base_uri">%env(ANTHROPIC_API_BASE_URL)%</argument>
                        <argument key="headers" type="collection">
                            <argument key="x-api-key">%env(ANTHROPIC_API_KEY)%</argument>
                            <argument key="anthropic-version">2023-06-01</argument>
                            <argument key="Content-Type">application/json</argument>
                        </argument>
                        <argument key="timeout">60</argument>
                    </argument>
                </service>
            </argument>
            <argument>claude-sonnet-4-20250514</argument>
            <argument>4000</argument>
            <argument>0.1</argument>
        </service>

        <service id="paysera.console_progress_bar_helper"
                 class="Paysera\Component\ConsoleProgressBarHelper\ConsoleProgressBarHelper">
        </service>

        <service id="paysera.context_container" class="Paysera\Component\Context\ContextContainer"/>

        <service id="paysera_health_report.database"
                 class="Paysera\Component\Health\DatabaseChecker">
            <argument type="service" id="doctrine.orm.default_entity_manager"/>

            <tag name="paysera_information.health_checker"/>
        </service>

        <service id="paysera_health_report.slave_database"
                 class="Paysera\Component\Health\SlaveDatabaseChecker">
            <argument type="service" id="doctrine.dbal.slave_gateway_connection_connection"/>

            <tag name="paysera_information.health_checker"/>
        </service>

        <service id="paysera_health_report.redis"
                 class="Paysera\Component\Health\RedisChecker">
            <argument>%paysera_health_report.redis_sentinel%</argument>
            <argument type="service" id="snc_redis.default"/>

            <tag name="paysera_information.health_checker"/>
        </service>

        <service id="paysera_health_report.rabbitmq"
                 class="Paysera\Bundle\InformationBundle\Service\SocketChecker">
            <argument>rabbitmq</argument>
            <argument>%paysera_health_report.rabbitmq_host%</argument>
            <argument>5672</argument>
            <argument>true</argument>

            <tag name="paysera_information.health_checker"/>
        </service>

        <service id="paysera_health_report.rabbitmq"
                 class="Paysera\Bundle\InformationBundle\Service\SocketChecker">
            <argument>rabbitmq_extra</argument>
            <argument>%paysera_health_report.rabbitmq_extra_host%</argument>
            <argument>5672</argument>
            <argument>false</argument>

            <tag name="paysera_information.health_checker"/>
        </service>

        <service id="evp.component.time.clock" class="Evp\Component\Time\Psr20ClockAdapter">
            <argument type="service">
                <service class="Evp\Component\Time\Clock"/>
            </argument>
        </service>

        <service id="evp.component.time.clock.tbilisi" class="Evp\Component\Time\Psr20ClockAdapter">
            <argument type="service">
                <service class="Evp\Component\Time\Clock">
                    <argument type="string">Asia/Tbilisi</argument>
                </service>
            </argument>
        </service>

        <service id="league.iso3166" class="League\ISO3166\ISO3166">
            <factory service="paysera_single_window.factory.iso3166" method="create"/>
        </service>

        <service id="paysera.console_command_logger_subscriber"
                 class="Paysera\Component\EventListener\ConsoleCommandLoggerSubscriber">
            <argument type="service" id="logger"/>
            <argument type="service" id="debug.stopwatch"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="paysera.component.user.user_id_fetcher"
                 class="Paysera\Component\User\UserIdFetcher">
            <argument type="service" id="security.token_storage"/>
        </service>

        <service id="evp.component.cache.doctrine_related_cache_factory"
                 class="Evp\Component\Cache\DoctrineRelatedCacheFactory">
        </service>

        <service id="Paysera\Component\FileManager\Normalizer\FileContentNormalizer">
            <tag name="paysera_normalization.autoconfigured_normalizer"/>
        </service>

        <service id="paysera.component.doctrine.slave_entity_repository_factory"
                 class="Paysera\Component\Doctrine\EntityRepositoryFactory">
            <argument type="service" id="paysera.component.doctrine.slave_entity_manager_factory"/>
        </service>

        <service id="paysera.component.stopwatch.stopwatch" class="Symfony\Component\Stopwatch\Stopwatch" shared="false"/>

        <service id="paysera.component.ai_client.openai_assistant_client"
                 class="Paysera\Component\AIClient\OpenAIAssistantClient">
            <tag name="paysera.ai_assistant_client" key="openai"/>
            <argument type="service">
                <service class="GuzzleHttp\Client">
                    <argument type="collection">
                        <argument key="base_uri">%env(OPENAI_ASSISTANT_API_BASE_URL)%</argument>
                        <argument key="headers" type="collection">
                            <argument key="Authorization">Bearer %env(OPENAI_ASSISTANT_API_KEY)%</argument>
                            <argument key="Content-Type">application/json</argument>
                            <argument key="OpenAI-Beta">assistants=v2</argument>
                        </argument>
                        <argument key="timeout">30</argument>
                    </argument>
                </service>
            </argument>
            <argument>15000</argument>
        </service>

        <service id="paysera.component.time.sleeper" class="Paysera\Component\Time\Sleeper"/>
        <service id="paysera.component.json_formatter.json_formatter" class="Paysera\Component\JsonFormatter\JsonFormatter"/>

        <service id="evp_rabbit_mq_extension.exception_handler.retriable"
                 class="Paysera\Component\RabbitMq\ExceptionHandler">
            <argument type="service" id="logger"/>
        </service>
    </services>
</container>
