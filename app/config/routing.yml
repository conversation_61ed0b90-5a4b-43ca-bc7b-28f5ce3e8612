# Put configuration in corresponding file:
#   this one - for api, dev, sandbox and prod envs (all envs)
#   front - for dev, sandbox and prod envs (when not needed in api)
#   prod - only for prod env
#   dev - only for dev env
#   sandbox - only for sandbox env

_besimple_soap:
    resource: "@BeSimpleSoapBundle/Resources/config/routing/webservicecontroller.xml"
    prefix:   /api

rest_bank_api.fos_rest_bundle_config:
    resource: Evp\Bundle\BankApiBundle\Controller\RestController
    prefix: /api
    type: rest

rest_bank_api.xml_config: # some routes are configured with FOSRestBundle
    resource: "@EvpBankApiBundle/Resources/config/routing.xml"
    prefix: /api

rest_plan_api.fos_rest_bundle_config:
    resource: Evp\Bundle\TransferPlanBundle\Controller\RestController
    prefix: /api
    type: rest

rest_plan_api.xml_config: # some routes are configured with FOSRestBundle
    resource: "@EvpTransferPlanBundle/Resources/config/routing.xml"
    prefix: /api

EvpBankSmsQueryBundle:
    resource: "@EvpBankSmsQueryBundle/Resources/config/routing.xml"
    prefix:   /sms-query

EvpBankAccountRestBundle:
    resource: "@EvpBankAccountRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpBankTransferRestBundle:
    resource: "@EvpBankTransferRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpBankHoldRestBundle:
    resource: "@EvpBankHoldRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpClientRestBundle:
    resource: "@EvpClientRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpBankPermissionRestBundle:
    resource: "@EvpBankPermissionRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpCardBundle:
    resource: "@EvpCardBundle/Resources/config/routing.xml"
    prefix:   /

EvpQuestionnaireBundle:
    resource: "@EvpQuestionnaireBundle/Resources/config/routing.xml"
    prefix:   /

EvpQuestionnaireAIBundle:
    resource: "@EvpQuestionnaireAIBundle/Resources/config/routing.xml"
    prefix:   /

EvpAdminBundle:
    resource: "@EvpAdminBundle/Resources/config/routing.xml"
    prefix: /

EvpContisBundle:
    resource: "@EvpContisBundle/Resources/config/routing.xml"
    prefix:   /

EvpRelatedBankAccountsBundle:
    resource: "@EvpRelatedBankAccountsBundle/Resources/config/routing.xml"
    prefix:   /

BankAccountBalanceBundle:
    resource: "@EvpBankAccountBalanceBundle/Resources/config/routing.xml"
    prefix:   /

EvpSepaBundle:
    resource: "@EvpSepaBundle/Resources/config/routing.xml"
    prefix:   /

EvpPlaisBundle:
    resource: "@EvpPlaisBundle/Resources/config/routing.xml"
    prefix:   /

EvpCurrencyRestBundle:
    resource: "@EvpCurrencyRestBundle/Resources/config/routing.xml"
    prefix:   /

EvpBlacklistBundle:
    resource: "@EvpBlacklistBundle/Resources/config/routing.xml"
    prefix:   /

PayseraTransferSurveillanceBundle:
    resource: "@PayseraTransferSurveillanceBundle/Resources/config/routing.xml"
    prefix:   /

PayseraInformationBundle:
    resource: "@PayseraInformationBundle/Resources/config/routing.xml"

PayseraTransferAmlBundle:
    resource: "@PayseraTransferAmlBundle/Resources/config/routing.xml"

#PayseraVersobankBundle:
#    resource: "@PayseraVersobankBundle/Resources/config/routing.xml"
#    prefix:   /api

evp_bank_charge_rest:
    resource: "@EvpBankChargeRestBundle/Resources/config/routing.xml"
    prefix:   /

evp_bank_refund_rest:
    resource: "@EvpBankRefundRestBundle/Resources/config/routing.xml"
    prefix:   /

paysera_sepa_instant:
    resource: "@PayseraSepaInstantBundle/Resources/config/routing.xml"
    prefix:   /

paysera_transfer_sign:
    resource: "@PayseraTransferSignBundle/Resources/config/routing.xml"
    prefix:   /

paysera_transfer_sign_with_app:
    resource: "@PayseraTransferSignWithAppBundle/Resources/config/routing.xml"
    prefix:   /

client_allowance_rest:
    resource: "@PayseraClientAllowanceRestBundle/Resources/config/routing.xml"
    prefix: /

paysera_trusted_beneficiary:
    resource: "@PayseraTrustedBeneficiaryBundle/Resources/config/routing.xml"
    prefix:   /

evp_bank_transfer_template:
    resource: "@EvpBankTransferTemplateBundle/Resources/config/routing.xml"
    prefix:   /

paysera_contis_push_notifications:
    resource: "@PayseraContisPushNotificationsBundle/Resources/config/routing.xml"
    prefix: /

PayseraFntt:
    resource: "@PayseraFnttBundle/Resources/config/routing.xml"
    prefix: /

paysera_target2:
    resource: "@PayseraTarget2Bundle/Resources/config/routing.xml"
    prefix: /

paysera_external_routing:
    resource: "@PayseraExternalRoutingBundle/Resources/config/routing.xml"
    prefix:   /
    condition: "false"

paysera_accounting_discrepancy:
    resource: "@PayseraAccountingDiscrepancyBundle/Resources/config/routing.xml"
    prefix: /

paysera_card_design:
    resource: "@PayseraCardDesignBundle/Resources/config/routing.xml"

paysera_currency_exchange:
    resource: "@PayseraCurrencyExchangeBundle/Resources/config/routing.xml"

paysera_transfer_import:
    resource: "@PayseraTransferImportBundle/Resources/config/routing.xml"
    prefix:   /

paysera_bullion:
    resource: "@PayseraBullionBundle/Resources/config/routing.xml"
    prefix: /

paysera_commission_report:
    resource: "@PayseraCommissionReportBundle/Resources/config/routing.xml"
    prefix: /

paysera_intermediate_statement:
    resource: "@PayseraIntermediateStatementBundle/Resources/config/routing.xml"
    prefix: /

paysera_transfer_aml_information:
    resource: "@PayseraTransferAmlInformationBundle/Resources/config/routing.xml"
    prefix:   /

paysera_afex:
    resource: "@PayseraAfexBundle/Resources/config/routing.xml"
    prefix:   /

evp_transfer_tax:
    resource: "@EvpTransferTaxBundle/Resources/config/routing.xml"
    prefix:   /

paysera_partner:
    resource: "@PayseraPartnerRestBundle/Resources/config/routing.xml"
    prefix: /

paysera_epay:
    resource: "@PayseraEpayBundle/Resources/config/routing.xml"
    prefix: /

paysera_file_system:
    resource: "@PayseraFileSystemBundle/Resources/config/routing.xml"
    prefix: /public

paysera_target2_rtgs:
    resource: "@PayseraTarget2RTGSBundle/Resources/config/routing.xml"
    prefix:   /

paysera_georgia_rtgs:
    resource: "@PayseraTarget2RTGSBundle/Resources/config/routing.xml"
    prefix:   /

paysera_accounting_operation:
    resource: "@PayseraAccountingOperationBundle/Resources/config/routing.xml"
    prefix: /

EvpGrsBundle:
    resource: "@EvpGrsBundle/Resources/config/routing.xml"
    prefix:   /

EvpDebtBundle:
    resource: "@EvpDebtBundle/Resources/config/routing.xml"
    prefix:   /

paysera_airwallex:
    resource: "@PayseraAirwallexBundle/Resources/config/routing.xml"
    prefix: /

paysera_georgia_balance_account:
    resource: "@GeorgiaBalanceAccountBundle/Resources/config/routing.xml"
    prefix:   /

paysera_georgia_rest:
    resource: "@PayseraGeorgiaRTGSRestBundle/Resources/config/routing.xml"
    prefix:   /

paysera_turnover_rest:
    resource: "@PayseraTurnoverRestBundle/Resources/config/routing.xml"
    prefix:   /

paysera_card_integration:
    resource: "@PayseraCardIntegrationBundle/Resources/config/routing.xml"
    prefix:   /

card_switch_rest:
    resource: "@CardSwitchBundle/Resources/config/routing.xml"
    prefix:   /

PayseraLibraBankBundle:
    resource: "@PayseraLibraBankBundle/Resources/config/routing.xml"
    prefix:   /

PayseraAIAssistantBundle:
    resource: "@PayseraAIAssistantBundle/Resources/config/routing.xml"
    prefix:   /

paysera_delayed_commission:
    resource: "@PayseraDelayedCommissionBundle/Resources/config/routing.xml"
    prefix:   /
