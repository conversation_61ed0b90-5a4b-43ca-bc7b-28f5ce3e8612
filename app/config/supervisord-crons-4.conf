[program:rabbitmq-consumer-privatbank-register-transfers]
command=php app/console rabbitmq:consumer job_privatbank_register_transfers
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-privatbank-confirm-transfers]
command=php app/console rabbitmq:consumer job_privatbank_confirm_transfers
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:privatbank-succeed-transfers-daemon]
command=php app/console paysera:privatbank:succeed-transfers --deamonize 120
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:rabbitmq-consumer-user_contact_information]
command=php app/console rabbitmq:consumer event_user_contact_information
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-georgia-enforcement-bureau-write_off_collection_amount]
command=php app/console rabbitmq:consumer job_gateway_georgia_enforcement_bureau_write_off_collection_amount
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-log-bank-permission-change]
command=php app/console rabbitmq:consumer job_log_bank_permission_change
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1


[program:rabbitmq-consumer-lb-integration-empty-files]
command=php app/console rabbitmq:consumer job_gateway_lb_integration_empty_files
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-event-user-merge]
autostart=true
autorestart=true
startsecs=10
startretries=10
command=php app/console rabbitmq:consumer event_user_merge
exitcodes=0
numprocs=1

[program:rabbitmq-consumer-questionnaire_process_restrictions]
command=php app/console rabbitmq:consumer job_questionnaire_process_restrictions
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-daily-client-average-balance]
command=php app/console rabbitmq:consumer job_daily_client_average_balance
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-monthly-client-average-balance]
command=php app/console rabbitmq:consumer job_monthly_client_average_balance
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:process-callbacks]
command=php app/console evp:process-callbacks --deamonize 1
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:finalize-deposits]
command=php app/console evp:card:finalize-deposits --deamonize 30
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-resurrect-waiting-funds-transfer]
command=php app/console rabbitmq:consumer job_gateway_resurrect_waiting_funds_transfer
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

#[program:transfer-status-publisher]
#command=php app/console evp:transfer:status-publisher --deamonize 2
#exitcodes=0
#startsecs=5
#startretries=10
#autorestart=true
#autostart=true

[program:transfer-internal-create-inspection-distributed-deamon]
command=php app/console evp:transfer:create-inspection:distributed:internal --deamonize 10
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:transfer-internal-distributed-deamon]
command=php app/console evp:transfer:process:distributed:internal --deamonize 10
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-update-contis-cardholders]
command=php app/console rabbitmq:consumer job_update_contis_cardholders
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-import-banking-history-client]
command=php app/console rabbitmq:consumer job_gateway_import_banking_history_client --memory-limit=400
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-import-banking-history-client-migration]
command=php app/console rabbitmq:consumer high_load_job_gateway_import_banking_history_client_migration --memory-limit=400
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5

[program:rabbitmq-consumer-update-banking-history-client]
command=php app/console rabbitmq:consumer job_gateway_update_banking_history_client --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=5

[program:rabbitmq-consumer-save-banking-history-transfer]
command=php app/console rabbitmq:consumer job_save_banking_history_transfer --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=35

[program:rabbitmq-consumer-save-banking-history-transfer-sepa]
command=php app/console rabbitmq:consumer job_save_banking_history_transfer_sepa --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=10

[program:rabbitmq-consumer-save-banking-history-transfer-sepa-inst-in]
command=php app/console rabbitmq:consumer job_save_banking_history_transfer_sepa_inst_in --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=20

[program:rabbitmq-consumer-save-banking-history-transfer-sepa-inst-out]
command=php app/console rabbitmq:consumer job_save_banking_history_transfer_sepa_inst_out --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=20

[program:rabbitmq-consumer-save-banking-history-transfer-instant]
command=php app/console rabbitmq:consumer job_save_banking_history_transfer_instant --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=10

[program:rabbitmq-consumer-save-banking-history-transfer-migration]
command=php app/console rabbitmq:consumer high_load_job_save_banking_history_transfer_migration --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5

[program:rabbitmq-event-risk-level-calculated]
command=php app/console rabbitmq:consumer event_risk_level_calculated --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5

[program:rabbitmq-event-high-load-risk-level-calculated]
command=php app/console rabbitmq:consumer event_high_load_risk_level_calculated --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5

[program:mobile-payments-make-bank-transfers]
command=php app/console paysera:mobile-payments:make-bank-transfers --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:mobile-payments-process-new-transfers]
command=php app/console paysera:mobile-payments:process-new-transfers --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

#[program:rabbitmq-consumer-statement-renewal]
#command=php app/console rabbitmq:consumer job_bank_account_statement_renewal --memory-limit=400
#exitcodes=0
#startsecs=5
#startretries=10
#autorestart=true
#autostart=true
#process_name=%(program_name)s_%(process_num)s
#numprocs=2

[program:rabbitmq-consumer-libra-process-transfers]
command=php app/console rabbitmq:consumer job_libra_process_transfers
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=10

[program:rabbitmq-consumer-issue-iban-alias]
command=php app/console rabbitmq:consumer job_issue_iban_alias
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-consumer-deactivate-iban-alias]
command=php app/console rabbitmq:consumer job_deactivate_iban_alias
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=5

#[program:rabbitmq-job-gateway-swift-send-transaction-out]
#command=php app/console rabbitmq:consumer job_gateway_swift_send_transaction_out
#exitcodes=0
#autorestart=true
#autostart=true
#startsecs=5
#startretries=10
#numprocs=5


[program:rabbitmq-job-process-accounting-discrepancy-cause-request]
command=php app/console rabbitmq:consumer job_process_accounting_discrepancy_cause_request
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:rabbitmq-job-accounting-discrepancy-check-user]
command=php app/console rabbitmq:consumer job_accounting_discrepancy_check_user
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=20

[program:rabbitmq-consumer-gateway-process-inactivity-notification]
command=php app/console rabbitmq:consumer job_process_inactivity_notification
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-contis-process-card-monthly-charge]
command=php app/console rabbitmq:consumer job_contis_process_card_monthly_charge
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs_start=1
numprocs=3

[program:rabbitmq-job-accounting-discrepancy-create-user]
command=php app/console rabbitmq:consumer job_accounting_discrepancy_create_user
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-client_monthly_charge_fee]
command=php app/console rabbitmq:consumer job_client_monthly_charge_fee
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-globus-register-transfers]
command=php app/console rabbitmq:consumer job_globus_register_transfers
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-globus-confirm-transfers]
command=php app/console rabbitmq:consumer job_globus_confirm_transfers
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-audit]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_audit --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-aml-notification]
command=php app/console rabbitmq:consumer job_transfer_surveillance_aml_notification --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-auto-accept-transfer-inspection]
command=php app/console rabbitmq:consumer job_transfer_surveillance_auto_accept_transfer_inspection --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-job-process-abandoned-questionnaire]
command=php app/console rabbitmq:consumer job_process_abandoned_questionnaire
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-questionnaire-submitted-process]
command=php app/console rabbitmq:consumer job_questionnaire_submitted_process
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=2
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-process-pep-related-questionnaire]
command=php app/console rabbitmq:consumer job_process_pep_related_questionnaire
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=2
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-translate-questionnaire-fields]
command=php app/console rabbitmq:consumer job_translate_questionnaire_fields
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-translate-questionnaire-fields-high-load]
command=php app/console rabbitmq:consumer job_translate_questionnaire_fields_high_load
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=10
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-process-maintenance_task]
command=php app/console rabbitmq:consumer job_process_maintenance_task
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=25

[program:rabbitmq-consumer-job-user-removal]
command=php app/console rabbitmq:consumer job_user_removal
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=2
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-translate-transfer-details]
command=php app/console rabbitmq:consumer job_translate_transfer_details
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=40
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-job-ai-assistant-task-completion]
command=php app/console rabbitmq:consumer job_ai_assistant_task_completion
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-gateway-process-isolation-notification]
command=php app/console rabbitmq:consumer job_process_isolation_notification
autostart=true
autorestart=true
exitcodes=0
startsecs=5
startretries=10
numprocs=15
process_name=%(program_name)s_%(process_num)s

#Distributed jobs
[program:rabbitmq-job-transfer-surveillance-transfer-inspection]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-out]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_out
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=4

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in-clearing]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in_clearing
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-whitelisted]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_whitelisted
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-macro-micro-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_macro_micro_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

# Audited transfers (already done)
[program:rabbitmq-job-transfer-surveillance-transfer-inspection-inst-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_inst_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-card-v2-ensure-account-balance]
command=php app/console rabbitmq:consumer job_gateway_card_v2_ensure_account_balance
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-card-v2-async-deposit-result]
command=php app/console rabbitmq:consumer job_gateway_process_card_v2_async_deposit_result
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-card-v2-account-closure]
command=php app/console rabbitmq:consumer job_gateway_process_card_v2_account_closure
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-card-v2-statement]
command=php app/console rabbitmq:consumer job_gateway_process_card_v2_statement
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:card-v2-fix-withdraw-transactions]
command=php app/console paysera:card-v2:fix-withdraw-transactions --deamonize 60
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
##Distributed jobs
