# start processes that have reached their failure count and are not restarted by supervisor. Timeout after almost 20 min.
*/10 * * * * cd $HOME; run-one timeout 1190 supervisorctl start all

# VMI
55 7,8,9,10,11,12,13,14,15,16,17,19 * * 1-5 cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-payments:start-payment --limit=5500 --quiet
0 19 * * 0,6 cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-payments:start-payment --limit=5500 --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:payments --quiet
0 7-20/2 * * 1-5 cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:payments --log --quiet
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:payment-transfer-statuses --quiet
0 14-23 * * 1-5 cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:stuck-transfers 6 --log --quiet
0 0-1 * * 2-6 cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:stuck-transfers 6 --log --quiet
# */30 7-20 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:monitor-reports --log --quiet
# 15 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:monitor-unreported-accounts --quiet
0 1 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:create-report-items-for-unreported-closed-accounts-except-failed --quiet
# 0 2 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:monitor-unreported-opened-accounts --quiet
0 0 * * 0 cd $DIR; run-one timeout 50400 php app/console evp:vmi-accounts:monitor-not-reported-accounts-after-client-code-change --quiet
*/25 * * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-report:check-status
*/25 * * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-ties:download-report --allow-delete --quiet
0 10 * * 1-5 cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-ties:download-report --message-types PMT --message-types CRS-DAC2-LT --message-types MAI55-SLIK --message-types MAI55-SKIS --message-types MAI55-SIPL --message-types FATCA-LT --allow-delete --quiet
# */30 2-5,13-23 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-report:send
# */30 6-12 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-report:send --status=repeat
# */30 2-23 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-ties:process-account-report-items
*/30 0-19 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-ties:process-account-report-items --cancellation=false
*/30 20-23 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-ties:process-account-report-items --cancellation=true
# 25,55 6-12,16-23 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=2000M app/console evp:vmi-report:auto-repeat-failed-report-items --quiet
# 30 7-20 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-report:submit-repeated-reports
10 0 * * * cd $DIR; run-one timeout 10800 php app/console evp:bank-account:check-account-partner-changes --quiet
5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:client:check-client-partner-changes --quiet
0 2 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:create-report-items-for-changed-license-partners --quiet
# 0 5 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-accounts:monitor-account-partner-changes --quiet
# 0 13-23/2 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-report:monitor-vmi-errors --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:stuck-vmi-payments paid --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:stuck-vmi-payments new --quiet
0 */1 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-payments:monitoring:stuck-vmi-payments hold --quiet
*/30 7-20 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-reports --log --quiet
*/30 7-20 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-report-items --quiet
0 13-23/2 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-report-errors --quiet
15 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-unreported-accounts --quiet
0 5 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-account-partner-changes --quiet
20 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:vmi-ties:monitor-account-owner-changes --quiet

30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:process-non-existent-beneficiary-notifications --quiet
1 0,1 1 * * cd $DIR; run-one timeout 10800 php app/console evp:transfer_plan:charge
45 3 28 * * cd $DIR; run-one timeout 10800 php app/console evp:sodra:update-insured-persons
35 * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:cancel-not-signed --quiet
10 9 * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:process-not-signed-notifications
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:fail-new-transfers
16 */3 * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_transfer:clean-up-transfer-in-events

* * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:monitor-stuck-transfers
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:tax:monitor-transfers --log --quiet

# publishes to queues balance report
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:statistics:report:balance:publish-latest-remotely

*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:client:fix-client-locale --quiet

0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:notification:process-incoming-funds

10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:notification:monitoring:notification-statuses --quiet
10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:notification:monitoring:notification-charge-statuses --quiet
10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:notification:monitoring:notification-amount --quiet
0 2 * * * cd $DIR; run-one timeout 10800 php app/console evp:notification:monitoring:unpermitted-configurations --quiet

# CONTIS
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-queued-cards
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-pending-cards --quiet
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-monthly-charge --quiet
20 4 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:expire-cards --quiet
30 0 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:synchronize-cards --quiet
0 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:cancel-pending-cards --quiet
45 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:cancel-not-activated-cards --quiet
0 10 15 * * cd $DIR; run-one timeout 10800 php app/console evp:contis:notify-for-expiring-card --quiet
0 5 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:synchronize-accounts --quiet
0 7 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:top-up-negative-balance-from-own-account --quiet
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:synchronize-cards-cancelled-at-contis --quiet

*/5 8-11 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:import-statements cardtxn_PAYSER_\%s.dat --quiet
*/30 11-23,0-8 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:import-statements cardtxn_PAYSER_\%s.dat --quiet
0 9 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-unpaid-cards --quiet
0 10 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:send-accounting-report --quiet
*/15 8-14 * * *  cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fixed-amount-top-up --quiet
0,15,30 14 * * *  cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fixed-amount-top-up --quiet
30,45 15 * * *  cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fixed-amount-top-up --quiet
*/15 16-17 * * *  cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fixed-amount-top-up --quiet
45 7 * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fixed-amount-top-up --quiet
15 7 * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fill-to-amount-top-up --quiet
15 15 * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-fill-to-amount-top-up --quiet
20 15 * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-holiday-top-up --quiet
0 16 * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:auto-holiday-top-up-check --quiet
0 09 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:auto-credit-failed-card-deposits
30 16 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:auto-credit-failed-card-deposits
*/3 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:amount-observer
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-contis-statements-without-account-id --quiet
0 10 * * * cd $DIR; run-one timeout 10800 app/console evp:contis:monitor-cards-and-accounts --quiet
0 10 * * * cd $DIR; run-one timeout 10800 app/console evp:contis:non-transaction-token-notification --quiet
0 * * * * cd $DIR; run-one timeout 10800 app/console evp:contis:create-operation-for-statement --quiet
0 6,18 * * * cd $DIR; run-one timeout 10800 app/console evp:contis:check-agreement-code-status --quiet

# adds events to rabbitMQ for each contis account to deposit funds if available
*/20 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:ensure-cards-balances --quiet

# processes accounting of contis transactions
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-accounting --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-partners-accounting --quiet
*/40 21-23,0-6 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:republish-partners-accounting --quiet
15 3 28 * * cd $DIR; run-one timeout 10800 php app/console evp:contis:update-status-partners-accounting yesterday --quiet

0,30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-debt-repayments --quiet
10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:process-contis-debt-repayment-accounting-operations --quiet

* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:monitor-our-balance
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-health-check
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-statement-import
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:contis-master-account:monitor-deposit-transfer
*/15 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-stuck-and-failed-cards --quiet
0 6 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-card-monthly-charges --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-unprocessed-contis-sca
0 6 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-unclosed-contis-accounts --quiet
0 6,12,22 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:monitor-statement-refund-import --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:expire-strong-customer-authentication --quiet

# CARD_V2
# adds events to rabbitMQ for each card (compass plus) account to deposit funds if available
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:card_v2:ensure-cards-balances --quiet

# SEPA
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:process-transfer-in-returns --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:transaction-out-processor --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:stuck-transfers-monitoring --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:notify-about-stuck-sepa-transfers --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:transfers-monitoring
* * * * * cd $DIR; run-one timeout 10800 php app/console  evp:scheduled-notification:send --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console  evp:scheduled-notification:needs-info:send --quiet
0 18 * * * cd $DIR; run-one timeout 10800 php app/console  evp:sepa:fix-rejected-transaction --quiet

* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:cancellation-request-out-processor --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:investigation-resolution-out-processor --quiet
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:claim-non-receipt-in-response-processor --quiet
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:claim-non-receipt-out-request-processor --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:monitor-unhandled-messages --quiet
0 0 * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:unhandled-messages-cleaner
 * * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:send-status-report-request-response
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:process-unhandled-messages
0 18 * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:fix-failed-to-return-transaction-in-by-reason XT85

# PLAIS
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:arrest-response-processor --quiet
13 * * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:resend-not-confirmed:arrest-responses --quiet
30 8-16/2 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:resend-not-confirmed:balance-logs --quiet
30 7-15/2 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:balance-report:send --quiet
0 6 * * 1-5 cd $DIR; run-one timeout 10800 php app/console evp:plais:balance-report:process-not-valid --limit=1500 --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:balance-reports --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:requests-statistics --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:requests-statistics --today --quiet
0 13 * * 1-5 cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:alert-no-responses --quiet
30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:not-valid-arrest-responses --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:active-arrests --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:not-confirmed-messages --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:plais:monitoring:skipped-ibans-and-documents --quiet

# DebtBundle
0 */1 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:monitor:debts_count --quiet
0 */1 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:monitor:write-off --quiet
0 */1 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:monitor:commission --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:monitor:client_partners --quiet
0 7 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:reprocess-holds --quiet
0 2 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:transfer-commission --quiet
# 0 9 * * * cd $DIR; run-one timeout 10800 php app/console paysera:debt:update-available-balance
*/5 8-15 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:debt:write-off:init

*/2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank:renew-swifts --quiet

# BlacklistBundle
# DJ full import disabled until COMP-506
0 3 11 * * cd $DIR; run-one timeout 18000 php -d memory_limit=2000M app/console evp:blacklist:dow-jones-import:full-import --force --quiet --maxRetries=200 --expectedCount=2050000
0 1 * * * cd $DIR; run-one timeout 10800 php app/console evp:blacklist:dow-jones-import:incremental-import --maxRetries=200
0 2 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=1000M app/console evp:blacklist:maintenance:whitelist:synchronize-whitelisted-profiles 29 --quiet
15 12 * * * cd $DIR; run-one timeout 10800 php app/console evp:blacklist:send-reports
#    Sanction lists periodic check
0 4 * * * cd $DIR; run-one timeout 10800 php app/console evp:blacklist:periodical-blacklist-control-data-check --profilesChunkSize=50 --quiet
0 */4 * * * cd $DIR; run-one timeout 10800 php app/console evp:blacklist:monitor-periodical-blacklist-control-data-check --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:blacklist:monitor-periodical-blacklist-control-data-check --today --quiet
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:blacklist:monitor-names-comparison-results --quiet
5 3,15 * * * cd $DIR; run-one timeout 10800 php app/console paysera:blacklist:monitor-names-comparison-answer-correctness --quiet

#Waiting funds transfers resurrect
30 * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:waiting-funds-resurrect

*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:requeue-callbacks

# Surveillance
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:process-new-notifications
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-inspections --quiet
0 7-20 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-inspections --log --quiet
0 9 * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-inspections --only-delayed --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-inspections --only-sepa-inst --log --quiet
*/30 7-20 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-missing-inspections --log --quiet
20 0 * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-not-matched-matcher-results
0 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-inspections-additional-statistics
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-aml-retrospective-review --quiet
50 23 * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-aml-retrospective-review --daily-statistics --quiet
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-names-comparison-results
5 3,15 * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-names-comparison-answer-correctness --quiet
0 2 * * * cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:resync-transfers --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:match-module:analyze --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:match-module:values:analyze-values --quiet
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:monitor-rules-availability

# Client charges
2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:client-charge:publish-monthly-charge

# SEPA INSTANT
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:cancellation-request-out:process
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:investigation-resolution-out:process
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:payment-request-out:create
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:payment-request-out:process
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitor-stuck-transfers
0 8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitor-failed-transaction-in
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitor-failed-to-return-transactions-in
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:payment-status-request-out:process
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:repeat-stuck-transaction-out
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitor-scheme-activity
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitor-special-participant-activity
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:process-unhandled-messages
0 15 * * 5 cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:process-unhandled-messages-by-type debit_credit_notification
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:monitoring-unhandled-messages
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:unhandled-messages-cleaner

19 */3 * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:process-transfer-in-post-prepare '2 days ago' '9 month ago'
*/1 * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:process-transfer-in-post-prepare 'today' 'yesterday'
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:sepa-instant:inactive-notifier-admi004

# Bank charges
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank-charge:monitor-stuck-processing
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:charge:fix-stuck-specific-account-charges

# FNTT commands
* 1-2 * * * cd $DIR; run-one timeout 10800 php app/console paysera:fntt:submit-reports --quiet
0 10 * * * cd $DIR; run-one timeout 10800 php app/console paysera:fntt:resubmit --quiet
0 7-20 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:fnnt:monitor-failed-cash-reports --log --quiet

# Target2
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2:transaction-out-processor
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2:statement-processor
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2:monitoring --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2:process-unresolved-transactions --quiet
14 4 * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2:collect-participants --quiet
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:sepa:process-cancellation-request-in-unresolved

# Target2RTGS
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:cancellation-request-out-processor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:payment-return-out-creation-processor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:payment-return-out-processor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:transaction-out-processor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:transaction-out-creation-processor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:monitor
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:process_unhandled_messages
 0 0 * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:clean_up_unhandled_messages
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:monitor-unhandled-messages --quiet
 * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:resolution_of_investigation_out_processing --quiet
 */15 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:interbank-payment-out-processor
 0 */8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:process-statement pending --quiet
 0 */8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:target2_rtgs:process-statement failed --quiet

# Persistable events monitoring
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:persistable-event:monitor-stuck-processing
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:persistable-event:monitor-pending
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:persistable-event:recover-stuck-priority-events

# Questionnaires
0 0 * * * cd $DIR; run-one timeout 10800 php app/console evp:questionnaire:cleanup-stalled-questionnaire-documents --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console evp:questionnaire:monitor-questionnaires-count
8 * * * * cd $DIR; run-one timeout 10800 php app/console evp:questionnaire:transition-to-update-needed --quiet

# Holds monitoring
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:hold:monitor-stuck-new

# Sends email notifications for Contis integration
0 10 * * 1 cd $DIR; run-one timeout 10800 php app/console evp:contis:notify-for-newly-received-funds-in-closed-accounts
0 10 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:notify-for-new-debit-transactions-in-closed-accounts
0 9 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:notify-for-duplicate-monthly-charges
0 23 * * * cd $DIR; run-one timeout 10800 php app/console evp:contis:process-refunds --quiet
0 10 * * 5 cd $DIR; run-one timeout 10800 php app/console evp:contis:notify-for-missing-refund-batch-statement --quiet

# Libra
0 8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:import-statements --periodFrom=P1D --periodTo=P1D
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:import-statements
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:process-transfers
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:check-sent-transfers-status
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:monitor-sent-transactions-without-payment-id
*/15 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:libra:reprocess-stuck-new-transactions '1 hour'
0 18 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:libra:send-pending-kyc-request

# TechVentures
0 8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:techventures:monitor-iban-requests
0 1 1-4 * * cd $DIR; run-one timeout 10800 php app/console paysera:techventures:create-monthly-charges
0 8 5 * * cd $DIR; run-one timeout 10800 php app/console paysera:techventures:monitor-iban-charges
0 8 25 * * cd $DIR; run-one timeout 10800 php app/console paysera:techventures:deactivate-unpaid-ibans

# Currency One
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:send-transfers --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:import-statements --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:process-statements --quiet
0 3 * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:save-day-balances --quiet
5 4 * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:send-day-balances --quiet
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:monitor-problems --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:resend-waiting-funds-transactions --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:resend-insufficient-funds-transactions --quiet
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:currency-one:fix-failed-transactions --quiet

# Webmoney
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:wmxi:process-webmoney-transfers

# Bank account
# 7 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank-account:populate-available-iban-list --quiet
0 * * * * cd $DIR; run-one timeout 60 php app/console evp:bank-account:monitor-new-account-incident
0 */12 * * * cd $DIR; run-one timeout 60 php app/console evp:bank-account:monitor-negative-reservation
*/2 * * * * cd $DIR; run-one timeout 180 php app/console evp:bank-account:publish-statement-money-movement-requests --quiet

# Statement turnover
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:statement-turnover:create-statement-turnover-from-registry --quiet

# Delayed Transfer Surveillance Inspections
35 4,5,6,7,23 * * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:process-delayed-inspections --quiet

# Aba codes
0 0 4 1 * cd $DIR; run-one timeout 10800 php app/console paysera:aba-codes-downloader --quiet

#Debtors report generation
# Generate data for new report
0 3 27 * * cd $DIR; run-one timeout 10800 php -d memory_limit=2G app/console evp:debtor:create-report --quiet
# Create report file
0 20 27-31 * * cd $DIR; run-one timeout 10800 php -d memory_limit=4G app/console evp:debtor:export-report > /home/<USER>/files/debtors.csv

# Check approaching database primary keys
0 0 * * 0 cd $DIR; run-one timeout 10800 php -d memory_limit=1024M app/console database-checker:check-database-approaching-primary-keys >/dev/null 2>&1

# Accounting
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp_bank_account:reprocess_operations:transfer --quiet

# Auto fix sepa refund under investigation
0 3 * * * cd $DIR; run-one timeout 10800 php -d memory_limit=1024M app/console evp:sepa:fix-refund-under-investigation-transaction-transfer --quiet

#Recurrect old queued transfer in events
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_transfer:resurrect-transfer-in-events --quiet -- "-10 minutes"

# Command to sync old and new accounting transaction and related to it tables
# */3 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:sync_accounting_transactions --quiet
# */3 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:sync_accounting_transactions Transaction --quiet
# */2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:sync_accounting_transactions AccountingEntry ********** --quiet
# */2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:sync_accounting_transactions TransferAccountingTransaction ********** --quiet
# */2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:sync_accounting_transactions AccountingTransaction --quiet

# Globus Bank
0,6,12,18,24,30,36,42,48,54 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:register-transfers
3,9,15,21,27,33,39,45,51,57 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:confirm-transfers
5,17,35,53 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus_bank:process-transaction-reports '1 day ago' 'now'
1,7,13,19,25,31,37,43,49,55 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus_bank:process-report-transactions
0 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:sync-confirmed-transfers
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:fail-transfers
*/15 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:sync-transfers
0 6 * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:import-bank-statements
10 6 * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:monitor-bank-history-statement-report-delivery-overview
10 6 * * 2-6 cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:monitor-statement-report-delivery-alert
30 6 * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:publish-bank-statements
40 7 * * 1-5 cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:send-day-balances
0 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:globus-bank:monitoring-problems

# Privatbank
0,6,12,18,24,30,36,42,48,54 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:register-transfers
3,9,15,21,27,33,39,45,51,57 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:confirm-transfers
0 8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:process-transaction-reports -- '-1 day'
15 8 * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:process-statement-reports
1,7,13,19,25,31,37,43,49,55 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:process-report-transactions
*/10 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:fail-transfers
9 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:succeed-cash-transfers
15 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:payout-refund
40 7 * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:send-day-balances
6 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:update-day-balances
0 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:privatbank:transfers-monitoring

# Partners
57 3,5,7,13,15,17 * * * cd $DIR; run-one timeout 600 php app/console paysera:partner:client-partner-change
33 */6 * * * cd $DIR; run-one timeout 600 php app/console evp:accounting:publish-new-remote-operation-requests
*/15 22-23,0-6 * * * cd $DIR; run-one timeout 600 php app/console evp:accounting:publish-failed-remote-operation-requests
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:partner:monitoring-remote-operation-issues

# Used to start inspection processing in unexpected cases, for more details see: SUPPORT-51755, SUPPORT-54841
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:reinspect-new-inspections --try-fix-all --limit=500 --interval-in-minutes=5 --quiet
*/2 * * * * cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:reinspect-new-inspections --only-sepa-inst --limit=100 --interval-in-minutes=2 --delay=5 --quiet
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:reinspect-new-inspections --tax-payments --limit=1000 --interval-in-minutes=5 --quiet
0 7-19/2 * * 1-5 cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:synchronize-suspended-transfers --quiet
0 * * * * cd $DIR; run-one timeout 10800 php app/console evp:banking-history-integration:monitor-out-of-sync-transfers --quiet

# Iban alias
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:iban-alias:monitor-pending --quiet

# Georgia Debtor Registry
*/5 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-debtor-registry:get-debtor-info
*/7 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-debtor-registry:inform-debtor-action
50 23 * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-debtor-registry:monitor-information-exchange --quiet
0 0 * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-debtor-registry:process-failed-items --quiet

# GeorgiaRTGS
# * * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:payment-return-out-processor
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:process-ready-transfers
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:send-unifi-transactions
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:send-swift-transactions
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:send-swift-interbank-transactions
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia_rtgs:monitoring
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia_rtgs:process_unhandled_messages
0 0 * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia_rtgs:clean_up_unhandled_messages
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia-rtgs:monitor-unhandled-messages --quiet
*/30 * * * * cd $DIR; run-one timeout 10800 php app/console paysera:georgia_rtgs:update_exchange --quiet

# Calculate end day clients balance
0 2 * * * cd $DIR; run-one timeout 10800 php app/console evp:bank_account:generate-end-day-account-balance 'paysera_ge' --quiet

3,33 * * * * cd $DIR; run-one timeout 600 php app/console paysera:partner:handle-debt-settlements

# Gain loss calculation
0 3 * * * cd $DIR; run-one timeout 10800 php app/console paysera:accounting-discrepancy:daily-gain-loss-generation 'paysera_ge' --quiet

# AccountingBundle
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:accounting:publish-bank-operations --quiet

# Monthly Auto Clean-up for Natural & Legal Questionnaires
15 0 10 * * cd $DIR; run-one timeout 10800 php -d memory_limit=4096M app/console evp:questionnaire:process-abandoned-questionnaire --quiet

# Revoke stuck processing transfers imports
0 */12 * * * $DIR; run-one timeout 10800 php app/console paysera:transfer-import:revoke-processing --quiet

# Daily && monthly average balance
0 1 * * * cd $DIR; run-one timeout 10800 php app/console paysera:client_average_balance:calculate-daily-client-average-balance

# Monthly charging fee for client
0 2 1 * * cd $DIR; run-one timeout 10800 php app/console paysera:client_monthly_charge_fee:publish-client-monthly-charge-fee
* * 1 * * cd $DIR; run-one timeout 10800 php app/console paysera:client_monthly_charge_fee:process-client-monthly-transfer_commission

# AML
0 0 1 * * cd $DIR; run-one timeout 10800 php app/console paysera:surveillance:report-processed-inspections-cancelled-transfers-monthly --quiet

# Restrict rejected questionnaire by Albanian partner
30 1 * * * cd $DIR; run-one timeout 10800 php app/console evp:questionnaire:restrict-rejected-questionnaires paysera_al --quiet

#Transfer sign async
* * * * * cd $DIR; run-one timeout 10800 php app/console evp:transfer:sign-request:clean

#AI services monitoring
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:monitoring:ai-services-monitoring --quiet

# CORE-3576 - Process manually waiting funds charges (remove after charges are processed)
*/5 0-5 * * * cd $DIR; run-one timeout 10800 php app/console evp:charge:repeat-waiting-funds 15469116 1000

#Questionnaire AI
0 0 * * 6 cd $DIR; run-one timeout 10800 php app/console evp:questionnaire_ai:audit:collect_clients
0 1 * * 6 cd $DIR; run-one timeout 10800 php app/console evp:questionnaire_ai:audit:qualification:process
* 3-23 * * 6 cd $DIR; run-one timeout 10800 php app/console evp:questionnaire_ai:audit:publish --limit=500
0 0 * * * cd $DIR; run-one timeout 10800 php app/console evp:questionnaire_ai:audit:clear-prompts

# TransferAmlBundle
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:transfer-aml:send-new-transfer-aml-details-request-notifications --quiet

# Isolation notification
* * * * * cd $DIR; run-one timeout 10800 php app/console paysera:client:notify-isolation-changes --quiet
