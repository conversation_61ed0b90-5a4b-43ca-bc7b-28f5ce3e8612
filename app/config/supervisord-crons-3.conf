[program:rabbitmq-consumer-create-accounting-operation]
command=php app/console rabbitmq:consumer job_create_accounting_operation
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=10

[program:refund-deamon]
command=php app/console evp:refund:process --deamonize 60
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:hold-deamon]
command=php app/console evp:hold:process --deamonize 5
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:hold-release-deamon]
command=php app/console evp:hold:release:process --deamonize 5
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:hold-high-load-release-deamon]
command=php app/console evp:hold:high_load_release:process --deamonize 5
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:sms-query-transfer-status-collect-daemon]
command=php app/console evp:sms-query:transfer:status:collect --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:sms-query-registration-status-collect]
command=php app/console evp:sms-query:registration:status:collect --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:sms-query-promo-status-collect]
command=php app/console evp:sms-query:promo:status:collect --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:report-stalled-transfers]
command=php app/console evp:transfer:report-stalled-transfers --deamonize 3600
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:process-notifications]
command=php app/console evp:notification:process-notifications --deamonize 10
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:permission-check-ended]
command=php app/console evp:permission:check-ended --deamonize 15
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-event-user-user]
command=php app/console rabbitmq:consumer event_user_user
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-high-load-event-user-user]
command=php app/console rabbitmq:consumer high_load_event_user_user
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5
process_name=%(program_name)s_%(process_num)s

[program:delete-stalled-cards]
command=php app/console evp:card:delete-stalled-cards --deamonize 30
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-mailer-email-bounce]
command=php app/console rabbitmq:consumer event_mailer_email_bounce
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:account-arrest-release]
command=php app/console evp:account-arrest:release --deamonize 60
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-event-user-user-phone]
command=php app/console rabbitmq:consumer event_user_phone
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-event-user-user-email]
command=php app/console rabbitmq:consumer event_user_email
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-event-user-user-inactivity]
command=php app/console rabbitmq:consumer event_user_user_inactivity
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-event-user-identity-document]
command=php app/console rabbitmq:consumer event_user_identity_document
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=5
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-profit-from-conversion]
command=php app/console rabbitmq:consumer job_gateway_profit_from_conversion
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-gateway-save-swift]
command=php app/console rabbitmq:consumer job_gateway_save_swift
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-gateway-plais-balance-report]
command=php app/console rabbitmq:consumer job_gateway_plais_balance_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-gateway-plais-balance-report-publisher]
command=php app/console rabbitmq:consumer job_gateway_plais_balance_report_publisher
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-gateway-plais-cancel-active-arrests]
command=php app/console rabbitmq:consumer job_gateway_plais_cancel_active_arrests
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-maintenace]
command=php app/console rabbitmq:consumer high_load_job_maintenance --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=3

[program:rabbitmq-job-paysera-fntt-create-report]
command=php app/console rabbitmq:consumer job_fntt_create_report --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:mobile-payments-check-informed-transfers]
autostart=true
autorestart=true
startsecs=5
startretries=10
command=php app/console paysera:mobile-payments:check-informed-transfers --deamonize 10
exitcodes=0

[program:rabbitmq-consumer-create-unidentified-client-hold]
command=php app/console rabbitmq:consumer job_gateway_hold_available_unidentified_client_balance --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-consumer-log-invalid-client-creation]
command=php app/console rabbitmq:consumer job_client_log_invalid_client_creation
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-client-cache-user-information]
command=php app/console rabbitmq:consumer job_client_cache_user_information
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-client-monthly-charge]
command=php app/console rabbitmq:consumer job_gateway_client_monthly_charge
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-consumer-processs-incoming-funds-notifications]
command=php app/console rabbitmq:consumer job_gateway_process_incoming_funds_notifications
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=3

[program:rabbitmq-consumer-processs-charge-notifications]
command=php app/console rabbitmq:consumer job_gateway_process_charge_notifications
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=5

[program:rabbitmq-consumer-process-bank-to-customer-account-report]
command=php app/console rabbitmq:consumer process_sepa_instant_bank_to_customer_account_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:check-transfer-challenges]
command=php app/console paysera:transfers:check-transfer-challenges
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1

[program:rabbitmq-find-charge-debts]
command=php app/console rabbitmq:consumer high_load_gateway_find_charge_debts
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:rabbitmq-consumer-account-activation]
command=php app/console rabbitmq:consumer job_account_activation
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-update-client-allowance]
command=php app/console rabbitmq:consumer job_update_client_allowance
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=10

[program:rabbitmq-consumer-delete-client-allowance]
command=php app/console rabbitmq:consumer job_delete_client_allowance
autorestart=true
autostart=true
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-review-pending-questionnaires]
command=php app/console rabbitmq:consumer job_review_pending_questionnaires
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-gateway-check-legal-client-inactivity]
command=php app/console rabbitmq:consumer job_check_legal_client_inactivity
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-consumer-gateway-afex-transfers-provisioning]
command=php app/console rabbitmq:consumer job_afex_transfers_provisioning
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10

[program:rabbitmq-consumer-gateway-afex-transfers-process]
command=php app/console rabbitmq:consumer job_afex_transfers_process
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10

[program:rabbitmq-consumer-gateway-activate-prepared-charge]
command=php app/console rabbitmq:consumer job_activate_prepared_charge
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10

[program:rabbitmq-consumer-gateway-recreate-vmi-report-item]
command=php app/console rabbitmq:consumer job_recreate_vmi_report_item
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10

[program:rabbitmq-consumer-gateway-submit-vmi-report]
command=php app/console rabbitmq:consumer job_submit_vmi_report
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10

[program:rabbitmq-consumer-gateway-vmi-payments-paid-payment]
command=php app/console rabbitmq:consumer job_gateway_vmi_payments_paid_payment
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-gateway-generate-crs-dac2-report-data]
command=php app/console rabbitmq:consumer high_load_job_generate_crs_dac2_report_data
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-gateway-crs-dac2-account-check]
command=php app/console rabbitmq:consumer high_load_job_crs_dac2_account_check
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-gateway-high-load-job-generate-mai55-report-data]
command=php app/console rabbitmq:consumer high_load_job_generate_mai55_report_data
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-gateway-send-mmr-sask-report]
command=php app/console rabbitmq:consumer high_load_job_send_mmr_sask_report
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-gateway-process-debt-notification]
command=php app/console rabbitmq:consumer job_process_debt_notification
autostart=true
autorestart=true
exitcodes=0
startsecs=5
startretries=10

[program:bank-transfer-publish-transfer-in-events]
command=php app/console evp:bank_transfer:publish-transfer-in-events --deamonize 2
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:rabbitmq-consumer-gateway-transfer-in-event-process]
command=php app/console rabbitmq:consumer job_transfer_in_event_process
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-consumer-process-statement-money-movement-request]
command=php app/console rabbitmq:consumer job_process_statement_money_movement_request
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=6

[program:rabbitmq-consumer-high-load-recreate-remote-operation-request]
command=php app/console rabbitmq:consumer job_high_load_recreate_remote_operation_request
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=10

[program:rabbitmq-consumer-natural-beneficiary-risk-center-check]
command=php app/console rabbitmq:consumer job_natural_beneficiary_risk_center_check
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-ordered-virtual-cards-response]
command=php app/console rabbitmq:consumer job_ordered_virtual_cards_response
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
exitcodes=0
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-consumer-process-remote-operation-request]
command=php app/console rabbitmq:consumer high_load_job_process_remote_operation_request
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
exitcodes=0
startsecs=5
startretries=10
numprocs=30

[program:rabbitmq-consumer-event-challenge-challenge]
command=php app/console rabbitmq:consumer event_challenge_challenge
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1
process_name=%(program_name)s_%(process_num)s

[program:rabbitmq-consumer-internal-account-debt-date-calculation]
command=php app/console rabbitmq:consumer job_internal_account_debt_date_calculation
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=6

[program:rabbitmq-consumer-process-statement-turnover-end-day-balance]
command=php app/console rabbitmq:consumer job_process_statement_turnover_end_day_balance
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1


#Distributed jobs
[program:rabbitmq-job-transfer-surveillance-transfer-inspection]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-out]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_out
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=4

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in-clearing]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in_clearing
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-whitelisted]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_whitelisted
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-macro-micro-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_macro_micro_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

# Audited transfers (already done)
[program:rabbitmq-job-transfer-surveillance-transfer-inspection-inst-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_inst_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

##Distributed jobs
