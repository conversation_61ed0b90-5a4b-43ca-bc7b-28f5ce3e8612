security:
    encoders:
        Symfony\Component\Security\Core\User\User: plaintext
        Application\Sonata\UserBundle\Entity\User:
            algorithm: sha512
            encode_as_base64: false
            iterations: 1
        Paysera\Bundle\SecurityBundle\Entity\ScopesAwareUser: plaintext

    providers:
        in_memory:
            id: system.security.in_memory_user_provider.api

        client:
            entity: { class: Evp\Bundle\ClientBundle\Entity\Client, property: id }
        fos_userbundle:
            id: fos_user.user_provider.username

        jwt_user_provider:
            id: paysera_jwt_authentication.service.jwt_user_provider

    firewalls:
        dev:
            pattern:  ^/(_(profiler|wdt)|css|images|js)/
            security: false

        soap_management:
            pattern:   ^/api/management
            http_basic:
                realm: "Soap login"
                provider: in_memory
            anonymous: false
            stateless: true

        soap_webtopay:
            pattern:   ^/api/webtopay
            http_basic:
                realm: "Soap login"
                provider: in_memory
            anonymous: false
            stateless: true

        soap_account:
            pattern:   ^/api/account
            http_basic:
                realm: "Soap login"
                provider: in_memory
            anonymous: false
            stateless: true

        rest_management:
            pattern:   ^/api/rest
            http_basic:
                realm: "Rest login"
                provider: in_memory
            anonymous: false
            stateless: true

        transfer_surveillance:
            pattern: ^/transfer-surveillance/rest/v1/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            simple_preauth:
                authenticator: paysera_jwt_authentication.authenticator.jwt
            provider: jwt_user_provider
            stateless: true

        accounting-discrepancies:
            pattern: ^/accounting-discrepancies/rest/v1/
            simple_preauth:
                authenticator: paysera_jwt_authentication.authenticator.jwt
            provider: jwt_user_provider
            stateless: true

        questionnaire:
            pattern: ^/questionnaire/rest/v2/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            simple_preauth:
                authenticator: paysera_jwt_authentication.authenticator.jwt
            provider: jwt_user_provider
            stateless: true

        ai_assistant:
            pattern: ^/ai-assistant/rest/v1/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            simple_preauth:
                authenticator: paysera_jwt_authentication.authenticator.jwt
            provider: jwt_user_provider
            stateless: true

        client_merges:
            pattern: ^/client/rest/v1/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            stateless: true

        public_nbg:
            pattern: ^/public/nbg
            anonymous: true
            stateless: true

        public_libra:
            pattern: ^/public/libra
            anonymous: true
            stateless: true
            simple_preauth:
                authenticator: paysera_libra_bank.x_api_key_authenticator

        public:
            pattern: ^/public/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            simple_preauth:
                authenticator: paysera_jwt_authentication.authenticator.jwt
            provider: jwt_user_provider
            anonymous: true
            stateless: true

        rest:
            pattern: ^(/|/transfer/|/local-accounting-operations/|/georgia-revenue-service/|/debt/|/epay-cash-out-request/|/compass-plus/|/card-integration/|/blacklist/|/card-switch/|/delayed-commission/)rest/
            mac:
                ext_validator: mac.security.ext_validator.content_hash
                provider: in_memory
            anonymous: false
            stateless: true

        sms_query:
            pattern: ^/sms-query
            anonymous: true
            stateless: true

        rest_card_integration_callback:
            pattern:   ^/card-integration-callback/
            anonymous: true
            stateless: true

        status:
            pattern: ^/status/
            http_basic:
                realm: "Status"
                provider: in_memory
            anonymous: false
            stateless: true

        ping:
            pattern: ^/ping$
            security: false

        health:
            pattern: ^/health$
            security: true
            anonymous: true
            stateless: true

        main:
            pattern: ^/
            form_login:
                provider: fos_userbundle
            anonymous:    true
            logout:
                path: sonata_user_admin_security_logout
                target: sonata_admin_dashboard

    access_control:
        - { path: ^/wdt/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/profiler/, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/js/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/css/, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/login$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/login_check$, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/admin/, role: ROLE_ADMIN }
        - { path: ^/resetting, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/admin/, role: ROLE_ADMIN }
        - { path: ^/api/v1/, role: ROLE_CLIENT }
        - { path: ^/api/management, role: ROLE_SOAP }
        - { path: ^/api/account, role: ROLE_SOAP }
        - { path: ^/api/webtopay, role: ROLE_SOAP }
        - { path: ^/api/rest, role: ROLE_SOAP }
        - { path: ^/rest/deposit/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/rest/client/v2/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/rest/, role: ROLE_SOAP }
        - { path: ^/transfer/rest/, role: ROLE_SOAP }
        - { path: ^/local-accounting-operations/rest/, role: ROLE_SOAP }
        - { path: ^/epay-cash-out-request/rest/, role: ROLE_SOAP }
#        - { path: ^/api/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/public/push-notifications/rest/v1/one-time-password/3d-secure, role: IS_AUTHENTICATED_ANONYMOUSLY, ips: '%evp_contis.push_notifications_ips%' }
        - { path: ^/public/push-notifications/rest/v1/one-time-password/3d-secure, role: IS_AUTHENTICATED_ANONYMOUSLY, ips: '%evp_contis.push_notifications_testing_ips%' }
        - { path: ^/public/push-notifications/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY, ips: '%evp_contis.push_notifications_ips%' }
        - { path: ^/public/push-notifications/, role: ROLE_NO_ACCESS }
        - { path: ^/public/libra/rest/v1, role: IS_AUTHENTICATED_ANONYMOUSLY, ips: '%paysera_libra_bank.whitelist_ips%' }
        - { path: ^/public/libra/rest/v1, role: ROLE_NO_ACCESS }
        - { path: ^/public/nbg/api/NBGExchanges, role: IS_AUTHENTICATED_ANONYMOUSLY, ips: '%paysera_georgia_rtgs.nbg_exchange_ips%' }
        - { path: ^/public/nbg/api/NBGExchanges, role: IS_AUTHENTICATED_ANONYMOUSLY, host: tariffcompare\.nbg\.gov\.ge$ }
        - { path: ^/public/nbg/api/NBGExchanges, role: ROLE_NO_ACCESS }
        - { path: ^/public/sepa-instant/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/public/airwallex/rest/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/public/, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/sms-query, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/card-integration-callback/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/transfer-surveillance/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/georgia-revenue-service/rest/v1/, role: ROLE_SOAP }
        - { path: ^/debt/rest/v1/, role: ROLE_SOAP }
        - { path: ^/compass-plus/rest/v1/, role: ROLE_SOAP }
        - { path: ^/card-integration/rest/v1/, role: ROLE_SOAP }
        - { path: ^/card-switch/rest/v1/, role: ROLE_SOAP }
        - { path: ^/blacklist/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/accounting-discrepancies/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
#        - { path: ^/api/versobank/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/api/sepa-instant/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/questionnaire/rest/v2/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/client/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/ai-assistant/rest/v1/, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/delayed-commission/rest/v1/, role: ROLE_SOAP }

        - { path: ^/health, host: (deployment|localhost|\.paysera(-staging)?\.(lan|net)|\.paysera-sandbox\.lan)$, role: IS_AUTHENTICATED_ANONYMOUSLY }

        - { path: ^/$, role: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/.*, role: ROLE_ADMIN }

    role_hierarchy:
        ROLE_ADMIN:       [ROLE_USER, ROLE_SONATA_ADMIN]
        ROLE_CLIENT:      ROLE_USER
        ROLE_SOAP:        ROLE_USER
        ROLE_SUPER_ADMIN: [ROLE_USER, ROLE_ADMIN, ROLE_ALLOWED_TO_SWITCH, ROLE_REPROCESS_UNKNOWN_CARD,
                           ROLE_EDIT_CARD_SHIPPING_ADDRESS, ROLE_PAYSERA_TECH_VENTURES_BANK_ADMIN_IBAN_DEACTIVATE,
                           ROLE_PAYSERA_TECH_VENTURES_BANK_ADMIN_IBAN_REQUEST_DEACTIVATION, ROLE_EVP_BANK_CHARGE_ADMIN_CHARGE_CREATE_MASTER,
                           ROLE_ISOLATE_PARTNER]

    access_decision_manager:
        strategy: affirmative
