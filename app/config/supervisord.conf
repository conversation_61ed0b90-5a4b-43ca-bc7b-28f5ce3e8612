[program:rabbitmq-job-process-charge]
command=php app/console rabbitmq:consumer job_gateway_process_charge
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:charge-deamon]
command=php app/console evp:charge:process --deamonize 60
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:transfer-in-post-prepare-deamon]
command=php app/console evp:transfer:process-transfer-in-post-prepare 'today' 'yesterday' --deamonize 6
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:transfer-out-distributed-deamon]
command=php app/console evp:transfer:process:distributed:out --deamonize 4
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:transfer-out-distributed-sucess-deamon]
command=php app/console evp:transfer:process:distributed:out:success --deamonize 4
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:transfer-currency-convert-distributed-deamon]
command=php app/console evp:transfer:process:distributed:currency_convert --deamonize 4
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:vmi-complete-payments-daemon]
command=php app/console evp:vmi-payments:complete-payments --deamonize --processing-time=3600 120
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:vmi-process-paid-payments-daemon]
command=php app/console evp:vmi-payments:process-paid-payments --deamonize --processing-time=3600 120
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:vmi-process-responses-daemon]
command=php app/console evp:vmi-payments:process-responses --deamonize --processing-time=3600 1200
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:rabbitmq-consumer-contis-ensure-account-balance]
command=php app/console rabbitmq:consumer job_contis_ensure_account_balance
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=3

[program:rabbitmq-consumer-contis-top-up-ensure-account-balance]
command=php app/console rabbitmq:consumer job_contis_top_up_ensure_account_balance
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=2

[program:rabbitmq-consumer-gateway-contis-card]
command=php app/console rabbitmq:consumer event_gateway_contis_card
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-contis-top-up-accounting-operations]
command=php app/console rabbitmq:consumer job_process_contis_account_top_up_accounting_operations
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-synchronize-contis-cards]
command=php app/console rabbitmq:consumer job_synchronize_contis_cards
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-contis-accounts-synchronize]
command=php app/console rabbitmq:consumer job_contis_accounts_synchronize
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=3

[program:rabbitmq-consumer-contis-top-up-negative-balance-from-own-account]
command=php app/console rabbitmq:consumer job_contis_top_up_negative_balance_from_own_account
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs_start=1
numprocs=3

[program:rabbitmq-consumer-contis-toggle-active-card-status-on-balance-change]
command=php app/console rabbitmq:consumer job_contis_toggle_active_card_status_on_balance_change
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs_start=1
numprocs=3

[program:rabbitmq-consumer-contis-update-card-xpay-tokens-status]
command=php app/console rabbitmq:consumer job_contis_update_card_xpay_tokens_status
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs_start=1
numprocs=3

[program:rabbitmq-consumer-contis-process-queued-card]
command=php app/console rabbitmq:consumer job_contis_process_queued_card
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs_start=1
numprocs=1

[program:rabbitmq-process-contis-card-charge-discount]
command=php app/console rabbitmq:consumer job_process_contis_card_charge_discount
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-job-process-unpaid-contis-cards]
command=php app/console rabbitmq:consumer job_process_unpaid_contis_cards
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-check-for-negative-balance-in-contis]
command=php app/console rabbitmq:consumer job_check_for_negative_balance_in_contis
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=5

[program:sepa-instant-ready-transfers-publish]
command=php app/console paysera:sepa-instant:ready-transfers:publish --deamonize 5
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:rabbitmq-process-sepa-instant-status-report]
command=php app/console rabbitmq:consumer process_sepa_instant_status_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-credit-transfer]
command=php app/console rabbitmq:consumer process_sepa_instant_credit_transfer
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=10

[program:rabbitmq-job-gateway-sepa-instant-send-transaction-out]
command=php app/console rabbitmq:consumer job_gateway_sepa_instant_send_transaction_out
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:rabbitmq-job-gateway-sepa-instant-archive-message]
command=php app/console rabbitmq:consumer job_gateway_sepa_instant_archive_message
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10

[program:rabbitmq-process-sepa-instant-cancellation-request]
command=php app/console rabbitmq:consumer process_sepa_instant_cancellation_request
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-debit-credit-notification]
command=php app/console rabbitmq:consumer process_sepa_instant_debit_credit_notification
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-investigation-resolution]
command=php app/console rabbitmq:consumer process_sepa_instant_investigation_resolution
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-participant]
command=php app/console rabbitmq:consumer process_sepa_instant_participant
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-payment-return]
command=php app/console rabbitmq:consumer process_sepa_instant_payment_return
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-status-request]
command=php app/console rabbitmq:consumer process_sepa_instant_status_request
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-sepa-instant-liquidity]
command=php app/console rabbitmq:consumer process_sepa_instant_liquidity
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-sepa-instant-participant-unavailability]
command=php app/console rabbitmq:consumer process_sepa_instant_participant_unavailability
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-sepa-instant-liquidity-position-notification]
command=php app/console rabbitmq:consumer process_sepa_instant_liquidity_position_notification
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-sepa-instant-liquidity-transfer-request]
command=php app/console rabbitmq:consumer process_sepa_instant_liquidity_transfer_request
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-consumer-process-lb-sepa-participants-list]
command=php app/console rabbitmq:consumer process_lb_sepa_participants_list
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-liquidity]
command=php app/console rabbitmq:consumer process_lb_liquidity
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-process-target2-payment-status]
command=php app/console rabbitmq:consumer process_target2_payment_status
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-target2-credit-transfer]
command=php app/console rabbitmq:consumer process_target2_credit_transfer
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-target2-participant]
command=php app/console rabbitmq:consumer process_target2_participant
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-target2-financial-document]
command=php app/console rabbitmq:consumer process_target2_financial_document
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-target2-connection-result]
command=php app/console rabbitmq:consumer process_target2_connection_result
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

[program:rabbitmq-process-target2-syntax-result]
command=php app/console rabbitmq:consumer process_target2_syntax_result
exitcodes=0
autostart=true
autorestart=true
startsecs=5
startretries=10

# TARGET2 RTGS
[program:rabbitmq-consumer-process-target2-rtgs-admi-notifications]
command=php app/console rabbitmq:consumer process_target2_rtgs_admi_notifications
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-status-reports]
command=php app/console rabbitmq:consumer process_target2_rtgs_status_reports
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-customer-credit-transfer]
command=php app/console rabbitmq:consumer process_target2_rtgs_customer_credit_transfer
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-payment-return-in]
command=php app/console rabbitmq:consumer process_target2_rtgs_payment_return_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-cancellation-request-in]
command=php app/console rabbitmq:consumer process_target2_rtgs_cancellation_request_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-resolution-of-investigation]
command=php app/console rabbitmq:consumer process_target2_rtgs_resolution_of_investigation
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-receipt-in]
command=php app/console rabbitmq:consumer process_target2_rtgs_receipt_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-debit-credit-notification]
command=php app/console rabbitmq:consumer process_target2_rtgs_debit_credit_notification
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-participant]
command=php app/console rabbitmq:consumer process_target2_rtgs_participant
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-interbank-payment-in]
command=php app/console rabbitmq:consumer process_target2_rtgs_interbank_payment_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-target2-rtgs-bank-to-customer-statement]
command=php app/console rabbitmq:consumer process_target2_rtgs_bank_to_customer_statement
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
# END OF TARGET2 RTGS

[program:rabbitmq-consumer-contis-update-account-balance]
command=php app/console rabbitmq:consumer job_contis_update_account_balance
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-consumer-georgia-revenue-service-debts]
command=php app/console rabbitmq:consumer job_gateway_georgia_revenue_service_debts
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

# GEORGIA RTGS
[program:rabbitmq-consumer-process_georgia-rtgs-file-reject]
command=php app/console rabbitmq:consumer process_georgia_rtgs_file_reject
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia-rtgs-swift-status-report]
command=php app/console rabbitmq:consumer process_georgia_rtgs_swift_status_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia-rtgs-swift-debit-credit-confirmation]
command=php app/console rabbitmq:consumer process_georgia_rtgs_swift_debit_credit_confirmation
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia-rtgs-iso-status-report]
command=php app/console rabbitmq:consumer process_georgia_rtgs_iso_status_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia-rtgs-swift-transfer-in]
command=php app/console rabbitmq:consumer process_georgia_rtgs_swift_transfer_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia-rtgs-unifi-transfer-in]
command=php app/console rabbitmq:consumer process_georgia_rtgs_unifi_transfer_in
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process_georgia_rtgs_debit_credit_notification]
command=php app/console rabbitmq:consumer process_georgia_rtgs_debit_credit_notification
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
# END OF GEORGIA RTGS

[program:rabbitmq-consumer-contis-process-debt-repayment]
command=php app/console rabbitmq:consumer job_contis_process_debt_repayment
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

# TURNOVER
[program:rabbitmq-consumer-turnover-calculation]
command=php app/console rabbitmq:consumer job_turnover_calculation
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=5
# END OF TURNOVER

# UPLOAD FILES PROCESSING (DONT MOVE TO ANOTHER FILE)
[program:rabbitmq-consumer-gateway-process-transfer-import]
command=php app/console rabbitmq:consumer job_gateway_process_transfer_import
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=3
# END UPLOAD FILES PROCESSING

[program:rabbitmq-consumer-questionnaire-revocation]
command=php app/console rabbitmq:consumer job_questionnaire_revocation
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-rejected-questionnaire-process-restrictions]
command=php app/console rabbitmq:consumer job_rejected_questionnaire_process_restrictions
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-questionnaire-client-activity]
command=php app/console rabbitmq:consumer job_client_activity_questionnaire
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

#Distributed jobs
[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-out]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_out
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in-clearing]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in_clearing
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-macro-micro-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_macro_micro_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-event-app-client-info]
command=php app/console rabbitmq:consumer event_app_client_info
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-transfer-sign-async]
command=php app/console rabbitmq:consumer job_gateway_transfer_async_sign_request
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-questionnaire-ai-consumer-validator]
command=php app/console rabbitmq:consumer job_client_activity_questionnaire_ai_analysis_validator
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-questionnaire-ai-consumer-analysis]
command=php app/console rabbitmq:consumer job_client_activity_questionnaire_ai_analysis
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-event-transfer-surveillance-analysis-notification]
command=php app/console rabbitmq:consumer job_transfer_surveillance_analysis_notification
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

###Distributed jobs
