imports:
    - { resource: config_dev.yml }
    - { resource: services_test.xml }

framework:
    test: ~
    session:
        storage_id: session.storage.filesystem
    profiler:
        enabled: true
        collect: false

swiftmailer:
    disable_delivery: true

doctrine:
    dbal:
        default_connection: default
        connections:
            default:
                driver: pdo_sqlite
                path: '%kernel.cache_dir%/__DBNAME__.db'
                dbname: test
                wrapper_class: \Evp\Component\Doctrine\Connection\TransactionAwareConnection

    orm:
        connection: default

parameters:
    doctrine.dbal.connection_factory.class: Liuggio\Fastest\Doctrine\DBAL\ConnectionFactory
    evp_blacklist.blacklist_configuration_name_v1: test_sanctions_production_v1
    evp_blacklist.blacklist_current_configuration: '%evp_blacklist.blacklist_configuration_name_v1%'
    evp_blacklist.blacklist_alias_name_active: test_sanctions_active
    evp_blacklist.blacklist_configuration_name_secondary_v1: test_sanctions_secondary_v1
    evp_blacklist.blacklist_alias_name_importing: test_sanctions_importing
    evp_blacklist.blacklist_elasticsearch_index_number_of_shards: 1
    evp_blacklist.blacklist_elasticsearch6_template_name: test_elasticsearch6
    evp_transfer_tax.vpo.client.factory_method: createSandboxClient
    evp_rabbit_mq_extension.remote_events_enabled: false
    evp_rabbit_mq_extension.remote_jobs_enabled: true
    security.access_listener.class: Evp\Bundle\ContisBundle\Tests\TestService\AccessListener
    evp_contis.paysera_account_number: EVP8010001000102
    evp_contis.deposit_amount: 100
    evp_contis_client.class.contis_account_client: Evp\Bundle\ContisBundle\Tests\TestService\ContisClient\ContisAccountClient
    evp_rabbit_mq_extension.producer.class: Evp\Tests\Integration\RabbitMq\Producer\ExtendedProducer
    evp_plais.charge_amount: '1.00'
    paysera_bic_main: 'EVIULT20XXX'
    paysera_bic_additional: 'EVIULT20XXX'
    lb_integration_sender_bic_code: 'EVIULT21XXX'
    evp_currency_pricing.uri: null
    paysera_libra_bank.whitelist_ips:
        - '127.0.0.1'
    paysera_georgia_debtor_registry.base_url: 'http://localhost:8000'

evp_payza:
    server: sandbox.payza.com
    send_money_url: /api/api.svc/sendmoney
    get_balance_url: /api/api.svc/GetBalance
    test_mode: true

stof_doctrine_extensions:
    orm:
        default:
            loggable: true
            timestampable: true

evp_audit:
    auth:
        type: mac

evp_application_logging:
    normalization_depth: 1

evp_test_case:
    secured_routes:
        forbidden_responses: [302, 401, 403]
        white_listed_routes:
            - _profiler*
            - _wdt
            - paysera_information*
            - paysera_external_routing*
            - paysera_developer*
            - fos*
            - EvpContisBundle_management*
            - _webservice*
            - Evp_Bundle_ClientBundle_Client_*Action
            - evp_bank_transfer_bundle.create_new_*_party
            - EvpOperatorWidgetBundle_*
            - sonata_media_*
            - '*_admin_*'
            - '*.admin.*'
            - admin_evp_*
            - admin_paysera_*
            - evp_admin*
            - admin_sonata_*
            - Evp_Bundle_ClientBundle_Client_*
            - EvpBankAccountBundle_findAccounts
    services_to_skip_in_resetter:
        - 'security.token_storage'
    allowed_requirements:
        -
            parameter: '_locale'
            requirements: ['[a-z]{2}', 'en']
    brute_force:
        protected_paths:
            - ^/public/.{0,}/rest/
        white_listed_routes:
            - paysera_currencyexchange_currencyexchangeapi_calculatecurrencyexchange
            - paysera_currencyexchange_currencyexchangeapi_executecurrencyexchange
