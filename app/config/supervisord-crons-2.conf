[program:rabbitmq-consumer-batch-save-banking-history-card-transaction]
command=php app/console rabbitmq:batch:consumer high_load_batch_job_save_banking_history_card_transaction --memory-limit=200
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=6

[program:rabbitmq-consumer-partner-intermediate-statement-request]
command=php app/console rabbitmq:consumer job_partner_intermediate_statement_request --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=10

[program:rabbitmq-consumer-process-partner-accounting-operation-reprocessing]
command=php app/console rabbitmq:consumer job_process_partner_accounting_operation_reprocessing --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=10

[program:rabbitmq-consumer-create-statement-turnover]
command=php app/console rabbitmq:consumer job_create_statement_turnover --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=15

[program:rabbitmq-consumer-create-statement-turnover-high-load]
command=php app/console rabbitmq:consumer high_load_job_create_statement_turnover --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-consumer-process-contis-partner-accounting-operation]
command=php app/console rabbitmq:consumer job_process_contis_partner_accounting_operation --memory-limit=400
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=12

[program:rabbitmq-consumer-job-accounting-operation-notify]
command=php app/console rabbitmq:consumer job_accounting_operation_notify
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=12

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-handle-audit]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_handle_audit --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-whitelist-approve]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_whitelist_approve
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=10

[program:rabbitmq-job-transfer-surveillance-synchronize-whitelisted-profiles]
command=php app/console rabbitmq:consumer job_transfer_surveillance_synchronize_whitelisted_profiles --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-high-load-transfer-surveillance-maintenance]
command=php app/console rabbitmq:consumer high_load_transfer_surveillance_maintenance --memory-limit=200
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=5

[program:rabbitmq-consumer-gateway-blacklist-dow-jones-full-import]
command=php app/console rabbitmq:consumer job_gateway_blacklist_dow_jones_full_import
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=15

[program:rabbitmq-consumer-gateway-blacklist-dow-jones-incremental-import]
command=php app/console rabbitmq:consumer job_gateway_blacklist_dow_jones_incremental_import
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=5

[program:rabbitmq-consumer-gateway-blacklist-check]
command=php app/console rabbitmq:consumer job_gateway_blacklist_check
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=3

[program:rabbitmq-consumer-gateway-blacklist-check-user-generate-report]
command=php app/console rabbitmq:consumer job_gateway_blacklist_check_user_generate_report
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=3

[program:rabbitmq-consumer-gateway-blacklist-check-user-generate-report-file]
command=php app/console rabbitmq:consumer job_gateway_blacklist_check_user_generate_report_file
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
process_name=%(program_name)s_%(process_num)s
numprocs=5

[program:rabbitmq-consumer-check-blacklist-updated-profile]
command=php app/console rabbitmq:consumer job_check_blacklist_updated_profile --memory-limit=200
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-consumer-blacklist-periodic-check-process-data]
command=php app/console rabbitmq:consumer job_gateway_blacklist_periodic_check_process_data --messages=1
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-event_blacklist_restriction]
command=php app/console rabbitmq:consumer event_blacklist_restriction
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs=1

[program:rabbitmq-consumer-gateway-process-inactive-client]
command=php app/console rabbitmq:consumer job_process_inactive_client
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=1


[program:rabbitmq-consumer-transfer-client-type-ai-checker-worker-transfer-out]
command=php app/console rabbitmq:consumer job_gateway_transfer_client_type_ai_checker_worker_transfer_out
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=5

[program:rabbitmq-consumer-transfer-client-type-ai-checker-worker-transfer-in]
command=php app/console rabbitmq:consumer job_gateway_transfer_client_type_ai_checker_worker_transfer_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=5

[program:contis-auto-fix-transactions]
command=php app/console evp:contis:auto-fix-transactions --statementFilterDatesTolerance=300 --deamonize 60
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-gateway-process-callbacks]
command=php app/console rabbitmq:consumer job_gateway_process_callbacks
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
process_name=%(program_name)s_%(process_num)s
numprocs_start=1
numprocs=5

[program:transfer-tax-ready-deamon]
command=php app/console evp:transfer:tax:ready --deamonize 60
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:process-events]
command=php app/console evp:notification:process-events --deamonize 20
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:rabbitmq-consumer-persistable-event-process-persistable-event]
command=php app/console rabbitmq:consumer job_persistable_event_process_persistable_event
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs_start=20
numprocs=45

[program:rabbitmq-consumer-persistable-event-process-priority-persistable-event]
command=php app/console rabbitmq:consumer job_persistable_event_process_priority_persistable_event
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
numprocs_start=1
numprocs=2

[program:notify-stalled-persistable-event]
command=php app/console evp:persistable-event:notify:stalled --deamonize 300
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:publish-persisted-events]
command=php app/console evp:persistable-event:publish --deamonize 2
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:clean-up-persisted-events]
command=php app/console evp:persistable-event:clean-up --deamonize 3600
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0

[program:waiting-transfers-daemon]
command=php app/console evp:transfer:process:waiting-transfers --deamonize 60
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0


[program:rabbitmq-consumer-process-lb-notification]
command=php app/console rabbitmq:consumer process_lb_notification
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-status-report]
command=php app/console rabbitmq:consumer process_lb_status_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-credit-transaction]
command=php app/console rabbitmq:consumer process_lb_credit_transaction
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-payment-return]
command=php app/console rabbitmq:consumer process_lb_payment_return
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-claim-value-date-correction]
command=php app/console rabbitmq:consumer process_lb_claim_value_date_correction
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-request-for-status-update]
command=php app/console rabbitmq:consumer process_lb_request_for_status_update
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-claim-non-receipt]
command=php app/console rabbitmq:consumer process_lb_claim_non_receipt
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-cancellation-request]
command=php app/console rabbitmq:consumer process_lb_cancellation_request
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-investigation-resolution]
command=php app/console rabbitmq:consumer process_lb_investigation_resolution
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-balance-report]
command=php app/console rabbitmq:consumer process_lb_balance_report
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:process-lb-investigation-result]
command=php app/console rabbitmq:consumer process_lb_investigation_result
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-debit-credit-notification]
command=php app/console rabbitmq:consumer process_lb_debit_credit_notification
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-consumer-process-lb-debit-transaction]
command=php app/console rabbitmq:consumer process_lb_debit_transaction
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

# Must always have numprocs=1 because we need to process files one by one
[program:rabbitmq-consumer-process-lb-plais-arrest]
command=php app/console rabbitmq:consumer process_lb_plais_arrest
exitcodes=0
process_name=%(program_name)s_%(process_num)s
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-process-lb-plais-arrest-payment_request]
command=php -d memory_limit=4G app/console rabbitmq:consumer process_lb_plais_arrest_payment_request
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-process-lb-plais-archived]
command=php app/console rabbitmq:consumer process_lb_plais_archived
exitcodes=0
process_name=%(program_name)s_%(process_num)s
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-process-lb-plais-sent-file]
command=php app/console rabbitmq:consumer process_lb_plais_sent_file
exitcodes=0
process_name=%(program_name)s_%(process_num)s
startsecs=5
startretries=10
autorestart=true
autostart=true
numprocs=1

[program:rabbitmq-consumer-process-lb-errors]
command=php app/console rabbitmq:consumer process_lb_errors
exitcodes=0
startsecs=5
startretries=10
autorestart=true
autostart=true

[program:rabbitmq-event-save-banking-history-address]
command=php app/console rabbitmq:consumer event_save_banking_history_address --memory-limit=200
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
startsecs=5
startretries=10
exitcodes=0
numprocs=1

[program:rabbitmq-consumer-batch-save-banking-history-transfer]
command=php app/console rabbitmq:batch:consumer high_load_batch_job_save_banking_history_transfer --memory-limit=200
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-consumer-batch-save-banking-history-transfer-migration]
command=php app/console rabbitmq:batch:consumer high_load_batch_job_save_banking_history_transfer_migration --memory-limit=200
autostart=true
autorestart=true
process_name=%(program_name)s_%(process_num)s
exitcodes=0
startsecs=5
startretries=10
numprocs=0

#Distributed jobs
[program:rabbitmq-job-transfer-surveillance-transfer-inspection]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-out]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_out
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-sepa-inst-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_sepa_inst_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=4

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in-clearing]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in_clearing
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=2

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

[program:rabbitmq-job-transfer-surveillance-transfer-inspection-macro-micro-in]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_macro_micro_in
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=3

# Audited transfers (already done)
[program:rabbitmq-job-transfer-surveillance-transfer-inspection-inst-internal]
command=php app/console rabbitmq:consumer job_transfer_surveillance_transfer_inspection_inst_internal
process_name=%(program_name)s_%(process_num)s
exitcodes=0
autorestart=true
autostart=true
startsecs=5
startretries=10
numprocs=1

##Distributed jobs
