{"info": {"_postman_id": "b98c9eb4-69e7-4411-b29a-df98c79e7447", "name": "Shared Node Sandbox", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "24550334"}, "item": [{"name": "iPiD Node API", "item": [{"name": "Token", "event": [{"listen": "test", "script": {"exec": ["var response = JSON.parse(responseBody);", "var token = response.access_token;", "console.log(token)", "postman.setEnvironmentVariable(\"token\", token);", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\" :\"{{client_id}}\",\n    \"client_secret\" : \"{{client_secret}}\",\n    \"grant_type\" : \"client_credentials\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{url}}/oauth/api/v1/token"}, "response": []}, {"name": "[Outbound] Validate - COP", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"country_code\": \"GB\",\n    \"creditor\": {\n        \"name\": \"S HOLMES\"\n    },\n    \"creditor_account\": {\n        \"account_id\": \"********\",\n        \"account_holder_type\": \"personal\"\n    },\n    \"creditor_agent\": {\n        \"clearing_system_id\": \"112233\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{url}}/api/v1/bank-account/validate"}, "response": []}, {"name": "[Outbound] Validate - VOP", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"country_code\": \"<PERSON>\",\n    \"creditor\": {\n        \"name\": \"<PERSON>\"\n    },\n    \"creditor_account\": {\n        \"iban\": \"**********************\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{url}}/api/v1/bank-account/validate"}, "response": []}, {"name": "[Outbound] Validate - Global", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"country_code\": \"IN\",\n    \"creditor\": {\n        \"name\": \"VIRAT KOHLI\"\n    },\n    \"creditor_account\": {\n        \"account_id\": \"************\"\n    },\n    \"creditor_agent\": {\n        \"clearing_system_id\": \"YESB0000002\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{url}}/api/v1/bank-account/validate"}, "response": []}, {"name": "[DataHub] Upload", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": []}]}, "url": "{{url}}/datahub/api/v1/upload"}, "response": []}]}]}